<?php

namespace App\Http\Controllers\GoogleAdwords;

use App\Models\Lead\Lead;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\GoogleAdwords\GoogleAdwardsOfflineConversionCall;

//use GetOpt\GetOpt;
use Google\Ads\GoogleAds\Lib\OAuth2TokenBuilder;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClient;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClientBuilder;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsException;
use Google\Ads\GoogleAds\Util\V18\ResourceNames;
use Google\Ads\GoogleAds\V18\Errors\GoogleAdsError;
use Google\Ads\GoogleAds\V18\Services\CallConversion;
use Google\Ads\GoogleAds\V18\Services\CallConversionResult;
use Google\Ads\GoogleAds\V18\Services\UploadCallConversionsRequest;
use Google\ApiCore\ApiException;
use DateTime;
use DB;
use Exception;
use Helper;
use Log;

class GoogleAdwordsOfflineConversionCallController extends Controller
{
    // Optional: Specify the conversion custom variable ID and value you want to
    // associate with the click conversion upload.
    private const CONVERSION_CUSTOM_VARIABLE_ID = null;
    private const CONVERSION_CUSTOM_VARIABLE_VALUE = null;

    public static $CUSTOMER_ID;
    public static $CONVERSION_ACTION_ID;
    public static $CALLER_ID;
    public static $CALL_START_DATE_TIME;
    public static $CONVERSION_DATE_TIME;
    public static $CONVERSION_VALUE;

    public static function main()
    {
        // Either pass the required parameters for this example on the command line, or insert them
        // into the constants above.
        // .ini file path for PMM, MT
        $filePath = base_path("google_ads_php.ini");

        // Generate a refreshable OAuth2 credential for authentication.
        $oAuth2Credential = (new OAuth2TokenBuilder())->fromFile($filePath)->build();

        // Construct a Google Ads client configured from a properties file and the
        // OAuth2 credentials above.
        $googleAdsClient = (new GoogleAdsClientBuilder())
            ->fromFile($filePath)
            ->withOAuth2Credential($oAuth2Credential)
            ->build();


        try {
            Log::info('Offline Conversion:- 1');
            return self::runExample(
                $googleAdsClient,
                GoogleAdwordsOfflineConversionCallController::$CUSTOMER_ID,
                GoogleAdwordsOfflineConversionCallController::$CONVERSION_ACTION_ID,
                GoogleAdwordsOfflineConversionCallController::$CALLER_ID,
                GoogleAdwordsOfflineConversionCallController::$CALL_START_DATE_TIME,
                GoogleAdwordsOfflineConversionCallController::$CONVERSION_DATE_TIME,
                GoogleAdwordsOfflineConversionCallController::$CONVERSION_VALUE
                /*self::CONVERSION_CUSTOM_VARIABLE_ID,
                self::CONVERSION_CUSTOM_VARIABLE_VALUE*/
            );
        } catch (GoogleAdsException $googleAdsException) {
            Log::info('Call Offline Conversion:- 2');
            printf(
                "Request with ID '%s' has failed.%sGoogle Ads failure details:%s",
                $googleAdsException->getRequestId(),
                PHP_EOL,
                PHP_EOL
            );
            foreach ($googleAdsException->getGoogleAdsFailure()->getErrors() as $error) {
                /** @var GoogleAdsError $error */
                printf(
                    "\t%s: %s%s",
                    $error->getErrorCode()->getErrorCode(),
                    $error->getMessage(),
                    PHP_EOL
                );
            }
            //exit(1);
        } catch (ApiException $apiException) {
            Log::info('Call Offline Conversion:- 3');
            printf(
                "ApiException was thrown with message '%s'.%s",
                $apiException->getMessage(),
                PHP_EOL
            );
            //exit(1);
        }
    }

    /**
     * Runs the example.
     *
     * @param GoogleAdsClient $googleAdsClient the Google Ads API client
     * @param int $customerId the customer ID
     * @param int $conversionActionId the ID of the conversion action to upload to
     * @param string $callerId the caller ID from which this call was placed. Caller ID is expected
     *     to be in E.164 format with preceding '+' sign. e.g. "+18005550100"
     * @param string $callStartDateTime the date and time at which the call occurred. The format is
     *     "yyyy-mm-dd hh:mm:ss+|-hh:mm", e.g. “2019-01-01 12:32:45-08:00”
     * @param string $conversionDateTime the date and time of the conversion (should be after the
     *     call time). The format is "yyyy-mm-dd hh:mm:ss+|-hh:mm", e.g.
     *     “2019-01-01 12:32:45-08:00”
     * @param float $conversionValue the value of the conversion
     * @param string|null $conversionCustomVariableId the ID of the conversion custom variable to
     *     associate with the upload
     * @param string|null $conversionCustomVariableValue the value of the conversion custom
     *     variable to associate with the upload
     * @param int|null $adUserDataConsent the consent status for ad user data
     */
    // [START upload_call_conversion]
    public static function runExample(GoogleAdsClient $googleAdsClient, $customerId, $conversionActionId, $callerId, $callStartDateTime, $conversionDateTime, $conversionValue) {

        try {
            // Creates a call conversion by specifying currency as USD.
            $callConversion = new CallConversion([
                'conversion_action' => ResourceNames::forConversionAction($customerId, $conversionActionId),
                'caller_id' => $callerId,
                'call_start_date_time' => $callStartDateTime,
                'conversion_date_time' => $conversionDateTime,
                'conversion_value' => $conversionValue,
                'currency_code' => 'USD'
            ]);

            // Issues a request to upload the call conversion.
            $conversionUploadServiceClient = $googleAdsClient->getConversionUploadServiceClient();
            // NOTE: This request contains a single conversion as a demonstration.  However, if you have
            // multiple conversions to upload, it's best to upload multiple conversions per request
            // instead of sending a separate request per conversion. See the following for per-request
            // limits:
            // https://developers.google.com/google-ads/api/docs/best-practices/quotas#conversion_upload_service
            $response = $conversionUploadServiceClient->uploadCallConversions(
            // Partial failure MUST be enabled for this request.
                UploadCallConversionsRequest::build($customerId, [$callConversion], true)
            );

            // Prints the status message if any partial failure error is returned.
            // Note: The details of each partial failure error are not printed here, you can refer to
            // the example HandlePartialFailure.php to learn more.
            if ($response->hasPartialFailureError()) {
                /*printf(
                    "Partial failures occurred: '%s'.%s",
                    $response->getPartialFailureError()->getMessage(),
                    PHP_EOL
                );*/

                $responseResult = json_encode(
                    array(
                        'api_response' => '',
                        'api_success' => 0 ,
                        'conversion_value' => '',
                        'conversion_name' => '',
                        'error' => 1,
                        'error_msg' => $response->getPartialFailureError()->getMessage()
                    )
                );

                Log::info('Call Offline Conversion:- Partial Failure');
                //print_r($responseResult);
                return $responseResult;
            } else {
                // Prints the result if exists.
                /** @var CallConversionResult $uploadedCallConversion */
                /*$uploadedCallConversion = $response->getResults()[0];*/
                /*printf(
                    "Uploaded call conversion that occurred at '%s' for caller ID '%s' to the "
                    . "conversion action with resource name '%s'.%s",
                    $uploadedCallConversion->getCallStartDateTime(),
                    $uploadedCallConversion->getCallerId(),
                    $uploadedCallConversion->getConversionAction(),
                    PHP_EOL
                );*/
                $success = 1;
                $responseResult = json_encode(
                    array(
                        'api_response' => $response,
                        'api_success' => $success,
                        'conversion_value' => $conversionValue,
                        'conversion_name' => '',
                        'error' => 0,
                        'error_msg' => ''
                    )
                );
                Log::info('Call Offline Conversion:- Success');
                //print_r($responseResult);
                return $responseResult;
            }
        } catch (ApiException $apiException) {
            $responseResult = json_encode(
                array(
                    'api_response' => '',
                    'api_success' => 0 ,
                    'conversion_value' => '',
                    'conversion_name' => '',
                    'error' => 1,
                    'error_msg' => $apiException->getMessage()
                )
            );
            Log::info('Call Offline Conversion:- Failure');
            //print_r($responseResult);
            return $responseResult;
        }
    }
    // [END upload_call_conversion]

    public function getOfflineConversion($originName, $date, $flag) {
        //echo $originName; die;
        Log::info('Call Offline Conversion Start Date:- ' . $date);
        $toNumber = $leadIdArray = $leadArray = [];
        $originId = 0;
        if($originName == 'VM') {
            $toNumber = array('18333920329', '18334080606'); //lead_call_number callit_number
            $originId = 19;
        } else if($originName == 'QTM') {
            $toNumber = array('18773892482', '18337922500', '8338096463'); //lead_call_number callit_number
            $originId = 14;
        } else if($originName == 'IS') {
            $toNumber = array('18337481230'); //lead_call_number callit_number
            $originId = 36;
        }

        //echo date('Y-m-d H:i:s', time()); die;
        $dateTime = new DateTime();
        $formattedDate = $dateTime->format('Y-m-d H:i:s');
        $hour = date('G', strtotime($formattedDate));
        $startDate = date('Y-m-d', strtotime($date . '-30 days')) . ' 00:00:00';
        $endDate = $date . ' 23:59:59';

        //check already added offline conversion
        $alreadyAdded = GoogleAdwardsOfflineConversionCall::select('lead_call_id')
            ->whereBetween('lead_date', [date('Y-m-d', strtotime($date . '-30 days')), $date])
            ->get()
            ->toArray();

        /*run this cron every hours current time is less than 3 to pick yesterday and modify -2 hours but current time is greater than 3
        pick current date and modify -2 hours and avoid already added offline conversation.*/
        /*$leads = DB::table('lead_call')
            ->select('lead_call_id', 'from_number', 'to_number', 'call_datetime', 'payout', 'created_at')
            ->whereNotIn('lead_call_id', $alreadyAdded)
            ->whereIn('to_number', $toNumber)
            ->where('duration', '>', 200)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->take($flag)
            ->get();*/

        $leads = DB::table('lead_call as lc')
            ->join('lead_transfer_call as lt', 'lc.lead_call_id', '=', 'lt.lead_call_id')
            ->select(
                'lc.lead_call_id',
                'lc.lead_id',
                'lc.from_number',
                'lc.to_number',
                'lc.call_datetime',
                'lc.payout as call_payout',
                'lc.duration as call_duration',
                'lc.created_at as call_created_at',
                'lt.duration as transfer_duration',
                DB::raw('(lc.duration + lt.duration) as total_duration')
            )
            ->whereNotIn('lc.lead_call_id', $alreadyAdded)
            ->whereIn('lc.to_number', $toNumber)
            ->whereBetween('lc.created_at', [$startDate, $endDate])
            ->having('total_duration', '>', 500)
            ->take($flag)
            ->get();

        if (count($leads) > 0) { foreach($leads as $lead) {
            $leadIdArray[] = $lead->lead_id;
        } }

        if (count($leadIdArray) > 0) {
            $leadDetail = Lead::whereIn('lead_id', $leadIdArray)->get(['lead_id', 'lead_generated_at']);
            if (count($leadDetail) > 0) { foreach($leadDetail as $lead) {
                $leadArray[$lead->lead_id] = $lead->lead_generated_at;
            } }
        }

        //echo '<pre>'; print_r($leads); die;
        if (count($leads) > 0) { foreach($leads as $lead) {
            $conversionTime = date('Y-m-d H:i:s', strtotime($leadArray[$lead->lead_id]))."-04:00";
            $callTime = date('Y-m-d H:i:s', strtotime($lead->call_datetime))."-04:00";

            try {
                if ($originName == "VM") {
                    GoogleAdwordsOfflineConversionCallController::$CUSTOMER_ID = 7000201456;
                    GoogleAdwordsOfflineConversionCallController::$CONVERSION_ACTION_ID = 6835310137;//ctId getting from url
                } else if($originName == "QTM") {
                    GoogleAdwordsOfflineConversionCallController::$CUSTOMER_ID = 3189698923;
                    GoogleAdwordsOfflineConversionCallController::$CONVERSION_ACTION_ID = 6839737848;
                } else if ($originName == "IS") {
                    GoogleAdwordsOfflineConversionCallController::$CUSTOMER_ID = 5101673588;
                    GoogleAdwordsOfflineConversionCallController::$CONVERSION_ACTION_ID = 6839607964;
                }

                GoogleAdwordsOfflineConversionCallController::$CALLER_ID = $this->formatToE164($lead->from_number);
                GoogleAdwordsOfflineConversionCallController::$CALL_START_DATE_TIME = $callTime;
                //echo $conversionTime;
                GoogleAdwordsOfflineConversionCallController::$CONVERSION_DATE_TIME = $conversionTime;
                //echo round($lead->payout, 2);
                GoogleAdwordsOfflineConversionCallController::$CONVERSION_VALUE = ($lead->call_payout > 0) ? round($lead->call_payout, 2) : 0.1;
                $response = GoogleAdwordsOfflineConversionCallController::main();
                // store response
                $responseData = json_decode($response, true);
                $status = 'yes';
                if($responseData['error'] == 1) {
                    $status = 'no';
                }

                $newRecord = [
                    'lead_source_id' => $originId,
                    'lead_call_id' => $lead->lead_call_id,
                    'lead_date' => date('Y-m-d', strtotime($lead->call_datetime)),
                    'response' => $response,
                    'status' => $status,
                    'type' => 'main',
                    'created_at' => date('Y-m-d H:i:s')
                ];
                //echo '<pre>'; print_r($record); die;
                GoogleAdwardsOfflineConversionCall::create($newRecord);

            } catch (Exception $e) {
                Log::info('Call Offline Conversion:- ' . $e->getMessage());
                echo $e->getMessage();
            }
        } } else {
            echo "No records found.";
        }
    }

    public function offlineConversion($date='') {
        //$runDate = date('Y-m-d',strtotime("-1 days"));
        //it will fetch 22 to 24 hours earliers records from lead table to submit google adwords
        $flag = 100;
        $runDate = date('Y-m-d');
        $runDate = date('Y-m-d', strtotime("-1 days"));
        if(isset($date) && !empty($date)) {
            $runDate = $date;
            $flag = 100;
        }
        //dd($runDate);
        //echo $runDate; die;
        $this->getOfflineConversion('VM', $runDate, $flag);
        $this->getOfflineConversion('QTM', $runDate, $flag);
        $this->getOfflineConversion('IS', $runDate, $flag);
    }

    public function formatToE164($phone) {
        // Remove non-digits
        $digits = preg_replace('/\D/', '', $phone);

        // Assume US if 10 digits
        if (strlen($digits) === 10) {
            return '+1' . $digits;
        } elseif (strlen($digits) === 11 && $digits[0] === '1') {
            return '+' . $digits;
        } elseif (strpos($digits, '+') !== 0) {
            return '+' . $digits;
        }

        return $digits;
    }
}