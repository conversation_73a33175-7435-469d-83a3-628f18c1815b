<?php

namespace App\Http\Controllers\Lead;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\Controller;
use App\Models\Lead\LeadSeeded;
use App\Models\Inbound\LeadSeededIbCall;
use App\Models\Inbound\LeadSeededIbSMS;
use App\Models\Business\Business;
use App\Models\Campaign\Campaign;
use App\Http\Controllers\API\Lead\LeadInsert;
use App\Models\Campaign\CampaignLeadDeliveryType;
use App\Http\Controllers\API\LeadDelivery\LeadDeliveryApi;
use App\Models\Master\MstCampaignType;
use App\Models\Master\MstCarType;
use App\Models\Master\MstChargeType;
use App\Models\Master\MstHeavyLiftingType;
use App\Models\Master\MstJunkSubType;
use App\Models\Master\MstJunkType;
use App\Models\Master\MstLeadCategory;
use App\Models\Master\MstLeadDeliveryType;
use App\Models\Master\MstLeadType;
use App\Models\Master\MstMoveSize;
use App\Models\Master\MstZipcode;
use App\Models\Master\MstLeadSource;
use App\Models\Master\MstAPIToken;
use App\Models\DevDebug;
use Auth;
use Exception;
use App\Models\Campaign\CampaignLocation;
use App\Helpers\CommonFunctions;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\Outbound\LeadCallNumber;
use App\Models\PlivoSms\ReceivedSMS;
use App\Models\Lead\Lead;
use App\Http\Controllers\Lead\LeadListController;
use App\Models\Lead\LeadCallBuffer;
use App\Models\Lead\LeadOldCallBuffer;
use App\Models\Lead\LeadFollowUp;
use App\Models\Outbound\LeadCallNumberAreacode;
use Http;

class LeadSeededController extends Controller {

    function __construct() {
        $this->middleware('permission:lead-seeded', ['only' => ['insert']]);
    }

    public function index() {
        $campaignNameArray = [];
        $businessId = Campaign::groupBy('business_id')->pluck('business_id');
        $campaign = Campaign::get();
        $businesses = Business::whereIn('business_id', $businessId)->orderby('business_id', 'DESC')->get();
        $LeadZipCodes = MstZipcode::groupBy('areacode')->get('zipcode');
        $junkSubType = MstJunkSubType::get();
        $junkType = MstJunkType::get();
        $campaignType = MstCampaignType::get();
        $carType = MstCarType::get();
        $chargeType = MstChargeType::get();
        $heavyLiftingType = MstHeavyLiftingType::get();
        $leadCategory = MstLeadCategory::get();
        $leadDeliveryType = MstLeadDeliveryType::get();
        $leadType = MstLeadType::get();
        $moveSize = MstMoveSize::get();
        $leadseededDetail = LeadSeeded::orderby('lead_seeded_id', 'DESC')->get()->toArray();
        $leadseededcallDetail = LeadSeededIbCall::orderby('lead_seeded_ib_call_id', 'DESC')->get()->toArray();
        $leadseededsmsDetail = LeadSeededIbSMS::orderby('lead_seeded_ib_sms_id', 'DESC')->get()->toArray();
        // echo "<pre>"; print_r($leadseededDetail); die;
        $endDecObj = new CommonFunctions;

        for ($c = 0; $c < count($campaign); $c++) {
            if (!in_array($campaign[$c]->campaign_name, $campaignNameArray)) {
                $campaignNameArray[$campaign[$c]->campaign_id] = $campaign[$c]->campaign_name;
            }
        }

        foreach ($leadseededDetail as $key => $leadseeded) {
            $leadseededDetail[$key]['leadseedencid'] = url('leaddetail/' . $endDecObj->customeEncryption($leadseeded['lead_id']));
            $leadseededDetail[$key]['campaignname'] = ($leadseeded['campaign_id'] > 0 && count($campaignNameArray) > 0) ? $campaignNameArray[$leadseeded['campaign_id']] : '';
            $leadseededDetail[$key]['lead_seeded_ency_id'] = $endDecObj->customeEncryption($leadseeded['lead_id']);
        }
        //echo "<pre>"; print_r($campaignNameArray); die;

        return view('Lead.leadseeded')->with('leadseededDetail', $leadseededDetail)->with('leadseededcallDetail', $leadseededcallDetail)->with('leadseededsmsDetail', $leadseededsmsDetail)->with('businesses', $businesses)->with('LeadZipCodes', $LeadZipCodes)->with('junkType', $junkType)->with('junkSubType', $junkSubType)->with('campaigns', $campaign)->with('carType', $carType)->with('chargeType', $chargeType)->with('heavyLiftingType', $heavyLiftingType)->with('leadCategory', $leadCategory)->with('leadDeliveryType', $leadDeliveryType)->with('leadType', $leadType)->with('moveSize', $moveSize);
    }

    public function leadSeededCampaign(Request $request) {
        $status = 0;
        $message = 'fail';
        $campaign_array = array();
        try {
            $businessesId = $request->get('id');
            $campaignCategory = $request->get('campaignCategory');
            $campaign = Campaign::where('business_id', $businessesId)->where('lead_category_id', $campaignCategory)->orderby('campaign_id', 'DESC')->get()->toArray();

            $status = 1;
            $message = "Success";
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        $data = [
            'status' => $status,
            'message' => $message,
            'data' => $campaign,
        ];
        //dd($data);
        return response()->json($data);
    }

    public function leadSeededDeliveryType(Request $request) {
        $status = 0;
        $message = 'fail';
        $campaignDetail = $emailDelivery = $smsDelivery = $crmDelivery = $defaultFields = $fromzipdata = $tozipdata = array();
        try {
            $campaignId = $request->get('id');
            $leadDelieveryTypeDetails = CampaignLeadDeliveryType::where('campaign_id', $campaignId)
                            ->with('campaignEmailDelivery')
                            ->with('campaignSmsDelivery')
                            ->with('campaignCrmDelivery', 'campaignCrmDelivery.LeadDeliveryCrmDefaultFields')
                            ->get()->toArray();
            if (count($leadDelieveryTypeDetails) > 0) {
                foreach ($leadDelieveryTypeDetails as $leadDelieveryType) {
                    if ($leadDelieveryType['lead_delivery_type_id'] == 1 || $leadDelieveryType['lead_delivery_type_id'] == "1") { //for Email
                        $emailDelivery = $leadDelieveryType['campaign_email_delivery'];
                    }
                    if ($leadDelieveryType['lead_delivery_type_id'] == 3 || $leadDelieveryType['lead_delivery_type_id'] == "3") { //for SMS
                        $smsDelivery = $leadDelieveryType['campaign_sms_delivery'];
                    }
                    if ($leadDelieveryType['lead_delivery_type_id'] == 4 || $leadDelieveryType['lead_delivery_type_id'] == "4") { //for CRM
                        $crmDelivery = $leadDelieveryType['campaign_crm_delivery'];
                        if (count($crmDelivery) > 0) {
                            foreach ($crmDelivery as $cdeliveryKey => $cdelivery) {
                                if (count($crmDelivery) > 0) {
                                    foreach ($cdelivery['lead_delivery_crm_default_fields'] as $dfield) {
                                        $defaultFields[$cdeliveryKey][] = $dfield;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            $fromzipdata = $this->getCampaignZipcodes($campaignId, "source");
            $tozipdata = $this->getCampaignZipcodes($campaignId, "destination");
            $status = 1;
            $message = "Success";
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        $data = [
            'status' => $status,
            'message' => $message,
            'emailDelivery' => $emailDelivery,
            'smsDelivery' => $smsDelivery,
            'crmDelivery' => $crmDelivery,
            'defaultFields' => $defaultFields,
            'fromzipdata' => $fromzipdata,
            'tozipdata' => $tozipdata
        ];
        //dd($data);
        return response()->json($data);
    }

    public function leadSeededPreview(Request $request) {
        $preview_query = $campaignemail_query = $campaignphone_query = $campaigncrmurl_query = $campaigndfield_query = '';
        unset($request['previewtext']);
        unset($request['_token']);
        unset($request['q']);

        if ($request['lead_category'] == 1) {
            unset($request['junk_type']);
            unset($request['junk_sub_type']);
            unset($request['heavy_lift_type']);
            unset($request['loading_assistance']);
            unset($request['is_operational']);
            unset($request['car_type']);
            unset($request['transport_type']);
            unset($request['vehicle_condition']);
        } else if ($request['lead_category'] == 2) {
            unset($request['move_size']);
            unset($request['heavy_lift_type']);
            unset($request['loading_assistance']);
            unset($request['is_operational']);
            unset($request['car_type']);
            unset($request['transport_type']);
            unset($request['vehicle_condition']);
        } else if ($request['lead_category'] == 3) {
            unset($request['move_size']);
            unset($request['junk_type']);
            unset($request['junk_sub_type']);
            unset($request['car_type']);
            unset($request['transport_type']);
            unset($request['vehicle_condition']);
        } else if ($request['lead_category'] == 4) {
            unset($request['move_size']);
            unset($request['junk_type']);
            unset($request['junk_sub_type']);
            unset($request['heavy_lift_type']);
            unset($request['loading_assistance']);
            unset($request['is_operational']);
        }
        foreach ($request->all() as $key => $value) {
            if ($key == 'campaignemail' || $key == 'campaignphone' || $key == 'campaigncrmurl' || $key == 'campaigndefaultFields') {
                continue;
            }
            $preview_query .= '&' . $key . '=' . $value;
        }
        if (array_key_exists("campaignemail", $request->all())) {
            foreach ($request['campaignemail'] as $key1 => $value1) {
                $campaignemail_query .= '&ldemail' . $key1 . '=' . $value1;
            }
        }
        if (array_key_exists("campaignphone", $request->all())) {
            foreach ($request['campaignphone'] as $key1 => $value1) {
                $campaignphone_query .= '&ldphone' . preg_replace('/[^A-Za-z0-9\-]/', '', $key1) . '=' . $value1;
            }
        }
        if (array_key_exists("campaigncrmurl", $request->all())) {
            foreach ($request['campaigncrmurl'] as $key1 => $value1) {
                $campaigncrmurl_query .= '&ldcrmurl' . $key1 . '=' . $value1;
            }
        }
        if (array_key_exists("campaigndefaultFields", $request->all())) {
            foreach ($request['campaigndefaultFields'] as $dkey => $dvalue) {
                foreach ($dvalue as $dkeyfield => $dvaluefield) {
                    $campaigndfield_query .= '&' . $dkeyfield . '=' . $dvaluefield;
                }
            }
        }
        $totalQuery = $preview_query . $campaignemail_query . $campaignphone_query . $campaigncrmurl_query . $campaigndfield_query;
        // echo "<pre>". print_r($totalQuery);
        // die;
        return $totalQuery;
    }

    public function insert(Request $request) {
        $status = 'Failure';
        $error = array();
        $leadId = 0;
        $code = 0;
        $lead_category = $request->get('lead_category');
        if ($lead_category == 2) {
            $rules = array(
                'business' => 'required',
                'campaign' => 'required',
                'name' => 'required',
                'email' => 'required|email',
                /* 'phone' => 'required|min:10|max:10', */
                'forward_number' => 'required|min:10|max:10',
                'from_zip' => 'required',
                'to_zip' => 'required',
                'move_date' => 'required|date',
                'junk_type' => 'required',
                'junk_sub_type' => 'required'
            );
            $input = $request->only(['campaign', 'business', 'name', 'email', 'phone', 'forward_number', 'to_zip', 'from_zip', 'move_date', 'junk_sub_type', 'junk_type']);
        } else if ($lead_category == 3) {
            $rules = array(
                'business' => 'required',
                'campaign' => 'required',
                'name' => 'required',
                'email' => 'required|email',
                /* 'phone' => 'required|min:10|max:10', */
                'forward_number' => 'required|min:10|max:10',
                'from_zip' => 'required',
                'to_zip' => 'required',
                'move_date' => 'required|date',
                'heavy_lift_type' => 'required',
                'loading_assistance' => 'required',
                'is_operational' => 'required'
            );
            $input = $request->only(['campaign', 'business', 'name', 'email', 'phone', 'forward_number', 'to_zip', 'from_zip', 'move_date', 'heavy_lift_type', 'loading_assistance', 'is_operational']);
        } else {
            $rules = array(
                'business' => 'required',
                'campaign' => 'required',
                'name' => 'required',
                'email' => 'required|email',
                /* 'phone' => 'required|min:10|max:10', */
                'forward_number' => 'required|min:10|max:10',
                'from_zip' => 'required',
                'to_zip' => 'required',
                'move_date' => 'required|date'
            );
            $input = $request->only(['campaign', 'business', 'name', 'email', 'phone', 'forward_number', 'to_zip', 'from_zip', 'move_date']);
        }
        $validator = Validator::make($input, $rules);
        if ($validator->fails()) {
            // return redirect('/leadtest')->withErrors($validator->errors());
            $error = $validator->getMessageBag()->toArray();
            $code = 1;
        } else {
            //dd( $validator->validated() );
            //search domestic numbers - state & rate center
            $commioNumber = $this->searchCommioNumber();
            if ($commioNumber->message == "success" && isset($commioNumber->dids[0])) {
                $did = $commioNumber->dids[0]->id;

                //purchase individual numbers
                $purchaseNumber = $this->purchaseCommioNumber($did);
                if ($purchaseNumber->status == "created" && $purchaseNumber->id) {
                    $orderId = $purchaseNumber->id;

                    //purchase complete order
                    $completeOrder = $this->completePurchaseCommioNumber($orderId);
                    if ($completeOrder->status == "completed") {

                        $postData = $request->all();
                        $originData = MstLeadSource::read('Test');
                        $tokenData = MstAPIToken::where("source", $originData->lead_source_name)->first();

                        $postData['origin'] = "Test(S)";
                        $postData['token'] = $tokenData->token;
                        $postData['IP'] = "127.0.0.1";
                        $postData['is_verified'] = "1";
                        $postData['generated_at'] = date('Y-m-d H:i:s');
                        $postData['phone'] = ( strlen($did) > 10 ) ? substr($did, 1, strlen($did)) : $did;
                        $postData['move_date'] = date("Y-m-d", strtotime($postData['move_date']));
                        $postData['lift_date'] = date("Y-m-d", strtotime($postData['move_date']));
                        $postData['heavy_lifting_type'] = $postData['heavy_lift_type'];
                        $postData['is_required_assistence'] = $postData['loading_assistance'];
                        $postData['is_operational'] = $postData['is_operational'];
                        $postData['junk_sub_type'] = implode(",", $postData['junk_sub_type']);
                        $postData['isLeadSeeded'] = 1;
                        //echo '<pre>';print_r($postData);die;

                        $postFields = trim(json_encode($postData, JSON_UNESCAPED_SLASHES));
                        $leadStorObj = new LeadInsert();
                        $response = $leadStorObj->InsertLead($postFields);
                        $response = json_decode($response, true);
                        //echo '<pre>';print_r($response); die;
                        if (isset($response['lead_id']) && $response['lead_id'] > 0) {

                            //Start For Lead Delivery API
                            $campaignId = $postData['campaign'];
                            $leadId = $response['lead_id'];
                            $leadPayout = $balance = $score = 0;
                            $jsonArray = array(
                                "campaign_id" => $campaignId,
                                "lead_id" => $leadId,
                                "cost" => $leadPayout,
                                "balance" => $balance,
                                "score" => $score
                            );
                            $leadDeliverApi = new LeadDeliveryApi();
                            $leadDeliverApi->leadDeliveryTemplate(json_encode($jsonArray));

                            //Added in lead_seeded
                            LeadSeeded::create([
                                'lead_id' => $leadId,
                                "campaign_id" => $campaignId,
                                'commio_number' => $did,
                                'forward_number' => $postData['forward_number'],
                                'user_id' => Auth::user()->id,
                                'created_at' => date('Y-m-d H:i:s'),
                            ]);

                            $status = 'success';
                            // return redirect('/leadlist');
                        } else {
                            if (isset($response['error'])) {
                                $code = 2;
                                $error[] = $response['error'];
                            }
                        }
                    } else {
                        $code = 2;
                        $error[] = 'Error during complete order.';
                    }
                } else {
                    $code = 2;
                    $error[] = 'Error during purchase number.';
                }
            } else {
                if (isset($commioNumber->error)) {
                    $code = 2;
                    $error[] = $commioNumber->error;
                } else {
                    $code = 2;
                    $error[] = 'No number found.';
                }
            }
        }
        $responseData = [
            'status' => $status,
            'error' => $error,
            'leadid' => $leadId,
            'code' => $code
        ];
        // echo '<pre>';print_r($responseData);die;
        return response()->json($responseData);
        // return redirect('/leadlist');
    }

    public function clean($string) {
        $string = str_replace(' ', '-', $string); // Replaces all spaces with hyphens.
        $str = preg_replace('/[^A-Za-z0-9\-]/', '', $string);
        $pattern = '/-/i';
        $str = preg_replace($pattern, '', $str);
        $str = chop($str, "-");
        return $str;
        // return preg_replace('/[^A-Za-z0-9\-]/', '', $string); // Removes special chars.
    }

    public function searchCommioNumber() {
        //$commioToken= Helper::checkServer()['general_variable_c'];
        $commioToken = "milan:68758d120ac29760793bb4c940550281ec4d51e6";
        $commioToken = base64_encode($commioToken); //Your authentication string is a base64 encoded version of username:token

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.thinq.com/inbound/get-numbers?searchType=domestic&searchBy=ratecenter&quantity=1&contiguous=false&state=CA&rateCenter=&related=true',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Authorization: Basic ' . $commioToken . ''
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);
        $response = json_decode($response);
        //echo "<pre>"; print_r($response); die;

        DevDebug::create([
            'sr_no' => 121,
            'result' => 'searchCommioNumber Request: ' . json_encode($response),
            'created_at' => date("Y-m-d H:i:s"),
        ]);
        return $response;
    }

    public function purchaseCommioNumber($did) {
        $accountId = 22374;
        $commioToken = "milan:68758d120ac29760793bb4c940550281ec4d51e6";
        $commioToken = base64_encode($commioToken); //Your authentication string is a base64 encoded version of username:token
        $postFields = [
            "order" => [
                "tns" => [
                    [
                        "caller_id" => null,
                        "account_location_id" => null,
                        "sms_routing_profile_id" => 688,
                        "route_id" => 13629,
                        "features" => [
                            "cnam" => false,
                            "sms" => true,
                            "e911" => false
                        ],
                        "did" => $did
                    ]
                ],
                "blocks" => []
            ],
        ];

        DevDebug::create([
            'sr_no' => 122,
            'result' => 'purchaseCommioNumber Request: ' . json_encode($postFields),
            'created_at' => date("Y-m-d H:i:s"),
        ]);

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.thinq.com/account/' . $accountId . '/origination/order/create',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($postFields),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Authorization: Basic ' . $commioToken . ''
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);
        $response = json_decode($response);
        //echo "<pre>"; print_r($response); die;

        DevDebug::create([
            'sr_no' => 123,
            'result' => 'purchaseCommioNumber Response: ' . json_encode($response),
            'created_at' => date("Y-m-d H:i:s"),
        ]);
        return $response;
    }

    public function completePurchaseCommioNumber($orderId) {
        $accountId = 22374;
        $commioToken = "milan:68758d120ac29760793bb4c940550281ec4d51e6";
        $commioToken = base64_encode($commioToken); //Your authentication string is a base64 encoded version of username:token
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.thinq.com/account/' . $accountId . '/origination/order/complete/' . $orderId,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Authorization: Basic ' . $commioToken . ''
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);
        $response = json_decode($response);
        //echo "<pre>"; print_r($response); die;
        DevDebug::create([
            'sr_no' => 124,
            'result' => 'completePurchaseCommioNumber Response: ' . json_encode($response),
            'created_at' => date("Y-m-d H:i:s"),
        ]);
        return $response;
    }

    public function getCampaignZipcodes($campaignId, $locationtype) {

        $status = 0;
        $message = 'fail';
        $data = [];
        $campaignDetail = $emailDelivery = $smsDelivery = $crmDelivery = $defaultFields = array();
        try {
            $campaignLocationsData = CampaignLocation::where('campaign_id', $campaignId)->where('location_type', $locationtype)->get()->toArray();
            $zipArr = $stateArr = $areacodeArr = [];
            if (count($campaignLocationsData) > 0) {
                foreach ($campaignLocationsData as $campaignLocation) {
                    if ($campaignLocation['location_coverage'] == 'zipcode') {
                        $zipArr[] = $campaignLocation['value'];
                    }
                    if ($campaignLocation['location_coverage'] == 'state') {
                        $stateArr[] = strtolower($campaignLocation['value']);
                    }
                    if ($campaignLocation['location_coverage'] == 'areacode') {
                        $areacodeArr[] = $campaignLocation['value'];
                    }
                }
            }
            $stateZipcode = MstZipcode::whereIn('state', $stateArr)->orWhereIn('areacode', $areacodeArr)->orWhereIn('zipcode', $zipArr)->groupBy('zipcode')->orderBy('zipcode', "ASC")->take(2000)->get(['zipcode', 'state', 'city', 'areacode'])->toArray();

            if (count($stateZipcode) > 0) {
                $data = $stateZipcode;
            }
            $status = 1;
            $message = "Success";
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $data = [
            'status' => $status,
            'message' => $message,
            'data' => $data,
            'locationtype' => $locationtype
        ];
        return $data;
        // return response()->json($data);
    }

    public function disconnectLeadSeededNumber(){
        $accountId = 22374;
        $commioToken = "SandeepAxin:10d7a0d9bf89f2207eb9e8617c851f4921bb7216";
        $commioToken = base64_encode($commioToken); // Your authentication string is a base64 encoded version of username:token
        $currentDate = date('Y-m-d H:i:s');
        //30 to 21 Day change by BK on 07/02/2024 as per discussed with DS
        $onlyDate = date('Y-m-d', strtotime($currentDate . '-21 days'));
        $getRecords = LeadSeeded::whereDate('created_at', '<=', $onlyDate)->where('status', 'active')->pluck('commio_number')->toArray();
        DevDebug::create(['sr_no' => 1, 'result' => '1 SnapIt Log disconnectCommioNumber Get number data: ' . json_encode($getRecords), 'created_at' => date("Y-m-d H:i:s")]);
        if (count($getRecords) > 0) {
            foreach ($getRecords as $number) {
                $postFields = [
                    "dids" => [$number]
                ];

                $curl = curl_init();
                    curl_setopt_array($curl, array(
                        CURLOPT_URL => 'https://api.thinq.com/account/' . $accountId . '/origination/disconnect',
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => '',
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'POST',
                        CURLOPT_POSTFIELDS => json_encode($postFields),
                        CURLOPT_HTTPHEADER => array(
                            'Content-Type: application/json',
                            'Authorization: Basic ' . $commioToken . ''
                        ),
                    ));

                    $response = curl_exec($curl);
                    curl_close($curl);
                    $response = json_decode($response);
                    if (isset($response->order->status) && strtolower(trim($response->order->status)) == 'completed') {
                        LeadSeeded::where('commio_number', $number)->update(['status' => 'inactive']);
                        $returnresponse = array(
                            'status' => 200,
                            'message' => 'Success',
                            'data' => json_encode($number),
                        );
                        DevDebug::create(['sr_no' => 2, 'result' => '2 SnapIt Log success disconnectCommioNumber response data: ' . json_encode($returnresponse), 'created_at' => date("Y-m-d H:i:s")]);
                    }else{
                        DevDebug::create(['sr_no' => 3, 'result' => '3 SnapIt Log fail disconnectCommioNumber response data: ' . json_encode($response), 'created_at' => date("Y-m-d H:i:s")]);
                    }
            }
        }else{
            $response = array(
                'status' => 200,
                'message' => 'There is no record from lead seeded table ' . $onlyDate,
            );
            DevDebug::create(['sr_no' => 4, 'result' => '4 SnapIt Log no record founc disconnectCommioNumber response data: ' . json_encode($response), 'created_at' => date("Y-m-d H:i:s")]);
        }

    }

    public function purchaseComioAreacode() {
        echo "Stop";
        die;
        $accountId = 22374;
        $commioToken = "milan:68758d120ac29760793bb4c940550281ec4d51e6";
        $commioToken = base64_encode($commioToken); //Your authentication string is a base64 encoded version of username:token
        $vmArray = array(832, 347, 480, 619, 702, 720, 210, 786, 512, 214, 916, 917, 407, 404, 760, 602, 817, 469, 314, 951, 510, 813, 909, 305, 317, 714, 904, 661, 919, 801, 757, 203, 954, 843, 719, 805, 612, 727, 503, 323, 541, 360, 678, 646, 910, 310, 614, 443, 303, 267, 773, 561, 408, 704, 630, 818, 717, 513, 206, 281, 253, 949, 208, 860, 816, 505, 770, 850, 248, 402, 908, 631, 516, 707, 209, 562, 703, 239, 330, 925, 901, 313, 609, 201, 732, 970, 315, 585, 415, 520, 864, 706, 708, 336, 615, 716, 603, 207, 406, 425);

        $qtmArray = array(480, 702, 720, 317, 904, 916, 404, 503, 314, 832, 407, 951, 214, 513, 530, 760, 210, 612, 512, 256, 813, 757, 850, 619, 817, 919, 707, 678, 469, 303, 510, 918, 360, 330, 954, 801, 714, 509, 727, 901, 786, 208, 408, 502, 602, 713, 909, 770, 815, 302);

        $isArray = array(720, 541, 407, 702, 786, 480, 317, 719, 208, 850, 503, 505, 843, 904, 601, 870, 970, 832, 803, 817, 217, 509, 207, 757, 806, 502, 801, 330, 360, 915, 920, 210, 530, 727, 405, 406, 469, 941, 561, 954, 205, 619, 254, 706, 918, 256, 404, 815, 910, 765);

        $vmArray = array(205, 619, 254, 706, 918, 256, 404, 815, 910, 765);
        for ($fd = 0; $fd < count($vmArray); $fd++) {
            $curl = curl_init();
            $url = "https://api.thinq.com/inbound/get-numbers?searchType=domestic&searchBy=npa&quantity=1&contiguous=false&npa=" . $vmArray[$fd] . "&rateCenter=&related=true";
            curl_setopt_array($curl, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'Authorization: Basic ' . $commioToken . ''
                ),
            ));
            $response = curl_exec($curl);
            curl_close($curl);
            $responseData = json_decode($response, true);
            if (isset($responseData['dids'][0]['id'])) {
                $did = $responseData['dids'][0]['id'];
                $postFields = [
                    "order" => [
                        "tns" => [
                            [
                                "caller_id" => null,
                                "account_location_id" => null,
                                "sms_routing_profile_id" => 699,
                                "route_id" => 13720,
                                "features" => [
                                    "cnam" => false,
                                    "sms" => true,
                                    "e911" => false
                                ],
                                "did" => $did
                            ]
                        ],
                        "blocks" => []
                    ],
                ];
                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.thinq.com/account/' . $accountId . '/origination/order/create',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => json_encode($postFields),
                    CURLOPT_HTTPHEADER => array(
                        'Content-Type: application/json',
                        'Authorization: Basic ' . $commioToken . ''
                    ),
                ));

                $purData = curl_exec($curl);
                curl_close($curl);
                $purchaseNumberData = json_decode($purData, true);
                if (isset($purchaseNumberData['status']) && trim($purchaseNumberData['status']) == "created") {
                    $orderId = $purchaseNumberData['id'];
                    $curl = curl_init();
                    curl_setopt_array($curl, array(
                        CURLOPT_URL => 'https://api.thinq.com/account/' . $accountId . '/origination/order/complete/' . $orderId,
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => '',
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'POST',
                        CURLOPT_HTTPHEADER => array(
                            'Content-Type: application/json',
                            'Authorization: Basic ' . $commioToken . ''
                        ),
                    ));
                    $orderData = curl_exec($curl);
                    curl_close($curl);
                    $finalCall = json_decode($orderData, true);
                    if (isset($finalCall['status']) && trim($finalCall['status']) == "completed") {
                        $insertDataArr = array();
                        $insertDataArr['phone'] = $did;
                        $insertDataArr['callit_number'] = substr($did, 1, 3) . " " . substr($did, 4, 3) . "-" . substr($did, 7, 4);
                        $insertDataArr['lead_source_id'] = 36;
                        $insertDataArr['is_current'] = "yes";
                        $insertDataArr['day'] = 0;
                        $insertDataArr['is_sms'] = "yes";
                        $insertDataArr['lead_category_id'] = 1;
                        $insertDataArr['comment'] = "Commio IS OB";
                        $insertDataArr['status'] = "active";
                        $insertDataArr['phone_source'] = "OB";
                        $insertDataArr['provider'] = "commio";
                        $insertDataArr['created_at'] = date("Y-m-d H:i:s");
                        echo $did . "," . $vmArray[$fd] . ",36<br>";
                        //echo "<pre>";print_r($insertDataArr);die;
                        LeadCallNumber::create($insertDataArr);
                        //echo "<pre>";print_r($insertDataArr);
                        //echo "<br>";
                    } else {
                        echo $vmArray[$fd] . "== 3 Order Not found<br>";
                    }
                } else {
                    echo $vmArray[$fd] . "== 2 Purchase Not found<br>";
                }
            } else {
                echo $vmArray[$fd] . "== 1 Search Not found<br>";
            }
        }
        echo "Done";
        die;
    }

    public function OBIncomoingSmsCommio(Request $request) {
        $parameters1 = $_REQUEST;
        DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => '1 SnapIt Log OBIncomoingSmsCommio = ' . json_encode($parameters1)]);
        try {
            $from = trim($request['from']);
            $txt = trim($request['message']);
            $to = trim($request['to']);
            $fromNumber = substr($from, 1, 10);
            $message = "To : " . $to . "From : " . $from . " Text = " . $txt;
            DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => '2 SnapIt Log OBIncomoingSmsCommio  -' . $from . "====" . $to . "===" . strtoupper($txt) . "==" . $message . "===" . $fromNumber]);
            if (strpos(strtoupper($txt), 'STOP') !== false) {
                $checkLead = Lead::where('phone', $fromNumber)->get()->toArray();
                $dncStartDate = date("Y-m-d H:i:s", strtotime("-90 days"));
                $dncEndDate = date("Y-m-d H:i:s");
                DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => '3 SnapIt Log OBIncomoingSmsCommio Lead Data  -' . json_encode($checkLead) . "==" . $dncStartDate . "==" . $dncEndDate . "==" . $fromNumber]);
                if (count($checkLead) > 0) {
                    LeadCallBuffer::where('customer_number', $fromNumber)->delete();
                    LeadOldCallBuffer::where('customer_number', $fromNumber)->delete();
                    LeadFollowUp::where('cust_phone_number', $fromNumber)->delete();
                    Lead::where('phone', $fromNumber)->whereBetween('created_at', [$dncStartDate, $dncEndDate])->update(['is_dnc_call' => 'yes', 'is_dnc_sms' => 'yes', 'is_dnc_email' => 'yes']);
                    $leadscalldnc = Lead::where('phone', $fromNumber)->whereBetween('created_at', [$dncStartDate, $dncEndDate])->where('is_dnc_call', 'yes')->get()->toArray();
                    DB::table('dev_debug')->insert(['sr_no' => 4, 'result' => '4 SnapIt Log OBIncomoingSmsCommio dncemailsend Data  -' . json_encode($leadscalldnc) . "==" . $dncStartDate . "==" . $dncEndDate . "==" . $fromNumber]);
                    $leadListControllerobj = new LeadListController();
                    foreach ($leadscalldnc as $key => $value) {
                        $leadListControllerobj->dncemailsend($value['lead_id'], "yes", "yes", "yes");
                    }
                }
            }
            $smsData = array();
            $smsData['provider'] = 'commio';
            $smsData['from_number'] = $from;
            $smsData['to_number'] = $to;
            $smsData['sms_body'] = $txt;
            $smsData['sms_type'] = "ob";
            $smsData['created_at'] = date("Y-m-d H:i:s");
            ReceivedSMS::create($smsData);
            $responseData = ['status' => "Success"];
            DB::table('dev_debug')->insert(['sr_no' => 5, 'result' => '5 SnapIt Log OBIncomoingSmsCommio ReceivedSMS Data  -' . json_encode($smsData) . "==" . json_encode($responseData)]);
        } catch (Exception $ex) {
            $message = $ex->getMessage();
            Log::info("6 SnapIt Log OBIncomoingSmsCommio Catch error " . $message);
            $responseData = ['status' => $message];
            DB::table('dev_debug')->insert(['sr_no' => 6, 'result' => '6 SnapIt Log OBIncomoingSmsCommio Catch error  -' . json_encode($responseData)]);
        }
        return response()->json($responseData);
    }

    public function OBIncomoingSmsPlivo(Request $request) {
        $parameters1 = $_REQUEST;
        DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => '1 SnapIt Log OBIncomoingSmsPlivo = ' . json_encode($parameters1)]);
        try {
            $from = trim($request['From']);
            $txt = trim($request['Text']);
            $to = trim($request['To']);
            $fromNumber = substr($from, 1, 10);
            $message = "To : " . $to . "From : " . $from . " Text = " . $txt;
            DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => '2 SnapIt Log OBIncomoingSmsPlivo  -' . $from . "====" . $to . "===" . strtoupper($txt) . "==" . $message . "===" . $fromNumber]);
            if (strpos(strtoupper($txt), 'STOP') !== false) {
                $checkLead = Lead::where('phone', $fromNumber)->get()->toArray();
                $dncStartDate = date("Y-m-d H:i:s", strtotime("-90 days"));
                $dncEndDate = date("Y-m-d H:i:s");
                DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => '3 SnapIt Log OBIncomoingSmsPlivo Lead Data  -' . json_encode($checkLead) . "==" . $dncStartDate . "==" . $dncEndDate . "==" . $fromNumber]);
                if (count($checkLead) > 0) {
                    LeadCallBuffer::where('customer_number', $fromNumber)->delete();
                    LeadOldCallBuffer::where('customer_number', $fromNumber)->delete();
                    LeadFollowUp::where('cust_phone_number', $fromNumber)->delete();
                    Lead::where('phone', $fromNumber)->whereBetween('created_at', [$dncStartDate, $dncEndDate])->update(['is_dnc_sms' => 'yes']);
                    $leadscalldnc = Lead::where('phone', $fromNumber)->whereBetween('created_at', [$dncStartDate, $dncEndDate])->where('is_dnc_call', 'yes')->get()->toArray();
                    DB::table('dev_debug')->insert(['sr_no' => 4, 'result' => '4 SnapIt Log OBIncomoingSmsPlivo dncemailsend Data  -' . json_encode($leadscalldnc) . "==" . $dncStartDate . "==" . $dncEndDate . "==" . $fromNumber]);
                    $leadListControllerobj = new LeadListController();
                    foreach ($leadscalldnc as $key => $value) {
                        $leadListControllerobj->dncemailsend($value['lead_id'], "yes", "yes", "yes");
                    }
                }
            }
            $smsData = array();
            $smsData['provider'] = 'plivo';
            $smsData['from_number'] = $from;
            $smsData['to_number'] = $to;
            $smsData['sms_body'] = $txt;
            $smsData['sms_type'] = "ob";
            $smsData['created_at'] = date("Y-m-d H:i:s");
            ReceivedSMS::create($smsData);
            $responseData = ['status' => "Success"];
            DB::table('dev_debug')->insert(['sr_no' => 5, 'result' => '5 SnapIt Log OBIncomoingSmsCommio ReceivedSMS Data  -' . json_encode($smsData) . "==" . json_encode($responseData)]);
        } catch (Exception $ex) {
            $message = $ex->getMessage();
            Log::info("6 SnapIt Log OBIncomoingSmsCommio Catch error " . $message);
            $responseData = ['status' => $message];
            DB::table('dev_debug')->insert(['sr_no' => 6, 'result' => '6 SnapIt Log OBIncomoingSmsCommio Catch error  -' . json_encode($responseData)]);
        }
        return response()->json($responseData);
    }

    public function insertAreacode() {
        echo "Stop";
        die;
        $areacodeArr = array(832, 347, 480, 619, 702, 720, 210, 786, 512, 214, 916, 917, 407, 404, 760, 602, 817, 469, 314, 951, 510, 813, 909, 305, 317, 714, 904, 661, 919, 801, 757, 203, 954, 843, 719, 805, 612, 727, 503, 323, 541, 360, 678, 646, 910, 310, 614, 443, 303, 267, 773, 561, 408, 704, 630, 818, 717, 513, 206, 281, 253, 949, 208, 860, 816, 505, 770, 850, 248, 402, 908, 631, 516, 707, 209, 562, 703, 239, 330, 925, 901, 313, 609, 201, 732, 970, 315, 585, 415, 520, 864, 706, 708, 336, 615, 716, 603, 207, 406, 425, 470, 215, 808, 803, 301, 321, 504, 414, 509, 518, 815, 302, 405, 352, 973, 240, 804, 502, 610, 559, 254, 412, 847, 419, 205, 608, 734, 713, 417, 724, 256, 623, 845, 571, 903, 601, 202, 540, 929, 626, 304, 484, 718, 918, 312, 530, 775, 863, 401, 912, 828, 440, 515, 907, 913, 781, 650, 831, 941, 423, 617, 856, 319, 410, 217, 346, 618, 682, 928, 224, 972, 920, 956, 216, 812, 858, 318, 937, 252, 573, 508, 859, 978, 386, 334, 772, 262, 616, 765, 662, 814, 607, 914, 501, 570, 915, 740, 413, 385, 651, 971, 785, 337, 605, 787, 574, 409, 229, 269, 940, 517, 225, 270, 865, 213, 309, 763, 424, 862, 219, 806, 774, 715, 830, 857, 307, 575, 231, 701, 260, 507, 737, 989, 316, 636, 810, 985, 870, 251, 979, 478, 802, 218, 361, 936, 931, 952, 432, 712, 725, 435, 586, 999, 980, 754, 228, 212, 563, 606, 551, 580, 325, 479, 442, 689, 434, 331, 320, 660, 620, 848, 984, 276, 234, 531, 669, 641, 747, 872, 731, 567, 779, 475, 308, 939, 339, 945, 906, 332, 458, 888, 223, 833, 629, 656, 667, 681, 838, 769, 272, 340, 463, 762, 800, 986, 108, 341, 840, 445, 539, 657, 659, 726, 854, 525, 220, 279, 528, 680, 429, 572, 809, 824, 987, 0, 282, 431, 555, 564, 868, 947, 576, 441, 529, 533, 743, 777, 829, 200, 416, 430, 524, 628, 745, 839, 844, 943, 959, 255, 491, 671, 780, 878, 999, 364, 866, 876, 326, 771, 500, 522, 820, 930, 380, 448, 480, 702, 720, 317, 904, 916, 404, 503, 314, 832, 407, 951, 214, 513, 530, 760, 210, 612, 512, 256, 813, 757, 850, 619, 817, 919, 707, 678, 469, 303, 510, 918, 360, 330, 954, 801, 714, 509, 727, 901, 786, 208, 408, 502, 602, 713, 909, 770, 815, 302, 321, 540, 646, 970, 207, 336, 917, 615, 630, 281, 443, 805, 347, 937, 541, 301, 601, 215, 402, 843, 470, 803, 806, 505, 520, 716, 240, 248, 706, 865, 631, 719, 406, 717, 412, 845, 561, 763, 804, 319, 501, 304, 949, 352, 419, 305, 307, 703, 773, 816, 239, 252, 704, 585, 614, 661, 740, 209, 415, 570, 253, 903, 847, 910, 315, 440, 507, 603, 860, 217, 425, 925, 417, 334, 479, 313, 309, 508, 912, 310, 409, 423, 708, 518, 608, 724, 734, 814, 765, 913, 202, 623, 701, 928, 206, 808, 818, 218, 225, 405, 205, 559, 262, 414, 609, 203, 267, 323, 812, 931, 863, 775, 864, 573, 650, 251, 605, 941, 337, 718, 386, 516, 859, 346, 626, 915, 410, 651, 870, 254, 810, 216, 270, 662, 312, 914, 920, 617, 618, 231, 219, 361, 830, 978, 484, 802, 972, 989, 504, 571, 907, 224, 828, 213, 269, 682, 401, 610, 316, 586, 971, 385, 616, 715, 562, 575, 772, 785, 979, 936, 858, 201, 435, 607, 580, 478, 260, 725, 517, 515, 985, 318, 413, 731, 732, 641, 940, 620, 689, 831, 908, 432, 781, 308, 660, 424, 774, 320, 737, 228, 636, 442, 952, 820, 856, 531, 980, 434, 574, 862, 973, 276, 563, 606, 929, 325, 567, 848, 613, 669, 857, 984, 331, 445, 712, 212, 872, 906, 939, 278, 787, 551, 779, 901, 229, 754, 963, 463, 624, 999, 474, 539, 747, 955, 441, 659, 752, 888, 264, 430, 458, 868, 885, 887, 956, 986, 989, 283, 351, 475, 667, 925, 234, 403, 681, 719, 771, 833, 834, 912, 999, 448, 838, 854, 928, 949, 417, 616, 769, 332, 657, 743, 800, 908, 945, 720, 541, 407, 702, 786, 480, 317, 719, 208, 850, 503, 505, 843, 904, 601, 870, 970, 832, 803, 817, 217, 509, 207, 757, 806, 502, 801, 330, 360, 915, 920, 210, 530, 727, 405, 406, 469, 941, 561, 954, 205, 619, 254, 706, 918, 256, 404, 815, 910, 765, 804, 678, 760, 863, 307, 661, 717, 816, 304, 575, 917, 740, 352, 443, 520, 715, 423, 402, 512, 216, 513, 540, 585, 956, 614, 570, 662, 305, 901, 919, 209, 704, 937, 347, 309, 214, 419, 903, 314, 386, 608, 775, 864, 270, 573, 928, 318, 501, 315, 518, 865, 336, 813, 602, 701, 805, 267, 615, 229, 484, 612, 417, 316, 707, 781, 860, 951, 253, 281, 708, 716, 912, 269, 515, 828, 228, 337, 909, 334, 303, 479, 559, 623, 931, 203, 504, 313, 508, 616, 346, 385, 724, 574, 620, 202, 916, 925, 929, 605, 215, 814, 470, 770, 252, 646, 859, 734, 319, 609, 712, 239, 251, 562, 607, 812, 978, 507, 413, 510, 517, 732, 802, 408, 412, 618, 240, 260, 320, 321, 323, 361, 440, 913, 610, 432, 703, 218, 401, 606, 641, 773, 225, 603, 714, 731, 989, 231, 219, 435, 276, 936, 414, 713, 857, 908, 971, 409, 636, 858, 682, 262, 302, 810, 516, 580, 985, 224, 325, 631, 718, 424, 617, 651, 774, 845, 856, 308, 442, 906, 248, 478, 551, 626, 763, 808, 310, 725, 630, 940, 567, 979, 571, 660, 984, 425, 434, 831, 847, 949, 973, 772, 312, 862, 907, 762, 410, 586, 531, 206, 657, 848, 201, 650, 779, 818, 785, 914, 754, 972, 980, 415, 563, 787, 407, 234, 405, 213, 689, 737, 301, 830, 458, 952, 204, 380, 331, 872, 404, 667, 402, 412, 553, 332, 408, 681, 942, 999, 564, 945, 475, 544, 647, 736, 833, 921, 384, 433, 548, 839, 220, 680, 851, 889, 938, 223, 249, 272, 403, 629, 800, 809, 939, 948, 999, 514, 927, 250, 409, 416, 406, 778, 867, 279, 418, 430, 705, 721, 726, 769, 840, 539, 628, 986);
        //echo count($areacodeArr);die;
        $numberArr = array(13464611651, 15168631221, 14254755751, 16198261092, 17259994043, 17209181188, 18302920946, 17869018862, 12542842117, 13464611652, 19169549960, 15168631239, 14079049282, 14703158229, 17603959021, 14068510896, 18178353004, 14695522171, 13148869840, 19515643481, 15104051989, 18138618936, 15625613166, 17543566768, 14632519735, 15625540607, 19047697833, 16614522797, 13365697034, 13853836140, 17576995046, 14752861829, 15618940764, 18436334294, 17194352973, 18055072286, 15079680317, 17274722930, 19714124435, 12137240042, 15413763408, 13605252443, 14703158311, 15168631243, 19104064122, 12137240098, 12206660083, 14434128838, 17209181189, 12674336219, 17737296808, 15618788634, 15104051990, 18036812331, 12243943973, 12137240102, 17174019099, 15135475156, 12534091516, 13464611661, 12533564989, 19497730918, 12085037539, 18605442488, 18164770639, 15054815604, 14703158357, 18502773096, 12489566391, 14024070003, 19084498157, 16314582090, 15168631253, 17073369957, 12097061958, 15625540651, 12406603191, 12394399857, 13309913964, 19252691889, 19015881774, 13135149108, 16096290768, 15513290697, 17328026927, 19704476071, 13153001425, 15855029850, 13412038923, 15205069271, 18646612483, 17065671325, 17085232127, 13365697035, 16158191590, 17163176668, 16037308166, 12075355401, 14068510896, 14254755751, 12075355401, 16037308166, 17163176668, 16158191590, 13365697035, 17085232127, 17065671325, 18646612483, 15205069271, 13412038923, 15855029850, 13153001425, 19704476071, 19252691889, 19015881774, 13135149108, 16096290768, 15513290697, 17328026927, 16314582090, 15168631253, 17073369957, 12097061958, 15625540651, 12406603191, 12394399857, 13309913964, 12085037539, 18605442488, 18164770639, 15054815604, 14703158357, 18502773096, 12489566391, 14024070003, 19084498157, 15104051990, 18036812331, 12243943973, 12137240102, 17174019099, 15135475156, 12534091516, 13464611661, 12533564989, 19497730918, 19104064122, 12137240098, 12206660083, 14434128838, 17209181189, 12674336219, 17737296808, 15618788634, 15618940764, 18436334294, 17194352973, 18055072286, 15079680317, 17274722930, 19714124435, 12137240042, 15413763408, 13605252443, 14703158311, 15168631243, 15625613166, 17543566768, 14632519735, 15625540607, 19047697833, 16614522797, 13365697034, 13853836140, 17576995046, 14752861829, 18178353004, 14695522171, 13148869840, 19515643481, 15104051989, 18138618936, 19169549960, 15168631239, 14079049282, 14703158229, 17603959021, 16198261092, 17259994043, 17209181188, 18302920946, 17869018862, 12542842117, 13464611652, 13412038923, 15205069271, 18646612483, 17065671325, 17085232127, 13365697035, 16158191590, 17163176668, 16037308166, 12075355401, 19252691889, 19015881774, 13135149108, 16096290768, 15513290697, 17328026927, 19704476071, 13153001425, 15855029850, 15054815604, 14703158357, 18502773096, 12489566391, 14024070003, 19084498157, 16314582090, 15168631253, 17073369957, 12097061958, 15625540651, 12406603191, 12394399857, 13309913964, 17174019099, 15135475156, 12534091516, 13464611661, 12533564989, 19497730918, 12085037539, 18605442488, 18164770639, 14024070003, 19084498157, 16314582090, 15168631253, 17073369957, 12097061958, 15625540651, 12406603191, 12394399857, 13309913964, 17174019099, 15135475156, 12534091516, 13464611661, 12533564989, 19497730918, 16158191590, 17163176668, 16037308166, 12075355401, 19252691889, 19015881774, 13135149108, 16096290768, 15513290697, 17328026927, 19704476071, 13153001425, 15855029850, 15054815604, 14703158357, 18502773096, 12489566391, 12206660083, 14434128838, 17209181189, 12674336219, 17737296808, 15618788634, 15104051990, 18036812331, 12243943973, 12137240102, 13153001425, 15855029850, 13412038923, 13153001425, 15855029850, 13412038923, 13153001425, 15855029850, 13412038923, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 19015881774, 13135149108, 16096290768, 19252691889, 16096290768, 17259994056, 17209181194, 14632519736, 19047697860, 19169549963, 14703158359, 19714124436, 13148869841, 13464611667, 14079049284, 19515643482, 13464611668, 15135475157, 15302904610, 17603959022, 18302920948, 15079680325, 12542842118, 12563347880, 18138618951, 17576995246, 18502773102, 16198261251, 18178353005, 13365697036, 17073369961, 14703158371, 14695522173, 17209181199, 15104051992, 19183722709, 13605259602, 13309913970, 15618940804, 13853836142, 15625540661, 15093482678, 17274722942, 19015881792, 17869018890, 12085037580, 15104051995, 15024808271, 13023859174, 13464611672, 15625663048, 14703158378, 18153314827, 13023859174, 18502773102, 16198261251, 18178353005, 13365697036, 17073369961, 14703158371, 14695522173, 17209181199, 15104051992, 19183722709, 13605259602, 13309913970, 15618940804, 13853836142, 15625540661, 15093482678, 17274722942, 19015881792, 17869018890, 12085037580, 15104051995, 15024808271, 13023859174, 13464611672, 15625663048, 14703158378, 18138618951, 18138618951, 18138618951, 18138618951, 18138618951, 12563347880, 12563347880, 12563347880, 12563347880, 12563347880, 12563347880, 12563347880, 12542842118, 12542842118, 12542842118, 12542842118, 12542842118, 12542842118, 12542842118, 15079680325, 15079680325, 15079680325, 15079680325, 15079680325, 15079680325, 15079680325, 18302920948, 18302920948, 18302920948, 18302920948, 18302920948, 18302920948, 18302920948, 17603959022, 17603959022, 17603959022, 17603959022, 17603959022, 17603959022, 17603959022, 15302904610, 15302904610, 15302904610, 15302904610, 15302904610, 15302904610, 15302904610, 15135475157, 15135475157, 15135475157, 15135475157, 15135475157, 15135475157, 15135475157, 15135475157, 13464611668, 13464611668, 13464611668, 13464611668, 13464611668, 13464611668, 13464611668, 13464611668, 19515643482, 19515643482, 19515643482, 19515643482, 19515643482, 19515643482, 19515643482, 19515643482, 14079049284, 14079049284, 14079049284, 14079049284, 14079049284, 14079049284, 14079049284, 14079049284, 14079049284, 13464611667, 13464611667, 13464611667, 13464611667, 13464611667, 13464611667, 13464611667, 13464611667, 13464611667, 13148869841, 13148869841, 13148869841, 13148869841, 13148869841, 13148869841, 13148869841, 13148869841, 13148869841, 13148869841, 19714124436, 19714124436, 19714124436, 19714124436, 19714124436, 19714124436, 19714124436, 19714124436, 19714124436, 14703158359, 14703158359, 14703158359, 14703158359, 14703158359, 14703158359, 14703158359, 14703158359, 14703158359, 14703158359, 19169549963, 19169549963, 19169549963, 19169549963, 19169549963, 19169549963, 19169549963, 19169549963, 19169549963, 19169549963, 19169549963, 19169549963, 19047697860, 19047697860, 19047697860, 19047697860, 19047697860, 19047697860, 19047697860, 19047697860, 19047697860, 19047697860, 19047697860, 14632519736, 14632519736, 14632519736, 14632519736, 14632519736, 14632519736, 14632519736, 14632519736, 14632519736, 14632519736, 14632519736, 14632519736, 17209181194, 17209181194, 17209181194, 17209181194, 17209181194, 17209181194, 17209181194, 17209181194, 17209181194, 17209181194, 17209181194, 17209181194, 17209181194, 17209181194, 17209181194, 17209181194, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 17259994056, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 16096290768, 17209181200, 15413763411, 14079049287, 17259995101, 17869018892, 17653075306, 14632519737, 17194352986, 12085037594, 18502773104, 19714124818, 15054815606, 18436334369, 19047697862, 16014392221, 18705518652, 19704476077, 13464611673, 18036743942, 18178353015, 12173015008, 15093482696, 12075355404, 17576995247, 18063296227, 15024808291, 13853836143, 13309913975, 13605259915, 19153062327, 19203083596, 18302920949, 15302904611, 17274722974, 14054482666, 14068510897, 14695522174, 19414400907, 15618788640, 15618940807, 12057549041, 16198263035, 12542831683, 17065671329, 19183722753, 12563347886, 14703158389, 18153314832, 19104064138, 17653075306, 17209181200, 15413763411, 14079049287, 17259995101, 17869018892, 17653075306, 14632519737, 17194352986, 12085037594, 18502773104, 19714124818, 15054815606, 18436334369, 19047697862, 16014392221, 18705518652, 19704476077, 13464611673, 18036743942, 18178353015, 12173015008, 15093482696, 12075355404, 17576995247, 18063296227, 15024808291, 13853836143, 13309913975, 13605259915, 19153062327, 19203083596, 18302920949, 15302904611, 17274722974, 14054482666, 14068510897, 14695522174, 19414400907, 15618788640, 15618940807, 12057549041, 16198263035, 12542831683, 17209181200, 15413763411, 14079049287, 17259995101, 17869018892, 17653075306, 14632519737, 17194352986, 12085037594, 18502773104, 19714124818, 15054815606, 18436334369, 19047697862, 16014392221, 18705518652, 19704476077, 13464611673, 18036743942, 18178353015, 12173015008, 15093482696, 12075355404, 17576995247, 18063296227, 15024808291, 13853836143, 13309913975, 13605259915, 19153062327, 19203083596, 18302920949, 15302904611, 17274722974, 14054482666, 14068510897, 14695522174, 19414400907, 15618788640, 15618940807, 12057549041, 16198263035, 12542831683, 13853836143, 13309913975, 13605259915, 19153062327, 19203083596, 18302920949, 15302904611, 17274722974, 14054482666, 14068510897, 14695522174, 19414400907, 15618788640, 15618940807, 12057549041, 16198263035, 12542831683, 15618788640, 15618940807, 12057549041, 16198263035, 12542831683, 14068510897, 14695522174, 17065671329, 17065671329, 17065671329, 17065671329, 17065671329, 17065671329, 17065671329, 17065671329, 17065671329, 17065671329, 19183722753, 19183722753, 19183722753, 19183722753, 19183722753, 19183722753, 19183722753, 19183722753, 19183722753, 19183722753, 19183722753, 12563347886, 12563347886, 12563347886, 12563347886, 12563347886, 12563347886, 12563347886, 12563347886, 12563347886, 12563347886, 14703158389, 14703158389, 14703158389, 14703158389, 14703158389, 14703158389, 14703158389, 14703158389, 14703158389, 14703158389, 14703158389, 14703158389, 14703158389, 18153314832, 18153314832, 18153314832, 18153314832, 18153314832, 18153314832, 18153314832, 18153314832, 18153314832, 18153314832, 18153314832, 18153314832, 18153314832, 18153314832, 18153314832, 19104064138, 19104064138, 19104064138, 19104064138, 19104064138, 19104064138, 19104064138, 19104064138, 19104064138, 19104064138, 19104064138, 19104064138, 19104064138, 19104064138, 19104064138, 19104064138, 17653075306, 17653075306, 17653075306, 17653075306, 17653075306, 17653075306, 17653075306, 17653075306, 17653075306, 17653075306, 17653075306, 17653075306, 17653075306, 17653075306, 17653075306, 17653075306, 17653075306, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 15413763411, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200, 17209181200);
        //echo count($numberArr);die;
        $sourceArr = array(19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 14, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36, 36);
        //echo count($sourceArr);die;
        $finalDataArr = array();
        for ($fg = 0; $fg < count($numberArr); $fg++) {
            $newArr = array();
            $newArr['phone'] = $numberArr[$fg];
            $newArr['callit_number'] = substr($numberArr[$fg], 1, 3) . " " . substr($numberArr[$fg], 4, 3) . "-" . substr($numberArr[$fg], 7, 4);
            $newArr['areacode'] = $areacodeArr[$fg];
            $newArr['lead_source_id'] = $sourceArr[$fg];
            $sourceName = "VM";
            if ($sourceArr[$fg] == 14) {
                $sourceName = "QTM";
            } else if ($sourceArr[$fg] == 36) {
                $sourceName = "IS";
            }
            $newArr['status'] = "active";
            $newArr['Comments'] = "Commion " . $sourceName . " OB";
            $newArr['is_deleted'] = "no";
            $newArr['created_at'] = date("Y-m-d H:i:s");
            LeadCallNumberAreacode::create($newArr);
            $finalDataArr[] = $newArr;
        }
        echo "<pre>";
        print_r($finalDataArr);
        echo "<br>Done";
        die;
    }

    public function releasecomionumber(Request $request) {

        $lead_seeded_id = $request['lead_seeded_id'];
        $endDecObj = new CommonFunctions;
        $lead_seeded_id = $endDecObj->customeDecryption($lead_seeded_id);

        $accountId = 22374;
        $commioToken = "milan:68758d120ac29760793bb4c940550281ec4d51e6";
        $commioToken = base64_encode($commioToken); // Your authentication string is a base64 encoded version of username:token
        $getRecords = LeadSeeded::where('lead_id', $lead_seeded_id)->pluck('commio_number')->toArray();

        //echo "<pre>"; print_r($getRecords); exit();
        $response = 'success';
        // Log::info(json_encode($getRecords));
        if (count($getRecords) > 0) {

            $postFields = [
                "dids" => $getRecords
            ];
            //echo "<pre>"; print_r($postFields); die();
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://api.thinq.com/account/' . $accountId . '/origination/disconnect',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode($postFields),
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'Authorization: Basic ' . $commioToken . ''
                ),
            ));

            $response = curl_exec($curl);
            curl_close($curl);
            $response = json_decode($response);
            // echo "<pre>"; print_r($response); die;
            //return $response;
            if (isset($response->order->status) && strtolower(trim($response->order->status)) == 'completed') {
                LeadSeeded::whereIn('commio_number', $getRecords)->update(['status' => 'inactive']);

                $data = array(
                    'status' => 1,
                    'message' => 'Success',
                    'data' => json_encode($postFields),
                );
            }else{
                $data = array(
                    'status' => 0,
                    'message' => 'Error',
                    'data' => json_encode($postFields),
                );
            }

            DevDebug::create([
                'sr_no' => 125,
                'result' => 'disconnectCommioNumber Response: ' . json_encode($response),
                'created_at' => date("Y-m-d H:i:s"),
            ]);
        } else {
            $data = array(
                'status' => 0,
                'message' => 'There is no record from lead seeded table ' . $onlyDate,
            );
        }

        return response()->json($data);
    }
    public function addObNumberInTable(Request $request) {

        $oldNumber = $request->query("oldnumber");
        $newNumberwithone = $newNumber = $request->query("newnumber");


        if (strlen($newNumber) === 11 && $newNumber[0] === '1') {
            $newNumber = substr($newNumber, 1);
        }

        // Format to 'XXX XXX-XXXX'
        $formatted = substr($newNumber, 0, 3) . ' ' .substr($newNumber, 3, 3) . '-' .substr($newNumber, 6);

        DB::statement("INSERT INTO lead_call_numbers_areacode (phone, callit_number, areacode, lead_source_id, Comments) SELECT ?, ?, areacode, lead_source_id, Comments FROM lead_call_numbers_areacode WHERE phone = ? ", [$newNumberwithone, $formatted, $oldNumber]);


        DB::statement("UPDATE lead_call_numbers_areacode SET STATUS = 'inactive', is_deleted = 'yes' WHERE phone = ? ", [$oldNumber]);

        DB::statement("UPDATE lead_call_numbers_areacode SET STATUS = 'active', is_deleted = 'no' WHERE phone = ? ", [$newNumberwithone]);


        DB::statement("INSERT INTO lead_call_number(phone, callit_number, lead_source_id, is_current, DAY, is_sms, lead_category_id, comment, status, phone_source, provider, number_type)SELECT ?, ?, lead_source_id, is_current, DAY, is_sms, lead_category_id, comment, status, phone_source, provider, number_type FROM lead_call_number WHERE lead_call_number_id = (SELECT lead_call_number_id FROM lead_call_number WHERE phone = ?)", [$newNumberwithone, $formatted, $oldNumber]);



    }

}
