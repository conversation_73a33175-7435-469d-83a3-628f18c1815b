<?php

namespace App\Http\Controllers\Emails;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Emails\TemplateEmail;
use App\Exports\VanguardLeadExport;
use App\Exports\VanguardCallExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Emails\EmailLog;

use PDF;
use Exception;
use DB;
use Log;

class PrimeMarketingVanguardInvoiceEmailController extends Controller
{
    public function primeVanguardInvoiceEmailSend()
    {
        // Store on a different disk (e.g. s3)
        $startDate      = date("Y-m-d", mktime(0, 0, 0, date("m")-1, 1));
        $endDate        = date("Y-m-d", mktime(0, 0, 0, date("m"), 0));
        $startDate      = '2024-05-08';
        $endDate        = '2024-08-15';
        $month          = date('F', strtotime(date('F') . " last month"));
        $year           = date('Y', strtotime(date('Y') . " last month"));
        $csvfileName    = 'VanguardMovingServicesLead_'.$month.'_'.$year.'.csv';
        $csvfileName1   = 'VanguardMovingServicesCall_'.$month.'_'.$year.'.csv';
        $pdffileName    = 'VanguardMovingServices_'.$month.'_'.$year.'.pdf';
        Excel::store(new VanguardLeadExport(), $csvfileName, 'export');
        Excel::store(new VanguardCallExport(), $csvfileName1, 'export');
        //Excel::store(new RexDirectLeadExport(), '/export/' . $csvfileName);

        $leadDetail     = DB::select("SELECT c.campaign_id, c.campaign_name, sum(lr.payout) as total_amount, COUNT(l.lead_id) AS total_leads
                                      FROM lead_routing lr
                                      LEFT JOIN `lead` l on lr.lead_id = l.lead_id
                                      LEFT JOIN campaign c on lr.campaign_id = c.campaign_id
                                      INNER JOIN lead_moving lm ON l.lead_id = lm.lead_id
                                      WHERE lr.campaign_id IN (4122, 4123, 4124, 4467) AND lr.route_status = 'sold' AND (lr.created_at >= '".$startDate." 00:00:00' AND lr.created_at <= '".$endDate." 23:59:59')
                                      GROUP BY lr.campaign_id");
        $leadArray      = [];
        if (isset($leadDetail) && !empty($leadDetail)) {
            foreach ($leadDetail as $lead) {
                $leadArray[]= array(
                    'campaign_name' => $lead->campaign_name,
                    'total_leads' => ($lead->campaign_id == 4123) ? 1102 : $lead->total_leads,
                    'total_amount' => ($lead->campaign_id == 4123) ? 77505 : $lead->total_amount,
                );
            }
        }
        view()->share('leadDetail', $leadArray);
        $pdf            = PDF::loadView('exports.vanguardinvoicepdf', $leadArray);
        $path           = public_path('/export/');
        $pdf->save($path . $pdffileName);

        // dd('sent');
        //send email
        $this->sendEmail(array($csvfileName, $csvfileName1, $pdffileName), $leadArray);

        //remove image from export directory
        //$this->destroy(array($csvfileName, $pdffileName));
        echo "Invoice email sent successfully."; exit();
    }

    public function sendEmail ($fileName, $leadDetail)
    {
        $subject        = 'Vanguard/Top Tier Invoice';
        $setFrom        = '<EMAIL>';
        $setTo          = '<EMAIL>';
        //$setTo        = '<EMAIL>, <EMAIL>';
        $postmarkToken  = '************************************';

        $mailBodyData   = array();
        $mail           = new TemplateEmail();

        $html           = 'Hello Vanguard Moving Services,<br><br> We want to let you know that Prime Marketing has sent you a Purchased Leads <strong>Invoice of $' . @number_format($leadDetail[0]['total_amount'] + $leadDetail[1]['total_amount'], 2) . '</strong> till <strong>30th ' . date('F', mktime(0, 0, 0, 6, 10)) . ', ' . date('Y', strtotime(date('Y') . " last month")) . '</strong>.<br><br> Please find the attached invoice and purchased lead details.<br><br> Please don\'t hesitate to get in <NAME_EMAIL> if you have any questions or need clarifications.<br><br> Best regards,<br> Client Support,<br> Prime Marketing LLC,<br> <a href="http://mover.primemarketing.us/">www.primemarketing.us</a>';
        // send email
        $mail->setTo($setTo);
        //$mail->setBcc('<EMAIL>');
        $mail->setFrom($setFrom);
        $mail->setSubject($subject);
        $mail->setHtmlbody($html);
        $excelContent   = base64_encode(file_get_contents(public_path() . '/export/' . $fileName[0]));
        $excelContent1  = base64_encode(file_get_contents(public_path() . '/export/' . $fileName[1]));
        $pdfContent     = base64_encode(file_get_contents(public_path() . '/export/' . $fileName[2]));
        $attchment[]    = ['Name' => $fileName[0], 'Content' => $excelContent, 'ContentType' => 'application/excel'];
        $attchment[]    = ['Name' => $fileName[1], 'Content' => $excelContent1, 'ContentType' => 'application/excel'];
        $attchment[]    = ['Name' => $fileName[2], 'Content' => $pdfContent, 'ContentType' => 'application/octet-stream'];
        //echo '<pre>'; print_r($attchment); die;
        $mail->setAttachment($attchment);
        $response = $mail->send_email($token = $postmarkToken, 'invoice');

        EmailLog::create([
            "business_id" => 942,
            "subject" => $subject,
            "set_to" => $setTo,
            "set_from" => $setFrom,
            "response" => json_encode($response->original['data']),
            "type" => 'invoice',
        ]);
    }

    public function destroy($fileName)
    {
        for ($i=0; $i < count($fileName); $i++) {
            $path = app_path("export/" . $fileName[$i]);
            unlink($path);
        }
    }
}