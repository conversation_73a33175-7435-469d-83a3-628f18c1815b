@section('title', 'Campaign Status Log List')
@section('label', 'Campaign Status Log List')
@section('url', 'campaignstatuslog-list')
@include('header.header')
@include('navigation.navigation')
@include('top.top')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
<div class="page-wrapper">
    <!-- Page Content-->
    <div class="page-content-tab">
        <div class="container-fluid">
            <!-- Page-Title -->
            <div class="row">
                <div class="col-sm-12">
                    <div class="page-title-box">
                        <div class="float-end">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">Setting Module</li>
                                <li class="breadcrumb-item">Notifications</li>
                                <li class="breadcrumb-item active">Campaign Status Log List</li>
                            </ol>
                        </div>
                        <h4 class="page-title">Campaign Status Log List</h4>
                    </div>
                    <!--end page-title-box-->
                </div>
                <!--end col-->
            </div>
            <!-- end page title end breadcrumb -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <form id="search_frm" name="search_frm" action="" method="get" autocomplete="off">
                            <div class="card-body">
                                @if ($message = Session::get('message'))
                                    <div class="alert alert-success border-0" role="alert">
                                        <strong>Well done!</strong> {{ $message }}
                                    </div>
                                @endif
                                <div class="row">
                                    <div class="col-md-2 balanchsearchbar">
                                        <div class="input-group" id="daterange">
                                            <input type="text" class="form-control" name="daterange" id="reportrange" value="{{ date('m/d/Y', strtotime(date('m/d/Y H:i:s') . ' -2 days')) }} - {{ date('m/d/Y') }}" />
                                        </div>
                                    </div>
                                    <div class="col-md-2 balanchsearchbarbtn">
                                        <div class="inner">
                                            <input type="submit" class="btn btn-primary" id="searchPayment" value="Search" />
                                        </div>
                                    </div>
                                    <div class="col-md-5"></div>
                                    <div class="col-md-3 d-flex justify-content-end mb-3">
                                        <button type="button" id="download-csv" class="btn btn-primary" onclick="return false;">Download CSV</button>
                                        <button type="button" id="download-xlsx" class="btn btn-primary ms-1" onclick="return false;">Download XLSX</button>
                                    </div>
                                    <!--end col-->
                                </div>
                                <div class="table-responsive">
                                    <div id="datatable-1"></div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div> <!-- end col -->
            </div> <!-- end row -->
        </div><!-- container -->
        <!--Start Rightbar-->
        <!--Start Rightbar/offcanvas-->
    @include('setting.setting')
    <!--end Rightbar/offcanvas-->
        <!--end Rightbar-->
        <!--Start Footer-->
        <!-- Footer Start -->
    @include('footer.footer')
    <!-- end Footer -->
        <!--end footer-->
    </div>
    <!-- end page content -->
</div>
<!-- end page-wrapper -->
<!-- App js -->
<script src="{{ asset('assets/src/jquery.min.js') }}"></script>
<script src="{{ asset('assets/js/app.js') }}"></script>
<!-- Sweet-Alert  -->
<script src="{{ asset('assets/plugins/sweet-alert2/sweetalert2.min.js') }}"></script>
<script src="{{ asset('assets/pages/sweet-alert.init.js') }}"></script>
<!-- Javascript  -->
<script src="{{ asset('assets/plugins/tabulator/tabulator.min.js') }}"></script>
<script type="text/javascript" src="https://oss.sheetjs.com/sheetjs/xlsx.full.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script type="text/javascript" src="{{ asset('assets/plugins/moment/moment-timezone-with-data.js') }}"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<script>
    moment.tz.setDefault("America/New_York");
    $(function() {
        $('#reportrange').daterangepicker({
            // startDate: start,
            // endDate: end,
            "maxDate": moment(),
            ranges: {
                'Today': [moment(), moment()],
                'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                'This Month': [moment().startOf('month'), moment().endOf('month')],
                'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            }
        });
    });

    $(document).ready(function() {
        if (window.location.href.indexOf("?daterange=") > -1) {
            let queryString = window.location.search;
            let urlParams = new URLSearchParams(queryString);
            let dates = urlParams.get('daterange');
        }
        if ((window.location.href.indexOf("sdate=") > -1) && ((window.location.href.indexOf("edate=") > -1))) {
            let queryString = window.location.search;
            let urlParams = new URLSearchParams(queryString);
            let sdate = urlParams.get('sdate');
            let edate = urlParams.get('edate');
            let dates = moment(sdate).format('MM/DD/YYYY') +' - '+moment(edate).format('MM/DD/YYYY');
            let elem = $("#reportrange").val(dates);
        }
    });

    //define data array
    var tabledata = JSON.parse('<?= json_encode($campaignLogArray, JSON_HEX_APOS); ?>');
    //tabledata
    var table = new Tabulator("#datatable-1", {
        placeholder: "No Data Available",
        data: tabledata, //load row data from array
        layout: "fitColumns", //fit columns to width of table
        //autoColumns:true, //create columns from data field names
        // responsiveLayout: "collapse", //hide columns that dont fit on the table
        tooltips: true, //show tool tips on cells
        addRowPos: "top", //when adding a new row, add it to the top of the table
        history: true, //allow undo and redo actions on the table
        pagination: "local", //paginate the data
        paginationSize: 50, //allow 7 rows per page of data
        paginationCounter: "rows", //display count of paginated rows in footer
        paginationSizeSelector: fileResponse.sizeArray, //enable page size select element with these options
        //movableColumns: true, //allow column order to be changed
        fitColumns: true,
        resizableRows: true, //allow row order to be changed
        paginationSizeSelector: fileResponse.sizeArray, //enable page size select element with these options
        initialSort: [ //set the initial sort order of the data
            {
                column: "id",
                dir: "desc"
            }
        ],
        columnDefaults: {
            tooltip: true //show tool tips on cells
        },
        columns: [ //define the table columns
            /*{
                title: "Sr No",
                width: "4%",
                hozAlign: "center",
                headerTooltip: true,
                headerSort: false,
                formatter: function(cell) {
                    const table = cell.getTable();
                    const pageSize = table.getPageSize();
                    const currentPage = table.getPage();
                    const rowIndex = cell.getRow().getPosition(false);

                    // For remote, assume rowIndex is 0-based in the current page
                    const srNo = ((currentPage - 1) * pageSize) + rowIndex;

                    return srNo;
                }
            },*/
            {
                title: "Business ID",
                field: "business_id",
                width: '8%',
                headerFilter: true,
                headerTooltip: true
            },
            {
                title: "Business Name",
                field: "business_name",
                width: '25%',
                headerFilter: true,
                headerTooltip: true
            },
            {
                title: "Campaign ID",
                field: "campaign_id",
                width: '8%',
                headerFilter: true,
                headerTooltip: true
            },
            {
                title: "Campaign Name",
                field: "campaign_name",
                width: '25%',
                headerFilter: true,
                headerTooltip: true
            },
            {
                title: "Old Status",
                field: "old_status",
                width: '7%',
                formatter: function(row) {
                    var rowData = row.getData();
                    var html = "";
                    if(rowData.old_status == 0){
                        html += '<span class="badge rounded-pill mx-1" style="background-color: #CCCC00">⏸ Temporary</span>' ;
                    }
                    if(rowData.old_status == 1){
                        html += '<span class="badge rounded-pill bg-success mx-1">✓ Active</span>' ;
                    }
                    if(rowData.old_status == 2){
                        html += '<span class="badge rounded-pill mx-1" style="background-color: #fd7e14">🕒 1 Hour Pause</span>' ;
                    }
                    if(rowData.old_status == 3){
                        html += '<span class="badge rounded-pill bg-danger mx-1">⛔ Permanent</span>' ;
                    }
                    return html;
                }
            },
            {
                title: "New Status",
                field: "new_status",
                width: '8%',
                formatter: function(row) {
                    var rowData = row.getData();
                    var html = "";
                    if(rowData.new_status == 0){
                        html += '<span class="badge rounded-pill mx-1" style="background-color: #CCCC00">⏸ Temporary</span>' ;
                    }
                    if(rowData.new_status == 1){
                        html += '<span class="badge rounded-pill bg-success mx-1">✓ Active</span>' ;
                    }
                    if(rowData.new_status == 2){
                        html += '<span class="badge rounded-pill mx-1" style="background-color: #fd7e14">🕒 1 Hour Pause</span>' ;
                    }
                    if(rowData.new_status == 3){
                        html += '<span class="badge rounded-pill bg-danger mx-1">⛔ Permanent</span>' ;
                    }
                    return html;
                }
            },
            {
                title: "Updated Date",
                field: "created_at",
                width: '10%',
                headerFilter: false,
                headerTooltip: true,
                sorterParams:{
                    format:"hh:mm A",
                    alignEmptyValues:"top"
                }
            },
            {
                title: "User",
                field: "created_by",
                width: '9%',
                headerFilter: true,
                headerTooltip: true
            },
            {
                title: "IP Address",
                field: "ip",
                width: '10%',
                headerFilter: true,
                headerTooltip: true
            },
        ]
    });

    document.getElementById("download-csv").addEventListener("click", function(){
        table.download("csv", "campaignstatusloglist.csv");
    });
    //trigger download of data.xlsx file
    document.getElementById("download-xlsx").addEventListener("click", function(){
        table.download("xlsx", "campaignstatusloglist.xlsx");
    });
</script>
</body>
</html>