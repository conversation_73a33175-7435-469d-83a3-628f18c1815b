<?php

namespace App\Http\Controllers\Leadroute;

use App\Http\Controllers\Controller;
use App\Models\Master\MstLeadSource;
use Illuminate\Http\Request;
use App\Helpers\Helper;
use App\Models\Lead\Lead;
use App\Models\Lead\LeadMatch;
//use App\Models\Lead\LeadFacades;
use App\Models\Lead\LeadRouting;
use App\Models\Campaign\Campaign;
//use App\Models\Campaign\CampaignFreeLead;
use App\Models\Business\Business;
use App\Models\Logic\LeadCampaignLogic;
use App\Models\Lead\LeadMoving;
use App\Models\Lead\LeadJunkRemoval;
use App\Models\Lead\LeadHeavyLifting;
use App\Models\Lead\LeadCarTransport;
use App\Models\Lead\SystemVerifyPhone;
//use App\Models\Lead\LeadRoutingLog;
use App\Models\Lead\Logs;
use App\Models\Logic\LogicDuplicateLead;
use App\Models\Logic\LogicMoving;
use App\Models\Logic\LogicJunk;
use App\Models\Logic\LogicHeavyLift;
use App\Models\Logic\LogicCarTransport;
use App\Models\Master\MstCallConfig;
use App\Helpers\CommonFunctions;
use App\Http\Controllers\API\LeadDelivery\LeadDeliveryApi;
use Exception;
use DB;
use Illuminate\Support\Facades\Log;
use App\Models\Lead\LeadActivity;
use App\Models\Lead\LeadMarketplace;
use App\Models\Lead\LeadIntent;
use App\Models\Lead\LeadLandingPage;
use App\Models\Seller\PingPostResponse;
use App\Http\Controllers\PlivoSms\ConfirmationSmsNew;
use App\Models\Master\MstTimeZone;
use App\Models\Master\MstSubscriptionPlan;
use App\Models\Subscription\Subscription;
use App\Models\Outbound\LeadCall;
use App\Models\Lead\LeadFollowUp;
use App\Models\Lead\LeadCallBuffer;
use DateTime;
use Carbon\Carbon;

class LeadRouteController extends Controller {

    public function reRouteLead($id, $directRoute = 0, $campId = 0, $isRouteSMS = 0) {
        $campaignType = $isLeadOrganic = $customerSayNo = 0;
        $matchCompany = $campaignIds = $campIdArr = $bus_check_exclusive = $bus_check_organic = $bus_check_dual = $bus_check_trio = $bus_check_custom = $campAssocArr = $routedBusinessIdArr = $activeCampAssocDataAr = $todayRouteData = $routedCampTypeArr = $resubmitRouteData = $leadMatchBusinessIdArr = array();
        $images_url = Helper::checkServer()['images_url'];
        $dateCurrent = $move_date = date('Y-m-d');
        $dateTimeCurrent = date('Y-m-d H:i:s');
        $endDecObj = new CommonFunctions;
        $leadId = $endDecObj->customeDecryption($id);
        $leadDeliverApi = new LeadDeliveryApi();
        if(isset( $leadId) && $leadId > 0){
            $leadactiviyobj = new LeadActivity();
            $leadactiviyobj->createActivity($leadId, 3);
        }
        try {
            //comment by BK on 28/06/2023
            //$leadRouting = LeadRouting::where('lead_id', $leadId)->where('route_status', 'sold')->get()->toArray();
            //join query added by BK on 28/06/2023
            $leadRouting = LeadRouting::select('lead_routing.*')
                ->join('campaign', 'lead_routing.campaign_id', 'campaign.campaign_id')
                ->where('lead_routing.route_status', 'sold')
                ->where('lead_routing.lead_id', $leadId)
                ->where('campaign.campaign_type_id', '!=', 3)
                ->get()->toArray();
            //echo "<pre>";print_r($leadRouting);die;
            for ($r = 0; $r < count($leadRouting); $r++) {
                $campIdArr[] = $leadRouting[$r]['campaign_id'];
                if ($dateCurrent == date('Y-m-d', strtotime($leadRouting[$r]['created_at']))) {
                    $todayRouteData[$leadRouting[$r]['campaign_id']][] = $leadRouting[$r];
                }
            }
            //echo "<pre>";print_r($todayRouteData);die;
            //Log::info('Lead already routed '.$leadId);
            if (count($campIdArr) > 0) {
                $getCampData = Campaign::whereIn('campaign_id', $campIdArr)->get()->toArray();
                for ($k = 0; $k < count($getCampData); $k++) {
                    if ($getCampData[$k]['campaign_type_id'] == 2) {
                        $bus_check_exclusive[] = $getCampData[$k];
                    }
                    if ($getCampData[$k]['campaign_type_id'] == 4) {
                        $bus_check_organic[] = $getCampData[$k];
                    }
                    if ($getCampData[$k]['campaign_type_id'] == 6) {
                        $bus_check_dual[] = $getCampData[$k];
                    }
                    if ($getCampData[$k]['campaign_type_id'] == 8) {
                        $bus_check_trio[] = $getCampData[$k];
                    }
                    if ($getCampData[$k]['campaign_type_id'] == 9) {
                        $bus_check_custom[] = $getCampData[$k];
                    }
                    if (!in_array($getCampData[$k]['campaign_type_id'], $routedCampTypeArr)) {
                        $routedCampTypeArr[] = $getCampData[$k]['campaign_type_id'];
                    }
                    $routedBusinessIdArr[] = trim($getCampData[$k]['business_id']);
                }
                $this->LeadRoutelog($leadId, 'Lead already routed to Business :' . json_encode($routedBusinessIdArr), 1);
            }

            if ($directRoute == 5) {
                for ($r = 0; $r < count($leadRouting); $r++) {
                    $resubmitRouteData[$leadRouting[$r]['campaign_id']][] = $leadRouting[$r];
                }

                if (count($resubmitRouteData[$campId]) > 0) {
                    //Start For Lead Delivery API
                    $key = $resubmitRouteData[$campId][0]['campaign_id'];
                    $leadId = $resubmitRouteData[$campId][0]['lead_id'];
                    $leadPayout = $resubmitRouteData[$campId][0]['payout'];
                    $balance = 0;
                    $score = $resubmitRouteData[$campId][0]['score'];

                    $jsonArray = array(
                        "campaign_id" => $key,
                        "lead_id" => $leadId,
                        "cost" => $leadPayout,
                        "balance" => $balance,
                        "score" => $score
                    );
                    $leadDeliverApi->leadDeliveryTemplate(json_encode($jsonArray));
                    //End For Lead Delivery API
                }
                return redirect()->back();
            }

            //echo "<pre>";print_r($routedCampTypeArr);die;
            if ($directRoute == 0 || $directRoute == 1) {
                //echo "<pre>";print_r($bus_check_exclusive);die;
                if (count($bus_check_exclusive) > 0) {
                    //echo "<pre>";print_r($bus_check_exclusive[0]['campaign_id']);die;
                    Log::info('2 Lead already routed to exclusive '.$bus_check_exclusive[0]['campaign_id']."===".$leadId);
                    $this->LeadRoutelog($leadId, 'Lead already routed to exclusive :' . $bus_check_exclusive[0]['campaign_id'], 2);
                    throw new Exception("Lead already routed");
                }

                if (count($bus_check_organic) > 0) {
                    Log::info('2 Lead already routed to organic '.$bus_check_organic[0]['campaign_id']."===".$leadId);
                    $this->LeadRoutelog($leadId, 'Lead already routed to organic :' . $bus_check_organic[0]['campaign_id'], 2);
                    throw new Exception("Lead already routed");
                }
            }

            $leadRouteSlot = 4;
            $getLeadRouteSlot = MstCallConfig::where('call_config', 'leadroute3slot')->get()->toArray();
            if (count($getLeadRouteSlot) > 0) {
                if (strtolower($getLeadRouteSlot[0]['status']) == "active") {
                    $leadRouteSlot = 3;
                }
            }
            //echo "<pre>";print_R($getLeadRouteSlot);die;
            $leadDetails = Lead::where('lead_id', $leadId)->get()->toArray();
            if (isset($leadDetails[0]['is_dnc_call']) && $leadDetails[0]['is_dnc_call'] == 'yes') {
                $this->LeadRoutelog($leadId, 'Lead is Call DNC do not route the lead', 7);
                throw new Exception("Lead is Call DNC do not route the lead");
            }

            //Added by BK on 20/02/2025 get sorce name based on lead wise
            if (isset($leadDetails)) {
                $sourceName = MstLeadSource::where('lead_source_id', $leadDetails[0]['lead_source_id'])->first();
                //check isDuplicate Leads
                if (strpos($sourceName->lead_source_name, 'VM') !== false) {
                    $sourceName = 'VM';
                } else if (strpos($sourceName->lead_source_name, 'QTM') !== false) {
                    $sourceName = 'QTM';
                } else if (strpos($sourceName->lead_source_name, 'IS') !== false) {
                    $sourceName = 'IS';
                }
            }

            $fromState = $toState = $leadDateTime = $email = $phone = $leadDate = $movingFields = "";
            $lead_distance = 0;
            $lead_category_id = 1;
            if (count($leadDetails) > 0) {
                $lead_category_id = $leadDetails[0]['lead_category_id'];
                //Added by BK on 23/07/2024
                if ($lead_category_id == 4) {
                    $leadRouteSlot = 6;
                }
                $remainSlot = ($leadRouteSlot - count($leadRouting));
                if ($directRoute == 2) {
                    $remainSlot = 4;
                }
                $this->LeadRoutelog($leadId, 'Lead remain slot :' . $remainSlot . "==and routed type: " . $directRoute, 3);

                $this->LeadRoutelog($leadId, 'Lead Details :' . json_encode($leadDetails), 4);
                if ($lead_category_id == 1) {
                    $moveTableName = "campaign_moving";
                    $movingFields = ",cm.min_distance,cm.move_type";
                    $leadMoveDetails = LeadMoving::where('lead_id', $leadId)->get()->toArray();
                } else if ($lead_category_id == 2) {
                    $moveTableName = "campaign_junk_type";
                    $movingFields = ",cm.junk_type_id,cm1.junk_sub_type_id";
                    $leadMoveDetails = LeadJunkRemoval::where('lead_id', $leadId)->get()->toArray();
                } else if ($lead_category_id == 3) {
                    $moveTableName = "campaign_heavy_lifting";
                    $movingFields = ",cm.min_distance,cm.move_type,cm.heavy_lifting_type_id,cm.is_required_assistence,cm.is_operational";
                    $leadMoveDetails = LeadHeavyLifting::where('lead_id', $leadId)->get()->toArray();
                } else if ($lead_category_id == 4) {
                    $moveTableName = "campaign_car_transport";
                    $movingFields = ",cm.min_distance,cm.move_type,cm.is_required_assistence,cm.is_operational,cctt.car_type_id";
                    $leadMoveDetails = LeadCarTransport::where('lead_id', $leadId)->get()->toArray();
                }
                //echo "<pre>";print_r($leadMoveDetails);die;
                if (count($leadMoveDetails) > 0) {
                    if ($lead_category_id == 1) {
                        $toState = $leadMoveDetails[0]['to_state'];
                        $move_date = $leadMoveDetails[0]['move_date'];
                        $lead_distance = $leadMoveDetails[0]['distance'];
                        $toZip = $leadMoveDetails[0]['to_zipcode'];
                        $toAreaCode = $leadMoveDetails[0]['to_areacode'];
                    } else if ($lead_category_id == 2) {
                        $toState = $leadMoveDetails[0]['from_state'];
                        $move_date = $leadMoveDetails[0]['junk_remove_date'];
                        $fromZip = $toZip = $leadMoveDetails[0]['from_zipcode'];
                        $fromAreaCode = $toAreaCode = $leadMoveDetails[0]['from_areacode'];
                    } else if ($lead_category_id == 3) {
                        $toState = $leadMoveDetails[0]['to_state'];
                        $move_date = $leadMoveDetails[0]['lift_date'];
                        $lead_distance = $leadMoveDetails[0]['distance'];
                        $toZip = $leadMoveDetails[0]['to_zipcode'];
                        $toAreaCode = $leadMoveDetails[0]['to_areacode'];
                    } else if ($lead_category_id == 4) {
                        $toState = $leadMoveDetails[0]['to_state'];
                        $move_date = $leadMoveDetails[0]['move_date'];
                        $lead_distance = $leadMoveDetails[0]['distance'];
                        $toZip = $leadMoveDetails[0]['to_zipcode'];
                        $toAreaCode = $leadMoveDetails[0]['to_areacode'];
                    }
                    $fromState = $leadMoveDetails[0]['from_state'];
                    $fromZip = $leadMoveDetails[0]['from_zipcode'];
                    $fromAreaCode = $leadMoveDetails[0]['from_areacode'];
                }
                $this->LeadRoutelog($leadId, 'Lead Move Details :' . json_encode($leadMoveDetails), 5);
                //echo "<pre>";print_r($leadDetails);die;
                $leadDateTime = $leadDetails[0]['lead_generated_at'];
                $email = $leadDetails[0]['email'];
                $phone = $leadDetails[0]['phone'];
                $is_verified = $leadDetails[0]['is_verified'];
                $lead_source_id = $leadDetails[0]['lead_source_id'];
                $leadDate = date("Y-m-d", strtotime($leadDetails[0]['lead_generated_at']));
            }
            if ($directRoute == 0 || $directRoute == 1 || $directRoute == 2 || $directRoute == 3 || $directRoute == 4) {
                if (strpos($email, '@test.com') !== false || $email == '<EMAIL>' || $leadDetails[0]['lead_source_id'] == 21 || $leadDetails[0]['lead_source_id'] == 138) {
                    $mover_dashboard_url = Helper::checkServer()['mover_dashboard_url'];
                    $businesslogo = $mover_dashboard_url . '/assets/images/email-image/logo.png';
                    $matchCompany['testCompany1'] = '9999999999|' . $businesslogo;
                    $matchCompany['testCompany2'] = '8888888888|' . $businesslogo;
                    $this->LeadRoutelog($leadId, 'Lead Test Email :' . $email, 6);
                    if ($directRoute == 0 || $directRoute == 2 || $directRoute == 3 || $directRoute == 4) {
                        return redirect()->back();
                    } else if ($directRoute == 1) {
                        sleep(30);
                        return $matchCompany;
                    }
                }
                $logicDpSource = new LogicDuplicateLead();
                $checkDuplicateSlot = $logicDpSource->checkDuplicate($email, $phone, $leadId, $leadDateTime, $lead_category_id, $sourceName);
                if (count($checkDuplicateSlot) > 0) {
                    if ($checkDuplicateSlot[0] == 1) {
                        if ($checkDuplicateSlot[1] == 4) {
                            $this->LeadRoutelog($leadId, 'Duplicate Lead in 48 hours with all slot sold', 7);
                            Log::info('7 Duplicate Lead in 48 hours with all slot sold=='.json_encode($checkDuplicateSlot)."===".$leadId);
                            throw new Exception("Duplicate Lead in 48 hours with all slot sold");
                        } else {
                            $this->LeadRoutelog($leadId, 'Duplicate Lead in 48 hours with all slot sold' . json_encode($checkDuplicateSlot), 8);
                            $remainSlot -= $checkDuplicateSlot[1];
                        }
                    }
                }
            }
            //Check Duplicate Sold Buyer Logic Start
            if ($directRoute == 3) {
                for ($h = 0; $h < count($bus_check_exclusive); $h++) {
                    if (in_array($bus_check_exclusive[$h]['campaign_id'], $campIdArr)) {
                        $remainSlot -= $leadRouteSlot;
                    }
                }

                for ($h = 0; $h < count($bus_check_organic); $h++) {
                    if (in_array($bus_check_organic[$h]['campaign_id'], $campIdArr)) {
                        $remainSlot -= $leadRouteSlot;
                    }
                }

                for ($d = 0; $d < count($bus_check_dual); $d++) {
                    if (in_array($bus_check_dual[$h]['campaign_id'], $campIdArr)) {
                        if ($remainSlot == 3 || $remainSlot == 5) {
                            $remainSlot = 1;
                            break;
                        } else {
                            $remainSlot -= $leadRouteSlot;
                        }
                    }
                }

                for ($d = 0; $d < count($bus_check_trio); $d++) {
                    if (in_array($bus_check_trio[$h]['campaign_id'], $campIdArr)) {
                        if ($remainSlot == 3) {
                            $remainSlot = 2;
                            break;
                        } else if ($remainSlot == 2) {
                            $remainSlot = 1;
                            break;
                        } else {
                            $remainSlot -= $leadRouteSlot;
                        }
                    }
                }

                for ($d = 0; $d < count($bus_check_custom); $d++) {
                    if (in_array($bus_check_custom[$h]['campaign_id'], $campIdArr)) {
                        $remainSlot = 0;
                    }
                }
            }

            //Check Duplicate Sold Buyer Logic Start
            if ($remainSlot < 0) {
                $this->LeadRoutelog($leadId, 'Duplicate Lead in 48 hours with negative slot sold-' . $remainSlot, 9);
                $remainSlot = 0;
            }
            if ($lead_category_id == 2) {
                $coverageType = "local";
            } else {
                $coverageType = "local";
                if (strcmp($fromState, $toState)) {
                    $coverageType = "long";
                }
            }
            //Added by BK on 60/07/2023 Sell Unverified Leads
            $sellUnverifiedLead = MstCallConfig::where('call_config', 'sellunverifiedlead')->get(['call_config_id', 'status'])->first();

            //Added by BK on 21/01/2025 get match business_id based on TCPA
            /*if (isset($leadDetails) && $leadDetails[0]['lp_lead_id'] > 0) {
                $sourceName = MstLeadSource::where('lead_source_id', $leadDetails[0]['lead_source_id'])->first();
                //check isDuplicate Leads
                if (strpos($sourceName->lead_source_name, 'VM') !== false) {
                    $sourceId = '19';
                } else if (strpos($sourceName->lead_source_name, 'QTM') !== false) {
                    $sourceId = '14';
                } else if (strpos($sourceName->lead_source_name, 'IS') !== false) {
                    $sourceId = '32';
                }
                $leadMatchBusinessIdArr = LeadMatch::where('lead_source_id', $sourceId)->where('lp_lead_id', $leadDetails[0]['lp_lead_id'])->pluck('business_id')->toArray();
            }*/

            $logicCampaign = new LeadCampaignLogic();
            $sqlForCampaign = "SELECT c.*,b.payment_type as business_payment_type,b.credit_available as business_credit_available,b.credit_reserved as business_credit_reserved,pb.business_id AS partner_business_id,pb.business_partner_id $movingFields FROM `campaign` c 
                INNER JOIN `business` b ON b.business_id=c.business_id 
                LEFT JOIN `business_partner_mapping` pb ON pb.business_id = b.business_id 
                LEFT JOIN `" . $moveTableName . "` cm on cm.campaign_id = c.campaign_id";
            if ($lead_category_id == 2) {
                $sqlForCampaign .= " LEFT JOIN `campaign_junk_sub_type` cm1 on cm1.campaign_id = c.campaign_id";
            }
            if ($lead_category_id == 4) {
                $sqlForCampaign .= " LEFT JOIN `campaign_car_transport_type` cctt on cctt.campaign_id = c.campaign_id";
            }
            $sqlForCampaign .= " WHERE b.status='active' AND c.is_active='yes' AND c.lead_category_id='" . $lead_category_id . "' AND c.lead_type_id=1";
            if ($sellUnverifiedLead->status == 'active') {
                $sqlForCampaign .= " AND (c.lead_status LIKE '%0%' OR c.lead_status LIKE '%1%')";
            } else {
                if ($is_verified == "yes") {
                    $sqlForCampaign .= " AND c.lead_status LIKE '%1%'";
                } else {
                    $sqlForCampaign .= " AND c.lead_status LIKE '%0%'";
                }
            }
            if (in_array(2, $routedCampTypeArr)) {
                $sqlForCampaign .= " AND c.campaign_type_id=2";
            }
            if ($leadId > 0) {
                $checkRental = $this->getLeadCategoryType($leadId);
                $keyword = "";
                if (isset($checkRental['keyword'])) {
                    $keyword = $checkRental['keyword'];
                }
                if (!empty($keyword)) {
                    $sqlForCampaign .= " AND c.is_remove_truck_rental='no'";
                }
            }
            /*if (isset($leadDetails) && $leadDetails[0]['lp_lead_id'] > 0) {
                if (count($leadMatchBusinessIdArr) > 0) {
                    $sqlForCampaign .= " AND b.business_id IN (".implode(',', $leadMatchBusinessIdArr).")";
                } else {
                    $sqlForCampaign .= " AND b.business_id IN (0)";
                }
            }*/

            if ($directRoute <> 1) {
                $sqlForCampaign .= " AND b.business_id NOT IN (993, 1117)";
            }
            $this->LeadRoutelog($leadId, 'Coverage Type: ' . $coverageType . ',Lead route query ==' . $sqlForCampaign, 10);
            $matchCampaignData = DB::select($sqlForCampaign);
            //$this->LeadRoutelog($leadId, 'Active Campaign Data ==' . json_encode($matchCampaignData), 5);
            //echo "<pre>";print_r($matchCampaignData);die;
            $finalCampDataArr = $campUniqueIdArr = array();
            for ($vf = 0; $vf < count($matchCampaignData); $vf++) {
                if (!in_array($matchCampaignData[$vf]->campaign_id, $campUniqueIdArr)) {
                    $finalCampDataArr[] = $matchCampaignData[$vf];
                    $campUniqueIdArr[] = $matchCampaignData[$vf]->campaign_id;
                }
            }
            //echo "<pre>";print_r($finalCampDataArr);die;
            //if (count($campIdArr) > 0) {
            //echo "<pre>";print_R(count($finalCampDataArr));die;
            //$nonCompetesBusinessIdArr = BusinessPartnerMapping::whereIn('business_id', $routedBusinessIdArr)->pluck('business_partner_id')->toArray();
            //echo "<pre>";print_R(count($nonCompetesBusinessIdArr));die;
            foreach ($finalCampDataArr as $key => $val) {
                if (in_array(trim($val->business_id), $routedBusinessIdArr)) {
                    $this->LeadRoutelog($leadId, 'reRouteLead unset Already routed Business == ' . $finalCampDataArr[$key]->business_id, 11);
                    unset($finalCampDataArr[$key]); //Uncomment here
                }
                //Start Business Partner Login
                if (in_array($val->business_partner_id, $routedBusinessIdArr)) {
                    unset($finalCampDataArr[$key]); //Uncommnent here
                }
                //End Business Partner Login
                //echo $val->campaign_id."==".$coverageType."==".$val->move_type."==".$lead_category_id."<br>";
                if (isset($val->move_type) && $val->move_type != $coverageType && $lead_category_id != 2) {
                    //echo $val->campaign_id;
                    unset($finalCampDataArr[$key]); //Uncommnent here
                }
            }
            //}
            if ($directRoute < 4) {
                //echo "<pre>";print_r($finalCampDataArr);
                //echo "<br>===========================<br>";
                $finalCampDataArr = array_values($finalCampDataArr);
                //echo "<pre>";print_r($finalCampDataArr);die;
                for ($d = 0; $d < count($finalCampDataArr); $d++) {
                    $activeCampAssocDataAr[$finalCampDataArr[$d]->campaign_id] = $finalCampDataArr[$d];
                    //echo "<pre>";print_r($activeCampAssocDataAr);die;
                    //Start For Check Campaign Minimun Distance with Lead Distance Logic
                    //echo $lead_distance."==".$finalCampDataArr[$d]->min_distance;die;
                    if (isset($finalCampDataArr[$d]->min_distance) && $lead_distance < $finalCampDataArr[$d]->min_distance && $lead_category_id != 2) {
                        //echo $finalCampDataArr[$d]->campaign_id."<br>";
                        //$this->LeadRoutelog($leadId, 'No Distance campaign ==' . $finalCampDataArr[$d]->campaign_id, 12);
                        continue;
                    }
                    //echo "<pre>";print_r($finalCampDataArr[$d]);die;
                    //End For Check Campaign Minimun Distance with Lead Distance Logic
                    if (!in_array($finalCampDataArr[$d]->campaign_id, $campaignIds)) {
                        //Start For Check Campaign Fund Logic
                        $payout = CommonFunctions::getCampaignPayout($finalCampDataArr[$d]->campaign_id, $coverageType, 0, 0);
                        //$this->LeadRoutelog($leadId, 'Check Campaign Payout ==' . $finalCampDataArr[$d]->campaign_id . "==payout==" . $payout, 13);
                        if ($payout > 0) {
                            //echo "<pre>";print_r($finalCampDataArr[$d]);die;
                            if ($finalCampDataArr[$d]->business_payment_type == 'pre') { // Check if business payment_type is PrePayment
                                //$this->LeadRoutelog($leadId, 'business payment type ==' . $finalCampDataArr[$d]->business_payment_type . "==campaing payment type==" . $finalCampDataArr[$d]->payment_type, 14);
                                if ($finalCampDataArr[$d]->payment_type == 0) { // business level balance;
                                    //$businessesData = Businesses::where('id', $campaign->businesses_id)->get(['credit_available', 'credit_reserved'])->first(); 
                                    //$this->LeadRoutelog($leadId, 'business credit available ==' . $finalCampDataArr[$d]->business_credit_available . "== business credit reserved ==" . $finalCampDataArr[$d]->business_credit_reserved, 15);
                                    if (($finalCampDataArr[$d]->business_credit_available - $finalCampDataArr[$d]->business_credit_reserved) >= $payout && $payout > 0) {
                                        //$this->LeadRoutelog($leadId, 'Business Credit Available win ==' . $finalCampDataArr[$d]->campaign_id, 16);
                                        $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                                    }
                                } else { // campaign level balance
                                    //$this->LeadRoutelog($leadId, 'campaign credit available ==' . $finalCampDataArr[$d]->credit_available . "== campaign credit reserved ==" . $finalCampDataArr[$d]->credit_reserved, 17);
                                    if (($finalCampDataArr[$d]->credit_available - $finalCampDataArr[$d]->credit_reserved) >= $payout && $payout > 0) {
                                        //$this->LeadRoutelog($leadId, 'Business Credit Available win ==' . $finalCampDataArr[$d]->campaign_id, 18);
                                        $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                                    }
                                }
                            } else { // If payment_type is PostPayment then no need to check credit availibility
                                //$this->LeadRoutelog($leadId, 'Post Business Credit Available win ==' . $finalCampDataArr[$d]->campaign_id, 19);
                                $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                            }
                        }
                        //End For Check Campaign Fund Logic
                    }
                }
                //echo "<pre>dd";print_r($campaignIds);die;
                $this->LeadRoutelog($leadId, 'Out of funds campaign == ' . implode(",", $campaignIds), 20);
                //Start For Check Campaign Source Logic
                $campaignIds = $logicCampaign->checkCampaignSource($campaignIds, $leadId);
                //echo "<pre>";print_R($campaignIds);die;
                //Start For Check Campaign Exclude Logic
                $campaignIds = $logicCampaign->checkCampaignExcludedDates($campaignIds, $activeCampAssocDataAr, $move_date);
                //echo "<pre>";print_R($campaignIds);die;
                $this->LeadRoutelog($leadId, 'Move Date Restriction == ' . implode(",", $campaignIds), 21);
                //End For Check Campaign Exclude Logic
                //Start For Check Campaign Per Hour Lead Route Limit Logic
                $campaignIds = $logicCampaign->checkTiming($campaignIds, $dateCurrent, $dateTimeCurrent, $coverageType, $todayRouteData);
                $this->LeadRoutelog($leadId, 'No Timing campaigns ==' . implode(",", $campaignIds), 22);
                //End For Check Campaign Per Hour Lead Route Limit Logic
                //Start For Check Campaign Daily Limit,Budget Limit and Hourly Limit Logic
                $campaignIds = $logicCampaign->checkLimit($campaignIds, $activeCampAssocDataAr, $coverageType, 0, 0);
                //echo "<pre>dd";print_R($campaignIds);die;
                $this->LeadRoutelog($leadId, 'No limits campaign ==' . implode(",", $campaignIds), 23);
                //End For Check Campaign Daily Limit,Budget Limit and Hourly Limit Logic
                //echo $fromZip."==".$toZip."==".$fromState."==".$toState."==".$fromAreaCode."==".$toAreaCode;die;
                $campaignIds = $logicCampaign->checkFromZip($campaignIds, $activeCampAssocDataAr, $fromZip, $toZip, $fromState, $toState, $fromAreaCode, $toAreaCode);
                //echo "<pre>dd";print_R($campaignIds);die;
                $this->LeadRoutelog($leadId, 'Region not matches == ' . implode(",", $campaignIds), 24);
                if ($lead_category_id == 1) {
                    $logicMovingCampaign = new LogicMoving();
                    $campaignIds = $logicMovingCampaign->checkMovingSizeLogic($campaignIds, $leadId, $leadMoveDetails);
                    // echo "<pre>";print_r($campaignIds);die;
                    $this->LeadRoutelog($leadId, "reRouteLead movingSize not matches==" . implode(",", $campaignIds), 25);
                }
                if ($lead_category_id == 2) {
                    $logicJunkCampaign = new LogicJunk();
                    $campaignIds = $logicJunkCampaign->checkJunkLogic($campaignIds, $leadId, $leadMoveDetails);
                    //echo "<pre>";print_r($campaignIds);die;
                    $this->LeadRoutelog($leadId, "reRouteLead Junk not matches==" . implode(",", $campaignIds), 25);
                }
                if ($lead_category_id == 3) {
                    $logicHeavyLiftCampaign = new LogicHeavyLift();
                    $campaignIds = $logicHeavyLiftCampaign->checkHeavyLiftLogic($campaignIds, $leadId, $leadMoveDetails);
                    //echo "<pre>";print_r($campaignIds);die;
                    $this->LeadRoutelog($leadId, "routeLead Heavy Lift not matches==" . implode(",", $campaignIds), 26);
                }
                if ($lead_category_id == 4) {
                    $logicCarCampaign = new LogicCarTransport();
                    $campaignIds = $logicCarCampaign->checkCarTransportLogic($campaignIds, $leadId);
                    //echo "<pre>";print_r($campaignIds);die;
                    $this->LeadRoutelog($leadId, "routeLead Car Transport not matches==" . implode(",", $campaignIds), 26);
                }
                //echo "<pre>";print_r($campaignIds);die;
                if ($directRoute == 2 || $directRoute == 3) {
                    $campaignIds = $logicCampaign->checkSoldToBuyer($campaignIds, $leadId, 0);
                    //echo "<pre>";print_r($campaignIds);die;
                    $this->LeadRoutelog($leadId, "Already Sold to Buyers==" . implode(",", $campaignIds), 27);
                }
                // 2021-08-23 sms/phone verify then check already sold buyer
                if ($directRoute == 0) {
                    $verifyDataPhone = SystemVerifyPhone::where('lead_id', $leadId)->get()->toArray();
                    //echo "<pre>dd";print_r($verifyDataPhone);die;
                    if (count($verifyDataPhone) > 0) {
                        $campaignIds = $logicCampaign->checkSoldToBuyer($campaignIds, $leadId, 23);
                        $this->LeadRoutelog($leadId, "System Verify Phone Already Sold to Buyers==" . implode(",", $campaignIds), 28);
                    }
                }
                $campaignIds = $logicCampaign->checkDuplicateBuyer($campaignIds, $email, $phone, $leadId, $leadDateTime, $lead_category_id);
                //echo "<pre>dd";print_r($campaignIds);die;
                $this->LeadRoutelog($leadId, 'Duplicate Buyer == ' . implode(",", $campaignIds), 29);
            }
            if ($directRoute == 5) {
                unset($campaignIds);
                $campaignIds[] = $campId;
            }
            if ($directRoute == 4) {
                unset($campaignIds);
                $campaignIds = explode(",", $campId);
            }
            if ($directRoute == 5 || $directRoute == 4) {
                $getCampData = Campaign::whereIn('campaign_id', $campaignIds)->get(['campaign_id', 'business_id', 'payment_type', 'credit_available', 'credit_reserved', 'free_lead', 'campaign_type_id'])->toArray();
                unset($campaignIds);
                $campaignIds = array();
                for ($cx = 0; $cx < count($getCampData); $cx++) {
                    if (in_array(2, $routedCampTypeArr) && $getCampData[$cx]['campaign_type_id'] != 2) {
                        if ($directRoute == 4) {
                            /* if (!in_array(trim($getCampData[$cx]['business_id']), $routedBusinessIdArr)) { */
                            $campaignIds[] = $getCampData[$cx]['campaign_id'];
                            //echo $getCampData[$cx]['campaign_id'];die;
                            /* } */
                        }
                        //Already routed to Excusive Campaign
                        $this->LeadRoutelog($leadId, 'Already routed to Excusive Campaign == ' . $getCampData[$cx]['campaign_type_id'] . ', routed campaign type:' . json_encode($routedCampTypeArr), 30);
                    } else {
                        if ($directRoute == 4) {
                            /* if (!in_array(trim($getCampData[$cx]['business_id']), $routedBusinessIdArr)) { */
                            $campaignIds[] = $getCampData[$cx]['campaign_id'];
                            //echo $getCampData[$cx]['campaign_id'];die;
                            /* } */
                        } else {
                            if (in_array(trim($getCampData[$cx]['business_id']), $routedBusinessIdArr)) {
                                $campaignIds[] = $getCampData[$cx]['campaign_id'];
                                //echo $getCampData[$cx]['campaign_id'];die;
                            }
                        }
                    }
                }
            }
            //echo "<pre>dd";print_r($campaignIds);die;
            $leadIntent = LeadIntent::where('lead_id', $leadId)->first();
            if (isset($leadIntent)) {
                $campaignType = 4;
            }
            if ($directRoute == 1 && $campId > 0) {
                $this->LeadRoutelog($leadId, 'LeadIntent campaign_id == ' . $leadIntent->campaign_id . ' ==intent ' . $leadIntent->intent, 100);
                if (!$leadIntent) {
                    unset($campaignIds);
                    $campaignIds[] = $campId;
                    $this->LeadRoutelog($leadId, 'LeadIntent campaign_id == ' . $leadIntent->campaign_id, 101);
                } else {
                    //intent 2 = particular organic campaign criteria and intent 3 = other mover organic campaign criteria
                    if ($leadIntent->intent == 2 || $leadIntent->intent == 3) {
                        $intentCampaignIds = explode(",", $leadIntent->campaign_id);
                        // Check buyer already same email / phone lead in last 30 days or not
                        $intentCampaignIds = $logicCampaign->checkDuplicateBuyer($intentCampaignIds, $email, $phone, $leadId, $leadDateTime, $lead_category_id);
                        if (count($intentCampaignIds) > 0) {
                            $isLeadOrganic = 1;
                            $customerSayNo = 1;
                            unset($campaignIds);
                            $campaignIds = explode(",", $leadIntent->campaign_id);
                        }
                        $this->LeadRoutelog($leadId, 'LeadIntent 2&3 campaign_id == ' . $leadIntent->campaign_id, 102);
                        $this->LeadRoutelog($leadId, 'LeadIntent 2&3 win campaign_id == ' . implode(",", $campaignIds), 103);
                        //intent 4 = exclusive mover campaign criteria
                    } else if ($leadIntent->intent == 4) {
                        $customerSayNo = 1;
                        $intentCampaignIds = explode(",", $leadIntent->campaign_id);
                        $intentCampaignIds = $logicCampaign->checkDuplicateBuyer($intentCampaignIds, $email, $phone, $leadId, $leadDateTime, $lead_category_id);
                        if(count($intentCampaignIds) > 0) {
                            unset($campaignIds);
                            if ($leadDetails[0]['lead_source_id'] <> 147) {
                                $campaignIds = explode(",", $leadIntent->campaign_id);
                            } else {
                                $campaignDetail = Campaign::where('campaign_id', $leadIntent->campaign_id)->first();
                                if ($campaignDetail->is_active == 'yes') {
                                    $campaignIds = explode(",", $leadIntent->campaign_id);
                                }
                            }
                        }
                        $this->LeadRoutelog($leadId, 'LeadIntent 4 campaign_id == ' . $leadIntent->campaign_id, 102);
                        $this->LeadRoutelog($leadId, 'LeadIntent 4 win campaign_id == ' . implode(",", $campaignIds), 103);
                    } else if ($leadIntent->intent == 0) {
                        $customerSayNo = 1;
                        unset($campaignIds);
                    } else if ($leadIntent->intent == 1) {
                        $notSoldList[] = $leadIntent->campaign_id;
                        $campaignIds = array_diff($campaignIds, $notSoldList);
                        $this->LeadRoutelog($leadId, 'LeadIntent 1 campaign_id == ' . $leadIntent->campaign_id, 102);
                        $this->LeadRoutelog($leadId, 'LeadIntent 1 win campaign_id == ' . implode(",", $campaignIds), 103);
                    }
                }
            }

            //echo "<pre>dd";print_r($campaignIds);die;
            $campaignSortBundle = array();
            if (count($campaignIds) > 0 && $directRoute == 4) {
                $logcondition = 1;
                $campaignSortBundle = $logicCampaign->checkBusinessLeadCampaignLogic($campaignIds, $coverageType, $leadDate, $leadDateTime, $remainSlot, $leadId, $lead_category_id, 0, $directRoute);
            } else if ($campaignType == 4 && $isLeadOrganic > 0) {
                $logcondition = 2;
                $campaignSortBundle = $logicCampaign->checkOrganicCampaignLogic($campaignIds, $coverageType, $leadDate, $leadDateTime, $remainSlot, $leadId);
            } else if (count($campaignIds) > 0) {
                $logcondition = 3;
                $campaignSortBundle = $logicCampaign->checkCampaignLogic($campaignIds, $coverageType, $leadDate, $leadDateTime, $remainSlot, $leadId, $lead_category_id, 0, $directRoute,0,"","", $campaignType);
            } else {
                $logcondition = 4;
                //$this->LeadRoutelog($leadId, 'reRouteLead No any campaign found for route lead', 31);
            }
            $this->LeadRoutelog($leadId, 'reRouteLead No any campaign found for route lead' . $logcondition . "==" . json_encode($campaignIds) . "===" . $directRoute . "==" . $isLeadOrganic, 31);
            //echo "<pre>dd";print_r($campaignSortBundle);die;
            $sortedIds = array();
            $this->LeadRoutelog($leadId, 'campaignSortBundle == ' . json_encode($campaignSortBundle), 56);

            $campIdArr = $campAssocArr = $businessIdArr = $businessAssocArr = array();
            foreach ($campaignSortBundle as $key => $score) {
                $campIdArr[] = $key;
            }
            if (count($campIdArr) > 0) {
                $getCampData = Campaign::whereIn('campaign_id', $campIdArr)->get(['campaign_id', 'business_id', 'campaign_type_id', 'payment_type', 'credit_available', 'credit_reserved', 'free_lead'])->toArray();
                for ($x = 0; $x < count($getCampData); $x++) {
                    $campAssocArr[$getCampData[$x]['campaign_id']] = $getCampData[$x];
                    if (!in_array($getCampData[$x]['business_id'], $businessIdArr)) {
                        $businessIdArr[] = $getCampData[$x]['business_id'];
                    }
                }
                $this->LeadRoutelog($leadId, 'Campaign Ids Win == ' . json_encode($campAssocArr), 57);
            }
            if (count($businessIdArr) > 0) {
                $getBusinessData = Business::where('business_id', $businessIdArr)->get(['credit_available', 'credit_reserved', 'business_id'])->toArray();
                for ($y = 0; $y < count($getBusinessData); $y++) {
                    $businessAssocArr[$getBusinessData[$y]['business_id']] = $getBusinessData[$y];
                }
                $this->LeadRoutelog($leadId, 'Campaign Business Ids Win == ' . json_encode($businessAssocArr), 57);
            }
            //echo "<pre>";print_r(array_keys($campaignSortBundle));die;
            //check lead entry already in marketplace or not
            $number = 0;
            $checkAlready = Lead::where('lead_id', $leadId)->where('is_marketplace', 'yes')->first();
            $PPSlot = PingPostResponse::where('lead_id', $leadId)->count();
            if (count($campaignSortBundle) > 0) {
                $winCampIdArr = array_keys($campaignSortBundle);
                $leadRoutedData = LeadRouting::where('lead_id', $leadId)->whereIn('campaign_id', $winCampIdArr)->get()->toArray();
                $routedCampPayoutArr = array();
                for ($bv = 0; $bv < count($leadRoutedData); $bv++) {
                    $routedCampPayoutArr[$leadRoutedData[$bv]['campaign_id']] = $leadRoutedData[$bv]['payout'];
                }
                //echo "<pre>";print_r($routedCampPayoutArr);die;
                foreach ($campaignSortBundle as $key => $score) {
                    if (isset($campAssocArr[$key])) {
                        $businessesCampaignDetails = $campAssocArr[$key];
                        $sortedIds[] = $key;
                        if (isset($routedCampPayoutArr[$key])) {
                            $leadPayout = $routedCampPayoutArr[$key];
                        } else {
                            $leadPayout = CommonFunctions::getCampaignPayout($key, $coverageType, 0, 0);
                        }
                        $balance = 0;
                        if ($businessesCampaignDetails['payment_type'] == 0) {
                            if (isset($businessAssocArr[$businessesCampaignDetails['business_id']])) {
                                $businessesData = $businessAssocArr[$businessesCampaignDetails['business_id']];
                                $balance = ($businessesData['credit_available'] - $businessesData['credit_reserved']);
                            }
                        } else {
                            $balance = ($businessesCampaignDetails['credit_available'] - $businessesCampaignDetails['credit_reserved']);
                        }
                        $this->LeadRoutelog($leadId, 'Campaign Id==' . $key . ',Campaign Payment Type == ' . $businessesCampaignDetails['payment_type'] . ',Balance=' . $balance."===".$leadPayout, 58);
                        $leadDeliverApi->LeadrouteWhenDeliveryOk($leadId, $key, $leadPayout, $score, $lead_source_id);

                        //Start For Lead Delivery API
                        $jsonArray = array(
                            "campaign_id" => $key,
                            "lead_id" => $leadId,
                            "cost" => $leadPayout,
                            "balance" => $balance,
                            "score" => $score
                        );
                        $leadDeliverApi->leadDeliveryTemplate(json_encode($jsonArray));
                        //End For Lead Delivery API
                        //Start For Lead Market Place Logic
                        if ($number == 0 && $customerSayNo == 0) {
                            if ((count($campaignSortBundle) < 4) || ($lead_category_id == 4 && count($campaignSortBundle) < 6)) {
                                if (isset($checkAlready)) {
                                    if ($businessesCampaignDetails['campaign_type_id'] != 2 && $businessesCampaignDetails['campaign_type_id'] != 4) {
                                        $remaingSlot = $checkAlready->remaining_slot - count($campaignSortBundle);
                                        if ($businessesCampaignDetails['campaign_type_id'] == 6 || isset($checkAlready) && ($checkAlready->sold_type == 'dual')) {
                                            if ($checkAlready->remaining_slot == 4 || $checkAlready->remaining_slot == 6) {
                                                $remaingSlot = 2 - count($campaignSortBundle);
                                            }
                                            if ($checkAlready->remaining_slot <= 3) {
                                                $remaingSlot = 0;
                                            }
                                        }else if ($businessesCampaignDetails['campaign_type_id'] == 8 || isset($checkAlready) && ($checkAlready->sold_type == 'trio')) {
                                            if ($checkAlready->remaining_slot == 4) {
                                                $remaingSlot = 3 - count($campaignSortBundle);
                                            }
                                            if ($checkAlready->remaining_slot <= 3) {
                                                $remaingSlot = 2 - count($campaignSortBundle);
                                            }
                                            if ($checkAlready->remaining_slot < 2) {
                                                $remaingSlot = 0;
                                            }
                                        } else if ($businessesCampaignDetails['campaign_type_id'] == 9) {
                                            $remaingSlot = 0;
                                        }
                                        if ($remaingSlot > 0) {
                                            Lead::where('lead_id', $leadId)->update(['remaining_slot' => $remaingSlot, 'is_marketplace' => 'yes']);
                                        } else {
                                            Lead::where('lead_id', $leadId)->update(['remaining_slot' => 0, 'is_marketplace' => 'no']);
                                        }
                                    } else {
                                        Lead::where('lead_id', $leadId)->update(['remaining_slot' => 0, 'is_marketplace' => 'no']);
                                    }
                                } else {
                                    if ($businessesCampaignDetails['campaign_type_id'] != 2 && $businessesCampaignDetails['campaign_type_id'] != 4) {
                                        $remaingSlot = $remainSlot - count($campaignSortBundle) - $PPSlot;
                                        if ($businessesCampaignDetails['campaign_type_id'] == 6) {
                                            if ($remainSlot == 4 || $remainSlot == 6) {
                                                $remaingSlot = 2 - count($campaignSortBundle);
                                            }
                                            if ($remainSlot < 3) {
                                                $remaingSlot = 0;
                                            }
                                        }else if ($businessesCampaignDetails['campaign_type_id'] == 8) {
                                            if ($remainSlot == 4) {
                                                $remaingSlot = 3 - count($campaignSortBundle);
                                            }
                                            if ($remainSlot < 2) {
                                                $remaingSlot = 0;
                                            }
                                        } else if ($businessesCampaignDetails['campaign_type_id'] == 9) {
                                            $remaingSlot = 0;
                                        }
                                        if ($remaingSlot > 0) {
                                            Lead::where('lead_id', $leadId)->update(['remaining_slot' => $remaingSlot, 'is_marketplace' => 'yes']);
                                        } else {
                                            Lead::where('lead_id', $leadId)->update(['remaining_slot' => 0, 'is_marketplace' => 'no']);
                                        }
                                    }
                                }
                            }
                        }
                        //End For Lead Market Place Logic
                        $number++;
                    }
                }

                $timeZoneDetail = MstTimeZone::where('timezone_id', $leadDetails[0]['timezone_id'])->get()->toArray();
                date_default_timezone_set($timeZoneDetail[0]['timezone']);
                $hoursCheck = date('G', time());
                if (($hoursCheck >= 8 && $hoursCheck <= 21) && ($directRoute == 1 || $isRouteSMS > 0)) {
                    $confirmationsms = new ConfirmationSmsNew();
                    $confirmationsms->LeadConfirmationSms($leadId, 1);
                }

                date_default_timezone_set("America/New_York");
            } else {
                $routePayout = 0;
                $leadRoutingData = LeadRouting::where('lead_id', $leadId)->where('route_status', 'sold')->get()->toArray();
                $leadType = Lead::where('lead_id', $leadId)->first();
                if (count($leadRoutingData) > 0) {
                    for ($q = 0; $q < count($leadRoutingData); $q++) {
                        $routePayout += $leadRoutingData[$q]['payout'];
                    }
                    Lead::where('lead_id', $leadId)->update(['payout' => $routePayout]);
                }
                $this->LeadRoutelog($leadId, 'Lead routed Campaign from routing == ' . json_encode($leadRoutingData) . ', total lead payout=' . $routePayout, 60);

                //Added by BK on 18/07/2023
                $wholesellerCampaignIdArray = [];
                $isWholesellerCampaign = 0;
                $campaignDetail = Campaign::whereIn('business_id', [55, 992])->where('lead_type_id', 1)->whereIn('campaign_type_id', [1, 2, 6])->get(['campaign_id']);
                if (count($campaignDetail) > 0) { foreach ($campaignDetail as $campaign) {
                    $wholesellerCampaignIdArray[] = $campaign->campaign_id;
                } }

                if (is_array($leadRoutingData) && count($leadRoutingData) > 0) {
                    foreach($leadRoutingData as $routing) {
                        if (in_array($routing['campaign_id'], $wholesellerCampaignIdArray)) {
                            $isWholesellerCampaign = 1;
                            break;
                        }
                    }
                }

                if (!isset($checkAlready) && $customerSayNo == 0 && $isWholesellerCampaign == 0 && $leadType->sold_type != "exclusive") {
                    Lead::where('lead_id', $leadId)->update([
                        'remaining_slot' => $remainSlot - $PPSlot,
                        'is_marketplace' => 'yes'
                    ]);
                }
            }


            // Update Final Amount in leads table
            if ($directRoute == 0 || $directRoute == 5 || $directRoute == 2 || $directRoute == 3 || $directRoute == 4) {
                $leadRoutingData = LeadRouting::where('lead_id', $leadId)->where('route_status', 'sold')->get()->toArray();
                if (count($leadRoutingData) > 0) {
                    $campaignIdFinal = $routedCampIdArr = $routedCampDataArr = $routedBusinessIdArr = $routedBusinessDataArr = array();
                    for ($g = 0; $g < count($leadRoutingData); $g++) {
                        $routedCampIdArr[] = $leadRoutingData[$g]['campaign_id'];
                    }
                    if (count($routedCampIdArr) > 0) {
                        $getCampData = Campaign::whereIn('campaign_id', $routedCampIdArr)->get(['campaign_id', 'business_id'])->toArray();
                        for ($c = 0; $c < count($getCampData); $c++) {
                            $routedCampDataArr[$getCampData[$c]['campaign_id']] = $getCampData[$c];
                            if (!in_array($getCampData[$c]['business_id'], $routedBusinessIdArr)) {
                                $routedBusinessIdArr[] = $getCampData[$c]['business_id'];
                            }
                        }
                    }
                    if (count($routedBusinessIdArr) > 0) {
                        if(!in_array(993, $routedBusinessIdArr) === false) {  // 993
                            $leadactiviyobj1 = new LeadActivity();
                            $followupLeadData = LeadFollowUp::where('lead_id', $leadId)->first();
                            if(isset( $followupLeadData )){
                                $leadactiviyobj1->createActivity($leadId, 13);
                            }
                            $bufferLeadData = LeadCallBuffer::where('lead_id', $leadId)->first();
                            if(isset( $bufferLeadData )) {
                                $leadactiviyobj1->createActivity($leadId, 8);
                            }                     
                            LeadCallBuffer::where('lead_id', $leadId)->delete();
                            LeadFollowUp::where('lead_id', $leadId)->delete();
                        }
                    }
                }
                return redirect()->back();
            } else if ($directRoute == 1) {
                $leadRoutingData = LeadRouting::where('lead_id', $leadId)->where('route_status', 'sold')->get()->toArray();
                if (count($leadRoutingData) > 0) {
                    $campaignIdFinal = $routedCampIdArr = $routedCampDataArr = $routedBusinessIdArr = $routedBusinessDataArr = array();
                    for ($g = 0; $g < count($leadRoutingData); $g++) {
                        $routedCampIdArr[] = $leadRoutingData[$g]['campaign_id'];
                    }
                    if (count($routedCampIdArr) > 0) {
                        $getCampData = Campaign::whereIn('campaign_id', $routedCampIdArr)->get(['campaign_id', 'business_id'])->toArray();
                        for ($c = 0; $c < count($getCampData); $c++) {
                            $routedCampDataArr[$getCampData[$c]['campaign_id']] = $getCampData[$c];
                            if (!in_array($getCampData[$c]['business_id'], $routedBusinessIdArr)) {
                                $routedBusinessIdArr[] = $getCampData[$c]['business_id'];
                            }
                        }
                    }
                    if (count($routedBusinessIdArr) > 0) {
                        if(!in_array(993, $routedBusinessIdArr) === false) {  // 993
                            $leadactiviyobj1 = new LeadActivity();
                            $followupLeadData = LeadFollowUp::where('lead_id', $leadId)->first();
                            if(isset( $followupLeadData )){
                                $leadactiviyobj1->createActivity($leadId, 13);
                            }
                            $bufferLeadData = LeadCallBuffer::where('lead_id', $leadId)->first();
                            if(isset( $bufferLeadData )) {
                                $leadactiviyobj1->createActivity($leadId, 8);
                            }                     
                            LeadCallBuffer::where('lead_id', $leadId)->delete();
                            LeadFollowUp::where('lead_id', $leadId)->delete();
                        }
                        $getBusinessData = Business::whereIn('business_id', $routedBusinessIdArr)->where('buyer_type_id', '!=', 3)->get(['business_id', 'logo', 'rely_number', 'display_name'])->toArray();
                        for ($m = 0; $m < count($getBusinessData); $m++) {
                            $routedBusinessDataArr[$getBusinessData[$m]['business_id']] = $getBusinessData[$m];
                        }
                    }
                    //echo "<pre>";print_r($routedBusinessDataArr);die;
                    foreach ($leadRoutingData as $businessesCampaign) {
                        if (isset($routedCampDataArr[$businessesCampaign['campaign_id']])) {
                            $busCampaignInformation = $routedCampDataArr[$businessesCampaign['campaign_id']];
                            $campaignIdFinal[] = $busCampaignInformation['campaign_id'];
                            if (isset($routedBusinessDataArr[$busCampaignInformation['business_id']])) {
                                $businessesDetails = $routedBusinessDataArr[$busCampaignInformation['business_id']];

                                $subscriptionPlan = Subscription::where('business_id', $businessesDetails['business_id'])->where('is_active', 'yes')
                                    ->first();
                                if($subscriptionPlan) {
                                    $planDetails = MstSubscriptionPlan::where('subscription_plan_id', $subscriptionPlan->subscription_plan_id)->first();
                                    if($planDetails) {
                                        if($planDetails->subscription_plan_id == 2) {
                                            $callCountN = $planDetails->call_count;
                                            if($callCountN > 0) {
                                                $startOfMonth = Carbon::now()->startOfMonth();
                                                $endOfMonth = Carbon::now()->endOfMonth();
                                                $leadCallCount = LeadCall::where('transfer_type', 'businessrely')
                                                ->where('business_id', $businessesDetails['business_id'])
                                                ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
                                                ->count();

                                                if($leadCallCount >= $callCountN) {
                                                    $businessesDetails['rely_number'] = '';
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    $businessesDetails['rely_number'] = '';
                                }

                                if ($businessesDetails['logo'] != '' && file_exists(public_path() . '/images/businesslogo/' . $businessesDetails['logo'])) {
                                    $businesslogo = $images_url . '/images/businesslogo/' . $businessesDetails['logo'];
                                } else {
                                    $businesslogo = $images_url . '/images/businesslogo/movers.png'; // URL::to('/')
                                }
                                $matchCompany[$businessesDetails['display_name']] = $businessesDetails['rely_number'] . '|' . $businesslogo;
                            }
                        }
                    }
                    $this->LeadRoutelog($leadId, 'Lead Matched Campaign == ' . json_encode($matchCompany), 62);
                    //echo "<pre>";print_r($matchCompany);die;
                    return $matchCompany;
                } else {
                    $this->LeadRoutelog($leadId, 'Lead Matched Campaign == ' . json_encode($matchCompany), 63);
                    return $matchCompany;
                }
            }
        } catch (Exception $ex) {
            $this->LeadRoutelog($leadId, 'Catch error when reroute lead == ' . $ex->getMessage(), 64);
            Log::info('64 Catch error when reroute lead == ' . $ex->getMessage() . $leadId);
            if ($directRoute == 1) {
                return $matchCompany;
            }
            dd($ex->getMessage());
        }
    }

    public function reRouteVerifiedLead($leadid) {
        Lead::where('lead_id', $leadid)->update(['is_verified' => 'yes']);

        $endDecObj = new CommonFunctions;
        $leadId = $endDecObj->customeEncryption($leadid);
        $this->reRouteLead($leadId, 0, 0);
        return redirect()->back();
    }

    public function LeadRoutelog($leadid, $log, $type) {
        Logs::create([
            'lead_id' => $leadid,
            'log' => $log,
            'log_srno' => $type,
            'created_at' => date("Y-m-d H:i:s")
        ]);
    }

    public function routeLead() {

        $this->LeadRoutelog(0, 'routeLead start', 65);
        try {

            $this->LeadRoutelog(0, 'routeLead try 1', 65);
            $date = new DateTime();
            $date->modify('-120 minutes');
            $formatted_date = $date->format('Y-m-d H:i:s');

            $date1 = new DateTime();
            $date1->modify('-10 seconds');
            $formatted_date1 = $date1->format('Y-m-d H:i:s');
            $this->LeadRoutelog(0, 'routeLead startdata= ' . $formatted_date . ', enddate= ' . $formatted_date1, 65);
            $leadDetails = Lead::whereBetween('lead_generated_at', [$formatted_date, $formatted_date1])
                            ->where('email', 'not like', "%test%")->where('email', 'not like', "%@test%")->where('email', '<>', "<EMAIL>")
                            ->where("is_verified", "yes")
                            ->where("is_handled", "no")
                            ->where(function ($q) {
                                $q->where('payout', '=', 0)
                                ->orWhereNull('payout');
                            })
                            // ->where('payout', 0)->orWhereNull('payout')
                            ->get()->take(1)->toArray();
            $this->LeadRoutelog(0, 'routeLead leaddata= ' . json_encode($leadDetails, true), 65);

            if (count($leadDetails) == 0) {
                $this->LeadRoutelog(0, 'routeLead lead not found', 65);
                Log::info('65 routeLead lead not found == ' . json_encode($leadDetails));
                throw new Exception("Lead not available");
            } else {
                $leadId = $leadDetails[0]['lead_id'];
                $this->LeadRoutelog($leadId, 'routeLead lead= ' . $leadId . ', payout= ' . $leadDetails[0]['payout'], 65);
                $endDecObj = new CommonFunctions;
                $leadIdEncrypt = $endDecObj->customeEncryption($leadId);
                $matchCompany = $this->reRouteLead($leadIdEncrypt, 1, 0);
                $leadDetails = Lead::where("lead_id", $leadId)->update(['is_handled' => "yes"]);
                $leaddata = array($leadId => $matchCompany);
                $this->LeadRoutelog($leadId, 'routeLead matchCompany= ' . json_encode($leaddata), 65);
                return json_encode($leaddata);
            }
        } catch (Exception $ex) {
            $this->LeadRoutelog(0, 'routeLead catch error= ' . json_encode($ex->getMessage(), true), 65);
            Log::info('65 routeLead catch error= ' . json_encode($ex->getMessage(), true));
            return json_encode($ex->getMessage());
        }
        // echo "<pre>";
        // print_r($leadData);
        // die;           
    }

    public function getLeadCategoryType($leadId) {
        $isVerified = $sourceId = 0;
        $getLeadsCategory = Lead::with('campaignInfo')->where('lead_id', $leadId)->get()->toArray();
        $data = array();
        $leadKeyWord = "";
        $keyWordArr = array("haul","penske","budget","enterprise","truck","rental");
        if (count($getLeadsCategory) > 0) {
            $isVerified = $getLeadsCategory[0]['is_verified'];
            $sourceId = $getLeadsCategory[0]['lead_source_id'];
            if(isset($getLeadsCategory[0]['campaign_info']['search_keyword']) && trim($getLeadsCategory[0]['campaign_info']['search_keyword']) != ""){
                $keyWord = strtolower(trim($getLeadsCategory[0]['campaign_info']['search_keyword']));
                foreach($keyWordArr as $key=>$val){
                    if (str_contains($keyWord, $val)) {
                        $leadKeyWord = $keyWord;
                        $this->LeadRoutelog($leadId, 'Word Found :' . $leadKeyWord . "== lead id==" . $leadId, 65);
                        continue;
                    }
                }                
            }
        }
        $data['keyword'] = $leadKeyWord;
        $data['origin_id'] = $sourceId;
        return $data;
    }

}