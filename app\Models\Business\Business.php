<?php

namespace App\Models\Business;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Contract\Contract;
use App\Models\User;

class Business extends Model
{
    use HasFactory;
    protected $table = "business";
    protected $primaryKey = "business_id";

    public $timestamps = false;

    protected $fillable = [
        "business_id",
        "business_name",
        "display_name",
        "buyer_type_id",
        "payment_type",
        "owner_name",
        "contact_name",
        "license_number",
        "dot_number",
        "address",
        "email",
        "notification_email",
        "business_phone",
        "notification_phone",
        "emergency_phone",
        "rely_number",
        "forward_number",
        "status",
        "logo",
        "credit_available",
        "credit_reserved",
        "is_low_fund_notification",
        "low_fund_notification_email",
        "low_fund_notification_phone",
        "low_fund_amount",
        "payment_cust_id",
        "is_contract_signed",
        "movers_username",
        "movers_password",
        "company_url",
        "created_at",
        "updated_at",
        "created_user_id",
        "updated_user_id"
    ];

    public function mstBuyerType(){
        return $this->belongsTo('App\Models\Master\MstBuyerType', 'buyer_type_id');
    }

    public function businessDailytLimit(){
        return $this->hasMany('App\Models\Business\BusinessDailyLimit', 'business_id');
    }

    public function businessUpdateLog(){
        return $this->hasMany('App\Models\Business\BusinessUpdateLog', 'business_id');
    }

    public function businessCampaign() {
        return $this->hasMany('App\Models\Campaign\Campaign', 'business_id');
    }

    public function businessCC() {
        return $this->hasMany('App\Models\Business\BusinessCC', 'business_id');
    }

    public function isContractSigned() {
        return $this->belongsTo(Contract::class, 'contract_id')->where('status', 'signed');
    }
    public function businessNotes() {
        return $this->hasMany('App\Models\Business\BusinessNote', 'business_id');
    }
    public function user() {
        return $this->hasOne(User::class, 'id', 'created_user_id');
    }
    public function businessBadges() {
        return $this->hasMany('App\Models\Business\BusinessBadges', 'business_id');
    }
}
