<?php

namespace App\Http\Controllers\Crm;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Helpers\CommonFunctions;
use Illuminate\Support\Facades\Validator;
use Auth;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Exception;
use App\Models\Delivery\SnapDeliveryFields;
use App\Models\Delivery\DeliveryMappingFields;
use App\Models\Delivery\DeliveryCrm;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Hash;
use App\Models\Campaign\LeadDeliveryCrmDefaultFields;
use App\Models\Campaign\CampaignCrmDelivery;
use App\Models\Campaign\CampaignCrmDeliveryLog;
use App\Models\Lead\Lead;
use App\Models\Lead\LeadRouting;
use App\Models\Lead\LeadMoving;
use App\Models\Lead\LeadMatches;
use App\Models\Lead\LeadRoutingLog;
use App\Models\Lead\LeadJunkRemoval;
use App\Models\Lead\LeadCarTransport;
use App\Models\Lead\LeadHeavyLifting;
use App\Models\Master\MstZipcode;
use App\Models\Master\MstCarType;
use App\Models\User;
use App\Models\Lead\LeadRequestLog;
use App\Models\Master\MstLeadCategory;
use App\Models\Campaign\CampaignLeadDeliveryResponse;
use App\Models\Emails\LeadNotDeliveredEmail;
use App\Models\Emails\EmailLog;
use App\Models\Campaign\Campaign;

class CrmController extends Controller {
    private $per_page = 10;
    function __construct() {
        $this->middleware('permission:crm-list', ['only' => ['getcrmpages']]);
        $this->middleware('permission:crmAdd', ['only' => ['crm-create']]);
        $this->middleware('permission:crmEdit', ['only' => ['crm-edit']]);
        $this->middleware('permission:user-delete', ['only' => ['crm-delete']]);
    }

    public function getcrmData( $lead_category) {
        $deliveryFields = SnapDeliveryFields::get()->toArray();
        $moveArray = [];
        foreach ($deliveryFields as $key => $value) {

            if($lead_category == 1){
                if( $value['field_name'] != 'owner_type' &&
                    $value['field_name'] != 'junk_type' &&
                    $value['field_name'] != 'junk_sub_type' &&
                    $value['field_name'] != 'heavy_lifting_type' &&
                    $value['field_name'] != 'is_required_assistence' &&
                    $value['field_name'] != 'is_operational' &&
                    $value['field_name'] != 'car_type' &&
                    $value['field_name'] != 'transport_type' &&
                    $value['field_name'] != 'car_condition'){
                    $moveArray[] = $value;
                }
            } else if($lead_category == 2){
                if( $value['field_name'] != 'move_size' &&
                    $value['field_name'] != 'heavy_lifting_type' &&
                    $value['field_name'] != 'is_required_assistence' &&
                    $value['field_name'] != 'is_operational' &&
                    $value['field_name'] != 'to_state' &&
                    $value['field_name'] != 'to_city' &&
                    $value['field_name'] != 'car_type' &&
                    $value['field_name'] != 'transport_type' &&
                    $value['field_name'] != 'car_condition' &&
                    $value['field_name'] != 'extra_movesize_in_lbs' &&
                    $value['field_name'] != 'movesize(room)' &&
                    $value['field_name'] != 'movesize( bedroom)' &&
                    $value['field_name'] != 'movesize(_bedroom)' &&
                    $value['field_name'] != 'field_bedroom_size_1'){

                    $moveArray[] = $value;
                }
            } else if($lead_category == 3){
                if( $value['field_name'] != 'move_size' &&
                    $value['field_name'] != 'move_type' &&
                    $value['field_name'] != 'owner_type' &&
                    $value['field_name'] != 'junk_type' &&
                    $value['field_name'] != 'junk_sub_type' &&
                    $value['field_name'] != 'car_type' &&
                    $value['field_name'] != 'transport_type' &&
                    $value['field_name'] != 'car_condition' &&
                    $value['field_name'] != 'extra_movesize_in_lbs' &&
                    $value['field_name'] != 'movesize(room)' &&
                    $value['field_name'] != 'movesize( bedroom)' &&
                    $value['field_name'] != 'movesize(_bedroom)' &&
                    $value['field_name'] != 'field_bedroom_size_1'){
                    $moveArray[] = $value;
                }
            } else if($lead_category == 4){
                if( $value['field_name'] != 'move_size' &&
                    $value['field_name'] != 'move_type' &&
                    $value['field_name'] != 'heavy_lifting_type' &&
                    $value['field_name'] != 'owner_type' &&
                    $value['field_name'] != 'junk_type' &&
                    $value['field_name'] != 'junk_sub_type' &&
                    $value['field_name'] != 'extra_movesize_in_lbs' &&
                    $value['field_name'] != 'movesize(room)' &&
                    $value['field_name'] != 'movesize( bedroom)' &&
                    $value['field_name'] != 'movesize(_bedroom)' &&
                    $value['field_name'] != 'field_bedroom_size_1'){
                    $moveArray[] = $value;
                }
            }
        }
        //     echo '<pre>';print_r($moveArray);
        //    die;
        $crmData = DeliveryCrm::with(['DeliveryMappingFields', 'DeliveryMappingFields.SnapDeliveryFields', "mstLeadCategory"])->where('lead_category_id', $lead_category)->orderBy('delivery_crm_id', 'DESC')->get();
        foreach ($crmData as $key => $value) {
            // echo '<pre>';print_r($value->DeliveryMappingFields->groupBy('root_element') );
            $value->DeliveryMappingFields = $value->DeliveryMappingFields->groupBy('root_element');
        }
        // echo '<pre>';print_r($crmData );
        // die;
        $leadCategories = MstLeadCategory::get();
        $data = [
            'crmRecord' => $crmData,
            'deliveryFields' => $moveArray,
            'leadCategories' => $leadCategories
        ];
        return $data;
        // return view("crm.index")->with(['crmRecord' => $crmData, 'deliveryFields' => $deliveryFields, 'leadCategories' => $leadCategories]);
    }

    public function getcrmpages($leadCategory) {
        if($leadCategory == 'moving'){
            $datacrmmoving = $this->getcrmData(1);
            return view("crm.crmmoving")->with($datacrmmoving);
        }else if($leadCategory == 'junk-removal'){
            $datacrmjunkremoval = $this->getcrmData(2);
            return view("crm.crmjunkremoval")->with($datacrmjunkremoval);
        }else if($leadCategory == 'heavy-lifting'){
            $datacrmheavylifting = $this->getcrmData(3);
            return view("crm.crmheavylifting")->with($datacrmheavylifting);
        }else if($leadCategory == 'car-transport'){
            $datacrmcartransport = $this->getcrmData(4);
            return view("crm.crmcartransport")->with($datacrmcartransport);
        }
    }

    public function sendtestcrm(Request $request){
        $crm_id = 0;
        if(isset($request->crm_id)) {
            $crm_id = $request->crm_id;
        }
        //  echo '<pre>';
        //  print_r($request->all());

        $deliveryMethods = DB::table('delivery_mapping_fields')->select("delivery_mapping_fields.*", "snap_delivery_fields.field_name as lead_param_name", "delivery_crm.movedate_format as crmmovedate_format")
            ->join('snap_delivery_fields', 'snap_delivery_fields.snap_delivery_field_id', '=', 'delivery_mapping_fields.snap_delivery_field_id')
            ->join('delivery_crm', 'delivery_crm.delivery_crm_id', '=', 'delivery_mapping_fields.delivery_crm_id')
            ->where("delivery_mapping_fields.delivery_crm_id", $crm_id)
            ->get();
        $deliveryMethods = $deliveryMethods->groupBy('root_element')->toArray();
        $crmDataHtml = "";
        // echo '<pre>';print_r($deliveryMethods);exit;
        if(count($deliveryMethods) > 0) {
            // echo '<pre> deliveryMethods > 0 / ';

            $crmDelivery = DeliveryCrm::where("delivery_crm_id", $crm_id)->first();

            $delivery_method = $crmDelivery->delivery_type;
            $delivery_url = $crmDelivery->delivery_url;
            $post_type = $crmDelivery->request_format;
            $crmDataHtml .= '<table class="table"><thead class="thead-light"><tr><th scope="col">Parameter</th><th scope="col">Value</th></tr></thead><tbody>';
            // echo '<pre> / crmDataHtml =';
            $rootname = "";
            // echo "<prE>";
            // print_r($deliveryMethods ); // die;
            if(count($deliveryMethods) > 0){
                foreach($deliveryMethods as $key => $mappingData) {
                    // echo '<pre> / mappingData =';
                    if(count($mappingData ) > 0){
                        foreach($mappingData as $mappingkey => $ld) {
                            // echo '<pre> / mappingkey =';
                            $rootname = $key;
                            if( $mappingkey == 0){
                                // echo '<pre> / mappingkey = 0 / ';
                                $crmDataHtml .= '<tr><td style="border-bottom-width: 0;"><h5>'.$key.'</h5></td></tr>';
                            }
                            $randaom = array("Kay", "Joe","Susan", "Frank","John","Steave","Michal","Jonsan","Jackson","Carter","Wyatt","Grayson","Julian","Christian","Hunter","Easton","Colton","Jack","Cott","Koltin","David","Michael","Tommy","Lewis","Nathan","Ronnie","Albert");
                            $randIndex = array_rand($randaom,3);
                            $crmDataHtml .= '<tr>
                                <td>'.$ld->post_para_key.'</td>
                                <td>';
                            $default_value = "";
                            if($ld->lead_param_name == 'name') {
                                $default_value = $randaom[$randIndex[0]] ;
                                // $default_value = 'TestABC' ;
                            } else if($ld->lead_param_name == 'email') {
                                $default_value = '<EMAIL>';
                            } else if($ld->lead_param_name == 'from_state') {
                                $default_value = 'ak';
                            } else if($ld->lead_param_name == 'to_state') {
                                $default_value = 'ak';
                            } else if($ld->lead_param_name == 'from_zip') {
                                $default_value = '99501';
                            } else if($ld->lead_param_name == 'to_zip') {
                                $default_value = '35005';
                            } else if($ld->lead_param_name == 'from_area') {
                                $default_value = '907';
                            } else if($ld->lead_param_name == 'to_area') {
                                $default_value = '907';
                            } else if($ld->lead_param_name == 'from_city') {
                                $default_value = 'anchorage';
                            } else if($ld->lead_param_name == 'to_city') {
                                $default_value = 'anchorage';
                            } else if($ld->lead_param_name == 'move_size') {
                                $default_value =  3;
                            } else if($ld->lead_param_name == 'phone_number') {
                                $default_value = rand(1000000000,9000000000);
                                // $default_value = '6987654321';
                            } else if($ld->lead_param_name == 'first_name') {
                                $default_value = $randaom[$randIndex[0]];
                                // $default_value = 'TestTest';
                            } else if($ld->lead_param_name == 'last_name') {
                                $default_value = 'Test';
                            } else if($ld->lead_param_name == 'move_date') {
                                if($ld->crmmovedate_format != NULL || $ld->crmmovedate_format != ""){
                                    $default_value = date($ld->crmmovedate_format, strtotime('+2 week'));
                                }else{
                                    $default_value = date("Y-m-d", strtotime('+2 week'));
                                }
                            } else if($ld->lead_param_name == 'ip') {
                                $default_value = $_SERVER['REMOTE_ADDR'];
                            } else if($ld->lead_param_name == 'origin') {
                                $default_value = "Test";
                            } else if($ld->lead_param_name == 'is_verified') {
                                $default_value = "1";
                            } else if($ld->lead_param_name == 'IP') {
                                $default_value = "127.0.0.1";
                            }

                            $inputType = "";
                            $inputClass = ' class="form-control" ';
                            $inputExtraCondition = "";
                            $maxLength = "";
                            if($ld->lead_param_name == 'email') {
                                $inputType = ' type="email" ';
                            } else {
                                $inputType = ' type="text" ';
                            }
                            if($ld->lead_param_name == 'move_date') {
                                $inputClass = ' class="datepicker form-control" ';
                            }
                            if($ld->lead_param_name == 'from_zip' || $ld->lead_param_name == 'to_zip' || $ld->lead_param_name == 'phone' || $ld->lead_param_name == 'move_size') {
                                $inputExtraCondition = "";
                            }
                            if($ld->lead_param_name == 'from_zip' || $ld->lead_param_name == 'to_zip') {
                                $maxLength = ' maxlength="5" minlength="5" ';
                            }
                            $default_value = $default_value;
                            if($ld->lead_param_name == 'extra_movesize_in_lbs') {
                                $default_value = "0 lbs";
                            }
                            if($ld->lead_param_name == 'movesize(room)') {
                                $default_value = "1 Room";
                            }
                            if($ld->lead_param_name == 'movesize( bedroom)') {
                                $default_value = "1 Bedroom";
                            }
                            if($ld->lead_param_name == 'movesize(_bedroom)') {
                                $default_value = "1_bedroom";
                            }
                            if($ld->lead_param_name == 'field_bedroom_size_1') {
                                $default_value = "2";
                            }
                            $crmDataHtml .= '<input '.$inputType.' '.$inputClass.' '.$inputExtraCondition.' '.$maxLength.' autocomplete="off" name="default_value[]" value="'.$default_value.'" />';
                            $crmDataHtml .= '<input type="hidden" name="client_param_name[]" value="'.$ld->post_para_key.'" />';
                            $crmDataHtml .= '<input type="hidden" name="lead_param_name[]" value="'.$ld->lead_param_name.'" />';
                            $crmDataHtml .= '<input type="hidden" name="itm_id[]" value="'. $ld->delivery_mapping_fields_id .'" />';

                            $crmDataHtml .= '<input type="hidden" name="root_data[]" value="'. $rootname .'" /></td>      </tr>';

                        }
                    }
                    // echo "/ Hello 123 / ";
                    // die;
                }
            }
            // echo '<pre> / tbody  ';
            $crmDataHtml .= '</tbody></table>';


            $crmDataHtml .= '<div class="post_template" style="word-break: break-word;"></div>';
            $crmDataHtml .= '<input type="hidden" id="delivery_method" name="delivery_method" value="'.$delivery_method.'" />
            <input type="hidden" id="delivery_url" name="delivery_url" value="'.$delivery_url.'" />
            <input type="hidden" id="post_type" name="post_type" value="'.$post_type.'" />
            <input type="hidden" id="crmid" name="crmid" value="'.$crm_id.'" />';

            // echo '<pre> / tbody complete ';
        }
        // echo '<pre> / full tbody complete ';
        $responseData = array(
            'status' => 200,
            'message' => 'CRM Fields lists.',
            'crmDataHtml' => $crmDataHtml,
            'data' => $deliveryMethods
        );
        // echo '<pre> / die';
        // die;
        return response()->json($responseData);
    }
    public function getMoveSizeArr_bedroom()
    {
        $moveSizeArray = array(1 => "Studio", 2 => "1 Bedroom", 3 => "2 Bedrooms", 4 => "3 Bedrooms", 5 => "4 Bedrooms", 6 => "5+ Bedrooms");
        return $moveSizeArray;
    }
    public function getMoveSizeArr_room() {
        $moveSizeArray = array(1 => "studio", 2 => "1_room", 3 => "2_rooms", 4 => "3_rooms", 5 => "4_rooms", 6 => "5+_rooms");
        return $moveSizeArray;
    }
    public function getMoveSizeArr_bedrooms() {
        $moveSizeArray = array(1 => "studio", 2 => "1_bedroom", 3 => "2_bedrooms", 4 => "3_bedrooms", 5 => "4_bedrooms", 6 => "5+_bedrooms");
        return $moveSizeArray;
    }
    public function getMoveSizeArr_bedroom_extra() {
        $moveSizeArray = array(1 => 2, 2 => 4, 3 => 6, 4 => 9, 5 => 10, 6 => 10);
        return $moveSizeArray;
    }
    public function deliveryMethodTest(Request $request){
        $dataArr = array();
        for($f = 0; $f < count($request->client_param_name); $f++) {
            if($request->root_data[$f] != "" && $request->root_data[$f] != NULL){
                $dataArr[$request->root_data[$f]][$request->client_param_name[$f]] = $request->default_value[$f];
            }else{
                $dataArr[$request->client_param_name[$f]] = $request->default_value[$f];
            }
        }
        // echo '<pre>';print_r($dataArr );
        $logAction = "";
        if($request->delivery_url != '') {
            $checkUrlVal = get_headers($request->delivery_url);
            $checkUrlValStr = $checkUrlVal[0];

            // if(strpos($checkUrlValStr,"200")) {
            if($request->post_type == 'HTTP') {

                if($request->delivery_method == 'post') {
                    $logAction = "Post Method with HTTP Request.";
                    $responseVar = self::postMethod($request->delivery_url, $dataArr);
                } else if($request->delivery_method == 'get') {
                    $logAction = "Get Method with HTTP Request.";
                    $responseVar = self::getMethod($request->delivery_url, $dataArr);
                } else {
                    $logAction = "Xml Method with HTTP Request.";
                    $responseVar = self::xmlMethod($request->delivery_url, $dataArr);
                }
            } else if($request->post_type == 'XML') {
                $logAction = "Xml Method with HTTP Request.";
                $responseVar = self::xmlMethod($request->delivery_url, $dataArr);
            } else {
                $logAction = "JSON Request.";
                $responseVar = self::jsonMethod($request->delivery_url, $dataArr);
            }
            echo '<p><strong>URL:</strong> '.$request->delivery_url.'<br /><strong>Request:</strong> '.json_encode($dataArr, JSON_UNESCAPED_SLASHES).'<br /><strong>Response:</strong> '.$responseVar.'</p>';
            $response_text = 'Failed';
            // $response = json_decode($responseVar, true);
            // foreach ($response as $key => $value) {
            if ($responseVar == "" || str_contains($responseVar, 'success') || str_contains($responseVar, 'Success')
                || str_contains($responseVar, 'SUCCESSS') || str_contains($responseVar, true)
                || str_contains($responseVar, True) || str_contains($responseVar, TRUE)) {
                $response_text = 'Success';
            } else if (str_contains($responseVar, 'error') || str_contains($responseVar, 'Error')
                || str_contains($responseVar, 'ERROR')|| str_contains($responseVar, 'failure')
                || str_contains($responseVar, 'Failure') || str_contains($responseVar, 'FAILURE')
                || str_contains($responseVar, 'failed') || str_contains($responseVar, 'Failed')
                || str_contains($responseVar, 'FAILED') || str_contains($responseVar, 'reject')
                || str_contains($responseVar, 'Reject') || str_contains($responseVar, 'REJECT')
                || str_contains($responseVar, 'rejected') || str_contains($responseVar, 'Rejected')
                || str_contains($responseVar, 'REJECTED') || str_contains($responseVar, false)
                || str_contains($responseVar, False) || str_contains($responseVar, FALSE) ) {
                $response_text = 'Failed';
            }
            // }
            CampaignCrmDeliveryLog::create([
                'campaign_id' => 0,
                'url' => $request->delivery_url,
                'request_parameter' => trim(json_encode($dataArr, JSON_UNESCAPED_SLASHES)),
                'response_parameter' => $responseVar,
                'lead_id' => 0,
                'log_type' => $logAction,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            echo '<p><strong>Response Status:</strong> '. $response_text .'</p>';
            // } else {
            //     echo 'url does not exist '.$request->delivery_url;
            // }
        }
    }

    public function crmAdd(Request $request)
    {
        // $regex = '/^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/';
        $validator = Validator::make($request->all(),[
            // 'delivery_url'=>'required|regex:'.$regex,
            'crmName'=>'required',
            'deliveryUrl'=>'required',
            'deliveryType' => 'required',
            'requestFormat' => 'required',
            'responseSuccess' => 'required',
        ],
            // [
            //     'crmName.required' => 'Please Enter CRM Name',
            //     'deliveryUrl.required' => 'Please Enter Delivery URL',
            //     // 'delivery_url.regex' => 'Invalid Delivery URL Format',
            //     'deliveryType.required' => 'Please Select Method Type',
            //     'requestFormat.required' => 'Please Select CRM Request Format',
            //     'requestFormat.required' => 'Please Select CRM Request Format'
            // ]
        );

        if ($validator->fails()) {
            $responseData =array(
                'status' => 400,
                'message' => 'There are incorect values in the form!',
                'error' => $validator->getMessageBag()->toArray()
            );
        } else {
            $crmName = $request->get('crmName');
            $deliveryUrl = $request->get('deliveryUrl');
            $deliveryType = $request->get('deliveryType');
            $requestFormat = $request->get('requestFormat');
            $lead_category = $request->get('lead_category');
            $requestHeader = $request->get('requestHeader');
            $movedate_format = $request->get('movedate_format');
            $responseSuccess = $request->get('responseSuccess');
            $responseFailed = $request->get('responseFailed');
            $leaddate_format = $request->get('leaddate_format');

            $deliveryMethod = new DeliveryCrm();
            $deliveryMethod->name = $crmName;
            $deliveryMethod->delivery_url = $deliveryUrl;
            $deliveryMethod->lead_category_id = $lead_category;
            $deliveryMethod->request_header = $requestHeader;
            $deliveryMethod->delivery_type = $deliveryType;
            $deliveryMethod->request_format = $requestFormat;
            $deliveryMethod->movedate_format = $movedate_format;
            $deliveryMethod->leaddate_format = $leaddate_format;
            $deliveryMethod->created_at = date("Y-m-d H:i:s");
            $deliveryMethod->save();
            $delivery_crm_id = $deliveryMethod->delivery_crm_id;

            $deliveryresponseData = [];
            if(!empty($responseSuccess) && isset($responseSuccess) && $responseSuccess != "" || $responseSuccess != NULL){
                $deliveryresponseData[] = [
                    "delivery_crm_id" => $delivery_crm_id,
                    "response_type" => 'success',
                    "response_text" => $responseSuccess,
                    "created_at" => date("Y-m-d H:i:s")
                ];
            }
            if(!empty($responseFailed) && isset($responseFailed) && $responseFailed != "" || $responseFailed != NULL){
                $deliveryresponseData[] = [
                    "delivery_crm_id" => $delivery_crm_id,
                    "response_type" => "reject",
                    "response_text" => $responseFailed,
                    "created_at" => date("Y-m-d H:i:s")
                ];
            }
            if(count($deliveryresponseData) > 0 ){
                CampaignLeadDeliveryResponse::insert( $deliveryresponseData );
            }
            $responseData = array(
                'status' => 200,
                'message' => 'CRM added successfully',
                'data' => $deliveryMethod
            );
        }
        return response()->json($responseData);
    }

    public function crmListByCampaignId(Request $request) {
        $campaign_id = 0;
        $campaign_id = $request->get('campaign_id');

        $crmData = DeliveryCrm::select("campaign_crm_delivery.*")->join('campaign_crm_delivery', 'campaign_crm_delivery.delivery_crm_id', '=', 'delivery_crm.delivery_crm_id')->where("campaign_id", $campaign_id)->get();

        $responseData = array(
            'status' => 200,
            'message' => 'Get CRM List',
            'data' => $crmData
        );
        return response()->json($responseData);
    }

    public function crmFetch(Request $request) {
        $status = 0;
        $message = 'fail';
        $businessesArray = array();
        $crmArray = array();
        $crmData = NULL;
        try {
            $crmData = DeliveryCrm::with(['campaignLeadDeliveryResponse'])->where('delivery_crm_id', '=', $request->get('crmId'))->first();
            //echo $request->get('crmId');
            //echo '<pre>';print_r($crmData);
            // $crmname = $crmData->crmName;

            // $crmArray = [
            //     'crmId' => $crmData->delivery_crm_id,
            //     'name' => $crmData->name,
            //     'delivery_url' => $crmData->delivery_url,
            //     'delivery_type' => $crmData->delivery_type,
            //     'request_format' => $crmData->request_format
            // ];
            $status = 1;
            $message = 'Success';
        }
        catch(Exception $e) {
            $message = $e->getMessage();
        }

        $responseData = [
            'status' => $status,
            'message' => $message,
            'data' => $crmData
        ];

        return response()->json($responseData);
    }

    public function crmEdit(Request $request)
    {

        $validator = Validator::make($request->all(),[
            'crmName'=>'required',
            'deliveryUrl'=>'required',
            'deliveryType' => 'required',
            'requestFormat' => 'required'
        ],
            // [
            //     'crmName.required' => 'Please Enter CRM Name',
            //     'deliveryUrl.required' => 'Please Enter Delivery URL',
            //     'deliveryType.required' => 'Please Select Method Type',
            //     'requestFormat.required' => 'Please Select CRM Request Format'
            // ]
        );

        if ($validator->fails()) {
            $responseData =array(
                'status' => 400,
                'message' => 'There are incorect values in the form!',
                'error' => $validator->getMessageBag()->toArray()
            );
        } else {
            $crmId = $request->get('crm_id');
            $crmName = $request->get('crmName');
            $deliveryUrl = $request->get('deliveryUrl');
            $deliveryType = $request->get('deliveryType');
            $requestFormat = $request->get('requestFormat');
            $lead_category = $request->get('lead_category');
            $requestHeader = $request->get('requestHeader');
            $movedate_format = $request->get('movedate_format');
            $leaddate_format = $request->get('leaddate_format');
            $responseSuccess = $request->get('responseSuccess');
            $responseFailed = $request->get('responseFailed');

            $deliveryMethod = DeliveryCrm::find($crmId);
            $deliveryMethod->name = $crmName;
            $deliveryMethod->delivery_url = $deliveryUrl;
            $deliveryMethod->lead_category_id = $lead_category;
            $deliveryMethod->request_header = $requestHeader;
            $deliveryMethod->delivery_type = $deliveryType;
            $deliveryMethod->request_format = $requestFormat;
            $deliveryMethod->movedate_format = $movedate_format;
            $deliveryMethod->leaddate_format = $leaddate_format;
            $deliveryMethod->updated_at = date("Y-m-d H:i:s");
            $deliveryMethod->save();
            $deliveryresponseData = [];
            if(!empty($responseSuccess) && isset($responseSuccess) && $responseSuccess != "" || $responseSuccess != NULL){
                $exist_success = CampaignLeadDeliveryResponse::where('delivery_crm_id', $crmId)->where( 'response_type', 'success' )->first();
                if($exist_success ){
                    CampaignLeadDeliveryResponse::where('delivery_crm_id', $crmId)->where( 'response_type', 'success' )->update(["response_text" => $responseSuccess, "updated_at" => date("Y-m-d H:i:s")]);
                }else{
                    $deliveryresponseData[] = [
                        "delivery_crm_id" => $crmId,
                        "response_type" => 'success',
                        "response_text" => $responseSuccess,
                        "created_at" => date("Y-m-d H:i:s")
                    ];
                }
            }else{
                CampaignLeadDeliveryResponse::where('delivery_crm_id', $crmId)->where( 'response_type', 'success' )->delete();
            }
            if(!empty($responseFailed) && isset($responseFailed) && $responseFailed != "" || $responseFailed != NULL){
                $exist_reject = CampaignLeadDeliveryResponse::where('delivery_crm_id', $crmId)->where( 'response_type', 'reject' )->first();
                if($exist_reject ){
                    CampaignLeadDeliveryResponse::where('delivery_crm_id', $crmId)->where( 'response_type', 'reject' )->update(["response_text" => $responseFailed, "updated_at" => date("Y-m-d H:i:s")]);
                }else{
                    $deliveryresponseData[] = [
                        "delivery_crm_id" => $crmId,
                        "response_type" => "reject",
                        "response_text" => $responseFailed,
                        "created_at" => date("Y-m-d H:i:s")
                    ];
                }
            }else{
                CampaignLeadDeliveryResponse::where('delivery_crm_id', $crmId)->where( 'response_type', 'reject' )->delete();
            }
            if(count($deliveryresponseData) > 0 ){
                CampaignLeadDeliveryResponse::insert( $deliveryresponseData );
            }
            $responseData = array(
                'status' => 200,
                'message' => 'CRM updated successfully',
                'data' => $deliveryMethod
            );
        }
        return response()->json($responseData);
    }

    public function getCRMFileds(Request $request) {
        $status = 0;
        $message = 'fail';
        $data = array();
        try {
            $crmId = $request->id;
            $deliveryMappingFields = DeliveryMappingFields::where('delivery_crm_id', $crmId)->where('snap_delivery_field_id', 1)->get()->toArray();

            $status = 1;
            $message = 'success';
            $data = "";

            foreach ($deliveryMappingFields as $value) {
                $data .= '<div class="mb-3"><label for="txtCrmDefault">'.$value['post_para_key'].'</label><div class="input-group"><input type="text" class="form-control crm_default_field" name="txtCrmDefault" value="'.$value['default_value'].'" data-filedname="'.$value['post_para_key'].'" data-mappingfield="'.$value['delivery_mapping_fields_id'].'" /></div></div>';
            }

        } catch(Exception $e) {
            $message = $e->getMessage();
        }
        //echo $crmId."<pre>";print_r($deliveryMappingFields);die;
        $jsonResponse = [
            'status' => $status,
            'message' => $message,
            'data' => $data
        ];

        return response()->json($jsonResponse);
    }

    public function getCRMParameters(Request $request) {
        $status = 0;
        $message = 'fail';
        $data = array();
        try {
            $crmId = $request->get('id');
            $deliveryMappingFields = DeliveryMappingFields::with('SnapDeliveryFields')->where('delivery_crm_id', $crmId)->get()->toArray();

            $status = 1;
            $message = 'success';
            $data = $deliveryMappingFields;

        } catch(Exception $e) {
            $message = $e->getMessage();
        }
        //echo "<pre>";print_r($data);die;
        $jsonResponse = [
            'status' => $status,
            'message' => $message,
            'data' => $data
        ];

        return response()->json($jsonResponse);
    }

    public function getCRMMappFileds(Request $request){
        $crmId = $request->get('crm_id');
        $deliveryMappingFields = DeliveryMappingFields::with('SnapDeliveryFields')->where('delivery_crm_id', $crmId)->get()->toArray();

        //echo '<pre>';print_r($deliveryMappingFields);die;
        $filesmapIdArr = [];
        foreach ($deliveryMappingFields as $key => $value) {
            $filesmapIdArr[] = $value['snap_delivery_field_id'];
        }
        // dd($filesmapIdArr);
        //echo "<pre>";print_r($data);die;
        $jsonResponse = [
            'status' => 1,
            'fields' => $filesmapIdArr
        ];
        return response()->json($jsonResponse);
    }

    public function addCRMParameters(Request $request) {
        $validator = Validator::make($request->all(),[
            'txtFieldName' => 'required',
            'deliveryField' => 'required',
            // 'movedate_format' => 'required_if:deliveryField,==,7',
            'txtFieldDefaultValue' => 'required_if:deliveryField,==,1',
        ],[
            'txtFieldName.required' => 'Please Enter Parameter Name',
            'deliveryField.required' => 'Please Select Mapping Field',
            // 'movedate_format.required_if' => 'Please Select Date Format',
            'txtFieldDefaultValue.required_if' => 'Please Enter Default Value',
            /*'txtFieldDefaultValue.max' => 'Default value can not be more than 200 characters',*/
        ]);

        if ($validator->fails()) {
            $responseData =array(
                'status' => 400,
                'message' => 'There are incorect values in the form!',
                'error' => $validator->getMessageBag()->toArray()
            );
        } else {
            $crm_id = $request->get('crm_id');
            $deliveryField = $request->get('deliveryField');
            $txtFieldName = $request->get('txtFieldName');
            $txtFieldRootElement = $request->get('txtFieldRootElement');
            $txtFieldDefaultValue = $request->get('txtFieldDefaultValue');

            $is_default = 'Dynamic';
            if($deliveryField == 1){
                $is_default = 'Default value';
            }
            $deliveryMappingField = new DeliveryMappingFields();
            $deliveryMappingField->delivery_crm_id = $crm_id;
            $deliveryMappingField->snap_delivery_field_id = $deliveryField;
            $deliveryMappingField->post_para_key = $txtFieldName;
            $deliveryMappingField->is_default = $is_default;
            $deliveryMappingField->root_element = $txtFieldRootElement;
            $deliveryMappingField->default_value = $txtFieldDefaultValue;
            $deliveryMappingField->save();
            $newfieldid = $deliveryMappingField->id;

            $responseData = array(
                'status' => 200,
                'message' => 'New Parameter added successfully',
                'data' => $deliveryMappingField
            );
        }
        return response()->json( $responseData );
    }

    public function editCRMParameters(Request $request) {
        $validator = Validator::make($request->all(),[
            'txtEditFieldName' => 'required',
            'editDeliveryField' => 'required',
            // 'editmovedate_format' => 'required_if:editDeliveryField,==,7',
            'editTxtFieldDefaultValue' => 'required_if:editDeliveryField,==,1',

        ],[
            'txtEditFieldName.required' => 'Please Enter Parameter Name',
            'editDeliveryField.required' => 'Please Select Mapping Field',
            // 'editmovedate_format.required_if' => 'Please Select Date Format',
            'editTxtFieldDefaultValue.required_if' => 'Please Enter Default Value',
            /*'editTxtFieldDefaultValue.max' => 'Default value can not be more than 200 characters',*/
        ]);

        if ($validator->fails()) {
            $responseData = array(
                'status' => 400,
                'message' => 'There are incorect values in the form!',
                'error' => $validator->getMessageBag()->toArray()
            );
        } else {
            //print_r($request->all()); exit;
            $editFieldId = $request->get('editFieldId');
            $editParamCRMId = $request->get('editParamCRMId');
            $editDeliveryField = $request->get('editDeliveryField');
            $txtEditFieldName = $request->get('txtEditFieldName');
            $edittxtFieldRootElement = $request->get('edittxtFieldRootElement');
            $editTxtFieldDefaultValue = ($editDeliveryField == 1) ? $request->get('editTxtFieldDefaultValue') : null;

            $is_default = 'Dynamic';
            if($editDeliveryField == 1){
                $is_default = 'Default value';
            }
            //$deliveryMappingField = new DeliveryMappingFields();
            DeliveryMappingFields::where('delivery_mapping_fields_id', $editFieldId)
                ->update([
                    'snap_delivery_field_id' => $editDeliveryField,
                    'post_para_key' => $txtEditFieldName,
                    'is_default' => $is_default,
                    'root_element' => $edittxtFieldRootElement,
                    'default_value' => $editTxtFieldDefaultValue
                ]);

            $responseData = array(
                'status' => 200,
                'message' => 'Parameter updated successfully',
                'data' => ''
            );
        }
        return response()->json( $responseData );
    }

    public function getCRMList( Request $request ) {

        $deliveryMethods = DeliveryCrm::all();
        $rows = [];
        foreach ($deliveryMethods as $key => $method) {
            $methodName = "";
            if($method->delivery_type == 1){
                $methodName = "POST";
            }elseif($method->delivery_type == 2){
                $methodName = "GET";
            }elseif($method->delivery_type == 3){
                $methodName = "XML";
            }
            $rows[$key]['delivery_crm_id'] = $method->delivery_crm_id;
            $rows[$key]['name'] = $method->name;
            $rows[$key]['delivery_url'] = $method->delivery_url;
            $rows[$key]['method_type'] = $methodName;
            $rows[$key]['actions'] = [];
        }
        return datatables()->of($rows)->make(true);
    }

    public function getCRMListByCopy( Request $request ) {

        $deliveryMethods = DeliveryCrm::where('delivery_crm_id', "!=", $request->id)->get();
        $rows = [];
        if(count($deliveryMethods) > 0) {
            foreach ($deliveryMethods as $key => $method) {
                /*$rows[$key]['delivery_crm_id'] = $method->delivery_crm_id;
                $rows[$key]['name'] = $method->name;*/

                $rows[] = "<option value='".$method->delivery_crm_id."'>".$method->name."</option>";
            }
            $status = 200;
        } else {
            $status = 400;
        }

        $responseData = array(
            'status' => $status,
            'message' => "",
            'crmData' => $rows
        );
        return response()->json( $responseData );
    }

    public function importDeliveryMethod(Request $request) {
        $new_contract_id = $request->new_crm_id;
        $old_crm_id = $request->old_crm_id;
        DeliveryMappingFields::where('delivery_crm_id',$old_crm_id)->delete();
        $deliveryMethods = DeliveryCrm::where('delivery_crm_id', "!=", $request->id)->get();
        $deliveryMethodPostParams = DeliveryMappingFields::where('delivery_crm_id',$new_contract_id)->get();
        foreach($deliveryMethodPostParams as $deliveryMethodPostParam) {
            $delivery_mapping_fields = new DeliveryMappingFields();
            $delivery_mapping_fields->delivery_crm_id = $old_crm_id;
            $delivery_mapping_fields->snap_delivery_field_id = $deliveryMethodPostParam->snap_delivery_field_id;
            $delivery_mapping_fields->post_para_key = $deliveryMethodPostParam->post_para_key;
            $delivery_mapping_fields->is_default = $deliveryMethodPostParam->is_default;
            $delivery_mapping_fields->root_element = $deliveryMethodPostParam->root_element;
            $delivery_mapping_fields->save();
        }
        echo json_encode(['success' => true]);
    }

    public function getDeliveryList( Request $request ) {

        $deliveryMethods = DeliveryCrm::where('businesses_campaign_id', $request->campaignId)->get();
        $rows = [];
        foreach ($deliveryMethods as $key => $method) {
            $methodName = "";
            if($method->delivery_type == 1){
                $methodName = "POST";
            }elseif($method->delivery_type == 2){
                $methodName = "GET";
            }elseif($method->delivery_type == 3){
                $methodName = "XML";
            }
            $rows[$key]['id'] = $method->id;
            $rows[$key]['delivery_url'] = $method->delivery_url;
            $rows[$key]['method_type'] = $methodName;
            $rows[$key]['actions'] = [];
        }
        return datatables()->of($rows)->make(true);
    }

    public function deleteParameter( Request $request ) {
        try{
            $paramId = $request->get('paramId');
            LeadDeliveryCrmDefaultFields::where('delivery_mapping_fields_id', $paramId)->delete();
            $deletedParam = DeliveryMappingFields::where('delivery_mapping_fields_id', $paramId)->delete();
            $status = 200;
            $message = 'Parameter deleted';
        } catch(Exception $e) {
            $status = 400;
            $message = $e->getMessage();
        }
        $responseData = array(
            'status' => $status,
            'message' => $message,
        );
        return response()->json( $responseData );
    }

    public function deleteDeliveryMethod( Request $request ) {
        try{
            $delfieldid = $request->post('delfieldid');
            //echo $delfieldid;die;
            CampaignLeadDeliveryResponse::where('delivery_crm_id', $delfieldid)->delete();
            $deliveryMappingFieldsdata = DeliveryMappingFields::where('delivery_crm_id', $delfieldid)->get()->toArray();

            $deliveryMappingFieldsdataArr = [];
            foreach ($deliveryMappingFieldsdata as $key => $value) {
                $deliveryMappingFieldsdataArr[] =  $value['delivery_mapping_fields_id'];

            }
            LeadDeliveryCrmDefaultFields::whereIn('delivery_mapping_fields_id',$deliveryMappingFieldsdataArr)->delete();
            DeliveryMappingFields::whereIn('delivery_mapping_fields_id', $deliveryMappingFieldsdataArr )->delete();
            CampaignCrmDelivery::where('delivery_crm_id', $delfieldid)->delete();
            DeliveryCrm::where('delivery_crm_id', $delfieldid)->delete();
            $status = 200;
            $message = 'Delivery Method deleted';
        } catch(Exception $e) {
            $status = 400;
            $message = $e->getMessage();
        }
        $responseData = array(
            'status' => $status,
            'message' => $message,
        );
        return response()->json( $responseData );
    }

    public function leaddeliverytest(Request $request) {
        $status = "SUCCESS";
        $error = "";
        $msg = '';
        $postData = $request->all();
        $sendData = json_encode($postData);

        return response()->json($sendData);
    }

    public function leaddeliverysuccess(Request $request) {
        $status = "SUCCESS";
        $error = "";
        $msg = '';
        $postData = $request->all();
        $sendData = $this->sendCRMCall($postData);
        //echo '<pre>';print_r($sendData);die;
        //echo '<pre>';print_r($postData);die;

        /*$response = array(
            'status' => 1,
            'message' => $msg,
            'error' => $error
        );*/

        return response()->json($sendData);
    }

    public function sendCRMCall($request) {
        $lead_id = $request['lead_id'];
        $campaign_id = $request['campaign_id'];

        $response = array(
            'status' => 0,
            'message' => "Something wrong.",
            'data' => []
        );

        $carTypeArray   = [];
        $carTypeDetails = MstCarType::distinct()->get(['car_type_id', 'car_type']);
        foreach ($carTypeDetails as $carType) {
            $carTypeArray[$carType->car_type_id] = $carType->car_type;
        }

        //get lead data from lead_id
        $leadData = Lead::with(['LeadJunkRemoval.MstJunkType', 'junksubMoveInfo', 'junksubMoveInfo.MstJunkSubType', 'MstLeadSource', 'MstLeadCategory', 'LeadLandingPage', 'LeadMoving', 'LeadJunkRemoval', 'LeadCarTransport', 'LeadCarTransport.mstCarType', 'LeadHeavyLifting', 'LeadHeavyLifting.MstHeavyLiftingType'])->find($lead_id)->toArray();
        // echo "<pre>"; print_r($leadData ); die;
        if(count($leadData) == 0) {

            $response['message'] = "Sorry, Lead id is wrong.";
        } else {
            $response['data']['leadData'] = $leadData;
        }
        $fieldsarray = $rootelement = [];
        $headerToken = "";
        //get crm data from campaign
        $campaignCrmDelivery = CampaignCrmDelivery::with(['LeadDeliveryCrmDefaultFields', "LeadDeliveryCrmDefaultFields.deliveryMappingfieldsdata", 'DeliveryMappingFields', 'DeliveryCrm','campaignInfo','campaignInfo.businessInfo'])->where('campaign_id', $campaign_id)->get()->toArray();
        $campaignData = Campaign::with('businessInfo')->where('campaign_id', $campaign_id)->first();
        // echo '<pre>'; print_r($campaignCrmDelivery); die;
        $crmNotDeliveredEmailData = [];
        $crmNotDeliveredEmailData['leadid'] = $leadData['lead_id'];
        $crmNotDeliveredEmailData['leadname'] = ucfirst($leadData['name']);
        $crmNotDeliveredEmailData['leademail'] = $leadData['email'];
        $crmNotDeliveredEmailData['leadphone'] = $leadData['phone'];
        $crmNotDeliveredEmailData['businessname'] = ucfirst($campaignData->businessInfo->business_name);
        $crmNotDeliveredEmailData['businessid'] = $campaignData->business_id;
        $crmNotDeliveredEmailData['campaignname'] = ucfirst($campaignData->campaign_name);
        $crmNotDeliveredEmailData['campaignid'] = $campaignData->campaign_id;
        $crmNotDeliveredEmailData['deliverytype'] = "CRM";
        $crmNotDeliveredEmailData['emailaddress'] = "";
        $crmNotDeliveredEmailData['emailtemplate'] = "";
        $crmNotDeliveredEmailData['smsnumber'] = "";
        $crmNotDeliveredEmailData['smscarrier'] = "";
        if(count($campaignCrmDelivery) == 0) {
            $response['message'] = "Sorry, Campaign id is wrong.";
        } else {
            $response['data']['campaignCrmDelivery'] = $campaignCrmDelivery;

            $c = 0;
            foreach($campaignCrmDelivery as $crmDetail) {
                //echo '<pre>';print_r($crmDetail);die;
                $url = $crmDetail['post_url'];
                $requestType = $crmDetail['delivery_crm']['delivery_type'];
                $requestFormat = $crmDetail['delivery_crm']['request_format'];
                $movedate_format = ($crmDetail['delivery_crm']['movedate_format']!= null) ? $crmDetail['delivery_crm']['movedate_format'] : "m/d/Y";
                $created_at_format = ($crmDetail['delivery_crm']['leaddate_format'] != null) ? $crmDetail['delivery_crm']['leaddate_format'] : 'm/d/Y H:i:s';
                $groupbyrootArr = $rootname = [];
                $snapdataArr = [];
                foreach($crmDetail['delivery_mapping_fields'] as $key => $mappingField) {
                    $groupbyrootArr[$mappingField['root_element']][$key] = $mappingField;
                    if(!in_array($mappingField['root_element'], $rootname)){
                        $rootname[] = $mappingField['root_element'];
                    }
                    $snapdataArr[$mappingField['snap_delivery_field_id']] =  $mappingField;
                }
                // ksort($groupbyrootArr, SORT_STRING);
                $mappfiledsArr = [];
                if(!empty($rootname)){
                    foreach($rootname as $key1 => $mappingField1) {
                        $fieldsarray = [];
                        foreach($groupbyrootArr[$mappingField1] as $key => $mappingField) {
                            $snapField = SnapDeliveryFields::where('snap_delivery_field_id', $mappingField['snap_delivery_field_id'])->first()->toArray();
                            $mappfiledsArr[] = $snapField['field_name'];
                            // $snapField = $snapdataArr[$mappingField['snap_delivery_field_id']];
                            if($snapField['field_name']  == 'default') {
                                $crmDefauleFields = $crmDetail['lead_delivery_crm_default_fields'];
                                // echo '<pre>';print_r($crmDefauleFields);
                                if(count($crmDefauleFields) > 0 ){
                                    foreach($crmDefauleFields as $keyCRm => $crmField) {
                                        if($crmField['delivery_mappingfieldsdata']['post_para_key'] == 'token'){
                                            $headerToken = $crmField['variable_value'];
                                        } else if($crmField['delivery_mappingfieldsdata']['is_default'] == 'Default value'){
                                            if($mappingField1 == $crmField['delivery_mappingfieldsdata']['root_element']){
                                                if ($crmField['variable_name'] == 'buckets') {
                                                    $fieldsarray[$crmField['variable_name']] = [(int)$crmField['variable_value']];
                                                } else if ($crmField['variable_name'] == 'tags') {
                                                    $fieldsarray[$crmField['variable_name']] = [(int)$crmField['variable_value']];
                                                } else {
                                                    $fieldsarray[$crmField['variable_name']] = $crmField['variable_value'];
                                                }
                                            }
                                        }
                                    }
                                }
                                //$fieldsarray[ $mappingField['post_para_key']] =  "";
                            } else if($snapField['field_name']  == 'name'){
                                $fieldsarray[$mappingField['post_para_key']] =  $leadData['name'];
                            } else if($snapField['field_name']  == 'first_name'){
                                $getFLName = $this->getFLName($leadData['name']);
                                $fieldsarray[$mappingField['post_para_key']] =  $getFLName['first_name'];
                            } else if($snapField['field_name'] == 'last_name'){
                                $getFLName = $this->getFLName($leadData['name']);
                                $fieldsarray[$mappingField['post_para_key']] = $getFLName['last_name'];
                            } else if($snapField['field_name']  == 'email'){
                                $fieldsarray[$mappingField['post_para_key']] = $leadData['email'];
                            } else if($snapField['field_name'] == 'phone_number'){
                                if($crmDetail['delivery_crm_id'] == 39){
                                    $fieldsarray[$mappingField['post_para_key']] = '1'.$leadData['phone'];
                                } else {
                                    $fieldsarray[$mappingField['post_para_key']] = $leadData['phone'];
                                }
                            } else if($snapField['field_name'] == 'origin'){
                                $fieldsarray[$mappingField['post_para_key']] = $leadData['mst_lead_source']['lead_source_name'];
                            }
                            if($snapField['field_name'] == 'IP'){
                                $fieldsarray[$mappingField['post_para_key']] = $leadData['lead_landing_page']['ip'];
                            }
                            if($snapField['field_name'] == 'keyword'){
                                $fieldsarray[$mappingField['post_para_key']] = $leadData['lead_landing_page']['search_keyword'];
                            }
                            if($snapField['field_name'] == 'created_at' ){
                                $org_created_at = date_create($leadData['created_at']);
                                $new_created_at = date_format($org_created_at, $created_at_format);
                                $fieldsarray[$mappingField['post_para_key']] = $new_created_at;
                            }
                            if($snapField['field_name'] == 'generated_at'){
                                $org_created_at = date_create($leadData['lead_generated_at']);
                                $new_created_at = date_format($org_created_at, $created_at_format);
                                $fieldsarray[$mappingField['post_para_key']] = $new_created_at;
                            }
                            if($snapField['field_name'] == 'lead_date' ){
                                $org_created_at = date_create($leadData['lead_generated_at']);
                                $new_created_at = date_format($org_created_at, $created_at_format);
                                $fieldsarray[$mappingField['post_para_key']] = $new_created_at;
                            }

                            if($snapField['field_name']  == 'is_verified') {
                                $fieldsarray[$mappingField['post_para_key']] = $leadData['is_verified'];
                            }
                            if($snapField['field_name'] == 'extra_phone_formated'){
                                $property_type = "";
                                $phone1 = ($leadData['phone'] != NULL && $leadData['phone'] != "") ? $leadData['phone'] : '-';
                                $phone1 = preg_match( '/^(\d{3})(\d{3})(\d{4})$/', $phone1,  $matches );
                                $phone  = ( $phone1 != NULL && $phone1 != "") ? '(' . $matches[1] . ') ' .$matches[2] . '-' . $matches[3] : '-';
                                $fieldsarray[$mappingField['post_para_key']] = $phone;
                            }
                            if ($snapField['field_name'] == 'extra_phone_formated(-)') {
                                $property_type = "";
                                $phone1 = ($leadData['phone'] != NULL && $leadData['phone'] != "") ? $leadData['phone'] : '-';
                                $phone1 = preg_match('/^(\d{3})(\d{3})(\d{4})$/', $phone1,  $matches);
                                $phone  = ($phone1 != NULL && $phone1 != "") ?  $matches[1] . '-' . $matches[2] . '-' . $matches[3] : '-';
                                $fieldsarray[$mappingField['post_para_key']] = $phone;
                            }
                            if ($snapField['field_name'] == 'phone2') {
                                $property_type = "";
                                $phone1 = ($leadData['phone'] != NULL && $leadData['phone'] != "") ? $leadData['phone'] : '-';
                                $phone1 = preg_match('/^(\d{3})(\d{3})(\d{4})$/', $phone1,  $matches);
                                $phone  = ($phone1 != NULL && $phone1 != "") ?  $matches[1] . $matches[2] . $matches[3] : '-';
                                $fieldsarray[$mappingField['post_para_key']] = $phone;
                            }
                            if ($snapField['field_name'] == 'lead_id') {
                                $fieldsarray[$mappingField['post_para_key']] = $leadData['lead_id'];
                            }
                            // echo '<pre>test  = '; print_r($fieldsarray);
                            $address = $city = $state = $zipcode = $toAddress = $toCity = $toState = $toZipcode = "";
                            $move_date = date("Y-m-d");
                            if($leadData['lead_category_id'] == 1) {
                                if($snapField['field_name'] == 'move_date') {
                                    if(isset($leadData['lead_moving']['move_date'])) {
                                        $org_moveDate = date_create($leadData['lead_moving']['move_date']);
                                        $new_moveDate = date_format($org_moveDate, $movedate_format);
                                        $move_date = $new_moveDate;
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $move_date;
                                }
                                if($snapField['field_name'] == 'from_zip' || $snapField['field_name'] == 'from_zipcode'){
                                    $from_zipcode = "";
                                    if(isset($leadData['lead_moving']['from_zipcode'])) {
                                        $from_zipcode = str_pad($leadData['lead_moving']['from_zipcode'], 5, '0', STR_PAD_LEFT);
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $from_zipcode;
                                }
                                if($snapField['field_name'] == 'from_state'){
                                    $from_state = "";
                                    if(isset($leadData['lead_moving']['from_state'])) {
                                        $from_state = $leadData['lead_moving']['from_state'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $from_state;
                                }
                                if($snapField['field_name'] == 'from_city'){
                                    $from_city = "";
                                    if(isset($leadData['lead_moving']['from_city'])) {
                                        $from_city = $leadData['lead_moving']['from_city'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $from_city;
                                }
                                if($snapField['field_name'] == 'from_areacode'){
                                    $from_areacode = "";
                                    if(isset($leadData['lead_moving']['from_areacode'])) {
                                        $from_areacode = $leadData['lead_moving']['from_areacode'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $from_areacode;
                                }
                                if($snapField['field_name'] == 'to_zip' || $snapField['field_name'] == 'to_zipcode'){
                                    $to_zipcode = "";
                                    if(isset($leadData['lead_moving']['to_zipcode'])) {
                                        $to_zipcode = str_pad($leadData['lead_moving']['to_zipcode'], 5, '0', STR_PAD_LEFT);
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $to_zipcode;
                                }
                                if($snapField['field_name'] == 'to_state'){
                                    $to_state = "";
                                    if(isset($leadData['lead_moving']['to_state'])) {
                                        $to_state = $leadData['lead_moving']['to_state'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $to_state;
                                }
                                if($snapField['field_name'] == 'to_city'){
                                    $to_city = "";
                                    if(isset($leadData['lead_moving']['to_city'])) {
                                        $to_city = $leadData['lead_moving']['to_city'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $to_city;
                                }
                                if($snapField['field_name'] == 'to_areacode'){
                                    $to_areacode = "";
                                    if(isset($leadData['lead_moving']['to_areacode'])) {
                                        $to_areacode = $leadData['lead_moving']['to_areacode'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $to_areacode;
                                }
                                if($snapField['field_name'] == 'move_size'){
                                    $move_size = "";
                                    if(isset($leadData['lead_moving']['move_size_id'])) {
                                        $move_size = $leadData['lead_moving']['move_size_id'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $move_size;
                                }
                                if($snapField['field_name'] == 'move_type'){
                                    $move_type = "";
                                    if(isset($leadData['lead_moving']['move_type'])) {
                                        $move_type = $leadData['lead_moving']['move_type'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $move_type;
                                }
                                if($snapField['field_name'] == 'distance'){
                                    $distance = "";
                                    if(isset($leadData['lead_moving']['distance'])) {
                                        $distance = $leadData['lead_moving']['distance'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $distance;
                                }
                                if($snapField['field_name'] == 'property_type'){
                                    $property_type = "";
                                    if(isset($leadData['lead_moving']['property_type'])) {
                                        $property_type = $leadData['lead_moving']['property_type'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $property_type;
                                }

                                $get_movesize_weight = CommonFunctions::getWeight($leadData['lead_moving']['move_size_id'] );
                                if(count( $get_movesize_weight) > 0 ){
                                    if($snapField['field_name'] == 'extra_movesize_in_lbs') {
                                        $extra_move_weight = $get_movesize_weight[1]. " lbs";
                                        $fieldsarray[$mappingField['post_para_key']] = $extra_move_weight;
                                    }
                                    if ($snapField['field_name'] == 'extra_movesize_notin_lbs') {
                                        $extra_move_weight = $get_movesize_weight[1];
                                        $fieldsarray[$mappingField['post_para_key']] = $extra_move_weight;
                                    }
                                    if($snapField['field_name'] == 'movesize(room)') {
                                        $movesizeArr =  $this->getMoveSizeArr_room();
                                        $movesize_rooms =  $movesizeArr[$leadData['lead_moving']['move_size_id']];
                                        $fieldsarray[$mappingField['post_para_key']] = $movesize_rooms;
                                    }
                                    if($snapField['field_name'] == 'movesize( bedroom)') {
                                        $movesizeArr_bedroom =  $this->getMoveSizeArr_bedroom();
                                        $movesize_bedroom =  $movesizeArr_bedroom[$leadData['lead_moving']['move_size_id']];
                                        $fieldsarray[$mappingField['post_para_key']] = $movesize_bedroom;
                                    }
                                    if($snapField['field_name'] == 'movesize(_bedroom)') {
                                        $movesizeArr_bedrooms =  $this->getMoveSizeArr_bedrooms();
                                        $movesize_bedrooms =  $movesizeArr_bedrooms[$leadData['lead_moving']['move_size_id']];
                                        $fieldsarray[$mappingField['post_para_key']] = $movesize_bedrooms;
                                    }
                                    if($snapField['field_name'] == 'field_bedroom_size_1') {
                                        $movesizeArr_bedrooms_extra = $this->getMoveSizeArr_bedroom_extra();
                                        $movesize_bedrooms = $movesizeArr_bedrooms_extra[$leadData['lead_moving']['move_size_id']];
                                        $fieldsarray[$mappingField['post_para_key']] = $movesize_bedrooms;
                                    }
                                }

                                $city = ($leadData['lead_moving']['from_city'] != NULL && $leadData['lead_moving']['from_city'] != "") ? $leadData['lead_moving']['from_city']. ", " : '';
                                $state = ($leadData['lead_moving']['from_state'] != NULL && $leadData['lead_moving']['from_state'] != "") ? $leadData['lead_moving']['from_state']. " " : '';
                                $zipcode = ($leadData['lead_moving']['from_zipcode'] != NULL && $leadData['lead_moving']['from_zipcode'] != "") ? $leadData['lead_moving']['from_zipcode'] : '';
                                $address =  $city . $state . $zipcode;


                                $toCity = ($leadData['lead_moving']['to_city'] != NULL && $leadData['lead_moving']['to_city'] != "") ? $leadData['lead_moving']['to_city']. ", " : '';
                                $toState = ($leadData['lead_moving']['to_state'] != NULL && $leadData['lead_moving']['to_state'] != "") ? $leadData['lead_moving']['to_state']. " " : '';
                                $toZipcode = ($leadData['lead_moving']['to_zipcode'] != NULL && $leadData['lead_moving']['to_zipcode'] != "") ? $leadData['lead_moving']['to_zipcode'] : '';
                                $toAddress =  $toCity . $toState . $toZipcode;

                                if($crmDetail['delivery_crm_id'] == 39 && $snapField['field_name'] == 'note'){
                                    $fieldsarray[$mappingField['post_para_key']] = 'states from : '.trim($state).'; states to : '.trim($toState);
                                }
                            }
                            if($leadData['lead_category_id'] == 2) {
                                if($snapField['field_name'] == 'move_date') {
                                    if(isset($leadData['lead_junk_removal']['junk_remove_date'])) {
                                        $org_moveDate = date_create($leadData['lead_junk_removal']['junk_remove_date']);
                                        $new_moveDate = date_format($org_moveDate, $movedate_format);
                                        $move_date = $new_moveDate;
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $move_date;
                                }
                                if($snapField['field_name'] == 'from_zip' || $snapField['field_name'] == 'from_zipcode'){
                                    $from_zipcode = "";
                                    if(isset($leadData['lead_junk_removal']['from_zipcode'])) {
                                        $from_zipcode = str_pad($leadData['lead_junk_removal']['from_zipcode'], 5, '0', STR_PAD_LEFT);
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $from_zipcode;
                                }
                                if($snapField['field_name'] == 'from_state'){
                                    $from_state = "";
                                    if(isset($leadData['lead_junk_removal']['from_state'])) {
                                        $from_state = $leadData['lead_junk_removal']['from_state'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $from_state;
                                }
                                if($snapField['field_name'] == 'from_city'){
                                    $from_city = "";
                                    if(isset($leadData['lead_junk_removal']['from_city'])) {
                                        $from_city = $leadData['lead_junk_removal']['from_city'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $from_city;
                                }
                                if($snapField['field_name'] == 'from_areacode'){
                                    $from_areacode = "";
                                    if(isset($leadData['lead_junk_removal']['from_areacode'])) {
                                        $from_areacode = $leadData['lead_junk_removal']['from_areacode'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $from_areacode;
                                }
                                if($snapField['field_name'] == 'to_zip' || $snapField['field_name'] == 'to_zipcode'){
                                    $to_zipcode = "";
                                    $fieldsarray[$mappingField['post_para_key']] = $to_zipcode;
                                }
                                if($snapField['field_name'] == 'to_state'){
                                    $to_state = "";
                                    $fieldsarray[$mappingField['post_para_key']] = $to_state;
                                }
                                if($snapField['field_name'] == 'to_city'){
                                    $to_city = "";
                                    $fieldsarray[$mappingField['post_para_key']] = $to_city;
                                }
                                if($snapField['field_name'] == 'to_areacode'){
                                    $to_areacode = "";
                                    $fieldsarray[$mappingField['post_para_key']] = $to_areacode;
                                }
                                if($snapField['field_name'] == 'move_size'){
                                    $move_size = "";
                                    $fieldsarray[$mappingField['post_para_key']] = $move_size;
                                }
                                if($snapField['field_name'] == 'move_type'){
                                    $move_type = "";
                                    $fieldsarray[$mappingField['post_para_key']] = $move_type;
                                }
                                if($snapField['field_name'] == 'description'){
                                    $description = "";
                                    if(isset($leadData['lead_junk_removal']['description'])) {
                                        $description = $leadData['lead_junk_removal']['description'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $description;
                                }
                                if($snapField['field_name'] == 'junk_type'){
                                    $junk_type_id = "";
                                    if(isset($leadData['lead_junk_removal']['mst_junk_type']['junk_type'])) {
                                        $junk_type_id = $leadData['lead_junk_removal']['mst_junk_type']['junk_type_id'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $junk_type_id;
                                }
                                if($snapField['field_name'] == 'junk_sub_type'){
                                    // $junk_sub_type_id = "";
                                    // if(isset($leadData['lead_junk_removal']['mst_junk_sub_type']['junk_sub_type'])) {
                                    //     $junk_sub_type_id = $leadData['lead_junk_removal']['mst_junk_sub_type']['junk_sub_type_id'];
                                    // }
                                    $junkSubName = [];
                                    if(isset($leadData['junksub_move_info'] ) && count($leadData['junksub_move_info']) > 0) {
                                        foreach ($leadData['junksub_move_info'] as $key => $value) {
                                            $junkSubName[] = $value['mst_junk_sub_type']['junk_sub_type_id'];
                                        }
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = (count( $junkSubName) > 0) ? implode(",", $junkSubName) : '-';
                                }
                                if($snapField['field_name'] == 'property_type'){
                                    $property_type = "";
                                    if(isset($leadData['lead_junk_removal']['property_type'])) {
                                        $property_type = $leadData['lead_junk_removal']['property_type'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $property_type;
                                }
                                if($snapField['field_name'] == 'owner_type'){
                                    $owner_type = "";
                                    if(isset($leadData['lead_junk_removal']['owner_type'])) {
                                        $owner_type = $leadData['lead_junk_removal']['owner_type'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $owner_type;
                                }
                                $city = ($leadData['lead_junk_removal']['from_city'] != NULL && $leadData['lead_junk_removal']['from_city'] != "") ? $leadData['lead_junk_removal']['from_city']. ", " : '';
                                $state = ($leadData['lead_junk_removal']['from_state'] != NULL && $leadData['lead_junk_removal']['from_state'] != "") ? $leadData['lead_junk_removal']['from_state']. " " : '';
                                $zipcode = ($leadData['lead_junk_removal']['from_zipcode'] != NULL && $leadData['lead_junk_removal']['from_zipcode'] != "") ? $leadData['lead_junk_removal']['from_zipcode'] : '';
                                $address =  $city . $state . $zipcode;

                                $toCity = ($leadData['lead_junk_removal']['from_city'] != NULL && $leadData['lead_junk_removal']['from_city'] != "") ? $leadData['lead_junk_removal']['from_city']. ", " : '';
                                $toState = ($leadData['lead_junk_removal']['from_state'] != NULL && $leadData['lead_junk_removal']['from_state'] != "") ? $leadData['lead_junk_removal']['from_state']. " " : '';
                                $toZipcode = ($leadData['lead_junk_removal']['from_zipcode'] != NULL && $leadData['lead_junk_removal']['from_zipcode'] != "") ? $leadData['lead_junk_removal']['from_zipcode'] : '';
                                $toAddress =  $toCity . $toState . $toZipcode;
                            }
                            if($leadData['lead_category_id'] == 3) {
                                if($snapField['field_name'] == 'move_date') {
                                    if(isset($leadData['lead_heavy_lifting']['lift_date'])) {
                                        $org_moveDate = date_create($leadData['lead_heavy_lifting']['lift_date']);
                                        $new_moveDate = date_format($org_moveDate, $movedate_format);
                                        $move_date = $new_moveDate;
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $move_date;
                                }
                                if($snapField['field_name'] == 'from_zip' || $snapField['field_name'] == 'from_zipcode'){
                                    $from_zipcode = "";
                                    if(isset($leadData['lead_heavy_lifting']['from_zipcode'])) {
                                        $from_zipcode = str_pad($leadData['lead_heavy_lifting']['from_zipcode'], 5, '0', STR_PAD_LEFT);
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $from_zipcode;
                                }
                                if($snapField['field_name'] == 'from_state'){
                                    $from_state = "";
                                    if(isset($leadData['lead_heavy_lifting']['from_state'])) {
                                        $from_state = $leadData['lead_heavy_lifting']['from_state'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $from_state;
                                }
                                if($snapField['field_name'] == 'from_city'){
                                    $from_city = "";
                                    if(isset($leadData['lead_heavy_lifting']['from_city'])) {
                                        $from_city = $leadData['lead_heavy_lifting']['from_city'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $from_city;
                                }
                                if($snapField['field_name'] == 'from_areacode'){
                                    $from_areacode = "";
                                    if(isset($leadData['lead_heavy_lifting']['from_areacode'])) {
                                        $from_areacode = $leadData['lead_heavy_lifting']['from_areacode'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $from_areacode;
                                }
                                if($snapField['field_name'] == 'to_zip' || $snapField['field_name'] == 'to_zipcode'){
                                    $to_zipcode = "";
                                    if(isset($leadData['lead_heavy_lifting']['to_zipcode'])) {
                                        $to_zipcode = str_pad($leadData['lead_heavy_lifting']['to_zipcode'], 5, '0', STR_PAD_LEFT);
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $to_zipcode;
                                }
                                if($snapField['field_name'] == 'to_state'){
                                    $to_state = "";
                                    if(isset($leadData['lead_heavy_lifting']['to_state'])) {
                                        $to_state = $leadData['lead_heavy_lifting']['to_state'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $to_state;
                                }
                                if($snapField['field_name'] == 'to_city'){
                                    $to_city = "";
                                    if(isset($leadData['lead_heavy_lifting']['to_city'])) {
                                        $to_city = $leadData['lead_heavy_lifting']['to_city'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $to_city;
                                }
                                if($snapField['field_name'] == 'to_areacode'){
                                    $to_areacode = "";
                                    if(isset($leadData['lead_heavy_lifting']['to_areacode'])) {
                                        $to_areacode = $leadData['lead_heavy_lifting']['to_areacode'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $to_areacode;
                                }
                                if($snapField['field_name'] == 'move_size'){
                                    $move_size = "";
                                    if(isset($leadData['lead_heavy_lifting']['move_size'])) {
                                        $move_size = $leadData['lead_heavy_lifting']['move_size'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $move_size;
                                }
                                if($snapField['field_name'] == 'move_type'){
                                    $move_type = "";
                                    if(isset($leadData['lead_heavy_lifting']['move_type'])) {
                                        $move_type = $leadData['lead_heavy_lifting']['move_type'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $move_type;
                                }
                                if($snapField['field_name'] == 'distance'){
                                    $distance = "";
                                    if(isset($leadData['lead_heavy_lifting']['distance'])) {
                                        $distance = $leadData['lead_heavy_lifting']['distance'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $distance;
                                }
                                if($snapField['field_name'] == 'is_operational'){
                                    $is_operational = "";
                                    if(isset($leadData['lead_heavy_lifting']['is_operational'])) {
                                        $is_operational = $leadData['lead_heavy_lifting']['is_operational'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $is_operational;
                                }
                                if($snapField['field_name'] == 'is_required_assistence'){
                                    $is_required_assistence = "";
                                    if(isset($leadData['lead_heavy_lifting']['is_required_assistence'])) {
                                        $is_required_assistence = $leadData['lead_heavy_lifting']['is_required_assistence'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $is_required_assistence;
                                }
                                if($snapField['field_name'] == 'heavy_lifting_type'){
                                    $heavy_lifting_type = "";
                                    if(isset($leadData['lead_heavy_lifting']['mst_heavy_lifting_type']['heavy_lifting_type_id'])) {
                                        $heavy_lifting_type = $leadData['lead_heavy_lifting']['mst_heavy_lifting_type']['heavy_lifting_type_id'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $heavy_lifting_type;
                                }
                                $city = ($leadData['lead_heavy_lifting']['from_city'] != NULL && $leadData['lead_heavy_lifting']['from_city'] != "") ? $leadData['lead_heavy_lifting']['from_city']. ", " : '';
                                $state = ($leadData['lead_heavy_lifting']['from_state'] != NULL && $leadData['lead_heavy_lifting']['from_state'] != "") ? $leadData['lead_heavy_lifting']['from_state']. " " : '';
                                $zipcode = ($leadData['lead_heavy_lifting']['from_zipcode'] != NULL && $leadData['lead_heavy_lifting']['from_zipcode'] != "") ? $leadData['lead_heavy_lifting']['from_zipcode'] : '';
                                $address =  $city . $state . $zipcode;

                                $toCity = ($leadData['lead_heavy_lifting']['to_city'] != NULL && $leadData['lead_heavy_lifting']['to_city'] != "") ? $leadData['lead_heavy_lifting']['to_city']. ", " : '';
                                $toState = ($leadData['lead_heavy_lifting']['to_state'] != NULL && $leadData['lead_heavy_lifting']['to_state'] != "") ? $leadData['lead_heavy_lifting']['to_state']. " " : '';
                                $toZipcode = ($leadData['lead_heavy_lifting']['to_zipcode'] != NULL && $leadData['lead_heavy_lifting']['to_zipcode'] != "") ? $leadData['lead_heavy_lifting']['to_zipcode'] : '';
                                $toAddress =  $toCity . $toState . $toZipcode;
                            }
                            if($leadData['lead_category_id'] == 4) {
                                if($snapField['field_name'] == 'move_date') {
                                    if(isset($leadData['lead_car_transport']['move_date'])) {
                                        $org_moveDate = date_create($leadData['lead_car_transport']['move_date']);
                                        $new_moveDate = date_format($org_moveDate, $movedate_format);
                                        $move_date = $new_moveDate;
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $move_date;
                                }
                                if($snapField['field_name'] == 'from_zip' || $snapField['field_name'] == 'from_zipcode'){
                                    $from_zipcode = "";
                                    if(isset($leadData['lead_car_transport']['from_zipcode'])) {
                                        $from_zipcode = str_pad($leadData['lead_car_transport']['from_zipcode'], 5, '0', STR_PAD_LEFT);
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $from_zipcode;
                                }
                                if($snapField['field_name'] == 'from_state'){
                                    $from_state = "";
                                    if(isset($leadData['lead_car_transport']['from_state'])) {
                                        $from_state = $leadData['lead_car_transport']['from_state'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $from_state;
                                }
                                if($snapField['field_name'] == 'from_city'){
                                    $from_city = "";
                                    if(isset($leadData['lead_car_transport']['from_city'])) {
                                        $from_city = $leadData['lead_car_transport']['from_city'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $from_city;
                                }
                                if($snapField['field_name'] == 'from_areacode'){
                                    $from_areacode = "";
                                    if(isset($leadData['lead_car_transport']['from_areacode'])) {
                                        $from_areacode = $leadData['lead_car_transport']['from_areacode'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $from_areacode;
                                }
                                if($snapField['field_name'] == 'to_zip' || $snapField['field_name'] == 'to_zipcode'){
                                    $to_zipcode = "";
                                    if(isset($leadData['lead_car_transport']['to_zipcode'])) {
                                        $to_zipcode = str_pad($leadData['lead_car_transport']['to_zipcode'], 5, '0', STR_PAD_LEFT);
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $to_zipcode;
                                }
                                if($snapField['field_name'] == 'to_state'){
                                    $to_state = "";
                                    if(isset($leadData['lead_car_transport']['to_state'])) {
                                        $to_state = $leadData['lead_car_transport']['to_state'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $to_state;
                                }
                                if($snapField['field_name'] == 'to_city'){
                                    $to_city = "";
                                    if(isset($leadData['lead_car_transport']['to_city'])) {
                                        $to_city = $leadData['lead_car_transport']['to_city'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $to_city;
                                }
                                if($snapField['field_name'] == 'to_areacode'){
                                    $to_areacode = "";
                                    if(isset($leadData['lead_car_transport']['to_areacode'])) {
                                        $to_areacode = $leadData['lead_car_transport']['to_areacode'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $to_areacode;
                                }
                                if($snapField['field_name'] == 'move_size'){
                                    $move_size = "";
                                    if(isset($leadData['lead_car_transport']['move_size'])) {
                                        $move_size = $leadData['lead_car_transport']['move_size'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $move_size;
                                }
                                if($snapField['field_name'] == 'move_type'){
                                    $move_type = "";
                                    if(isset($leadData['lead_car_transport']['move_type'])) {
                                        $move_type = $leadData['lead_car_transport']['move_type'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $move_type;
                                }
                                if($snapField['field_name'] == 'distance'){
                                    $distance = "";
                                    if(isset($leadData['lead_car_transport']['distance'])) {
                                        $distance = $leadData['lead_car_transport']['distance'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $distance;
                                }
                                if($snapField['field_name'] == 'is_operational'){
                                    $is_operational = "";
                                    if(isset($leadData['lead_car_transport']['is_operational'])) {
                                        $is_operational = $leadData['lead_car_transport']['is_operational'];
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $is_operational;
                                }
                                if($snapField['field_name'] == 'car_type'){
                                    $car_type = "";
                                    if(isset($leadData['lead_car_transport']['car_type_id'])) {
                                        $car_type = $leadData['lead_car_transport']['mst_car_type']['car_type_id'];
                                        if (isset($carTypeArray[$leadData['lead_car_transport']['car_type_id']])){
                                            $car_type = $carTypeArray[$leadData['lead_car_transport']['car_type_id']];
                                        }
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $car_type;
                                }
                                if($snapField['field_name'] == 'transport_type'){
                                    $transport_type = 1;
                                    if($leadData['lead_car_transport']['transport_type'] == 'enclosed') {
                                        $transport_type = 2;
                                    }
                                    $fieldsarray[$mappingField['post_para_key']] = $transport_type;
                                }
                                if($snapField['field_name'] == 'property_type'){
                                    $fieldsarray[$mappingField['post_para_key']] = "vehicle transportation";
                                }
                                $city = ($leadData['lead_car_transport']['from_city'] != NULL && $leadData['lead_car_transport']['from_city'] != "") ? $leadData['lead_car_transport']['from_city']. ", " : '';
                                $state = ($leadData['lead_car_transport']['from_state'] != NULL && $leadData['lead_car_transport']['from_state'] != "") ? $leadData['lead_car_transport']['from_state']. " " : '';
                                $zipcode = ($leadData['lead_car_transport']['from_zipcode'] != NULL && $leadData['lead_car_transport']['from_zipcode'] != "") ? $leadData['lead_car_transport']['from_zipcode'] : '';
                                $address =  $city . $state . $zipcode;

                                $toCity = ($leadData['lead_car_transport']['to_city'] != NULL && $leadData['lead_car_transport']['to_city'] != "") ? $leadData['lead_car_transport']['to_city']. ", " : '';
                                $toState = ($leadData['lead_car_transport']['to_state'] != NULL && $leadData['lead_car_transport']['to_state'] != "") ? $leadData['lead_car_transport']['to_state']. " " : '';
                                $toZipcode = ($leadData['lead_car_transport']['to_zipcode'] != NULL && $leadData['lead_car_transport']['to_zipcode'] != "") ? $leadData['lead_car_transport']['to_zipcode'] : '';
                                $toAddress =  $toCity . $toState . $toZipcode;
                            }
                            if($snapField['field_name'] == 'address_formatted'){
                                $fieldsarray[$mappingField['post_para_key']] = $address;
                            }
                            if($snapField['field_name'] == 'address_formatted1'){
                                $fieldsarray[$mappingField['post_para_key']] = $toAddress;
                            }
                            if ($snapField['field_name'] == 'from_city2') {
                                $fieldsarray[$mappingField['post_para_key']] = $city;
                            }
                            if ($snapField['field_name'] == 'from_state2') {
                                $fieldsarray[$mappingField['post_para_key']] = $state;
                            }
                            if ($snapField['field_name'] == 'from_zip2') {
                                $fieldsarray[$mappingField['post_para_key']] = $zipcode;
                            }

                            if(isset($mappingField['root_element']) && $mappingField['root_element'] != NULL){
                                // if($key == 0 ){
                                $rootelement[$mappingField['root_element']] = $fieldsarray;
                                // }
                            }else{
                                $rootelement = $fieldsarray;
                            }
                        }

                    }
                }
                $response['data']['campaignCrmDelivery'][$c]['sendData'] = $rootelement;
                $response['data']['campaignCrmDelivery'][$c]['sendUrl'] = $url;

                $requestParameter = trim(json_encode($rootelement, JSON_UNESCAPED_SLASHES));
                if($crmDetail['delivery_crm']['delivery_type'] == 'post' && $crmDetail['delivery_crm']['request_format'] == "HTTP") {
                    $responseVar = self::postMethod($url, $rootelement);
                    $logAction = "Post Method with HTTP Request.";
                    $response['data']['campaignCrmDelivery'][$c]['responseVar'] = $responseVar;
                    $requestParameter = http_build_query($rootelement);
                } else if($crmDetail['delivery_crm']['delivery_type'] == 'post' && $crmDetail['delivery_crm']['request_format'] == "JSON") {
                    // \Log::info( "jsonMethod start= ". json_encode( $rootelement));
                    $logAction = "Post Method with JSON Request.";
                    $responseVar = self::jsonMethod($url, $rootelement, $campaignData->business_id, $headerToken);
                    $response['data']['campaignCrmDelivery'][$c]['responseVar'] = $responseVar;
                    // \Log::info( "jsonMethod response= ". json_encode( $responseVar));
                } else if($crmDetail['delivery_crm']['delivery_type'] == 'get') {
                    $logAction = "Get Method.";
                    $responseVar = self::getMethod($url, $rootelement);
                    $response['data']['campaignCrmDelivery'][$c]['responseVar'] = $responseVar;
                    $requestParameter = http_build_query($rootelement);
                } else {
                    $logAction = "XML Method.";
                    $responseVar = self::xmlMethod($url, $rootelement);
                    $response['data']['campaignCrmDelivery']['responseVar'][$c] = $responseVar;
                }
                CampaignCrmDeliveryLog::create([
                    'campaign_id' => $campaign_id,
                    'url' => $url,
                    'request_parameter' => $requestParameter,
                    'response_parameter' => $responseVar,
                    'lead_id' => $lead_id,
                    'log_type' => $logAction,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
                $exist_success = CampaignLeadDeliveryResponse::where('delivery_crm_id', $crmDetail['delivery_crm']['delivery_crm_id'])->where( 'response_type', 'success' )->first();
                $response_text = 'Failed';
                // $crmNotDeliveredEmailData['crmname'] = ucfirst($crmDetail['delivery_crm']['name']);
                if( $exist_success){
                    if ($responseVar != "" &&
                        (
                            str_contains(strtolower($responseVar), strtolower($exist_success->response_text))
                            || str_contains(strtolower($responseVar), strtolower('true'))
                            || str_contains(strtolower($responseVar), strtolower('OK'))
                            || @in_array('OK', @explode(',', $responseVar))
                            || str_contains(strtolower($responseVar), strtolower('"success":1'))
                            || str_contains(strtolower($responseVar), strtolower('BLVD Moving'))
                        )
                    ) {
                        $response_text = 'Success'; // echo "CRM Delivered";
                    }else{
                        $crmNotDeliveredEmailData['leaddeliveryvalue1'] = ucfirst($crmDetail['delivery_crm']['name']);
                        $crmNotDeliveredEmailData['leaddeliveryvalue2'] = $crmDetail['delivery_crm']['delivery_url'];
                        $startdate = date("Y-m-d") . " 00:00:00";
                        $enddate = date("Y-m-d") . " 23:59:59";
                        //DB::enableQueryLog();
                        $emailcount = EmailLog::where("campaign_id", $crmDetail['campaign_info']['campaign_id'] )->where("subject","LIKE","%". " - CRM - Lead Delivery Email Failed")->where("type","leaddelivery")->whereBetween('created_at', [$startdate, $enddate])->get()->toArray();
                        //$query = DB::getQueryLog();
                        if( count($emailcount) == 0 ){
                            $leadNotDeliveredEmailobj = new LeadNotDeliveredEmail();
                            $leadNotDeliveredEmailobj->emailLeadNotDeliveredEmail($crmNotDeliveredEmailData); // echo "CRM not Delivered and will email set to user";
                        }
                    }
                }
                $c++;
            }
        }

        if(count($campaignCrmDelivery) > 0 && count($leadData) > 0) {
            $response['message'] = "Lead & Campaign Data.";
            $response['status'] = 1;
        }
        return $response;
    }

    public static function getMethod($url, $params) {
        try {
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => $url.'?'.http_build_query($params),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
            ));

            $response = curl_exec($curl);
            curl_close($curl);
            return $response;
        }
        catch(Exception $e)
        {
            \Log::info($e);
            return "Get Method Curl not call. Error response.";
        }
    }

    public static function xmlMethod($url, $deliveryDataArr) {
        //echo $url;
        //This xml is only work for all_my_sons CRM
        $xml = '<?xml version="1.0" encoding="UTF-8"?><MoverLead version="1.0" source="primemarketing.us">';
        foreach($deliveryDataArr as $k => $val) {
            $xml .= '<'.$k.'>';
            foreach ($val as $subelekey => $subelevalue) {
                $xml.= '<'.$subelekey.'>'.$subelevalue.'</'.$subelekey.'>';
            }
            $xml .= '</' . $k . '>';

        }
        $xml .= '</MoverLead>';

        $headers = array(
            "Content-type: application/xml",
            "Content-length: " . strlen($xml),
            "Connection: close",
        );
        //echo $xml;
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL,$url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $xml);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            //echo $url;
            /*$ch = curl_init($url);
            curl_setopt( $ch, CURLOPT_POST, TRUE );
            curl_setopt( $ch, CURLOPT_RETURNTRANSFER, TRUE );
            curl_setopt( $ch, CURLOPT_SSL_VERIFYPEER, FALSE );
            curl_setopt( $ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC );
            curl_setopt( $ch, CURLOPT_POSTFIELDS, $deliveryDataArr );*/

            $response = curl_exec($ch);
            return $response;
            if(curl_errno($ch))
                curl_error($ch);
            else
                curl_close($ch);
        }
        catch(Exception $e)
        {
            \Log::info($e);
            return "XML Method Curl not call. Error response.";
        }
    }

    public static function jsonMethod($url, $deliveryDataArr, $businessId=0, $headerToken="") {
        \Log::info("jsonMethod URL: " . $url);
        $jsonData = json_encode($deliveryDataArr);
        //echo 'jsonData: '.$jsonData;die;
        if($url == 'https://east-2.calltools.io/api/contacts/') {
            //Only for call tools CRM
            if ($businessId == 725 || $businessId == '725') {
                $headers = array(
                    'Accept: application/json',
                    'Content-Type: application/json',
                    'Authorization: Token 163075e56ce78d1f19cf557b3f2eda7ae851d6e3'
                );
            } else {
                $headers = array(
                    'Accept: application/json',
                    'Content-Type: application/json',
                    'Authorization: Token 5597ca2cbfce15ac30e8cdae804d32d83179a3c8'
                );
            }
        } else if($url == 'https://west-2.calltools.io/api/contacts/') {
            //Only for call tools CRM
            $headers = array(
                'Accept: application/json',
                'Content-Type: application/json',
                'Authorization: Token cc5b6e685cbda31d28bc8d8b80fa8fbe49a21c24'
            );
        } else if($url == 'https://oncueapp.com/api/v1/companies/') {
            // Only for oncueapp CRM
            $url = $url . $deliveryDataArr['company_id'] . "/customers";
            $oncueArr = array();
            $oncueArr['company_id'] = $deliveryDataArr['company_id'];
            $oncueArr['customer']['lead_type'] = "paid_lead";
            $oncueArr['customer']['referral_source'] = $deliveryDataArr['company_name'];
            $oncueArr['customer']['scheduled_on'] = date("Y-m-d", strtotime($deliveryDataArr['scheduled_on']));
            $oncueArr['customer']['from_attributes']['zip_code'] = $deliveryDataArr['from_zip_code'];
            $oncueArr['customer']['to_attributes']['zip_code'] = $deliveryDataArr['to_zip_code'];
            $oncueArr['customer']['from_attributes']['move_type'] = "single_family_home";
            $oncueArr['customer']['from_attributes']['move_size'] = $deliveryDataArr['move_size'];
            $oncueArr['customer']['name'] = $deliveryDataArr['first_name'] . ' ' . $deliveryDataArr['last_name'];
            $oncueArr['customer']['phone_number'] = $deliveryDataArr['phone_number'];
            $oncueArr['customer']['email_address'] = $deliveryDataArr['email_address'];
            $jsonData = json_encode($oncueArr);
            $headers = array(
                "Content-Type:application/json"
            );
        } else if($url == 'https://groovinmovin.demo.chariotmove.com/api/external/lead' || $url == 'https://smmovingcompany.chariotmove.com/api/external/lead') {
            // Only for Chariot Move

            $chariot_moveArr = array();
            $chariot_moveArr['meta']['account_id'] = $deliveryDataArr['meta']['account_id'];
            $chariot_moveArr['meta']['auth_token'] = $deliveryDataArr['meta']['auth_token'];
            $chariot_moveArr['customer']['first_name'] = $deliveryDataArr['customer']['first_name'];
            $chariot_moveArr['customer']['last_name'] = $deliveryDataArr['customer']['last_name'];
            $chariot_moveArr['customer']['phone'] = $deliveryDataArr['customer']['phone'];
            $chariot_moveArr['customer']['email'] = $deliveryDataArr['customer']['email'];
            $chariot_moveArr['customer']['category'] = $deliveryDataArr['customer']['category'];
            $chariot_moveArr['locations'][]= array(
                                'raw_address'  =>  $deliveryDataArr['locations']['raw_address'],
                                'floor'  => (isset($deliveryDataArr['inventory']['floor']) && !empty( $deliveryDataArr['inventory']['floor']) ? $deliveryDataArr['inventory']['floor'] : "")
                            );
            $chariot_moveArr['lead']['job_services']['moving_service'] = $deliveryDataArr['lead']['moving_service'];
            $chariot_moveArr['job']['notes'] = $deliveryDataArr['job']['notes'];
            // $chariot_moveArr['inventory']['inventory_items'][] = array(
            //                                                             'name'  => (isset($deliveryDataArr['inventory']['name']) && !empty( $deliveryDataArr['inventory']['name']) ? $deliveryDataArr['inventory']['name'] : ""),
            //                                                             'room' => (isset($deliveryDataArr['inventory']['room']) && !empty( $deliveryDataArr['inventory']['room']) ? $deliveryDataArr['inventory']['room'] : ""),
            //                                                             'quantity'=> (isset($deliveryDataArr['inventory']['quantity']) && !empty( $deliveryDataArr['inventory']['quantity']) ? $deliveryDataArr['inventory']['quantity'] : 0),
            //                                                             'not_moving_quantity' => (isset($deliveryDataArr['inventory']['not_moving_quantity']) && !empty( $deliveryDataArr['inventory']['not_moving_quantity']) ? $deliveryDataArr['inventory']['not_moving_quantity'] : 0),
            //                                                             'volume' => (isset($deliveryDataArr['inventory']['volume']) && !empty( $deliveryDataArr['inventory']['volume']) ? $deliveryDataArr['inventory']['volume'] : 0),
            //                                                             'weight' => (isset($deliveryDataArr['inventory']['weight']) && !empty( $deliveryDataArr['inventory']['weight']) ? $deliveryDataArr['inventory']['weight'] : 0)
            //                                                         );
            // $chariot_moveArr['inventory']['notes'] =(isset($deliveryDataArr['inventory']['notes']) && !empty( $deliveryDataArr['inventory']['notes']) ? $deliveryDataArr['inventory']['notes'] : null);

            $jsonData = json_encode($chariot_moveArr );
            // echo 'chariot_moveArr array: '; print_r($jsonData); die;
            $headers = array(
                "Content-Type:application/json"
            );
        } else if($url == 'https://api.supermove.co/v1/projects/sync' ) {
            // Only for Chariot Move
            $super_moveArr = array();
            $super_moveArr['id'] = $deliveryDataArr['id'];
            $customer = array(
                'first_name'  => (isset($deliveryDataArr['customer']['first_name']) && !empty( $deliveryDataArr['customer']['first_name']) ? $deliveryDataArr['customer']['first_name'] : ""),
                'last_name'  => (isset($deliveryDataArr['customer']['last_name']) && !empty( $deliveryDataArr['customer']['last_name']) ? $deliveryDataArr['customer']['last_name'] : ""),
                'phone_number'  => (isset($deliveryDataArr['customer']['phone_number']) && !empty( $deliveryDataArr['customer']['phone_number']) ? $deliveryDataArr['customer']['phone_number'] : ""),
                'email'  => (isset($deliveryDataArr['customer']['email']) && !empty( $deliveryDataArr['customer']['email']) ? $deliveryDataArr['customer']['email'] : ""),
            );
            $super_moveArr['project']['customer'] = $customer;

            $super_moveArr['project']['jobs'][0] = array(
                                        'status'  => (isset($deliveryDataArr['jobs']['status']) && !empty( $deliveryDataArr['jobs']['status']) ? $deliveryDataArr['jobs']['status'] : ""),
                                        'move_size'  => (isset($deliveryDataArr['jobs']['move_size']) && !empty( $deliveryDataArr['jobs']['move_size']) ? $deliveryDataArr['jobs']['move_size'] : ""),
                                        'date'  => (isset($deliveryDataArr['jobs']['date']) && !empty( $deliveryDataArr['jobs']['date']) ? $deliveryDataArr['jobs']['date'] : ""),
                                        'start_time_1'  => (isset($deliveryDataArr['jobs']['start_time_1']) && !empty( $deliveryDataArr['jobs']['start_time_1']) ? $deliveryDataArr['jobs']['start_time_1'] : 0000),
                                        'additional_notes'  => (isset($deliveryDataArr['jobs']['additional_notes']) && !empty( $deliveryDataArr['jobs']['additional_notes']) ? $deliveryDataArr['jobs']['additional_notes'] : ""),
                                        'dispatch_notes'  => (isset($deliveryDataArr['jobs']['dispatch_notes']) && !empty( $deliveryDataArr['jobs']['dispatch_notes']) ? $deliveryDataArr['jobs']['dispatch_notes'] : ""),
                                        'office_notes'  => (isset($deliveryDataArr['jobs']['office_notes']) && !empty( $deliveryDataArr['jobs']['office_notes']) ? $deliveryDataArr['jobs']['office_notes'] : "")
                                    );
            $super_moveArr['project']['jobs'][0]['job_type'] = array( "identifier" => (isset($deliveryDataArr['jobs']['identifier']) && !empty( $deliveryDataArr['jobs']['identifier']) ? $deliveryDataArr['jobs']['identifier'] : ""));
            $super_moveArr['project']['jobs'][0]['locations'][0]= array(
                                        'address'  => (isset($deliveryDataArr['locations']['address']) && !empty( $deliveryDataArr['locations']['address']) ? $deliveryDataArr['locations']['address'] : ""),
                                        'city'  => (isset($deliveryDataArr['locations']['city']) && !empty( $deliveryDataArr['locations']['city']) ? $deliveryDataArr['locations']['city'] : ""),
                                        'zip_code'  => (isset($deliveryDataArr['locations']['zip_code']) && !empty( $deliveryDataArr['locations']['zip_code']) ? $deliveryDataArr['locations']['zip_code'] : ""),
                                        'unit'  => (isset($deliveryDataArr['locations']['unit']) && !empty( $deliveryDataArr['locations']['unit']) ? $deliveryDataArr['locations']['unit'] : ""),
                                        'floor_number'  => (isset($deliveryDataArr['locations']['floor_number']) && !empty( $deliveryDataArr['locations']['floor_number']) ? $deliveryDataArr['locations']['floor_number'] : 0),
                                        'notes'  => (isset($deliveryDataArr['locations']['notes']) && !empty( $deliveryDataArr['locations']['notes']) ? $deliveryDataArr['locations']['notes'] : ""),
                                    );
            $super_moveArr['project']['jobs'][0]['locations'][1]= array(
                'address'  => (isset($deliveryDataArr['tolocations']['address']) && !empty( $deliveryDataArr['tolocations']['address']) ? $deliveryDataArr['tolocations']['address'] : ""),
                'city'  => (isset($deliveryDataArr['tolocations']['city']) && !empty( $deliveryDataArr['tolocations']['city']) ? $deliveryDataArr['tolocations']['city'] : ""),
                'zip_code'  => (isset($deliveryDataArr['tolocations']['zip_code']) && !empty( $deliveryDataArr['tolocations']['zip_code']) ? $deliveryDataArr['tolocations']['zip_code'] : ""),
                'unit'  => (isset($deliveryDataArr['tolocations']['unit']) && !empty( $deliveryDataArr['tolocations']['unit']) ? $deliveryDataArr['tolocations']['unit'] : ""),
                'floor_number'  => 0,
                'notes'  => "",
            );
            $jsonData = json_encode($super_moveArr );
            if ($businessId == 713 || $businessId == '713') {
                $headers = array(
                    "Content-Type:application/json",
                    'authorization: Bearer supermove_live_5d1dd3748e35d23d6d003a7e82dcd847'
                );
            } else if ($businessId == 1077 || $businessId == '1077') {
                $headers = array(
                    "Content-Type:application/json",
                    'authorization: Bearer supermove_live_65e38b2a6239e692930db4b0b3dbb56e'
                );
            } else {
                $headers = array(
                    "Content-Type:application/json",
                    'authorization: Bearer supermove_live_0f01e13fe82ef6e9031e156cadb67329'
                );
            }
            \Log::info('SUPERMOVE='.  $jsonData);
        } /*else if ($url == 'https://rest.gohighlevel.com/v1/contacts/') {
            if ($businessId == 852 || $businessId == '852') {
                //Only for call tools CRM
                $headers = array(
                    'Content-Type: application/json',
                    'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2NhdGlvbl9pZCI6Ijc1U0ZORWVXNUIwT0gwUTF3dGxlIiwidmVyc2lvbiI6MSwiaWF0IjoxNzE5MjYxNjM3MzYxLCJzdWIiOiJUTVJTNEdYOTRPMHFOVjFRNWdMZyJ9.7yT0lK90j3vvOm8ETA1SUtSq5hH05LrOfHrivuEpgNU'
                );
            } else {
                //Only for call tools CRM
                $headers = array(
                    'Content-Type: application/json',
                    'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2NhdGlvbl9pZCI6ImJidllNS1F6Ukt0Q2doOGxKZXJWIiwidmVyc2lvbiI6MSwiaWF0IjoxNzQxMjc2NjE3NDc1LCJzdWIiOiJyUDJVQlhicHFCYWNUMEFFNEZseSJ9.mdAa9_I_-Gag4678wCOxZzH09oqKaweHxH7glmB7Piw'
                );
            }
        }*/ else if ($url == 'https://webform.moverstech.com/cPaydy7o/jobs') {
            // Only for Movers Tech Move
            $movers_techArr = array();
            $movers_techArr['data'] = $deliveryDataArr;
            $jsonData = json_encode($movers_techArr);
            $headers = array(
                "Content-Type:application/json"
            );
        } else if($url == 'https://jcefgbojephdfbwzkcpj.supabase.co/functions/v1/submit-lead') {
            $headers = array(
                'Content-Type: application/json',
                'x-api-key: 1dc0dcc7-a940-40d1-9171-88761200bddc'
            );
        } else if (isset($headerToken) && $headerToken!="") {
            $headers = array(
                'Content-Type: application/json',
                'Authorization: Bearer ' . $headerToken
            );
        } else {
            $headers = array(
                "Type:application/json",
                "Content-Type:application/json"
            );
        }
        //echo $jsonData;
        try {
            \Log::info('Try start=');
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL,$url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_TIMEOUT, 50);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            $response = curl_exec($ch);
            \Log::info(json_encode( $response) );
            if(curl_errno($ch)){
                curl_error($ch);
                \Log::info(curl_error($ch));
            } else {
                curl_close($ch);
            }
            return $response;
        }
        catch(Exception $e)
        {
            \Log::info($e);
            return "POST Method with JSON Curl not call. Error response.";
        }
    }

    public static function postMethod($url, $fields) {
        try {
            if($url == "https://trackdrive.com/api/v1/leads") {
                $fields = http_build_query($fields);
                $res = curl_init($url."?".$fields);
            } else {
                $res = curl_init($url);
                //$fields =  json_encode($fields);
            }

            curl_setopt( $res, CURLOPT_POST, TRUE );
            curl_setopt( $res, CURLOPT_RETURNTRANSFER, TRUE );
            curl_setopt( $res, CURLOPT_SSL_VERIFYPEER, FALSE );
            curl_setopt( $res, CURLOPT_HTTPAUTH, CURLAUTH_BASIC );
            if($url == "https://trackdrive.com/api/v1/leads") {
            } else {
                curl_setopt( $res, CURLOPT_POSTFIELDS, $fields);
            }

            $result = curl_exec( $res );
            curl_close($res);
            return $result;
        }
        catch(Exception $e)
        {
            \Log::info($e);
            return "POST Method with HTTP Curl not call. Error response.";
        }
    }

    public function getFLName( $name ) {
        $res = [];
        $res['first_name'] = "";
        $res['last_name'] = "";
        if($name != null) {
            $fnames = explode(" ", $name);
            if(isset($fnames[0])) {
                $res['first_name'] = trim($fnames[0]);
            }
            if(isset($fnames[1])) {
                $res['last_name'] = trim($fnames[1]);
            }
        }
        return $res;
    }
}
