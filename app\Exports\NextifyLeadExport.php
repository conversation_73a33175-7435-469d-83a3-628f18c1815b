<?php

namespace App\Exports;

use App\Models\Leads\Leads;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use DB;

class NextifyLeadExport implements FromCollection, WithHeadings
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function headings(): array
    {
        //Put Here Header Name That you want in your excel sheet
        return [
            'Id',
            'Ping Id',
            /*'Source Name',*/
            'Name',
            'Phone',
            'Email',
            'From Zip',
            'To Zip',
            'Payout',
            'Move Size',
            'Created DateTime'
        ];
    }

    public function collection()
    {
        $startDate  = '2025-07-16'; //date("Y-m-d", mktime(0, 0, 0, date("m")-1, 1));
        $endDate    = '2025-07-31'; //date("Y-m-d", mktime(0, 0, 0, date("m"), 0));
        $leadArray  = array();
        $leadDetail = DB::select("SELECT l.lead_id, pp.ping_id, pp.payout AS ping_payout, l.name, l.phone, l.email, lm.from_zipcode, lm.to_zipcode, lm.move_size_id, ls.lead_source_name, mz.move_size_id, lr.payout, lr.created_at
                                  FROM lead_routing lr
                                  LEFT JOIN `lead` l on lr.lead_id = l.lead_id
                                  LEFT JOIN campaign c on lr.campaign_id = c.campaign_id
                                  INNER JOIN lead_moving lm on l.lead_id = lm.lead_id
                                  LEFT JOIN mst_lead_source ls ON l.lead_source_id = ls.lead_source_id
                                  LEFT JOIN mst_move_size mz ON lm.move_size_id = mz.move_size_id
                                  LEFT JOIN ping_post_payout pp ON l.lead_id = pp.lead_id
                                  WHERE lr.campaign_id IN (4683, 4682) AND lr.route_status = 'sold' AND pp.is_win='yes' AND (lr.created_at >= '".$startDate." 00:00:00' AND lr.created_at <= '".$endDate." 23:59:59')
						          GROUP BY l.lead_id ORDER BY l.lead_id ASC");
        //echo '<pre>'; print_r($leadDetail); exit;

        DB::table('dev_debug')->insert(['sr_no' => 120, 'result' => "SELECT l.lead_id, pp.ping_id, pp.payout AS ping_payout, l.name, l.phone, l.email, lm.from_zipcode, lm.to_zipcode, lm.move_size_id, ls.lead_source_name, mz.move_size_id, lr.payout, lr.created_at
                                  FROM lead_routing lr
                                  LEFT JOIN `lead` l on lr.lead_id = l.lead_id
                                  LEFT JOIN campaign c on lr.campaign_id = c.campaign_id
                                  INNER JOIN lead_moving lm on l.lead_id = lm.lead_id
                                  LEFT JOIN mst_lead_source ls ON l.lead_source_id = ls.lead_source_id
                                  LEFT JOIN mst_move_size mz ON lm.move_size_id = mz.move_size_id
                                  LEFT JOIN ping_post_payout pp ON l.lead_id = pp.lead_id
                                  WHERE lr.campaign_id IN (4683, 4682) AND lr.route_status = 'sold' AND pp.is_win='yes' AND (lr.created_at >= '".$startDate." 00:00:00' AND lr.created_at <= '".$endDate." 23:59:59')
						          GROUP BY l.lead_id ORDER BY l.lead_id ASC"]);

        for ($i=0; $i < count($leadDetail); $i++) {
            $leadArray[] = array(
                'lead_id' => $leadDetail[$i]->lead_id,
                'ping_id' => $leadDetail[$i]->ping_id,
                /*'sourcename' => $leadDetail[$i]->lead_source_name,*/
                'name' => $leadDetail[$i]->name,
                'phone' => $leadDetail[$i]->phone,
                'email' => $leadDetail[$i]->email,
                'from_zip' => $leadDetail[$i]->from_zipcode,
                'to_zip' => $leadDetail[$i]->to_zipcode,
                'payout' => $leadDetail[$i]->payout,
                'move_size' => $leadDetail[$i]->move_size_id,
                'created_at' => $leadDetail[$i]->created_at
            );
        }
        //echo '<pre>'; print_r($leadArray); exit;
        return collect($leadArray);
    }
}
