<?php

namespace App\Http\Controllers\Businesses;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\Controller;
use App\Helpers\CommonFunctions;
use App\Models\Business\Business;
use App\Models\Campaign\Campaign;
use App\Models\Business\BusinessCampaingnPaymentLog;
use App\Models\Business\BusinessCC;
use App\Models\Business\StaxPayment;
use App\Models\Master\MstChargeType;
use App\Models\Master\MstLeadCategory;
use App\Models\User;
use Exception;
use Auth;
use App\Helpers\Helper;
use App\Models\Business\BusinessesCampaignEomBalance;
use Illuminate\Support\Facades\DB;

class BusinessPaymentHistoryController extends Controller
{
    public function transactionHistory(Request $request,$bid) {
        $endDecObj = new CommonFunctions;
        $businessId = $endDecObj->customeDecryption($bid);
        $businessDetail = Business::where("business_id", $businessId)->first();
        return view("businesses.transactionhistory", compact('bid', 'businessDetail'));
    }

    public function paymentHistoryDataTable(Request $request) {
        $campaignCategory = "alldata";
        // if ($request->get('campaignCategory') != "alldata") {
        //     $campaignCategory = $request->get('campaignCategory');
        // }

        if (!empty($request->get('chargeType'))) {
            $chargeType = $request->get('chargeType');
            $startDate = $request->get('startDate');
            $endDate = $request->get('endDate');

            $campaignIdArray = $finalData = [];
            $requestBusinessId = $requestCampaignId = $appendQuery = "";
            if (!empty($request->get('reqBusinessesId'))) {
                $requestBusinessId = $request->get('reqBusinessesId');
                //fetch campaign_id from business_id
                $campaignDetail = Campaign::where('business_id', $requestBusinessId)->get(['campaign_id']);
                if (count($campaignDetail) > 0) { foreach ($campaignDetail as $campaign) {
                    $campaignIdArray[] = $campaign->campaign_id;
                }
                    if (count($campaignIdArray) > 0) {
                        $requestCampaignId = implode(",", $campaignIdArray);
                        $appendQuery = " AND ( business_id = '" . $requestBusinessId . "' or campaign_id IN (" . $requestCampaignId . ") )";
                    }
                } else {
                    $appendQuery = " AND ( business_id = '" . $requestBusinessId . "')";
                }
            }
            if ($startDate == "a") {
                $startDate = date("Y-m-d");
                $endDate = date("Y-m-d", strtotime("+1 day"));
            }

            if ($chargeType == 'alldata') {
                //dump("I AM HERE");
                $query = "SELECT * from business_campaign_payment where created_at between '" . $startDate . " 00:00:00' and '" . $endDate . " 23:59:59' " . $appendQuery;
                //print_r($query);
                $paymentDetail = DB::select($query);
                
            } else {
                // $query = "SELECT * FROM business_campaign_payment where DATE(`created_at`) >= '" . $startDate . "' AND DATE(`created_at`) < '" . $endDate . "' and charge_type_id = " . 
                // $chargeType . " " . $appendQuery;

                $query = "SELECT * from business_campaign_payment where created_at between '" . $startDate . " 00:00:00' and '" . $endDate . " 23:59:59' and charge_type_id = " . $chargeType . " " . $appendQuery;
                $paymentDetail = DB::select($query);
            }

            $paymentIdArray = $businessIdArray = $campaignIdArray = $businessArray = $campaignArray = $userIdArray = $userNameArray = $cardIdArray = $cardArray = $chargeTypeArray = $categoryArray = $messageArray = $transactionIdArray = $settlementIdArray = $batchIdArray = array();
            foreach ($paymentDetail as $payment) {
                if ($payment->business_campaign_payment_id > 0 && !in_array($payment->business_campaign_payment_id, $paymentIdArray)) {
                    $paymentIdArray[] = $payment->business_campaign_payment_id;
                }
                if ($payment->business_id > 0 && !in_array($payment->business_id, $businessIdArray)) {
                    $businessIdArray[] = $payment->business_id;
                }
                if ($payment->charge_user_id > 0 && !in_array($payment->charge_user_id, $businessIdArray)) {
                    $businessIdArray[] = $payment->charge_user_id;
                }
                if ($payment->campaign_id > 0 && !in_array($payment->campaign_id, $campaignIdArray)) {
                    $campaignIdArray[] = $payment->campaign_id;
                }
                if (($payment->charge_method_type == "snapituser" || $payment->charge_method_type == "subscription") && !in_array($payment->charge_user_id, $userIdArray)) {
                    $userIdArray[] = $payment->charge_user_id;
                }
                if ($payment->business_cc_id > 0 && !in_array($payment->business_cc_id, $cardIdArray)) {
                    $cardIdArray[] = $payment->business_cc_id;
                }
                if (!in_array($payment->fatt_payment_id, $transactionIdArray)) {
                    $transactionIdArray[] = $payment->fatt_payment_id;
                }
            }

            $logDetail = BusinessCampaingnPaymentLog::whereIn('business_campaign_payment_id', $paymentIdArray)->get()->toArray();
            for ($s = 0; $s < count($logDetail); $s++) {
                $response = json_decode($logDetail[$s]['response'], true);
                if (isset($response['success']) && $response['success'] == true) {
                    $messageArray[$logDetail[$s]['business_campaign_payment_id']] = 'Approved';
                } else {
                    if (isset($response['message'])) {
                        $messageArray[$logDetail[$s]['business_campaign_payment_id']] = $response['message'];
                    } else {
                        $messageArray[$logDetail[$s]['business_campaign_payment_id']] = preg_replace('/[^a-zA-Z0-9\s]/', ' ', $logDetail[$s]['response']);
                    }
                }
            }

            $staxDetail = StaxPayment::whereIn('fatt_transaction_id', $transactionIdArray)->get()->toArray();
            for ($s = 0; $s < count($staxDetail); $s++) {
                $settlementIdArray[$staxDetail[$s]['fatt_transaction_id']] = $staxDetail[$s]['fatt_settlement_id'];
                $batchIdArray[$staxDetail[$s]['fatt_transaction_id']] = $staxDetail[$s]['display_batch_id'];
            }

            $campaignDetail = Campaign::whereIn('campaign_id', $campaignIdArray)->get(['campaign_id', 'business_id', 'campaign_name', 'lead_category_id'])->toArray();
            for ($b = 0; $b < count($campaignDetail); $b++) {
                $campaignArray[$campaignDetail[$b]['campaign_id']] = $campaignDetail[$b];
                if (!in_array($campaignDetail[$b]['business_id'], $businessIdArray)) {
                    $businessIdArray[] = $campaignDetail[$b]['business_id'];
                }
            }
            //echo "<pre>";print_r($campaignArr);die;
            $businessDetail = Business::whereIn('business_id', $businessIdArray)->get(['business_id', 'business_name'])->toArray();
            for ($c = 0; $c < count($businessDetail); $c++) {
                $businessArray[$businessDetail[$c]['business_id']] = $businessDetail[$c]['business_name'];
            }
            $userDetail = User::whereIn('id', $userIdArray)->get(['id', 'name'])->toArray();
            for ($u = 0; $u < count($userDetail); $u++) {
                $userNameArray[$userDetail[$u]['id']] = $userDetail[$u]['name'];
            }
            $cardDetail = BusinessCC::whereIn('business_cc_id', $cardIdArray)->get(['business_cc_id', 'card'])->toArray();
            for ($g = 0; $g < count($cardDetail); $g++) {
                $cardArray[$cardDetail[$g]['business_cc_id']] = $cardDetail[$g]['card'];
            }

            $chargeTypeDetail = MstChargeType::get(['charge_type_id', 'charge_type']);
            for ($ct=0; $ct < count($chargeTypeDetail); $ct++) {
                $chargeTypeArray[$chargeTypeDetail[$ct]['charge_type_id']] = $chargeTypeDetail[$ct]['charge_type'];
            }

            $categoryDetail = MstLeadCategory::get(['lead_category_id', 'lead_category_name']);
            for ($ct=0; $ct < count($categoryDetail); $ct++) {
                $categoryArray[$categoryDetail[$ct]['lead_category_id']] = $categoryDetail[$ct]['lead_category_name'];
            }

            foreach ($paymentDetail as $payment) {
                $data = [];
                $data['id'] = $payment->business_campaign_payment_id;
                $businessIdCheck = $payment->business_id;
                $data['businessId'] = ($payment->business_id == ""|| $payment->business_id == 0) ? '--' : $payment->business_id;
                $data['newbusinessId'] = $payment->business_id;
                $data['campaignId'] = '--';
                $data['campaignName'] = '--';
                $categoryName = '--';
                $category = "0";
                if ($payment->campaign_id > 0) {
                    $data['campaignId'] = $payment->campaign_id;
                    if (isset($campaignArray[$payment->campaign_id])) {
                        $data['campaignName'] = $campaignArray[$payment->campaign_id]['campaign_name'];
                        $categoryId = $campaignArray[$payment->campaign_id]['lead_category_id'];
                        //echo $category."==".$payment->campaign_id."<br>";
                        $categoryName = $categoryArray[$categoryId];
                        $businessIdCheck = $campaignArray[$payment->campaign_id]['business_id'];
                    }
                }
                $data['campaignCategory'] = $categoryName;
                $data['businessName'] = '';
                if ($businessIdCheck > 0) {
                    $data['businessName'] = $businessArray[$businessIdCheck];
                    
                }
                $data['beforeCharge'] = ($payment->balance_before_charge == 0) ? '--' : '$' . sprintf('%0.2f', $payment->balance_before_charge);
                $data['amountCharge'] = ($payment->amount_charge == 0) ? '--' : '$' . sprintf('%0.2f', $payment->amount_charge);
                $data['afterCharge'] = ($payment->balance_after_charge == 0) ? '--' : '$' . sprintf('%0.2f', $payment->balance_after_charge);
                $data['totalCharge'] = (empty($payment->convenience_fee) || $payment->convenience_fee == 0) ? '--' : '$' . $this->setTwoDecimalPoint($payment->amount_charge + $payment->convenience_fee);
                $data['chargeType'] = ($payment->charge_type_id > 0) ? $chargeTypeArray[$payment->charge_type_id] : "--";
                $data['chargeMethod'] = "--";
                if ($payment->charge_method_type == "snapituser") {
                    $chargeMethod = "";
                    if ($payment->charge_method == "recurrent") {
                        $chargeMethod = " (Recurrent)";
                    }
                    if (isset($userNameArray[$payment->charge_user_id])) {
                        $data['chargeMethod'] = $userNameArray[$payment->charge_user_id] . $chargeMethod;
                    }
                } else if ($payment->charge_method_type == "subscription") {
                    $chargeMethod = " (Subscription)";
                    if ($payment->charge_method == "recurrent") {
                        $chargeMethod = " (Subscription Recurrent)";
                    }
                    if (isset($userNameArray[$payment->charge_user_id])) {
                        $data['chargeMethod'] = $userNameArray[$payment->charge_user_id] . $chargeMethod;
                    }
                } else {
                    if (isset($businessArray[$payment->charge_user_id])) {
                        $chargeMethodType = "";
                        if ($payment->charge_method_type == "moverinterfaceuser") {
                            $chargeMethodType = "Dashboard";
                        } else if ($payment->charge_method_type == "moveremailsms") {
                            $chargeMethodType = "Email/SMS";
                        } else if ($payment->charge_method_type == "contract") {
                            $chargeMethodType = "Contract";
                        } else if ($payment->charge_method_type == "cardcontract") {
                            $chargeMethodType = "Card Contract";
                        }else if ($payment->charge_method_type == "lowfund") {
                            $chargeMethodType = "Lowfund Email/SMS";
                        }
                        $data['chargeMethod'] = $businessArray[$payment->charge_user_id] . $chargeMethodType;
                    }
                }
                $time = strtotime($payment->created_at);
                $month = date("m", $time);
                $date = date("d", $time);

                $dateTime = strtotime($payment->created_at);
                $hour = date("h", $dateTime);
                $minute = date("i", $dateTime);
                $marid = date("a", $dateTime);
                // $data['date'] = $month . '/' . $date;
                // $data['time'] = $hour . ':' . $minute . ' ' . strtoupper($marid);
                $data['date'] = date("m/d/Y H:i:s", $dateTime);
                $data['transactionId'] = empty($payment->fatt_payment_id) ? '--' : $payment->fatt_payment_id;
                $data['settlementId'] = $settlementIdArray[$payment->fatt_payment_id] ?? '--';
                $data['batchId'] = $batchIdArray[$payment->fatt_payment_id] ?? '--';
                $data['isNotSettlement'] = 0;
                $data['chargeTypeId'] = $payment->charge_type_id;
                if (isset($settlementIdArray[$payment->fatt_payment_id]) && isset($batchIdArray[$payment->fatt_payment_id])) {
                    $data['isNotSettlement'] = 1;
                }
                $data['status'] = ($payment->is_charge_approved == "yes") ? 'Approved' : 'Declined';
                $data['message'] = $messageArray[$payment->business_campaign_payment_id] ?? '--';
                
                $data['cardNumber'] = '';
                if (isset($cardArray[$payment->business_cc_id])) {
                    $data['cardNumber'] = 'XXXX-XXXX-XXXX-' . $cardArray[$payment->business_cc_id];
                }
                if ($payment->campaign_id > 0 && $campaignCategory != 'alldata') {
                    
                    //echo $payment->businesses_campaign_id."==".$campaignCategory."<br>";
                    if ($campaignCategory == $category) {
                        //echo $payment->businesses_campaign_id."==".$campaignCategory."<br>";
                        $finalData[] = $data;
                    }
                } else {
                    //echo $payment->businesses_campaign_id.'<br>';
                    $finalData[] = $data;
                }
            }

            return datatables()->of($finalData)->rawColumns(['businessName', 'campaignName'])->make(true);
        }
    }

    public function paymentHistoryData(Request $request) {
        // Summary Data 1 ( Include debit and credit - temporary updates)
        $chargeType = $request->get('chargeType');
        $campCategory = "alldata";
        $getCategoryCampArr = array();
        if ($request->get('campaignCategory') != "alldata") {
            $campCategory = $request->get('campaignCategory');
            
        }
        
        $sdate = $request->get('startDate');
        $edate = $request->get('endDate');
        $reqBusinessesId = "";
        $reqCampaignIds = [];
        $reqCampaignIdsStr = $appendQry = "";
        if (!empty($request->get('reqBusinessesId'))) {
            $reqBusinessesId = $request->get('reqBusinessesId');
            // Fetch campaignIds from business_id
            $fetchCampaignIds = Campaign::where('business_id', $reqBusinessesId)->get('campaign_id');
            if (count($fetchCampaignIds) > 0) {
                foreach ($fetchCampaignIds as $campId) {
                    $reqCampaignIds[] = $campId->campaign_id;
                }
                if (count($reqCampaignIds) > 0) {
                    $reqCampaignIdsStr = implode(",", $reqCampaignIds);
                    $appendQry = " AND ( business_id = '" . $reqBusinessesId . "' or campaign_id IN (" . $reqCampaignIdsStr . ") )";
                }
            } else {
                $appendQry = " AND ( business_id = '" . $reqBusinessesId . "')";
                $reqCampaignIdsStr = 1000001;
            }
        }
        if ($sdate == "a") {
            $sdate = date("Y-m-d");
            $edate = date("Y-m-d");
        }
        if ($chargeType == 'alldata') {
            $getCategoryCampStr = "";
            if (count($getCategoryCampArr) > 0) {
                $getCategoryCampStr = implode(",", $getCategoryCampArr);
                $queryStr = "SELECT 
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type = 'subscription' AND fatt_payment_id is NULL THEN amount_charge
                END) AS 'CCSCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type = 'subscription' AND fatt_payment_id is NOT NULL THEN amount_charge
                END) AS 'CCssystemCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type = 'subscription' AND fatt_payment_id is NOT NULL THEN convenience_fee
                END) AS 'CCssystemFeeCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type <> 'subscription' AND fatt_payment_id is NULL THEN amount_charge
                END) AS 'CCCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type <> 'subscription' AND fatt_payment_id is NOT NULL THEN amount_charge
                END) AS 'CCsystemCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type <> 'subscription' AND fatt_payment_id is NOT NULL THEN convenience_fee
                END) AS 'CCsystemFeeCr',
                SUM(CASE
                    WHEN charge_type_id  = 7 THEN amount_charge
                END) AS 'RecurrentCr',
                SUM(CASE
                    WHEN charge_type_id  = 8 THEN amount_charge
                END) AS 'BadLeadCr',
                SUM(CASE
                    WHEN charge_type_id  = 9 THEN amount_charge
                END) AS 'OtherCr',
                SUM(CASE
                    WHEN charge_type_id  = 10 THEN amount_charge
                END) AS 'TransferToCr',
                SUM(CASE
                    WHEN charge_type_id  = 3 THEN amount_charge
                END) AS 'OtherDb',
                SUM(CASE
                    WHEN charge_type_id  = 4 THEN amount_charge
                END) AS 'RefundDb',
                SUM(CASE
                    WHEN charge_type_id  = 5 THEN amount_charge
                END) AS 'ChargeBackDb',
                SUM(CASE
                    WHEN charge_type_id  = 6 THEN amount_charge
                END) AS 'TransferFromDb',
                SUM(CASE
                    WHEN charge_type_id  = 2 THEN amount_charge
                END) AS 'BankCr',
                SUM(CASE
                    WHEN charge_type_id  = 11 AND charge_method_type <> 'subscription' THEN amount_charge
                END) AS 'VoidPaymentCr'
            FROM
            business_campaign_payment where if(campaign_id >0,campaign_id in($getCategoryCampStr),'') AND is_charge_approved = 'yes' and created_at between '" . $sdate . " 00:00:00' and '" . $edate . " 23:59:59' " . $appendQry;
            } else {
                $queryStr = "SELECT 
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type = 'subscription' AND fatt_payment_id is NULL THEN amount_charge
                END) AS 'CCSCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type = 'subscription' AND fatt_payment_id is NOT NULL THEN amount_charge
                END) AS 'CCssystemCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type = 'subscription' AND fatt_payment_id is NOT NULL THEN convenience_fee
                END) AS 'CCssystemFeeCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type <> 'subscription' AND fatt_payment_id is NULL THEN amount_charge
                END) AS 'CCCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type <> 'subscription' AND fatt_payment_id is NOT NULL THEN amount_charge
                END) AS 'CCsystemCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type <> 'subscription' AND fatt_payment_id is NOT NULL THEN convenience_fee
                END) AS 'CCsystemFeeCr',
                SUM(CASE
                    WHEN charge_type_id  = 7 THEN amount_charge
                END) AS 'RecurrentCr',
                SUM(CASE
                    WHEN charge_type_id  = 8 THEN amount_charge
                END) AS 'BadLeadCr',
                SUM(CASE
                    WHEN charge_type_id  = 9 THEN amount_charge
                END) AS 'OtherCr',
                SUM(CASE
                    WHEN charge_type_id  = 10 THEN amount_charge
                END) AS 'TransferToCr',
                SUM(CASE
                    WHEN charge_type_id  = 3 THEN amount_charge
                END) AS 'OtherDb',
                SUM(CASE
                    WHEN charge_type_id  = 4 THEN amount_charge
                END) AS 'RefundDb',
                SUM(CASE
                    WHEN charge_type_id  = 5 THEN amount_charge
                END) AS 'ChargeBackDb',
                SUM(CASE
                    WHEN charge_type_id  = 6 THEN amount_charge
                END) AS 'TransferFromDb',
                SUM(CASE
                    WHEN charge_type_id  = 2 THEN amount_charge
                END) AS 'BankCr',
                SUM(CASE
                    WHEN charge_type_id  = 11 AND charge_method_type <> 'subscription' THEN amount_charge
                END) AS 'VoidPaymentCr'
                    FROM
                    business_campaign_payment where is_charge_approved = 'yes' and created_at between '" . $sdate . " 00:00:00' and '" . $edate . " 23:59:59' " . $appendQry;
            }
            //echo $queryStr;die;
            $dataSummary1 = DB::select($queryStr);
        } else {
            $getCategoryCampStr = "";
            if (count($getCategoryCampArr) > 0) {
                $getCategoryCampStr = implode(",", $getCategoryCampArr);
                $queryStr = "SELECT
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type = 'subscription' AND fatt_payment_id is NULL THEN amount_charge
                END) AS 'CCSCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type = 'subscription' AND fatt_payment_id is NOT NULL THEN amount_charge
                END) AS 'CCssystemCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type = 'subscription' AND fatt_payment_id is NOT NULL THEN convenience_fee
                END) AS 'CCssystemFeeCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type <> 'subscription' AND fatt_payment_id is NULL THEN amount_charge
                END) AS 'CCCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type <> 'subscription' AND fatt_payment_id is NOT NULL THEN amount_charge
                END) AS 'CCsystemCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type <> 'subscription' AND fatt_payment_id is NOT NULL THEN convenience_fee
                END) AS 'CCsystemFeeCr',
                SUM(CASE
                    WHEN charge_type_id  = 7 THEN amount_charge
                END) AS 'RecurrentCr',
                SUM(CASE
                    WHEN charge_type_id  = 8 THEN amount_charge
                END) AS 'BadLeadCr',
                SUM(CASE
                    WHEN charge_type_id  = 9 THEN amount_charge
                END) AS 'OtherCr',
                SUM(CASE
                    WHEN charge_type_id  = 10 THEN amount_charge
                END) AS 'TransferToCr',
                SUM(CASE
                    WHEN charge_type_id  = 3 THEN amount_charge
                END) AS 'OtherDb',
                SUM(CASE
                    WHEN charge_type_id  = 4 THEN amount_charge
                END) AS 'RefundDb',
                SUM(CASE
                    WHEN charge_type_id  = 5 THEN amount_charge
                END) AS 'ChargeBackDb',
                SUM(CASE
                    WHEN charge_type_id  = 6 THEN amount_charge
                END) AS 'TransferFromDb',
                SUM(CASE
                    WHEN charge_type_id  = 2 THEN amount_charge
                END) AS 'BankCr',
                SUM(CASE
                    WHEN charge_type_id  = 11 AND charge_method_type <> 'subscription' THEN amount_charge
                END) AS 'VoidPaymentCr'
            FROM
            business_campaign_payment where if(campaign_id >0,campaign_id in($getCategoryCampStr),'') AND (created_at between '" . $sdate . " 00:00:00' and '" . $edate . " 23:59:59') and is_charge_approved = 'yes' and charge_type_id  = " . $chargeType . " " . $appendQry;
            } else {
                $queryStr = "SELECT
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type = 'subscription' AND fatt_payment_id is NULL THEN amount_charge
                END) AS 'CCSCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type = 'subscription' AND fatt_payment_id is NOT NULL THEN amount_charge
                END) AS 'CCssystemCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type = 'subscription' AND fatt_payment_id is NOT NULL THEN convenience_fee
                END) AS 'CCssystemFeeCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type <> 'subscription' AND fatt_payment_id is NULL THEN amount_charge
                END) AS 'CCCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type <> 'subscription' AND fatt_payment_id is NOT NULL THEN amount_charge
                END) AS 'CCsystemCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type <> 'subscription' AND fatt_payment_id is NOT NULL THEN convenience_fee
                END) AS 'CCsystemFeeCr',
                SUM(CASE
                    WHEN charge_type_id  = 7 THEN amount_charge
                END) AS 'RecurrentCr',
                SUM(CASE
                    WHEN charge_type_id  = 8 THEN amount_charge
                END) AS 'BadLeadCr',
                SUM(CASE
                    WHEN charge_type_id  = 9 THEN amount_charge
                END) AS 'OtherCr',
                SUM(CASE
                    WHEN charge_type_id  = 10 THEN amount_charge
                END) AS 'TransferToCr',
                SUM(CASE
                    WHEN charge_type_id  = 3 THEN amount_charge
                END) AS 'OtherDb',
                SUM(CASE
                    WHEN charge_type_id  = 4 THEN amount_charge
                END) AS 'RefundDb',
                SUM(CASE
                    WHEN charge_type_id  = 5 THEN amount_charge
                END) AS 'ChargeBackDb',
                SUM(CASE
                    WHEN charge_type_id  = 6 THEN amount_charge
                END) AS 'TransferFromDb',
                SUM(CASE
                    WHEN charge_type_id  = 2 THEN amount_charge
                END) AS 'BankCr',
                SUM(CASE
                    WHEN charge_type_id  = 11 AND charge_method_type <> 'subscription' THEN amount_charge
                END) AS 'VoidPaymentCr'
            FROM
            business_campaign_payment where (created_at between '" . $sdate . " 00:00:00' and '" . $edate . " 23:59:59') and is_charge_approved = 'yes' and charge_type_id  = " . $chargeType . " " . $appendQry;
            }
            $dataSummary1 = DB::select($queryStr);
        }
        //echo "<pre>";print_r($dataSummary1);die;
        $summaryData = [];
        $allCredit = $allDebit = $allCCCharge = 0;
        foreach ($dataSummary1 as $summary) {
            //echo "<pre>";print_r($dataSummary1);die;
            $summaryData['CCSCr'] = (empty($summary->CCSCr) || $summary->CCSCr == 0) ? '--' : '$' . sprintf('%0.2f', $summary->CCSCr);
            $summaryData['CCssystemC'] = (empty($summary->CCssystemCr) || $summary->CCssystemCr == 0) ? '--' : '$' . sprintf('%0.2f',$summary->CCssystemCr);
            $summaryData['CCssystemCr'] = (empty($summary->CCssystemCr) || $summary->CCssystemCr == 0) ? '--' : '$' . sprintf('%0.2f',$summary->CCssystemCr + $summary->CCssystemFeeCr);
            $summaryData['CCCr'] = (empty($summary->CCCr) || $summary->CCCr == 0) ? '--' : '$' . sprintf('%0.2f', $summary->CCCr);
            $summaryData['CCsystemC'] = (empty($summary->CCsystemCr) || $summary->CCsystemCr == 0) ? '--' : '$' . sprintf('%0.2f',$summary->CCsystemCr);
            $summaryData['CCsystemCr'] = (empty($summary->CCsystemCr) || $summary->CCsystemCr == 0) ? '--' : '$' . sprintf('%0.2f',$summary->CCsystemCr + $summary->CCsystemFeeCr);
            $summaryData['RecurrentCr'] = (empty($summary->RecurrentCr) || $summary->RecurrentCr == 0) ? '--' : '$' . sprintf('%0.2f', $summary->RecurrentCr);
            $summaryData['BadLeadCr'] = (empty($summary->BadLeadCr) || $summary->BadLeadCr == 0) ? '--' : '$' . sprintf('%0.2f', $summary->BadLeadCr);
            $summaryData['OtherCr'] = (empty($summary->OtherCr) || $summary->OtherCr == 0) ? '--' : '$' . sprintf('%0.2f', $summary->OtherCr);
            $summaryData['TransferToCr'] = (empty($summary->TransferToCr) || $summary->TransferToCr == 0) ? '--' : '$' . sprintf('%0.2f', $summary->TransferToCr);
            $summaryData['OtherDb'] = (empty($summary->OtherDb) || $summary->OtherDb == 0) ? '--' : '$' . sprintf('%0.2f', $summary->OtherDb);
            $summaryData['RefundDb'] = (empty($summary->RefundDb) || $summary->RefundDb == 0) ? '--' : '$' . sprintf('%0.2f', $summary->RefundDb);
            $summaryData['ChargeBackDb'] = (empty($summary->ChargeBackDb) || $summary->ChargeBackDb == 0) ? '--' : '$' . sprintf('%0.2f', $summary->ChargeBackDb);
            $summaryData['TransferFromDb'] = (empty($summary->TransferFromDb) || $summary->TransferFromDb == 0) ? '--' : '$' . sprintf('%0.2f', $summary->TransferFromDb);
            $summaryData['BankCr'] = (empty($summary->BankCr) || $summary->BankCr == 0) ? '--' : '$' . sprintf('%0.2f', $summary->BankCr);
            $summaryData['VoidPaymentCr'] = (empty($summary->VoidPaymentCr) || $summary->VoidPaymentCr == 0) ? '--' : '$' . sprintf('%0.2f', $summary->VoidPaymentCr);
            $allCredit = $summary->CCCr + $summary->CCsystemCr + $summary->RecurrentCr + $summary->BadLeadCr + $summary->OtherCr + $summary->TransferToCr + $summary->BankCr;
            $allDebit = $summary->OtherDb + $summary->RefundDb + $summary->ChargeBackDb + $summary->TransferFromDb + $summary->VoidPaymentCr;
            $allCCCharge = $summary->CCCr + $summary->CCsystemCr + $summary->CCsystemFeeCr - ($summary->RefundDb + $summary->VoidPaymentCr );
        }
        // Summary Data 2
        $categoryQuery = "";
        if (!empty($request->get('campCategory'))) {
            if ($campCategory != 'alldata') {
                $categoryQuery = " AND category='" . $campCategory . "'";
            }
        }
        //Added BY HJ On 18-08-2023 For Orgnaic Transfer Payout Subtract from total sold lead Payout Start
        $totalOrganicTransStr = "SELECT SUM(ltc.payout) as payout FROM lead_transfer_call AS ltc JOIN `lead` AS l ON l.lead_id = ltc.lead_id WHERE ltc.payout > 0 and ltc.campaign_id in (select campaign_id from campaign where campaign_type_id = 4) AND (ltc.created_at between '$sdate 00:00:00' and '$edate 23:59:59') AND ltc.campaign_id in ($reqCampaignIdsStr)";  
        $organicPayout = DB::select($totalOrganicTransStr);
        //Added BY HJ On 18-08-2023 For Orgnaic Transfer Payout Subtract from total sold lead Payout End
        $totalLeadStr = "SELECT sum(lr.payout) payout FROM lead_routing AS lr  where lr.route_status = 'sold' and lr.payout > 0 and ( lr.created_at between '" . $sdate . " 00:00:00' and '" . $edate . " 23:59:59' ) and lr.campaign_id in (select campaign_id from campaign where lead_type_id = 1)"; 
        //echo $totalLeadStr;die;
        // $totalOutBoundStr = "SELECT SUM(aa.pay) as outpayout FROM (SELECT lr.lead_id AS lid, avg(lr.payout) AS pay FROM lead_routing AS lr INNER JOIN (SELECT lead_id FROM lead_call WHERE call_type = 'outbound' GROUP BY lead_id) as lc ON lr.lead_id = lc.lead_id where lr.route_status = 'sold' and lr.payout > 0 AND ( lr.created_at between '$sdate 00:00:00' and '$edate 23:59:59' ) AND lr.campaign_id in ($reqCampaignIdsStr) GROUP BY lr.lead_id, lr.campaign_id) AS aa";

        $totalOutBoundStr = "SELECT SUM(ltc.payout) as totaloutpayout FROM lead_transfer_call AS ltc JOIN `lead` AS l ON l.lead_id = ltc.lead_id WHERE ltc.calls_type = 'outbound' AND ltc.payout > 0 AND (ltc.created_at between '$sdate 00:00:00' and '$edate 23:59:59') AND ltc.campaign_id in ($reqCampaignIdsStr)";
        // $totalInBoundStr = "SELECT SUM(aa.pay) as inpayout FROM (SELECT lr.lead_id AS lid, avg(lr.payout) AS pay FROM lead_routing AS lr INNER JOIN (SELECT lead_id FROM lead_call WHERE call_type = 'inbound' GROUP BY lead_id) as lc ON lr.lead_id = lc.lead_id where lr.route_status = 'sold' and lr.payout > 0 AND ( lr.created_at between '$sdate 00:00:00' and '$edate 23:59:59' ) AND lr.campaign_id in ($reqCampaignIdsStr) GROUP BY lr.lead_id, lr.campaign_id) AS aa";  
        //$totalInBoundStr = "select sum(payout) payout from lead_call where payout >0 and (created_at between '" . $sdate . " 00:00:00' and '" . $edate . " 23:59:59')";
        $totalInBoundStr = "SELECT SUM(ltc.payout) as totalinpayout FROM lead_transfer_call AS ltc JOIN `lead` AS l ON l.lead_id = ltc.lead_id WHERE ltc.calls_type = 'inbound' AND ltc.payout > 0 AND (ltc.created_at between '$sdate 00:00:00' and '$edate 23:59:59') AND ltc.campaign_id in ($reqCampaignIdsStr)";
        if (!empty($reqCampaignIdsStr)) {
            $totalLeadStr .= " and lr.campaign_id in (" . $reqCampaignIdsStr . ")";
            //$totalOutBoundStr .= " and lr.campaign_id in (" . $reqCampaignIdsStr . ")";
            //$totalInBoundStr .= " and lr.campaign_id in (" . $reqCampaignIdsStr . ")";
        }
        //echo $totalLeadStr;die;
        $dataSummary2 = DB::select($totalLeadStr);
        foreach ($dataSummary2 as $summary) {
            if(count($organicPayout) > 0 && $summary->payout > 0){
                $summary->payout -= $organicPayout[0]->payout;
            }
            $summaryData['leads'] = (empty($summary->payout) || $summary->payout == 0) ? '--' : '$' . sprintf('%0.2f', $summary->payout);
            $allDebit += $summary->payout;
        }
        // total inbound call
        //$totalInBoundStr .= " AND lc.lead_id IS NOT NULL";
        //echo $totalInBoundStr; die;
        $dataSummary3 = DB::select($totalInBoundStr);
        foreach ($dataSummary3 as $summary) {
            $summaryData['inbound'] = (empty($summary->totalinpayout) || $summary->totalinpayout == 0) ? '--' : '$' . sprintf('%0.2f', $summary->totalinpayout);
            $allDebit += $summary->totalinpayout;
        }

        // total outbound call
        //$totalOutBoundStr .= " AND lc.lead_id IS NOT NULL";
        //echo $totalOutBoundStr; die;
        $dataSummary4 = DB::select($totalOutBoundStr);
        foreach ($dataSummary4 as $summary) {
            $summaryData['outbound'] = (empty($summary->totaloutpayout) || $summary->totaloutpayout == 0) ? '--' : '$' . sprintf('%0.2f', $summary->totaloutpayout);
            $allDebit += $summary->totaloutpayout;
        }

        $summaryData['allCredit'] = (empty($allCredit) || $allCredit == 0) ? '--' : '$' . sprintf('%0.2f', $allCredit);
        $summaryData['allDebit'] = (empty($allDebit) || $allDebit == 0) ? '--' : '$' . sprintf('%0.2f', $allDebit);
        $summaryData['allCCCharge'] = (empty($allCCCharge) || $allCCCharge == 0) ? '--' : '$' . sprintf('%0.2f', $allCCCharge);
        $summaryData['allCredit_val'] = sprintf('%0.2f', $allCredit);
        $summaryData['allDebit_val'] = sprintf('%0.2f', $allDebit);
        if ($reqBusinessesId) {
            $all_campaign_id = Campaign::where('business_id', $reqBusinessesId)->pluck('campaign_id');

            $opeing_blanace = BusinessesCampaignEomBalance::where('date', $sdate)->where(function ($q) use ($reqBusinessesId, $all_campaign_id) {
                        $q->where('business_id', $reqBusinessesId); // ->orWhereIn('businesses_campaign_id', $all_campaign_id)
                    })->sum('balance');

            $next_day = date('Y-m-d', strtotime('+1 day', strtotime($edate)));
            $ending_blanace = BusinessesCampaignEomBalance::where('date', $next_day)->where(function ($q) use ($reqBusinessesId, $all_campaign_id) {
                        $q->where('business_id', $reqBusinessesId); // ->orWhereIn('businesses_campaign_id', $all_campaign_id)
                    })->sum('balance');

            $summaryData['opening_balance'] = (empty($opeing_blanace) || $opeing_blanace == 0) ? '--' : '$' . sprintf('%0.2f', $opeing_blanace);
            $summaryData['ending_balance'] = (empty($ending_blanace) || $ending_blanace == 0) ? '--' : '$' . sprintf('%0.2f', $ending_blanace);
            $summaryData['opening_balance_val'] = sprintf('%0.2f', $opeing_blanace);
            $summaryData['ending_balance_val'] = sprintf('%0.2f', $ending_blanace);
        } else {
            $busall = Business::pluck('business_id')->toArray();
            if (!empty($request->get('campCategory'))) {
                if ($campCategory == 'alldata') {
                    $all_campaign_id = Campaign::whereIn('business_id', $busall)->pluck('campaign_id');
                } else {
                    $all_campaign_id = Campaign::whereIn('business_id', $busall)->where('lead_type_id', $campCategory)->pluck('campaign_id');
                }
            }else{
                $all_campaign_id = Campaign::whereIn('business_id', $busall)->where('lead_type_id', $campCategory)->pluck('campaign_id');
            }
            
            $opeing_blanace = BusinessesCampaignEomBalance::where('date', $sdate)->where(function ($q) use ($busall, $all_campaign_id) {
                        $q->whereIn('business_id', $busall); // ->orWhereIn('businesses_campaign_id', $all_campaign_id)
                    })->sum('balance');

            $next_day = date('Y-m-d', strtotime('+1 day', strtotime($edate)));
            $ending_blanace = BusinessesCampaignEomBalance::where('date', $next_day)->where(function ($q) use ($busall, $all_campaign_id) {
                        $q->whereIn('business_id', $busall); // ->orWhereIn('businesses_campaign_id', $all_campaign_id)
                    })->sum('balance');

            $summaryData['opening_balance'] = (empty($opeing_blanace) || $opeing_blanace == 0) ? '--' : '$' . sprintf('%0.2f', $opeing_blanace);
            $summaryData['ending_balance'] = (empty($ending_blanace) || $ending_blanace == 0) ? '--' : '$' . sprintf('%0.2f', $ending_blanace);
            $summaryData['opening_balance_val'] = sprintf('%0.2f', $opeing_blanace);
            $summaryData['ending_balance_val'] = sprintf('%0.2f', $ending_blanace);
        }
        $summaryData['diff_balance_val'] = sprintf('%0.2f', ($summaryData['opening_balance_val'] + $summaryData['allCredit_val']) - $summaryData['allDebit_val']);


        /*$total_Camp_balance = $total_Camp_balance1 = 0;
        $businessesRecords = Business::where('business_id',$reqBusinessesId)->get();
        $businessIdArr = $businessCreditArr = array();
        foreach ($businessesRecords as $key1 => $value1) {
            $businessIdArr[] = $value1->business_id;
        }
        //echo "<pre>";print_r($businessIdArr);die;
        if ($campCategory == 'alldata') {
            $campData = Campaign::whereIn('business_id', $businessIdArr)->get(['business_id', 'credit_available', 'credit_reserved'])->toArray();
        } else {
            $campData = Campaign::whereIn('business_id', $businessIdArr)->where('lead_category_id', $campCategory)->get(['business_id', 'credit_available', 'credit_reserved'])->toArray();
        }
        //echo "<pre>";print_r($campData);die;
        for ($g = 0; $g < count($campData); $g++) {
            if (isset($businessCreditArr[$campData[$g]['business_id']]['credit_available'])) {
                $businessCreditArr[$campData[$g]['business_id']]['credit_available'] += $campData[$g]['credit_available'];
            } else {
                $businessCreditArr[$campData[$g]['business_id']]['credit_available'] = $campData[$g]['credit_available'];
            }
            if (isset($businessCreditArr[$campData[$g]['business_id']]['credit_reserved'])) {
                $businessCreditArr[$campData[$g]['business_id']]['credit_reserved'] += $campData[$g]['credit_reserved'];
            } else {
                $businessCreditArr[$campData[$g]['business_id']]['credit_reserved'] = $campData[$g]['credit_reserved'];
            }
        }
        //echo "<pre>";print_r($businessCreditArr);die;
        foreach ($businessesRecords as $key => $value) {
            $current_campaign_balance = $current_campaign_balance1 = 0;
            if (isset($businessCreditArr[$value->id]['credit_available'])) {
                $current_campaign_balance = $businessCreditArr[$value->id]['credit_available'];
            }
            if (isset($businessCreditArr[$value->id]['credit_reserved'])) {
                $current_campaign_balance1 = $businessCreditArr[$value->id]['credit_reserved'];
            }
            $total_Camp_balance += $current_campaign_balance + $value->credit_available;
            $total_Camp_balance1 += $current_campaign_balance1 + $value->credit_reserved;
        }
        $current_balance = $total_Camp_balance;
        $current_balance_new = $total_Camp_balance1 + $current_balance;
        $summaryData['current_balance_new'] = '$' . sprintf('%0.2f', $current_balance_new);*/
        //Added By HJ On 14-07-2023 For Get Campaign and Business Current Balance Start
        $getCampaignBalance = Campaign::select(DB::raw("SUM(credit_available) as camp_balance"))->where('business_id', $reqBusinessesId)->get()->toArray();
        $getBusinessBalance = Business::select(DB::raw("SUM(credit_available) as bus_balance"))->where('business_id', $reqBusinessesId)->get()->toArray();
        $campCurrentBal = $busCurrentBal = 0;
        if(count($getCampaignBalance) > 0){
            $campCurrentBal = $getCampaignBalance[0]['camp_balance'];
        }
        if(count($getBusinessBalance) > 0){
            $busCurrentBal = $getBusinessBalance[0]['bus_balance'];
        }
        $summaryData['current_balance_new'] = '$' . $this->setTwoDecimalPoint($campCurrentBal+$busCurrentBal);
        //Added By HJ On 14-07-2023 For Get Campaign and Business Current Balance End

        return response()->json($summaryData);
    }
    public function setTwoDecimalPoint($value) {
        return number_format($value, 2, '.', ',');
    }
}
