<?php

namespace App\Http\Controllers\Seller;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Master\MstZipcode;
use App\Models\Master\MstAPIToken;
use App\Models\Lead\LeadMoving;
use App\Models\Logic\LogicDuplicateLead;
use App\Models\Seller\PPSeller;
use App\Models\Seller\PPPing;
use App\Models\Seller\PPPost;
use App\Models\Seller\PPLogs;
use App\Models\Seller\PPMatchedCompany;
use App\Http\Controllers\API\Lead\LeadInsert;
use Exception;
use Auth;
use DB;
use Helper;
use DateTime;

class PostSubmitNewController extends Controller
{
    public function index()
    {
        $error = $moveSize = $moveDate = $ipaddress = $browser = $campaignIdString = "";
        $status = "Rejected";
        $postId = $logsId = $buyerPayout = $leadPayout = $leadId = $soldSlot = 0;
        $campaignIds = $pmSoldcampaignIds = array();
        $dateTimeCurrent = date('Y-m-d H:i:s');
        try {
            $elements = json_decode(file_get_contents('php://input'), true);
            $logsId = PPLogs::create([
                "request"       => json_encode($elements),
                "type"          => 1,
                "request_time"  => date('Y-m-d H:i:s')
            ])->id;

            //api_key validation
            $apiKey = $elements['api_key'] ?? '';
            $pingId = $elements['ping_id'] ?? '';
            $pingObj = new PingSubmitController();
            $sellerArray = $pingObj->checkSellerApiKey(trim($apiKey));
            if ($sellerArray['seller_id'] == 0) {
                throw new Exception("Unauthorized");
            }
            $sellerId = $sellerArray['seller_id'];
            $sellerDetail = PPSeller::where('seller_id', $sellerId)->first();

            //api_key validation
            $pingDetail = PPPing::where('ping_id', $pingId)->where('seller_id', $sellerId)->first();
            if (empty($pingDetail)) {
                throw new Exception("Ping not available");
            }
            $dtStime = strtotime($pingDetail->created_at);
            $dtEtime = strtotime($dateTimeCurrent);
            $diff = abs($dtStime-$dtEtime); //get seconds diff
            //echo $diff; die;
            if ($diff > 60) {
                throw new Exception("Ping request expired");
            }

            $soldType = $elements['sold_type'] ?? '';
            if (!isset($soldType) && empty($soldType)) {
                throw new Exception("Invalid Sold Type");
            }
            $firstName = $elements['firstname'] ?? '';
            $lastName = $elements['lastname'] ?? '';
            $email = $elements['email'] ?? '';
            $phone = $elements['phone'] ?? '';
            //is_verified
            $isVerified = $elements['is_verified'];
            //from_zip
            $fromZip = $elements['from_zip'];
            $checkFromZip = MstZipcode::where("zipcode", $fromZip)->count();
            if ($checkFromZip == 0) {
                throw new Exception("Invalid From Zip");
            }
            //to_zip
            $toZip = $elements['to_zip'];
            $checkToZip = MstZipcode::where("zipcode", $toZip)->count();
            if ($checkToZip == 0) {
                throw new Exception("Invalid To Zip");
            }
            //is_verified
            $isVerified = $elements['is_verified'] ?? '';
            $verificationMethod = $elements['verification_method'];
            $ipaddress = $elements['ip'] ?? '';
            $browser = $elements['browser'] ?? '';

            //move_size validation
            $moveSize = preg_replace("/\D/", "", trim($elements['move_size']));
            $moveSize = trim($elements['move_size']);
            //move_date
            $moveDate = LeadMoving::getMovedate($elements['move_date']);

            if ($fromZip != $pingDetail->from_zip || $toZip != $pingDetail->to_zip || $moveSize != $pingDetail->move_size || $moveDate != date('m/d/Y', strtotime($pingDetail->move_date)) || $isVerified != $pingDetail->is_verified || $verificationMethod != $pingDetail->verified_method) {
                throw new Exception("Post data does not match with ping");
            }

            if (strpos($email, '@test.com') !== false) {
                throw new Exception("Invalid email");
            }

            /*if ($isVerified == "yes" || $isVerified == "1" || $isVerified == 1) {
                $isVerified = 'yes';
            } else {
                $isVerified = 'no';
            }*/

            $logicDpSource = new LogicDuplicateLead();
            $checkDuplicateSlot = $logicDpSource->checkDuplicate($email, $phone, $leadId, $dateTimeCurrent, $sellerDetail->source_name);
            if(count($checkDuplicateSlot) > 0 )
            {
                if($checkDuplicateSlot[0] == 1)
                {
                    if($checkDuplicateSlot[1] == 4)
                    {
                        throw new Exception("Duplicate Lead in 48 hours with all slot sold");
                    }
                }
            }

            $tokenDetail = MstAPIToken::where('source', $sellerDetail->source_name)->first();
            $checkToken = DB::select("SELECT api_token_id, SUBSTR(source, 1) AS source, token FROM mst_api_token WHERE token = '".$tokenDetail->token."'");
            if (empty($checkToken)) {
                throw new Exception("Unauthorized");
            }

            if($soldType == 'shared') {
                $campaignIds        = PPMatchedCompany::where('ping_id', $pingId)->where('lead_type', 'shared')->pluck('pm_sold_company_id')->toArray();
                $buyerPayout        = $pingDetail->shared_buyer_amount;
                $leadPayout         = $pingDetail->shared_payout;
                $pmSoldcampaignIds  = PPMatchedCompany::where('ping_id', $pingId)->where('lead_type', 'shared')->pluck('payout', 'pm_sold_company_id')->toArray();
            } else if($soldType == 'dual') {
                $campaignIds        = PPMatchedCompany::where('ping_id', $pingId)->where('lead_type', 'dual')->pluck('pm_sold_company_id')->toArray();
                $buyerPayout        = $pingDetail->dual_buyer_amount;
                $leadPayout         = $pingDetail->dual_payout;
                $pmSoldcampaignIds  = PPMatchedCompany::where('ping_id', $pingId)->where('lead_type', 'dual')->pluck('payout', 'pm_sold_company_id')->toArray();
            } else if($soldType == 'trio') {
                $campaignIds        = PPMatchedCompany::where('ping_id', $pingId)->where('lead_type', 'trio')->pluck('pm_sold_company_id')->toArray();
                $buyerPayout        = $pingDetail->trio_buyer_amount;
                $leadPayout         = $pingDetail->trio_payout;
                $pmSoldcampaignIds  = PPMatchedCompany::where('ping_id', $pingId)->where('lead_type', 'trio')->pluck('payout', 'pm_sold_company_id')->toArray();
            } else {
                $campaignIds        = PPMatchedCompany::where('ping_id', $pingId)->where('lead_type', 'exclusive')->pluck('pm_sold_company_id')->toArray();
                $buyerPayout        = $pingDetail->exclusive_buyer_amount;
                $leadPayout         = $pingDetail->exclusive_payout;
                $pmSoldcampaignIds  = PPMatchedCompany::where('ping_id', $pingId)->where('lead_type','exclusive')->pluck('payout', 'pm_sold_company_id')->toArray();
            }

            if (count($campaignIds) > 0) {
                $campaignIdString   = implode(",", $campaignIds);
            }

            $sourceName             = $sellerDetail->source_name;
            if (strpos($sellerDetail->source_name, 'NEX') !== false) {
                if ($soldType == 'shared') {
                    $sourceName     = $sellerDetail->source_name . '(SH)';
                } else {
                    $sourceName     = $sellerDetail->source_name . '(Ex)';
                }
            }

            $leadData = array(
                "token"             => $tokenDetail->token,
                "origin"            => $sourceName,
                "utm_campaign"      => "",
                "utm_medium"        => "",
                "campaign_id"       => 0,
                "ad_group_id"       => "",
                "ad_group"          => 0,
                "keyword"           => "",
                "device"            => "",
                "GCLID"             => "",
                "Landing_Page"      => "",
                "from_zip"          => $fromZip,
                "to_zip"            => $toZip,
                "move_date"         => date('Y-m-d', strtotime($moveDate)),
                "move_size"         => $moveSize,
                "name"              => $firstName.' '.$lastName,
                "email"             => $email,
                "phone"             => $phone,
                "is_verified"       => (string)$isVerified,
                "IP"                => $ipaddress,
                "generated_at"      => date("Y-m-d H:i:s"),
                "is_ping_post"      => 1,
                "pp_campaign_id"    => $campaignIdString,
                "lead_id"           => 0
            );

            $leadInsertObj          = new LeadInsert;
            $response               = $leadInsertObj->InsertLead(json_encode($leadData));
            $response               = json_decode($response, true);
            //print_r($response); die;
            $error                  = $response['error'];
            $leadId                 = $response['lead_id'];

            $status                 = "Accepted";
            $soldSlot               = count($campaignIds);
            //echo $leadPayout; die;
            if (!empty($error)) {
                $status             = "Rejected";
                $leadPayout         = 0;
                $buyerPayout        = 0;
                $soldSlot           = 0;
                $pmSoldcampaignIds  = [];
            } else {
                PPMatchedCompany::whereIn('pm_sold_company_id', $campaignIds)->where('lead_type', $soldType)->update([
                    "is_win"        => "yes"
                ]);
            }

            $postId = PPPost::create([
                "api_key"           => $apiKey,
                "seller_id"         => $sellerId,
                "ping_id"           => $pingId,
                "lead_id"           => $leadId,
                "fname"             => $firstName,
                "lname"             => $lastName,
                "email"             => $email,
                "phone"             => $phone,
                "from_zip"          => $fromZip,
                "to_zip"            => $toZip,
                "move_size"         => $moveSize,
                "move_date"         => date('Y-m-d', strtotime($moveDate)),
                "buyer_amount"      => $buyerPayout,
                "payout"            => $leadPayout,
                "result"            => $status,
                "fail_reason"       => $error,
                "is_verified"       => (string)$isVerified,
                "verified_method"   => $verificationMethod,
                "created_at"        => date('Y-m-d H:i:s'),
            ])->id;

        } catch (Exception $e) {
            $error = $e->getMessage();
        }

        $responseArray = [
            'status'                => $status,
            'post_id'               => $postId,
            'lead_id'               => $leadId,
            'pm_sold_company_id'    => $pmSoldcampaignIds,
            'sold_slot'             => $soldSlot,
            'payout'                => $buyerPayout,
            'error'                 => $error
        ];

        PPLogs::where('logs_id', $logsId)->update([
            "type_id"               => $postId,
            "response"              => json_encode($responseArray),
            "response_time"         => date('Y-m-d H:i:s'),
            "ip"                    => $ipaddress,
            "browser"               => $browser
        ]);
        return response()->json($responseArray);
    }
}
