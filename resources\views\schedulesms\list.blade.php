@section('title', 'Schedule SMS List')
@section('label', 'Schedule SMS List')
@section('url', 'schedulesms-list')
@include('header.header')
@include('navigation.navigation')
@include('top.top')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
<div class="page-wrapper">
    <!-- Page Content-->
    <div class="page-content-tab">
        <div class="container-fluid">
            <!-- Page-Title -->
            <div class="row">
                <div class="col-sm-12">
                    <div class="page-title-box">
                        <div class="float-end">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">Setting Module</li>
                                <li class="breadcrumb-item">Schedule SMS</li>
                                <li class="breadcrumb-item active">Schedule SMS List</li>
                            </ol>
                        </div>
                        <h4 class="page-title">Schedule SMS List</h4>
                    </div>
                    <!--end page-title-box-->
                </div>
                <!--end col-->
            </div>
            <!-- end page title end breadcrumb -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            @if ($message = Session::get('message'))
                                <div class="alert alert-success border-0" role="alert">
                                    <strong>Well done!</strong> {{ $message }}
                                </div>
                            @endif
                            {{--<div class="row">
                                <div class="col mb-3">
                                    <a href="javascript: void (0)">
                                        <button type="button" class="btn btn-primary addScheduleSMS"><i class="mdi mdi-plus"></i>Add New</button>
                                    </a>
                                </div>
                                <!--end col-->
                            </div>--}}
                            <div class="table-responsive">
                                <div id="datatable-1"></div>
                            </div>
                        </div>
                    </div>
                </div> <!-- end col -->
            </div> <!-- end row -->
        </div><!-- container -->
        <!--Start Rightbar-->
        <!--Start Rightbar/offcanvas-->
    @include('setting.setting')
    <!--end Rightbar/offcanvas-->
        <!--end Rightbar-->
        <!--Start Footer-->
        <!-- Footer Start -->
    @include('footer.footer')
    <!-- end Footer -->
        <!--end footer-->
    </div>
    <!-- end page content -->
</div>
<!-- end page-wrapper -->
<!-- App js -->
<script src="{{ asset('assets/src/jquery.min.js') }}"></script>
<script src="{{ asset('assets/js/app.js') }}"></script>
<!-- Sweet-Alert  -->
<script src="{{ asset('assets/plugins/sweet-alert2/sweetalert2.min.js') }}"></script>
<script src="{{ asset('assets/pages/sweet-alert.init.js') }}"></script>
<!-- Javascript  -->
<script src="{{ asset('assets/plugins/tabulator/tabulator.min.js') }}"></script>
<script type="text/javascript" src="https://oss.sheetjs.com/sheetjs/xlsx.full.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>
<script>
    //define data array
    var tabledata = JSON.parse('<?= json_encode($scheduleSMSArray) ?>');
    //tabledata
    var table = new Tabulator("#datatable-1", {
        placeholder: "No Data Available",
        data: tabledata, //load row data from array
        layout: "fitColumns", //fit columns to width of table
        //autoColumns:true, //create columns from data field names
        // responsiveLayout: "collapse", //hide columns that dont fit on the table
        tooltips: true, //show tool tips on cells
        addRowPos: "top", //when adding a new row, add it to the top of the table
        history: true, //allow undo and redo actions on the table
        pagination: "local", //paginate the data
        paginationSize: 50, //allow 7 rows per page of data
        paginationCounter: "rows", //display count of paginated rows in footer
        paginationSizeSelector: fileResponse.sizeArray, //enable page size select element with these options
        //movableColumns: true, //allow column order to be changed
        fitColumns: true,
        resizableRows: true, //allow row order to be changed
        paginationSizeSelector: fileResponse.sizeArray, //enable page size select element with these options
        initialSort: [ //set the initial sort order of the data
            {
                column: "id",
                dir: "desc"
            }
        ],

        columnDefaults: {
            tooltip: true //show tool tips on cells
        },
        columns: [ //define the table columns
            {
                title: "ID",
                field: "schedule_sms_id",
                width: '5%',
                headerFilter: false,
                headerTooltip: true,
                sorter: function(a, b) {
                    return a - b;
                }
            },
            {
                title: "Subject",
                field: "sms_subject",
                width: '30%',
                headerFilter: false,
                headerTooltip: true,
            },
            {
                title: "Campaign",
                field: "campaign_id",
                width: '45%',
                headerFilter: false,
                headerTooltip: true,
                formatter: function(cell) {
                    return String(cell.getValue()).split(",").join("<br>")
                } ,
                formatterParams: { allowHtml: true }
            },
            {
                title: "Created Date",
                field: "created_at",
                width: '10%',
                headerFilter: false,
                headerTooltip: true,
                sorterParams:{
                    format:"hh:mm A",
                    alignEmptyValues:"top",
                }
            },
            {
                title: "<div class='text-center'>Actions</div>",
                field: "action",
                headerSort: false,
                headerTooltip: true,
                download:false,
                tooltip: false,
                width: '10%',
                formatter: function(row) {
                    var data = row.getData(); //get data object for row
                    var value = data.schedule_sms_id;
                    var actionHtml = "";
                    actionHtml += "<a href='javascript: void (0)' onclick='editScheduleSMS(" + value + ")'>";
                    actionHtml += "<div class='tooltip4'>";
                    actionHtml += "<i class='las la-pen text-secondary font-16 hover_i'></i>";
                    actionHtml += "<span class='tooltiptext4'>Edit</span>";
                    actionHtml += "</div>";
                    actionHtml += "</a>";
                    actionHtml += "<a href='javascript:void(0)' onclick='previewScheduleSMS(" + value + ")'>";
                    actionHtml += "<div class='tooltip4'>";
                    actionHtml += "<i class='las la-eye text-secondary font-16 hover_i'></i>";
                    actionHtml += "<span class='tooltiptext4'>Preview</span>";
                    actionHtml += "</div>";
                    actionHtml += "</a>";
                    return "<div class='text-center'>" + actionHtml+ "</div>";
                }
            }
        ]
    });

    $('body').on('click', '.addScheduleSMS', function(e){
        $(".smsSubjectError").text('');
        $(".campaignIdsError").text('');
        $('#addScheduleSMSModal').modal('show');

        /*setTimeout(function () {
            $("#campaignIds").select2({
                placeholder: "Select an option",
                allowClear: true
            });
        }, 100)*/
    });

    function editScheduleSMS(scheduleSmsId) {
        $(".smsSubjectError").text('');
        $(".campaignIdsError").text('');
        //$('#editScheduleSMSModal').modal('show');

        $.ajax({
            type:'POST',
            url:'{{ url("schedulesms-edit")}}',
            data:{ scheduleSmsId },
            success:function(response){
                if(response.message == 'Success') {
                    $('#txtCard').empty();
                    $('#editScheduleSMSModal').modal('show');
                    var html = '';
                    for (var key in response.data[0].campaignId){
                        /*console.log(response.data[0].campaignId[key])*/
                        html += '<div class="non' + response.data[0].campaignId[key] + '" > <div class="textDiv">'  + response.data[0].campaignId[key] + " " + response.campaignArray[response.data[0].campaignId[key]] + '<button name="delete" type="button" value="delete" onclick="deleteSelectedCampaignIds(' + response.data[0].campaignId[key] + ');"><i class="ti ti-x"></i></button> <input type="hidden" value="' + response.data[0].campaignId[key] + '" name="toggleCampaignIds[]"> </div> </div>'
                    }
                    $("#scheduleSmsId").val(response.data[0].scheduleSmsId);
                    $("#esmsSubject").val(response.data[0].smsSubject);
                    $(".eselectedCampaignIds").html(html);
                }
            }
        });
    }

    function deleteScheduleSMS(elem) {
        var scheduleSmsId = $(elem).attr('data-id');
        Swal.fire({
            title: 'Are you sure?',
            text: "Deleted data will not be restored!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            customClass: {
                confirmButton: 'btn btn-primary',
                cancelButton: 'btn btn-outline-danger ml-1'
            },
            buttonsStyling: true
        }).then(function(result) {
            if (result.value) {
                $.ajax({
                    type: 'post',
                    url: "{{ url('schedulesms-delete') }}",
                    data: {
                        "scheduleSmsId": scheduleSmsId,
                        "_token": "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        if (response.status == 1) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Deleted!',
                                text: response.message,
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            }).then(({
                                value
                            }) => {
                                location.reload(true);
                            });
                        } else {
                            Swal.fire({
                                title: 'Cancelled',
                                text: 'Error while delete this record:)',
                                icon: 'error',
                                customClass: {
                                    confirmButton: 'btn btn-success'
                                }
                            });
                        }
                    }
                });
            } else if (result.dismiss === Swal.DismissReason.cancel) {
                Swal.fire({
                    title: 'Cancelled',
                    text: 'This is safe :)',
                    icon: 'error',
                    customClass: {
                        confirmButton: 'btn btn-success'
                    }
                });
            }
        });
    }

    function previewScheduleSMS(scheduleSmsId) {
        $.ajax({
            type:'POST',
            url:'{{ url("schedulesms-preview")}}',
            data:{ scheduleSmsId },
            success:function(response){
                /*alert(response.data);*/
                if(response.message == 'Success') {
                    $('#previewScheduleSMSModal').modal('show');
                    $("#requestSubject").html(response.subject);
                    $("#requestLog").html(response.data);
                }
            }
        });
    }

    $(document).ready(function(){
        $.ajaxSetup({
            headers:{
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $("#addScheduleSMS").click(function(){
            $('.loader').show();
            $('#addScheduleSMS').attr("disabled", "disabled");

            $(".smsSubjectError").text('');
            $(".campaignIdsError").text('');
            var smsSubject = $("#smsSubject").val();
            var campaignIds = $('input[name="toggleCampaignIds[]"]').map(function() {
                return $(this).val();  // Return the value of each hidden input
            }).get();
            $.ajax({
                type:'POST',
                url:'{{ url("schedulesms-insert") }}',
                data:{ smsSubject, campaignIds },
                success:function(response){
                    $('#addScheduleSMS').removeAttr("disabled", "disabled");
                    $('.loader').hide();
                    if(response.message == 'Success') {
                        Swal.fire({
                            icon: 'success',
                            title: 'Added!',
                            text: "Schedule SMS added successfully",
                            customClass: {
                                confirmButton: 'btn btn-success'
                            }
                        }).then(({value}) => {
                            location.reload(true);
                        });
                    } else {
                        $.each( response.error, function( key, value ) {
                            if(key){
                                $("[name="+key+"]").next().show().find(".error_text").text(value[0]);
                            }
                            if (key == 'campaignIds') {
                                $("#campaignIds").next().show().find(".error_text").text(value[0]);
                            }
                        });
                    }
                }
            });
        });

        $("#editScheduleSMS").click(function(){
            $('.loader').show();
            $('#editScheduleSMS').attr("disabled", "disabled");

            $(".smsSubjectError").text('');
            $(".campaignIdsError").text('');
            var scheduleSmsId = $("#scheduleSmsId").val();
            var smsSubject = $("#esmsSubject").val();
            var campaignIds = $('input[name="toggleCampaignIds[]"]').map(function() {
                return $(this).val();  // Return the value of each hidden input
            }).get();
            $.ajax({
                type:'POST',
                url:'{{ url("schedulesms-update") }}',
                data:{ scheduleSmsId, smsSubject, campaignIds },
                success:function(response){
                    $('#editScheduleSMS').removeAttr("disabled", "disabled");
                    $('.loader').hide();
                    if(response.message == 'Success') {
                        Swal.fire({
                            icon: 'success',
                            title: 'Added!',
                            text: "Schedule SMS updated successfully",
                            customClass: {
                                confirmButton: 'btn btn-success'
                            }
                        }).then(({value}) => {
                            location.reload(true);
                        });
                    } else {
                        $.each( response.error, function( key, value ) {
                            if (key == 'smsSubject') {
                                $("#esmsSubject").next().show().find(".error_text").text('The sms subject field is required.');
                            }
                            if (key == 'campaignIds') {
                                $("#ecampaignIds").next().show().find(".error_text").text(value[0]);
                            }
                        });
                    }
                }
            });
        });
    });

    function appendSelectedCampaignIds(id){
        var checkedCount = $('input[name="toggleCampaignIds[]"]').length;
        if (checkedCount > 2) {
            alert('Only 3 campaigns are allowed');
        } else {
            var text = $('#campaignIds option[value='+id+ ']').text();
            //  console.log(id);
            /*if(!$('.selectedCampaignIds div').hasClass('non'+id)){
                var html = '<div class="non'+id+'" ><div class="textDiv">'+text+' <button name="delete" value="delete" onclick="deleteSelectedCampaignIds('+id+');"><i class="ti ti-x"></i></button><input type="hidden" value="'+id+'" name="toggleCampaignIds[]"></div></div>';
                $('.selectedCampaignIds').append(html);
            }*/
            if(!$('.eselectedCampaignIds div').hasClass('non'+id)){
                var html = '<div class="non'+id+'" ><div class="textDiv">'+text+' <button name="delete" value="delete" onclick="deleteSelectedCampaignIds('+id+');"><i class="ti ti-x"></i></button><input type="hidden" value="'+id+'" name="toggleCampaignIds[]"></div></div>';
                $('.eselectedCampaignIds').append(html);
            }
        }
    }

    function deleteSelectedCampaignIds(id){
        $('.selectedCampaignIds').find('div.non'+id).remove();
        $('.eselectedCampaignIds').find('div.non'+id).remove();
        $('#campaignIds option[value='+id+ ']').attr('selected', false);
    }
</script>
<div class="modal fade" id="addScheduleSMSModal" tabindex="-1" role="dialog" aria-labelledby="addScheduleSMSModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title m-0" id="exampleModalDefaultLabel">Add Schedule SMS</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div><!--end modal-header-->
            <div class="modal-body">
                <div class="row">
                    <form enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-lg-12">
                                <!-- business details -->
                                <div class="col-lg-12">
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label>Subject</label>
                                            <input type="text" class="form-control" id="smsSubject" name="smsSubject" placeholder="Subject">
                                            <div class="invalid-feedback" role="alert" style="display: block;">
                                                <span class="error_text smsSubjectError"></span>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label>Campaign</label>
                                            <select id="campaignIds" name="campaignIds[]" class="form-control" onchange="appendSelectedCampaignIds(this.value)">
                                                <option value="0">Select as Campaign</option>
                                                @foreach($campaignDetail as $key => $campaign)
                                                    <option value="{{ $campaign->campaign_id }}">{{ $campaign->campaign_id }} {{ $campaign->campaign_name }}</option>
                                                @endforeach
                                            </select>
                                            <div class="invalid-feedback" role="alert" style="display: block;">
                                                <span class="error_text campaignIdsError"></span>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="selectedCampaignIds"></div>
                                        </div>
                                    </div>
                                    <!--end card-->
                                </div><!--end col-->
                            </div>
                        </div>
                    </form>
                </div><!--end row-->
            </div><!--end modal-body-->
            <div class="modal-footer">
                <button type="button" class="btn btn-de-secondary btn-sm" data-bs-dismiss="modal">Close</button>
                <button type="button" id="addScheduleSMS" class="btn btn-de-primary btn-sm">Save Changes</button>
            </div><!--end modal-footer-->
        </div><!--end modal-content-->
    </div><!--end modal-dialog-->
</div><!--end modal-->

<div class="modal fade" id="editScheduleSMSModal" tabindex="-1" role="dialog" aria-labelledby="editScheduleSMSModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title m-0" id="exampleModalDefaultLabel">Add Schedule SMS</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div><!--end modal-header-->
            <div class="modal-body">
                <div class="row">
                    <form enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-lg-12">
                                <!-- business details -->
                                <div class="col-lg-12">
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label>Subject</label>
                                            <input type="hidden" id="scheduleSmsId" name="scheduleSmsId">
                                            <input type="text" class="form-control" id="esmsSubject" name="esmsSubject" placeholder="Subject" readonly>
                                            <div class="invalid-feedback" role="alert" style="display: block;">
                                                <span class="error_text smsSubjectError"></span>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label>Campaign</label>
                                            <select id="ecampaignIds" name="ecampaignIds[]" class="form-control" onchange="appendSelectedCampaignIds(this.value)">
                                                <option value="0">Select as Campaign</option>
                                                @foreach($campaignDetail as $key => $campaign)
                                                    <option value="{{ $campaign->campaign_id }}">{{ $campaign->campaign_id }} {{ $campaign->campaign_name }}</option>
                                                @endforeach
                                            </select>
                                            <div class="invalid-feedback" role="alert" style="display: block;">
                                                <span class="error_text campaignIdsError"></span>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="eselectedCampaignIds"></div>
                                        </div>
                                    </div>
                                    <!--end card-->
                                </div><!--end col-->
                            </div>
                        </div>
                    </form>
                </div><!--end row-->
            </div><!--end modal-body-->
            <div class="modal-footer">
                <button type="button" class="btn btn-de-secondary btn-sm" data-bs-dismiss="modal">Close</button>
                <button type="button" id="editScheduleSMS" class="btn btn-de-primary btn-sm">Save Changes</button>
            </div><!--end modal-footer-->
        </div><!--end modal-content-->
    </div><!--end modal-dialog-->
</div>

<div class="modal fade" id="previewScheduleSMSModal" tabindex="-1" role="dialog" aria-labelledby="previewScheduleSMSLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title m-0" id="exampleModalDefaultLabel">Preview Schedule SMS</h6>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div><!--end modal-header-->
            <div class="modal-body">
                <div class="row">
                    <div>
                        <table id="classTable" class="table table-bordered">
                            <tbody>
                            <tr>
                                <td><strong>Subject</strong></td>
                                <td style="word-break:break-all;"><span id="requestSubject"></span></td>
                            </tr>
                            <tr>
                                <td><strong>Request</strong></td>
                                <td style="word-break:break-all;"><span id="requestLog"></span></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div><!--end row-->
            </div><!--end modal-body-->
            <div class="modal-footer">
                <button type="button" class="btn btn-de-secondary btn-sm" data-bs-dismiss="modal">Close</button>
            </div><!--end modal-footer-->
        </div><!--end modal-content-->
    </div><!--end modal-dialog-->
</div>
</body>
</html>