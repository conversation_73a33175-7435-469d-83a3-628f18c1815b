<?php

namespace App\Models\Outbound;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Logic\LeadCampaignLogic;
use App\Models\Lead\Lead;
use App\Models\Lead\LeadMoving;
use App\Models\Lead\LeadJunkRemoval;
use App\Models\Lead\LeadHeavyLifting;
use App\Models\Lead\LeadCarTransport;
use App\Models\Campaign\Campaign;
use App\Models\Campaign\CampaignMoving;
use App\Models\Campaign\CampaignHeavyLifting;
use App\Models\Campaign\CampaignCarTransport;
use App\Models\Campaign\CampaignLeadDialer;
use App\Models\Campaign\CampaignOngoingCall;
use App\Models\Business\Business;
use App\Models\Business\BusinessPartnerMapping;
use App\Models\Master\MstLeadSource;
use App\Models\Lead\LeadRouting;
use App\Models\Outbound\LeadCallLog;
use App\Models\Outbound\GeneralNumberConfig;
use App\Helpers\CommonFunctions;
use App\Models\Logic\LogicJunk;
use App\Models\Logic\LogicHeavyLift;
use App\Models\Logic\LogicCarTransport;
use App\Models\Logic\ScoreLead;
use DB;

class LeadCall extends Model {

    use HasFactory;

    protected $table = "lead_call";
    protected $primaryKey = "lead_call_id";
    public $timestamps = false;
    protected $fillable = [
        "lead_call_id",
        "lead_id",
        "from_number",
        "to_number",
        "transfer_number",
        "transfer_type",
        "call_datetime",
        "user_id",
        "duration",
        "recording_url",
        "recording_id",
        "call_type",
        "call_disposition_id",
        "legA_call_uuid",
        "call_count",
        "is_checked_recording",
        "business_id",
        "campaign_id",
        "payout",
        "campaign_score",
        "inbound_call_status",
        "inbound_type",
        "auto_old_buffer",
        "created_at"
    ];

    public function users() {
        return $this->belongsTo('App\Models\User', 'user_id');
    }

    public function leadtransferCalls() {
        return $this->hasOne('App\Models\Lead\LeadTransferCall', 'lead_call_id');
    }

    public function calldesposition() {
        return $this->belongsTo('App\Models\Master\MstCallDisposition', 'call_disposition_id');
    }

    public function leadData() {
        return $this->hasOne('App\Models\Lead\Lead', 'lead_id', "lead_id");
    }

    public function routingCallCampaignInfo() {
        return $this->hasOne("App\Models\Campaign\Campaign", 'campaign_id', 'campaign_id');
    }
    public function businessData() {
        return $this->hasOne("App\Models\Business\Business", 'business_id', 'business_id');
    }
    public function user() {
        return $this->hasOne("App\Models\User", 'id', 'user_id');
    }

    public function getCampaignCheckFromZip($campaignIds, $leadId) {
        $logicLead = new LeadCampaignLogic();
        //$leads_data = Leads::with(['moveInfo'])->where('id', $leadId)->first(); //Comment By HJ and Added Below
        //Added By HJ On 18-10-2022 For Junk Removal and Heavy Equipment Start
        $leads_data = Lead::where('lead_id', $leadId)->get()->toArray();
        $activeCampAssocDataAr = array();
        $lead_category = 0;
        if (count($leads_data) > 0) {
            $lead_category = $leads_data[0]['lead_category_id'];
            if ($lead_category == 2) {
                $moveData = LeadJunkRemoval::where('lead_id', $leadId)->get()->toArray();
                //echo "<pre>";print_r($moveData);die;
                if (count($moveData) > 0) {
                    $fromZip = $toZip = $moveData[0]['from_zipcode'];
                    $fromState = $toState = $moveData[0]['from_state'];
                    $fromAreaCode = $toAreaCode = $moveData[0]['from_areacode'];
                }
            } else if ($lead_category == 3) {
                $moveData = LeadHeavyLifting::where('lead_id', $leadId)->get()->toArray();
                if (count($moveData) > 0) {
                    //echo "<pre>";print_r($moveData);die;
                    $fromZip = $moveData[0]['from_zipcode'];
                    $toZip = $moveData[0]['to_zipcode'];
                    $fromState = $moveData[0]['from_state'];
                    $toState = $moveData[0]['to_state'];
                    $fromAreaCode = $moveData[0]['from_areacode'];
                    $toAreaCode = $moveData[0]['to_areacode'];
                }
            } else if ($lead_category == 4) {
                $moveData = LeadCarTransport::where('lead_id', $leadId)->get()->toArray();
                if (count($moveData) > 0) {
                    //echo "<pre>";print_r($moveData);die;
                    $fromZip = $moveData[0]['from_zipcode'];
                    $toZip = $moveData[0]['to_zipcode'];
                    $fromState = $moveData[0]['from_state'];
                    $toState = $moveData[0]['to_state'];
                    $fromAreaCode = $moveData[0]['from_areacode'];
                    $toAreaCode = $moveData[0]['to_areacode'];
                }
            } else {
                $moveData = LeadMoving::where('lead_id', $leadId)->get()->toArray();
                if (count($moveData) > 0) {
                    //echo "<pre>";print_r($moveData);die;
                    $fromZip = $moveData[0]['from_zipcode'];
                    $toZip = $moveData[0]['to_zipcode'];
                    $fromState = $moveData[0]['from_state'];
                    $toState = $moveData[0]['to_state'];
                    $fromAreaCode = $moveData[0]['from_areacode'];
                    $toAreaCode = $moveData[0]['to_areacode'];
                }
            }
            $campaignData = Campaign::whereIn('campaign_id', $campaignIds)->get()->toArray();
            for ($d = 0; $d < count($campaignData); $d++) {
                $activeCampAssocDataAr[$campaignData[$d]['campaign_id']] = $campaignData[$d];
            }
            ///echo "<pre>";print_r($campaignIds);die;
            //echo $fromZip."==".$toZip."==".$fromState."==".$toState."==".$fromAreaCode."==".$toAreaCode;die;
            $campaignIds = $logicLead->checkFromZip($campaignIds, $activeCampAssocDataAr, $fromZip, $toZip, $fromState, $toState, $fromAreaCode, $toAreaCode);
            //echo "<pre>";print_r($campaignIds);die;
        }
        return $campaignIds;
    }

    public function getBusinesses($businesses_campaign_exit, $leadId, $leaddialer = 0, $tok_config, $inboundCall = 0) {
        $leads_data = Lead::where('lead_id', $leadId)->get()->toArray();
        //echo "<pre>";print_r($leads_data);die;
        $campaignIdArr = $campaignCategoryArr = $removeCampData = $finalCampDataArr = $businesses = $businessesgeting = $businesses_campaign_exit_all = array();
        $from_state = $to_state = '';
        if (count($leads_data) > 0) {
            $lead_category = $leads_data[0]['lead_category_id'];
            $is_spanish = $leads_data[0]['is_spanish'];
            if(strtoupper($is_spanish) == "YES"){
                $is_spanish = array("yes");
            }else{
                $is_spanish = array("yes","no");
            }
            if ($lead_category == 2) {
                $moveInfo = LeadJunkRemoval::where('lead_id', $leadId)->get()->toArray();
                if (count($moveInfo) > 0) {
                    $from_state = $to_state = $moveInfo[0]['from_state'];
                }
            } else if ($lead_category == 3) {
                $moveInfo = LeadHeavyLifting::where('lead_id', $leadId)->get()->toArray();
                if (count($moveInfo) > 0) {
                    $from_state = $moveInfo[0]['from_state'];
                    $to_state = $moveInfo[0]['to_state'];
                }
            } else if ($lead_category == 4) {
                $moveInfo = LeadCarTransport::where('lead_id', $leadId)->get()->toArray();
                if (count($moveInfo) > 0) {
                    $from_state = $moveInfo[0]['from_state'];
                    $to_state = $moveInfo[0]['to_state'];
                }
            } else {
                $moveInfo = LeadMoving::where('lead_id', $leadId)->get()->toArray();
                if (count($moveInfo) > 0) {
                    $from_state = $moveInfo[0]['from_state'];
                    $to_state = $moveInfo[0]['to_state'];
                }
            }
            $coverage_type = "long";
            if ($from_state == $to_state) {
                $coverage_type = "local";
            }
            if (count($businesses_campaign_exit) > 0) {
                $businessesgeting = Campaign::whereIn('campaign_id', $businesses_campaign_exit)->groupBy('business_id')->pluck('business_id')->toArray();
                $nonCompetesBusinessIdArr = BusinessPartnerMapping::whereIn('business_id', $businessesgeting)->pluck('business_partner_id')->toArray();
                for ($ds = 0; $ds < count($nonCompetesBusinessIdArr); $ds++) {
                    if (!in_array($nonCompetesBusinessIdArr[$ds], $businessesgeting)) {
                        $businessesgeting[] = $nonCompetesBusinessIdArr[$ds];
                    }
                }
                $businesses_campaign_exit_all = Campaign::whereIn('business_id', $businessesgeting)->pluck('campaign_id')->toArray();
            }
            //echo "<pre>";print_r($tok_config);die;
            $callTypeArr = array('both', 'outbound');
            if ($inboundCall > 0) {
                $callTypeArr = array('both', 'inbound');
            }
            //coverage_type -campaign_moving
            //DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 snapIt log getBusinesses where==lead id==" . $leadId . "===" . $inboundCall . "==" . json_encode($businesses_campaign_exit) . "==" . json_encode($businesses_campaign_exit_all) . "===" . $coverage_type . "===" . json_encode($callTypeArr) . "==" . $lead_category . "===" . json_encode($businessesgeting) . "===" . strtolower($tok_config)]);
            if (strtolower($tok_config) == "active") {
                $businesses = Business::with([
                            'businessCampaign' => function ($query) use ($businesses_campaign_exit_all, $callTypeArr, $lead_category,$is_spanish) {
                                return $query->where('campaign.is_active', '=', 'yes')->where('campaign.lead_type_id', 2)->whereIn('campaign.call_type', $callTypeArr)->whereIn('campaign.campaign_type_id', [1, 2, 6, 8])->whereIn('campaign.is_spanish', $is_spanish)->whereNotIn('campaign.campaign_id', $businesses_campaign_exit_all)->where('campaign.lead_category_id', $lead_category)->orderBy('campaign.default_score', 'desc');
                            }])->where('business.status', 'active')->whereNotIn('business_id', $businessesgeting)->get()->toArray();
                //echo "<pre>";print_r($businesses);die;
            } else {
                if ($leaddialer == 0) {
                    //echo "<pre>";print_r($lead_category);die;
                    $businesses = Business::with([
                                'businessCampaign' => function ($query) use ($businesses_campaign_exit_all, $callTypeArr, $lead_category,$is_spanish) {
                                    return $query->where('campaign.is_active', '=', 'yes')->where('campaign.lead_type_id', 2)->whereIn('campaign.call_type', $callTypeArr)->whereIn('campaign.campaign_type_id', [1, 2, 6, 8])->whereIn('campaign.is_spanish', $is_spanish)->whereNotIn('campaign.campaign_id', $businesses_campaign_exit_all)->where('campaign.lead_category_id', $lead_category)->orderBy('campaign.default_score', 'desc');
                                }])->where('business.status', 'active')->whereNotIn('business_id', $businessesgeting)->get()->toArray();
                } else if ($leaddialer == 1) {
                    $lead_campaign_exisit = LeadRouting::where('lead_id', $leadId)->where('route_status', 'sold')->pluck('campaign_id')->toArray();
                    if (count($lead_campaign_exisit) > 0) {
                        $lead_dialer_campaign = CampaignLeadDialer::whereIn('campaign_id', $lead_campaign_exisit)->pluck('live_transfer_campaign_id')->first();
                        if ($lead_dialer_campaign > 0) {
                            $businesses_campaign_exit_all = Campaign::whereIn('business_id', $businessesgeting)->whereNotIn('campaign.campaign_type_id', [3])->pluck('campaign_id')->toArray();
                            //DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 snapIt log getBusinesses dialer result==lead id==" . $leadId . "==" . json_encode($lead_dialer_campaign) . "===" . json_encode($businesses_campaign_exit_all) . "===" . json_encode($callTypeArr) . "==" . json_encode($lead_campaign_exisit) . "===" . strtolower($tok_config) . "===" . $lead_category]);

                            $businesses = Business::with([
                                        'businessCampaign' => function ($query) use ($businesses_campaign_exit_all, $lead_dialer_campaign, $callTypeArr, $lead_category,$is_spanish) {
                                            return $query->where('campaign.is_active', '=', 'yes')->where('campaign.lead_type_id', 2)->whereIn('campaign.call_type', $callTypeArr)->whereIn('campaign.campaign_type_id', [3])->whereIn('campaign.is_spanish', $is_spanish)->whereNotIn('campaign.campaign_id', $businesses_campaign_exit_all)->where('campaign.campaign_id', $lead_dialer_campaign)->where('campaign.lead_category_id', $lead_category)->orderBy('campaign.additional_score', 'desc');
                                        }])->where('business.status', 'active')->whereIn('business_id', $businessesgeting)->get()->toArray();
                        }
                    }
                }
            }
        }
        //DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 snapIt log getBusinesses result==lead id==" . $leadId . "===" . $lead_category . "===" . json_encode($businesses) . "===" . strtolower($tok_config)]);
        //Added By HJ On 13-06-2023 For Check Campaign and Lead Move Type Start
        $campIdArr = $moveTypeIdArr = $moveData = array();
        foreach ($businesses as $busi_key => $busi_value) {
            if (isset($busi_value['business_campaign'])) {
                $campData = $busi_value['business_campaign'];
                for ($rd = 0; $rd < count($campData); $rd++) {
                    $campIdArr[] = $campData[$rd]['campaign_id'];
                }
            }
        }
        //DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 snapIt log getBusinesses result==lead id==" . $leadId . json_encode($campIdArr)]);
        //echo "<pre>";print_r($lead_category);die;
        if (count($campIdArr) > 0) {
            if ($lead_category == 1) {
                $moveData = CampaignMoving::whereIn('campaign_id', $campIdArr)->where('move_type', $coverage_type)->get()->toArray();
            } else if ($lead_category == 3) {
                $moveData = CampaignHeavyLifting::whereIn('campaign_id', $campIdArr)->where('move_type', $coverage_type)->get()->toArray();
            } else if ($lead_category == 4) {
                $moveData = CampaignCarTransport::whereIn('campaign_id', $campIdArr)->where('move_type', $coverage_type)->get()->toArray();
            }
            for ($fg = 0; $fg < count($moveData); $fg++) {
                $moveTypeIdArr[] = $moveData[$fg]['campaign_id'];
            }
        }
        //echo "<pre>";print_r($businesses);die;
        if ($lead_category != 2) { //Added By HJ On 12-07-2023 For Junk Not Require to check Coverage type. Junk always local defined
            foreach ($businesses as $busi_key => $busi_value) {
                if (isset($busi_value['business_campaign'])) {
                    $campData = $busi_value['business_campaign'];
                    for ($rd = 0; $rd < count($campData); $rd++) {
                        if (!in_array($campData[$rd]['campaign_id'], $moveTypeIdArr)) {
                            unset($businesses[$busi_key]['business_campaign'][$rd]);
                            //echo "ss".$campData[$rd]['campaign_id'];die;
                        }
                    }
                }
            }
        }
        //Added By HJ On 13-06-2023 For Check Campaign and Lead Move Type End
        //echo "<pre>";print_r($businesses);die;
        DB::table('dev_debug')->insert(['sr_no' => 4, 'result' => "4 snapIt log getBusinesses result==lead id==" . $leadId . json_encode($businesses) . "===" . json_encode($moveTypeIdArr)]);
        return $businesses;
    }

    public function getBusinessesCriteria($businesses, $leadId, $pendingSlot, $tokpayout = 0, $leaddialer = 0, $leadBooking = 0, $tok_config, $inboundCall = 0, $lead_category = 1) {
        $move_date = date("Y-m-d", strtotime('+5 days'));
        $fromAreaCode = $fromCity = $fromState = $toAreaCode = $toCity = $toState = $fromZip = $toZip = null;
        if ($lead_category == 1) {
            $moveData = LeadMoving::where('lead_id', $leadId)->get()->toArray();
            if (count($moveData) > 0) {
                $move_date = $moveData[0]['move_date'];
                $fromState = $moveData[0]['from_state'];
                $toState = $moveData[0]['to_state'];
                $fromZip = $moveData[0]['from_zipcode'];
                $toZip = $moveData[0]['to_zipcode'];
                $fromAreaCode = $moveData[0]['from_areacode'];
                $toAreaCode = $moveData[0]['to_areacode'];
            }
        } else if ($lead_category == 2) {
            $moveData = LeadJunkRemoval::where('lead_id', $leadId)->get()->toArray();
            if (count($moveData) > 0) {
                $move_date = $moveData[0]['junk_remove_date'];
                $fromState = $toState = $moveData[0]['from_state'];
                $fromZip = $toZip = $moveData[0]['from_zipcode'];
                $fromAreaCode = $toAreaCode = $moveData[0]['from_areacode'];
            }
        } else if ($lead_category == 3) {
            $moveData = LeadHeavyLifting::where('lead_id', $leadId)->get()->toArray();
            if (count($moveData) > 0) {
                $move_date = $moveData[0]['lift_date'];
                $fromState = $moveData[0]['from_state'];
                $toState = $moveData[0]['to_state'];
                $fromZip = $moveData[0]['from_zipcode'];
                $toZip = $moveData[0]['to_zipcode'];
                $fromAreaCode = $moveData[0]['from_areacode'];
                $toAreaCode = $moveData[0]['to_areacode'];
            }
        } else if ($lead_category == 4) {
            $moveData = LeadCarTransport::where('lead_id', $leadId)->get()->toArray();
            if (count($moveData) > 0) {
                $move_date = $moveData[0]['move_date'];
                $fromState = $moveData[0]['from_state'];
                $toState = $moveData[0]['to_state'];
                $fromZip = $moveData[0]['from_zipcode'];
                $toZip = $moveData[0]['to_zipcode'];
                $fromAreaCode = $moveData[0]['from_areacode'];
                $toAreaCode = $moveData[0]['to_areacode'];
            }
        }
        $new_lead_own = $campaignIdArr = $campaignIdsArr = $campaignTypeArr = $campaignPayoutArr = $campaignPhoneArr = array();
        $dateReceived = date('Y-m-d');
        $dateTimeReceived = date("Y-m-d H:i:s");
        foreach ($businesses as $busi_key => $busi_value) {
            //echo "<pre>";print_r($busi_value);die;
            if (isset($busi_value['business_campaign']) && !empty($busi_value['business_campaign'])) {
                foreach ($busi_value['business_campaign'] as $busCamKey => $busCamvalue) {
                    //echo "<pre>";print_r($busCamvalue);die;
                    if ($busCamvalue['campaign_id'] > 0 && !in_array($busCamvalue['campaign_id'], $campaignIdArr)) {
                        $campaignIdArr[] = $busCamvalue['campaign_id'];
                    }
                    if ($busCamvalue['payment_type'] == 1 || $busCamvalue['payment_type'] == 0 || $busCamvalue['payment_type'] == '') {
                        if ($busCamvalue['campaign_id'] > 0 && !in_array($busCamvalue['campaign_id'], $campaignIdsArr)) {
                            $campaignIdsArr[] = $busCamvalue['campaign_id'];
                        }
                    }
                }
            }
        }
        //echo "<pre>";print_r($campaignIdArr);die;
        $trackDataArr = $activeCampAssocDataAr = $todayRouteData = array();
        if (count($campaignIdArr) > 0) {
            $campData = Campaign::whereIn('campaign_id', $campaignIdArr)->get(['campaign_id', 'campaign_type_id'])->toArray();
            for ($ba = 0; $ba < count($campData); $ba++) {
                $campaignTypeArr[$campData[$ba]['campaign_id']] = $campData[$ba]['campaign_type_id'];
            }
            if (strtolower($tok_config) == 'active' /*&& $pendingSlot != 4*/) { //Comment by BK on 12/08/2025 as per discussion with DS & MA
                $campaign_active_call = CampaignOngoingCall::whereIn('campaign_id', $campaignIdArr)->get(['campaign_id'])->toArray();
                for ($ca = 0; $ca < count($campaign_active_call); $ca++) {
                    if (isset($trackDataArr[$campaign_active_call[$ca]['campaign_id']])) {
                        $trackDataArr[$campaign_active_call[$ca]['campaign_id']] += 1;
                    } else {
                        $trackDataArr[$campaign_active_call[$ca]['campaign_id']] = 1;
                    }
                }
            }
        }
        //echo "<pre>";print_r($campaignIdsArr);die;
        if (count($campaignIdsArr) > 0) {
            $businessesCampaignPayout = CommonFunctions::getCampaignPayout($campaignIdsArr, 0, $tokpayout, $inboundCall);
            //echo "<pre>";print_r($businessesCampaignPayout);die;
            for ($pa = 0; $pa < count($businessesCampaignPayout); $pa++) {
                $campaignPayoutArr[$businessesCampaignPayout[$pa]['campaign_id']] = $businessesCampaignPayout[$pa];
            }
        }
        DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 snapIt log getBusinessesCriteria result==lead id==" . $leadId . json_encode($campaignPayoutArr) . "===" . json_encode($campaignTypeArr) . "==" . json_encode($campaignIdArr) . "===" . $pendingSlot . "==" . json_encode($trackDataArr)]);
        //echo "<pre>";print_r($campaignPayoutArr);die;
        foreach ($businesses as $busi_key => $busi_value) {
            //echo "<pre>";print_r($busi_value);die;
            $businessPaymentType = 'pre';
            if (isset($busi_value['payment_type'])) {
                $businessPaymentType = $busi_value['payment_type'];
            }
            if (isset($busi_value['business_campaign']) && !empty($busi_value['business_campaign'])) {
                foreach ($busi_value['business_campaign'] as $busCamKey => $busCamvalue) {
                    //echo "<pre>";print_r($busCamvalue);die;
                    $campaign_active_call = 0;
                    //$campaign_active_call = CampaignOngoingCall::where('campaign_id', $busCamvalue->id)->count();
                    if (isset($trackDataArr[$busCamvalue['campaign_id']])) {
                        $campaign_active_call = $trackDataArr[$busCamvalue['campaign_id']];
                    }
                    $activeCampAssocDataAr[$busCamvalue['campaign_id']] = (object) $busCamvalue;
                    $payout = $real_time_call_limit = 0;
                    $payoutName = "outbound_call";
                    if ($inboundCall > 0) {
                        $payoutName = "inbound_call";
                    }
                    if (isset($campaignPayoutArr[$busCamvalue['campaign_id']])) {
                        $campPayoutData = $campaignPayoutArr[$busCamvalue['campaign_id']];
                        $real_time_call_limit = $campPayoutData['real_time_call_limit'];
                        $payout = $campaignPayoutArr[$busCamvalue['campaign_id']][$payoutName];
                    }

                    // Check payout null or not
                    if(!isset($payout) && $campaignTypeArr[$busCamvalue['campaign_id']] <> 3) {
                        DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 snapIt log getBusinessesCriteria Payout Not Set" . $leadId]);
                        continue;
                    }

                    //echo $real_time_call_limit."==".$payout;die;
                    if ($campaign_active_call >= $real_time_call_limit && $real_time_call_limit != 0) {
                        //$this->createOutboundLogs($leadId, '29', 'Call track Calllimit over.');
                        DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 snapIt log getBusinessesCriteria Call track Calllimit over.==" . $leadId . "===" . $busCamvalue['campaign_id']]);
                        continue;
                    }
                    if (isset($campaignTypeArr[$busCamvalue['campaign_id']]) && $campaignTypeArr[$busCamvalue['campaign_id']] == 2 && strtolower($tok_config) == 'active' && $pendingSlot != 4) {
                        //$this->createOutboundLogs($leadId, '29', 'Exclusive Campaign Continue..');
                        DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 snapIt log getBusinessesCriteria Exclusive Campaign Continue..==" . $leadId . "===" . $busCamvalue['campaign_id']]);
                        continue;
                    }
                    if (isset($campaignTypeArr[$busCamvalue['campaign_id']]) && $campaignTypeArr[$busCamvalue['campaign_id']] == 6 && strtolower($tok_config) == 'active' && $pendingSlot >= 2) {
                        //$this->createOutboundLogs($leadId, '30', 'Dual Slot Campaign Continue..');
                        DB::table('dev_debug')->insert(['sr_no' => 4, 'result' => "4 snapIt log getBusinessesCriteria Dual Slot Campaign Continue..==" . $leadId . "===" . $busCamvalue['campaign_id']]);
                        //continue;
                    }
                    if ($busCamvalue['payment_type'] == 1 || $busCamvalue['payment_type'] == 0 || $busCamvalue['payment_type'] == '') {
                        if ($busCamvalue['payment_type'] == 0 || $busCamvalue['payment_type'] == '') {
                            $busCamvalue['credit_available'] = $busi_value['credit_available'];
                        }
                        if (strtolower($tok_config) == 'active') {
                            $leaddialer = 0;
                        }
                        if (($busCamvalue['credit_available'] >= $payout && $payout > 0) || $leaddialer == 1 || $leadBooking == 1 || strtolower($businessPaymentType) == "post") {
                            $busCamvalue['lead_id'] = $leadId;
                            $busCamvalue['payout'] = $payout;
                            $busCamvalue['call_desc'] = 1;
                            $busCamvalue['phonenumber'] = '';
                            if (isset($busCamvalue['forward_number']) && $busCamvalue['forward_number'] != "") {
                                $busCamvalue['phonenumber'] = $busCamvalue['forward_number'];
                            }
                            if ($busCamvalue['phonenumber'] != 0 && $busCamvalue['phonenumber'] != '') {
                                $new_lead_own[] = $busCamvalue;
                            }
                        }
                    }
                }
            }
        }
        $logicCampaign = new LeadCampaignLogic();
        //echo "<pre>";print_r($new_lead_own);die;
        /* filter for lead from to start zip area code */
        $campaignIds = array_column($new_lead_own, 'campaign_id');
        DB::table('dev_debug')->insert(['sr_no' => 5, 'result' => "5 snapIt log getBusinessesCriteria Before All campaign checkCampaignExcludedDates==" . $leadId . "===" . implode(',', $campaignIds)]);
        //$this->createOutboundLogs($leadId, '35', 'Before All campaign checkCampaignExcludedDates: ' . implode(',', $campaignIds));
        $campaignIds = $logicCampaign->checkCampaignExcludedDates($campaignIds, $activeCampAssocDataAr, $move_date);
        //$campaignIds = $logicCampaign->checkCampaignSource($campaignIds, $leadId);
        //$this->createOutboundLogs($leadId, '35', 'After All campaign checkCampaignExcludedDates: ' . implode(',', $campaignIds));
        //echo "<pre>";print_R($campaignIds);die;
        //$this->createOutboundLogs($leadId, '31', 'Before All campaign CheckTiming: ' . implode(',', $campaignIds));
        DB::table('dev_debug')->insert(['sr_no' => 6, 'result' => "6 snapIt log getBusinessesCriteria After All campaign checkCampaignExcludedDates==" . $leadId . "===" . implode(',', $campaignIds)]);
        $coverageType = 0;
        if (strcmp($fromState, $toState)) {
            $coverageType = 1;
        }
        $dateCurrent = date('Y-m-d');
        $dateTimeCurrent = date('Y-m-d H:i:s');
        $leadRouting = LeadRouting::where('lead_id', $leadId)->where('route_status', 'sold')->get()->toArray();
        for ($r = 0; $r < count($leadRouting); $r++) {
            $campIdArr[] = $leadRouting[$r]['campaign_id'];
            if ($dateCurrent == date('Y-m-d', strtotime($leadRouting[$r]['created_at']))) {
                $todayRouteData[$leadRouting[$r]['campaign_id']][] = $leadRouting[$r];
            }
        }
        $campaignIds = $logicCampaign->checkTiming($campaignIds, $dateCurrent, $dateTimeCurrent, $coverageType, $todayRouteData);
        DB::table('dev_debug')->insert(['sr_no' => 7, 'result' => "7 snapIt log getBusinessesCriteria After All campaign CheckTiming and before checkFromZip==" . $leadId . "===" . implode(',', $campaignIds)]);
        //$this->createOutboundLogs($leadId, '33', 'After All campaign CheckTiming and before checkFromZip: ' . implode(',', $campaignIds));
        //DB::table('dev_debug')->insert(['sr_no' => 7, 'result' => "7 snapIt log getBusinessesCriteria After All campaign checkLimit==" . $leadId . "===" . implode(',', $campaignIds)]);
        $campaignIds = $logicCampaign->checkLimit($campaignIds, $activeCampAssocDataAr, $coverageType, $tokpayout, $inboundCall);
        //echo "<pre>";print_R($campaignIds);die;
        //$this->createOutboundLogs($leadId, '34', 'After All campaign checkLimit: ' . implode(',', $campaignIds));
        DB::table('dev_debug')->insert(['sr_no' => 8, 'result' => "8 snapIt log getBusinessesCriteria After All campaign checkLimit==" . $leadId . "===" . implode(',', $campaignIds)]);
        $campaignIds = $logicCampaign->checkFromZip($campaignIds, $activeCampAssocDataAr, $fromZip, $toZip, $fromState, $toState, $fromAreaCode, $toAreaCode);
        //echo "<pre>";print_R($campaignIds);die;
        //$this->createOutboundLogs($leadId, '32', 'After All campaign checkFromZip: ' . implode(',', $campaignIds));
        //$this->createOutboundLogs($leadId, '32', 'After All campaign checkFromZip leadcatrgory=' . $lead_category . ': ' . implode(',', $campaignIds));
        DB::table('dev_debug')->insert(['sr_no' => 9, 'result' => "9 snapIt log getBusinessesCriteria After All campaign checkFromZip leadcatrgory==" . $leadId . "==" . $lead_category . "===" . implode(',', $campaignIds)]);
        if ($lead_category == 2) {
            $logicJunk = new LogicJunk();
            $campaignIds = $logicJunk->checkJunkLogic($campaignIds, $leadId, $moveData);
            //echo "<pre>";print_R($campaignIds);die;
        }
        //DB::table('dev_debug')->insert(['sr_no' => 10, 'result' => "10 snapIt log getBusinessesCriteria After All campaign Check Junk Category==" . $leadId . "==" . $lead_category . "===" . implode(',', $campaignIds)]);
        //$this->createOutboundLogs($leadId, '35', 'After All campaign Check Junk Category=' . $lead_category . ': ' . implode(',', $campaignIds));
        if ($lead_category == 3) {
            $logicHeavyLift = new LogicHeavyLift();
            $campaignIds = $logicHeavyLift->checkHeavyLiftLogic($campaignIds, $leadId, $moveData);
            //echo "<pre>";print_R($campaignIds);die;
        }
        //$this->createOutboundLogs($leadId, '36', 'After All campaign Check Heavy Lift Category=' . $lead_category . ': ' . implode(',', $campaignIds));
        //DB::table('dev_debug')->insert(['sr_no' => 11, 'result' => "11 snapIt log getBusinessesCriteria After All campaign Check Heavy Lift Category==" . $leadId . "==" . $lead_category . "===" . implode(',', $campaignIds)]);
        if ($lead_category == 4) {
            $logicCarTransport = new LogicCarTransport();
            $campaignIds = $logicCarTransport->checkCarTransportLogic($campaignIds, $leadId, $moveData);
            //echo "<pre>7";print_r($campaignIds);die;
            //echo "<br>";
        }
        //$this->createOutboundLogs($leadId, '36', 'After All campaign Check Car Transportation Category=' . $lead_category . ': ' . implode(',', $campaignIds));
        //DB::table('dev_debug')->insert(['sr_no' => 12, 'result' => "12 snapIt log getBusinessesCriteria After All campaign Check Car Transportation Category==" . $leadId . "==" . $lead_category . "===" . implode(',', $campaignIds)]);
        $filter_state_zip_area = array();
        foreach ($new_lead_own as $checkfromzipkey => $checkfromzipvalue) {
            if (in_array($checkfromzipvalue['campaign_id'], $campaignIds)) {
                $filter_state_zip_area[] = $checkfromzipvalue;
            }
        }
        $new_withscore_calculate = $this->getScoreCalculate($filter_state_zip_area, $lead_category, $moveData);
        if (strtolower($tok_config) == 'active' && $pendingSlot == 0) {
            $pendingSlot = 1;
        }
        $onlysport_fill = array_slice($new_withscore_calculate, 0, $pendingSlot);
        DB::table('dev_debug')->insert(['sr_no' => 13, 'result' => "13 snapIt log getBusinessesCriteria Final Data==" . $leadId . "==" . $pendingSlot . "===" . json_encode($onlysport_fill)]);
        return $onlysport_fill;
    }

    public function getScoreCalculate($campaignData, $lead_category, $moveData) {
        //echo "<pre>dd";print_r($campaignData);die;
        $scoreLeadPriceObj = new ScoreLead();
        $dateReceived = date('Y-m-d');
        $dateTimeReceived = date("Y-m-d H:i:s");
        $score_campaign = $premiumCampaignIds = array();
        foreach ($campaignData as $premiumCampaignKey => $premiumCampaign) {
            $premiumCampaignIds[$premiumCampaign['campaign_id']] = ($premiumCampaign['default_score'] + $premiumCampaign['additional_score'] + $premiumCampaign['subscription_score']);
        }
        //echo "<pre>dd";print_r($premiumCampaignIds);die;
        //Comment By BK on 28/05/2025 as per discussion with client
        //$premiumCampaignIds = $scoreLeadPriceObj->countScoreRemainingTime($dateReceived, $dateTimeReceived, $premiumCampaignIds);
        //$premiumCampaignIds = $scoreLeadPriceObj->countScoreLastPhoneLead($premiumCampaignIds);

        //Added By BK on 28/05/2025 as per discussion with client
        $premiumCampaignIds = $scoreLeadPriceObj->countScoreLeadPrice($premiumCampaignIds);
        $premiumCampaignIds = $scoreLeadPriceObj->countScoreRecurrentPayment($premiumCampaignIds);
        $premiumCampaignIds = $scoreLeadPriceObj->countScoreCampaignFund($premiumCampaignIds);
        if ($lead_category == 2) {
            $logicJunk = new LogicJunk();
            $premiumCampaignIds = $logicJunk->junkScoreCalculate($premiumCampaignIds);
        }
        if ($lead_category == 3) {
            $logicHeavyLift = new LogicHeavyLift();
            $allTypeCampIds = $logicHeavyLift->heavyLiftScoreCalculate($premiumCampaignIds);
        }
        if ($lead_category == 4) {
            $logicCarTransport = new LogicCarTransport();
            $premiumCampaignIds = $logicCarTransport->carTransportScoreCalculate($premiumCampaignIds);
        }
        //echo "<pre>dd";print_r($campaignData);die;
        $campignIdArr = $score_array = array();
        $highastScore = 0;
        foreach ($campaignData as $premiumCampaignKey => $premiumCampaign) {
            if (in_array($premiumCampaign['campaign_id'], $campignIdArr)) {
                $premiumCampaign['latest_score'] = $highastScore + 1;
            } else {
                $premiumCampaign['latest_score'] = $premiumCampaignIds[$premiumCampaign['campaign_id']];
            }
            //echo "<pre>dd";print_r($premiumCampaign);die;
            $premiumCampaign['lead_data'] = $moveData;
            $score_array[] = $premiumCampaign;
        }
        array_multisort(array_column($score_array, 'latest_score'), SORT_DESC, $score_array);
        $score_campaign = $score_array;
        //echo "<pre>";print_r($score_campaign);die;
        return $score_campaign;
    }

    public function getNumber($source_id, $custNumber) {
        $numFrom = '';
        $getSourceName = MstLeadSource::where('lead_source_id', $source_id)->get()->toArray();
        if (count($getSourceName) > 0) {
            //echo "<pre>";print_r($getSourceName);die;
            //Start For Moving Number
            $sourceName = $getSourceName[0]['lead_source_name'];
            $pos = strpos($sourceName, 'VM');
            $pos1 = strpos($sourceName, 'QTM');
            $pos2 = strpos($sourceName, 'IS');
            //End For Moving Number
            //Start For Junk Removal Number
            $pos3 = strpos($sourceName, 'VMJR');
            $pos4 = strpos($sourceName, 'QTMJR');
            $pos5 = strpos($sourceName, 'ISJR');
            //End For Junk Removal Number
            //Start For Heavy Equipment Number
            $pos6 = strpos($sourceName, 'VMHL');
            $pos7 = strpos($sourceName, 'QTMHL');
            $pos8 = strpos($sourceName, 'ISHL');
            //End For Heavy Equipment Number
            //Start For Car Transportation Number
            $pos9 = strpos($sourceName, 'VMCT');
            $pos10 = strpos($sourceName, 'QTMCT');
            $pos11 = strpos($sourceName, 'ISCT');
            //End For Car Transportation Number
            if ($pos !== false || $pos3 !== false || $pos6 !== false || $pos9 !== false) {
                $numFrom = GeneralNumberConfig::getDialNumberByAreacode($custNumber)['vm_areacode_outbound'];
            } else if ($pos1 !== false || $pos4 !== false || $pos7 !== false || $pos10 !== false) {
                $numFrom = GeneralNumberConfig::getDialNumberByAreacode($custNumber)['qtm_areacode_outbound'];
            } else if ($pos2 !== false || $pos5 !== false || $pos8 !== false || $pos11 !== false) {
                $numFrom = GeneralNumberConfig::getDialNumberByAreacode($custNumber)['ism_areacode_outbound'];
            } else {
                $numFrom_data = GeneralNumberConfig::getDialNumberByAreacode($custNumber);
                if(isset($numFrom_data['vm_areacode_outbound'])){
                    $numFrom = $numFrom_data['vm_areacode_outbound'];
                }else if(isset($numFrom_data['qtm_areacode_outbound'])){
                    $numFrom = $numFrom_data['qtm_areacode_outbound'];
                }else if(isset($numFrom_data['ism_areacode_outbound'])){
                    $numFrom = $numFrom_data['ism_areacode_outbound'];
                }
            }
        }
        if ($numFrom == "") {
            $numFrom_data = GeneralNumberConfig::getDialNumberByAreacode($custNumber);
            if(isset($numFrom_data['vm_areacode_outbound'])){
                $numFrom = $numFrom_data['vm_areacode_outbound'];
            }else if(isset($numFrom_data['qtm_areacode_outbound'])){
                $numFrom = $numFrom_data['qtm_areacode_outbound'];
            }else if(isset($numFrom_data['ism_areacode_outbound'])){
                $numFrom = $numFrom_data['ism_areacode_outbound'];
            }
        }
        return $numFrom;
    }

    public function createOutboundLogs($lead_id, $type, $log) {
        $log = array(
            'lead_id' => $lead_id,
            'lead_call_id' => $lead_id,
            'log_srno' => $type,
            'log' => $log,
            'created_at' => date('Y-m-d H:i:s')
        );
        LeadCallLog::create($log);
    }

    public function getVoicePromt($origin_id) {
        $getSourceName = MstLeadSource::where('lead_source_id', $origin_id)->get()->toArray();
        $file = 'MQ_1VM.mp3';
        if (count($getSourceName) > 0) {
            $sourceName = $getSourceName[0]['lead_source_name'];
            $pos = strpos($sourceName, 'VM');
            $pos1 = strpos($sourceName, 'QTM');
            $pos2 = strpos($sourceName, 'IS');
            if ($pos !== false) {
                $file = 'MQ_1VM.mp3';
            } else if ($pos1 !== false) {
                $file = 'MQ_2QTM.mp3';
            } else if ($pos2 !== false) {
                $file = 'MQ_2IS.mp3';
            }
        }
        return $file;
    }
    
    public function getOldVoicePromt($origin_id) {
        $getSourceName = MstLeadSource::where('lead_source_id', $origin_id)->get()->toArray();
        $file = 'MQ_1VM.mp3';
        if (count($getSourceName) > 0) {
            $sourceName = $getSourceName[0]['lead_source_name'];
            $pos = strpos($sourceName, 'VM');
            $pos1 = strpos($sourceName, 'QTM');
            $pos2 = strpos($sourceName, 'IS');
            if ($pos !== false) {
                $file = 'VM_automated_outbound.mp3';
            } else if ($pos1 !== false) {
                $file = 'QTM_automated_outbound.mp3';
            } else if ($pos2 !== false) {
                $file = 'ISM_automated_outbound.mp3';
            }
        }
        return $file;
    }

}
