<?php

namespace App\Http\Controllers\Emails;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Emails\TemplateEmail;
use App\Exports\GoldenGateLeadExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Emails\EmailLog;

use PDF;
use Exception;
use DB;
use Log;

class PrimeMarketingGoldenGateInvoiceEmailController extends Controller
{
    public function primeGoldenGateInvoiceEmailSend()
    {
        // Store on a different disk (e.g. s3)
        $startDate      = date("Y-m-d", mktime(0, 0, 0, date("m")-1, 1));
        $endDate        = date("Y-m-d", mktime(0, 0, 0, date("m"), 0));
        $startDate      = '2024-03-20';
        $endDate        = '2024-06-28';
        $month          = date('F', strtotime(date('F') . " last month"));
        $year           = date('Y', strtotime(date('Y') . " last month"));
        $csvfileName    = 'GoldenGateRelocationLLC_'.$month.'_'.$year.'.csv';
        $pdffileName    = 'GoldenGateRelocationLLC_'.$month.'_'.$year.'.pdf';
        Excel::store(new GoldenGateLeadExport(), $csvfileName, 'export');
        //Excel::store(new RexDirectLeadExport(), '/export/' . $csvfileName);

        $leadDetail     = DB::select("SELECT c.campaign_id, c.campaign_name, sum(lr.payout) as total_amount, COUNT(l.lead_id) AS total_leads
                                      FROM lead_routing lr
                                      LEFT JOIN `lead` l on lr.lead_id = l.lead_id
                                      LEFT JOIN campaign c on lr.campaign_id = c.campaign_id
                                      INNER JOIN lead_moving lm ON l.lead_id = lm.lead_id
                                      WHERE lr.campaign_id IN (3361, 3362, 3365, 3366, 3367, 3368, 4136, 4893) AND lr.route_status = 'sold' AND (lr.created_at >= '".$startDate." 00:00:00' AND lr.created_at <= '".$endDate." 23:59:59')
                                      GROUP BY lr.campaign_id");
        $leadArray      = [];
        if (isset($leadDetail) && !empty($leadDetail)) {
            foreach ($leadDetail as $lead) {
                $leadArray[]= array(
                    'campaign_name' => $lead->campaign_name,
                    'total_leads' => $lead->total_leads,
                    'total_amount' => $lead->total_amount,
                );
            }
        }
        view()->share('leadDetail', $leadArray);
        $pdf            = PDF::loadView('exports.goldengateinvoicepdf', $leadArray);
        $path           = public_path('/export/');
        $pdf->save($path . $pdffileName);

        // dd('sent');
        //send email
        $this->sendEmail(array($csvfileName, $pdffileName), $leadArray);

        //remove image from export directory
        //$this->destroy(array($csvfileName, $pdffileName));
        echo "Invoice email sent successfully."; exit();
    }

    public function sendEmail ($fileName, $leadDetail)
    {
        /*$subject      = 'Golden Gate Relocation LLC Invoice and lead list';*/
        $subject        = 'Golden Gate Relocation LLC Invoice';
        $setFrom        = '<EMAIL>';
        $setTo          = '<EMAIL>';
        //$setTo        = '<EMAIL>';
        $postmarkToken  = '************************************';

        $mailBodyData   = array();
        $mail           = new TemplateEmail();

        $html           = 'Hello Golden Gate Relocation LLC,<br><br> We want to let you know that Prime Marketing has sent you a Purchased Leads <strong>Invoice of $' . @number_format($leadDetail[0]['total_amount'] + $leadDetail[1]['total_amount'] + $leadDetail[2]['total_amount'] + $leadDetail[3]['total_amount'] + $leadDetail[4]['total_amount'] - 10000, 2) . '</strong> till <strong>31st ' . date('F', mktime(0, 0, 0, 7, 10)) . ', ' . date('Y', strtotime(date('Y') . " last month")) . '</strong>.<br><br> Please find the attached invoice and purchased lead details.<br><br> Please don\'t hesitate to get in <NAME_EMAIL> if you have any questions or need clarifications.<br><br> Best regards,<br> Client Support,<br> Prime Marketing LLC,<br> <a href="http://mover.primemarketing.us/">www.primemarketing.us</a>';
        // send email
        $mail->setTo($setTo);
        //$mail->setBcc('<EMAIL>');
        $mail->setFrom($setFrom);
        $mail->setSubject($subject);
        $mail->setHtmlbody($html);
        $excelContent   = base64_encode(file_get_contents(public_path() . '/export/' . $fileName[0]));
        $pdfContent     = base64_encode(file_get_contents(public_path() . '/export/' . $fileName[1]));
        $attchment[]    = ['Name' => $fileName[0], 'Content' => $excelContent, 'ContentType' => 'application/excel'];
        $attchment[]    = ['Name' => $fileName[1], 'Content' => $pdfContent, 'ContentType' => 'application/octet-stream'];
        //echo '<pre>'; print_r($attchment); die;
        $mail->setAttachment($attchment);
        $response = $mail->send_email($token = $postmarkToken, 'invoice');

        EmailLog::create([
            "business_id" => 804,
            "subject" => $subject,
            "set_to" => $setTo,
            "set_from" => $setFrom,
            "response" => json_encode($response->original['data']),
            "type" => 'invoice',
        ]);
    }

    public function destroy($fileName)
    {
        for ($i=0; $i < count($fileName); $i++) {
            $path = app_path("export/" . $fileName[$i]);
            unlink($path);
        }
    }
}