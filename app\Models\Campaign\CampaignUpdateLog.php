<?php

namespace App\Models\Campaign;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CampaignUpdateLog extends Model
{
    use HasFactory;
    protected $table = "campaign_update_log";
    protected $primaryKey = "campaign_update_log_id";

    public $timestamps = false;

    protected $fillable = [
        "campaign_update_log_id",
        "campaign_id",
        "field_name",
        "old_value",
        "new_value",
        "created_at",
        "created_user_id",
        "ip"
    ];
    public function campaigns(){
        return $this->belongsTo('App\Models\Campaign\Campaign', 'campaign_id');
    }       
    public function users() {
        return $this->belongsTo('App\Models\User', 'created_user_id');
    }    
}
