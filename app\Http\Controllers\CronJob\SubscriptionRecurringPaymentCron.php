<?php

namespace App\Http\Controllers\CronJob;

use Illuminate\Http\Request;
use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\Subscription\Subscription;
use App\Models\Subscription\SubscriptionRecuringPayment;
use App\Models\Business\Business;
use App\Models\Business\BusinessCC;
use App\Models\Business\BusinessCampaignPayment;
use App\Models\Business\BusinessCampaingnPaymentLog;
use App\Http\Controllers\Businesses\BusinessPaymentController;
use App\Models\Master\MstSubscriptionPlan;
use App\Models\Emails\RecurringEmailLog;
use App\Models\Emails\TemplateEmail;
use App\Models\CronJob\CronLog;
use App\Models\DevDebug;
use DB;
use Exception;
use Auth;

class SubscriptionRecurringPaymentCron extends Controller
{
    public function subscriptionRecurringPayment() {
        try {
            $this->insertLog(11,'SubscriptionRecurringPayment Cron Start: ' . date("Y-m-d H:i:s"));
            $subscriptionDetail = Subscription::where('subscription_start', '>=', date('Y-m-01', strtotime('+1 month')))->where('subscription_end', '<=', date('Y-m-t', strtotime('+1 month')))->where('is_active', 'yes')->get(['business_id']);
            $businessIdArray = $subscriptionPlanArray = [];
            $currentDate = date('Y-m-d', time());
            if (count($subscriptionDetail) > 0) { foreach($subscriptionDetail as $subscription) {
                $businessIdArray[] = $subscription->business_id;
            } }

            $subscriptionPlan = MstSubscriptionPlan::get();
            for ($p=0; $p<count($subscriptionPlan); $p++) {
                $subscriptionPlanArray[$subscriptionPlan[$p]->subscription_plan_id] = $subscriptionPlan[$p]->subscription_plan;
            }

            $businessPaymentObj = New BusinessPaymentController();
            $recuringPaymentDetail = SubscriptionRecuringPayment::where('is_active', 'yes')->whereNotIn('business_id', $businessIdArray)->get();
            //echo '<pre>'; print_r($recuringPaymentDetail); die;
            foreach ($recuringPaymentDetail as $recuringPayment) {
                $subscriptionPlanDetail = MstSubscriptionPlan::where('subscription_plan_id', $recuringPayment->subscription_plan_id)->first();
                $data = [];
                $businessName = $businessEmail = $businessCampaignName = "";
                $businessId = $recuringPayment->business_id;
                $cardId = $recuringPayment->business_cc_id;

                $checkLastStatus = DB::select("SELECT is_charge_approved, business_campaign_payment_id FROM business_campaign_payment WHERE business_cc_id = '" . $cardId . "' ORDER BY business_campaign_payment_id DESC LIMIT 2");
                $statusFail = $paymentId = 0;
                foreach ($checkLastStatus as $value) {
                    if ($value->is_charge_approved == 'no') {
                        $statusFail += 1;
                    }
                    if ($paymentId == 0) {
                        $paymentId = $value->business_campaign_payment_id;
                    }
                }
                $this->insertLog(11, 'businessId: ' . $businessId . ' statusFail: ' . $statusFail);
                if ($statusFail == 2) {
                    //change recurring status 0
                    SubscriptionRecuringPayment::where('business_cc_id', $cardId)
                        ->update([
                            'is_active' => 'no'
                        ]);

                    $businessCampaignPaymentLogDetail = BusinessCampaingnPaymentLog::where('business_campaign_payment_id', $paymentId)->first();
                    $cardDetail = BusinessCC::where('business_cc_id', $cardId)->first();
                    $businessDetail = Business::where('business_id', $businessId)->first();
                    $data['campaign_id'] = '--';
                    $data['campaign_name'] = '--';
                    $data['email'] = $businessDetail->email;
                    $businessCampaignName = $businessDetail->display_name ?? '';

                    $data['business_cc_id'] = $cardId;
                    $data['business_id'] = $businessDetail->business_id;
                    $data['business_name'] = $businessDetail->display_name;
                    $data['email'] = $businessDetail->email;
                    $response = json_decode($businessCampaignPaymentLogDetail->response);
                    $data['response'] = (isset($response->message)) ? $response->message : $response->avs_message;
                    $data['cardNumber'] = 'XXXX-XXXX-XXXX-' . sprintf("%04d", $cardDetail->card);

                    //Added by BK on 05/02/2025 convenience fee email to evey success payment
                    $businessPaymentObj->recurrentStopEmailNotificationTemplate($businessId, $businessDetail->display_name, $businessCampaignName, $businessDetail->email);

                    $businessPaymentObj->subscriptionChangedEmailNotificationTemplate($businessId, $businessDetail->display_name, $businessDetail->email, $recuringPayment->subscription_plan_id, $subscriptionPlanArray[$recuringPayment->subscription_plan_id], 1, $subscriptionPlanArray[1]);

                    //send inactive email notification to sales rep
                    $this->sendEmail($data);
                    goto stop;
                }

                //fetch card details from business_cc table
                $paymentCardId = '';
                $cardDetail = BusinessCC::where('business_cc_id', $cardId)
                    ->where('status','active')
                    ->get(['payment_cust_id', 'payment_card_id', 'card'])
                    ->first();
                if($cardDetail) {
                    $paymentCardId = $cardDetail->payment_card_id;
                }
                //echo '<pre>'; print_r($cardDetail); die;
                $charge = $subscriptionPlanDetail->amount;
                $flag = $convenienceFee = 0;
                // amount based
                if (!empty($businessId)) {
                    $businessCampaignPaymentDetail = Business::where('status', 'active')
                        ->where('business_id', $businessId)
                        ->first();
                }

                if (!empty($businessCampaignPaymentDetail)) {
                    $businessDetail = Business::where('business_id', $businessCampaignPaymentDetail->business_id)->first();
                    $businessName = $businessDetail->display_name;
                    $businessEmail = $businessDetail->email;
                    $businessCampaignName = $businessName;
                    $flag = 1;
                }

                // echo '<pre>flag = '. $flag .', businessCampaignType = '. $businessId;
                $this->insertLog(11, 'flag: ' . $flag . ' and paymentCardId: ' . $paymentCardId);
                if ($flag == 1 && !empty($paymentCardId)) {
                    if ($charge > 0) {
                        $convenienceFee = ($charge * 3) / 100;
                        $charge         = $charge + $convenienceFee;
                    }

                    $omni_token         = Helper::checkServer()['omni_token'];
                    $postFields         = array("payment_method_id" => $paymentCardId, "meta" => array("tax" => 0), "total" => $charge, "pre_auth" => 0);
                    $jsonData           = json_encode($postFields);
                    $ch                 = curl_init();
                    curl_setopt($ch, CURLOPT_URL, "https://apiprod.fattlabs.com/charge");
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
                    curl_setopt($ch, CURLOPT_HEADER, FALSE);
                    curl_setopt($ch, CURLOPT_POST, TRUE);
                    DevDebug::create(['sr_no' => 211, 'result' => 'Subscription Payment request: ' . $jsonData, 'created_at' => date('Y-m-d H:i:s')]);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);

                    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                        "Content-Type: application/json",
                        "Authorization:Bearer ".$omni_token,
                        "Accept: application/json"
                    ));

                    $response           = curl_exec($ch);
                    curl_close($ch);
                    $jsonResponse       = json_decode($response, true);
                    $responseStatus     = "no";
                    if (isset($jsonResponse['success']) && $jsonResponse['success'] == true) {
                        $responseStatus = "yes";
                    }
                    DevDebug::create(['sr_no' => 212, 'result' => 'Subscription Payment response: ' . json_encode($jsonResponse), 'created_at' => date('Y-m-d H:i:s')]);
                    //print_r($jsonResponse);

                    //Added by BK on 03/02/2025 convenience fee to evey payment
                    if ($convenienceFee > 0) {
                        $charge = $charge - $convenienceFee;
                    }

                    // echo '<pre>flag = '. $flag .', businessCampaignType = '. $businessId;  print_r($jsonResponse);
                    $this->insertLog(11, 'chargePayment Response: ' .  $response);
                    $beforeCharge = $afterCharge = 0;
                    $amountCharge = $charge;

                    $businessCampaignPaymentId = BusinessCampaignPayment::create([
                        'business_id' => $businessId,
                        'business_cc_id' => $cardId,
                        'balance_before_charge' => $beforeCharge,
                        'amount_charge' => $amountCharge,
                        'balance_after_charge' => $afterCharge,
                        'convenience_fee' => $convenienceFee,
                        'charge_type_id' => 1,
                        'charge_method_type' => 'subscription',
                        'charge_user_id' => 6,
                        'charge_method' => 'recurrent',
                        'fatt_payment_id' => (isset($jsonResponse['id'])) ? $jsonResponse['id'] : '',
                        'is_charge_approved' => $responseStatus,
                        'created_at' => date('Y-m-d H:i:s')
                    ])->business_campaign_payment_id;

                    BusinessCampaingnPaymentLog::create([
                        'business_campaign_payment_id' => $businessCampaignPaymentId,
                        'request' => $jsonData,
                        'response' => json_encode($jsonResponse, true),
                        'created_at' => date('Y-m-d H:i:s'),
                    ])->business_campaign_payment_log_id;

                    //fetch total balance from businessespaymenthistory table
                    if ($responseStatus == "yes") {
                        /*Subscription::where('business_id', $businessId)->update([
                            'is_active' => 'no'
                        ]);*/

                        Subscription::create([
                            'business_id' => $businessId,
                            'subscription_plan_id' => $recuringPayment->subscription_plan_id,
                            'subscription_start' => date('Y-m-01', strtotime('+1 month', strtotime($currentDate))),
                            'subscription_end' => date('Y-m-t', strtotime('+1 month', strtotime($currentDate))),
                            'amount' => $subscriptionPlanDetail->amount,
                            'is_active' => 'yes',
                            'created_user_id' => 6,
                            'created_at' => date('Y-m-d H:i:s')
                        ]);

                        //Added by BK on 05/02/2025 convenience fee email to evey success payment
                        $transactionId = (isset($jsonResponse['id'])) ? $jsonResponse['id'] : '';
                        $businessPaymentObj->moversRechargeEmailNotificationTemplate($businessId, $businessName, $businessCampaignName, $businessEmail, $cardDetail->card, $charge, $convenienceFee, $transactionId);
                    } else {
                        //throw new Exception($jsonResponse['message']);
                    }
                }
                DevDebug::create(['sr_no' => 213, 'result' => 'businessId ' . $businessId, 'created_at' => date('Y-m-d H:i:s')]);
                stop:
            }
        } catch (Exception $e) {
            DevDebug::create(['sr_no' => 214, 'result' => 'Catch error ' . $e->getMessage(), 'created_at' => date('Y-m-d H:i:s')]);
            dd($e->getMessage());
        }
    }

    public function insertLog($srno, $log){
        CronLog::create([
            'log_srno' => $srno,
            'log' => $log,
            'cron_type' => 'recurringpaymentcron',
            'created_at' => date('Y-m-d H:i:s'),
        ]);
    }

    public function sendEmail($data)
    {
        try {

            $postmarkToken  = "************************************";
            $mail           = new TemplateEmail();

            $mail->setFrom('<EMAIL>');
            //$mail->setTo($data['email']);
            $mail->setTo('<EMAIL>,<EMAIL>,<EMAIL>');
            // $mail->setCc('<EMAIL>');
            $mail->setSubject(ucwords($data['business_name']).' - Subscription Recurrent Payment status Inactivated');
            $pageContent    = $this->sendEmailTemplate($data);
            $mail->setHtmlbody($pageContent); // set body content
            $response       = $mail->send_email($postmarkToken, 'recurrent_payment_failed');

            RecurringEmailLog::create([
                "business_id" => $data['business_id'],
                "business_cc_id" => $data['business_cc_id'],
                "subject" => 'Your Subscription Recurring Payment status Inactive',
                "set_to" => '<EMAIL>,<EMAIL>',
                "set_from" => '<EMAIL>',
                "response" => json_encode($response->original['data']),
                'created_at' => date('Y-m-d H:i:s')
            ]);

            return $response;
        } catch (Exception $e) {
            $this->insertLog(1, json_encode($e));
        }
    }

    public function sendEmailTemplate($data) {
        $businessName = ($data['business_id'] > 0) ? $data['business_name']." (".$data['business_id'].")" : "--";
        $campaignName = ($data['campaign_id'] > 0) ? $data['campaign_name']." (".$data['campaign_id'].")" : "--";
        $content = "<!DOCTYPE html>
                    <html>
                        <head>
                            <meta charset=\"ISO-8859-1\">
                            <title>Subscription Recurrent Payment Information</title>
                        </head>
                        <body>
                            <table>
                                <tr><td>Dear ".ucwords($data['business_name'])."</td></tr>
                                <tr><td>Thanks for doing business with us</td></tr>
                                <tr><td>We regret to inform you that your consecutive recurring payment attempt has unfortunately failed. Due to this, we have stopped recurring payments.</td></tr>
                                <tr><td><hr></td></tr>
                                <tr><td>
                    
                                <tr><td><hr></td></tr>
                                <tr><td>Your last Recurrent Payment details are enlisted.</td></tr>
                                <tr>
                                    <td>
                                        <table>
                                            <tr><td colspan=\"3\"><b>Recurrent Payment Info :</b></td></tr>
                                            <tr><td width=\"100px\">Business Name</td><td> : </td><td>".$businessName."</td></tr>
                                            <tr><td>Campaign Name</td><td> : </td><td>".$campaignName."</td></tr>
                                            <tr><td>Recurring Type</td><td> : </td><td>".ucwords($data['recurring_type'])."</td></tr>
                                            <tr><td>Card Number</td><td> : </td><td>".ucwords($data['cardNumber'])."</td></tr>
                                            <tr><td>Last Cancelled/Declined Transaction Details</td><td> : </td>".$data['response']."<td></td></tr>
                    
                                            <tr><td colspan=\"3\">&nbsp;</td></tr>
                                            <tr><td colspan=\"3\">Thank you,</td></tr>
                                            <tr><td colspan=\"3\">Prime Marketing Sales Team</td></tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                        </body>
                    </html>";
        return $content;
    }
}
