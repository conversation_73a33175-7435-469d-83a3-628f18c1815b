<?php

namespace App\Http\Controllers\Emails;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Emails\TemplateEmail;
use App\Exports\MoverJunctionLeadExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Emails\EmailLog;

use PDF;
use Exception;
use DB;
use Log;

class PrimeMarketingMoverJunctionInvoiceEmailController extends Controller
{
    public function primeMoverJunctionInvoiceEmailSend()
    {
        // Store on a different disk (e.g. s3)
        $startDate      = '2025-07-16'; //date("Y-m-d", mktime(0, 0, 0, date("m")-1, 1));
        $endDate        = '2025-07-31'; //date("Y-m-d", mktime(0, 0, 0, date("m"), 0));
        $month          = date('F', strtotime(date('F'))); //date('F', strtotime(date('F') . " last month"));
        $year           = date('Y', strtotime(date('Y') . " last month"));
        $csvfileName    = 'MoverJunction_'.$month.'_'.$year.'.csv';
        $pdffileName    = 'MoverJunction_'.$month.'_'.$year.'.pdf';
        Excel::store(new MoverJunctionLeadExport(), $csvfileName, 'export');
        //Excel::store(new RexDirectLeadExport(), '/export/' . $csvfileName);

        $leadDetail     = DB::select("SELECT MONTH(l.lead_generated_at) AS month, sum(lr.payout) as total_amount, COUNT(l.lead_id) AS total_leads, COUNT(CASE WHEN lm.from_state!=lm.to_state THEN 1 END) as long_leads, COUNT(CASE WHEN lm.from_state=lm.to_state THEN 1 END) as local_leads
                                      FROM lead_routing lr
                                      LEFT JOIN `lead` l on lr.lead_id = l.lead_id
                                      LEFT JOIN campaign c on lr.campaign_id = c.campaign_id
                                      INNER JOIN lead_moving lm ON l.lead_id = lm.lead_id
                                      WHERE lr.campaign_id IN (5503, 5504) AND lr.route_status = 'sold' AND (lr.created_at >= '".$startDate." 00:00:00' AND lr.created_at <= '".$endDate." 23:59:59')
                                      GROUP BY MONTH(l.lead_generated_at)
                                      ORDER BY MONTH(l.lead_generated_at) ASC");
        $leadArray      = [];
        $invoiceMonth   = 0;
        $totalLeads     = 0;
        $totalAmount    = 0;
        if (isset($leadDetail) && !empty($leadDetail)) {
            foreach ($leadDetail as $lead) {
                $invoiceMonth  = $lead->month;
                $totalLeads += $lead->total_leads;
                $totalAmount += $lead->total_amount;
            }
            $leadArray  = array(
                'month' => $invoiceMonth,
                'total_leads' => $totalLeads,
                'total_amount' => $totalAmount
            );
        }
        view()->share('leadDetail', $leadArray);
        $pdf            = PDF::loadView('exports.moverjunctioninvoicepdf', $leadArray);
        $path           = public_path('/export/');
        $pdf->save($path . $pdffileName);

        // dd('sent');
        //send email
        $this->sendEmail(array($csvfileName, $pdffileName), $leadArray);

        //remove image from export directory
        //$this->destroy(array($csvfileName, $pdffileName));
        echo "Invoice email sent successfully."; exit();
    }

    public function sendEmail ($fileName, $leadDetail)
    {
        $subject        = 'Mover Junction Invoice and lead list - ' . date('F', mktime(0, 0, 0, $leadDetail['month'], 10)) . ' ' . date('Y', strtotime(date('Y') . " last month"));
        $setFrom        = '<EMAIL>';
        $setTo          = '<EMAIL>';
        //$setTo        = '<EMAIL>;
        $postmarkToken  = '************************************';

        $mailBodyData   = array();
        $mail           = new TemplateEmail();

        $html           = 'Hello Mover Junction Inc.,<br><br> We want to let you know that Prime Marketing has sent you a Monthly Purchased Leads <strong>Invoice of $' . @number_format($leadDetail['total_amount'], 2) . '</strong> for <strong>' . date('F', mktime(0, 0, 0, $leadDetail['month'], 10)) . ', ' . date('Y', strtotime(date('Y') . " last month")) . '</strong>.<br><br> Please find the attached invoice and purchased lead details.<br><br> Please don\'t hesitate to get in <NAME_EMAIL> if you have any questions or need clarifications.<br><br> Best regards,<br> Client Support,<br> Prime Marketing LLC,<br> <a href="http://mover.primemarketing.us/">www.primemarketing.us</a>';
        // send email
        $mail->setTo($setTo);
        //$mail->setBcc('<EMAIL>');
        $mail->setFrom($setFrom);
        $mail->setSubject($subject);
        $mail->setHtmlbody($html);
        $excelContent   = base64_encode(file_get_contents(public_path() . '/export/' . $fileName[0]));
        $pdfContent     = base64_encode(file_get_contents(public_path() . '/export/' . $fileName[1]));
        $attchment[]    = ['Name' => $fileName[0], 'Content' => $excelContent, 'ContentType' => 'application/excel'];
        $attchment[]    = ['Name' => $fileName[1], 'Content' => $pdfContent, 'ContentType' => 'application/octet-stream'];
        //echo '<pre>'; print_r($attchment); die;
        $mail->setAttachment($attchment);
        $response = $mail->send_email($token = $postmarkToken, 'invoice');

        EmailLog::create([
            "business_id" => 1117,
            "subject" => $subject,
            "set_to" => $setTo,
            "set_from" => $setFrom,
            "response" => json_encode($response->original['data']),
            "type" => 'invoice',
        ]);
    }

    public function destroy($fileName)
    {
        for ($i=0; $i < count($fileName); $i++) {
            $path = app_path("export/" . $fileName[$i]);
            unlink($path);
        }
    }
}
