<?php

namespace App\Http\Controllers\Seller;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Master\MstZipcode;
use App\Models\Master\MstLeadSource;
use App\Models\Master\MstAPIToken;
use App\Models\Lead\LeadMoving;
use App\Models\Logic\LogicDuplicateLead;
use App\Models\Logic\LeadCampaignLogic;
use App\Helpers\CommonFunctions;
use App\Models\Seller\PPSeller;
use App\Models\Seller\PPPing;
use App\Models\Seller\PPPost;
use App\Models\Seller\PPCertificate;
use App\Models\Seller\PPLogs;
use App\Http\Controllers\Seller\PingSubmitController;
use App\Http\Controllers\Seller\SellerBiddingPercentageController;
use App\Http\Controllers\API\Lead\LeadInsert;
use Exception;
use Auth;
use DB;
use Helper;
use DateTime;

class PostSubmitController extends Controller
{
    public function index()
    {
        $error = $moveSize = $moveDate = $ipaddress = $browser = "";
        $status = "Rejected";
        $campaign_type = $postId = $logsId = $leadPayout = $newleadPayout = $leadId = 0;
        $campaignIds = $activeCampAssocDataAr = $todayRouteData = array();
        $dateCurrent = date('Y-m-d');
        $dateTimeCurrent = date('Y-m-d H:i:s');
        try {
            $elements = json_decode(file_get_contents('php://input'), true);
            $logsId = PPLogs::create([
                "request"       => json_encode($elements),
                "type"          => 1,
                "request_time"  => date('Y-m-d H:i:s')
            ])->logs_id;

            //api_key validation
            $apiKey = $elements['api_key'];
            $pingId = $elements['ping_id'];
            $pingObj = new PingSubmitController();
            $sellerArray = $pingObj->checkSellerApiKey(trim($apiKey));
            if ($sellerArray['seller_id'] == 0) {
                throw new Exception("Unauthorized");
            }
            $sellerId = $sellerArray['seller_id'];
            $sellerDetail = PPSeller::where('seller_id', $sellerId)->first();

            if ($sellerId == 4) {
                if ($elements['trusted_from_id'] == "") {
                    throw new Exception("Invalid Certificate");
                }
            }

            //api_key validation
            $pingDetail = PPPing::where('ping_id', $pingId)->where('seller_id', $sellerId)->first();
            if (empty($pingDetail)) {
                throw new Exception("Ping not available");
            }
            $dtStime = strtotime($pingDetail->created_at);
            $dtEtime = strtotime($dateTimeCurrent);
            $diff = abs($dtStime-$dtEtime); //get seconds diff
            //echo $diff; die;
            if ($diff > 60) {
                throw new Exception("Ping request expired");
            }

            $firstName = $elements['firstname'];
            $lastName = $elements['lastname'];
            $email = $elements['email'];
            $phone = $elements['phone'];
            //is_verified
            $isVerified = $elements['is_verified'];
            //from_zip
            $fromZip = $elements['from_zip'];
            $checkFromZip = MstZipcode::where("zipcode", $fromZip)->count();
            if ($checkFromZip == 0) {
                throw new Exception("Invalid From Zip");
            }
            //to_zip
            $toZip = $elements['to_zip'];
            $checkToZip = MstZipcode::where("zipcode", $toZip)->count();
            if ($checkToZip == 0) {
                throw new Exception("Invalid To Zip");
            }
            //is_verified
            $isVerified = $elements['is_verified'];
            $verificationMethod = $elements['verification_method'];
            $ipaddress = $elements['ip'];
            $browser = $elements['browser'];

            $zipcodeData = MstZipcode::read($fromZip, $toZip);
            if (strcmp($fromZip, $toZip) && $toZip) {
                if ($fromZip == $zipcodeData[0]['zipcode']) {
                    $fromData = $zipcodeData[0];
                } elseif (isset($zipcodeData[1])) {
                    $fromData = $zipcodeData[1];
                }
                if ($toZip == $zipcodeData[0]['zipcode']) {
                    $toData = $zipcodeData[0];
                } elseif (isset($zipcodeData[1])) {
                    $toData = $zipcodeData[1];
                }
            } else {
                $fromData = $toData = $zipcodeData[0];
            }
            $fromCity = strtoupper($fromData['city']);
            $fromState = strtoupper($fromData['state']);
            $toCity = strtoupper($toData['city']);
            $toState = strtoupper($toData['state']);
            $fromAreaCode = $fromData['areacode'];
            $toAreaCode = $toData['areacode'];

            //move_size validation
            $moveSize = preg_replace("/\D/", "", trim($elements['move_size']));
            $moveSize = trim($elements['move_size']);
            //move_date
            $moveDate = LeadMoving::getMovedate($elements['move_date']);

            if ($fromZip != $pingDetail->from_zip || $toZip != $pingDetail->to_zip || $moveSize != $pingDetail->move_size || $moveDate != date('m/d/Y', strtotime($pingDetail->move_date)) || $isVerified != $pingDetail->is_verified || $verificationMethod != $pingDetail->verified_method) {
                throw new Exception("Post data does not match with ping");
            }

            if (strpos($email, '@test.com') !== false) {
                throw new Exception("Invalid email");
            }

            $logicDpSource = new LogicDuplicateLead();
            $checkDuplicateSlot = $logicDpSource->checkDuplicate($email, $phone, $leadId, $dateTimeCurrent, $sellerDetail->source_name);
            if(count($checkDuplicateSlot) > 0 )
            {
                if($checkDuplicateSlot[0] == 1)
                {
                    if($checkDuplicateSlot[1] == 4)
                    {
                        throw new Exception("Duplicate Lead in 48 hours with all slot sold");
                    }
                }
            }

            // Logic 1 Check CoverageType (Local, Long Distance)
            if (strcmp($fromState, $toState)) {
                $coverageType = "long";
            } else {
                $coverageType = "local";
            }

            // Logic 2 List all active campaign and businesses based on Logic 1
            $logicCampaign = new LeadCampaignLogic();
            $sqlForCampaign = "SELECT c.*,b.payment_type as business_payment_type,b.credit_available as business_credit_available,b.credit_reserved as business_credit_reserved,pb.business_id AS partner_business_id,pb.business_partner_id, cm.min_distance, cm.move_type FROM `campaign` c
                INNER JOIN `business` b ON b.business_id=c.business_id
                LEFT JOIN `business_partner_mapping` pb ON pb.business_id = b.business_id
                LEFT JOIN `campaign_moving` cm on cm.campaign_id = c.campaign_id";
            $sqlForCampaign .= " WHERE b.status='active' AND b.buyer_type_id <> 3 AND c.is_active='yes'
                AND c.lead_category_id=1 AND c.lead_type_id=1";
            if ($isVerified == "yes" || $isVerified == "1" || $isVerified == 1) {
                $sqlForCampaign .= " AND c.lead_status LIKE '%1%'";
            } else {
                $sqlForCampaign .= " AND c.lead_status LIKE '%0%'";
            }
            $matchCampaignData = DB::select($sqlForCampaign);

            $finalCampDataArr = $campUniqueIdArr = array();
            for ($vf = 0; $vf < count($matchCampaignData); $vf++) {
                if (!in_array($matchCampaignData[$vf]->campaign_id, $campUniqueIdArr)) {
                    $finalCampDataArr[] = $matchCampaignData[$vf];
                    $campUniqueIdArr[] = $matchCampaignData[$vf]->campaign_id;
                }
            }

            foreach ($finalCampDataArr as $key => $val) {
                //echo $val->campaign_id."==".$coverageType."==".$val->move_type."==".$val->lead_category_id."<br>";
                if (isset($val->move_type) && $val->move_type != $coverageType && $val->lead_category_id != 2) {
                    //echo $val->campaign_id;
                    unset($finalCampDataArr[$key]); //Uncommnent here
                }
            }
            $finalCampDataArr = array_values($finalCampDataArr);

            for ($d = 0; $d < count($finalCampDataArr); $d++) {
                $activeCampAssocDataAr[$finalCampDataArr[$d]->campaign_id] = $finalCampDataArr[$d];
                //echo "<pre>";print_r($activeCampAssocDataAr);die;
                if (!in_array($finalCampDataArr[$d]->campaign_id, $campaignIds)) {
                    //Start For Check Campaign Fund Logic
                    $payout = CommonFunctions::getCampaignPayout($finalCampDataArr[$d]->campaign_id, $coverageType, 0, 0);
                    if ($payout > 0) {
                        //echo "<pre>";print_r($finalCampDataArr[$d]);die;
                        if ($finalCampDataArr[$d]->business_payment_type == 'pre') { // Check if business payment_type is PrePayment
                            if ($finalCampDataArr[$d]->payment_type == 0) { // business level balance;
                                if (($finalCampDataArr[$d]->business_credit_available - $finalCampDataArr[$d]->business_credit_reserved) >= $payout && $payout > 0) {
                                    $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                                }
                            } else { // campaign level balance
                                if (($finalCampDataArr[$d]->credit_available - $finalCampDataArr[$d]->credit_reserved) >= $payout && $payout > 0) {
                                    $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                                }
                            }
                        } else { // If payment_type is PostPayment then no need to check credit availibility
                            $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                        }
                    }
                    //End For Check Campaign Fund Logic
                }
            }

            //Start For Check Campaign Exclude Logic
            $campaignIds = $logicCampaign->checkCampaignExcludedDates($campaignIds, $activeCampAssocDataAr, $moveDate);

            //Start For Check Campaign Per Hour Lead Route Limit Logic
            $campaignIds = $logicCampaign->checkTiming($campaignIds, $dateCurrent, $dateTimeCurrent, $coverageType, $todayRouteData);

            //Start For Check Campaign Daily Limit,Budget Limit and Hourly Limit Logic
            $campaignIds = $logicCampaign->checkLimit($campaignIds, $activeCampAssocDataAr, $coverageType, 0, 0);

            $campaignIds = $logicCampaign->checkFromZip($campaignIds, $activeCampAssocDataAr, $fromZip, $toZip, $fromState, $toState, $fromAreaCode, $toAreaCode);

            $campaignIds = $logicCampaign->checkCampaignLogic($campaignIds, $coverageType, $dateCurrent, $dateTimeCurrent, 4, 0, 0, 0, 0, 1);

            $payout = 0;
            if (count($campaignIds) > 0) { foreach($campaignIds as $campaignId => $score) {
                $payout += CommonFunctions::getCampaignPayout($campaignId, $coverageType, 0, 0);
            } }

            $percentageObj = new SellerBiddingPercentageController();
            $percentage = $percentageObj->sellerBiddingPercentage($coverageType);
            $leadPayout = ($payout * ($percentage + $sellerDetail->bidding_percentage)) / 100;
            //Ping-Post: Change from 75% to 80% fix Bidding percentage for "Make It Your Move" company on 14/09/2022
            /*if ($sellerId == 3) {
                $leadPayout = ($payout * 80) / 100;
            }*/
            //Check Lead Sold Count For MYM Selller
            //Logic for Check Dynamic Local Lead Limit for Seller Added By BK on 31/01/2023
            if ($coverageType=="local" && $sellerDetail->daily_local_limit > 0) {
                $originId = MstLeadSource::where('lead_source_name', $sellerDetail->source_name)->get('lead_source_id')->first();
                /*$totlaLead= Leads::with(['moveInfo' => function($query) {
                    $query->where('from_state', '=', 'to_state');
                }])->where('origin_id', $originId->id)->whereBetween('date_received', [$dateCurrent, $dateCurrent])->count('*');*/
                $totlaLead = DB::select("SELECT COUNT(l.lead_id) AS total_lead FROM `lead` l INNER JOIN `lead_moving` lm ON l.lead_id = lm.lead_id WHERE l.lead_source_id = '".$originId->lead_source_id."' AND lm.from_state = lm.to_state AND l.lead_generated_at BETWEEN '".$dateCurrent."' AND '".$dateCurrent."'");
                if ($totlaLead[0]->total_lead >= $sellerDetail->daily_local_limit) {
                    $leadPayout = 0;
                }
            }

            //when Daily Local Limit Status is Inactive leadPayout=0 Added By BK on 31/01/2023
            if ($coverageType=="local" && $sellerDetail->daily_local_limit_status == 0) {
                $leadPayout = 0;
            }

            //Logic for Check Dynamic Local Lead Limit for Seller Added By BK on 31/01/2023
            if ($coverageType=="long" && $sellerDetail->daily_long_limit > 0) {
                $originId = MstLeadSource::where('lead_source_name', $sellerDetail->source_name)->get('lead_source_id')->first();
                $totlaLead = DB::select("SELECT COUNT(l.lead_id) AS total_lead FROM `lead` l INNER JOIN `lead_moving` lm ON l.lead_id = lm.lead_id WHERE l.lead_source_id = '".$originId->lead_source_id."' AND lm.from_state <> lm.to_state AND l.lead_generated_at BETWEEN '".$dateCurrent."' AND '".$dateCurrent."'");
                if ($totlaLead[0]->total_lead >= $sellerDetail->daily_long_limit) {
                    $leadPayout = 0;
                }
            }

            $tokenDetail = MstAPIToken::where('source', $sellerDetail->source_name)->first();
            $checkToken = DB::select("SELECT api_token_id, SUBSTR(source, 1) AS source, token FROM mst_api_token WHERE token = '".$tokenDetail->token."'");
            if (empty($checkToken)) {
                throw new Exception("Unauthorized");
            }

            //when Daily Long Limit Status is Inactive leadPayout=0 Added By BK on 31/01/2023
            if ($coverageType=="long" && $sellerDetail->daily_long_limit_status == 0) {
                $leadPayout = 0;
            }

            //for LS Local Distance Leads = 10$ and Long Distance Leads = 32$ Added By BK on 05/11/2023
            //comment by BK on 22/05/2023
            /*if ($sellerId == 7) {
                if ($coverageType=="local") {
                    if ($payout < 10) {
                        $leadPayout = 0;
                    } else {
                        $leadPayout = 10;
                    }
                } else if ($coverageType=="long") {
                    if ($payout < 32) {
                        $leadPayout = 0;
                    } else {
                        $leadPayout = 32;
                    }
                }
            }*/

            if ($payout >= (($pingDetail->payout * 90) / 100)) {
                $leadData = array(
                    "token"             => $tokenDetail->token,
                    "origin"            => $sellerDetail->source_name,
                    "utm_campaign"      => "",
                    "utm_medium"        => "",
                    "campaign_id"       => 0,
                    "ad_group_id"       => "",
                    "ad_group"          => 0,
                    "keyword"           => "",
                    "device"            => "",
                    "GCLID"             => "",
                    "Landing_Page"      => "snapit",
                    "from_zip"          => $fromZip,
                    "to_zip"            => $toZip,
                    "move_date"         => date('Y-m-d', strtotime($moveDate)),
                    "move_size"         => $moveSize,
                    "name"              => $firstName.' '.$lastName,
                    "email"             => $email,
                    "phone"             => $phone,
                    "is_verified"       => $isVerified,
                    "IP"                => $ipaddress,
                    "generated_at"      => date("Y-m-d H:i:s"),
                    "is_ping_post"      => 1,
                    "lead_id"           => 0
                );
                $leadInsertObj = new LeadInsert;
                $response = $leadInsertObj->InsertLead(json_encode($leadData));
                $response = json_decode($response, true);
                //print_r($response); die;
                $error = $response['error'];
                $leadId = $response['lead_id'];
                //ob_end_clean();
            } else {
                $error = "Payout changed from ping";
            }

            $status = "Accepted";
            //echo $leadPayout; die;
            if (!empty($error)) {
                $status = "Rejected";
                $leadPayout = 0;
            }
            $postId = PPPost::create([
                "api_key"               => $apiKey,
                "seller_id"             => $sellerId,
                "ping_id"               => $pingId,
                "lead_id"               => $leadId,
                "fname"                 => $firstName,
                "lname"                 => $lastName,
                "email"                 => $email,
                "phone"                 => $phone,
                "from_zip"              => $fromZip,
                "to_zip"                => $toZip,
                "move_size"             => $moveSize,
                "move_date"             => date('Y-m-d', strtotime($moveDate)),
                "buyer_amount"          => $leadPayout,
                "payout"                => $payout,
                "result"                => $status,
                "fail_reason"           => $error,
                "is_verified"           => (string)$isVerified,
                "verified_method"       => $verificationMethod,
                "created_at"            => date('Y-m-d H:i:s'),
            ])->id;

            if ($sellerId == 4) {
                PPCertificate::create([
                    "seller_id"         => $sellerId,
                    "post_id"           => $postId,
                    "trusted_from_id"   => $elements['trusted_from_id'],
                    "landing_page_url"  => $elements['landing_page_url'],
                    "tcpa_consent_language"=> $elements['tcpa_consent_language'],
                    "created_at"        => date('Y-m-d H:i:s'),
                ])->id;
            }

        } catch (Exception $e) {
            $error = $e->getMessage();
        }

        $responseArray = [
            'status'                    => $status,
            'post_id'                   => $postId,
            'payout'                    => $leadPayout,
            'error'                     => $error
        ];

        PPLogs::where('logs_id', $logsId)->update([
            "type_id"                   => $postId,
            "response"                  => json_encode($responseArray),
            "response_time"             => date('Y-m-d H:i:s'),
            "ip"                        => $ipaddress,
            "browser"                   => $browser
        ]);
        return response()->json($responseArray);
    }
}
