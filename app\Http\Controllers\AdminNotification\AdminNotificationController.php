<?php

namespace App\Http\Controllers\AdminNotification;

use App\Helpers\CommonFunctions;
use App\Http\Controllers\Controller;
use App\Models\Business\Business;
use App\Models\Campaign\Campaign;
use App\Models\Lead\Lead;
use App\Models\Lead\LeadCarTransport;
use App\Models\Lead\LeadHeavyLifting;
use App\Models\Lead\LeadMoving;
use App\Models\Lead\LeadRouting;
use App\Models\Master\MstLeadCategory;
use App\Models\Master\MstLeadSource;
use App\Models\Notification\Notification;
use App\Models\Notification\NotificationBusinessOrCampaign;
use App\Models\Notification\NotificationLeadCategory;
use App\Models\Notification\NotificationOption;
use App\Models\Notification\NotificationSmsEmailLog;
use App\Models\Notification\NotificationSource;
use App\Models\Notification\NotificationSubSource;
use App\Models\Notification\UserNotification;
use App\Models\Lead\LeadActivity;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class AdminNotificationController extends Controller
{

    public function index(Request $request)
    {
        $userNameArray = $finalArray = [];
        $endDecObj                          = new CommonFunctions;
        $userDetail                         = User::get(['id', 'name'])->toArray();
        for ($u = 0; $u < count($userDetail); $u++) {
            $userNameArray[$userDetail[$u]['id']] = $userDetail[$u]['name'];
        }

        $list                               = Notification::orderBy('notification_id', 'DESC')->get();
        $i                                  = 1;
        $array                              = array('ca2', 'ca3');
        foreach ($list as $key => $singleList) {
            if($singleList->module_name == 'routing') {
                $main_module_name           = 'campaign/business routing';
            }
            else if($singleList->module_name == 'lead_in_project') {
                $main_module_name           = 'lead_in_linkup';
            }
            else {
                $main_module_name           = $singleList->module_name;
            }
            //$main_module_name = $singleList->module_name == 'routing' ? 'campaign/business routing' : $singleList->module_name;
            $finalArray[$key]               = array(
                'id'                        => $singleList->notification_id,
                'notification_id'           => $endDecObj->customeEncryption($singleList->notification_id),
                'notification_name'         => $singleList->notification_name,
                'module_name'               => str_replace('_', ' ', $main_module_name),
                'lead_condition_datatype'   => str_replace('_', ' ', str_replace($array, '', $singleList->lead_condition_datatype)),
                'start_time'                => $singleList->start_time,
                'end_time'                  => $singleList->end_time,
                'how_often'                 => str_replace('_', ' ', $singleList->how_often),
                'data_selection'            => str_replace('_', ' ', $singleList->data_selection),
                'status'                    => $singleList->status,
                'created_by'                => ($userNameArray[$singleList->created_user_id]) ?? '--',
            );
        }
        return view("adminnotification.list")->with('list', $finalArray);
    }

    public function addNotification()
    {
        // case 1
        $leadCategory = MstLeadCategory::get();
        $leadSource = MstLeadSource::whereIn('lead_source_name', ['VM', 'QTM','IS'])->get();
        $allUser = User::get();
        return view("adminnotification.add", compact('leadCategory', 'leadSource', 'allUser'));
    }

    public function subSource(Request $request)
    {
        $data = $request->source;
        $response = [];
        if($data){
            foreach ($data as $key => $value) {
                $fetchData = MstLeadSource::where('lead_source_id', $value)->first();
                if ($fetchData) {
                    $fetchRecords = MstLeadSource::where('lead_source_name', 'like', $fetchData->lead_source_name . '%')->get()->toArray();
                    foreach ($fetchRecords as $reckey => $recvalue) {
                        //$response[$recvalue['lead_source_id']] = $recvalue['lead_source_name'];
                        $response[] = [
                            'lead_source_id'    => $recvalue['lead_source_id'],
                            'lead_source_name'  => $recvalue['lead_source_name'],
                        ];
                    }
                }
            }
            if (!empty($response)) {
                $responseData = [
                    'status' => 1,
                    'message' => 'success',
                    'data' => $response,
                    'error' => array(),

                ];
                return json_encode($responseData);
            } else {
                $responseData = [
                    'status' => 0,
                    'message' => 'Sub source not found',
                    'error' => array(),

                ];
                return json_encode($responseData);
            }
        } else {
            $responseData = [
                'status' => 0,
                'message' => 'Sub source not found',
                'error' => array(),

            ];
            return json_encode($responseData);
        }

    }

    public function saveNotification(Request $request)
    {
        $status = 0;
        $message = 'fail';
        $error = array();

        $rules1 = $rules2  = $rules3 = array();
        $customMessage1 = $customMessage2  = $customMessage3 = array();
        $input1 = $input2  = $input3 = array();

        $mainRules = array(
            'notification_name' => 'required',
            'moduleType' => 'required|in:lead_in_project,lead_routing,routing',
            'status' => 'required|in:active,inactive',
            'start_time' => 'required',
            'end_time' => 'required|gt:start_time',
            'how_often' => 'required',
            'data_selection' => 'required',

        );

        $customMessage = array(
            'notification_name.required' => 'The notification name field is required.',
            'moduleType.required' => 'The module type field is required.',
            'status.required' => 'The status field is required.',
            'start_time.required' => 'The start time field is required.',
            'end_time.required' => 'The end time field is required.',
            'how_often.required' => 'The how often field is required.',
            'data_selection.required' => 'The data selection field is required.',

        );

        $mainInput = $request->only(['notification_name', 'moduleType', 'status', 'start_time', 'end_time', 'how_often', 'data_selection']);


        if ($request->moduleType == 'lead_in_project') {
            $rules1 = array(
                'lead_category1' => 'required',
                'lead_source1' => 'required',
                //'lead_sub_source1' => 'required',
                'lead_verified' => 'required',
                'lead_condition_datatype1' => 'required',
                'lead_condition1' => 'required',
                'lead_condition_value1' => 'required',
            );
            $customMessage1 = array(
                'lead_category1.required' => 'The lead category field is required.',
                'lead_source1.required' => 'The lead source field is required.',
                //'lead_sub_source1.required' => 'The lead sub source field is required.',
                'lead_verified.required' => 'The verified lead field is required.',
                'lead_condition_datatype1.required' => 'The lead condition datatype field is required.',
                'lead_condition1.required' => 'The lead condition field is required.',
                'lead_condition_value1.required' => 'The value field is required.',
            );
            $input1 = $request->only(['lead_category1', 'lead_source1', 'lead_sub_source1', 'lead_verified', 'lead_condition_datatype1', 'lead_condition1', 'lead_condition_value1']);
        } else if ($request->moduleType == 'lead_routing') {
            $rules2 = array(
                'lead_category2' => 'required',
                'lead_source2' => 'required',
                //'lead_sub_source2' => 'required',
                'sold_type' => 'required',
                'lead_condition2' => 'required',
                'lead_condition_value2' => 'required',
            );
            $customMessage2 = array(
                'lead_category2.required' => 'The lead category field is required.',
                'lead_source2.required' => 'The lead source field is required.',
                //'lead_sub_source2.required' => 'The lead sub source field is required.',
                'sold_type.required' => 'The sold type field is required.',
                'lead_condition2.required' => 'The lead condition field is required.',
                'lead_condition_value2.required' => 'The value field is required.',
            );
            $input2 = $request->only(['lead_category2', 'lead_source2', 'lead_sub_source2', 'sold_type', 'lead_condition2', 'lead_condition_value2']);
        } else if ($request->moduleType == 'routing') {
            $rules3 = array(
                'lead_category3' => 'required',
                'businness_or_campaign' => 'required|in:business,campaign',
                'business_ids' => 'required_if:businness_or_campaign,==,business',
                'campaign_ids' => 'required_if:businness_or_campaign,==,campaign',
                'lead_condition_sold_type' => 'required',
                'lead_condition3' => 'required',
                'lead_condition_value3' => 'required',
            );
            $customMessage3 = array(
                'lead_category3.required' => 'The lead category field is required.',
                'businness_or_campaign.required' => 'The busienss or campaign field is required.',
                'business_ids.required_if' => 'The business list is required when business or campaign is business.',
                'campaign_ids.required_if' => 'The campaign list is required when business or campaign is campaign.',
                'lead_condition_sold_type.required' => 'The lead sold type field is required.',
                'lead_condition3.required' => 'The lead condition field is required.',
                'lead_condition_value3.required' => 'The value field is required.',
            );
            $input3 = $request->only(['lead_category3', 'businness_or_campaign', 'business_ids', 'campaign_ids', 'lead_condition_sold_type', 'lead_condition3', 'lead_condition_value3']);
        }
        $finalRules = array_merge($mainRules, $rules1, $rules2, $rules3);
        $finalUnput = array_merge($mainInput, $input1, $input2, $input3);
        $finalMessages = array_merge($customMessage, $customMessage1, $customMessage2, $customMessage3);
        $validator = Validator::make($finalUnput, $finalRules, $finalMessages);
        if ($validator->fails()) {
            $error = $validator->errors();
            $responseData = ['status' => $status, 'message' => $message, 'error' => $error];
            return response()->json($responseData);
        } else {
            if ($request->option_email) {
                $final_option_email = explode(',', $request->option_email);
                $isEmailValid = false;
                foreach ($final_option_email as $key => $value) {
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $isEmailValid = true;
                        break;
                    }
                }
                if ($isEmailValid) {
                    $errorArr['notificationOptionEmail'] = "Invalid email address in list";
                    $responseData = ['status' => $status, 'message' => $message, 'error' => $errorArr];
                    return response()->json($responseData);
                }
            } else {
                $final_option_email = [];
            }

            if ($request->option_sms) {
                $final_option_sms = explode(',', $request->option_sms);
                $isPhoneInvalid = $isPhoneInvalidValidation = false;
                foreach ($final_option_sms as $key => $value) {
                    if (!preg_match('/^[0-9]{10}+$/', $value)) {
                        $isPhoneInvalidValidation = true;
                        break;
                    }
                    $phoneCheck = array_unique(str_split(trim($value)));
                    if (count($phoneCheck) === 1) {
                        $isPhoneInvalid = true;
                        break;
                    }
                }
                if ($isPhoneInvalidValidation) {
                    $errorArr['notificationOptionSms'] = "Invalid number in list.";
                    $responseData = ['status' => $status, 'message' => $message, 'error' => $errorArr];
                    return response()->json($responseData);
                }
                if ($isPhoneInvalid) {
                    $errorArr['notificationOptionSms'] = "Same number repeated in list.";
                    $responseData = ['status' => $status, 'message' => $message, 'error' => $errorArr];
                    return response()->json($responseData);
                }
            } else {
                $final_option_sms = [];
            }

            if (!empty($request->option_users)) {
                $final_option_users = $request->option_users;
            } else {
                $final_option_users = [];
            }

            if (count($final_option_email) + count($final_option_sms) + count($final_option_users) <= 0) {
                $errorArr['notificationOption'] = "Email,SMS and User any one filed have atleast one value";
                $responseData = ['status' => $status, 'message' => $message, 'error' => $errorArr];
                return response()->json($responseData);
            }

            if ($request->moduleType == 'lead_in_project') {
                $data = [
                    'notification_name' => $request->notification_name,
                    'module_name' => $request->moduleType,
                    'lead_verified' => $request->lead_verified,
                    'lead_condition_datatype' => $request->lead_condition_datatype1,
                    'lead_condition' => $request->lead_condition1,
                    'lead_condition_value' => $request->lead_condition_value1,
                    'start_time' => $request->start_time,
                    'end_time' => $request->end_time,
                    'how_often' => $request->how_often,
                    'data_selection' => $request->data_selection,
                    'status' => $request->status,
                    'created_user_id' => Auth::user()->id
                ];
                $notification = Notification::create($data);
                if (!empty($request->lead_category1)) {
                    foreach ($request->lead_category1 as $key => $value) {
                        NotificationLeadCategory::updateOrCreate([
                            'notification_id' => $notification->notification_id,
                            'category_id' => $value
                        ], [
                            'notification_id' => $notification->notification_id,
                            'category_id' => $value
                        ]);
                    }
                }
                if (!empty($request->lead_source1)) {
                    foreach ($request->lead_source1 as $key => $value) {
                        NotificationSource::updateOrCreate([
                            'notification_id' => $notification->notification_id,
                            'source_id' => $value
                        ], [
                            'notification_id' => $notification->notification_id,
                            'source_id' => $value
                        ]);
                    }
                }
                if (!empty($request->lead_sub_source1)) {
                    foreach ($request->lead_sub_source1 as $key => $value) {
                        NotificationSubSource::updateOrCreate([
                            'notification_id' => $notification->notification_id,
                            'sub_source_id' => $value
                        ], [
                            'notification_id' => $notification->notification_id,
                            'sub_source_id' => $value
                        ]);
                    }
                }
            } else if ($request->moduleType == 'lead_routing') {
                $data = [
                    'notification_name' => $request->notification_name,
                    'module_name' => $request->moduleType,
                    'lead_sold_type' => $request->sold_type,
                    'lead_condition_datatype' => 'ca2_lead_count',
                    'lead_condition' => $request->lead_condition2,
                    'lead_condition_value' => $request->lead_condition_value2,
                    'start_time' => $request->start_time,
                    'end_time' => $request->end_time,
                    'how_often' => $request->how_often,
                    'data_selection' => $request->data_selection,
                    'status' => $request->status,
                    'created_user_id' => Auth::user()->id
                ];
                $notification = Notification::create($data);
                if (!empty($request->lead_category2)) {
                    foreach ($request->lead_category2 as $key => $value) {
                        NotificationLeadCategory::updateOrCreate([
                            'notification_id' => $notification->notification_id,
                            'category_id' => $value
                        ], [
                            'notification_id' => $notification->notification_id,
                            'category_id' => $value
                        ]);
                    }
                }
                if (!empty($request->lead_source2)) {
                    foreach ($request->lead_source2 as $key => $value) {
                        NotificationSource::updateOrCreate([
                            'notification_id' => $notification->notification_id,
                            'source_id' => $value
                        ], [
                            'notification_id' => $notification->notification_id,
                            'source_id' => $value
                        ]);
                    }
                }
                if (!empty($request->lead_sub_source2)) {
                    foreach ($request->lead_sub_source2 as $key => $value) {
                        NotificationSubSource::updateOrCreate([
                            'notification_id' => $notification->notification_id,
                            'sub_source_id' => $value
                        ], [
                            'notification_id' => $notification->notification_id,
                            'sub_source_id' => $value
                        ]);
                    }
                }
            } else if ($request->moduleType == 'routing') {
                $data = [
                    'notification_name' => $request->notification_name,
                    'module_name' => $request->moduleType,
                    'lead_condition_sold_type' => $request->lead_condition_sold_type,
                    'lead_condition_datatype' => 'ca3_lead_sold',
                    'lead_condition' => $request->lead_condition3,
                    'lead_condition_value' => $request->lead_condition_value3,
                    'start_time' => $request->start_time,
                    'end_time' => $request->end_time,
                    'how_often' => $request->how_often,
                    'data_selection' => $request->data_selection,
                    'status' => $request->status,
                    'routing_on' => $request->businness_or_campaign,
                    'created_user_id' => Auth::user()->id
                ];
                $notification = Notification::create($data);
                if (!empty($request->lead_category3)) {
                    foreach ($request->lead_category3 as $key => $value) {
                        NotificationLeadCategory::updateOrCreate([
                            'notification_id' => $notification->notification_id,
                            'category_id' => $value
                        ], [
                            'notification_id' => $notification->notification_id,
                            'category_id' => $value
                        ]);
                    }
                }
                if (!empty($request->business_ids) && ($request->businness_or_campaign == 'business')) {
                    foreach ($request->business_ids as $key => $value) {
                        NotificationBusinessOrCampaign::updateOrCreate([
                            'notification_id' => $notification->notification_id,
                            'type' => 'business',
                            'business_id' => $value
                        ], [
                            'notification_id' => $notification->notification_id,
                            'type' => 'business',
                            'business_id' => $value
                        ]);
                    }
                }
                if (!empty($request->campaign_ids) && ($request->businness_or_campaign == 'campaign')) {
                    foreach ($request->campaign_ids as $key => $value) {
                        NotificationBusinessOrCampaign::updateOrCreate([
                            'notification_id' => $notification->notification_id,
                            'type' => 'campaign',
                            'campaign_id' => $value
                        ], [
                            'notification_id' => $notification->notification_id,
                            'type' => 'campaign',
                            'campaign_id' => $value
                        ]);
                    }
                }
            } else {
            }
            if (!empty($final_option_email)) {
                foreach ($final_option_email as $key => $value) {
                    NotificationOption::updateOrCreate([
                        'notification_id' => $notification->notification_id,
                        'option_type' => 'email',
                        'option_value' => $value
                    ], [
                        'notification_id' => $notification->notification_id,
                        'option_type' => 'email',
                        'option_value' => $value
                    ]);
                }
            }

            if (!empty($final_option_sms)) {
                foreach ($final_option_sms as $key => $value) {
                    NotificationOption::updateOrCreate([
                        'notification_id' => $notification->notification_id,
                        'option_type' => 'sms',
                        'option_value' => $value
                    ], [
                        'notification_id' => $notification->notification_id,
                        'option_type' => 'sms',
                        'option_value' => $value
                    ]);
                }
            }

            if (!empty($request->option_users)) {
                foreach ($request->option_users as $key => $value) {
                    NotificationOption::updateOrCreate([
                        'notification_id' => $notification->notification_id,
                        'option_type' => 'user',
                        'option_value' => $value
                    ], [
                        'notification_id' => $notification->notification_id,
                        'option_type' => 'user',
                        'option_value' => $value
                    ]);
                }
            }

            $status = 1;
            $message = 'Success';
            $request->session()->flash('message', $message);
            $responseData = ['status' => $status, 'message' => $message, 'error' => $error];
            return response()->json($responseData);
        }
    }

    public function changeNotificationStatus(Request $request)
    {
        $input = $request->all();
        //echo "<pre>";print_r($input);die;
        $status = 0;
        $message = 'fail';
        $error = array();
        try {
            if (Auth::check()) {
                $commonFunctionsObj = new CommonFunctions;
                $getId = $commonFunctionsObj->customeDecryption($request->input('userId'));
                if ($getId > 0) {
                    if ($request->status == 'inactive') {
                        $status = 'active';
                        Notification::where("notification_id", $getId)->update(['status' => $status]);
                        $message = 'Status Activated successfully';
                        $responseData = ['status' => 1, 'message' => $message, 'error' => $error];
                        return response()->json($responseData);
                    } else if ($request->status == 'active') {
                        $status = 'inactive';
                        Notification::where("notification_id", $getId)->update(['status' => $status]);
                        $message = 'Status DeActivated successfully';
                        $responseData = ['status' => 1, 'message' => $message, 'error' => $error];
                        return response()->json($responseData);
                    }
                }
                $responseData = ['status' => $status, 'message' => $message, 'error' => $error];
                return response()->json($responseData);
            } else {
                return redirect('/logout');
            }
        } catch (Exception $ex) {
            dd($ex);
            return redirect('/notification-list');
        }
    }

    public function changeReadUnread(Request $request)
    {
        $status = 0;
        $message = 'fail';
        $error = array();
        try {
            if (Auth::check()) {
                $findNotification = UserNotification::where(['user_notification_id' => $request->notification_id])->first();
                if (!$findNotification) {
                    $message = "Notification not found";
                    $responseData = ['status' => $status, 'message' => $message, 'error' => $error];
                    return response()->json($responseData);
                } else {
                    $findNotification->is_read = 'yes';
                    $findNotification->save();
                    $status = 1;
                    $message = 'Success';
                    $notificationCount = UserNotification::where(['user_id' => Auth::user()->id, 'is_read' => 'no'])->orderBy('is_read', 'DESC')->orderBy('user_notification_id', 'DESC')->take(10)->count();
                    $responseData = ['status' => $status, 'message' => $message, 'error' => $error, 'count' => $notificationCount, 'is_remove_main_dot' => $notificationCount <= 0 ? true : false];
                    return response()->json($responseData);
                }
            } else {
                return redirect('/logout');
            }
        } catch (Exception $ex) {
            dd($ex);
        }
    }

    public function deleteNotification(Request $request)
    {
        $status = 0;
        $message = 'fail';
        $error = array();
        try {
            $endDecObj = new CommonFunctions;
            $id = $endDecObj->customeDecryption($request->id);
            $findRecord = Notification::where(['notification_id' => $id])->first();
            if (!$findRecord) {
                $message = "Record not found";
                $responseData = ['status' => $status, 'message' => $message, 'error' => $error];
                return response()->json($responseData);
            }
            DB::beginTransaction();
            // remove data
            NotificationBusinessOrCampaign::where("notification_id", $id)->delete();
            NotificationLeadCategory::where("notification_id", $id)->delete();
            NotificationOption::where("notification_id", $id)->delete();
            NotificationSmsEmailLog::where("notification_id", $id)->delete();
            NotificationSource::where("notification_id", $id)->delete();
            NotificationSubSource::where("notification_id", $id)->delete();
            Notification::where("notification_id", $id)->delete();
            DB::commit();
            $status = 1;
            $message = 'Deleted Successfully';
            $responseData = ['status' => $status, 'message' => $message, 'error' => $error];
            return response()->json($responseData);
        } catch (Exception $ex) {
            dd($ex);
            return redirect('/notification-list');
        }
    }

    public function editNotification($id)
    {
        $endDecObj = new CommonFunctions;
        $findId = $endDecObj->customeDecryption($id);
        $findData = Notification::with(['LeadCategory', 'LeadSource', 'LeadSubSource', 'Business', 'Campaign', 'OptionValueEmail', 'OptionValueSms', 'OptionValueUser'])->where(['notification_id' => $findId])->first();
        if (!$findData) {
        }
        $leadCategory = MstLeadCategory::get();
        $leadSource = MstLeadSource::whereIn('lead_source_name', ['VM', 'QTM','IS'])->get();
        $allUser = User::get();
        return view("adminnotification.edit", compact('leadCategory', 'leadSource', 'allUser', 'findData'));
    }

    public function updateNotification(Request $request)
    {
        $findData = Notification::where(['notification_id' => $request->notification_id])->first();
        if (!$findData) {
        }
        $status = 0;
        $message = 'fail';
        $error = array();

        $rules1 = $rules2  = $rules3 = array();
        $customMessage1 = $customMessage2  = $customMessage3 = array();
        $input1 = $input2  = $input3 = array();

        $mainRules = array(
            'notification_name' => 'required',
            'moduleType' => 'required|in:lead_in_project,lead_routing,routing',
            'status' => 'required|in:active,inactive',
            'start_time' => 'required',
            'end_time' => 'required',
            'how_often' => 'required',
            'data_selection' => 'required',

        );

        $customMessage = array(
            'notification_name.required' => 'The notification name field is required.',
            'moduleType.required' => 'The module type field is required.',
            'status.required' => 'The status field is required.',
            'start_time.required' => 'The start time field is required.',
            'end_time.required' => 'The end time field is required.',
            'how_often.required' => 'The how often field is required.',
            'data_selection.required' => 'The data selection field is required.',

        );

        $mainInput = $request->only(['notification_name', 'moduleType', 'status', 'start_time', 'end_time', 'how_often', 'data_selection']);


        if ($request->moduleType == 'lead_in_project') {
            $rules1 = array(
                'lead_category1' => 'required',
                'lead_source1' => 'required',
                //'lead_sub_source1' => 'required',
                'lead_verified' => 'required',
                'lead_condition_datatype1' => 'required',
                'lead_condition1' => 'required',
                'lead_condition_value1' => 'required',
            );
            $customMessage1 = array(
                'lead_category1.required' => 'The lead category field is required.',
                'lead_source1.required' => 'The lead source field is required.',
                //'lead_sub_source1.required' => 'The lead sub source field is required.',
                'lead_verified.required' => 'The verified lead field is required.',
                'lead_condition_datatype1.required' => 'The lead condition datatype field is required.',
                'lead_condition1.required' => 'The lead condition field is required.',
                'lead_condition_value1.required' => 'The value field is required.',
            );
            $input1 = $request->only(['lead_category1', 'lead_source1', 'lead_sub_source1', 'lead_verified', 'lead_condition_datatype1', 'lead_condition1', 'lead_condition_value1']);
        } else if ($request->moduleType == 'lead_routing') {
            $rules2 = array(
                'lead_category2' => 'required',
                'lead_source2' => 'required',
                'lead_sub_source2' => 'required',
                'sold_type' => 'required',
                'lead_condition2' => 'required',
                'lead_condition_value2' => 'required',
            );
            $customMessage2 = array(
                'lead_category2.required' => 'The lead category field is required.',
                'lead_source2.required' => 'The lead source field is required.',
                'lead_sub_source2.required' => 'The lead sub source field is required.',
                'sold_type.required' => 'The sold type field is required.',
                'lead_condition2.required' => 'The lead condition field is required.',
                'lead_condition_value2.required' => 'The value field is required.',
            );
            $input2 = $request->only(['lead_category2', 'lead_source2', 'lead_sub_source2', 'sold_type', 'lead_condition2', 'lead_condition_value2']);
        } else if ($request->moduleType == 'routing') {
            $rules3 = array(
                'lead_category3' => 'required',
                'businness_or_campaign' => 'required|in:business,campaign',
                'business_ids' => 'required_if:businness_or_campaign,==,business',
                'campaign_ids' => 'required_if:businness_or_campaign,==,campaign',
                'lead_condition_sold_type' => 'required',
                'lead_condition3' => 'required',
                'lead_condition_value3' => 'required',
            );
            $customMessage3 = array(
                'lead_category3.required' => 'The lead category field is required.',
                'businness_or_campaign.required' => 'The busienss or campaign field is required.',
                'business_ids.required_if' => 'The business list is required when business or campaign is business.',
                'campaign_ids.required_if' => 'The campaign list is required when business or campaign is campaign.',
                'lead_condition_sold_type.required' => 'The lead sold type field is required.',
                'lead_condition3.required' => 'The lead condition field is required.',
                'lead_condition_value3.required' => 'The value field is required.',
            );
            $input3 = $request->only(['lead_category3', 'businness_or_campaign', 'business_ids', 'campaign_ids', 'lead_condition_sold_type', 'lead_condition3', 'lead_condition_value3']);
        }
        $finalRules = array_merge($mainRules, $rules1, $rules2, $rules3);
        $finalUnput = array_merge($mainInput, $input1, $input2, $input3);
        $finalMessages = array_merge($customMessage, $customMessage1, $customMessage2, $customMessage3);
        $validator = Validator::make($finalUnput, $finalRules, $finalMessages);
        if ($validator->fails()) {
            $error = $validator->errors();
            $responseData = ['status' => $status, 'message' => $message, 'error' => $error];
            return response()->json($responseData);
        } else {

            if ($request->option_email) {
                $final_option_email = explode(',', $request->option_email);
                $isEmailValid = false;
                foreach ($final_option_email as $key => $value) {
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $isEmailValid = true;
                        break;
                    }
                }
                if ($isEmailValid) {
                    $errorArr['notificationOptionEmail'] = "Invalid email address in list";
                    $responseData = ['status' => $status, 'message' => $message, 'error' => $errorArr];
                    return response()->json($responseData);
                }
            } else {
                $final_option_email = [];
            }

            if ($request->option_sms) {
                $final_option_sms = explode(',', $request->option_sms);
                $isPhoneInvalid = $isPhoneInvalidValidation = false;
                foreach ($final_option_sms as $key => $value) {
                    if (!preg_match('/^[0-9]{10}+$/', $value)) {
                        $isPhoneInvalidValidation = true;
                        break;
                    }
                    $phoneCheck = array_unique(str_split(trim($value)));
                    if (count($phoneCheck) === 1) {
                        $isPhoneInvalid = true;
                        break;
                    }
                }
                if ($isPhoneInvalidValidation) {
                    $errorArr['notificationOptionSms'] = "Invalid number in list.";
                    $responseData = ['status' => $status, 'message' => $message, 'error' => $errorArr];
                    return response()->json($responseData);
                }
                if ($isPhoneInvalid) {
                    $errorArr['notificationOptionSms'] = "Same number repeated in list.";
                    $responseData = ['status' => $status, 'message' => $message, 'error' => $errorArr];
                    return response()->json($responseData);
                }
            } else {
                $final_option_sms = [];
            }

            if (!empty($request->option_users)) {
                $final_option_users = $request->option_users;
            } else {
                $final_option_users = [];
            }
            if (count($final_option_email) + count($final_option_sms) + count($final_option_users) <= 0) {
                $errorArr['notificationOption'] = "Email,SMS and User any one filed have atleast one value";
                $responseData = ['status' => $status, 'message' => $message, 'error' => $errorArr];
                return response()->json($responseData);
            }

            if ($request->moduleType == 'lead_in_project') {
                $findData->notification_name = $request->notification_name;
                $findData->module_name = $request->moduleType;
                $findData->lead_verified = $request->lead_verified;
                $findData->lead_condition_datatype = $request->lead_condition_datatype1;
                $findData->lead_condition = $request->lead_condition1;
                $findData->lead_condition_value = $request->lead_condition_value1;
                $findData->start_time = $request->start_time;
                $findData->end_time = $request->end_time;
                $findData->how_often = $request->how_often;
                $findData->data_selection = $request->data_selection;
                $findData->status = $request->status;
                $findData->save();
                if (!empty($request->lead_category1)) {
                    NotificationLeadCategory::where([
                        'notification_id' => $findData->notification_id
                    ])->delete();
                    foreach ($request->lead_category1 as $key => $value) {
                        NotificationLeadCategory::updateOrCreate([
                            'notification_id' => $findData->notification_id,
                            'category_id' => $value
                        ], [
                            'notification_id' => $findData->notification_id,
                            'category_id' => $value
                        ]);
                    }
                }
                if (!empty($request->lead_source1)) {
                    NotificationSource::where([
                        'notification_id' => $findData->notification_id
                    ])->delete();
                    foreach ($request->lead_source1 as $key => $value) {
                        NotificationSource::updateOrCreate([
                            'notification_id' => $findData->notification_id,
                            'source_id' => $value
                        ], [
                            'notification_id' => $findData->notification_id,
                            'source_id' => $value
                        ]);
                    }
                }
                if (!empty($request->lead_sub_source1)) {
                    NotificationSubSource::where([
                        'notification_id' => $findData->notification_id
                    ])->delete();
                    foreach ($request->lead_sub_source1 as $key => $value) {
                        NotificationSubSource::updateOrCreate([
                            'notification_id' => $findData->notification_id,
                            'sub_source_id' => $value
                        ], [
                            'notification_id' => $findData->notification_id,
                            'sub_source_id' => $value
                        ]);
                    }
                }
            } else if ($request->moduleType == 'lead_routing') {

                $findData->notification_name = $request->notification_name;
                $findData->module_name = $request->moduleType;
                $findData->lead_sold_type = $request->sold_type;
                $findData->lead_condition_datatype = 'ca2_lead_count';
                $findData->lead_condition = $request->lead_condition2;
                $findData->lead_condition_value = $request->lead_condition_value2;
                $findData->start_time = $request->start_time;
                $findData->end_time = $request->end_time;
                $findData->how_often = $request->how_often;
                $findData->data_selection = $request->data_selection;
                $findData->status = $request->status;
                $findData->save();
                if (!empty($request->lead_category2)) {
                    NotificationLeadCategory::where([
                        'notification_id' => $findData->notification_id
                    ])->delete();
                    foreach ($request->lead_category2 as $key => $value) {
                        NotificationLeadCategory::updateOrCreate([
                            'notification_id' => $findData->notification_id,
                            'category_id' => $value
                        ], [
                            'notification_id' => $findData->notification_id,
                            'category_id' => $value
                        ]);
                    }
                }
                if (!empty($request->lead_source2)) {
                    NotificationSource::where([
                        'notification_id' => $findData->notification_id
                    ])->delete();
                    foreach ($request->lead_source2 as $key => $value) {
                        NotificationSource::updateOrCreate([
                            'notification_id' => $findData->notification_id,
                            'source_id' => $value
                        ], [
                            'notification_id' => $findData->notification_id,
                            'source_id' => $value
                        ]);
                    }
                }
                if (!empty($request->lead_sub_source2)) {
                    NotificationSubSource::where([
                        'notification_id' => $findData->notification_id
                    ])->delete();
                    foreach ($request->lead_sub_source2 as $key => $value) {
                        NotificationSubSource::updateOrCreate([
                            'notification_id' => $findData->notification_id,
                            'sub_source_id' => $value
                        ], [
                            'notification_id' => $findData->notification_id,
                            'sub_source_id' => $value
                        ]);
                    }
                }
            } else if ($request->moduleType == 'routing') {

                $findData->notification_name = $request->notification_name;
                $findData->module_name = $request->moduleType;
                $findData->lead_condition_sold_type = $request->lead_condition_sold_type;
                $findData->lead_condition_datatype = 'ca3_lead_sold';
                $findData->lead_condition = $request->lead_condition3;
                $findData->lead_condition_value = $request->lead_condition_value3;
                $findData->start_time = $request->start_time;
                $findData->end_time = $request->end_time;
                $findData->how_often = $request->how_often;
                $findData->data_selection = $request->data_selection;
                $findData->status = $request->status;
                $findData->routing_on = $request->businness_or_campaign;
                $findData->save();

                if (!empty($request->lead_category3)) {
                    NotificationLeadCategory::where([
                        'notification_id' => $findData->notification_id
                    ])->delete();
                    foreach ($request->lead_category3 as $key => $value) {
                        NotificationLeadCategory::updateOrCreate([
                            'notification_id' => $findData->notification_id,
                            'category_id' => $value
                        ], [
                            'notification_id' => $findData->notification_id,
                            'category_id' => $value
                        ]);
                    }
                }
                if (!empty($request->business_ids) && ($request->businness_or_campaign == 'business')) {

                    NotificationBusinessOrCampaign::where([
                        'notification_id' => $findData->notification_id,
                    ])->delete();
                    foreach ($request->business_ids as $key => $value) {
                        NotificationBusinessOrCampaign::updateOrCreate([
                            'notification_id' => $findData->notification_id,
                            'type' => 'business',
                            'business_id' => $value
                        ], [
                            'notification_id' => $findData->notification_id,
                            'type' => 'business',
                            'business_id' => $value
                        ]);
                    }
                }
                if (!empty($request->campaign_ids) && ($request->businness_or_campaign == 'campaign')) {
                    NotificationBusinessOrCampaign::where([
                        'notification_id' => $findData->notification_id,
                    ])->delete();
                    foreach ($request->campaign_ids as $key => $value) {
                        NotificationBusinessOrCampaign::updateOrCreate([
                            'notification_id' => $findData->notification_id,
                            'type' => 'campaign',
                            'campaign_id' => $value
                        ], [
                            'notification_id' => $findData->notification_id,
                            'type' => 'campaign',
                            'campaign_id' => $value
                        ]);
                    }
                }
            } else {
            }
            NotificationOption::where([
                'notification_id' => $findData->notification_id
            ])->delete();
            if (!empty($final_option_email)) {
                foreach ($final_option_email as $key => $value) {
                    NotificationOption::updateOrCreate([
                        'notification_id' => $findData->notification_id,
                        'option_type' => 'email',
                        'option_value' => $value
                    ], [
                        'notification_id' => $findData->notification_id,
                        'option_type' => 'email',
                        'option_value' => $value
                    ]);
                }
            }

            if (!empty($final_option_sms)) {
                foreach ($final_option_sms as $key => $value) {
                    NotificationOption::updateOrCreate([
                        'notification_id' => $findData->notification_id,
                        'option_type' => 'sms',
                        'option_value' => $value
                    ], [
                        'notification_id' => $findData->notification_id,
                        'option_type' => 'sms',
                        'option_value' => $value
                    ]);
                }
            }

            if (!empty($request->option_users)) {
                foreach ($request->option_users as $key => $value) {
                    NotificationOption::updateOrCreate([
                        'notification_id' => $findData->notification_id,
                        'option_type' => 'user',
                        'option_value' => $value
                    ], [
                        'notification_id' => $findData->notification_id,
                        'option_type' => 'user',
                        'option_value' => $value
                    ]);
                }
            }

            $status = 1;
            $message = 'Success';
            $request->session()->flash('message', $message);
            $responseData = ['status' => $status, 'message' => $message, 'error' => $error];
            return response()->json($responseData);
        }
    }

    public function getBusinessOrCampaignList(Request $request)
    {
        $source = $request->source;
        $lead_category = $request->lead_category;
        if ($source == 'business') {
            $data = Business::get();
            foreach ($data as $key => $value) {
                $response[] = [
                    'business_id'   => $value->business_id,
                    'business_name' => $value->business_name,

                ];
            }
            if (!empty($response)) {
                $responseData = [
                    'status' => 1,
                    'message' => 'success',
                    'data' => $response,
                    'error' => array(),

                ];
                return json_encode($responseData);
            } else {
                $responseData = [
                    'status' => 0,
                    'message' => 'Business not found',
                    'error' => array(),

                ];
                return json_encode($responseData);
            }
        } else if ($source == 'campaign') {
            $data = Campaign::whereIn('lead_category_id', $lead_category)->get();
            foreach ($data as $key => $value) {
                $response[] = [
                    'campaign_id'   => $value->campaign_id,
                    'campaign_name' => $value->campaign_name,
                ];
            }
            if (!empty($response)) {
                $responseData = [
                    'status' => 1,
                    'message' => 'success',
                    'data' => $response,
                    'error' => array(),

                ];
                return json_encode($responseData);
            } else {
                $responseData = [
                    'status' => 0,
                    'message' => 'Business not found',
                    'error' => array(),

                ];
                return json_encode($responseData);
            }
        }
    }

    public function runCron(Request $request)
    {
        // My original code start
        $current_date_time = date("Y-m-d H:i:s");
        CommonFunctions::createDevDebugLog(8, 'Notification Cron Start: ' . $current_date_time);
        $hour_number = date('H', strtotime($current_date_time));
        CommonFunctions::createDevDebugLog(8, 'hour_number: ' . $hour_number);
        $minute_number = date('i', strtotime($current_date_time));
        CommonFunctions::createDevDebugLog(8, 'minute_number: ' . $hour_number);
        if ($minute_number >= 30) {
            $minute_data = 5;
        } else {
            $minute_data = 0;
        }
        $compare_number = (float)$hour_number . '.' . $minute_data;
        CommonFunctions::createDevDebugLog(8, 'compare_number: ' . $compare_number);
        $notificationRecords = Notification::where('status', 'active')->get();
        $notificationIds = [];
        foreach ($notificationRecords as $key => $value) {
            $lower_boundary = $value->start_time;
            $upper_boundary = $value->end_time;
            if (($compare_number >= $lower_boundary) && ($compare_number <= $upper_boundary)) {
                $get_how_often = $this->getHowOften($value->how_often);
                for ($i = $lower_boundary; $i <= $upper_boundary; $i += $get_how_often) {
                    if (($compare_number >= $i) && ($compare_number <= $i)) {
                        $notificationIds[] = $value->notification_id;
                        break;
                    } else {
                        continue;
                    }
                }
            }
        }
        CommonFunctions::createDevDebugLog(8, 'notificationIds: ' . json_encode($notificationIds));
        $fireRecordsArray = $fireRecords = [];
        if (!empty($notificationIds)) {
            $notificationRecords = Notification::whereIn('notification_id', $notificationIds)->get();
            foreach ($notificationRecords as $key => $value) {
                $isSetNotification = false;
                $totalCount = 0;

                $mins = $this->getMinutes($value->data_selection);

                if ($mins > 0) {
                    $current_date_time = date('Y-m-d H:i:s');
                    $startdate = date('Y-m-d H:i:s', strtotime($current_date_time . '-' . $mins . ' minutes'));
                    $enddate = date('Y-m-d H:i:s', strtotime($current_date_time));
                    CommonFunctions::createDevDebugLog(8, 'startdate: ' . $startdate . ' enddate ' . $enddate);
                } else {
                    CommonFunctions::createDevDebugLog(8, "Mins not found " . $value->notification_id);
                    continue;
                }

                if ($value->module_name == 'lead_in_project') {
                    CommonFunctions::createDevDebugLog(8, 'lead in snapit');
                    $lead_category_ids = $value->LeadCategory()->pluck('category_id')->toArray();
                    // $lead_source_ids = $value->LeadSource()->pluck('source_id')->toArray();
                    // $lead_sub_source_ids = $value->LeadSubSource()->pluck('sub_source_id')->toArray();
                    // $final_source_ids = array_unique(array_merge($lead_source_ids, $lead_sub_source_ids));
                    $final_source_ids = $value->LeadSubSource()->pluck('sub_source_id')->toArray();
                    // verified case- start
                    if ($value->lead_verified == 'verified') {
                        $isVerified = ['yes'];
                    }
                    if ($value->lead_verified == 'notverified') {
                        $isVerified = ['no'];
                    }
                    if ($value->lead_verified == 'both') {
                        $isVerified = ['yes', 'no'];
                    }
                    // verified case- end
                    if ($value->lead_condition_datatype == 'total_lead_count') {
                        $getLead = Lead::whereIn('lead_category_id', $lead_category_ids)->whereIn('lead_source_id', $final_source_ids)->whereIn('is_verified', $isVerified)->whereBetween('created_at', [$startdate, $enddate])->get();
                        $totalCount = $getLead->count();
                        CommonFunctions::createDevDebugLog(8, 'total_lead_count: ' . $totalCount);
                        //condition values
                        $isSetNotification = $this->getIsSetNotification($value->lead_condition, $totalCount, $value->lead_condition_value);
                        CommonFunctions::createDevDebugLog(8, 'total_lead_count isSetNotification: ' . json_encode($isSetNotification));
                    } else if ($value->lead_condition_datatype == 'long_leads') {
                        $getLead = Lead::with(['LeadMoving', 'LeadHeavyLifting', 'LeadCarTransport'])->whereIn('lead_category_id', $lead_category_ids)->whereIn('lead_source_id', $final_source_ids)->whereIn('is_verified', $isVerified)->whereBetween('created_at', [$startdate, $enddate])->orderBy('lead_id', 'DESC')->get();
                        $movingLongCount = $junkLongCount = $heavyLongCount = $carLongCount = $totalCount = 0;
                        foreach ($getLead as $key => $valueLead) {
                            if ($valueLead->lead_category_id == 1 && ($valueLead->LeadMoving != null)) {
                                if ($valueLead->LeadMoving->from_state != $valueLead->LeadMoving->to_state) {
                                    $movingLongCount = $movingLongCount + 1;
                                }
                            } else if ($valueLead->lead_category_id == 3 && ($valueLead->LeadHeavyLifting != null)) {
                                if ($valueLead->LeadHeavyLifting->from_state != $valueLead->LeadHeavyLifting->to_state) {
                                    $heavyLongCount = $heavyLongCount + 1;
                                }
                            } else if ($valueLead->lead_category_id == 4 && ($valueLead->LeadCarTransport != null)) {
                                if ($valueLead->LeadCarTransport->from_state != $valueLead->LeadCarTransport->to_state) {
                                    $carLongCount = $carLongCount + 1;
                                }
                            }
                        }
                        $totalCount = $movingLongCount + $junkLongCount + $heavyLongCount + $carLongCount;
                        CommonFunctions::createDevDebugLog(8, 'long_leads_counr: ' . $totalCount);
                        $isSetNotification = $this->getIsSetNotification($value->lead_condition, $totalCount, $value->lead_condition_value);
                        CommonFunctions::createDevDebugLog(8, 'long_leads isSetNotification: ' . $isSetNotification);
                    } else if ($value->lead_condition_datatype == 'local_leads') {
                        $getLead = Lead::with(['LeadMoving', 'LeadHeavyLifting', 'LeadCarTransport'])->whereIn('lead_category_id', $lead_category_ids)->whereIn('lead_source_id', $final_source_ids)->whereIn('is_verified', $isVerified)->whereBetween('created_at', [$startdate, $enddate])->orderBy('lead_id', 'DESC')->get();
                        $movingLongCount = $junkLongCount = $heavyLongCount = $carLongCount = $totalCount = 0;
                        foreach ($getLead as $key => $valueLead) {
                            if ($valueLead->lead_category_id == 1 && ($valueLead->LeadMoving != null)) {
                                if ($valueLead->LeadMoving->from_state == $valueLead->LeadMoving->to_state) {
                                    $movingLongCount = $movingLongCount + 1;
                                }
                            } else if ($valueLead->lead_category_id == 2) {
                                $junkLongCount = $junkLongCount + 1;
                            } else if ($valueLead->lead_category_id == 3 && ($valueLead->LeadHeavyLifting != null)) {
                                if ($valueLead->LeadHeavyLifting->from_state == $valueLead->LeadHeavyLifting->to_state) {
                                    $heavyLongCount = $heavyLongCount + 1;
                                }
                            } else if ($valueLead->lead_category_id == 4 && ($valueLead->LeadCarTransport != null)) {
                                if ($valueLead->LeadCarTransport->from_state == $valueLead->LeadCarTransport->to_state) {
                                    $carLongCount = $carLongCount + 1;
                                }
                            }
                        }
                        $totalCount = $movingLongCount + $junkLongCount + $heavyLongCount + $carLongCount;
                        CommonFunctions::createDevDebugLog(8, 'local_leads_count: ' . $totalCount);
                        $isSetNotification = $this->getIsSetNotification($value->lead_condition, $totalCount, $value->lead_condition_value);
                        CommonFunctions::createDevDebugLog(8, 'local_leads isSetNotification: ' . $isSetNotification);
                    } else if ($value->lead_condition_datatype == 'total_payout') {
                        $getLeadRecords = Lead::whereIn('lead_category_id', $lead_category_ids)->whereIn('lead_source_id', $final_source_ids)->whereIn('is_verified', $isVerified)->whereBetween('created_at', [$startdate, $enddate])->get();
                        if ($getLeadRecords->count() > 0) {
                            $getLeadSum = $getLeadRecords->sum('payout');
                            CommonFunctions::createDevDebugLog(8, 'total_payout_sum: ' . $getLeadSum);
                            $isSetNotification = $this->getIsSetNotification($value->lead_condition, $getLeadSum, $value->lead_condition_value);
                            CommonFunctions::createDevDebugLog(8, 'total_payout isSetNotification: ' . $isSetNotification);
                        } else {
                            $getLeadSum = 0;
                            CommonFunctions::createDevDebugLog(8, 'total_payout_sum: ' . $getLeadSum);
                        }
                    } else if ($value->lead_condition_datatype == 'long_payout') {
                        $getLead = Lead::with(['LeadMoving', 'LeadHeavyLifting', 'LeadCarTransport'])->whereIn('lead_category_id', $lead_category_ids)->whereIn('lead_source_id', $final_source_ids)->whereIn('is_verified', $isVerified)->whereBetween('created_at', [$startdate, $enddate])->orderBy('lead_id', 'DESC')->get();
                        $allLeadIds = [];
                        $totalCount = $totalSum = 0;

                        foreach ($getLead as $key => $valueLead) {
                            if ($valueLead->lead_category_id == 1 && ($valueLead->LeadMoving != null)) {
                                if ($valueLead->LeadMoving->from_state != $valueLead->LeadMoving->to_state) {
                                    $allLeadIds[] = $valueLead->lead_id;
                                }
                            } else if ($valueLead->lead_category_id == 3 && ($valueLead->LeadHeavyLifting != null)) {
                                if ($valueLead->LeadHeavyLifting->from_state != $valueLead->LeadHeavyLifting->to_state) {
                                    $allLeadIds[] = $valueLead->lead_id;
                                }
                            } else if ($valueLead->lead_category_id == 4 && ($valueLead->LeadCarTransport != null)) {
                                if ($valueLead->LeadCarTransport->from_state != $valueLead->LeadCarTransport->to_state) {
                                    $allLeadIds[] = $valueLead->lead_id;
                                }
                            }
                        }

                        $totalCount = count($allLeadIds);
                        if ($totalCount > 0) {
                            CommonFunctions::createDevDebugLog(8, 'long_payout_count: ' . $totalCount);
                            $routingLead = LeadRouting::whereIn('lead_id', $allLeadIds)->where('route_status', 'sold')->get();
                            $totalSum = $routingLead->sum('payout');
                            CommonFunctions::createDevDebugLog(8, 'long_payout_sum: ' . $totalSum);
                            $isSetNotification = $this->getIsSetNotification($value->lead_condition, $totalSum, $value->lead_condition_value);
                            CommonFunctions::createDevDebugLog(8, 'long_payout isSetNotification: ' . $isSetNotification);
                        }
                    } else if ($value->lead_condition_datatype == 'local_payout') {
                        $getLead = Lead::with(['LeadMoving', 'LeadHeavyLifting', 'LeadCarTransport'])->whereIn('lead_category_id', $lead_category_ids)->whereIn('lead_source_id', $final_source_ids)->whereIn('is_verified', $isVerified)->whereBetween('created_at', [$startdate, $enddate])->orderBy('lead_id', 'DESC')->get();
                        $allLeadIds = [];
                        $totalCount = $totalSum = 0;

                        foreach ($getLead as $key => $valueLead) {
                            if ($valueLead->lead_category_id == 1 && ($valueLead->LeadMoving != null)) {
                                if ($valueLead->LeadMoving->from_state != $valueLead->LeadMoving->to_state) {
                                    $allLeadIds[] = $valueLead->lead_id;
                                }
                            } else if ($valueLead->lead_category_id == 3 && ($valueLead->LeadHeavyLifting != null)) {
                                if ($valueLead->LeadHeavyLifting->from_state != $valueLead->LeadHeavyLifting->to_state) {
                                    $allLeadIds[] = $valueLead->lead_id;
                                }
                            } else if ($valueLead->lead_category_id == 4 && ($valueLead->LeadCarTransport != null)) {
                                if ($valueLead->LeadCarTransport->from_state != $valueLead->LeadCarTransport->to_state) {
                                    $allLeadIds[] = $valueLead->lead_id;
                                }
                            }
                        }

                        $totalCount = count($allLeadIds);
                        if ($totalCount > 0) {
                            CommonFunctions::createDevDebugLog(8, 'local_payout_count: ' . $totalCount);
                            $routingLead = LeadRouting::whereIn('lead_id', $allLeadIds)->where('route_status', 'sold')->get();
                            $totalSum = $routingLead->sum('payout');
                            CommonFunctions::createDevDebugLog(8, 'local_payout_sum: ' . $totalSum);
                            $isSetNotification = $this->getIsSetNotification($value->lead_condition, $totalSum, $value->lead_condition_value);
                            CommonFunctions::createDevDebugLog(8, 'local_payout isSetNotification: ' . $isSetNotification);
                        }
                    } else if ($value->lead_condition_datatype == 'avg_payout') {
                        $getLeadRecords = Lead::with(['LeadMoving', 'LeadHeavyLifting', 'LeadCarTransport'])->whereIn('lead_category_id', $lead_category_ids)->whereIn('lead_source_id', $final_source_ids)->whereIn('is_verified', $isVerified)->whereBetween('created_at', [$startdate, $enddate])->orderBy('lead_id', 'DESC')->get();
                        CommonFunctions::createDevDebugLog(8, 'avg_payout_count: ' . $getLeadRecords->count());
                        if ($getLeadRecords->count() > 0) {
                            $getLeadSum = $getLeadRecords->sum('payout');
                            $getLeadCount = $getLeadRecords->count();
                            CommonFunctions::createDevDebugLog(8, 'avg_payout_sum: ' . $getLeadSum . ' avg_payout_count ' . $getLeadCount);
                            if ($getLeadSum > 0 && $getLeadCount > 0) {
                                $getAvgSum = $getLeadSum / $getLeadCount;
                                $getAvgSum = (float) sprintf('%0.2f', $getAvgSum);
                                CommonFunctions::createDevDebugLog(8, 'avg_payout_final_sum: ' . $getAvgSum);
                                $isSetNotification = $this->getIsSetNotification($value->lead_condition, $getAvgSum, $value->lead_condition_value);
                                CommonFunctions::createDevDebugLog(8, 'avg_payout isSetNotification: ' . $isSetNotification);
                            }
                        }
                    } else if ($value->lead_condition_datatype == 'avg_long_payout') {
                        $getLead = Lead::with(['LeadMoving', 'LeadHeavyLifting', 'LeadCarTransport'])->whereIn('lead_category_id', $lead_category_ids)->whereIn('lead_source_id', $final_source_ids)->whereIn('is_verified', $isVerified)->whereBetween('created_at', [$startdate, $enddate])->orderBy('lead_id', 'DESC')->get();
                        $allLeadIds = [];
                        $totalCount = $totalSum = 0;

                        foreach ($getLead as $key => $valueLead) {
                            if ($valueLead->lead_category_id == 1 && ($valueLead->LeadMoving != null)) {
                                if ($valueLead->LeadMoving->from_state != $valueLead->LeadMoving->to_state) {
                                    $allLeadIds[] = $valueLead->lead_id;
                                }
                            } else if ($valueLead->lead_category_id == 3 && ($valueLead->LeadHeavyLifting != null)) {
                                if ($valueLead->LeadHeavyLifting->from_state != $valueLead->LeadHeavyLifting->to_state) {
                                    $allLeadIds[] = $valueLead->lead_id;
                                }
                            } else if ($valueLead->lead_category_id == 4 && ($valueLead->LeadCarTransport != null)) {
                                if ($valueLead->LeadCarTransport->from_state != $valueLead->LeadCarTransport->to_state) {
                                    $allLeadIds[] = $valueLead->lead_id;
                                }
                            }
                        }

                        $totalCount = count($allLeadIds);
                        if ($totalCount > 0) {
                            $routingLead = LeadRouting::whereIn('lead_id', $allLeadIds)->where('route_status', 'sold')->get();
                            $totalSum = $routingLead->sum('payout');
                            CommonFunctions::createDevDebugLog(8, 'avg_long_payout_count: ' . $totalCount . ' avg_long_payout_sum ' . $totalSum);
                            $avgSum = $totalSum / $totalCount;
                            $avgSum = (float) sprintf('%0.2f', $avgSum);
                            CommonFunctions::createDevDebugLog(8, 'avg_long_payout_final_sum: ' . $avgSum);
                            if ($avgSum > 0) {
                                $isSetNotification = $this->getIsSetNotification($value->lead_condition, $avgSum, $value->lead_condition_value);
                                CommonFunctions::createDevDebugLog(8, 'avg_long_payout isSetNotification: ' . $isSetNotification);
                            }
                        }
                    } else if ($value->lead_condition_datatype == 'avg_local_payout') {
                        $getLead = Lead::with(['LeadMoving', 'LeadHeavyLifting', 'LeadCarTransport'])->whereIn('lead_category_id', $lead_category_ids)->whereIn('lead_source_id', $final_source_ids)->whereIn('is_verified', $isVerified)->whereBetween('created_at', [$startdate, $enddate])->orderBy('lead_id', 'DESC')->get();

                        $allLeadIds = [];
                        $totalCount = $totalSum = 0;

                        foreach ($getLead as $key => $valueLead) {
                            if ($valueLead->lead_category_id == 1 && ($valueLead->LeadMoving != null)) {
                                if ($valueLead->LeadMoving->from_state == $valueLead->LeadMoving->to_state) {
                                    $allLeadIds[] = $valueLead->lead_id;
                                }
                            } else if ($valueLead->lead_category_id == 2) {
                                $allLeadIds[] = $valueLead->lead_id;
                            } else if ($valueLead->lead_category_id == 3 && ($valueLead->LeadHeavyLifting != null)) {
                                if ($valueLead->LeadHeavyLifting->from_state == $valueLead->LeadHeavyLifting->to_state) {
                                    $allLeadIds[] = $valueLead->lead_id;
                                }
                            } else if ($valueLead->lead_category_id == 4 && ($valueLead->LeadCarTransport != null)) {
                                if ($valueLead->LeadCarTransport->from_state == $valueLead->LeadCarTransport->to_state) {
                                    $allLeadIds[] = $valueLead->lead_id;
                                }
                            }
                        }

                        $totalCount = count($allLeadIds);
                        if ($totalCount > 0) {
                            $routingLead = LeadRouting::whereIn('lead_id', $allLeadIds)->where('route_status', 'sold')->get();
                            $totalSum = $routingLead->sum('payout');
                            CommonFunctions::createDevDebugLog(8, 'avg_local_payout_count: ' . $totalCount . ' avg_local_payout_sum ' . $totalSum);
                            $avgSum = $totalSum / $totalCount;
                            $avgSum = (float) sprintf('%0.2f', $avgSum);
                            CommonFunctions::createDevDebugLog(8, 'avg_local_payout_final_sum: ' . $avgSum);
                            if ($avgSum > 0) {
                                $isSetNotification = $this->getIsSetNotification($value->lead_condition, $avgSum, $value->lead_condition_value);
                                CommonFunctions::createDevDebugLog(8, 'avg_local_payout isSetNotification: ' . $isSetNotification);
                            }
                        }
                    }
                } else if ($value->module_name == 'lead_routing' && $value->lead_condition_datatype == 'ca2_lead_count') {
                    CommonFunctions::createDevDebugLog(8, 'lead_routing & ca2_lead_count Case2');
                    $lead_category_ids = $value->LeadCategory()->pluck('category_id')->toArray();

                    // $lead_source_ids = $value->LeadSource()->pluck('source_id')->toArray();

                    // $lead_sub_source_ids = $value->LeadSubSource()->pluck('sub_source_id')->toArray();

                    // $final_source_ids = array_unique(array_merge($lead_source_ids, $lead_sub_source_ids));

                    $final_source_ids = $value->LeadSubSource()->pluck('sub_source_id')->toArray();

                    // $getLead = Lead::whereIn('lead_category_id', $lead_category_ids)->whereIn('lead_source_id', $final_source_ids)->get();
                    // $totalCount = $getLead->count();


                    $soldType = strtolower($value->lead_sold_type);
                    if ($soldType == 'shared') {
                        $soldType = 'premium';
                    }
                    $leadCount = 0;
                    $leadData = Lead::with(['routingInfoLeadList' => function ($query) {
                        $query->orderBy('route_status', 'DESC');
                    }, 'routingInfoLeadList.routingCampaignInfo', 'routingInfoLeadList.routingCampaignInfo.mstCampaignType'])->whereBetween('created_at', [$startdate, $enddate])->orderBy('lead_id', 'DESC')->get()->toArray();
                    CommonFunctions::createDevDebugLog(8, 'leads data' . json_encode($leadData));
                    foreach ($leadData as $key => $leadDataValue) {
                        if (!empty($leadDataValue['routing_info_lead_list'])) {
                            foreach ($leadDataValue['routing_info_lead_list'] as $routingkey => $routingData) {
                                if ($routingData['route_status'] == 'sold') {
                                    $leadType = strtolower($routingData['routing_campaign_info']['mst_campaign_type']['campaign_type']);

                                    if (strpos(strtolower($leadType), 'exclusive') !== false) {
                                        if ($soldType == 'exclusive') {
                                            $leadCount = $leadCount + 1;
                                        }
                                        break;
                                    } else if (strpos(strtolower($leadType), 'dual') !== false) {
                                        if ($soldType == 'dual') {
                                            $leadCount = $leadCount + 1;
                                        }
                                        break;
                                    } else if (strpos(strtolower($leadType), 'premium') !== false) {
                                        if ($soldType == 'premium') {
                                            $leadCount = $leadCount + 1;
                                        }
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    CommonFunctions::createDevDebugLog(8, 'leads count ' . $leadCount);
                    $isSetNotification = $this->getIsSetNotification($value->lead_condition, $leadCount, $value->lead_condition_value);
                    CommonFunctions::createDevDebugLog(8, 'isSetNotification ' . $isSetNotification);
                } else if ($value->module_name == 'routing' && $value->lead_condition_datatype == 'ca3_lead_sold') {
                    CommonFunctions::createDevDebugLog(8, 'routing & ca3_lead_sold Case3');
                    $leadRoutingCount = 0;
                    if ($value->routing_on == 'campaign') {
                        $allCampaignIds = NotificationBusinessOrCampaign::where(['notification_id' => $value->notification_id, 'type' => 'campaign'])->pluck('campaign_id')->toArray();
                        $leadRoutingCount = LeadRouting::whereIn('campaign_id', $allCampaignIds)->where('route_status', $value->lead_condition_sold_type)->whereBetween('created_at', [$startdate, $enddate])->count();
                    } else if ($value->routing_on == 'business') {
                        $allBusinessIds = NotificationBusinessOrCampaign::where(['notification_id' => $value->notification_id, 'type' => 'business'])->pluck('business_id')->toArray();
                        $allCampaignIds = Campaign::whereIn('business_id', $allBusinessIds)->pluck('campaign_id')->toArray();
                        $leadRoutingCount = LeadRouting::whereIn('campaign_id', $allCampaignIds)->where('route_status', $value->lead_condition_sold_type)->whereBetween('created_at', [$startdate, $enddate])->count();
                    }
                    CommonFunctions::createDevDebugLog(8, 'leadRoutingCount ' . $leadRoutingCount);
                    $isSetNotification = $this->getIsSetNotification($value->lead_condition, $leadRoutingCount, $value->lead_condition_value);
                    CommonFunctions::createDevDebugLog(8, 'isSetNotification ' . $isSetNotification);
                }

                if ($isSetNotification == true) {
                    $fireRecords = $this->fireNotification($value->notification_id);
                    $fireRecordsArray[] = ['notification_id' => $value->notification_id, 'records' => $fireRecords];
                }
            }
            CommonFunctions::createDevDebugLog(8, 'isSetNotification Data ' . json_encode($fireRecords));
            return $fireRecordsArray;
        } else {
            CommonFunctions::createDevDebugLog(8, 'notification Array Empty');
        }
        $current_date_time = date("Y-m-d H:i:s");
        CommonFunctions::createDevDebugLog(8, 'Notification Cron End Data ' . $current_date_time);
    }

    public function getMinutes($minute_string)
    {
        switch ($minute_string) {
            case 'last_30_mins':
                $mins = 30;
                break;
            case 'last_60_mins':
                $mins = 60;
                break;
            case 'last_90_mins':
                $mins = 90;
                break;
            case 'last_2_hours':
                $mins = 120;
                break;
            case 'last_4_hours':
                $mins = 240;
                break;
            case 'last_8_hours':
                $mins = 480;
                break;
            case 'last_12_hours':
                $mins = 720;
                break;
            case 'last_24_hours':
                $mins = 1440;
                break;

            default:
                $mins = 0;
                break;
        }
        return $mins;
    }

    public function getHowOften($how_often_string)
    {
        switch ($how_often_string) {
            case 'every_30_mins':
                $how_often_mins = 0.50;
                break;
            case 'every_60_mins':
                $how_often_mins = 1;
                break;
            case 'every_90_mins':
                $how_often_mins = 1.50;
                break;
            case 'every_4_hours':
                $how_often_mins = 4;
                break;
            case 'every_12_hours':
                $how_often_mins = 12;
                break;
            default:
                $how_often_mins = 0;
                break;
        }
        return $how_often_mins;
    }

    public function getIsSetNotification($condition, $count_or_sum, $condition_value)
    {
        if ($condition == '=') {
            if ($count_or_sum == $condition_value) {
                return true;
            } else {
                return false;
            }
        } else if ($condition == '<') {
            if ($count_or_sum < $condition_value) {
                return true;
            } else {
                return false;
            }
        } else if ($condition == '>') {
            if ($count_or_sum > $condition_value) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    public function fireNotification($notification_id)
    {
        $emailsArray = [];
        $SmsArray = [];
        $user_idsArray = [];
        $getNotification = Notification::where('notification_id', $notification_id)->first();
        switch ($getNotification->lead_condition_datatype) {
            case 'total_lead_count':
                $text = 'Total Lead count';
                break;
            case 'long_leads':
                $text = 'Long Leads';
                break;
            case 'local_lead':
                $text = 'Local Leads';
                break;
            case 'total_payout':
                $text = 'Total Payout';
                break;
            case 'long_payout':
                $text = 'Long Payout';
                break;
            case 'local_payout':
                $text = 'local Payout';
                break;
            case 'avg_payout':
                $text = 'Average Payout';
                break;
            case 'avg_long_payout':
                $text = 'Average Long Payout';
                break;
            case 'avg_local_payout':
                $text = 'Average Local Payout';
                break;
            case 'total_cost':
                $text = 'Total Cost';
                break;
            case 'cpa':
                $text = 'CPA';
                break;

            case 'ca2_lead_count':
                $text = 'Lead Count';
                break;
            case 'ca3_lead_sold':
                $text = 'Lead Sold';
                break;

            default:
                $text = '';
                break;
        }
        switch ($getNotification->lead_condition) {
            case '=':
                $leadConditiontext = 'equal to';
                break;
            case '<':
                $leadConditiontext = 'less than';
                break;
            case '>':
                $leadConditiontext = 'grater than';
                break;
            default:
                $leadConditiontext = '';
                break;
        }
        $getNotificationOptions = NotificationOption::where('notification_id', $notification_id)->get();
        if ($getNotificationOptions->count() > 0) {
            foreach ($getNotificationOptions as $optionkey => $optionvalue) {
                if ($optionvalue->option_type == 'sms') {
                    try{
                        $to = '+1' . $optionvalue->option_value;
                        $from = '+18153673130';
                        $smstext = 'Notification - ' . $getNotification->notitifaction_name . '(#' . $getNotification->notification_id . ') executed because ' . $text . ' ' . $leadConditiontext . '  ' . $getNotification->lead_condition_value;
                        $sendSMS = CommonFunctions::send_sms_by_plivo($to, $from, $smstext);
                        $smsLogData = array();
                        $smsLogData['from_number'] = $from;
                        $smsLogData['to_number'] = $to;
                        $smsLogData['type'] = 'normal';
                        $smsLogData['sms_subject'] = "LinkUp notification";
                        $smsLogData['request'] = $smstext;
                        $smsLogData['response'] = json_encode($sendSMS);
                        $smsLogData['status'] = "success";
                        $createSMSLogData = array(
                            'notification_id' => $getNotification->notification_id,
                            'from_number' => $from,
                            'to_number' => $to,
                            'subject' => $smstext,
                            'request' => json_encode($smsLogData),
                            'response' => json_encode($sendSMS),
                            'log_type' => 'SMS',
                            'status' => 'success',
                            'created_at' => date('Y-m-d H:i:s')
                        );
                        NotificationSmsEmailLog::create($createSMSLogData);
                        $SmsArray[$optionkey] = $optionvalue->option_value;
                    } catch (Exception $e) {
                        CommonFunctions::createDevDebugLog(8, 'Error in SMS ');
                        CommonFunctions::createDevDebugLog(8, $e);
                        $createSMSLogData = array(
                            'notification_id' => $getNotification->notification_id,
                            'from_number' => $from,
                            'to_number' => $to,
                            'subject' => $smstext,
                            'request' => json_encode([]),
                            'response' => $e,
                            'log_type' => 'SMS',
                            'status' => 'fail',
                            'created_at' => date('Y-m-d H:i:s')
                        );
                        NotificationSmsEmailLog::create($createSMSLogData);
                        $SmsArray[$optionkey] = $optionvalue->option_value;
                    }
                } else if ($optionvalue->option_type == 'email') {
                    try{
                        $emaildata = [];
                        $emaildata['from'] = '<EMAIL>';
                        $emaildata['to'] = $optionvalue->option_value;
                        $emaildata['subject'] = 'LinkUp notification ' . $getNotification->notification_name . '(' . $getNotification->notification_id . ') executed.';
                        $emaildata['notificationName'] = $getNotification->notification_name;
                        $emaildata['notificationId'] = $getNotification->notification_id;
                        $emaildata['notificationText'] = 'You received this notification beacuse ' . $text . ' ' . $leadConditiontext . '  ' . $getNotification->lead_condition_value;
                        $sendEmail = new Notification();
                        $emailResponse = $sendEmail->sendEmail($emaildata);
                        $status = 'fail';
                        if ($emailResponse['Message'] == 'OK') {
                            $status = 'success';
                        }
                        $createLogData = array(
                            'notification_id' => $getNotification->notification_id,
                            'subject' => $emaildata['subject'],
                            'request' => json_encode($emaildata),
                            'response' => json_encode($emailResponse),
                            'log_type' => 'email',
                            'status' => $status,
                            'created_at' => date('Y-m-d H:i:s')
                        );
                        NotificationSmsEmailLog::create($createLogData);
                        $emailsArray[$optionkey] = $optionvalue->option_value;
                    } catch (Exception $e) {
                        $message = $e->getMessage();
                        CommonFunctions::createDevDebugLog(8, 'Error in EMAIL');
                        CommonFunctions::createDevDebugLog(8, json_encode($e));
                        $emailsArray[$optionkey] = $optionvalue->option_value;
                    }
                } else if ($optionvalue->option_type == 'user') {
                    try {
                        $userNotificationData = [];
                        $userNotificationData['subject'] = $getNotification->notification_name . '(' . $getNotification->notification_id . ') executed.';
                        $userNotificationData['notificationText'] = 'You received this notification because ' . $text . ' ' . $leadConditiontext . '  ' . $getNotification->lead_condition_value;

                        $createSMSLogData = array(
                            'user_id' => $optionvalue->option_value,
                            'title' => $userNotificationData['subject'],
                            'sub_title' => $userNotificationData['notificationText'],
                            'log_type' => 'user',
                            'status' => 'success',
                            'created_at' => date('Y-m-d H:i:s')
                        );
                        UserNotification::create($createSMSLogData);

                        $createLogData = array(
                            'notification_id' => $getNotification->notification_id,
                            'user_id' => $optionvalue->option_value,
                            'subject' => $createSMSLogData['title'],
                            'request' => json_encode($createSMSLogData),
                            'response' => json_encode([]),
                            'log_type' => 'user',
                            'status' => 'success',
                            'created_at' => date('Y-m-d H:i:s')
                        );
                        NotificationSmsEmailLog::create($createLogData);
                        $user_idsArray[$optionkey] = $optionvalue->option_value;
                    } catch (Exception $e) {
                        $message = $e->getMessage();
                        CommonFunctions::createDevDebugLog(8, 'Error in EMAIL');
                        CommonFunctions::createDevDebugLog(8, json_encode($e));
                        $user_idsArray[$optionkey] = $optionvalue->option_value;
                    }
                }
            }
        }
        return [
            'smsArray' => $SmsArray,
            'emailArray' => $emailsArray,
            'user_ids' => $user_idsArray,
        ];
    }

    public function notificationReport()
    {
        return view('reports.notification');
    }

    public function notificationReportData(Request $request)
    {
        $notificationData   = [];
        $leadActivity       = new LeadActivity();
        $notificationDetail = UserNotification::where(['user_id' => Auth::user()->id])->orderBy('user_notification_id', 'DESC')->take(20)->get();
        foreach ($notificationDetail as $key => $notification) {
            $notificationData[$key]['title'] = $notification->title;
            $notificationData[$key]['sub_title'] = $notification->sub_title;
            $notificationData[$key]['time'] = $leadActivity->timeAgo(strtotime($notification->created_at));
        }
        UserNotification::where(['user_id' => Auth::user()->id, 'is_read' => 'no'])->update(['is_read' => 'yes']);
        return datatables()->of($notificationData)->make(true);
    }
}

