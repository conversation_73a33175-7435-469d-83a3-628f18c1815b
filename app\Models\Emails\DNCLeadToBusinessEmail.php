<?php 

namespace App\Models\Emails;
use Illuminate\Database\Eloquent\Model;   
use App\Helpers\Helper;
use App\Models\Emails\EmailLog;
use App\Models\Master\MstMoveSize;
use App\Models\Lead\LeadActivity;
use App\Models\Emails\DNCLeadFailedEmail;
class DNCLeadToBusinessEmail extends Model {
     
    public function emailDNCLeadToBusinessEmail( $leadbusiness, $leadCategory, $categoriesData, $junkSubType, $leaddnccallvalue = "no", $leaddncsmsvalue = "no", $leaddncemailvalue = "no" ){ 
        
        $dncfor = [];
        $postmarkey = Helper::checkServer()['postmark_token'];
        $business_name = (isset($leadbusiness['business_name']) ) ? $leadbusiness['business_name'] : '-';
        $phone1 = ($leadbusiness['phone'] != NULL && $leadbusiness['phone'] != "") ? $leadbusiness['phone'] : '-';
        // $phone1 = preg_match('/^(\d{3})(\d{3})(\d{4})$/', $phone1,  $matches);
        // $phone = ($phone1 != NULL && $phone1 != "") ?  $matches[1] . '-' . $matches[2] . '-' . $matches[3] : '-';
        $name = (isset($leadbusiness['name']) ) ? $leadbusiness['name'] : '-';
        $email = (isset($leadbusiness['email']) ) ? $leadbusiness['email'] : '-';
        $movesizeArra = [];
        $movesizeDetails = MstMoveSize::distinct()->get(['move_size_id', 'move_size'])->toArray();
        for ($m = 0; $m < count($movesizeDetails); $m++) {
            $movesizeArray[$movesizeDetails[$m]['move_size_id']] = $movesizeDetails[$m]['move_size'];
        }

        if($leaddnccallvalue == "yes") { $dncfor[] = "Call"; }
        if($leaddncsmsvalue == "yes") { $dncfor[] = "SMS"; }
        if($leaddncemailvalue == "yes") { $dncfor[] = "Email"; }
        $fromareacode = $fromzipcode = $fromcity = $fromstate = $toareacode = $tozipcode = $tocity = $tostate = $movesize = $movedate = $movedistance = "-";
        $categoryContent = "";
        if( $leadCategory ){
            $fromareacode = (isset($categoriesData->from_areacode) ) ? $categoriesData->from_areacode : '-';
            $fromzipcode = (isset($categoriesData->from_zipcode) ) ? $categoriesData->from_zipcode : '-';
            $fromcity  = (isset($categoriesData->from_city) ) ? $categoriesData->from_city : '-';
            $fromstate = (isset($categoriesData->from_state) ) ? $categoriesData->from_state : '-';
            if( $leadCategory != 2 || $leadCategory != "2" ){
                $toareacode = (isset($categoriesData->to_areacode) ) ? $categoriesData->to_areacode : '-';
                $tozipcode = (isset($categoriesData->to_zipcode) ) ? $categoriesData->to_zipcode : '-';
                $tocity = (isset($categoriesData->to_city) ) ? $categoriesData->to_city : '-';
                $tostate  = (isset($categoriesData->to_state) ) ? $categoriesData->to_state : '-';
                $movedistance  = (isset($categoriesData->distance) ) ? $categoriesData->distance : '-';
            
                $categoryContent = '<tr><td colspan="5"><b>To:</b></td></tr>
                <tr><td>To Areacode</td><td> : </td><td>'. $toareacode .'</td></tr>
                <tr><td>To Zip</td><td> : </td><td>'. $tozipcode .'</td></tr>
                <tr><td>To State</td><td> : </td><td>'. $tostate .'</td></tr>
                <tr><td>To City</td><td> : </td><td>'. $tocity .'</td></tr>';
            }
            if( $leadCategory == 1 || $leadCategory == "1" ){
                $movesize  = (isset($categoriesData->move_size_id) ) ? $movesizeArray[$categoriesData->move_size_id] : '-';
                $movedate = (isset($categoriesData->move_date) ) ? date("m/d/Y", strtotime( $categoriesData->move_date)) : '-';
                $categoryContent .= '<tr><td>Moving Size</td><td> : </td><td>'.$movesize.'</td></tr>
                                    <tr><td>Move Date</td><td> : </td><td>'.$movedate.'</td></tr>
                                    <tr><td>Distance</td><td> : </td><td>'. $movedistance .'</td></tr>';
            }else if( $leadCategory == 2 || $leadCategory == "2" ){ 
                $movedate = (isset($categoriesData->junk_remove_date) ) ? date("m/d/Y", strtotime( $categoriesData->junk_remove_date)) : '-';
                $categoryContent .= '<tr><td>Junk Removal Date</td><td> : </td><td>'.$movedate.'</td></tr>
                                    <tr><td>Junk Type</td><td> : </td><td>'.$categoriesData->MstJunkType->junk_type.'</td></tr>
                                    <tr><td>Junk Sub Type</td><td> : </td><td>'. implode(", ", $junkSubType).'</td></tr>
                                    <tr><td>Junk Property Type</td><td> : </td><td>'.ucfirst($categoriesData->property_type).'</td></tr>
                                    <tr><td>Junk Ownership Type</td><td> : </td><td>'.ucfirst($categoriesData->owner_type).'</td></tr>';
            }else if( $leadCategory == 3 || $leadCategory == "3" ){ 
                $movedate = (isset($categoriesData->lift_date) ) ? date("m/d/Y", strtotime( $categoriesData->lift_date)) : '-';
                $categoryContent .= '<tr><td>Move Date</td><td> : </td><td>'.$movedate.'</td></tr>
                                    <tr><td>Heavy Lift Type</td><td> : </td><td>'.$categoriesData->MstHeavyLiftingType->heavy_lifting_type.'</td></tr>
                                    <tr><td>Is Loading Assistance?</td><td> : </td><td>'.ucfirst($categoriesData->is_required_assistence).'</td></tr>
                                    <tr><td>Is Operational?</td><td> : </td><td>'.ucfirst($categoriesData->is_operational).'</td></tr>
                                    <tr><td>Distance</td><td> : </td><td>'. $movedistance .'</td></tr>';
            }else if( $leadCategory == 4 || $leadCategory == "4" ){ 
                $movedate = (isset($categoriesData->move_date) ) ? date("m/d/Y", strtotime( $categoriesData->move_date)) : '-';
                $categoryContent .= '<tr><td>Move Date</td><td> : </td><td>'.$movedate.'</td></tr>
                                    <tr><td>Car Type</td><td> : </td><td>'.$categoriesData->mstCarType->car_type.'</td></tr>
                                    <tr><td>Transport Type</td><td> : </td><td>'.ucfirst($categoriesData->transport_type).'</td></tr>
                                    <tr><td>Vehicle Condition</td><td> : </td><td>'.ucfirst($categoriesData->vehicle_condition).'</td></tr>
                                    <tr><td>Distance</td><td> : </td><td>'. $movedistance .'</td></tr>';
            }
        }
        // echo "<pre>emailDNCLeadToBusinessEmail = ";
        // print_r(  $categoriesData->MstJunkType->junk_type );
        // die;      
        $contents = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="ISO-8859-1">
            <title>Leads Information</title>
        </head>
        <body>
        <table>
            <tr><td>Dear '. ucfirst($business_name) .',</td></tr>
            <tr><td>Following lead is marked as DNC</td></tr>
            <tr><td><hr></td></tr>
            <tr><td>
        
            <tr><td><hr></td></tr>
            <tr><td>Your lead details are enlisted.</td></tr>
            <tr>
                <td>
                    <table>
                        <tr><td colspan="5"><b>Lead Information</b></td></tr>
                        <tr><td width="200px">Name</td><td> : </td><td>'. ucfirst($name) .'</td></tr>
                        <tr><td>Email</td><td> : </td><td>'. $email .'</td></tr>
                        <tr><td>Phone</td><td> : </td><td>'. $phone1 .'</td></tr>
        
                        <tr><td colspan="5"><b>From:</b></td></tr>
                        <tr><td>From Areacode</td><td> : </td><td>'. $fromareacode .'</td></tr>
                        <tr><td>From Zip</td><td> : </td><td>'. $fromzipcode .'</td></tr>
                        <tr><td>From State</td><td> : </td><td>'. $fromstate .'</td></tr>
                        <tr><td>From City</td><td> : </td><td>'. $fromcity .'</td></tr>     
                        '.$categoryContent .'                        
                    </table>
                </td>
            </tr>
        </table>
        </body>
        </html>';
       
        // $json = json_encode(array(
        //     'From' => $from,
        //     'To' => $to, 
        //     'Subject' => $subject, 
        //     'HtmlBody' => $contents      
        // ));
        // $from = "<EMAIL>";
        $from = "<EMAIL>";
        $to = $leadbusiness['low_fund_notification_email'];
        $subject = "DNC Lead: ".  $leadbusiness['name'];
        $json = json_encode(array(
            'From' => $from,
            'To' => $to, 
            'Subject' => $subject,
            'HtmlBody' => $contents,
            'Headers' => [],
            'Attachments' => []
        ));
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.postmarkapp.com/email');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Accept: application/json',
            'Content-Type: application/json',
            'X-Postmark-Server-Token: ' . $postmarkey
        ));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
        $result = curl_exec($ch);
        $response = json_decode($result, true); 
        $status = 'failed';
        
        // echo "<pre>emailDNCLeadToBusinessEmail = ";
        // print_r(  $campaign_name );
        // die;
        if($response['Message'] == 'OK' ){           
            $status = 'success';
        }else{
            $campaign_name = $leadbusiness['campaign_name'];
            $business_id = $leadbusiness['business_id'];
            $lead_id = $leadbusiness['lead_id'];
            $leadbusiness['name'];
            $emaildata = [
                'business_id' => $leadbusiness['business_id'],
                'lead_id' => $leadbusiness['lead_id'],
                'campaign_name' => $leadbusiness['campaign_name'],
                'business_name' => $business_name,
                'failed_to' => $leadbusiness['low_fund_notification_email']
            ];
            $objDNCLeadFailedEmail = new DNCLeadFailedEmail();
            $objDNCLeadFailedEmail->emailDNCLeadFailedEmail($emaildata, $response);
        } 
        $leadactiviyobj = new LeadActivity();
        //$leadactiviyobj->createActivity($leadbusiness['lead_id'], 4);
        EmailLog::create([
            "business_id" => $leadbusiness['business_id'],
            "subject" => $subject,
            "set_to" => $to,
            "set_from" => $from,
            "response" => $result,
            "type" => "dnc_business",
            'created_at' => date('Y-m-d H:i:s')
        ]);
        return $status;
    }
  
}
