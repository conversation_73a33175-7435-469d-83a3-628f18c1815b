<?php

namespace App\Http\Controllers\CronJob;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Helpers\Helper;
use Exception;
use DB;
use Illuminate\Support\Facades\Log;
use App\Models\Campaign\Campaign;
use App\Models\Campaign\CampaignOrganic;
use App\Models\Business\Business;
use App\Helpers\CommonFunctions;
use App\Models\Lead\LeadRouting;
use DateTime;
use DateInterval;
use App\Models\Outbound\LeadCallNumber;
use App\Models\Outbound\LeadCallNumberAreacode;

class PurchaseRelayNumberController extends Controller {

    public function purchaseBusinessRelayNumber(Request $request) {
        $parameters = $request->all();
        $businessIdArr = array();
        if (isset($parameters['businessId']) && trim($parameters['businessId']) > 0) {
            $businessIdArr = explode(",", strtoupper(trim($parameters['businessId'])));
        } else {
            echo "Please pass business id.";
            die;
        }
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        if (strpos($matrixBaseURL_Outbound, 'linkup.software') !== false) { //Replace linkup.software
            $this->purchaseRelayNumber($businessIdArr);
        }
    }

    public function releasePurchaseBusinessRelaynumber() {
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        //$matrixBaseURL_Outbound = 'linkup.software';
        if (strpos($matrixBaseURL_Outbound, 'linkup.software') !== false) {
            $todayDateTime = date('Y-m-d 00:00:00');
            $todayDate = date('Y-m-d');
            $oldDateTime = date('Y-m-d 00:00:00', strtotime("-90 days"));
            //echo $todayDate."==".$oldDateTime;die;
            //echo "SELECT b.created_at,b.business_id, b.owner_name, b.business_name, b.rely_number, b.credit_available FROM business b  WHERE b.business_id NOT IN (SELECT DISTINCT(b.business_id) FROM campaign b WHERE campaign_id IN (SELECT DISTINCT(r.campaign_id) FROM lead_routing r WHERE r.created_at BETWEEN '" . $oldDateTime . "' AND '" . $todayDateTime . "')) AND b.created_at < '" . $todayDate . "'";die;
            $leadDetail = DB::select("SELECT b.created_at,b.business_id, b.owner_name, b.business_name, b.rely_number, b.credit_available FROM business b  WHERE b.business_id NOT IN (SELECT DISTINCT(b.business_id) FROM campaign b WHERE campaign_id IN (SELECT DISTINCT(r.campaign_id) FROM lead_routing r WHERE r.created_at BETWEEN '" . $oldDateTime . "' AND '" . $todayDateTime . "')) AND b.created_at < '" . $todayDate . "' AND b.rely_number IS NOT NULL"); //Release this number
            $endDecObj = new Helper;
            $general_variable_1 = Helper::checkServer()['general_variable_1'];
            $general_variable_2 = Helper::checkServer()['general_variable_2'];
            $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
            $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
            echo $decVariable1 . "==" . $decVariable2;
            die;
            DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 releasePurchaseBusinessRelaynumber Release Data : " . json_encode($leadDetail) . '===Date-' . $todayDate . ", From Date=" . $oldDateTime . ", To Date=" . $todayDateTime]);
            echo "<pre>";
            print_r($leadDetail);
            die;
            $businessIdArr = $purBusinessIdArr = array();
            for ($f = 0; $f < count($leadDetail); $f++) {
                //echo "<pre>";print_r($leadDetail[$f]);die;
                if ($leadDetail[$f]->rely_number != "**********" && trim($leadDetail[$f]->rely_number) != "" && trim($leadDetail[$f]->rely_number) != null) {
                    $newNumber = "1" . $leadDetail[$f]->rely_number;
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/Number/' . $newNumber . '/');
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
                    curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
                    curl_setopt($ch, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
                    $response = curl_exec($ch);
                    curl_close($ch);
                    $response = json_decode($response, true);
                    //echo "<pre>";print_r($response);die;
                    if (!isset($response['error'])) {
                        $businessIdArr[] = $leadDetail[$f]->id;
                        DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 releasePurchaseBusinessRelaynumber Released Number : " . $newNumber . '===Business Id-' . $leadDetail[$f]->id . ", Number=" . $newNumber . ", Business Name=" . $leadDetail[$f]->business_name]);
                    } else {
                        DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 releasePurchaseBusinessRelaynumber Released Number Error : " . $newNumber . '===Business Id-' . $leadDetail[$f]->id . ", Number=" . $newNumber . ", Business Name=" . $leadDetail[$f]->business_name . ",Error Found=" . json_encode($response)]);
                    }
                } else {
                    //echo trim($leadDetail[$f]->rely_number);die;
                }
            }
            if (count($businessIdArr) > 0) {
                Business::whereIn('business_id', $businessIdArr)->update(['rely_number' => null]);
            }

            $purchaseBusinessData = DB::select("SELECT b.business_id,count(lr.lead_routing_id)  as leads, MAX(lr.created_at) as route_date FROM business b INNER join campaign bc on b.business_id=bc.business_id inner join lead_routing lr on bc.campaign_id=lr.campaign_id WHERE lr.created_at BETWEEN '" . $oldDateTime . "' AND '" . $todayDateTime . "' AND b.rely_number IS NULL GROUP by b.business_id ORDER BY MAX(lr.created_at) DESC"); //Add Relay Number
            //echo "<pre>dd";print_r($purchaseBusinessData);die;
            for ($d = 0; $d < count($purchaseBusinessData); $d++) {
                $purBusinessIdArr[] = $purchaseBusinessData[$d]->id;
                //break;
            }
            if (count($purBusinessIdArr) > 0) {
                DB::table('dev_debug')->insert(['sr_no' => 4, 'result' => "4 releasePurchaseBusinessRelaynumber Purchase Data : " . json_encode($purchaseBusinessData) . '===Date-' . $todayDate . ", From Date=" . $oldDateTime . ", To Date=" . $todayDateTime]);
                $this->purchaseRelayNumber($purBusinessIdArr);
            }
        } else {
            echo "This Cron only for Live Server.";
            die;
        }
        echo "done";
        die;
    }

    public function purchaseOrganicRelayNumber($campaignIdArr, $is_forward = 1) {
        try {
            $businessIdArr = $campaignnameArr = $usedNumberArr = $businessNameArr = array();
            $checkBusinessNumber = CampaignOrganic::whereIn('campaign_id', $campaignIdArr)->get()->toArray();
            $getCampData = Campaign::whereIn('campaign_id', $campaignIdArr)->get(['campaign_id', 'campaign_name', 'business_id'])->toArray();
            for ($g = 0; $g < count($getCampData); $g++) {
                $campaignnameArr[$getCampData[$g]['campaign_id']] = $getCampData[$g]['campaign_name'];
                $businessIdArr[$getCampData[$g]['campaign_id']] = $getCampData[$g]['business_id'];
            }
            for ($b = 0; $b < count($checkBusinessNumber); $b++) {
                $primaryId = $checkBusinessNumber[$b]['campaign_organic_id'];
                if (trim($checkBusinessNumber[$b]['contact_number']) == "" || trim($checkBusinessNumber[$b]['contact_number']) == NULL) {
                    echo $primaryId . "<br>";
                    $campaignName = $checkBusinessNumber[$b]['business_name'];
                    if (isset($campaignnameArr[$checkBusinessNumber[$b]['campaign_id']])) {
                        $campaignName = $campaignnameArr[$checkBusinessNumber[$b]['campaign_id']];
                    }
                    $campaignName = str_replace("- ", "", $campaignName);
                    $aliasName = "ORG_" . str_replace(" ", "_", trim($campaignName));
                    $endDecObj = new Helper;
                    $general_variable_1 = Helper::checkServer()['general_variable_1'];
                    $general_variable_2 = Helper::checkServer()['general_variable_2'];
                    $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
                    $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/PhoneNumber/?country_iso=US&type=local&state=san francisco&services=voice');
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
                    curl_setopt($ch, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
                    $getNumberJson = curl_exec($ch);
                    //echo "<pre>";print_r($getNumberJson);die;
                    $getNumberResponse = json_decode($getNumberJson, true);
                    //echo "<pre>";print_r($getNumberResponse);
                    //echo "<br>";
                    if (curl_errno($ch)) {
                        echo 'Error:' . curl_error($ch);
                        die;
                    }
                    curl_close($ch);
                    if (isset($getNumberResponse['objects'][0])) {
                        $numberArr = $getNumberResponse['objects'][0];
                        $purchaseNumber = $numberArr['number'];
                        $relayAppId = "*****************"; // For Interface Call (Relay Number) Move
                        $para = json_encode(array('time_limit' => '3600'));
                        //echo "<pre>";print_r($purchaseNumber);die;
                        $ch3 = curl_init();
                        curl_setopt($ch3, CURLOPT_URL, 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/PhoneNumber/' . $purchaseNumber . '/');
                        curl_setopt($ch3, CURLOPT_RETURNTRANSFER, 1);
                        curl_setopt($ch3, CURLOPT_POST, 1);
                        curl_setopt($ch3, CURLOPT_POSTFIELDS, $para);
                        curl_setopt($ch3, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
                        $headers = array();
                        $headers[] = 'Content-Type: application/json';
                        curl_setopt($ch3, CURLOPT_HTTPHEADER, $headers);
                        $assignAppjson = curl_exec($ch3);
                        $assignAppResponse = json_decode($assignAppjson, true);
                        //echo "<pre>b";print_r($assignAppResponse);
                        //echo "<br>";
                        if (curl_errno($ch3)) {
                            echo 'Error:' . curl_error($ch3);
                            die;
                        }
                        curl_close($ch3);

                        $para = json_encode(array('time_limit' => '3600', 'app_id' => $relayAppId, 'alias' => $aliasName));
                        $ch2 = curl_init();
                        curl_setopt($ch2, CURLOPT_URL, 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/Number/' . $purchaseNumber . '/');
                        curl_setopt($ch2, CURLOPT_RETURNTRANSFER, 1);
                        curl_setopt($ch2, CURLOPT_POST, 1);
                        curl_setopt($ch2, CURLOPT_POSTFIELDS, $para);
                        curl_setopt($ch2, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
                        $headers = array();
                        $headers[] = 'Content-Type: application/json';
                        curl_setopt($ch2, CURLOPT_HTTPHEADER, $headers);
                        $aliasNamejson = curl_exec($ch2);
                        $aliasNameResponse = json_decode($aliasNamejson, true);
                        if (curl_errno($ch2)) {
                            echo 'Error:' . curl_error($ch2);
                            die;
                        }
                        curl_close($ch2);

                        if (isset($aliasNameResponse['message']) && strtolower(trim($aliasNameResponse['message'])) == "changed") {
                            echo "asd<br>";
                            CampaignOrganic::where('campaign_organic_id', $primaryId)->update(['contact_number' => $this->setTenDigitNumber($purchaseNumber)]);
                            echo "Update contact_number in Organic Campaign Id =" . $checkBusinessNumber[$b]['campaign_id'] . "<br>";
                        }
                    } else {
                        echo "Plivo number not found";
                        die;
                    }
                    //echo "<pre>";print_r($businessPhoneArr);die;
                }
                if ($is_forward == 1) {
                    if (trim($checkBusinessNumber[$b]['forward_number']) == "") {
                        if (isset($businessIdArr[$checkBusinessNumber[$b]['campaign_id']])) {
                            $business_id = $businessIdArr[$checkBusinessNumber[$b]['campaign_id']];
                            $getBusinessName = Business::where('business_id', $business_id)->get(['business_id', 'forward_number'])->toArray();
                            if (count($getBusinessName) > 0) {
                                CampaignOrganic::where('campaign_organic_id', $primaryId)->update(['forward_number' => $getBusinessName[0]['forward_number']]);
                                echo "Update forward_number in Organic Campaign Id =" . $checkBusinessNumber[$b]['campaign_id'] . "<br>";
                            }
                        }
                    }
                }
                echo "<br>=============================================================================================================================<br>";
            }
            //echo "<pre>";print_r($businessnameArr);die;
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        echo $message;
        die;
    }

    public function purchaseBusinessRelayNumberCron(Request $request) {
        $parameters = $request->all();
        $businessIdArr = array();
        if (isset($parameters['businessId']) && trim($parameters['businessId']) > 0) {
            $businessIdArr = explode(",", strtoupper(trim($parameters['businessId'])));
        } else {
            echo "Please pass business id.";
            die;
        }
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        if (strpos($matrixBaseURL_Outbound, 'linkup.software') !== false) {
            $this->purchaseRelayNumber($businessIdArr);
        }
    }

    public function purchaseOrganicRelayNumberCron(Request $request) {
        $parameters = $request->all();
        $campaignIdArr = array();
        if (isset($parameters['campaignId']) && trim($parameters['campaignId']) > 0) {
            $campaignIdArr = explode(",", strtoupper(trim($parameters['campaignId'])));
        } else {
            echo "Please pass campaign id.";
            die;
        }
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        if (strpos($matrixBaseURL_Outbound, 'linkup.software') !== false) {
            $this->purchaseOrganicRelayNumber($campaignIdArr, 1);
        }
        echo "Successfully Organic Campaign Number Updated.";
        die;
    }

    public function purchaseRelayNumber($businessIdArr) {
        try {
            $businessPhoneArr = $businessnameArr = $usedNumberArr = array();
            $checkBusinessNumber = Business::whereIn('business_id', $businessIdArr)->whereNull('rely_number')->get()->toArray();
            for ($b = 0; $b < count($checkBusinessNumber); $b++) {
                $businessPhoneArr[$checkBusinessNumber[$b]['business_id']] = $checkBusinessNumber[$b]['rely_number'];
            }
            $endDecObj = new Helper;
            $general_variable_1 = Helper::checkServer()['general_variable_1'];
            $general_variable_2 = Helper::checkServer()['general_variable_2'];
            $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
            $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/PhoneNumber/?country_iso=US&type=tollfree&services=voice&offset=80');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
            curl_setopt($ch, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
            $getNumberJson = curl_exec($ch);
            //echo "<pre>";print_r($getNumberJson);die;
            $getNumberResponse = json_decode($getNumberJson, true);
            //echo "<pre>";print_r($getNumberResponse);die;
            if (curl_errno($ch)) {
                //echo 'Error:' . curl_error($ch);
                //die;
            }
            curl_close($ch);
            $relayAppId = "*****************"; // For Interface Call (Relay Number) Move
            $getBusinessName = Business::whereIn('business_id', $businessIdArr)->get(['business_id', 'business_name'])->toArray();
            for ($g = 0; $g < count($getBusinessName); $g++) {
                $businessnameArr[$getBusinessName[$g]['business_id']] = $getBusinessName[$g]['business_name'];
            }

            //echo "<pre>";print_r($businessnameArr);die;
            if (isset($getNumberResponse['objects'])) {
                for ($p = 0; $p < count($businessIdArr); $p++) {
                    if (isset($businessPhoneArr[$businessIdArr[$p]])) {
                        //echo "Business Id :" . $businessIdArr[$p] . "== Relay Number : " . $businessPhoneArr[$businessIdArr[$p]] . "== Exists<br>";
                    } else {
                        if (isset($businessnameArr[$businessIdArr[$p]])) {
                            $aliasName = $businessnameArr[$businessIdArr[$p]];
                            //echo $aliasName."<br>";
                            if (isset($getNumberResponse['objects'][$p])) {
                                $numberArr = $getNumberResponse['objects'][$p];
                                if (!in_array($numberArr, $usedNumberArr)) {
                                    $usedNumberArr[] = $numberArr;
                                    //echo "<pre>";print_r($numberArr);die;
                                    $newNumber = $numberArr['number'];
                                    $para = json_encode(array('time_limit' => '3600'));
                                    $ch1 = curl_init();
                                    curl_setopt($ch1, CURLOPT_URL, 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/PhoneNumber/' . $newNumber . '/');
                                    curl_setopt($ch1, CURLOPT_RETURNTRANSFER, 1);
                                    curl_setopt($ch1, CURLOPT_POST, 1);
                                    curl_setopt($ch1, CURLOPT_POSTFIELDS, $para);
                                    curl_setopt($ch1, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
                                    $headers = array();
                                    $headers[] = 'Content-Type: application/json';
                                    curl_setopt($ch1, CURLOPT_HTTPHEADER, $headers);
                                    $result = curl_exec($ch1);
                                    //echo "<pre>";print_r($result);die;
                                    if (curl_errno($ch1)) {
                                        //echo 'Error:' . curl_error($ch1);
                                        //die;
                                    }
                                    curl_close($ch1);
                                    //Added By HJ On 11-07-2022 For Update Config Number App Id and Alias Name Start
                                    //echo $newNumber."<br>";
                                    $para = json_encode(array('time_limit' => '3600', 'app_id' => $relayAppId, 'alias' => $aliasName));
                                    $ch3 = curl_init();
                                    curl_setopt($ch3, CURLOPT_URL, 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/Number/' . $newNumber . '/');
                                    curl_setopt($ch3, CURLOPT_RETURNTRANSFER, 1);
                                    curl_setopt($ch3, CURLOPT_POST, 1);
                                    curl_setopt($ch3, CURLOPT_POSTFIELDS, $para);
                                    curl_setopt($ch3, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
                                    $headers = array();
                                    $headers[] = 'Content-Type: application/json';
                                    curl_setopt($ch3, CURLOPT_HTTPHEADER, $headers);
                                    $updateConfigJson = curl_exec($ch3);
                                    $updateConfigResponse = json_decode($updateConfigJson, true);
                                    //echo "<pre>b";print_r($updateConfigJson);die;
                                    if (curl_errno($ch3)) {
                                        //echo 'Error:' . curl_error($ch3);
                                        //die;
                                    }
                                    curl_close($ch3);
                                    if (isset($updateConfigResponse['message']) && strtolower(trim($updateConfigResponse['message'])) == "changed") {
                                        //echo "Purchased Plivo Number=" . $newNumber . " for business=" . $businessIdArr[$p] . "<br>";
                                        $insertArr = array();
                                        $insertArr['rely_number'] = $this->setTenDigitNumber($newNumber);
                                        Business::where('business_id', $businessIdArr[$p])->update($insertArr);
                                        //echo "Updated Business Relay Number In Business Table = " . $businessIdArr[$p] . "==" . $insertArr['rely_number'] . "<br>";
                                        //die;
                                    }
                                } else {
                                    //echo "Business Number Not Added of Business Id=" . $businessIdArr[$p] . ", B'coz Number Already Used==" . $newNumber . "<br>";
                                }
                            } else {
                                //echo "Plivo Number limit over.";
                                //die;
                            }
                        } else {
                            //echo "Business Name not found.<br>";
                        }
                    }
                }
            } else {
                //echo "Plivo Number not found.";
                //die;
            }
            //echo "Relay number script done.";
            //die;
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
    }

    public function assignrelynumberorganiccamp() {
        try {
            $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
            if (strpos($matrixBaseURL_Outbound, 'linkup.software') !== false) {
                DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 Run Cron assignrelynumberorganiccamp For Assign relynumber to organic Campaign ==" . date("Y-m-d H:i:s")]);
                $campaignIdArr = Campaign::where('campaign_type_id', 4)->where("is_active", 'yes')->pluck('campaign_id')->toArray();
                $blankNumber = "";
                //echo "<pre>";print_r($campaignIdArr);die;
                $organicCampaigns = CampaignOrganic::whereIn('campaign_id', $campaignIdArr)->where(function ($q) use ($blankNumber) {
                            $q->where(function ($q) use ($blankNumber) {
                                $q->whereNull('contact_number');
                            })->orWhere(function ($q) use ($blankNumber) {
                                $q->where("contact_number", $blankNumber);
                            });
                        })->pluck('campaign_id')->toArray();
                echo "<pre>";
                print_r($organicCampaigns);
                die;
                if (count($organicCampaigns) > 0) {
                    DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 assignrelynumberorganiccamp Organic Campaigns: " . json_encode($organicCampaigns)]);
                    $this->purchaseOrganicRelayNumber($organicCampaigns, 0);
                } else {
                    DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 assignrelynumberorganiccamp No Organic Campaigns found."]);
                }
                return response()->json($organicCampaigns);
            } else {
                echo "This Cron only for Live Server.";
                die;
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
            DB::table('dev_debug')->insert(['sr_no' => 4, 'result' => "4 assignrelynumberorganiccamp Assign relynumber to organic Campaign Exception Error : " . $error]);
            return "No campaigns";
            die;
        }
    }

    public function setTenDigitNumber($phone) {
        /*$phoneLength = strlen(trim($phone));
        if ($phoneLength > 10) {
            $removeChr = $phoneLength - 10;
            $phone = substr(trim($phone), $removeChr);
        }*/
        $phone = preg_replace('/[^0-9]/', '', $phone);
        $phoneLength = strlen($phone);
        if ($phoneLength > 10) {
            $phone = substr($phone, -10);
        }
        return $phone;
    }

    public function purchaseReleaseBusinessRelayCommio() {
        //Added By KP On 13-09-2023 For Relase Business Relay number From Commio Start
        $endDecObj = new CommonFunctions;
        $endDate = date('Y-m-d H:i:s');
        $startDate = date('Y-m-d H:i:s', strtotime($endDate . '-26 days'));
        //echo $startDate."==".$endDate;die;
        DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => '1 SnapIt Log purchaseReleaseBusinessRelayCommio Run at: ' . $endDate . "== 26 days ago =" . $startDate, 'created_at' => date("Y-m-d H:i:s")]);
        try {
            echo $startDate."==".$endDate."<br>";
            $campaignids = LeadRouting::where('route_status', 'sold')->where('created_at', '>',$startDate)->groupBy('campaign_id')->pluck('campaign_id')->toArray();
            //echo "<pre>";print_r($campaignids);exit();
            $releaseNumberArr = $getBusinessIds = $getBusinessData = array();
            if (count($campaignids) > 0) {
                $getBusinessIds = Campaign::whereIn('campaign_id', $campaignids)->groupBy('business_id')->pluck('business_id')->toArray();
                $getBusinessData = Business::whereNotIn('business_id', $getBusinessIds)->where('rely_number', '>', 0)->get(['business_id', 'rely_number'])->toArray();
                //echo "<pre>";print_r($getBusinessData);
                //echo "<br>";
                foreach ($getBusinessData as $key => $value) {
                    $rely_number = array();
                    $rely_number[] = trim($value['rely_number']);
                    $business_id = trim($value['business_id']);
                    $response = $endDecObj->ReleaseComioNumber($rely_number);
                    echo "<pre>";
                    print_r($response);
                    echo $response->order->status."==".$business_id.'<br>';
                    echo "<br>";
                    if (isset($response->order->status) && strtolower(trim($response->order->status)) == 'completed') {
                        Business::where('business_id', $business_id)->update(['rely_number' => null]);
                        $releaseNumberArr[$business_id] = $response;
                    }
                }
                echo "Done";
            }
            DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => '2 SnapIt Log purchaseReleaseBusinessRelayCommio release: ' . json_encode($campaignids) . "==" . json_encode($getBusinessIds) . "==Business Data" . json_encode($getBusinessData) . "== Release Numbers =" . json_encode($releaseNumberArr), 'created_at' => date("Y-m-d H:i:s")]);
            //Added By KP On 13-09-2023 For Relase Business Relay number From Commio Start
            $accountId = 22374;
            $commioToken = "milan:68758d120ac29760793bb4c940550281ec4d51e6";
            $commioToken = base64_encode($commioToken); //Your authentication string is a base64 encoded version of username:token
            $getBusinessData = Business::whereNull('rely_number')->get(['business_id', 'business_name'])->toArray();
            $businessNameArr = array();
            for ($f = 0; $f < count($getBusinessData); $f++) {
                $businessNameArr[$getBusinessData[$f]['business_id']] = $getBusinessData[$f]['business_name'];
            }
            $businessIdArr = array_keys($businessNameArr);
            if (count($businessIdArr) > 0) {
                //echo "<pre>";print_r($businessIdArr);die;
                $getCampaignIds = Campaign::whereIn('business_id', $businessIdArr)->pluck('campaign_id')->toArray();
                $uniqueAssociativeArray = array_unique($getCampaignIds);
                $reindexedArray = array_values($uniqueAssociativeArray);
                //echo "<pre>";print_r($reindexedArray);die;
                $currentDate = new DateTime();
                $interval = new DateInterval('P30D'); // P30D means a period of 30 days
                $before30Days = $currentDate->sub($interval);
                $valStartDate = $before30Days->format('Y-m-d H:i:s');
                $valEndDate = date("Y-m-d H:i:s");
                //echo $valStartDate."==".$valEndDate;die;
                $lead_routing = LeadRouting::whereIn('campaign_id', $reindexedArray)->where('route_status', 'sold')->whereBetween('created_at', [$valStartDate, $valEndDate])->groupBy('campaign_id')->pluck('campaign_id')->toArray();
                //echo "<pre>";print_r($lead_routing);die;
                $businessesCampaign = Campaign::whereIn('campaign_id', $lead_routing)->groupBy('business_id')->pluck('business_id')->toArray();
                //echo "<pre>";print_r($businessesCampaign);die;
                if (count($businessesCampaign) > 0) {
                    echo implode(",", $businessesCampaign);
                    echo "<br>";
                    echo "<pre>";
                    print_r($businessesCampaign);
                    echo "<br>";
                }
                DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => '3 SnapIt Log purchaseReleaseBusinessRelayCommio release: ' . json_encode($businessesCampaign), 'created_at' => date("Y-m-d H:i:s")]);
                for ($fd = 0; $fd < count($businessesCampaign); $fd++) {
                    $curl = curl_init();
                    curl_setopt_array($curl, array(
                        CURLOPT_URL => 'https://api.thinq.com/inbound/get-numbers?searchType=domestic&searchBy=ratecenter&quantity=1&contiguous=false&state=CA&rateCenter=&related=true',
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => '',
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'GET',
                        CURLOPT_HTTPHEADER => array(
                            'Content-Type: application/json',
                            'Authorization: Basic ' . $commioToken . ''
                        ),
                    ));

                    $response = curl_exec($curl);
                    curl_close($curl);
                    $commioNumber = json_decode($response, true);
                    //echo "<pre>"; print_r($commioNumber); die;
                    DB::table('dev_debug')->insert(['sr_no' => 4, 'result' => '4 SnapIt Log getOrganicCampaignCommio get-numbers: ' . $response, 'created_at' => date("Y-m-d H:i:s")]);
                    if (isset($commioNumber['message']) && strtolower($commioNumber['message']) == "success" && isset($commioNumber['dids'][0])) {
                        $did = $commioNumber['dids'][0]['id'];
                        echo $did . "<br>";
                        $postFields = [
                            "order" => [
                                "tns" => [
                                    [
                                        "caller_id" => null,
                                        "account_location_id" => null,
                                        "sms_routing_profile_id" => 699,
                                        "route_id" => 13834,
                                        "features" => [
                                            "cnam" => false,
                                            "sms" => true,
                                            "e911" => false
                                        ],
                                        "did" => $did
                                    ]
                                ],
                                "blocks" => []
                            ],
                        ];

                        $curl = curl_init();
                        curl_setopt_array($curl, array(
                            CURLOPT_URL => 'https://api.thinq.com/account/' . $accountId . '/origination/order/create',
                            CURLOPT_RETURNTRANSFER => true,
                            CURLOPT_ENCODING => '',
                            CURLOPT_MAXREDIRS => 10,
                            CURLOPT_TIMEOUT => 0,
                            CURLOPT_FOLLOWLOCATION => true,
                            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                            CURLOPT_CUSTOMREQUEST => 'POST',
                            CURLOPT_POSTFIELDS => json_encode($postFields),
                            CURLOPT_HTTPHEADER => array(
                                'Content-Type: application/json',
                                'Authorization: Basic ' . $commioToken . ''
                            ),
                        ));
                        $response = curl_exec($curl);
                        curl_close($curl);
                        $purchaseNumber = json_decode($response, true);
                        DB::table('dev_debug')->insert(['sr_no' => 5, 'result' => '5 SnapIt Log purchaseReleaseBusinessRelayCommio order create purchase Comio Number=' . $did . '==Business Update Data' . json_encode($purchaseNumber), 'created_at' => date("Y-m-d H:i:s")]);
                        //echo "<pre>"; print_r($response); die;
                        if (isset($purchaseNumber['status']) && strtolower($purchaseNumber['status']) == "created" && $purchaseNumber['id']) {
                            $orderId = $purchaseNumber['id'];
                            $curl = curl_init();
                            curl_setopt_array($curl, array(
                                CURLOPT_URL => 'https://api.thinq.com/account/' . $accountId . '/origination/order/complete/' . $orderId,
                                CURLOPT_RETURNTRANSFER => true,
                                CURLOPT_ENCODING => '',
                                CURLOPT_MAXREDIRS => 10,
                                CURLOPT_TIMEOUT => 0,
                                CURLOPT_FOLLOWLOCATION => true,
                                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                                CURLOPT_CUSTOMREQUEST => 'POST',
                                CURLOPT_HTTPHEADER => array(
                                    'Content-Type: application/json',
                                    'Authorization: Basic ' . $commioToken . ''
                                ),
                            ));
                            $response = curl_exec($curl);
                            curl_close($curl);
                            $completeOrder = json_decode($response, true);
                            DB::table('dev_debug')->insert(['sr_no' => 6, 'result' => '6 SnapIt Log purchaseReleaseBusinessRelayCommio order complete purchase Comio Number=' . $did . '==Business Update Data' . json_encode($completeOrder), 'created_at' => date("Y-m-d H:i:s")]);
                            //echo "<pre>"; print_r($response); die;
                            if (isset($completeOrder['status']) && strtolower($completeOrder['status']) == "completed") {
                                //Insert into lead call number
                                $insertDataArr = array();
                                $insertDataArr['phone'] = $did;
                                $insertDataArr['callit_number'] = substr($did, 1, 3) . " " . substr($did, 4, 3) . "-" . substr($did, 7, 4);
                                $insertDataArr['lead_source_id'] = 19;
                                $insertDataArr['is_current'] = "yes";
                                $insertDataArr['day'] = 0;
                                $insertDataArr['is_sms'] = "yes";
                                $insertDataArr['lead_category_id'] = 1;
                                $business_name = $businessesCampaign[$fd];
                                if (isset($businessNameArr[$businessesCampaign[$fd]])) {
                                    $business_name = $businessNameArr[$businessesCampaign[$fd]];
                                }
                                $insertDataArr['comment'] = $business_name . ' business relay number';
                                $insertDataArr['status'] = "active";
                                $insertDataArr['phone_source'] = "BR";
                                $insertDataArr['provider'] = "commio";
                                $insertDataArr['created_at'] = date("Y-m-d H:i:s");
                                LeadCallNumber::create($insertDataArr);
                                //Here Update into business
                                $updateBusinessArr = array();
                                $updateBusinessArr['rely_number'] = substr(trim($did), 1);
                                Business::where('business_id', $businessesCampaign[$fd])->update($updateBusinessArr);
                                echo $businessesCampaign[$fd] . "==" . $did . "== Business number purchased and updated";

                                DB::table('dev_debug')->insert(['sr_no' => 7, 'result' => '7 SnapIt Log purchaseReleaseBusinessRelayCommio purchase business name: ' . $business_name . "==Comio Number=" . $did . "==Business Update Data" . json_encode($updateBusinessArr) . "==Lead call number inser data=" . json_encode($insertDataArr), 'created_at' => date("Y-m-d H:i:s")]);
                            } else {
                                DB::table('dev_debug')->insert(['sr_no' => 8, 'result' => '8 SnapIt Log purchaseReleaseBusinessRelayCommio 3 Order Not found campaign_id: ' . $businessesCampaign[$fd], 'created_at' => date("Y-m-d H:i:s")]);
                                echo $businessesCampaign[$fd] . "== 3 Order Not found<br>";
                            }
                        } else {
                            DB::table('dev_debug')->insert(['sr_no' => 9, 'result' => '9 SnapIt Log purchaseReleaseBusinessRelayCommio 2 Purchase Not found campaign_id: ' . $businessesCampaign[$fd], 'created_at' => date("Y-m-d H:i:s")]);
                            echo $businessesCampaign[$fd] . "== 2 Purchase Not found<br>";
                        }
                    } else {
                        DB::table('dev_debug')->insert(['sr_no' => 10, 'result' => '10 SnapIt Log purchaseReleaseBusinessRelayCommio 2 Search Not found campaign_id: ' . $businessesCampaign[$fd], 'created_at' => date("Y-m-d H:i:s")]);
                        echo $businessesCampaign[$fd] . "== 1 Search Not found<br>";
                    }
                    echo ($fd + 1) . "done<br>";
                }
            }
        } catch (Exception $ex) {
            $message = $ex->getMessage();
            Log::info('11 SnapIt Log purchaseReleaseBusinessRelayCommio Catch error: ' . $message);
            DB::table('dev_debug')->insert(['sr_no' => 11, 'result' => '11 SnapIt Log purchaseReleaseBusinessRelayCommio Catch error: ' . $message, 'created_at' => date("Y-m-d H:i:s")]);
        }
        DB::table('dev_debug')->insert(['sr_no' => 12, 'result' => '12 SnapIt Log purchaseReleaseBusinessRelayCommio Successfully ran', 'created_at' => date("Y-m-d H:i:s")]);
        echo "Done";
    }

    public function getOrganicCampaignCommio() {
        DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => '1 SnapIt Log getOrganicCampaignCommio Cron run at: ' . date("Y-m-d H:i:s"), 'created_at' => date("Y-m-d H:i:s")]);
        try {
            $accountId = 22374;
            $commioToken = "milan:68758d120ac29760793bb4c940550281ec4d51e6";
            $commioToken = base64_encode($commioToken); //Your authentication string is a base64 encoded version of username:token
            $businessesCampaign = CampaignOrganic::leftJoin('campaign as c', 'campaign_organic.campaign_id', 'c.campaign_id')->select('c.campaign_id', 'c.campaign_name', 'campaign_organic.is_call_lead')->where('c.is_active', 'yes')->where('campaign_organic.contact_number' ,0)->groupBy('campaign_organic.campaign_id')->get()->toArray();
            if (count($businessesCampaign) > 0) {
                echo implode(",", $businessesCampaign);
                echo "<br>";
                echo "<pre>";
                print_r($businessesCampaign);
            }
            DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => '2 SnapIt Log getOrganicCampaignCommio Data: ' . json_encode($businessesCampaign), 'created_at' => date("Y-m-d H:i:s")]);
            for ($fd = 0; $fd < count($businessesCampaign); $fd++) { if ($businessesCampaign[$fd]['is_call_lead'] == 'yes') {
                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.thinq.com/inbound/get-numbers?searchType=domestic&searchBy=ratecenter&quantity=1&contiguous=false&state=CA&rateCenter=&related=true',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'GET',
                    CURLOPT_HTTPHEADER => array(
                        'Content-Type: application/json',
                        'Authorization: Basic ' . $commioToken . ''
                    ),
                ));

                $response = curl_exec($curl);
                curl_close($curl);
                $commioNumber = json_decode($response, true);
                //echo "<pre>"; print_r($response); die;
                DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => '3 SnapIt Log getOrganicCampaignCommio get-numbers: ' . $response, 'created_at' => date("Y-m-d H:i:s")]);
                if (isset($commioNumber['message']) && strtolower($commioNumber['message']) == "success" && isset($commioNumber['dids'][0])) {
                    $did = $commioNumber['dids'][0]['id'];
                    $postFields = [
                        "order" => [
                            "tns" => [
                                [
                                    "caller_id" => null,
                                    "account_location_id" => null,
                                    "sms_routing_profile_id" => 699,
                                    "route_id" => 13835,
                                    "features" => [
                                        "cnam" => false,
                                        "sms" => true,
                                        "e911" => false
                                    ],
                                    "did" => $did
                                ]
                            ],
                            "blocks" => []
                        ],
                    ];

                    $curl = curl_init();
                    curl_setopt_array($curl, array(
                        CURLOPT_URL => 'https://api.thinq.com/account/' . $accountId . '/origination/order/create',
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => '',
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'POST',
                        CURLOPT_POSTFIELDS => json_encode($postFields),
                        CURLOPT_HTTPHEADER => array(
                            'Content-Type: application/json',
                            'Authorization: Basic ' . $commioToken . ''
                        ),
                    ));
                    $response = curl_exec($curl);
                    curl_close($curl);
                    $purchaseNumber = json_decode($response, true);
                    //echo "<pre>"; print_r($response); die;
                    DB::table('dev_debug')->insert(['sr_no' => 4, 'result' => '4 SnapIt Log getOrganicCampaignCommio order create: ' . $response, 'created_at' => date("Y-m-d H:i:s")]);
                    if (isset($purchaseNumber['status']) && strtolower($purchaseNumber['status']) == "created" && $purchaseNumber['id']) {
                        $orderId = $purchaseNumber['id'];
                        $curl = curl_init();
                        curl_setopt_array($curl, array(
                            CURLOPT_URL => 'https://api.thinq.com/account/' . $accountId . '/origination/order/complete/' . $orderId,
                            CURLOPT_RETURNTRANSFER => true,
                            CURLOPT_ENCODING => '',
                            CURLOPT_MAXREDIRS => 10,
                            CURLOPT_TIMEOUT => 0,
                            CURLOPT_FOLLOWLOCATION => true,
                            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                            CURLOPT_CUSTOMREQUEST => 'POST',
                            CURLOPT_HTTPHEADER => array(
                                'Content-Type: application/json',
                                'Authorization: Basic ' . $commioToken . ''
                            ),
                        ));
                        $response = curl_exec($curl);
                        curl_close($curl);
                        $completeOrder = json_decode($response, true);
                        //echo "<pre>"; print_r($response); die;
                        DB::table('dev_debug')->insert(['sr_no' => 5, 'result' => '5 SnapIt Log getOrganicCampaignCommio order complete: ' . $response, 'created_at' => date("Y-m-d H:i:s")]);
                        if (isset($completeOrder['status']) && strtolower($completeOrder['status']) == "completed") {
                            //Insert into lead call number
                            $insertDataArr = array();
                            $insertDataArr['phone'] = $did;
                            $insertDataArr['callit_number'] = substr($did, 1, 3) . " " . substr($did, 4, 3) . "-" . substr($did, 7, 4);
                            $insertDataArr['lead_source_id'] = 19;
                            $insertDataArr['is_current'] = "yes";
                            $insertDataArr['day'] = 0;
                            $insertDataArr['is_sms'] = "yes";
                            $insertDataArr['lead_category_id'] = 1;
                            $business_name = $businessesCampaign[$fd]['campaign_id'];
                            if (isset($businessNameArr[$businessesCampaign[$fd]])) {
                                $business_name = $businessNameArr[$businessesCampaign[$fd]];
                            }
                            $insertDataArr['comment'] = $business_name . ' business relay number';
                            $insertDataArr['status'] = "active";
                            $insertDataArr['phone_source'] = "OR";
                            $insertDataArr['provider'] = "commio";
                            $insertDataArr['created_at'] = date("Y-m-d H:i:s");
                            DB::table('dev_debug')->insert(['sr_no' => 6, 'result' => '6 SnapIt Log getOrganicCampaignCommio LeadCallNumber Data: ' . json_encode($insertDataArr), 'created_at' => date("Y-m-d H:i:s")]);
                            LeadCallNumber::create($insertDataArr);
                            //Here Update into business
                            $updateBusinessArr = array();
                            $updateBusinessArr['contact_number'] = substr(trim($did), 1);
                            DB::table('dev_debug')->insert(['sr_no' => 7, 'result' => '7 SnapIt Log getOrganicCampaignCommio LeadCallNumber Data: ' . json_encode($updateBusinessArr) . "==Organic campaign_id=" . $businessesCampaign[$fd]['campaign_id'], 'created_at' => date("Y-m-d H:i:s")]);
                            CampaignOrganic::where('campaign_id', $businessesCampaign[$fd]['campaign_id'])->update($updateBusinessArr);
                            echo $businessesCampaign[$fd]['campaign_id'] . "==" . $did . "== Business number purchased and updated<br>";
                        } else {
                            DB::table('dev_debug')->insert(['sr_no' => 8, 'result' => '8 SnapIt Log getOrganicCampaignCommio 3 Order Not found campaign_id: ' . $businessesCampaign[$fd]['campaign_id'], 'created_at' => date("Y-m-d H:i:s")]);
                            echo $businessesCampaign[$fd]['campaign_id'] . "== 3 Order Not found<br>";
                        }
                    } else {
                        DB::table('dev_debug')->insert(['sr_no' => 9, 'result' => '9 SnapIt Log getOrganicCampaignCommio 2 Purchase Not found campaign_id: ' . $businessesCampaign[$fd]['campaign_id'], 'created_at' => date("Y-m-d H:i:s")]);
                        echo $businessesCampaign[$fd]['campaign_id'] . "== 2 Purchase Not found<br>";
                    }
                } else {
                    DB::table('dev_debug')->insert(['sr_no' => 10, 'result' => '10 SnapIt Log getOrganicCampaignCommio 2 Search Not found campaign_id: ' . $businessesCampaign[$fd]['campaign_id'], 'created_at' => date("Y-m-d H:i:s")]);
                    echo $businessesCampaign[$fd]['campaign_id'] . "== 1 Search Not found<br>";
                }
            } }
        } catch (Exception $ex) {
            $message = $ex->getMessage();
            Log::info('11 SnapIt Log getOrganicCampaignCommio Catch error: ' . $message);
            DB::table('dev_debug')->insert(['sr_no' => 11, 'result' => '11 SnapIt Log getOrganicCampaignCommio Catch error: ' . $message, 'created_at' => date("Y-m-d H:i:s")]);
        }
        DB::table('dev_debug')->insert(['sr_no' => 12, 'result' => '12 SnapIt Log getOrganicCampaignCommio Successfully ran', 'created_at' => date("Y-m-d H:i:s")]);
        echo "Done";
    }

    public function releaseOrganicRelayNumber() {

        $endDecObj = new CommonFunctions;

        $getRecords = CampaignOrganic::join('campaign as c', 'campaign_organic.campaign_id', 'c.campaign_id')->select('c.campaign_id', 'c.campaign_name', 'campaign_organic.contact_number')->where('campaign_organic.contact_number', '>', 0)->where('c.is_active', '=', 'no')->groupBy('campaign_organic.campaign_id')->get(['campaign_organic.campaign_id', 'campaign_organic.contact_number'])->toArray();

        echo "<pre>";
        print_r($getRecords);
        exit();

        if (count($getRecords) > 0) {

            foreach ($getRecords as $key => $value) {
                $contact_number = $value['contact_number'];
                $campaign_id = $value['campaign_id'];

                $response = $endDecObj->ReleaseComioNumber($contact_number);

                if ($response == 'success') {
                    CampaignOrganic::where('campaign_id', $campaign_id)
                            ->update([
                                'contact_number' => null
                    ]);
                }
                DB::table('dev_debug')->insert(['sr_no' => 125, 'result' => 'disconnectCommioNumber Response: ' . json_encode($response), 'created_at' => date("Y-m-d H:i:s")]);
            }

            echo "Done";
        } else {
            echo 'No record found';
        }
        die;
    }

    public function updateInboundOutboundCallNumber() {
        /* Inbound/Outbound Number Script - Script of get the list and update number_type */
        $leadcallnumberareacode = LeadCallNumberAreacode::pluck('phone')->toArray();
        $leadcallnumber = LeadCallNumber::pluck('phone')->toArray();
        foreach ($leadcallnumber as $key => $value) {
            $phone = $value;
            if (in_array($phone, $leadcallnumberareacode)) {
                LeadCallNumber::where('phone', $phone)->update(['number_type' => 'outbound']);
                echo "outbound call number" . $phone . "<br>";
            } else {
                echo "inbound call number" . $phone . "<br>";
            }
        }
        echo "Done Successfully.";
        die;
    }

}
