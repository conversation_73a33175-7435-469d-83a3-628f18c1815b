<?php

namespace App\Http\Controllers\PlivoSms;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Helpers\CommonFunctions;
use App\Http\Controllers\Controller;
use App\Models\PlivoSms\ScheduleSMS;
use App\Http\Controllers\Leadroute\LeadRouteController;
use App\Http\Controllers\PlivoSms\ConfirmationSms;
use App\Models\CronJob\CronLog;
use App\Models\Lead\Lead;
use App\Models\Lead\LeadCallBuffer;
use App\Models\Lead\LeadCustomerSmsLog;
use App\Models\Lead\LeadOldCallBuffer;
use App\Models\PlivoSms\ReceivedSMS;
use Exception;
use App\Models\Lead\SystemVerifyPhone;
use App\Models\Master\MstTimeZone;
use App\Http\Controllers\Lead\LeadListController;
use App\Models\Lead\LeadFollowUp;
use App\Models\Lead\LeadActivity;
use App\Models\Outbound\GeneralNumberConfig;

class AfterHourSms extends Controller
{
    public function afterHourSMSCronCommio(Request $request)
    {
        $cronLog = new CronLog();
        $cronLog->cronlog(5, 'request='.json_encode($request->all(), true), 'afterhoursms');
        $cronLog->cronlog(5, 'start', 'afterhoursms');

        $str                = 'milan:3f7b24833410b9c7682d8c80bcd06442d63d02ad';
        $auth_token         = base64_encode($str);

        $smsstartDate       = date("Y-m-d", strtotime("-1 day")) . ' 00:00:00';
        $smsendDate         = date("Y-m-d H:i:s");
        // dump($smsstartDate, $smsendDate);

        $startDate          = date("Y-m-d", strtotime("-1 days")) . ' 00:00:00';
        $endDate            = date("Y-m-d", strtotime("-1 days")) . ' 23:59:59';

        $smsphoneumber      = $finalRecord = [];
        $getSMSLogsData     = LeadCustomerSmsLog::where('sms_subject', 'Spot Routing SMS')->whereBetween('created_at', [$smsstartDate, $smsendDate])->get()->toArray();
        for ($sms = 0; $sms < count($getSMSLogsData); $sms++) {
            $smsphoneumber[]= $getSMSLogsData[$sms]['lead_id'];
        }
        $cronLog->cronlog(5, 'afterhoursms smsphonenumber= ' . json_encode($smsphoneumber, true), 'afterhoursms');
        $smsnumbercount     = (count($smsphoneumber) > 0)  ? true : false;

        $timezoneids = [];
        $timezone           = $request['timezone'];
        if( $timezone == 'est'){
            $timezoneids    = MstTimeZone::where("timezone", "America/New_York")->pluck('timezone_id')->toArray();
        }else if($timezone == 'cst'){
            $timezoneids    = MstTimeZone::where("timezone", "America/Chicago")->pluck('timezone_id')->toArray();
        }else if($timezone == 'mst'){
            $timezoneids    = MstTimeZone::whereIn("timezone", ["America/Denver", "America/Phoenix"])->pluck('timezone_id')->toArray();
        }else{
            $timezoneids    = MstTimeZone::whereNotIn("timezone", ["America/New_York", "America/Chicago","America/Denver", "America/Phoenix"])->pluck('timezone_id')->toArray();
        }
        $cronLog->cronlog(4, 'timezone= ' . $timezone . ", timezoneids= " . json_encode($timezoneids, true), 'afterhoursms');

        $cWhere             = '';
        if (count($smsphoneumber) > 0) {
            $cWhere        .= "AND lead.lead_id NOT IN (".implode(',', $smsphoneumber).") ";
        }
        $timezoneids        = implode(',', $timezoneids);

        $scheduleSMS        = ScheduleSMS::where('sms_subject', 'Spot Routing SMS')->first();
        if (isset($scheduleSMS->campaign_id)) {
            $campaignIds    = explode(',', $scheduleSMS->campaign_id);
        }
        $leadsData          = DB::select("SELECT `origins`.`lead_source_id` AS `o_id`, `origins`.`lead_source_name` AS `origin_name`, `lead`.*, `lm`.`move_date`, `lm`.`from_state`, `lm`.`to_city`, `lm`.`to_state`, `lm`.`from_zipcode`, `lm`.`to_zipcode`
                                 FROM `lead`
                                 INNER JOIN `mst_lead_source` AS `origins` ON `origins`.`lead_source_id` = `lead`.`lead_source_id`
                                 INNER JOIN `lead_moving` AS `lm` ON `lm`.`lead_id` = `lead`.`lead_id`
                                 LEFT JOIN `lead_routing` AS `lr` ON `lr`.`lead_id` = `lead`.`lead_id`
                                 WHERE `lead`.`lead_type` = 'normal' AND `lead`.`timezone_id` IN ($timezoneids) AND (`lead`.`remaining_slot` > 0 || (`lr`.`campaign_id` IN (SELECT campaign_id FROM campaign WHERE business_id = 525) AND `lr`.`route_status` = 'sold')) AND `lead`.`is_dnc_sms` = 'no' AND `lm`.`to_state` <> `lm`.`from_state` AND `lead`.`created_at` >= '".$startDate."' AND `lead`.`created_at` <= '".$endDate."'
                                 $cWhere GROUP BY `lead`.`lead_id` ORDER BY `lead`.`lead_id` ASC LIMIT 20");
        //echo "<pre>start=". $startDate. ", end= ". $endDate." / ";
        //print_r($leadsData); die;
        $cronLog->cronlog(5, 'afterhoursms leads= ' . json_encode($leadsData, true), 'afterhoursms');

        $src                = '**********'; // temporary
        $message            = "success";
        if (count($leadsData) > 0) { foreach ($leadsData as $lead) {
            //$timezone     = $this->getFromstateToTimezone($lead->from_state);
            $timezoneName   = MstTimeZone::where("timezone_id", $lead->timezone_id)->first();
            date_default_timezone_set($timezoneName->timezone);
            $hoursCheck     = date('G');
            if ($hoursCheck < 9 || $hoursCheck >= 21) {
                return 'Night time';
            }

            $businessData   = DB::select("SELECT b.business_id, co.business_name, c.campaign_id, co.contact_number, IF(c.payment_type > 0, c.credit_available, b.credit_available) AS credit_available
                                        FROM `business` b 
                                        LEFT JOIN campaign c ON b.business_id = c.business_id
                                        LEFT JOIN campaign_organic co ON c.campaign_id = co.campaign_id
                                        LEFT JOIN campaign_moving cm ON c.campaign_id=cm.campaign_id
                                        LEFT JOIN campaign_location cl ON c.campaign_id = cl.campaign_id
                                        WHERE c.campaign_type_id='4' AND 
                                        b.business_id NOT IN (SELECT business_id FROM campaign WHERE campaign_id IN (SELECT campaign_id FROM lead_routing WHERE lead_id='".$lead->lead_id."' AND route_status='sold')) AND cm.move_type = 'long' AND cl.value NOT IN ('AK', 'HI') AND co.contact_number IS NOT NULL AND c.campaign_id IN ($scheduleSMS->campaign_id) GROUP BY b.business_id ORDER BY FIELD('campaign_id', $scheduleSMS->campaign_id)  /*ORDER BY RAND() LIMIT 1*/");
          
            $businessData   = array_map(function ($value) {
                return (array)$value;
            }, $businessData);
            //echo '<pre>'; print_r($businessData); die;
            if (empty($businessData)) {
                return 'No leads to send sms';
            }

            $body = $sWhere = "";

            try {
                $pos        = strpos($lead->origin_name, 'VM');
                $pos1       = strpos($lead->origin_name, 'QTM');
                $pos2       = strpos($lead->origin_name, 'IS');
                $to         = $lead->phone;

                if ($pos !== false) {
                    $key = 0;
                    foreach ($businessData as $value) {
                        if (count($businessData) == 2) {
                            if ($key > 0) {
                                $sWhere.= "\n📞 " . $value['business_name'] . ": +1 " . $this->formatPhoneNumber($value['contact_number']) . "\n📞 Quote The Move: ******-767-2122";
                            } else {
                                $sWhere.= "\n📞 " . $value['business_name'] . ": +1 " . $this->formatPhoneNumber($value['contact_number']);
                            }
                        } else if (count($businessData) == 1) {
                            $sWhere.= "\n📞 " . $value['business_name'] . ": +1 " . $this->formatPhoneNumber($value['contact_number']) . "\n📞 Quote The Move: ******-767-2122\n📞 Interstates Mover: ******-566-3184";
                        }
                        $key++;
                    }
                    $body   = "Still moving to " . ucwords(strtolower($lead->to_city)) . "?\nThese movers are ready with discounted quotes:" . $sWhere . "\nCall today for your best deal! 📦\nReply STOP to unsubscribe.";

                } else if ($pos1 !== false) {
                    $key = 0;
                    foreach ($businessData as $value) {
                        if (count($businessData) == 2) {
                            if ($key > 0) {
                                $sWhere.= "\n📞 " . $value['business_name'] . ": +1 " . $this->formatPhoneNumber($value['contact_number']) . "\n📞 Van Lines Move: ******-492-6496";
                            } else {
                                $sWhere.= "\n📞 " . $value['business_name'] . ": +1 " . $this->formatPhoneNumber($value['contact_number']);
                            }
                        } else if (count($businessData) == 1) {
                            $sWhere.= "\n📞 " . $value['business_name'] . ": +1 " . $this->formatPhoneNumber($value['contact_number']) . "\n📞 Van Lines Move: ******-492-6496\n📞 Interstates Mover: ******-566-3184";
                        }
                        $key++;
                    }
                    $body   = "Still moving to " . ucwords(strtolower($lead->to_city)) . "?\nThese movers are ready with discounted quotes:" . $sWhere . "\nCall today for your best deal! 📦\nReply STOP to unsubscribe.";

                } else if ($pos2 !== false) {
                    $key = 0;
                    foreach ($businessData as $value) {
                        if (count($businessData) == 2) {
                            if ($key > 0) {
                                $sWhere.= ', ' . $value['business_name'].', +1 '.$this->formatPhoneNumber($value['contact_number']).' or Van Lines Move, ******-492-6496';
                            } else {
                                $sWhere.= "\n📞 " . $value['business_name'] . ": +1 " . $this->formatPhoneNumber($value['contact_number']);
                            }
                        } else if (count($businessData) == 1) {
                            $sWhere.= "\n📞 " . $value['business_name'] . ": +1 " . $this->formatPhoneNumber($value['contact_number']) . "\n📞 Van Lines Move: ******-492-6496\n📞 Quote The Move: ******-767-2122";
                        }
                        $key++;
                    }
                    $body   = "Still moving to " . ucwords(strtolower($lead->to_city)) . "?\nThese movers are ready with discounted quotes:" . $sWhere . "\nCall today for your best deal! 📦\nReply STOP to unsubscribe.";
                }
                $from       = '+'.$src; // temporary
                $postData   = array(
                    'from_did' => $from,
                    'to_did' => $to,
                    'message' => $body
                );

                $postFields = json_encode($postData, true);
                if ($from != '') {
                    //Commio - API Curl request start
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, 'https://api.thinq.com/account/22374/product/origination/sms/send');
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($ch, CURLOPT_POST, 1);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);

                    $headers = array();
                    $headers[] = 'Content-Type: application/json';
                    $headers[] = 'Authorization: Basic ' . $auth_token;
                    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

                    $response = curl_exec($ch);
                    if (curl_errno($ch)) {
                        echo 'Error:' . curl_error($ch);
                    }
                    curl_close($ch);
                    //Commio - API Curl request End
                } else {
                    $response = [];
                }
                $finalRecord[]['lead_id'] = $lead->lead_id;

                //SMS Log Here By Start
                date_default_timezone_set('America/New_York');
                $smsLogData = array();
                $smsLogData['from_number'] = $from;
                $smsLogData['to_number'] = $to;
                $smsLogData['lead_type'] = $lead->lead_type;
                $smsLogData['sms_subject'] = "Spot Routing SMS";
                $smsLogData['request'] = $body;
                $smsLogData['response'] = json_encode($response);
                $smsLogData['lead_id'] = $lead->lead_id;
                $smsLogData['status'] = "success";
                $smsLogData['created_at'] = date('Y-m-d H:i:s');
                LeadCustomerSmsLog::create($smsLogData)->lead_customer_sms_log_id;
            } catch (Exception $e) {
                $message = $e->getMessage();
            }
        } }

        $responseData = [
            'LeadData' => $leadsData,
            'finaData' => $finalRecord,
            'message' => $message,
        ];

        return response()->json($responseData);
    }

    function formatPhoneNumber($number) {
        // Remove any non-numeric characters
        $number = preg_replace('/\D/', '', $number);

        // Check if the number has exactly 10 digits
        if (strlen($number) === 10) {
            // Format the number
            $formattedNumber = sprintf('%s-%s-%s',
                substr($number, 0, 3),  // Area code
                substr($number, 3, 3),  // Prefix
                substr($number, 6, 4)   // Line number
            );
            return $formattedNumber;
        } else {
            // Return an error or handle invalid number
            return 'Invalid number';
        }
    }

    public function getFromstateToTimezone($leadfromstate)
    {
        try {
            $tz_states = array(
                'America/Anchorage' => array('AK'),
                'America/Boise' => array('ID'),
                'America/Chicago' => array('AL', 'AR', 'IL', 'IA', 'KS', 'LA', 'MN', 'MS', 'MO', 'NE', 'OK', 'SD', 'TN', 'TX', 'WI'),
                'America/Denver' => array('CO', 'MT', 'NM', 'UT', 'WY'),
                'America/Detroit' => array('MI'),
                'America/Indiana/Indianapolis' => array('IN'),
                'America/Kentucky/Louisville' => array('KY'),
                'America/Los_Angeles' => array('CA', 'NV', 'OR', 'WA'),
                'America/New_York' => array('CT', 'DE', 'FL', 'GA', 'ME', 'MD', 'MA', 'NH', 'NJ', 'NY', 'NC', 'OH', 'PA', 'RI', 'SC', 'VT', 'VA', 'DC', 'WV'),
                'America/North_Dakota/Center' => array('ND'),
                'America/Phoenix' => array('AZ'),
                'Pacific/Honolulu' => array('HI'),
            );
            foreach ($tz_states as $key => $value) {
                if (in_array($leadfromstate, $value)) {
                    return $key;
                }
            }
        } catch (Exception $ex) {
            Log::info($ex);
        }
    }

    public function smsReceivedCommio(Request $request)
    {
        $parameters1 = $_REQUEST;
        CommonFunctions::createDevDebugLog(8, 'receivesms start smsreceivedcommio = ' . json_encode($parameters1) . "===" . $request['from'] . "===" . $request->from);
        CommonFunctions::createDevDebugLog(8, 'receivesms start smsreceivedcommio2= ' . json_encode($request->all()));
        $from = trim($request['from']);
        $txt = trim($request['message']);
        $to = trim($request['to']);
        $strLen = strlen($from);
        $fromWithoutCountryCode = substr($from,1,10);

        $message = "To : " . $to . "From : " . $from . " Text = " . $txt;
        CommonFunctions::createDevDebugLog(8, 'smsreceivedcommio4 -' . $from . "====" . $to . "===" . $txt . "===" . $fromWithoutCountryCode . "===" . json_encode($parameters1));
        $message = "To : " . $to . "From : " . $from . " Text = " . $txt;
        CommonFunctions::createDevDebugLog(8, 'smsreceivedcommio5 -' . $message);
        $dataIds = [];
        $empty = '';
        $data = SystemVerifyPhone::where('to_number', "LIKE", "%" .$fromWithoutCountryCode ."%" )
        ->where(function ($data) use ($empty) {
            $data->where('is_verified', $empty)
            ->orWhereNull('is_verified')
            ->orWhere('is_verified', "no");
        })->orderBy("system_verify_phone_id", "DESC")->first();
        // $devdebug->devDebuglog(70, "PlivoNoVerifyLeadSms receivedldata=". json_encode($data));
        CommonFunctions::createDevDebugLog(8, 'PlivoNoVerifyLeadSms data='. json_encode($data));
        if (strpos(strtoupper($txt), 'STOP') !== false) {
            $checkLead = Lead::where('phone', $fromWithoutCountryCode)->get()->toArray();
            if (count($checkLead) > 0) {
                $dncStartDate  = date("Y-m-d H:i:s", strtotime("-90 days"));
                $dncEndDate    = date("Y-m-d H:i:s");
                //LeadCallBuffer::where('customer_number', $fromWithoutCountryCode)->delete();
                //LeadOldCallBuffer::where('customer_number', $fromWithoutCountryCode)->delete();
                //LeadFollowUp::where('cust_phone_number', $fromWithoutCountryCode)->delete();
                Lead::where('phone', $fromWithoutCountryCode)->whereBetween('created_at', [$dncStartDate, $dncEndDate])->update(['is_dnc_sms' => 'yes']);
                //echo "<pre>smsphoneumber = ";
                //print_r($nonVerifyLeads);
                //die;
                //$leadscalldnc = Lead::where('phone', $fromWithoutCountryCode)->whereBetween('created_at', [$dncStartDate, $dncEndDate])->where('is_dnc_call','yes')->get()->toArray();
                $leadscalldnc = Lead::where('phone', $fromWithoutCountryCode)->whereBetween('created_at', [$dncStartDate, $dncEndDate])->get()->toArray();
                foreach ($leadscalldnc as $key => $value) {
                    //$leadListControllerobj = new LeadListController();
                    //$leadListControllerobj->dncemailsend($value['lead_id'], "no", "yes", "no");

                    $leadactivityObj = new LeadActivity();
                    //$leadactivityObj->createActivity($value['lead_id'], 21);
                    $leadactivityObj->createActivity($value['lead_id'], 22);
                    //$leadactivityObj->createActivity($value['lead_id'], 23);
                }
            }
            if(isset($data)){
                $system_verify_phone_id = $data->system_verify_phone_id;
                SystemVerifyPhone::where('system_verify_phone_id', $system_verify_phone_id)->update(['reply_text' => $txt, 'is_verified' => "stop"]);
            }
        } else if ($txt == "1") {
            CommonFunctions::createDevDebugLog(8, 'smsreceivedcommio6 Press 1 LeadIDs');
            $data = Lead::where('phone', $fromWithoutCountryCode)->orderBy('lead_id', 'DESC')->first();
            if ($data) {
                $leadId = $data->lead_id;
                $dataIds[] = $data->lead_id;
                CommonFunctions::createDevDebugLog(8, "LeadID : " .   $leadId);

                $endDecObj = new CommonFunctions;
                $leadIdEncrypt = $endDecObj->customeEncryption($leadId);
                $leadRouteCron = new LeadRouteController();
                $leadRouteCron->reRouteLead($leadIdEncrypt, 3, 0, 1);
            } else {
                CommonFunctions::createDevDebugLog(8, "Lead not found : " .   $fromWithoutCountryCode);
                return 'Lead not found';
            }
        }else if ( $txt == 'Y' || $txt == 'y') {
            CommonFunctions::createDevDebugLog(8, "PlivoNoVerifyLeadSms received Y");
            if(isset($data)){
                CommonFunctions::createDevDebugLog(8, "PlivoNoVerifyLeadSms received issetdata=".json_encode($data));
                $system_verify_phone_id = $data->system_verify_phone_id;
                $leadId = $data->lead_id;
                CommonFunctions::createDevDebugLog(8, "PlivoNoVerifyLeadSms system_verify_phone_id=". $system_verify_phone_id." lead=".$leadId);
                SystemVerifyPhone::where('system_verify_phone_id', $system_verify_phone_id)->update(['reply_text' => $txt, 'is_verified' => "yes"]);
                CommonFunctions::createDevDebugLog(8, "PlivoNoVerifyLeadSms 1");
                Lead::where('lead_id', $leadId)->update(['is_handled' => "yes", 'is_verified' => "yes"]);
                CommonFunctions::createDevDebugLog(8, "PlivoNoVerifyLeadSms 2");
                $endDecObj = new CommonFunctions;
                $leadIdEncrypt = $endDecObj->customeEncryption($leadId);
                $leadRouteCron = new LeadRouteController();
                $leadRouteCron->reRouteLead($leadIdEncrypt, 3, 0, 1);
                CommonFunctions::createDevDebugLog(8, "PlivoNoVerifyLeadSms 3");
                // CommonFunctions::createDevDebugLog(8, "PlivoNoVerifyLeadSms system_verify_phone_id=". $system_verify_phone_id." lead=".$leadId);
            }
        } else {
            if(isset($data)){
                $system_verify_phone_id = $data->system_verify_phone_id;
                SystemVerifyPhone::where('system_verify_phone_id', $system_verify_phone_id)->update(['reply_text' => $txt]);
            }
        }
        $smsData = array();
        $smsData['provider'] = 'commio';
        $smsData['from_number'] = $from;
        $smsData['to_number'] = $to;
        $smsData['sms_body'] = $txt;
        $smsData['created_at'] = date("Y-m-d H:i:s");
        ReceivedSMS::create($smsData);

        $status = 1;
        $data = ['status' => $status, 'message' => $message];

        CommonFunctions::createDevDebugLog(8, "smsreceivedcommio6  : " .   json_encode($data));
        $responseData = [
            'LeadData' => $dataIds
        ];
        return response()->json($responseData);
    }

    public function smsReceivedPlivo(Request $request)
    {
        $parameters1 = $_REQUEST;
        CommonFunctions::createDevDebugLog(9, 'receivesms start smsreceivedplivo = ' . json_encode($parameters1) . "===" . $request['from'] . "===" . $request->from);
        CommonFunctions::createDevDebugLog(9, 'receivesms start smsreceivedplivo= ' . json_encode($request->all()));
        $from = trim($request['From']);
        $txt = trim($request['Text']);
        $to = trim($request['To']);
        $strLen = strlen($from);
        $fromWithoutCountryCode = substr($from, 1, $strLen);
        CommonFunctions::createDevDebugLog(9, 'smsreceivedplivo3 -' . $from . "===" . $to . "===" . $txt . "===" . $fromWithoutCountryCode);
        $message = "To : " . $to . "From : " . $from . " Text = " . $txt;
        CommonFunctions::createDevDebugLog(9, 'smsreceivedplivo4 -' . $message);
        $dataIds = [];
        if (strpos(strtoupper($txt), 'STOP') !== false) {

            $checkLead = Lead::where('phone', $fromWithoutCountryCode)->get()->toArray();
            if (count($checkLead) > 0) {
                $dncStartDate  = date("Y-m-d H:i:s", strtotime("-90 day"));
                $dncEndDate    = date("Y-m-d H:i:s");
                LeadCallBuffer::where('customer_number', $fromWithoutCountryCode)->delete();
                LeadOldCallBuffer::where('customer_number', $fromWithoutCountryCode)->delete();
                Lead::where('phone', $fromWithoutCountryCode)->whereBetween('created_at', [$dncStartDate, $dncEndDate])->update(['is_dnc_call' => 'yes', 'is_dnc_sms' => 'yes', 'is_dnc_email' => 'yes']);
            }
        } else if ($txt == "1") {
            CommonFunctions::createDevDebugLog(9, 'smsreceivedplivo5 Press 1 LeadIDs');
            $data = Lead::where('phone', $fromWithoutCountryCode)->orderBy('lead_id', 'DESC')->first();
            if ($data) {
                $leadId = $data->lead_id;
                $dataIds[] = $data->lead_id;
                CommonFunctions::createDevDebugLog(9, "LeadID : " .   $leadId);

                $endDecObj = new CommonFunctions;
                $leadIdEncrypt = $endDecObj->customeEncryption($leadId);
                $leadRouteCron = new LeadRouteController();
                $leadRouteCron->reRouteLead($leadIdEncrypt, 2);
            } else {
                CommonFunctions::createDevDebugLog(9, "Lead not found : " .   $fromWithoutCountryCode);
                return 'Lead not found';
            }
        }
        $smsData = array();
        $smsData['provider'] = 'commio';
        $smsData['from_number'] = $from;
        $smsData['to_number'] = $to;
        $smsData['sms_body'] = $txt;
        $smsData['created_at'] = date("Y-m-d H:i:s");
        ReceivedSMS::create($smsData);

        $status = 1;
        $data = ['status' => $status, 'message' => $message];

        CommonFunctions::createDevDebugLog(9, "smsreceivedplivo6  : " .   json_encode($data));
        $responseData = [
            'LeadData' => $dataIds
        ];
        return response()->json($responseData);
    }
}
