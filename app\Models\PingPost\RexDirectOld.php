<?php

namespace App\Models\PingPost;

use App\Models\PingPost\PingPostPayout;
use App\Models\Lead\LeadRoutingLog;
use App\Models\Lead\LeadRouting;
use App\Models\Lead\LeadMoving;
use App\Models\Business\Business;
use App\Models\Lead\LeadLandingPage;
use App\Models\DevDebug;
use Exception;
use DB;

class RexDirectOld
{
    private $lpCampaignId   = '5e67ab15a9a26';
    private $lpCampaignKey  = 'mCKJtRdcghNkzHwF64Mv';
    private $affKey         = '110490';
    private $lpResponse     = 'JSON';
    private $lpTest         = 1;
    private $lpPingId       = '';
    private $leadId         = 0;
    private $campaignId     = 0;

    public function ping($leadData, $campaignId) {
        $this->campaignId = $campaignId;
        $promise = NULL;
        DevDebug::create([
            'sr_no' => 110,
            'result' => 'LeadData Array ' . json_encode($leadData),
            'created_at' => date("Y-m-d H:i:s"),
        ]);
        $leadId = $leadData[0]['lead_id'];
        try {
            //var_dump($this->checkCampaignStatus());
            if($this->isAdmissiblePing($leadId, $campaignId) && $this->checkCampaignStatus($campaignId)) {
                $getMoveInfo = LeadMoving::where('lead_id', $leadId)->get()->toArray();
                $landingPageDetails = LeadLandingPage::where('lead_id', $leadId)->get()->toArray();

                $body = array(
                    'lp_campaign_id' => $this->lpCampaignId,
                    'lp_campaign_key' => $this->lpCampaignKey,
                    'state' =>  $getMoveInfo[0]['from_state'],
                    'zip_code' => $getMoveInfo[0]['from_zipcode'],
                    'to_zip_code' => $getMoveInfo[0]['to_zipcode'],
                    'move_date' => $this->moveDateValue($getMoveInfo[0]['move_date']),
                    'move_size' => $this->moveSizeValue($getMoveInfo[0]['move_size_id']),
                    'affkey' => $this->affKey,
                    'lp_response' => $this->lpResponse,
                    'trusted_form_cert_id' => $landingPageDetails[0]['trusted_form_cert_id'] ?? '',
                );
                //dd($body);
                $headers = [
                    'Content-Type' => 'application/x-www-form-urlencoded'
                ];
                $url = 'https://rexdirect.leadspediatrack.com/ping.do';

                $client = new \GuzzleHttp\Client();
                $promise = $client->postAsync($url, [
                    'header' => $headers,
                    'form_params' => $body
                ]);
                $this->leadId = $leadId;
                $payout = 0;
                $promise->then(function ($response) {
                    $responseData = json_decode($response->getBody(), true);
                    $this->lpPingId = $responseData['ping_id'];
                    $payout = $responseData['price'];

                    PingPostPayout::create([
                        'lead_id' => $this->leadId,
                        'campaign_id' => $this->campaignId,
                        'payout' => $payout,
                        'ping_id' => $this->lpPingId,
                        //'ping_request' => json_encode($body),
                        'ping_response' => json_encode($responseData),
                        'is_request' => 'ping',
                        'created_at' => date("Y-m-d H:i:s")
                    ]);
                });
                return $promise;
            }
        }
        catch(Exception $e) {
            DevDebug::create([
                'sr_no' => 109,
                'result' => 'RexDirect PingPost ' . $e->getMessage(),
                'created_at' => date("Y-m-d H:i:s"),
            ]);
            dd($e->getMessage());
        }

        return $promise;
        //$promise->wait();
    }

    public function post($leadData, $campaignId) {
        $this->campaignId = $campaignId;
        $leadId = $leadData[0]['lead_id'];
        $leadName = $leadData[0]['name'];
        $email = $leadData[0]['email'];
        $phone = $leadData[0]['phone'];
        if($this->isAdmissiblePost($leadId, $campaignId) && $this->checkCampaignStatus($campaignId)) {
            // Fetch PingId from ws_payout
            $wsPayoutData = PingPostPayout::where('lead_id', $leadId)->where('campaign_id', $this->campaignId)->get(['ping_id'])->first();
            $getMoveInfo = LeadMoving::where('lead_id', $leadId)->get()->toArray();
            $landingPageDetails = LeadLandingPage::where('lead_id', $leadId)->get()->toArray();
            $first_name = $last_name = '';
            $nameData = explode(" ", $leadName);
            if (isset($nameData[0]) && !empty($nameData[0])) {
                $first_name = $nameData[0];
            }
            if (isset($nameData[1]) && !empty($nameData[1])) {
                $last_name = $nameData[1];
            }

            $firstName = $lastName = 'Test';
            if(strpos(url()->current(), 'linkup.software') !== false) {
                $this->lpTest = 0;
                $firstName = $first_name;
                $lastName = $last_name;
            } else if(strpos(url()->current(), '54.67.5.60') !== false || strpos(url()->current(), 'tweetstage.tweetsoftware.co') !== false) {
                $this->lpTest = 0;
                $firstName = $first_name;
                $lastName = $last_name;
            }

            $body = array(
                'lp_campaign_id'=> $this->lpCampaignId,
                'lp_campaign_key'=> $this->lpCampaignKey,
                'lp_ping_id' => $wsPayoutData->ping_id,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'phone_home' => $phone,
                'city' => $getMoveInfo[0]['from_city'],
                'state' => $getMoveInfo[0]['from_state'],
                'zip_code' => sprintf("%05d", $getMoveInfo[0]['from_zipcode']),
                'email_address' => $email,
                'ip_address' => "",
                'to_city' => $getMoveInfo[0]['to_city'],
                'to_state' => $getMoveInfo[0]['to_state'],
                'to_zip_code' => sprintf("%05d", $getMoveInfo[0]['to_zipcode']),
                'move_date' => $this->moveDateValue($getMoveInfo[0]['move_date']),
                'move_size' => $this->moveSizeValue($getMoveInfo[0]['move_size_id']),
                'affkey' => $this->affKey,
                'lp_response'=> $this->lpResponse,
                'lp_test' => $this->lpTest,
                'trusted_form_cert_id' => $landingPageDetails[0]['trusted_form_cert_id'] ?? '',
            );
            $headers = [
                'Content-Type' => 'application/x-www-form-urlencoded'
            ];
            $url = 'https://rexdirect.leadspediatrack.com/post.do';

            $client = new \GuzzleHttp\Client([
                'headers' => $headers
            ]);
            $r = $client->request('POST', $url, [
                'form_params' => $body
            ]);
            $response = $r->getBody()->getContents();
            // Store Request/ Response in lead_routing_log
            LeadRoutingLog::create([
                'lead_id' => $leadId,
                'campaign_id' => $this->campaignId,
                'request' => http_build_query($body),
                'response' => $response
            ]);

            $responseData = json_decode($response, true);
            if($responseData['result'] == 'success') {
                return true;
            }
        }
        return false;
    }

    public function moveSizeValue($moveSize = 0) {
        if($moveSize == 1) {
            return 1500;
        } else if($moveSize == 2) {
            return 2000;
        } else if($moveSize == 3) {
            return 5000;
        } else if($moveSize == 4) {
            return 10000;
        } else if($moveSize == 5) {
            return 15000;
        } else {
            return 20000;
        }
    }

    public function moveDateValue($moveDate) {
        $date               = date_create($moveDate);
        return date_format($date, "m/d/Y");
    }

    public function isAdmissiblePing($leadId, $campaignId) {
        $this->campaignId   = $campaignId;
        $wsPayout           = PingPostPayout::where('lead_id', $leadId)->where('campaign_id', $this->campaignId)->where('is_request', 'ping')->get()->toArray();
        //echo "<pre>"; print_r($wsPayout); die;
        if($wsPayout) {
            return false;
        } else {
            return true;
        }
    }

    public function isAdmissiblePost($leadId, $campaignId) {
        $this->campaignId   = $campaignId;
        $wsRouting          = LeadRouting::where('lead_id', $leadId)->where('campaign_id', $this->campaignId)->first();
        if ($wsRouting) {
            return false;
        } else {
            return true;
        }
    }

    public function checkCampaignStatus($campaignId) {
        $this->campaignId   = $campaignId;
        $status             = 'yes';
        $businessCampaign   = Business::with([
            'businessCampaign' => function ($query) use ($status) {
                return $query->where('is_active', '=', $status)->where('campaign_id', $this->campaignId);
            }])->where('status', 'active')->where('buyer_type_id', 3)->get();
        //dd(count($businessesCampaign));
        if (count($businessCampaign) > 0) {
            return true;
        } else {
            return false;
        }
    }
}