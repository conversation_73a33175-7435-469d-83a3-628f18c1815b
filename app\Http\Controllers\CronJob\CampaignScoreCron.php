<?php

namespace App\Http\Controllers\CronJob;

use App\Http\Controllers\Controller;
use App\Models\Campaign\Campaign;
use App\Models\Campaign\CampaignOngoingCall;
use App\Models\DevDebug;
use Exception;
use App\Models\Logic\ScoreLead;

class CampaignScoreCron extends Controller
{

    public function campaignScoreCalculateCron()
    {
        try {
            $scoreLeadPriceObj = new ScoreLead();
            //Comment By BK on 28/05/2025 as per discussion with client
            // Point 1
            //$dataFromRegion = $scoreLeadPriceObj->countScoreFromRegion();
            // Point 2
            //$dataMoveSize = $scoreLeadPriceObj->countScoreMoveSize();
            // Point 3
            //$dataMoveDate = $scoreLeadPriceObj->countScoreMoveDate();
            // Point 4
            //$dataRecurrentPayment = $scoreLeadPriceObj->countScoreRecurrentPayment();
            // Point 5 In Live
            // $dataRemainingTime = $scoreLeadPriceObj->countScoreRemainingTimeCampaign();           
            // Point 6
            //$dataLeadLimit = $scoreLeadPriceObj->countScoreLeadLimit();
            // Point 7
            //$dataLeadSpot = $scoreLeadPriceObj->countScoreYesterdayLeadSpot();
            // Point 8
            //$dataLeadPrice = $scoreLeadPriceObj->countScoreLeadPrice();
            // Point 9 In Live
            // $dataLastLead = $scoreLeadPriceObj->countScoreLastLeadCampaign();       
            // Point 10 In Live
            // $dataCampaignFund = $scoreLeadPriceObj->countScoreCampaignFundCampaign();

            //Added By BK on 28/05/2025 as per discussion with client
            // Point 1
            $scoreSubscription = $scoreLeadPriceObj->countScoreSubscription();
            // Point 2
            $scoreRecurrentPayment = $scoreLeadPriceObj->countScoreRecurrentPayment();

            $finalScore = $scoreLeadPriceObj->array_merge_recursive_numeric_score($scoreSubscription, $scoreRecurrentPayment);
            //echo "<pre>";print_r($finalScore); die;
            foreach ($finalScore as $key => $value) {
                Campaign::where('campaign_id', $key)->update(['default_score' => $value]);
            }
        } catch (Exception $e) {
            dd($e->getMessage());
        }
    }

    public function campaignScoreResetCron() {
        $businessArr = array(947, 462, 525);
        Campaign::whereNotIn('business_id', $businessArr)->update(['additional_score' => 0]);
    }

    //Cron Added By SS On 22-05-2023 For Delete Record From CampaignOngoingCall Table After 120 Minute Start
    public function deleteCampaignOngoingCallData()
    {
        try {
            $devdebuglg = new DevDebug();
            $devdebuglg->devDebuglog(12, "12 Run cron deleteCallTrackData : " . date('Y-m-d H:i:s'));  //log
            $getTrackData = CampaignOngoingCall::WhereNotNull('created_at')->get()->toArray();
            $currentDateTime = date("Y-m-d H:i:s");
            $deleteIdArr = array();
            for ($f = 0; $f < count($getTrackData); $f++) {
                $created_at = $getTrackData[$f]['created_at'];
                echo "=============================================================================<br>campaign_ongoing_call Table Id Start==" . $getTrackData[$f]['campaign_ongoing_call_id'];
                echo "<br>";
                echo "Current Date Time=" . $currentDateTime . "====created_at=" . $created_at;
                echo "<br>";
                $difference_between_call = abs(strtotime($currentDateTime) - strtotime($created_at)) / 60; //Get Minute Different
                echo "Minute Different==" . $difference_between_call . "<br>";
                $devdebuglg->devDebuglog(12, "deleteCallTrackData Id: " . $getTrackData[$f]['campaign_ongoing_call_id'] . '===Minute==' . $difference_between_call . ", Current Date Time=" . $currentDateTime . ", Table created_at=" . $created_at);  //log
                if ($difference_between_call >= 120) {
                    $deleteIdArr[] = $getTrackData[$f]['campaign_ongoing_call_id'];
                }
                echo "campaign_ongoing_call Table Id End==" . $getTrackData[$f]['campaign_ongoing_call_id'];
                echo "<br>";
            }
            echo "=============================================================================";
            echo "<pre>Delete id Array==<br>";
            $devdebuglg->devDebuglog(12, "deleteCallTrackData Delete Id Array: " . json_encode($deleteIdArr) . '===Date-' . $currentDateTime);
            print_r($deleteIdArr);
            echo "<br>";
            //echo "<pre>";print_r($getTrackData);die;
            if (count($deleteIdArr) > 0) {
                CampaignOngoingCall::WhereIn('campaign_ongoing_call_id', $deleteIdArr)->delete();
            }
            //CampaignOngoingCall::WhereNull('created_at')->delete();
        } catch (Exception $ex) {
            dd($ex);
        }
    }
    //Cron Added By SS On 22-05-2023 For Delete Record From CampaignOngoingCall Table After 120 Minute End
}