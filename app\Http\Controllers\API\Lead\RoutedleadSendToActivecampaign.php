<?php

namespace App\Http\Controllers\API\Lead;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\Business\Business;
use App\Models\Campaign\Campaign;
use App\Models\Lead\Lead;
use App\Models\Lead\LeadActiveCamapaignLog;
use App\Models\Master\MstCampaignType;
use App\Models\Master\MstCarType;
use App\Models\Master\MstLeadSource;
use App\Models\Lead\LeadActivity;
use App\Models\Master\MstSubscriptionPlan;
use App\Models\Subscription\Subscription;
use App\Models\Outbound\LeadCall;
use Carbon\Carbon;

class RoutedleadSendToActivecampaign extends Controller
{
    public function leadSendtoactiveCampaign($leadId, $source_lead_id)
    {
        //DB::table('test')->insert(['result' => 'Start leadSendtoactiveCampaign Lead_id : ' . $leadId .  ', Source_lead_id : ' . $source_lead_id]);
        $element_lead_id = base64_encode($source_lead_id);
        $enctypted_leadId = base64_encode($leadId);

        $url = 'https://primemarketing15119.api-us1.com';
        $leadsemaildata = Lead::with(['moveInfo', 'junkMoveInfo', 'heavyLiftMoveInfo', 'carTransMoveInfo', 'routingInfoLeadList'])->where('lead_id', $leadId)->get()->first();
        //DB::table('test')->insert(['result' => 'leadsData : ' . json_encode($leadsemaildata)]);
        $origin = MstLeadSource::where('lead_source_id', $leadsemaildata->lead_source_id)->get()->first();
        //Added By HJ On 27-07-2023 For Email Not Send to Seeded Lead As Per Discussed With MV Start
        if (strtolower($origin->lead_source_name) == "test(s)") {
            return 'Email Not send because lead is Seeded.';
        }
        //Added By HJ On 27-07-2023 For Email Not Send to Seeded Lead As Per Discussed With MV End
        $listid = 0;
        $originName = $phoneMask = "";
        $pos = strpos($origin->lead_source_name, 'VM');
        $pos1 = strpos($origin->lead_source_name, 'QTM');
        $pos2 = strpos($origin->lead_source_name, 'IS');
        if ($pos !== false) {
            //$listid = 5;
            $listid = 34;
            /*if ($leadsemaildata->lead_category_id == "1") {
                $url = 'https://primemarketing17812.api-us1.com';
            }*/
            if ($leadsemaildata->lead_category_id == "2") {
                $listid = 20;
            } else if ($leadsemaildata->lead_category_id == "3") {
                $listid = 22;
            } else if ($leadsemaildata->lead_category_id == "4") {
                $listid = 24;
            }
            $originName = "VM";
            $phoneMask = '******' . substr($leadsemaildata->phone, 6, 4);
        } else if ($pos1 !== false) {
            $listid = 6;
            if ($leadsemaildata->lead_category_id == "2") {
                $listid = 26;
            } else if ($leadsemaildata->lead_category_id == "3") {
                $listid = 28;
            } else if ($leadsemaildata->lead_category_id == "4") {
                $listid = 30;
            }
            $originName = "QTM";
        } else if ($pos2 !== false) {
            $listid = 8;
            if ($leadsemaildata->lead_category_id == "2") {
                $listid = 20;
            } else if ($leadsemaildata->lead_category_id == "3") {
                $listid = 22;
            } else if ($leadsemaildata->lead_category_id == "4") {
                $listid = 24;
            }
            $originName = "IS";
            $phoneMask = '******' . substr($leadsemaildata->phone, 6, 4);
        }

        $splitname = explode(' ', $leadsemaildata->name);

        if (count($splitname) >= 1) {
            $first_name = $splitname[0];
            $lastnamearray = array();
            foreach ($splitname as $keyl => $valuel) {
                if ($keyl != 0) {
                    $lastnamearray[] = $valuel;
                }
            }
            $last_name  = implode(' ', $lastnamearray);
        } else {
            $first_name = $splitname[0];
            $last_name = '';
        }

        if ($leadsemaildata->lead_category_id == "1") {
            $move_sizeLabel = 0;
            if ($leadsemaildata->moveInfo->move_size_id == 1) {
                $move_sizeLabel = 'Studio';
            } else if ($leadsemaildata->moveInfo->move_size_id == 2) {
                $move_sizeLabel = '1 Bedroom';
            } else if ($leadsemaildata->moveInfo->move_size_id == 3) {
                $move_sizeLabel = '2 Bedrooms';
            } else if ($leadsemaildata->moveInfo->move_size_id == 4) {
                $move_sizeLabel = '3 Bedrooms';
            } else if ($leadsemaildata->moveInfo->move_size_id == 5) {
                $move_sizeLabel = '4 Bedrooms';
            } else if ($leadsemaildata->moveInfo->move_size_id == 6) {
                $move_sizeLabel = '5+ Bedrooms';
            }
        } else if ($leadsemaildata->lead_category_id == "2") {
            $junRemovalType = "Nothing";
            if ($leadsemaildata->junkMoveInfo->junk_type_id == "1") {
                $junRemovalType = 'Haul Waste';
            } else if ($leadsemaildata->junkMoveInfo->junk_type_id == "2") {
                $junRemovalType = 'Appliance Removal';
            } else if ($leadsemaildata->junkMoveInfo->junk_type_id == "3") {
                $junRemovalType = 'Waste Dump';
            }
        } else if ($leadsemaildata->lead_category_id == "3") {
            $heavyLiftingType = "Nothing";
            if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == "1") {
                $heavyLiftingType = 'Heavy Equipment';
            } else if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == "2") {
                $heavyLiftingType = 'Freight Shipping';
            } else if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == "3") {
                $heavyLiftingType = 'Heavy Duty Trucks';
            } else if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == "4") {
                $heavyLiftingType = 'Boat Transport';
            } else if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == "5") {
                $heavyLiftingType = 'RV Shipping';
            } else if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == "6") {
                $heavyLiftingType = 'Trailer';
            }
        } else if ($leadsemaildata->lead_category_id == "4") {
            $carTypeId  = $leadsemaildata->carTransMoveInfo->car_type_id;
            $carTypeDetail = MstCarType::where("car_type_id", $carTypeId)->first();
            $carType = $carTypeDetail->car_type;

            $transportType = ucfirst($leadsemaildata->carTransMoveInfo->transport_type);
        }

        $params = array(

            // the API Key can be found on the "Your Settings" page under the "API" tab.
            // replace this with your API Key
            //'api_key'                         => ($pos !== false && $leadsemaildata->lead_category_id == "1") ? 'f2feba8bfe13b040a9ce0d29f2e8abc30c9de0d693617d61f0b12b374b442e37f8296a3e' : '48bc7240813d9983486c007bc65bd4b2d7b8c4e46d0bf6ce8831abd8ed594b8e50fc1705',
            'api_key'                           => '48bc7240813d9983486c007bc65bd4b2d7b8c4e46d0bf6ce8831abd8ed594b8e50fc1705',

            // this is the action that adds a contact
            'api_action'                        => 'contact_sync',

            // define the type of output you wish to get back
            // possible values:
            // - 'xml'  :      you have to write your own XML parser
            // - 'json' :      data is returned in JSON format and can be decoded with
            //                 json_decode() function (included in PHP since 5.2.0)
            // - 'serialize' : data is returned in a serialized format and can be decoded with
            //                 a native unserialize() function
            'api_output'                        => 'serialize',
        );

        // here we define the data we are posting in order to perform an update
        if ($leadsemaildata->lead_category_id == "1") {
            $moveType = "Home";
            if ($leadsemaildata->moveInfo->type == 2) {
                $moveType = "Apartment";
            } else if ($leadsemaildata->moveInfo->type == 3) {
                $moveType = "Storage";
            } else if ($leadsemaildata->moveInfo->type == 4) {
                $moveType = "Mobile Home";
            }
            $post = array(
                'email'                         => $leadsemaildata->email,
                'first_name'                    => ucwords(strtolower($first_name)),
                'last_name'                     => ucwords(strtolower($last_name)),
                'phone'                         => (int) $leadsemaildata->phone,
                //'customer_acct_name'          => $leadsemaildata->name,
                'tags'                          => 'api',
                //'ip4'                         => '127.0.0.1',

                // any custom fields
                // 'field[345,0]'               => 'field value', // where 345 is the field ID
                'field[FIRSTNAME,0]'            => ucwords(strtolower($first_name)), // using the personalization tag instead (make sure to encode the key)
                'field[LASTNAME,0]'             => ucwords(strtolower($last_name)),
                'field[EMAIL,0]'                => $leadsemaildata->email,
                'field[%UNIQUE_KEY%,0]'         => $element_lead_id,
                'field[%SOURCE%,0]'             => $origin->lead_source_name,
                'field[%TWEET_ENCRY_ID%,0]'     => $enctypted_leadId,
                'field[%PHONE_VERIFIED%,0]'     => ($leadsemaildata->is_verified == 'yes') ? 1 : 0,
                'field[%PHONE_ENCRY_ID%,0]'     => base64_encode($leadsemaildata->phone),
                'field[%MASK_PHONE%,0]'         => $phoneMask,

                'field[%FROM_ZIP%,0]'           =>  $leadsemaildata->moveInfo->from_zipcode,
                'field[%TO_ZIP%,0]'             => $leadsemaildata->moveInfo->to_zipcode,
                'field[%MOVE_DATE%,0]'          => $leadsemaildata->moveInfo->move_date,
                'field[%MOVE_TYPE%,0]'          => $moveType,
                'field[%MOVE_SIZE%,0]'          => $move_sizeLabel,
                'field[%CONFIRMATION_NUMBER%,0]' => $leadsemaildata->lead_id,
                'field[%FROM_CITY%,0]'          => ucwords(strtolower($leadsemaildata->moveInfo->from_city)),
                'field[%TO_CITY%,0]'            => ucwords(strtolower($leadsemaildata->moveInfo->to_city)),
                'field[%FROM_STATE%,0]'         => $leadsemaildata->moveInfo->from_state,
                'field[%TO_STATE%,0]'           => $leadsemaildata->moveInfo->to_state,

                // assign to lists:
                'p[' . $listid . ']'                => $listid, // example list ID (REPLACE '123' WITH ACTUAL LIST ID, IE: p[5] = 5)
                'status[' . $listid . ']'           => 1, // 1: active, 2: unsubscribed (REPLACE '123' WITH ACTUAL LIST ID, IE: status[5] = 1)
                //'form'                        => 1001, // Subscription Form ID, to inherit those redirection settings
                //'noresponders[123]'           => 1, // uncomment to set "do not send any future responders"
                //'sdate[123]'                  => '2009-12-07 06:00:00', // Subscribe date for particular list - leave out to use current date/time
                // use the folowing only if status=1
                'instantresponders[' . $listid . ']' => 1, // set to 0 to if you don't want to sent instant autoresponders
                //'lastmessage[123]'            => 1, // uncomment to set "send the last broadcast campaign"

                //'p[]'                         => 345, // some additional lists?
                //'status[345]'                 => 1, // some additional lists?
            );
        } else if ($leadsemaildata->lead_category_id == "2") {
            $post = array(
                'email'                         => $leadsemaildata->email,
                'first_name'                    => ucwords(strtolower($first_name)),
                'last_name'                     => ucwords(strtolower($last_name)),
                'phone'                         => (int) $leadsemaildata->phone,
                //'customer_acct_name'          => $leadsemaildata->name,
                'tags'                          => 'api',
                //'ip4'                         => '127.0.0.1',

                // any custom fields
                // 'field[345,0]'               => 'field value', // where 345 is the field ID
                'field[FIRSTNAME,0]'            => ucwords(strtolower($first_name)), // using the personalization tag instead (make sure to encode the key)
                'field[LASTNAME,0]'             => ucwords(strtolower($last_name)),
                'field[EMAIL,0]'                => $leadsemaildata->email,
                'field[%UNIQUE_KEY%,0]'         => $element_lead_id,
                'field[%SOURCE%,0]'             => $origin->lead_source_name,
                'field[%TWEET_ENCRY_ID%,0]'     => $enctypted_leadId,
                'field[%PHONE_VERIFIED%,0]'     => ($leadsemaildata->is_verified == "yes") ? 1 : 0,
                'field[%PHONE_ENCRY_ID%,0]'     => base64_encode($leadsemaildata->phone),

                'field[%ZIP_CODE%,0]'           =>  $leadsemaildata->junkMoveInfo->from_zipcode,
                'field[%DATE_OF_JUNK_REMOVAL%,0]' => $leadsemaildata->junkMoveInfo->junk_remove_date,
                'field[%WASTE_MATERIAL_REMOVAL_TYPE%,0]' => $junRemovalType,

                // assign to lists:
                'p[' . $listid . ']'                => $listid, // example list ID (REPLACE '123' WITH ACTUAL LIST ID, IE: p[5] = 5)
                'status[' . $listid . ']'           => 1, // 1: active, 2: unsubscribed (REPLACE '123' WITH ACTUAL LIST ID, IE: status[5] = 1)
                //'form'                        => 1001, // Subscription Form ID, to inherit those redirection settings
                //'noresponders[123]'           => 1, // uncomment to set "do not send any future responders"
                //'sdate[123]'                  => '2009-12-07 06:00:00', // Subscribe date for particular list - leave out to use current date/time
                // use the folowing only if status=1
                'instantresponders[' . $listid . ']' => 1, // set to 0 to if you don't want to sent instant autoresponders
                //'lastmessage[123]'            => 1, // uncomment to set "send the last broadcast campaign"

                //'p[]'                         => 345, // some additional lists?
                //'status[345]'                 => 1, // some additional lists?
            );
        } else if ($leadsemaildata->lead_category_id == "3") {
            $post = array(
                'email'                         => $leadsemaildata->email,
                'first_name'                    => ucwords(strtolower($first_name)),
                'last_name'                     => ucwords(strtolower($last_name)),
                'phone'                         => (int) $leadsemaildata->phone,
                //'customer_acct_name'          => $leadsemaildata->name,
                'tags'                          => 'api',
                //'ip4'                         => '127.0.0.1',
                // any custom fields
                // 'field[345,0]'               => 'field value', // where 345 is the field ID
                'field[FIRSTNAME,0]'            => ucwords(strtolower($first_name)), // using the personalization tag instead (make sure to encode the key)
                'field[LASTNAME,0]'             => ucwords(strtolower($last_name)),
                'field[EMAIL,0]'                => $leadsemaildata->email,
                'field[%UNIQUE_KEY%,0]'         => $element_lead_id,
                'field[%SOURCE%,0]'             => $origin->lead_source_name,
                'field[%TWEET_ENCRY_ID%,0]'     => $enctypted_leadId,
                'field[%PHONE_VERIFIED%,0]'     => ($leadsemaildata->is_verified == "yes") ? 1 : 0,
                'field[%PHONE_ENCRY_ID%,0]'     => base64_encode($leadsemaildata->phone),

                'field[%ZIP_CODE%,0]'           => $leadsemaildata->heavyLiftMoveInfo->from_zipcode,
                'field[%HAULING_FROM%,0]'       => $leadsemaildata->heavyLiftMoveInfo->from_zipcode,
                'field[%HAULING_TO%,0]'         => $leadsemaildata->heavyLiftMoveInfo->to_zipcode,
                'field[%HAULING_DATE%,0]'       => $leadsemaildata->heavyLiftMoveInfo->lift_date,
                'field[%HEAVY_LIFTING_TYPE%,0]' => $heavyLiftingType,
                // assign to lists:
                'p[' . $listid . ']'                => $listid, // example list ID (REPLACE '123' WITH ACTUAL LIST ID, IE: p[5] = 5)
                'status[' . $listid . ']'           => 1, // 1: active, 2: unsubscribed (REPLACE '123' WITH ACTUAL LIST ID, IE: status[5] = 1)
                //'form'                        => 1001, // Subscription Form ID, to inherit those redirection settings
                //'noresponders[123]'           => 1, // uncomment to set "do not send any future responders"
                //'sdate[123]'                  => '2009-12-07 06:00:00', // Subscribe date for particular list - leave out to use current date/time
                // use the folowing only if status=1
                'instantresponders[' . $listid . ']' => 1, // set to 0 to if you don't want to sent instant autoresponders
                //'lastmessage[123]'            => 1, // uncomment to set "send the last broadcast campaign"

                //'p[]'                         => 345, // some additional lists?
                //'status[345]'                 => 1, // some additional lists?
            );
        } else if ($leadsemaildata->lead_category_id == "4") {
            $post = array(
                'email'                         => $leadsemaildata->email,
                'first_name'                    => ucwords(strtolower($first_name)),
                'last_name'                     => ucwords(strtolower($last_name)),
                'phone'                         => (int) $leadsemaildata->phone,
                //'customer_acct_name'          => $leadsemaildata->name,
                'tags'                          => 'api',
                //'ip4'                         => '127.0.0.1',
                // any custom fields
                // 'field[345,0]'               => 'field value', // where 345 is the field ID
                'field[FIRSTNAME,0]'            => ucwords(strtolower($first_name)), // using the personalization tag instead (make sure to encode the key)
                'field[LASTNAME,0]'             => ucwords(strtolower($last_name)),
                'field[EMAIL,0]'                => $leadsemaildata->email,
                'field[%UNIQUE_KEY%,0]'         => $element_lead_id,
                'field[%SOURCE%,0]'             => $origin->lead_source_name,
                'field[%TWEET_ENCRY_ID%,0]'     => $enctypted_leadId,
                'field[%PHONE_VERIFIED%,0]'     => ($leadsemaildata->is_verified == "yes") ? 1 : 0,
                'field[%PHONE_ENCRY_ID%,0]'     => base64_encode($leadsemaildata->phone),

                'field[%ZIP_CODE%,0]'           => $leadsemaildata->carTransMoveInfo->from_zipcode,
                'field[%TRANSPORT_CAR_FROM%,0]' => $leadsemaildata->carTransMoveInfo->from_zipcode,
                'field[%TRANSPORT_CAR_TO%,0]'   => $leadsemaildata->carTransMoveInfo->to_zipcode,
                'field[%CAR_MOVE_DATE%,0]'      => $leadsemaildata->carTransMoveInfo->move_date,
                'field[%CAR_MADE%,0]'           => $carType,
                'field[%TRAILER_TYPE_OPENCLOSED%,0]' => $transportType,
                // assign to lists:
                'p[' . $listid . ']'                => $listid, // example list ID (REPLACE '123' WITH ACTUAL LIST ID, IE: p[5] = 5)
                'status[' . $listid . ']'           => 1, // 1: active, 2: unsubscribed (REPLACE '123' WITH ACTUAL LIST ID, IE: status[5] = 1)
                //'form'                        => 1001, // Subscription Form ID, to inherit those redirection settings
                //'noresponders[123]'           => 1, // uncomment to set "do not send any future responders"
                //'sdate[123]'                  => '2009-12-07 06:00:00', // Subscribe date for particular list - leave out to use current date/time
                // use the folowing only if status=1
                'instantresponders[' . $listid . ']' => 1, // set to 0 to if you don't want to sent instant autoresponders
                //'lastmessage[123]'            => 1, // uncomment to set "send the last broadcast campaign"

                //'p[]'                         => 345, // some additional lists?
                //'status[345]'                 => 1, // some additional lists?
            );
        }

        $campaignType = "";
        if (isset($leadsemaildata->routingInfoLeadList) && ($leadsemaildata->lead_category_id == "1")) {
            foreach ($leadsemaildata->routingInfoLeadList as $eachroutekey => $eachroutevalue) {
                $campaigin = Campaign::where('campaign_id', $eachroutevalue['campaign_id'])->get()->first();
                $business_fetch = Business::where('business_id', $campaigin->business_id)->get()->first();
                $phone_fetch = $business_fetch->rely_number;

                $subscriptionPlan = Subscription::where('business_id', $business_fetch->business_id)
                                    ->where('is_active', 'yes')
                                    ->first();
                if($subscriptionPlan) {
                    $planDetails = MstSubscriptionPlan::where('subscription_plan_id', $subscriptionPlan->subscription_plan_id)->first();
                    if($planDetails) {
                        if($planDetails->subscription_plan_id == 2) {
                            $callCountN = $planDetails->call_count;
                            if($callCountN > 0) {
                                $startOfMonth = Carbon::now()->startOfMonth();
                                $endOfMonth = Carbon::now()->endOfMonth();
                                $leadCallCount = LeadCall::where('transfer_type', 'businessrely')
                                ->where('business_id', $business_fetch->business_id)
                                ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
                                ->count();

                                if($leadCallCount >= $callCountN) {
                                    $phone_fetch = '';
                                }
                            }
                        }
                    }
                } else {
                    $phone_fetch = '';
                }

                $movers_key = $eachroutekey + 1;
                $post['field[%BUSINESS_LOGO%,0]'] = ($business_fetch->logo != "") ? $business_fetch->logo : "movers.png";
                $post['field[%BUSINESS_NAME_' . $movers_key . '%,0]'] = $business_fetch->display_name;
                if($phone_fetch != null || strlen($phone_fetch) >= 10 ){
                    $post['field[%BUSINESS_PH_' . $movers_key . '%,0]'] = $phone_fetch;
                    $post['field[%BUSINESS_PHONE_' . $movers_key . '_DYNAMIC%,0]'] = preg_replace("/.*(\d{3})[^\d]{0,7}(\d{3})[^\d]{0,7}(\d{4})/", '($1) $2-$3', $phone_fetch);    
                }else{
                    if ($campaigin->campaign_type_id == 2 || $campaigin->campaign_type_id == 4) {
                        $post['field[%BUSINESS_PH_' . $movers_key . '%,0]'] = "8337570501";
                        $post['field[%BUSINESS_PHONE_' . $movers_key . '_DYNAMIC%,0]'] = "(833)757-0501";
                    } else {
                        $post['field[%BUSINESS_PH_' . $movers_key . '%,0]'] = "";
                        $post['field[%BUSINESS_PHONE_' . $movers_key . '_DYNAMIC%,0]'] = "";
                    }
                }
                
                if ($business_fetch->buyer_type_id == 3) {
                    $campaignType = 1;
                } else {
                    $campaignType = $campaigin->campaign_type_id;
                }
                $post['field[%CAMPAIGN_TYPE%,0]'] = $campaignType; //1=Premium, 2=Exclusive, 4=VM Organic, 6=Dual Slot
            }
        }
        
        LeadActiveCamapaignLog::create([
            'lead_id' => $leadId,
            'log'     => json_encode($post)
        ]);
        // This section takes the input fields and converts them to the proper format
        $query = "";
        foreach ($params as $key => $value) $query .= urlencode($key) . '=' . urlencode($value) . '&';
        $query = rtrim($query, '& ');

        // This section takes the input data and converts it to the proper format
        $data = "";
        foreach ($post as $key => $value) $data .= urlencode($key) . '=' . urlencode($value) . '&';
        $data = rtrim($data, '& ');

        // clean up the url
        $url = rtrim($url, '/ ');

        // define a final API request - GET
        $api = $url . '/admin/api.php?' . $query;
        
        $request = curl_init($api); // initiate curl object
        curl_setopt($request, CURLOPT_HEADER, 0); // set to 0 to eliminate header info from response
        curl_setopt($request, CURLOPT_RETURNTRANSFER, 1); // Returns response data instead of TRUE(1)
        curl_setopt($request, CURLOPT_POSTFIELDS, $data); // use HTTP POST to send form data
        //curl_setopt($request, CURLOPT_SSL_VERIFYPEER, FALSE); // uncomment if you get no gateway response and are using HTTPS
        curl_setopt($request, CURLOPT_FOLLOWLOCATION, true);

        $response = (string)curl_exec($request); // execute curl post and store results in $response

        // additional options may be required depending upon your server configuration
        // you can find documentation on curl options at http://www.php.net/curl_setopt
        curl_close($request); // close curl object

        // if (!$response) {
        //     //throw new Exception("Nothing was returned. Do you have a connection to Email Marketing server?");
        //     $response['error'] = 'Nothing was returned. Do you have a connection to Email Marketing server?';
        //     return json_encode($response);
        // }

        // This line takes the response and breaks it into an array using:
        // JSON decoder
        //$result = json_decode($response);
        // unserializer
        $response = unserialize($response);
        $logActiveCampaign = LeadActiveCamapaignLog::create([
            'lead_id' => $leadId,
            'log'     => json_encode($response)
        ]);
        $leadactiviyobj = new LeadActivity();
        $leadactiviyobj->createActivity($leadId, 4);
        //DB::table('test')->insert(['result' => 'LogActiveCampaign with Valid Email: ' . json_encode($logActiveCampaign)]);
        //  echo "<pre>";print_r($response);exit();
        return json_encode($response);
    }
}
