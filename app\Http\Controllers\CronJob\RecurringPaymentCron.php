<?php

namespace App\Http\Controllers\CronJob;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Business\Business;
use App\Models\Campaign\Campaign;
use App\Models\Business\BusinessCC;
use App\Models\Business\BusinessCampaignPayment;
use App\Models\Business\BusinessCampaignRecuringPayment;
use App\Models\Business\BusinessCampaignRecurringPaymentDay;
use App\Models\Business\BusinessCampaingnPaymentLog;
use App\Http\Controllers\Businesses\BusinessPaymentController;
use DB;
use Exception;
use Auth;
use App\Helpers\Helper;
use App\Models\CronJob\CronLog;
use App\Models\DevDebug;
use App\Helpers\CommonFunctions;
use App\Models\Emails\TemplateEmail;
use App\Models\Emails\RecurringEmailLog;

class RecurringPaymentCron extends Controller
{
    public function recurringPayment() {
        try {
            $this->insertLog(3,'recurringPayment Cron Start: ' . date("Y-m-d H:i:s"));
            // $publishableKey = Helper::checkServer()['stripe_publishable_key'];
            // $secretKey = Helper::checkServer()['stripe_secret_key'];
            $businessPaymentObj = New BusinessPaymentController();
            $recuringPaymentDetail = BusinessCampaignRecuringPayment::where('is_active', 'yes')->get();
            //echo '<pre>'; print_r($recuringPaymentDetail); die;
            foreach ($recuringPaymentDetail as $recuringPayment) {
                $data = [];
                $businessName = $businessEmail = $businessCampaignName = "";
                $businessCampaignType = 0;
                $recurringPaymentId = $recuringPayment->business_campaign_recurring_payment_id;
                $businessId = $recuringPayment->business_id;
                $campaignId = $recuringPayment->campaign_id;
                $cardId = $recuringPayment->business_cc_id;

                $checkLastStatus = DB::select("SELECT is_charge_approved, business_campaign_payment_id FROM business_campaign_payment WHERE business_cc_id = '" . $cardId . "' ORDER BY business_campaign_payment_id DESC LIMIT 2");
                $statusFail = $paymentId = 0;
                foreach ($checkLastStatus as $value) {
                    if ($value->is_charge_approved == 'no') {
                        $statusFail += 1;
                    }
                    if ($paymentId == 0) {
                        $paymentId = $value->business_campaign_payment_id;
                    }
                }
                $this->insertLog(3, 'businessId: ' . $businessId . ' and campaignId: ' . $campaignId . ' statusFail: ' . $statusFail);
                if ($statusFail == 2) {
                    //change recurring status 0
                    BusinessCampaignRecuringPayment::where('business_cc_id', $cardId)
                        ->update([
                            'is_active' => 'no'
                        ]);

                    $businessCampaignPaymentLogDetail = BusinessCampaingnPaymentLog::where('business_campaign_payment_id', $paymentId)->first();
                    $cardDetail = BusinessCC::where('business_cc_id', $cardId)->first();
                    if ($campaignId > 0) {
                        $campaignDetail = Campaign::where('campaign_id', $campaignId)->first();
                        $businessDetail = Business::where('business_id', $campaignDetail->business_id)->first();
                        $data['campaign_id'] = $campaignDetail->campaign_id;
                        $data['campaign_name'] = $campaignDetail->campaign_name;
                        $businessCampaignName = $campaignDetail->campaign_name;
                    } else {
                        $businessDetail = Business::where('business_id', $businessId)->first();
                        $data['campaign_id'] = '--';
                        $data['campaign_name'] = '--';
                        $data['email'] = $businessDetail->email;
                        $businessCampaignName = $businessDetail->business_name ?? '';
                    }
                    $data['business_cc_id'] = $cardId;
                    $data['business_id'] = $businessDetail->business_id;
                    $data['business_name'] = $businessDetail->business_name;
                    $data['email'] = $businessDetail->email;
                    $data['recurring_type'] = $recuringPayment->recurring_type;
                    $response = json_decode($businessCampaignPaymentLogDetail->response);
                    $data['response'] = (isset($response->message)) ? $response->message : $response->avs_message;
                    $data['cardNumber'] = 'XXXX-XXXX-XXXX-' . sprintf("%04d", $cardDetail->card);

                    //Added by BK on 05/02/2025 convenience fee email to evey success payment
                    $businessPaymentObj->recurrentStopEmailNotificationTemplate($businessId, $businessDetail->business_name, $businessCampaignName, $businessDetail->email);

                    //send inactive email notification to sales rep
                    $this->sendEmail($data);
                    goto stop;
                }

                //fetch card details from business_cc table
                $paymentCardId = '';
                $cardDetail = BusinessCC::where('business_cc_id', $cardId)
                    ->where('status','active')
                    ->get(['payment_cust_id', 'payment_card_id', 'card'])
                    ->first();
                if($cardDetail) {
                    $paymentCardId = $cardDetail->payment_card_id;
                }
                //echo '<pre>'; print_r($cardDetail); die;
                
                $recuringType = $recuringPayment->recurring_type;
                $charge = $recuringPayment->amount;
                $threshold = $recuringPayment->threshold_amount;
                //echo $recuringType; die;
                $this->insertLog(3, 'recuringType: ' . $recuringType);
                $flag = $convenienceFee = 0;
                if ($recuringType == 'amount') {
                    // amount based
                    if (!empty($businessId)) {
                        $businessCampaignType = 1;
                        $businessCampaignPaymentDetail = Business::where('status', 'active')
                            ->where('credit_available', '<=', $threshold)
                            ->where('business_id', $businessId)
                            ->first();
                    }

                    if (!empty($campaignId)) {
                        $businessCampaignType = 2;
                        $businessCampaignPaymentDetail = Campaign::where('is_active', 'yes')
                            ->where('credit_available', '<=', $threshold)
                            ->where('campaign_id', $campaignId)
                            ->first();
                    }

                    if (!empty($businessCampaignPaymentDetail)) {
                        $businessDetail = Business::where('business_id', $businessCampaignPaymentDetail->business_id)->first();
                        $businessName = $businessDetail->display_name;
                        $businessEmail = $businessDetail->email;
                        $businessCampaignName = ($businessCampaignType > 1) ? $businessCampaignPaymentDetail->campaign_name : $businessName;
                        $flag = 1;
                    }
                } else {
                    $date = date("Y-m-d");
                    $dateTime = date("Y-m-d H:i:s");
                    // Find Day
                    $date = strtotime($date);
                    $dayNumber = date("N", $date);

                    // Find Hour
                    $hour = strtotime($dateTime);
                    $hourNumber = date('H', $hour);

                    //echo $dayNumber." ".$hourNumber." ".$recurringPaymentId; die;
                    $this->insertLog(3, 'dayNumber: ' . $dayNumber . ' and hourNumber: ' . $hourNumber . ' and recurringPaymentId: ' .$recurringPaymentId);
                    $recuringPaymentDailyDetail = BusinessCampaignRecurringPaymentDay::where('business_campaign_recurring_payment_id', $recurringPaymentId)->where('day', $dayNumber)->where('payment_time', $hourNumber)->first();
                    //echo '<pre>'; print_r($recuringPaymentDailyDetail); die;
                    if (!empty($recuringPaymentDailyDetail)) {
                        //now check today update or not
                        if (!empty($businessId)) {
                            $businessCampaignType = 1;
                            $businessCampaignPaymentDetail = BusinessCampaignPayment::where('business_id', $businessId)->where('created_at', 'LIKE', date("Y-m-d") . '%')->where('charge_user_id', 6)->where('is_charge_approved', 'yes')->first();
                            $businessStatus = Business::where('business_id', $businessId)->where('status', 'active')->first();
                            if (empty($businessStatus)) {
                                $businessCampaignType = 0;
                            }
                        }

                        if (!empty($campaignId)) {
                            $businessCampaignType = 2;
                            $businessCampaignPaymentDetail = BusinessCampaignPayment::where('campaign_id', $campaignId)->where('created_at', 'LIKE', date("Y-m-d") . '%')->where('charge_user_id', 6)->where('is_charge_approved', 'yes')->first();
                            $campaignStatus = Campaign::where('campaign_id', $campaignId)->where('is_active', 'yes')->first();
                            $campaignDetail = Campaign::where('campaign_id', $campaignId)->first();
                            $businessId = $campaignDetail->business_id;
                            if (empty($campaignStatus)) {
                                $businessCampaignType = 0;
                            }
                        }

                        if (empty($businessCampaignPaymentDetail)) {
                            $businessDetail = Business::where('business_id', $businessId)->first();
                            $businessName = $businessDetail->display_name;
                            $businessEmail = $businessDetail->email;
                            $businessCampaignName = ($businessCampaignType > 1) ? $campaignDetail->campaign_name : $businessName;
                            $flag = 1;
                        }
                    }
                }

                // echo '<pre>flag = '. $flag .', businessCampaignType = '. $businessCampaignType.', businessCampaignType = '. $businessId;
                $this->insertLog(3, 'flag: ' . $flag . ' and businessCampaignType: ' . $businessCampaignType . ' and paymentCardId: ' . $paymentCardId);
                if ($flag == 1 && $businessCampaignType > 0 && !empty($paymentCardId)) {
                    //$userId = Auth::user()->id;

                    //Added by BK on 03/02/2025 convenience fee to evey payment
                    if ($charge > 0) {
                        $convenienceFee = ($charge * 3) / 100;
                        $charge         = $charge + $convenienceFee;
                    }

                    $omni_token         = Helper::checkServer()['omni_token'];
                    $postFields         = array("payment_method_id" => $paymentCardId, "meta" => array("tax" => 0), "total" => $charge, "pre_auth" => 0);
                    $jsonData           = json_encode($postFields);
                    $ch                 = curl_init();
                    curl_setopt($ch, CURLOPT_URL, "https://apiprod.fattlabs.com/charge");
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
                    curl_setopt($ch, CURLOPT_HEADER, FALSE);
                    curl_setopt($ch, CURLOPT_POST, TRUE);
                    DevDebug::create(['sr_no' => 111, 'result' => 'Payment request: ' . $jsonData, 'created_at' => date('Y-m-d H:i:s')]);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);

                    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                        "Content-Type: application/json",
                        "Authorization:Bearer ".$omni_token,
                        "Accept: application/json"
                    ));

                    $response           = curl_exec($ch);
                    curl_close($ch);
                    $jsonResponse       = json_decode($response, true);
                    $responseStatus     = "no";
                    if (isset($jsonResponse['success']) && $jsonResponse['success'] == true) {
                        $responseStatus = "yes";
                    }
                    DevDebug::create(['sr_no' => 112, 'result' => 'Payment response: ' . json_encode($jsonResponse), 'created_at' => date('Y-m-d H:i:s')]);
                    //print_r($jsonResponse);

                    //Added by BK on 03/02/2025 convenience fee to evey payment
                    if ($convenienceFee > 0) {
                        $charge = $charge - $convenienceFee;
                    }

                    if ($businessCampaignType == 2) { //campaign level

                        // echo '<pre>flag = '. $flag .', businessCampaignType = '. $businessId;  print_r($jsonResponse);
                        $this->insertLog(3, 'chargePayment Response: ' .  $response);
                        $totalBalance = Campaign::where('campaign_id', $campaignId)->get(['credit_available']);
                        $beforeCharge = 0;
                        if (count($totalBalance) > 0) {
                            $beforeCharge = $totalBalance[0]->credit_available;
                        }
                        $amountCharge = $charge;
                        $afterCharge = $beforeCharge;
                        if($responseStatus == "yes") {
                            $afterCharge = $beforeCharge + $amountCharge;
                        }

                        $businessCampaignPaymentId = BusinessCampaignPayment::create([
                            'campaign_id' => $campaignId,
                            'business_cc_id' => $cardId,
                            'balance_before_charge' => $beforeCharge,
                            'amount_charge' => $amountCharge,
                            'balance_after_charge' => $afterCharge,
                            'convenience_fee' => $convenienceFee,
                            'charge_type_id' => 1,
                            'charge_method_type' => 'snapituser',
                            'charge_user_id' => 6,
                            'charge_method' => 'recurrent',
                            'fatt_payment_id' => (isset($jsonResponse['id'])) ? $jsonResponse['id'] : '',
                            'is_charge_approved' => $responseStatus,
                            'created_at' => date('Y-m-d H:i:s')
                        ])->business_campaign_payment_id;

                        BusinessCampaingnPaymentLog::create([
                            'business_campaign_payment_id' => $businessCampaignPaymentId,
                            'request' => $jsonData,
                            'response' => json_encode($jsonResponse, true),
                            'created_at' => date('Y-m-d H:i:s'),
                        ])->business_campaign_payment_log_id;

                    } else if ($businessCampaignType == 1) { //business level

                        // echo '<pre>flag = '. $flag .', businessCampaignType = '. $businessId;  print_r($jsonResponse);
                        $this->insertLog(3, 'chargePayment Response: ' .  $response);
                        $totalBalance = Business::where('business_id', $businessId)->get(['credit_available']);
                        $beforeCharge = 0;
                        if(count($totalBalance) > 0) {
                            $beforeCharge = $totalBalance[0]->credit_available;
                        }
                        $amountCharge = $charge;
                        $afterCharge = $beforeCharge;
                        if($responseStatus == "yes") {
                            $afterCharge = $beforeCharge + $amountCharge;
                        }

                        $businessCampaignPaymentId = BusinessCampaignPayment::create([
                            'business_id' => $businessId,
                            'business_cc_id' => $cardId,
                            'balance_before_charge' => $beforeCharge,
                            'amount_charge' => $amountCharge,
                            'balance_after_charge' => $afterCharge,
                            'convenience_fee' => $convenienceFee,
                            'charge_type_id' => 1,
                            'charge_method_type' => 'snapituser',
                            'charge_user_id' => 6,
                            'charge_method' => 'recurrent',
                            'fatt_payment_id' => (isset($jsonResponse['id'])) ? $jsonResponse['id'] : '',
                            'is_charge_approved' => $responseStatus,
                            'created_at' => date('Y-m-d H:i:s')
                        ])->business_campaign_payment_id;

                        BusinessCampaingnPaymentLog::create([
                            'business_campaign_payment_id' => $businessCampaignPaymentId,
                            'request' => $jsonData,
                            'response' => json_encode($jsonResponse, true),
                            'created_at' => date('Y-m-d H:i:s'),
                        ])->business_campaign_payment_log_id;
                    }

                    //fetch total balance from businessespaymenthistory table
                    if ($responseStatus == "yes") {
                        if($businessCampaignType == 2) {
                            //update balance in campaign table
                            Campaign::where('campaign_id', $campaignId)
                                ->update([
                                    'credit_available' => $afterCharge
                                ]);
                        } else {
                            //update balance in business table
                            Business::where('business_id', $businessId)
                                ->update([
                                    'credit_available' => $afterCharge
                                ]);
                        }

                        //Added by BK on 05/02/2025 convenience fee email to evey success payment
                        $transactionId = (isset($jsonResponse['id'])) ? $jsonResponse['id'] : '';
                        $businessPaymentObj->moversRechargeEmailNotificationTemplate($businessId, $businessName, $businessCampaignName, $businessEmail, $cardDetail->card, $charge, $convenienceFee, $transactionId);
                    } else {
                        //throw new Exception($jsonResponse['message']);
                    }
                }
                DevDebug::create(['sr_no' => 113, 'result' => 'campaignId ' . $campaignId .', businessId ' . $businessId, 'created_at' => date('Y-m-d H:i:s')]);
                stop:
            }
        } catch (Exception $e) {
            DevDebug::create(['sr_no' => 114, 'result' => 'Catch error ' . $e->getMessage(), 'created_at' => date('Y-m-d H:i:s')]);
            dd($e->getMessage());
        }
    }

    public function insertLog($srno, $log){
        CronLog::create([
            'log_srno' => $srno,
            'log' => $log,
            'cron_type' => 'recurringpaymentcron',
            'created_at' => date('Y-m-d H:i:s'),
        ]);
    }

    public function sendEmail($data)
    {
        try {

            $postmarkToken  = "************************************";
            $mail           = new TemplateEmail();

            $mail->setFrom('<EMAIL>');
            //$mail->setTo($data['email']);
            $mail->setTo('<EMAIL>,<EMAIL>,<EMAIL>');
            // $mail->setCc('<EMAIL>');
            $mail->setSubject(ucwords($data['business_name']).' - Recurrent Payment status Inactivated');
            $pageContent    = $this->sendEmailTemplate($data);
            $mail->setHtmlbody($pageContent); // set body content
            $response       = $mail->send_email($postmarkToken, 'recurrent_payment_failed');

            RecurringEmailLog::create([
                "business_id" => $data['business_id'],
                "business_cc_id" => $data['business_cc_id'],
                "subject" => 'Your Recurring Payment status Inactive',
                "set_to" => '<EMAIL>,<EMAIL>',
                "set_from" => '<EMAIL>',
                "response" => json_encode($response->original['data']),
                'created_at' => date('Y-m-d H:i:s')
            ]);

            return $response;
        } catch (Exception $e) {
            $this->insertLog(1, json_encode($e));
        }
    }

    public function sendEmailTemplate($data) {
        $businessName = ($data['business_id'] > 0) ? $data['business_name']." (".$data['business_id'].")" : "--";
        $campaignName = ($data['campaign_id'] > 0) ? $data['campaign_name']." (".$data['campaign_id'].")" : "--";
        $content = "<!DOCTYPE html>
        <html>
            <head>
                <meta charset=\"ISO-8859-1\">
                <title>Recurrent Payment Information</title>
            </head>
            <body>
                <table>
                    <tr><td>Dear ".ucwords($data['business_name'])."</td></tr>
                    <tr><td>Thanks for doing business with us</td></tr>
                    <tr><td>We regret to inform you that your consecutive recurring payment attempt has unfortunately failed. Due to this, we have stopped recurring payments.</td></tr>
                    <tr><td><hr></td></tr>
                    <tr><td>
        
                    <tr><td><hr></td></tr>
                    <tr><td>Your last Recurrent Payment details are enlisted.</td></tr>
                    <tr>
                        <td>
                            <table>
                                <tr><td colspan=\"3\"><b>Recurrent Payment Info :</b></td></tr>
                                <tr><td width=\"100px\">Business Name</td><td> : </td><td>".$businessName."</td></tr>
                                <tr><td>Campaign Name</td><td> : </td><td>".$campaignName."</td></tr>
                                <tr><td>Recurring Type</td><td> : </td><td>".ucwords($data['recurring_type'])."</td></tr>
                                <tr><td>Card Number</td><td> : </td><td>".ucwords($data['cardNumber'])."</td></tr>
                                <tr><td>Last Cancelled/Declined Transaction Details</td><td> : </td>".$data['response']."<td></td></tr>
        
                                <tr><td colspan=\"3\">&nbsp;</td></tr>
                                <tr><td colspan=\"3\">Thank you,</td></tr>
                                <tr><td colspan=\"3\">Prime Marketing Sales Team</td></tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </body>
        </html>";

        return $content;
    }
}
