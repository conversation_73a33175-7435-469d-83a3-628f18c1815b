<?php

namespace App\Http\Controllers\Emails;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Emails\TemplateEmail;
use App\Exports\RexDirectLeadExport;
use App\Exports\RexDirectCallExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Emails\EmailLog;

use PDF;
use Exception;
use DB;
use Log;

class PrimeMarketingRexdirectInvoiceEmailController extends Controller
{
    public function primeRexdirectInvoiceEmailSend()
    {
        // Store on a different disk (e.g. s3)
        $startDate      = date("Y-m-d", mktime(0, 0, 0, date("m")-1, 1));
        $endDate        = date("Y-m-d", mktime(0, 0, 0, date("m"), 0));
        $month          = date('F', strtotime(date('F') . " last month"));
        $year           = date('Y', strtotime(date('Y') . " last month"));
        $csvfileName    = 'RexDirectLead_'.$month.'_'.$year.'.csv';
        $csvfileName1   = 'RexDirectCall_'.$month.'_'.$year.'.csv';
        $pdffileName    = 'RexDirect_'.$month.'_'.$year.'.pdf';
        Excel::store(new RexDirectLeadExport(), $csvfileName, 'export');
        Excel::store(new RexDirectCallExport(), $csvfileName1, 'export');
        //Excel::store(new RexDirectLeadExport(), '/export/' . $csvfileName);

        $leadDetail     = DB::select("SELECT MONTH(l.created_at) AS month, sum(lr.payout) as total_amount, COUNT(l.lead_id) AS total_leads, COUNT(CASE WHEN lm.from_state!=lm.to_state THEN 1 END) as long_leads, COUNT(CASE WHEN lm.from_state=lm.to_state THEN 1 END) as local_leads
                                      FROM lead_routing lr
                                      LEFT JOIN `lead` l on lr.lead_id = l.lead_id
                                      LEFT JOIN campaign c on lr.campaign_id = c.campaign_id
                                      INNER JOIN lead_moving lm ON l.lead_id = lm.lead_id
                                      WHERE lr.campaign_id IN (122, 352, 1449, 2398) AND lr.route_status = 'sold' AND (lr.created_at >= '".$startDate." 00:00:00' AND lr.created_at <= '".$endDate." 23:59:59')
                                      GROUP BY MONTH(l.lead_generated_at)
                                      ORDER BY MONTH(l.lead_generated_at) ASC");
        $leadArray      = [];
        $invoiceMonth   = 0;
        $totalLeads     = 0;
        $totalAmount    = 0;
        if (isset($leadDetail) && !empty($leadDetail)) {
            foreach ($leadDetail as $lead) {
                $invoiceMonth = $lead->month;
                $totalLeads += $lead->total_leads;
                $totalAmount += $lead->total_amount;
            }
            $leadArray[]= array(
                'month' => $invoiceMonth,
                'total_leads' => $totalLeads,
                'total_amount' => $totalAmount
            );
        }

        /*$leadDetail1  = DB::select("SELECT MONTH(l.created_at) AS month, COUNT(1) AS total_ldcall, SUM(IF(l.duration > 30 OR l.duration IS NULL, 65, 0)) AS total_ldcallamount
                                      FROM lead_transfer_call l 
                                      LEFT JOIN prod_linkup.lead ll ON l.lead_id = ll.lead_id
                                      LEFT JOIN lead_moving lm ON l.lead_id = lm.lead_id
                                      WHERE l.campaign_id IN (4965, 4966) AND l.created_at >= '".$startDate." 00:00:00' AND l.created_at <= '".$endDate." 23:59:59'
                                      AND HOUR(l.created_at) >=08 AND HOUR(l.created_at) < 18 AND WEEKDAY(l.created_at)>= 0 AND WEEKDAY(l.created_at) < 5 
                                      AND (lm.from_state NOT IN ('AK', 'HI') OR lm.from_state IS NULL)");*/

        $leadDetail1    = DB::select("SELECT MONTH(l.created_at) AS month, COUNT(1) AS total_ldcall, SUM(IF(l.duration > 30 OR l.duration IS NULL, 68, 0)) AS total_ldcallamount 
                                      FROM lead_transfer_call l 
                                      LEFT JOIN prod_linkup.lead ll ON l.lead_id = ll.lead_id
                                      LEFT JOIN lead_moving lm ON l.lead_id = lm.lead_id
                                      WHERE l.campaign_id IN (SELECT c.campaign_id FROM campaign c INNER JOIN campaign_moving cm ON c.campaign_id = cm.campaign_id WHERE cm.move_type = 'long' AND c.business_id = 55) AND l.created_at >= '".$startDate." 00:00:00' AND l.created_at <= '".$endDate." 23:59:59'
                                      AND HOUR(l.created_at) >= 11 AND HOUR(l.created_at) < 19 AND WEEKDAY(l.created_at)>= 0 AND WEEKDAY(l.created_at) < 5 /*AND WEEKDAY(l.created_at) <> 4*/ 
                                      AND (lm.from_state NOT IN ('AK', 'HI') OR lm.from_state IS NULL)");

        if (isset($leadDetail1) && !empty($leadDetail1)) {
            $leadArray[]= array(
                'month' => $leadDetail1[0]->month,
                'total_ldcall' => $leadDetail1[0]->total_ldcall ?? 0,
                'total_ldcallamount' => $leadDetail1[0]->total_ldcallamount ?? 0
            );
        }

        /*$leadDetail2  = DB::select("SELECT MONTH(l.created_at) AS month, COUNT(1) AS total_ldahcall, SUM(IF(l.duration > 90 OR l.duration IS NULL, 40, 0)) AS total_ldahcallamount
                                      FROM lead_transfer_call l
                                      LEFT JOIN `lead` ll ON l.lead_id = ll.lead_id
                                      LEFT JOIN lead_moving lm ON l.lead_id = lm.lead_id
                                      WHERE l.campaign_id IN (4965, 4966) AND l.created_at >= '".$startDate." 00:00:00' AND l.created_at <= '".$endDate." 23:59:59'
                                      AND ( (HOUR(l.created_at) >=08 AND HOUR(l.created_at) < 18 AND ( WEEKDAY(l.created_at) >= 5)) OR (HOUR(l.created_at) >= 18) )
                                      AND (lm.from_state NOT IN ('AK', 'HI') OR lm.from_state IS NULL)");*/

        $leadDetail2    = DB::select("SELECT MONTH(l.created_at) AS month, COUNT(1) AS total_ldahcall, SUM(IF(l.duration > 30 OR l.duration IS NULL, 48, 0)) AS total_ldahcallamount
                                      FROM lead_transfer_call l
                                      LEFT JOIN `lead` ll ON l.lead_id = ll.lead_id
                                      LEFT JOIN lead_moving lm ON l.lead_id = lm.lead_id
                                      WHERE l.campaign_id IN (SELECT c.campaign_id FROM campaign c INNER JOIN campaign_moving cm ON c.campaign_id = cm.campaign_id WHERE cm.move_type = 'long' AND c.business_id = 55) AND l.created_at >= '".$startDate." 00:00:00' AND l.created_at <= '".$endDate." 23:59:59'
                                      AND ( (HOUR(l.created_at) >= 13 AND HOUR(l.created_at) < 19 AND ( WEEKDAY(l.created_at) >= 5)) /*OR (HOUR(l.created_at) >= 18)*/ )
                                      AND (lm.from_state NOT IN ('AK', 'HI') OR lm.from_state IS NULL)");

        if (isset($leadDetail2) && !empty($leadDetail2)) {
            $leadArray[]= array(
                'month' => $leadDetail2[0]->month,
                'total_ldahcall' => $leadDetail2[0]->total_ldahcall ?? 0,
                'total_ldahcallamount' => $leadDetail2[0]->total_ldahcallamount ?? 0
            );
        }

        /*$leadDetail3  = DB::select("SELECT MONTH(l.created_at) AS month, COUNT(1) AS total_localcall, SUM(IF(l.duration > 60 OR l.duration IS NULL, 15, 0)) AS total_localcallamount
                                      FROM lead_transfer_call l 
                                      LEFT JOIN `lead` ll ON l.lead_id = ll.lead_id
                                      LEFT JOIN lead_moving lm ON l.lead_id = lm.lead_id
                                      WHERE l.campaign_id IN (4964, 4967) AND l.created_at >= '".$startDate." 00:00:00' AND l.created_at <= '".$endDate." 23:59:59'
                                      AND HOUR(l.created_at) >=08 AND HOUR(l.created_at) < 18 AND WEEKDAY(l.created_at)>= 0 AND WEEKDAY(l.created_at) < 5 
                                      AND (lm.from_state NOT IN ('AK', 'HI') OR lm.from_state IS NULL)");*/

        $leadDetail3    = DB::select("SELECT MONTH(l.created_at) AS month, COUNT(1) AS total_localcall, SUM(IF(l.duration > 75 OR l.duration IS NULL, 14, 0)) AS total_localcallamount
                                      FROM lead_transfer_call l 
                                      LEFT JOIN `lead` ll ON l.lead_id = ll.lead_id
                                      LEFT JOIN lead_moving lm ON l.lead_id = lm.lead_id
                                      WHERE l.campaign_id IN (SELECT c.campaign_id FROM campaign c INNER JOIN campaign_moving cm ON c.campaign_id = cm.campaign_id WHERE cm.move_type = 'local' AND c.business_id = 55) AND l.created_at >= '".$startDate." 00:00:00' AND l.created_at <= '".$endDate." 23:59:59'
                                      AND HOUR(l.created_at) >= 08 AND HOUR(l.created_at) < 21 /*AND WEEKDAY(l.created_at)>= 0 AND WEEKDAY(l.created_at) < 5*/ 
                                      AND (lm.from_state NOT IN ('AK', 'HI') OR lm.from_state IS NULL)");
        if (isset($leadDetail3) && !empty($leadDetail3)) {
            $leadArray[]= array(
                'month' => $leadDetail3[0]->month,
                'total_localcall' => $leadDetail3[0]->total_localcall ?? 0,
                'total_localcallamount' => $leadDetail3[0]->total_localcallamount ?? 0
            );
        }

        $leadDetail4    = DB::select("SELECT MONTH(l.created_at) AS month, COUNT(1) AS total_localahcall, SUM(IF(l.duration > 90 OR l.duration IS NULL, 4, 0)) AS total_localahcallamount
                                      FROM lead_transfer_call l
                                      LEFT JOIN `lead` ll ON l.lead_id = ll.lead_id
                                      LEFT JOIN lead_moving lm ON l.lead_id = lm.lead_id
                                      WHERE l.campaign_id IN (SELECT c.campaign_id FROM campaign c INNER JOIN campaign_moving cm ON c.campaign_id = cm.campaign_id WHERE cm.move_type = 'local' AND c.business_id = 55) AND l.created_at >= '".$startDate." 00:00:00' AND l.created_at <= '".$endDate." 23:59:59'
                                      AND ( (HOUR(l.created_at) >= 08 AND HOUR(l.created_at) < 18 AND ( WEEKDAY(l.created_at) >= 5)) OR (HOUR(l.created_at) >= 18) )
                                      AND (lm.from_state NOT IN ('AK', 'HI') OR lm.from_state IS NULL)");
        if (isset($leadDetail4) && !empty($leadDetail4)) {
            $leadArray[]= array(
                'month' => $leadDetail4[0]->month,
                'total_localahcall' => $leadDetail4[0]->total_localahcall,
                'total_localahcallamount' => $leadDetail4[0]->total_localahcallamount ?? 0
            );
        }
        //echo '<pre>'; print_r($leadArray); die;

        view()->share('leadDetail', $leadArray);
        $pdf            = PDF::loadView('exports.rexdirectinvoicepdf', $leadArray);
        $path           = public_path('/export/');
        $pdf->save($path . $pdffileName);

        // dd('sent');
        //send email
        $this->sendEmail(array($csvfileName, $csvfileName1, $pdffileName), $leadArray);

        //remove image from export directory
        //$this->destroy(array($csvfileName, $pdffileName));
        echo "Invoice email sent successfully."; exit();
    }

    public function sendEmail ($fileName, $leadDetail)
    {
        $subject        = 'Rexdirect Invoice and lead list - ' . date('F', mktime(0, 0, 0, $leadDetail[0]['month'], 10)) . ' ' . date('Y', strtotime(date('Y') . " last month"));
        $setFrom        = '<EMAIL>';
        $setTo          = '<EMAIL>';
        //$setTo        = '<EMAIL>';
        $postmarkToken  = '************************************';

        $mailBodyData   = array();
        $mail           = new TemplateEmail();

        $html           = 'Hello Rexdirect,<br><br> We want to let you know that Prime Marketing has sent you a Monthly Purchased Leads <strong>Invoice of $' . @number_format($leadDetail[0]['total_amount'] + $leadDetail[1]['total_ldcallamount'] + $leadDetail[2]['total_ldahcallamount'] + $leadDetail[3]['total_localcallamount'] + $leadDetail[4]['total_localahcallamount'] - 54, 2) . '</strong> for <strong>' . date('F', mktime(0, 0, 0, $leadDetail[0]['month'], 10)) . ', ' . date('Y', strtotime(date('Y') . " last month")) . '</strong>.<br><br> Please find the attached invoice and purchased lead details.<br><br> Please don\'t hesitate to get in <NAME_EMAIL> if you have any questions or need clarifications.<br><br> Best regards,<br> Client Support,<br> Prime Marketing LLC,<br> <a href="http://mover.primemarketing.us/">www.primemarketing.us</a>';
        // send email
        $mail->setTo($setTo);
        //$mail->setBcc('<EMAIL>');
        $mail->setFrom($setFrom);
        $mail->setSubject($subject);
        $mail->setHtmlbody($html);
        $excelContent   = base64_encode(file_get_contents(public_path() . '/export/' . $fileName[0]));
        $excelContent1  = base64_encode(file_get_contents(public_path() . '/export/' . $fileName[1]));
        $pdfContent     = base64_encode(file_get_contents(public_path() . '/export/' . $fileName[2]));
        $pdfContent1    = base64_encode(file_get_contents(public_path() . '/export/RexDirect_April_2025.pdf'));
        $attchment[]    = ['Name' => $fileName[0], 'Content' => $excelContent, 'ContentType' => 'application/excel'];
        $attchment[]    = ['Name' => $fileName[1], 'Content' => $excelContent1, 'ContentType' => 'application/excel'];
        $attchment[]    = ['Name' => $fileName[2], 'Content' => $pdfContent, 'ContentType' => 'application/octet-stream'];
        //$attchment[]    = ['Name' => 'RexDirect_April_2025.pdf', 'Content' => $pdfContent1, 'ContentType' => 'application/octet-stream'];
        //echo '<pre>'; print_r($attchment); die;
        $mail->setAttachment($attchment);
        $response = $mail->send_email($token = $postmarkToken, 'invoice');

        EmailLog::create([
            "business_id" => 55,
            "subject" => $subject,
            "set_to" => $setTo,
            "set_from" => $setFrom,
            "response" => json_encode($response->original['data']),
            "type" => 'invoice',
        ]);
    }

    public function destroy($fileName)
    {
        for ($i=0; $i < count($fileName); $i++) {
            $path = app_path("export/" . $fileName[$i]);
            unlink($path);
        }
    }
}
