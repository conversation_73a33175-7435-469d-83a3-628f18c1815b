<?php

namespace App\Http\Controllers\API\MoverInformation;

use App\Http\Controllers\Controller;
use App\Models\Lead\Lead;
use App\Models\Campaign\Campaign;
use App\Models\Business\Business;
use App\Models\Lead\LeadRouting;
use Exception;
use App\Helpers\Helper;
use App\Models\Master\MstLeadSource;
use App\Models\Master\MstSubscriptionPlan;
use App\Models\Subscription\Subscription;
use App\Models\Outbound\LeadCall;
use Carbon\Carbon;
class MoverInformation extends Controller
{
    /**
     * @return Response
     */
    public function getleadrouteinfobyphonesource(){
        $error = "";
        $status = "FAILURE";
        $match_movers = array();
        $images_url = Helper::checkServer()['images_url'];
        try {
            $elements = json_decode(file_get_contents('php://input'), true);
            if(!$elements['source']){
                throw new Exception("Invalid Source.");
            }
            
            $phone = preg_replace("/\D/", "", trim($elements['phone']));
            $invalid = strlen($phone) != 10  || preg_match('/^1/', $phone) || preg_match('/^.11/', $phone) || preg_match('/^...1/', $phone) || preg_match('/^....11/', $phone) || preg_match('/^.9/', $phone);
            if ($invalid == true) {
                $phone = "";
            }
           
            if (filter_var($phone, FILTER_VALIDATE_REGEXP, array("options" => ["regexp" => "/[1-9]{1}[0-9]{9}/"])) === false) {
                throw new Exception("Invalid phone.");
            } 
             
            $startDate = date("Y-m-d", strtotime('today - 30 days')) . ' 00:00:00';
            $endDate = date("Y-m-d H:i:s");
          
            $source = $elements['source'].'%';
            $origins = MstLeadSource::where('lead_source_name','LIKE',$source)->pluck('lead_source_id');
            $match_leads = Lead::where('phone',$phone)->whereIn('lead_source_id',$origins)->whereBetween('created_at', [$startDate, $endDate])->orderBy('lead_id', 'DESC')->take(1)->pluck('lead_id')->toArray();
            //    echo "<pre>";
            //     print_r($match_leads);
            //     die;
            if(count($match_leads) > 0 ){ 
                $campaignIds = LeadRouting::whereIn('lead_id',$match_leads)->where('route_status',"=", "sold")->groupBy('campaign_id')->pluck('campaign_id')->toArray();
                $businessIds = Campaign::whereIn('campaign_id',$campaignIds)->pluck('business_id')->toArray();
                $business_data = Business::whereIn('business_id', $businessIds)->where('buyer_type_id', '!=', 3)->get()->toArray();
                if(count($business_data) > 0){               
                    foreach ( $business_data as $business ) {                
                        if($business['logo'] != '' && file_exists(public_path().'/images/businesslogo/'.$business['logo'])){
                            $businesslogo = $images_url.'/images/businesslogo/'.$business['logo'];
                        }else{
                            $businesslogo = $images_url.'/images/businesslogo/movers.png';
                        }

                        $subscriptionPlan = Subscription::where('business_id', $business['business_id'])
                            ->where('is_active', 'yes')
                            ->first();
                        if($subscriptionPlan) {
                            $planDetails = MstSubscriptionPlan::where('subscription_plan_id', $subscriptionPlan->subscription_plan_id)->first();
                            if($planDetails) {
                                if($planDetails->subscription_plan_id == 2) {
                                    $callCountN = $planDetails->call_count;
                                    if($callCountN > 0) {
                                        $startOfMonth = Carbon::now()->startOfMonth();
                                        $endOfMonth = Carbon::now()->endOfMonth();
                                        $leadCallCount = LeadCall::where('transfer_type', 'businessrely')
                                        ->where('business_id', $business['business_id'])
                                        ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
                                        ->count();

                                        if($leadCallCount >= $callCountN) {
                                            $business['rely_number'] = '';
                                        }
                                    }
                                }
                            }
                        } else {
                            $business['rely_number'] = '';
                        }

                        $match_movers[ucwords(strtolower($business['display_name']))] = $business['rely_number'].'|'.$businesslogo;
                    }
                }
            }
           $status = "SUCCESS";
           
        }catch(Exception $e) {
            $error = $e->getMessage();
        }
        // echo "<pre>";
        // print_r($match_movers);
        // die;
        $response = array(
            'status'=> $status,
            'data'=> $match_movers,
            'error' => $error
        );

        return json_encode($response);
    }
}
