<?php

namespace App\Http\Controllers\LeadCallBuffer;

use App\Models\Lead\Lead;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Campaign\Campaign;
use App\Models\Campaign\CampaignLeadDialer;
use App\Models\Lead\LeadRouting;
use App\Models\Lead\LeadFollowUp;
use App\Models\Lead\LeadCallBuffer;
use App\Models\Outbound\LeadCall;
use App\Models\Outbound\LeadCallLog;
use DB;
use App\Models\Lead\LeadActivity;
use App\Models\Lead\Logs;
use App\Models\Master\MstCampaignType;
use App\Models\Master\MstTimeZone;
use DateTime;
use DateTimeZone;
use App\Models\CronJob\CronLog;
use App\Models\Master\MstLeadSource;
use App\Models\Master\MstCallConfig;
use App\Models\Business\Business;

class CallsbufferCronController extends Controller {

    function __construct() {
        //$this->middleware('permission:lead-list', ['only' => ['index']]);
    }
    public function followupToBufferSystemCronOld(Request $request) {
        date_default_timezone_set('America/New_York');

        $currentDate = date('Y-m-d H:i:s');
        $followupStart = date('Y-m-d H:i:s', strtotime($currentDate) - (5 * 60));
        
        $followup = LeadFollowUp::join("lead", "lead_follow_up.lead_id", "=", "lead.lead_id")->select("lead_follow_up.*", "lead.lead_type")->whereBetween("next_follow_datetime", [$followupStart, $currentDate])->whereIn('lead.lead_type', ["phone", "normal"])->orderBy('lead_follow_up.lead_follow_up_id', 'DESC')->get()->toArray();
 
        foreach ($followup as $followupLead) {
            if ($followupLead['lead_type'] == 'normal') {
                $saveBuffoers = $this->saveBufferLead($followupLead['lead_id']);
            } else {
                $saveBuffoers = $this->SaveToBufferViaPhoneonly($followupLead['lead_id']); 
            }
        }
        return 1;
    }
    public function followupToBufferSystemCron(Request $request) {
        $cronLog = new CronLog();
        $cronLog->cronlog(8, 'followupToBufferSystemCron start= ' . json_encode($request->all(), true), 'followuptocallbuffer');
        date_default_timezone_set('America/New_York');
        $leadactiviyobj = new LeadActivity();
       
        $currentDate = date('Y-m-d H:i:s');
        // $followupStart = date('Y-m-d H:i:s', strtotime($currentDate) - (5 * 60));
        $date = new DateTime();
        $date->modify('-24 hours');
        $followupStart = $date->format('Y-m-d H:i:s'); 
        $cronLog->cronlog(8, 'followupToBufferSystemCron currentDate= '. $currentDate .', followupStart= '. $followupStart, 'followuptocallbuffer');
       
        // $followup = LeadFollowUp::join("lead", "lead_follow_up.lead_id", "=", "lead.lead_id")->select("lead_follow_up.*", "lead.lead_type", "lead.email", "lead.lead_category_id", "lead.phone", "lead.lead_generated_at")->whereBetween("next_follow_datetime", [$followupStart, $currentDate])->whereIn('lead.lead_type', ["phone", "normal"])->where('lead.email', 'not like', "%test%")->where('lead.is_dnc_call', "no")->orderBy('lead_follow_up.lead_follow_up_id', 'DESC')->get()->toArray();

        $followup = LeadFollowUp::join("lead", "lead_follow_up.lead_id", "=", "lead.lead_id")->select("lead_follow_up.*", "lead.lead_type", "lead.email", "lead.lead_category_id", "lead.phone", "lead.lead_generated_at")->where("next_follow_datetime", "<=", $currentDate)->whereIn('lead.lead_type', ["phone", "normal"])->where('lead.is_dnc_call', "no")->orderBy('lead_follow_up.lead_follow_up_id', 'DESC')->get()->toArray();
       
        $cronLog->cronlog(8, 'followupToBufferSystemCron followupleads= ' . json_encode($followup, true), 'followuptocallbuffer');
        $callbufferArr = $leadData = $followleadIdArr = $leaddataArr = $leadIdArr = [];
        $currentdate = date('Y-m-d');
        foreach ($followup as $lead) {
            $followleadIdArr[] = $lead['lead_id'];
        }
        
        $leadData = Lead::with([ 'LeadMoving', 'LeadJunkRemoval', 'LeadCarTransport', 'LeadHeavyLifting'])->whereIn('lead_id', $followleadIdArr)->get()->toArray();
        foreach ($leadData as $lead ) {
            $leaddataArr[$lead['lead_id']] = $lead;
        }
        // echo '<pre>';print_r($leaddataArr);  die;
        foreach ($followup as $followupLead) {
            $exist_buffer = LeadCallBuffer::where('lead_id', $followupLead['lead_id'])->where('lead_category_id', $followupLead['lead_category_id'])->get()->first();
            $spotleft = 0;
            $bufferscore = 0;
            $failed_Attempt = 0;
            $leadtype = 1;
           
            $move_date = date('Y-m-d');
            $leadArr = $leaddataArr[$followupLead['lead_id']];
            // echo '<pre>';print_r($leadArr); 
            $ismovedatelarge = 0;
            if (count($leadArr) > 0 ) {
                $cronLog->cronlog(9, 'followupToBufferSystemCron leads found', 'followuptocallbuffer');
                if ($leadArr['lead_category_id'] == 1) {
                    $move_date = (isset($leadArr['lead_moving']['move_date'])) ? trim($leadArr['lead_moving']['move_date']) : ''; 
                } else if ($leadArr['lead_category_id'] == 2) {
                    $move_date = (isset($leadArr['lead_junk_removal']['junk_remove_date'])) ? trim($leadArr['lead_junk_removal']['junk_remove_date']) : '';
                } else if ($leadArr['lead_category_id'] == 3) {
                    $move_date = (isset($leadArr['lead_heavy_lifting']['lift_date'])) ? trim($leadArr['lead_heavy_lifting']['lift_date']) : '';
                } else if ($leadArr['lead_category_id'] == 4) {
                    $move_date = (isset($leadArr['lead_car_transport']['move_date'])) ? trim($leadArr['lead_car_transport']['move_date']) : '';
                }
              
                // if( $move_date >= $currentdate) {
                    $cronLog->cronlog(9, 'followupToBufferSystemCron movedate lead= ' . $followupLead['lead_id'] . ', move_date=' . $move_date, 'followuptocallbuffer');
                    $ismovedatelarge = 1;
                // }
            }

            if ($followupLead['lead_type'] == 'normal') { 
                $spotleft = 1;
                $bufferscore = 200;
                $failed_Attempt = 0;
            }else {
                $spotleft = 4;
                $bufferscore = 350;
                $failed_Attempt = 0;
                $leadtype = 2;
            }
            if (isset($exist_buffer)) {              
                $data_pushto_buffer = array(
                    'spot_left' => $spotleft,
                    'fail_attempt' => $bufferscore,
                    'call_buffer_score' => $failed_Attempt, 
                );
                if( $ismovedatelarge == 1 ){
                    $leadIdArr[] = $followupLead['lead_id'];
                    LeadCallBuffer::where('lead_call_buffer_id', $exist_buffer->lead_call_buffer_id)->update($data_pushto_buffer);
                    
                    $followupLeadData = LeadFollowUp::where('lead_id', $followupLead['lead_id'])->where('cust_phone_number', $followupLead['phone'])->first();
                    if(isset( $followupLeadData )){
                        $leadactiviyobj->createActivity($followupLead['lead_id'], 13);
                    }
                    LeadFollowUp::where('lead_id', $followupLead['lead_id'])->where('cust_phone_number', $followupLead['phone'])->delete();
                }
            } else {
                if( $ismovedatelarge == 1 ){
                    $leadIdArr[] = $followupLead['lead_id'];
                    $callbufferArr[] = [
                        "lead_id" => $followupLead['lead_id'],
                        "customer_number" => $followupLead['phone'],
                        "spot_left" => $spotleft,
                        "lead_type_id" => $leadtype,
                        "lead_date" => $followupLead['lead_generated_at'],
                        "fail_attempt" => $failed_Attempt,
                        "call_buffer_score" => $bufferscore,
                        "lead_category_id" => $followupLead['lead_category_id'],
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    $leadactiviyobj = new LeadActivity();
                    $followupLeadData = LeadFollowUp::where('lead_id', $followupLead['lead_id'])->where('cust_phone_number', $followupLead['phone'])->first();
                    if(isset( $followupLeadData )){
                        $leadactiviyobj->createActivity($followupLead['lead_id'], 13);
                    }               
                    $leadactiviyobj->createActivity($followupLead['lead_id'], 7);
                    LeadFollowUp::where('lead_id', $followupLead['lead_id'])->where('cust_phone_number', $followupLead['phone'])->delete();
                }
            } 
            // LeadFollowUp::where('lead_id', $followupLead['lead_id'])->where('cust_phone_number', $followupLead['phone'])->delete();
        }
        $callbufferArr = collect($callbufferArr); 
        foreach($callbufferArr->chunk(200) as $chunk){
            LeadCallBuffer::insert($chunk->toArray());
        }
        $cronLog->cronlog(8, 'followupToBufferSystemCron leadArr= ' . json_encode($leadIdArr, true), 'followuptocallbuffer');
        return response()->json($leadIdArr);
        
        // echo "<pre>";print_r($callbufferArr);die;
    }
    public function saveBufferLead($lead_id, $isoriginVMMA = 0) {
        $cronLog = new CronLog();
        //echo '<p>Buffer Lead';
        $currentdateTime = date('Y-m-d H:i');
        $currentdate = date('Y-m-d');
        $leadArr = Lead::with(['routingInfoLeadList.routingCampaignInfo', 'MstLeadSource', 'MstLeadCategory', 'LeadLandingPage', 'LeadFollowUp', 'LeadMoving', 'LeadJunkRemoval', 'LeadCarTransport', 'LeadHeavyLifting'])->where('lead.lead_type', "normal")->find($lead_id)->toArray();
        $cronLog->cronlog(9, 'saveBufferLead leads='.json_encode($leadArr, true), 'followuptocallbuffer');
        //echo '<pre>';print_r($leadArr);
        $fromState = $toState = $move_type = '';
        $move_date = date('Y-m-d');
        if ($leadArr) {
            $cronLog->cronlog(9, 'saveBufferLead leads found', 'followuptocallbuffer');
            if ($leadArr['lead_category_id'] == 1) {
                $move_type = (isset($leadArr['lead_moving']['move_type'])) ? trim($leadArr['lead_moving']['move_type']) : '';
                $move_date = (isset($leadArr['lead_moving']['move_date'])) ? trim($leadArr['lead_moving']['move_date']) : '';
                $fromState = (isset($leadArr['lead_moving']['from_state'])) ? trim($leadArr['lead_moving']['from_state']) : '';
                $toState = (isset($leadArr['lead_moving']['to_state'])) ? trim($leadArr['lead_moving']['to_state']) : '';
            } else if ($leadArr['lead_category_id'] == 2) {
                $move_type = 'local';
                $move_date = (isset($leadArr['lead_junk_removal']['junk_remove_date'])) ? trim($leadArr['lead_junk_removal']['junk_remove_date']) : '';
                $fromState = $toState = (isset($leadArr['lead_junk_removal']['from_state'])) ? trim($leadArr['lead_junk_removal']['from_state']) : '';
            } else if ($leadArr['lead_category_id'] == 3) {
                $move_type = (isset($leadArr['lead_heavy_lifting']['move_type'])) ? trim($leadArr['lead_heavy_lifting']['move_type']) : '';
                $move_date = (isset($leadArr['lead_heavy_lifting']['lift_date'])) ? trim($leadArr['lead_heavy_lifting']['lift_date']) : '';
                $fromState = (isset($leadArr['lead_heavy_lifting']['from_state'])) ? trim($leadArr['lead_heavy_lifting']['from_state']) : '';
                $toState = (isset($leadArr['lead_heavy_lifting']['to_state'])) ? trim($leadArr['lead_heavy_lifting']['to_state']) : '';
            } else if ($leadArr['lead_category_id'] == 4) {
                $move_type = (isset($leadArr['lead_car_transport']['move_type'])) ? trim($leadArr['lead_car_transport']['move_type']) : '';
                $move_date = (isset($leadArr['lead_car_transport']['move_date'])) ? trim($leadArr['lead_car_transport']['move_date']) : '';
                $fromState = (isset($leadArr['lead_car_transport']['from_state'])) ? trim($leadArr['lead_car_transport']['from_state']) : '';
                $toState = (isset($leadArr['lead_car_transport']['to_state'])) ? trim($leadArr['lead_car_transport']['to_state']) : '';
            }
            $pos = strpos($leadArr['email'], 'test');
            if ($leadArr['is_dnc_call'] == 'yes') {
                $cronLog->cronlog(9, 'saveBufferLead DNC Lead='. $leadArr['is_dnc_call'], 'followuptocallbuffer');
                $this->LeadRoutelog($lead_id, 'Lead DNC For Call...' . $leadArr['is_dnc_call'], 1);
              
                $callbufferlead = LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $leadArr['lead_category_id'])->first();
                if(isset( $callbufferlead )){
                    $leadactiviyobj = new LeadActivity();
                    $leadactiviyobj->createActivity($lead_id, 8);
                }
                LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $leadArr['lead_category_id'])->delete();
                return 0;
            }
            if ($pos !== false || $move_date < $currentdate) {
                $cronLog->cronlog(9, 'saveBufferLead Testlead or MoveDate Expired= ' . $move_date . '<' . $currentdate, 'followuptocallbuffer');
                $this->LeadRoutelog($lead_id, 'Test lead or Move Date Expired...' . $move_date . '<' . $currentdate, 1);
                return 1;
            }
            //exclusive dual lead on/off check
            $exclusiveDualSlotStatus = 0;
            $getExclusiveDualSlot = MstCallConfig::where('call_config', 'exclusivedualslot')->get(['status'])->toArray();
            if (count($getExclusiveDualSlot) > 0) {
                if (strtolower($getExclusiveDualSlot[0]['status']) == "active") {
                    $exclusiveDualSlotStatus = 1;
                }
            }
            // Start sport wise point given  //
            $sport_total = 4;
            $routingInfo = $score = 0;
            $lead_routing_exist_check = $campTypeArr = $campbusinessesArr = $buyerTypeArr = array();
            if (isset($leadArr['routing_info_lead_list'])) {
                foreach ($leadArr['routing_info_lead_list'] as $routekey => $routevalue) {
                    $lead_routing_exist_check[] = $routevalue['campaign_id'];
                }
                $routingInfo = count($leadArr['routing_info_lead_list']);
            }
            $cronLog->cronlog(9, 'saveBufferLead routingInfocount=' . $routingInfo, 'followuptocallbuffer');
            $premium_count = $exclusive_count = $dialer_count = $vmo_count = $dualslot_count = $trio_count = $custom_count = 0;
            $getCampaignData = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->get(['campaign_type_id','business_id'])->toArray();
            $cronLog->cronlog(9, 'saveBufferLead getCampaignData=' . json_encode($getCampaignData), 'followuptocallbuffer');
            $getMasterCampaignType = MstCampaignType::get()->toArray();
            for ($mc = 0; $mc < count($getMasterCampaignType); $mc++) {
                $campTypeArr[$getMasterCampaignType[$mc]['campaign_type_id']] = $getMasterCampaignType[$mc]['campaign_type'];
            }
            $cronLog->cronlog(9, 'saveBufferLead campTypeArr='. json_encode($campTypeArr), 'followuptocallbuffer');
           
            for ($c = 0; $c < count($getCampaignData); $c++) {
                $campbusinessesArr[] = $getCampaignData[$c]['business_id'];
            }
            $businesses = Business::whereIn("business_id", $campbusinessesArr)->get(['buyer_type_id',"business_id"])->toArray();
            for ($c = 0; $c < count($businesses); $c++) {
                $buyerTypeArr[$businesses[$c]['business_id']] = $businesses[$c]['buyer_type_id'];
            }
            for ($c = 0; $c < count($getCampaignData); $c++) {
                if (isset($campTypeArr[$getCampaignData[$c]['campaign_type_id']])) {
                    $campaign_type = $campTypeArr[$getCampaignData[$c]['campaign_type_id']];
                    if (strpos(strtolower($campaign_type), 'premium') !== false) {
                        $premium_count += 1;
                    } else if (strpos(strtolower($campaign_type), 'exclusive') !== false) {
                        $exclusive_count += 1;
                    } else if (strpos(strtolower($campaign_type), 'dual') !== false) {
                        $dualslot_count += 1;
                    } else if (strpos(strtolower($campaign_type), 'trio') !== false) {
                        $trio_count += 1;
                    } else if (strpos(strtolower($campaign_type), 'custom') !== false) {
                        $custom_count += 1;
                    } else if (strpos(strtolower($campaign_type), 'dialer') !== false) {
                        $dialer_count += 1;
                    } else if (strpos(strtolower($campaign_type), 'organic') !== false) {
                        $vmo_count += 1;
                    }
                }
                /*if($buyerTypeArr[$getCampaignData[$c]['business_id']] == 3 && $leadArr['sold_type'] == 'exclusive'){
                    return 0; 
                }*/
            }

            $this->LeadRoutelog($lead_id, 'Routed Campaign Count...Premium=' . $premium_count . ',Exclusive=' . $exclusive_count . ',Dual=' . $dualslot_count . ',Dialer=' . $dialer_count . ',VM Organic=' . $vmo_count, 1);
            $cronLog->cronlog(9, 'saveBufferLead Routed Campaign Count...Premium=' . $premium_count . ',Exclusive=' . $exclusive_count . ',Dual=' . $dualslot_count . ',Dialer=' . $dialer_count . ',VM Organic=' . $vmo_count, 'followuptocallbuffer');
            //Dual Slot Changes
            if ($trio_count > 0) {
                $sport_total = 3;
            }
            if ($dualslot_count > 0) {
                $sport_total = 2;
            }
            if ($exclusive_count > 0) {
                $sport_total = 1;
            }
            $sport_remaining = $sport_total - ($routingInfo - $dialer_count);
            if ($custom_count > 0 && $routingInfo > 0) {
                $sport_remaining = 0;
            }
            $cronLog->cronlog(9, 'saveBufferLead sport_remaining=' . $vmo_count."==sport_remaining==".$sport_remaining, 'followuptocallbuffer');
            if (isset($leadArr['routing_info_lead_list'])) {
                if (count($lead_routing_exist_check) > 0) {
                    if ($premium_count > 0 || $dualslot_count > 0) {
                        $lead_dialer = 1;
                    }
                    if ($vmo_count > 0 || $exclusive_count > 0) {
                        $lead_dialer = 2;
                    }
                }
                if ($sport_remaining == 4) {
                    $score += 60;
                } else if ($sport_remaining == 3) {
                    $score += 40;
                    $exclusiveLeadDialer = 0;
                    if ($lead_dialer == 2) {
                        $score += 10;
                        $leadDialerCount = CampaignLeadDialer::whereIn('campaign_id', $lead_routing_exist_check)->count();
                        if ($leadDialerCount > 0) {
                            $exclusiveLeadDialer = 1;
                        }
                        if ($exclusiveLeadDialer == 0) {
                            $lead_comeing_time = strtotime(substr($leadArr['lead_generated_at'], 0, strlen($leadArr['lead_generated_at']) - 3));
                            $timestamp2 = strtotime($currentdateTime);
                            $difference = abs($timestamp2 - $lead_comeing_time) / (60 * 60);
                            if ($difference < 24) {
                                $this->saveFollowupentry($leadArr, 24);
                                return 0;
                            }
                        }
                    }
                } else if ($sport_remaining == 2) {
                    $lead_comeing_time = strtotime(substr($leadArr['lead_generated_at'], 0, strlen($leadArr['lead_generated_at']) - 3));
                    $timestamp2 = strtotime($currentdateTime);
                    $difference = abs($timestamp2 - $lead_comeing_time) / (60 * 60);
                    if ($difference < 2) {
                        
                        $callbufferlead = LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $leadArr['lead_category_id'])->first();
                        if(isset( $callbufferlead )){ 
                            $leadactiviyobj = new LeadActivity();
                            $leadactiviyobj->createActivity($lead_id, 8);
                        }
                        LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $leadArr['lead_category_id'])->delete();
                        /*$this->saveFollowupentry($leadArr, 2);
                        return 0;*/
                    }
                    $score += 20;
                } else if ($sport_remaining == 1) {
                    $hourCount = 2;
                    if ($dualslot_count > 0) {
                        $hourCount = 1;
                    }
                    $lead_comeing_time = strtotime(substr($leadArr['lead_generated_at'], 0, strlen($leadArr['lead_generated_at']) - 3));
                    $timestamp2 = strtotime($currentdateTime);
                    $difference = abs($timestamp2 - $lead_comeing_time) / (60 * 60);

                    if ($difference < $hourCount) {

                        $leadactiviyobj = new LeadActivity();
                        $callbufferlead = LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $leadArr['lead_category_id'])->first();
                        if(isset( $callbufferlead )){
                            $leadactiviyobj->createActivity($lead_id, 8);
                            $this->saveFollowupentry($leadArr, $hourCount);
                        }
                        LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $leadArr['lead_category_id'])->delete();
                        //exclusive dual lead on/off check
                        /*if ($exclusiveDualSlotStatus == 0) {
                            return 0;
                        }*/
                    }
                    $score += 10;
                } else if ($sport_remaining == 0) {
                    //Dual Slot here
                    $callbufferlead = LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $leadArr['lead_category_id'])->first();
                    if(isset( $callbufferlead )){
                        $leadactiviyobj = new LeadActivity();
                        $leadactiviyobj->createActivity($lead_id, 8);
                    }
                    LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $leadArr['lead_category_id'])->delete();
                    $lead_comeing_time = strtotime(substr($leadArr['lead_generated_at'], 0, strlen($leadArr['lead_generated_at']) - 3));
                    $timestamp2 = strtotime($currentdateTime);
                    $difference = abs($timestamp2 - $lead_comeing_time) / (60 * 60);
                    $hourCount = 24;

                    if ($difference < $hourCount) {
                        if ($exclusive_count > 0) {
                            $timeZoneDetail = MstTimeZone::where('timezone_id', $leadArr['timezone_id'])->get()->toArray();
                            date_default_timezone_set($timeZoneDetail[0]['timezone']);
                            $hoursCheck = date('G', time());
                            if ($hoursCheck >= 8 && $hoursCheck <= 19) {

                            } else {
                                $this->saveFollowupentry($leadArr, $hourCount);
                                return 0;
                            }
                        } else {
                            $this->saveFollowupentry($leadArr, 2);
                            return 0;
                        }
                    }
                } else {
                    $callbufferlead = LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $leadArr['lead_category_id'])->first();
                    if(isset( $callbufferlead )){
                        $leadactiviyobj = new LeadActivity();
                        $leadactiviyobj->createActivity($lead_id, 8);
                    }
                    LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $leadArr['lead_category_id'])->delete();
                    $lead_comeing_time = strtotime(substr($leadArr['lead_generated_at'], 0, strlen($leadArr['lead_generated_at']) - 3));
                    $timestamp2 = strtotime($currentdateTime);
                    $difference = abs($timestamp2 - $lead_comeing_time) / (60 * 60);
                    if ($difference < 24) {
                        $this->saveFollowupentry($leadArr, 2);
                        return 0;
                    } else {
                        return 1;
                    }
                }
            } else {
                $score += 60;
            }
            $mint_before3 = date('Y-m-d H:i:s', strtotime('-3 minute'));
            $lead_comeing_time = strtotime($leadArr['lead_generated_at']);
            $timestamp2 = strtotime($mint_before3);
            $difference = abs($timestamp2 - $lead_comeing_time) / (60 * 60);
            if ($difference < 2) {
                $score += 40;
            } else if ($difference < 24) {
                $score += 20;
            } else if ($difference < 48) {
                $score += 10;
            }

            $failed_Attempt = LeadCall::where('lead_id', $lead_id)->where('call_type', 'outbound')->whereIn('call_disposition_id', [1, 5, 7])->where('call_datetime', '>=', $leadArr['lead_generated_at'])->orderBy('lead_call_id', 'DESC')->count();

            if ($failed_Attempt == 0) {
                $score += 70;
            } else if ($failed_Attempt == 1) {
                $score += 30;
            } else if ($failed_Attempt == 2) {
                $score += 20;
            } else if ($failed_Attempt == 3) {
                $score += 10;
            } else {
                $score += 5;
            }
            if ($isoriginVMMA == 1) {
                $score = 180;
            }
            $exist_buffer1 = LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $leadArr['lead_category_id'])->get()->first();
            if (isset($exist_buffer1)) {
                $exist_buffer = $exist_buffer1->lead_call_buffer_id;
                $lead_category_id = $exist_buffer1->lead_category_id;
                $data_pushto_buffer = array(
                    'spot_left' => $sport_remaining,
                    'fail_attempt' => $failed_Attempt,
                    'call_buffer_score' => $score + 180,
                    'lead_category_id' => $lead_category_id
                );
                LeadCallBuffer::where('lead_call_buffer_id', $exist_buffer)->update($data_pushto_buffer);
            } else {
                $coverageType = 0;
                if (strcmp($fromState, $toState)) {
                    $coverageType = 1;
                }
                $campaignData = DB::table('campaign')->join('business', 'business.business_id', '=', 'campaign.business_id')->select('campaign.campaign_type_id', 'campaign.lead_type_id', 'campaign.campaign_id', 'business.business_id')->where('business.status', 'active')->get()->toArray();
                //echo "<pre>";print_r($campaignData);die;
                $callCampaignIds = array();
                foreach ($campaignData as $keyP => $valueP) {
                    if ($valueP->lead_type_id == 2) {
                        $callCampaignIds[] = $valueP->campaign_id;
                    }
                }
                //echo "<pre>";print_r($callCampaignIds);die;
                $leadCall = new LeadCall();
                // $tempArr = $leadCall->getCampaignCheckFromZip($callCampaignIds, $lead_id);
                //echo "<pre>";print_r($tempArr);die;
                $inserCallBuffer = 0;
                // $this->LeadRoutelog($lead_id, 'last callbuffer table entry of call' . json_encode($tempArr), 6);
                // if (count($tempArr) > 0) {
                    $inserCallBuffer = 1;
                // }
                // Get EST hour
                $estHoursCheck = date('G');
                if ($estHoursCheck >= 11 && $estHoursCheck < 19 && ($sport_remaining == 4 || $sport_remaining == 3)) { //11 AM EST, 7 PM EST
                    // Get TimeZone Wise
                    $lead_comeing_time = strtotime($this->getLeadTimeZone($leadArr, 0));
                }

                if ($inserCallBuffer > 0) {
                    //Added by BK on 20/02/2025 get sorce name based on lead wise as per discussion with MA
                    $sourceName = MstLeadSource::where('lead_source_id', $leadArr['lead_source_id'])->first();
                    //check isDuplicate Leads
                    if (strpos($sourceName->lead_source_name, 'VM') !== false) {
                        $sourceName = 'VM';
                    } else if (strpos($sourceName->lead_source_name, 'QTM') !== false) {
                        $sourceName = 'QTM';
                    } else if (strpos($sourceName->lead_source_name, 'IS') !== false) {
                        $sourceName = 'IS';
                    }
                    $sourceIdArr = MstLeadSource::where('lead_parent_source', $sourceName)->pluck('lead_source_id')->toArray();
                    $leadIdArr = Lead::whereIn('lead_source_id', $sourceIdArr)->where('phone', $leadArr['phone'])->pluck('lead_id')->toArray();

                    $checkOldPhone = LeadCallBuffer::where('customer_number', $leadArr['phone'])->where('lead_category_id', $leadArr['lead_category_id'])->whereIn('lead_id', $leadIdArr)->get()->toArray();
                    if (count($checkOldPhone) > 0) {
                        $this->LeadRoutelog($lead_id, 'Tweet Lead Already exists in buffer of this number=' . json_encode($checkOldPhone), 6);
                        return 1;
                    }
                    date_default_timezone_set('America/New_York');
                    //Dual Slot Changes
                    $score = $score + 170;
                    if ($dualslot_count > 0 && $exclusiveDualSlotStatus > 0) {
                        $score = 400;
                    }
                    //For ISM Added by BK on 16/07/2024
                    $pos1 = strpos($leadArr['mst_lead_source']['lead_source_name'], 'VM');
                    $pos2 = strpos($leadArr['mst_lead_source']['lead_source_name'], 'QTM');
                    $pos3 = strpos($leadArr['mst_lead_source']['lead_source_name'], 'IS');
                    $weekDayNumber = date('N');
                    $currentDateTime = date('Y-m-d H:i:s');
                    $startDateTime = date("Y-m-d") . ' 08:00:00';
                    $endDateTime = date("Y-m-d") . ' 22:00:00';
                    $configDetail = MstCallConfig::whereIn('call_config', ['vmlongroutelater', 'qtmlongroutelater', 'islongroutelater', 'vmlocalroutelater', 'qtmlocalroutelater', 'islocalroutelater'])->get(['status'])->toArray();
                    if ((strtotime($currentDateTime) >= strtotime($startDateTime) && strtotime($currentDateTime) < strtotime($endDateTime) && $pos1 !== false && $weekDayNumber < 6 && strtolower($configDetail[0]['status']) == "active" && $move_type == "long")
                        || (strtotime($currentDateTime) >= strtotime($startDateTime) && strtotime($currentDateTime) < strtotime($endDateTime) && $pos2 !== false && $weekDayNumber < 6 && strtolower($configDetail[1]['status']) == "active" && $move_type == "long")
                        || (strtotime($currentDateTime) >= strtotime($startDateTime) && strtotime($currentDateTime) < strtotime($endDateTime) && $pos3 !== false && $weekDayNumber < 6 && strtolower($configDetail[2]['status']) == "active" && $move_type == "long")
                        || (strtotime($currentDateTime) >= strtotime($startDateTime) && strtotime($currentDateTime) < strtotime($endDateTime) && $pos1 !== false && $weekDayNumber < 6 && strtolower($configDetail[3]['status']) == "active" && $move_type == "local")
                        || (strtotime($currentDateTime) >= strtotime($startDateTime) && strtotime($currentDateTime) < strtotime($endDateTime) && $pos2 !== false && $weekDayNumber < 6 && strtolower($configDetail[4]['status']) == "active" && $move_type == "local")
                        || (strtotime($currentDateTime) >= strtotime($startDateTime) && strtotime($currentDateTime) < strtotime($endDateTime) && $pos3 !== false && $weekDayNumber < 6 && strtolower($configDetail[5]['status']) == "active" && $move_type == "local")) {
                        $score = 400;
                    }
                    $data_pushto_buffer = array(
                        'lead_id' => $lead_id,
                        'customer_number' => $leadArr['phone'],
                        'lead_type_id' => 2,
                        'spot_left' => $sport_remaining,
                        'lead_date' => date('Y-m-d H:i:s', $lead_comeing_time),
                        'fail_attempt' => $failed_Attempt,
                        'call_buffer_score' => $score,
                        'lead_category_id' => $leadArr['lead_category_id'],
                        'created_at' => date('Y-m-d H:i:s')
                    );
                    $buffertableid = LeadCallBuffer::create($data_pushto_buffer)->lead_call_buffer_id;
                    $leadactiviyobj = new LeadActivity();
                    $leadactiviyobj->createActivity($lead_id, 7);
                    //echo '<p>Call Buffer ID: '.$buffertableid;
                    $followupdata = LeadFollowUp::where('lead_id', $lead_id)->first();
                    if(isset( $followupdata )){
                        $leadactiviyobj = new LeadActivity();
                        $leadactiviyobj->createActivity($lead_id, 13);
                    }
                    LeadFollowUp::where('lead_id', $lead_id)->delete();
                } else {
                    $this->saveFollowupentry($leadArr, 2);
                }
            }
        } else {
            $this->LeadRoutelog($lead_id, 'Lead Data not found', 1);
        }
        //echo '<pre>';print_r($leadArr);
        return 1;
    }

    public function SaveToBufferViaPhoneonly($lead_id, $isMissedCallLead = '',$leadType='') {
        $cronLog = new CronLog();
        //echo '<p>Buffer Phone Lead';
        $currentdateTime = date('Y-m-d H:i');
        $currentdate = date('Y-m-d');
        if($leadType == "1") {
            $phoneonly = Lead::with(['routingInfoLeadList.routingCampaignInfo', 'MstLeadSource', 'MstLeadCategory', 'LeadLandingPage', 'LeadFollowUp', 'LeadMoving', 'LeadJunkRemoval', 'LeadCarTransport', 'LeadHeavyLifting'])->whereIn('lead.lead_type', ["phone","normal"])->find($lead_id);    
        } else {
            $phoneonly = Lead::with(['routingInfoLeadList.routingCampaignInfo', 'MstLeadSource', 'MstLeadCategory', 'LeadLandingPage', 'LeadFollowUp', 'LeadMoving', 'LeadJunkRemoval', 'LeadCarTransport', 'LeadHeavyLifting'])->where('lead.lead_type', "phone")->find($lead_id);    
        }
        
        //echo '<pre>';print_r($phoneonly);
        $this->LeadRoutelog($lead_id, 'Phone Lead SaveToBufferViaPhoneonly data=' . json_encode($phoneonly), 1);
        if ($phoneonly) {
            $lead_category = $phoneonly->lead_category_id;
            $exist_buffer1 = LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $lead_category)->first();
            if ($phoneonly->is_dnc_call == 'yes') {
                if(isset( $exist_buffer1 )){
                    $leadactiviyobj = new LeadActivity();
                    $leadactiviyobj->createActivity($lead_id, 8);
                }
                LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $lead_category)->delete();
                return 1;
            }

            $failed_Attempt = LeadCall::where('lead_id', $lead_id)->where('call_type', 'outbound')->whereIn('call_disposition_id', [1, 5, 7])->where('call_datetime', '>=', $phoneonly->lead_generated_at)->orderBy('lead_call_id', 'DESC')->count();

            if (isset($exist_buffer1)) {
                $exist_buffer = $exist_buffer1->lead_call_buffer_id;
                $data_pushto_buffer = array(
                    'lead_type_id' => 2,
                    'spot_left' => 4,
                    'fail_attempt' => $failed_Attempt ? $failed_Attempt : 0,
                    'call_buffer_score' => ($isMissedCallLead == '1') ? '180' : '180', // Added By HJ On 21-05-2020
                    'lead_category_id' => $lead_category
                );
                LeadCallBuffer::where('lead_call_buffer_id', $exist_buffer)->update($data_pushto_buffer);
                if ($failed_Attempt >= 5) {
                    $leadactiviyobj = new LeadActivity();
                    $leadactiviyobj->createActivity($lead_id, 8);
                    LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $lead_category)->delete();
                }
            } else {
                if ($failed_Attempt >= 5) { 
                    $callbufferlead = LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $lead_category)->first();
                    if(isset( $callbufferlead )){
                        $leadactiviyobj = new LeadActivity();
                        $leadactiviyobj->createActivity($lead_id, 8);
                    }
                    LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $lead_category)->delete();
                } else {
                    //Added by BK on 20/02/2025 get sorce name based on lead wise as per discussion with MA
                    $sourceName = MstLeadSource::where('lead_source_id', $phoneonly->lead_source_id)->first();
                    //check isDuplicate Leads
                    if (strpos($sourceName->lead_source_name, 'VM') !== false) {
                        $sourceName = 'VM';
                    } else if (strpos($sourceName->lead_source_name, 'QTM') !== false) {
                        $sourceName = 'QTM';
                    } else if (strpos($sourceName->lead_source_name, 'IS') !== false) {
                        $sourceName = 'IS';
                    }
                    $sourceIdArr = MstLeadSource::where('lead_parent_source', $sourceName)->pluck('lead_source_id')->toArray();
                    $leadIdArr = Lead::whereIn('lead_source_id', $sourceIdArr)->where('phone', $phoneonly->phone)->pluck('lead_id')->toArray();

                    $checkOldPhone = LeadCallBuffer::where('customer_number', $phoneonly->phone)->where('lead_category_id', $lead_category)->whereIn('lead_id', $leadIdArr)->get()->toArray();
                    if (count($checkOldPhone) > 0) {
                        $this->LeadRoutelog($lead_id, 'Phone Lead Already exists in buffer of this number=' . json_encode($checkOldPhone), 6);
                        return 1;
                    }
                    $splitdate = $phoneonly->lead_generated_at;
                    date_default_timezone_set('America/New_York');
                    $data_pushto_buffer = array(
                        'lead_id' => $phoneonly->lead_id,
                        'customer_number' => $phoneonly->phone,
                        'lead_type_id' => 2,
                        'spot_left' => 4,
                        'lead_date' => $splitdate,
                        'fail_attempt' => $failed_Attempt ? $failed_Attempt : 0,
                        'call_buffer_score' => ($isMissedCallLead == '1') ? '350' : '350', // Added By HJ On 21-05-2020
                        'lead_category_id' => $lead_category,
                        'created_at' => date('Y-m-d H:i:s')
                    );
                    $buffertableid = LeadCallBuffer::create($data_pushto_buffer)->lead_call_buffer_id;
                    $leadactiviyobj = new LeadActivity();
                    $leadactiviyobj->createActivity($phoneonly->lead_id, 7);
                    //echo '<p>Phone Call Buffer ID: '.$buffertableid;
                    $followupData = LeadFollowUp::where('lead_id', $phoneonly->lead_id)->first();
                    if(isset( $followupData )){
                        $leadactiviyobj = new LeadActivity();
                        $leadactiviyobj->createActivity($phoneonly->lead_id, 13);
                    }
                    LeadFollowUp::where('lead_id', $phoneonly->lead_id)->delete();
                }
            }
        }
        //echo '<pre>';print_r($phoneonly);
        return 1;
    }

    public function updateBufferSystemScoreCron() {
        $cronLog = new CronLog();
        $buffers = LeadCallBuffer::with('Lead', 'Lead.routingInfoWithCampaign')->orderby('lead_call_buffer_id', 'desc')->get();
        // echo '<pre>';print_r($buffers);die; 
        //  $buffers = LeadCallBuffer::with('Lead', 'Lead.routingInfo')->orderby('lead_call_buffer_id', 'desc')->get();
        if (isset($buffers)) {
            foreach ($buffers as $bufferkey => $buffervalue) {
                $premium_count = $exclusive_count = $dialer_count = $vmo_count = $dualslot_count = 0;
                $callBufferId = $buffervalue->lead_call_buffer_id;
                $call_buffer_score = $buffervalue->call_buffer_score;
                $callBufferLeadId = $buffervalue->lead_id;
                $lead_type = $buffervalue->Lead->lead_type;
                //echo '<p>'.$callBufferLeadId;
                $lead_generated_at = date('Y-m-d H:i:s');
                if (isset($buffervalue->Lead->lead_generated_at)) {
                    $lead_generated_at = $buffervalue->Lead->lead_generated_at;
                }

                $score = 170;
                $current = date('Y-m-d H:i:s');
                $sport_remaining = $buffervalue->spot_left;
                $lead_comeing_time = strtotime($buffervalue->lead_date);
                $timestamp2 = strtotime($current);
                $failed_Attempt = $buffervalue->fail_attempt;
                $lead_category_id = $buffervalue->lead_category_id;
                // $routedCompany = 1;
                // if ($routedCompany > 0) {
                    $finalRouting = $buffervalue->lead->routingInfoWithCampaign;

                    $failed_Attempt = $this->failAttempt($callBufferLeadId, $lead_generated_at);
                    $getCampaignData = [];

                    if ($buffervalue->lead->routingInfoWithCampaign) {
                        $getCampaignData = $buffervalue->lead->routingInfoWithCampaign;
                    }

                    for ($c = 0; $c < count($getCampaignData); $c++) {
                        if ($getCampaignData[$c]->routingCampaignInfo->campaign_type_id == 1) {
                            $premium_count += 1;
                        } else if ($getCampaignData[$c]->routingCampaignInfo->campaign_type_id == 2) {
                            $exclusive_count += 1;
                        } else if ($getCampaignData[$c]->routingCampaignInfo->campaign_type_id == 3) {
                            $dialer_count += 1;
                        } else if ($getCampaignData[$c]->routingCampaignInfo->campaign_type_id == 4) {
                            $vmo_count += 1;
                        } else if ($getCampaignData[$c]->routingCampaignInfo->campaign_type_id == 6) {
                            $dualslot_count += 1;
                        }
                    }
                    $sport_total = 4;
                    //Dual Slot Changes
                    if ($dualslot_count > 0) {
                        $sport_total = 2;
                    }
                    if ($exclusive_count > 0) {
                        $sport_total = 1;
                    }
                    $sport_remaining = $sport_total - (count($finalRouting) - $dialer_count);
                    if($sport_remaining < 0){
                        $sport_remaining = 0;
                    }
                    $cronLog->cronlog(1, 'updateBufferSystemScoreCron lead id=' . $callBufferLeadId . ', routing='. count($finalRouting) . ',sport_total='.$sport_total.",dialer_count=".$dialer_count.",sport_remaining=".$sport_remaining, 'updateBufferSystemScoreCron');
                    $updateBufferData = array();
                    $updateBufferData['spot_left'] = $sport_remaining;
                    // $buffervalue->spot_left = $sport_remaining;
                    if ($sport_remaining == 4) {
                        $score += 60;
                    } else if ($sport_remaining == 3) {
                        if ($exclusive_count > 0) {
                            $score += 10;
                        }
                        $score += 40;
                    } else if ($sport_remaining == 2) {
                        if ($exclusive_count > 0) {
                            $score += 10;
                        }
                        $score += 20;
                    } else if ($sport_remaining == 1) {
                        if ($exclusive_count > 0) {
                            $score += 10;
                        }
                        $score += 10;
                    }

                    $difference = abs($timestamp2 - $lead_comeing_time) / (60 * 60);
                    if ($difference < 2) {
                        $score += 40;
                    } else if ($difference < 24) {
                        $score += 20;
                    } else if ($difference < 48) {
                        $score += 10;
                    }

                    if ($failed_Attempt == 0) {
                        $score += 70;
                    } else if ($failed_Attempt == 1) {
                        $score += 30;
                    } else if ($failed_Attempt == 2) {
                        $score += 20;
                    } else if ($failed_Attempt == 3) {
                        $score += 10;
                    } else if ($failed_Attempt == 4) {
                        $score += 5;
                    }
                    if ($lead_type == "normal" && $call_buffer_score < 400) {
                        // $buffervalue->call_buffer_score = $score;
                        $updateBufferData['call_buffer_score'] = $score;
                    }
                    $updateBufferData['fail_attempt'] = $failed_Attempt;
                    LeadCallBuffer::where('lead_call_buffer_id', $callBufferId)->where('lead_category_id', $lead_category_id)->update($updateBufferData);
                    // $buffervalue->fail_attempt = $failed_Attempt;
                    // $buffervalue->save();
                    
                // } else {
                //     $leadvalue = $buffervalue->lead;
                //     LeadCallBuffer::where('lead_id', $callBufferLeadId)->where('lead_category_id', $lead_category_id)->delete();
                //     $leadactiviyobj = new LeadActivity();
                //     $leadactiviyobj->createActivity($callBufferLeadId, 8);
                //     $this->saveFollowupentry($leadvalue, 2);
                // }
            }
        }
        return '1';
    }

    public function failAttempt($callBufferLeadId, $lead_generated_at) {
        $cronLog = new CronLog();
        return LeadCall::where('lead_id', $callBufferLeadId)->whereIn('call_disposition_id', [5,6,7])->where('call_datetime', '>=', $lead_generated_at)->orderBy('lead_call_id', 'DESC')->count();
    }

    public function deleteBufferSystemEntryCron() {
        $cronLog = new CronLog();
        $cronLog->cronlog(10, 'deleteBufferSystemEntryCron start', 'deletecallbuffercron');
        
        $currentdate = date('Y-m-d');
        $datetime = date("Y-m-d H:i:s");
        $time = strtotime($datetime) - (144 * 60 * 60);
        $datetime = date("Y-m-d H:i:s", $time);
        $buffersdeletes = LeadCallBuffer::where('lead_date', '<', $datetime)->orWhere('fail_attempt', '>=', 5)->pluck( 'lead_id' )->toArray();
        $leadactiviyobj = new LeadActivity();
        if(count( $buffersdeletes ) > 0){
            foreach ($buffersdeletes as $key => $value) {
                $leadactiviyobj->createActivity($value, 8);
            }
        }
        LeadCallBuffer::where('lead_date', '<', $datetime)->orWhere('fail_attempt', '>=', 5)->delete();
        $cronLog->cronlog(10, 'deleteBufferSystemEntryCron currentdate=' . $currentdate . ', datetime='. $datetime, 'deletecallbuffercron');
        $origins = MstLeadSource::get()->toArray();
        $originArr = [];
        foreach ($origins as $origin => $originvalue) {
            $originArr[$originvalue['lead_source_id']] = $originvalue;            
        }
        $cronLog->cronlog(10, 'deleteBufferSystemEntryCron originArr=' . json_encode($originArr, true), 'deletecallbuffercron');
        $buffers = LeadCallBuffer::with(['Lead', "Lead.LeadMoving", "Lead.LeadJunkRemoval", "Lead.LeadHeavyLifting", "Lead.LeadCarTransport"])->orderBy('lead_call_buffer_id', 'desc')->get()->toArray();
        $cronLog->cronlog(10, 'deleteBufferSystemEntryCron buffersdata=' . json_encode($buffers, true), 'deletecallbuffercron');
        $bufferArr = [];
        if (count($buffers) > 0) {
          
            $current = strtotime(date('Y-m-d H:i:s'));
            foreach ($buffers as $bufferkey => $buffervalue) {
                
                $move_date = date('Y-m-d');
                if ($buffervalue['lead']['lead_category_id'] == 1) {
                    if (isset($buffervalue['lead']['lead_moving']['move_date'])) {
                        $move_date = $buffervalue['lead']['lead_moving']['move_date'];
                    }
                } else if ($buffervalue['lead']['lead_category_id'] == 2) {
                    if (isset($buffervalue['lead']['lead_junk_removal']['junk_remove_date'])) {
                        $move_date = $buffervalue['lead']['lead_junk_removal']['junk_remove_date'];
                    }
                } else if ($buffervalue['lead']['lead_category_id'] == 3) {
                    if (isset($buffervalue['lead']['lead_heavy_lifting']['lift_date'])) {
                        $move_date = $buffervalue['lead']['lead_heavy_lifting']['lift_date'];
                    }
                } else if ($buffervalue['lead']['lead_category_id'] == 4) {
                    if (isset($buffervalue['lead']['lead_car_transport']['move_date'])) {
                        $move_date = $buffervalue['lead']['lead_car_transport']['move_date'];
                    }
                }
                // echo "<pre> leadid = ". $buffervalue['lead']['lead_id'] . ", movedat = ". $move_date . " / "; 
                $originData = $originArr[$buffervalue['lead']['lead_source_id']];
                $pos1 = strpos(strtolower($originData['lead_source_name']), 'test'); 
                
                if (($buffervalue['lead']['is_dnc_call'] == 'yes') || ($move_date < $currentdate) || ($pos1 !== false) ) {
                    $bufferArr[] = $buffervalue['lead_call_buffer_id'];
                    $leadactiviyobj->createActivity($buffervalue['lead']['lead_id'], 8);
                } 
            }
            $cronLog->cronlog(10, 'deleteBufferSystemEntryCron LeadCallBuffer=' . json_encode($bufferArr, true), 'deletecallbuffercron');
            LeadCallBuffer::destroy($bufferArr);
        }
        // echo "<pre>";print_r($bufferArr ); die;
        return response()->json($bufferArr);
    }
    public function deleteFollowupCron() {
        $cronLog = new CronLog();
        $currentdate = date('Y-m-d'); 
        $cronLog->cronlog(11, 'deleteFollowupCron start=' . $currentdate, 'deletefollowupcron');        
         
        $origins = MstLeadSource::get()->toArray();
        $originArr = $followupArr = $leadidArr = [];
        foreach ($origins as $origin => $originvalue) {
            $originArr[$originvalue['lead_source_id']] = $originvalue;            
        }
        $cronLog->cronlog(11, 'deleteFollowupCron originArr=' . json_encode($originArr, true), 'deletefollowupcron');
        
        $followup = LeadFollowUp::with(['Lead'])->orderby('lead_follow_up_id', 'desc')->get()->toArray();
        $cronLog->cronlog(11, 'deleteFollowupCron followupdata=' . json_encode($followup, true), 'deletefollowupcron');
        if (count($followup) > 0) { 
            foreach ($followup as $followupkey => $followupvalue) {
                $originData = $originArr[$followupvalue['lead']['lead_source_id']];
                $pos1 = strpos(strtolower($originData['lead_source_name']), 'test');                
                if (($followupvalue['lead']['is_dnc_call'] == 'yes') || ($pos1 !== false) ) {
                    $followupArr[] = $followupvalue['lead_follow_up_id'];
                    $leadidArr[] = $followupvalue['lead_id'];
                    $leadactiviyobj = new LeadActivity();
                    $leadactiviyobj->createActivity($followupvalue['lead']['lead_id'], 13);
                } 
            }
            $cronLog->cronlog(11, 'deleteFollowupCron LeadCallBuffer=' . json_encode($followupArr, true), 'deletefollowupcron');
            $cronLog->cronlog(11, 'deleteFollowupCron LeadIds=' . json_encode($leadidArr, true), 'deletefollowupcron');
            LeadFollowUp::destroy($followupArr);
        }
        return response()->json($leadidArr);
    }
    
    public function saveFollowupentry($lead, $hour, $isPhonelead=0) {
        $dualslot_count = 0;
        $cronLog = new CronLog();
        $leadArr = Lead::with(['routingInfoLeadList.routingCampaignInfo'])->find($lead['lead_id'])->toArray();
        //echo '<pre>';print_r($leadArr);
        if ($leadArr) {

            $lead_routing_exist_check = $campbusinessesArr = $buyerTypeArr = array();
            if (isset($leadArr['routing_info_lead_list'])) {
                foreach ($leadArr['routing_info_lead_list'] as $routekey => $routevalue) {
                    $lead_routing_exist_check[] = $routevalue['campaign_id'];
                }
            }
            $premium_count = $exclusive_count = $dialer_count = $vmo_count = $dualslot_count = 0;
           
            $getCampaignData = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->get(['campaign_type_id','business_id'])->toArray();
            for ($c = 0; $c < count($getCampaignData); $c++) {
                $campbusinessesArr[] = $getCampaignData[$c]['business_id'];
                if ($getCampaignData[$c]['campaign_type_id'] == 6) {
                    $dualslot_count += 1;
                }
            }

            $businesses = Business::whereIn("business_id", $campbusinessesArr)->get(['buyer_type_id',"business_id"])->toArray();
            for ($c = 0; $c < count($businesses); $c++) {
                $buyerTypeArr[$businesses[$c]['business_id']] = $businesses[$c]['buyer_type_id'];
            }

            /*for ($c = 0; $c < count($getCampaignData); $c++) {
                if($buyerTypeArr[$getCampaignData[$c]['business_id']] == 3 && $leadArr['sold_type'] == 'exclusive'){
                    return 0; 
                }
            }*/
        }
        // Get TimeZone Wise
        $newDates = $this->getLeadTimeZone($lead, $hour, $dualslot_count);
        $entry_follow1 = LeadFollowUp::where('lead_id', $lead['lead_id'])->first();
        $leadactiviyobj = new LeadActivity();
        $leadactiviyobj->createActivity($lead['lead_id'], 12);
        date_default_timezone_set('America/New_York');

        if ($isPhonelead > 0) {
            $hoursCheck = date('G');
            $newDates = date('Y-m-d'). ' 11:00:00';
            // if ($hoursCheck >= 21 && $hoursCheck <= 23) {
            //     $newDates = date("Y-m-d", strtotime("+1 day")). ' 11:00:00';
            // }
            if ($hoursCheck >= 9 && $hoursCheck < 19) { //9 AM to 7 PM EST
                $date = date('Y-m-d H:i:s');
                $newDates = date('Y-m-d H:i:s', strtotime('+2 hours', strtotime($date)));
            }elseif ($hoursCheck >= 21 && $hoursCheck <= 23) {
                $newDates = date("Y-m-d", strtotime("+1 day")). ' 11:00:00';
            }
        }
        if (isset($entry_follow1)) {
            $entry_follow = $entry_follow1->lead_follow_up_id;
            $follow_up_parameter = array(
                'next_follow_datetime' => $newDates,
                'updated_at' => date('Y-m-d H:i:s')
            );
            LeadFollowUp::where('lead_follow_up_id', $entry_follow)->update($follow_up_parameter);
        } else {
            $follow_up_parameter = array(
                'lead_id' => $lead['lead_id'],
                'next_follow_datetime' => $newDates,
                'cust_phone_number' => $lead['phone'],
                'created_at' => date('Y-m-d H:i:s')
            );
            LeadFollowUp::create($follow_up_parameter);
        }
        
    }

    public function getLeadTimeZone($lead, $hour, $dualslot_count=0) {
        //exclusive dual lead on/off check
        $exclusiveDualSlotStatus = 0;
        $getExclusiveDualSlot = MstCallConfig::where('call_config', 'exclusivedualslot')->get(['status'])->toArray();
        if (count($getExclusiveDualSlot) > 0) {
            if (strtolower($getExclusiveDualSlot[0]['status']) == "active") {
                $exclusiveDualSlotStatus = 1;
            }
        }

        $cronLog = new CronLog();
        $flag = 0;
        $newDates = $utcDateTime = "";
        // Get Lead TimeZone
        $timeZoneDetail = MstTimeZone::where('timezone_id', $lead['timezone_id'])->get()->toArray();
        $estHoursCheck = date('G');
        // Get EST Date
        $estDate = date('Y-m-d');
        if (count($timeZoneDetail) > 0) {
            if ($estHoursCheck >= 0 && $estHoursCheck < 8) { //12 AM EST to 8 AM EST
                // Set Lead TimeZone
                date_default_timezone_set($timeZoneDetail[0]['timezone']);
                $utcDateTime = $estDate . ' 08:00:00';
                $flag = 1;
            } else if ($estHoursCheck >= 8 && $estHoursCheck < 11) { //8 AM EST TO 11 AM EST
                // Set Lead TimeZone
                date_default_timezone_set($timeZoneDetail[0]['timezone']);
                $plus = '+' . $hour . ' hours';
                //Added by BK Dual 0 Slot remaining and Exclusive Dual Slot switch off than lead should go in follow up after 30 min
                if ($dualslot_count > 0 && $exclusiveDualSlotStatus == 0) {
                    $plus = '+30 minutes';
                }
                $utcDateTime = $estDate . ' 08:00:00';

                $hoursCheck = date('G', time());
                if ($hoursCheck >= 8) {
                    $utcDateTime = date('Y-m-d H:i:s', strtotime($plus, time()));
                }
                $flag = 1;
            } else if ($estHoursCheck >= 19 && $estHoursCheck <= 23) { //7 PM EST to 12 AM EST
                // Set Lead TimeZone
                date_default_timezone_set($timeZoneDetail[0]['timezone']);
                $plus = '+' . $hour . ' hours';
                //Added by BK Dual 0 Slot remaining and Exclusive Dual Slot switch off than lead should go in follow up after 30 min
                if ($dualslot_count > 0 && $exclusiveDualSlotStatus == 0) {
                    $plus = '+30 minutes';
                }
                $utcDateTime = date('Y-m-d', strtotime('+1 day', strtotime($estDate))) . ' 08:00:00';

                $hoursCheck = date('G', strtotime($plus, time()));
                if ($hoursCheck >= 8 && $hoursCheck < 21) {
                    $utcDateTime = date('Y-m-d H:i:s', strtotime($plus, time()));
                }
                $flag = 1;
            } else if ($estHoursCheck >= 11 && $estHoursCheck < 19) { //11 AM EST, 7 PM EST
                // Set Lead TimeZone
                date_default_timezone_set($timeZoneDetail[0]['timezone']);
                $plus = '+' . $hour . ' hours';
                //Added by BK Dual 0 Slot remaining and Exclusive Dual Slot switch off than lead should go in follow up after 30 min
                if ($dualslot_count > 0 && $exclusiveDualSlotStatus == 0) {
                    $plus = '+30 minutes';
                }
                $utcDateTime = date('Y-m-d H:i:s', strtotime($plus, time()));

                $hoursCheck = date('G', strtotime($utcDateTime));
                if ($hoursCheck <= 8 || $hoursCheck >= 19) {
                    if ($hoursCheck <= 8) {
                        $utcDateTime = date('Y-m-d') . ' 08:00:00';
                    } else if ($hoursCheck >= 19 || $hour == 24) {
                        $utcDateTime = date('Y-m-d', strtotime('+1 day', strtotime($estDate))) . ' 08:00:00';
                    }
                }
                $flag = 1;
            }
        } else {
            $plus = '+' . $hour . ' hours';
            //Added by BK Dual 0 Slot remaining and Exclusive Dual Slot switch off than lead should go in follow up after 30 min
            if ($dualslot_count > 0 && $exclusiveDualSlotStatus == 0) {
                $plus = '+30 minutes';
            }
            $newDates = date('Y-m-d H:i:s', strtotime($plus, time()));
        }

        if ($flag > 0) {
            // Set time
            $time = new DateTime($utcDateTime);
            // Get Timezone - America/New_York
            $estTimezone = new DateTimeZone('America/New_York');
            $time->setTimeZone($estTimezone);
            // Convert UTC to America/New_York
            $newDates = $time->format('Y-m-d H:i:s');
        }

        return $newDates;
    }

    // public function createLeadCallLogs($lead_id, $lead_call_id, $log_srno, $log) {
    //     $cronLog = new CronLog();
    //     $logArr = array(
    //         'lead_id' => $lead_id,
    //         'lead_call_id' => $lead_call_id,
    //         'log_srno' => $log_srno,
    //         'log' => $log,
    //     );
    //     LeadCallLog::create($logArr);
    // }

    public function LeadRoutelog($leadid, $log, $type) {
        date_default_timezone_set('America/New_York');
        Logs::create(['lead_id' => $leadid, 'log' => $log, 'log_srno' => $type, 'created_at' => date("Y-m-d H:i:s")]);
    }

    public function callBuffertoFollowupcron() {
        $cronLog = new CronLog();
        $this->LeadRoutelog(0, 'buffertoFollowcron start', 1);
        $messages = $custNumberArr = [];
        try {
            $this->LeadRoutelog(0, 'buffertoFollowcron try', 2);
            $buffers_leads = LeadCallBuffer::with(['Lead'])->orderby('lead_call_buffer_id', 'desc')->get()->toArray();
            $this->LeadRoutelog(0, 'buffertoFollowcron allbufferleads= ' . json_encode($buffers_leads, true), 3);
            if (count($buffers_leads) > 0) {
                foreach ($buffers_leads as $bufferkey => $busffervalue) {
                    $custNumberArr[] = $busffervalue['customer_number'];
                }
            }
            $this->LeadRoutelog(0, 'buffertoFollowcron all cust nums= ' . json_encode($custNumberArr, true), 4);
            if (count($custNumberArr) > 0) {
                $contact_followups = LeadFollowUp::with("Lead")->whereIn('cust_phone_number', $custNumberArr)->orderby('lead_follow_up_id', 'desc')->get()->toArray();
                $this->LeadRoutelog(0, 'buffertoFollowcron cust nums exist in followup= ' . json_encode($contact_followups, true), 5);
                foreach ($contact_followups as $followupkey => $followupvalue) {
                    if (count($followupvalue) > 0) {
                        $lead = $followupvalue['lead'];
                        $lead_comeing_time = $this->getLeadTimeZone($lead, 1);
                        // echo "<pre>testfollow = " . $lead['lead_id']. ", lead_comeing_time = " . $lead_comeing_time ;    
                        $follow_up_parameter = array(
                            'next_follow_datetime' => $lead_comeing_time,
                            'updated_at' => date('Y-m-d H:i:s')
                        );
                        LeadFollowUp::where('lead_follow_up_id', $followupvalue['lead_follow_up_id'])->where('cust_phone_number', $followupvalue['cust_phone_number'])->update($follow_up_parameter);
                    }
                }
            }
            $bufferArr = $followupdata = [];
            if (count($buffers_leads) > 0) {
                foreach ($buffers_leads as $bufferkey => $buffervalue) {
                    $followupdata = LeadFollowUp::with("Lead")->where('lead_id', $buffervalue['lead_id'])->where('cust_phone_number', $buffervalue['customer_number'])->first();
                    $leadactiviyobj = new LeadActivity();
                    $leadactiviyobj->createActivity($buffervalue['lead_id'], 8);
                    if (isset($followupdata)) {
                        $followupdata = $followupdata->toArray();
                        $lead_comeing_time = $this->getLeadTimeZone($followupdata['lead'], 1);
                        $entry_follow = $followupdata['lead_follow_up_id'];
                        date_default_timezone_set('America/New_York');
                        $follow_up_parameter = array(
                            'next_follow_datetime' => $lead_comeing_time,
                            'updated_at' => date('Y-m-d H:i:s')
                        );
                        LeadFollowUp::where('lead_follow_up_id', $entry_follow)->update($follow_up_parameter);
                    } else {
                        $lead = $buffervalue['lead'];
                        $lead_comeing_time = $this->getLeadTimeZone($lead, 1);
                        date_default_timezone_set('America/New_York');
                        $follow_up_parameter = array(
                            'lead_id' => $buffervalue['lead_id'],
                            'next_follow_datetime' => $lead_comeing_time,
                            'cust_phone_number' => $buffervalue['customer_number'],
                            'created_at' => date('Y-m-d H:i:s')
                        );
                        LeadFollowUp::create($follow_up_parameter);
                        $leadactiviyobj->createActivity($buffervalue['lead_id'], 12);
                    }
                    $bufferArr[] = $buffervalue['lead_call_buffer_id'];
                }
                $this->LeadRoutelog(0, 'buffertoFollowcron followupdata= ' . json_encode($followupdata, true), 6);
                $this->LeadRoutelog(0, 'buffertoFollowcron deleted bufferids= ' . json_encode($bufferArr, true), 6);
                // LeadCallBuffer::whereIn('lead_call_buffer_id', $bufferArr)->each(function ($buffer, $key) {
                //     $buffer->delete();
                // });
                LeadCallBuffer::destroy($bufferArr);
            }
        } catch (Exception $e) {
            $messages = $e->getMessage();
        }
        // echo "<pre>testfollow = "; print_r($buffers_leads);     
        echo "Buffer destryed, No data in buffer";
        die;
    }

    public function callBuffertoFollowupTimeZonecron(Request $request) {
        $leadactiviyobj = new LeadActivity();
        $check = $request->id;
        $estDate = date('Y-m-d');
        $cronLog = new CronLog();
        $this->LeadRoutelog(0, 'buffertoFollowcron start', 1);
        $messages = $custNumberArr = [];
        try {
            $this->LeadRoutelog(0, 'buffertoFollowcron try', 2);
            if($check == 1) {
                $leadData = DB::select("SELECT b.lead_id, b.customer_number FROM lead_call_buffer b LEFT JOIN `lead` l ON b.lead_id = l.lead_id LEFT JOIN mst_timezone m ON l.timezone_id = m.timezone_id WHERE m.timezone = 'America/New_York'");
                $utcDateTime = date('Y-m-d', strtotime('+1 day', strtotime($estDate))) . ' 08:00:00';
            } else if($check == 2) {
                $leadData = DB::select("SELECT b.lead_id, b.customer_number FROM lead_call_buffer b LEFT JOIN `lead` l ON b.lead_id = l.lead_id LEFT JOIN mst_timezone m ON l.timezone_id = m.timezone_id WHERE m.timezone in ('America/New_York','America/Chicago')");
                $utcDateTime = date('Y-m-d', strtotime('+1 day', strtotime($estDate))) . ' 09:00:00';
            } else if($check == 3) {
                $leadData = DB::select("SELECT b.lead_id, b.customer_number FROM lead_call_buffer b LEFT JOIN `lead` l ON b.lead_id = l.lead_id LEFT JOIN mst_timezone m ON l.timezone_id = m.timezone_id WHERE m.timezone in ('America/New_York', 'America/Chicago','America/Denver','America/Phoenix')");
                $utcDateTime = date('Y-m-d', strtotime('+1 day', strtotime($estDate))) . ' 10:00:00';
            }

            $leadIdArr = array();
            for($i=0;$i<count($leadData);$i++) {
                $follow_up_parameter = array(
                    'lead_id' => $leadData[$i]->lead_id,
                    'next_follow_datetime' => $utcDateTime,
                    'cust_phone_number' => $leadData[$i]->customer_number,
                    'created_at' => date('Y-m-d H:i:s')
                );
                //print_r($follow_up_parameter);
                //echo "<hr>";
                LeadFollowUp::create($follow_up_parameter);
                $leadactiviyobj->createActivity($leadData[$i]->lead_id, 12);
                $leadIdArr[] = $leadData[$i]->lead_id;
            }
            LeadCallBuffer::whereIn('lead_id', $leadIdArr)->delete();
            //print_r($leadIdArr);
        } catch (Exception $e) {
            $messages = $e->getMessage();
        }
        // echo "<pre>testfollow = "; print_r($buffers_leads);
        echo "Buffer destryed, No data in buffer";
        die;
    }
}

