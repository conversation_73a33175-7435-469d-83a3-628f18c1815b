<?php

namespace App\Http\Controllers\Businesses;

use App\Http\Controllers\Controller;
use App\Models\Campaign\CampaignLocation;
use App\Models\Campaign\CampaignMoving;
use App\Models\Campaign\CampaignScheule;
use Illuminate\Http\Request;
use App\Models\Business\Business;
use App\Helpers\CommonFunctions;
use App\Models\Business\BusinessCC;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use App\Models\Campaign\Campaign;
use App\Models\Lead\LeadRouting;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Models\Master\MstCampaignType;
use App\Models\Master\MstLeadType;
use App\Models\Master\MstLeadCategory;
use App\Models\Campaign\CampaignUpdateLog;
use App\Models\Master\MstBuyerType;
use App\Models\Business\BusinessDailyLimit;
use App\Models\Business\BusinessesCampaignEomBalance;
use App\Models\Business\BusinessUpdateLog;
use App\Models\Business\BusinessPartnerMapping;
use Exception;
use App\Models\Business\BusinessModule;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Contract\Contract;
use App\Models\Master\MstCallConfig;
use App\Models\Campaign\CampaignOrganic;
use App\Models\Campaign\CampaignMoveSize;
use App\Models\Outbound\LeadCallNumber;
use App\Models\Outbound\LeadCall;
use PDF;
use App\Helpers\Helper;
use App\Models\Business\BusinessNote;
use App\Http\Controllers\Campaign\CampaignController;
use App\Models\Business\BusinessBadges;
use App\Models\Emails\BusinessMoverLoginCredentialsEmail;
use App\Models\Business\LSBusinessList;
use App\Models\Business\LMBusinessList;
use App\Models\Business\PMLSBusiness;
use App\Models\Business\PMLMBusiness;
use Illuminate\Support\Facades\Log;
use App\Models\Master\MstBusinessToken;
use App\Models\Setting\FMCSA;
use App\Models\Setting\FMCSABUSINESS;
use App\Models\Business\BusinessCompany;
use App\Models\Business\FmcsaCargoCarriedData;
use App\Models\Lead\LeadDeliverySMSCarrier;
use App\Models\DevDebug;
use Aws\S3\S3Client;
use Buglinjo\LaravelWebp\Webp;
use Illuminate\Http\UploadedFile;
use Spatie\Image\Image;
use Spatie\Image\Manipulations;
use App\Models\Master\MstSubscriptionPlan;
use App\Models\Subscription\Subscription;

class BusinessesController extends Controller
{

    private $per_page = 50;
    function __construct()
    {
        $this->middleware('permission:business-list', ['only' => ['index','monthlyStatement','notificationSetup','logBusiness','addbadges']]);
        $this->middleware('permission:business-add', ['only' => ['createBusiness', 'saveBusiness']]);
        $this->middleware('permission:business-edit', ['only' => ['editBusiness', 'updateBusiness']]);
        $this->middleware('permission:business-contractlist', ['only' => ['businessContractsList']]);
        $this->middleware('permission:business-contract-create', ['only' => ['createBusinessContract', 'saveBusinessContract']]);
        $this->middleware('permission:business-note', ['only' => ['getBusinessNote']]);
    }

    public function index(Request $request)
    {
        //$this->createOrganicMarketplaceCampaign(959);
        $requestData = $request->all();
        $endDecObj = new CommonFunctions;
        $finalArray = $subscriptionArray = [];
        $qsearch = trim($request['qsearch']);

        $sort_column = $request['sort_column'];
        $sort_order = $request['sort_order'];
        $low_fund = (bool) $request['low_fund'];
        if ($low_fund == true) {
            $default_balance_record = MstCallConfig::where('call_config', 'lowfund_balance')->first();
            $default_balance = $default_balance_record ? $default_balance_record->value : 20;
            $campaign = new Campaign();
            $final_business_ids = $campaign->lowFundCampaignBusiness($default_balance);
            $query = Business::with(['user'])->whereIn('business_id', $final_business_ids);

            if (!empty($qsearch)) {
                $query->where(function ($query) use ($qsearch) {
                    $query->where('business_id', 'like', '%' . $qsearch . '%')
                        ->orWhere('business_name', 'like', '%' . $qsearch . '%')
                        ->orWhere('contact_name', 'like', '%' . $qsearch . '%')
                        ->orWhere('email', 'like', '%' . $qsearch . '%')
                        ->orWhere('business_phone', 'like', '%' . $qsearch . '%')
                        ->orWhere('credit_available', 'like', '%' . $qsearch . '%');
                })->get();

            }

            if (isset($sort_column) && !empty($sort_column) && isset($sort_order) && !empty($sort_order)) {
                $businessesRecord = $query->orderBy($sort_column, $sort_order)->get();
            } else if (isset($sort_column) && !empty($sort_column) && !isset($sort_order) && empty($sort_order)) {
                $businessesRecord = $query->orderBy($sort_column, 'DESC')->get();
            } else if (!isset($sort_column) && empty($sort_column) && isset($sort_order) && !empty($sort_order)) {
                $businessesRecord = $query->orderBy('business_id', $sort_order)->get();
            } else {
                $businessesRecord = $query->orderBy('business_id', 'desc')->get();
            }
        } else {
            $query = Business::with(['user']);
            if (!empty($qsearch)) {

                $query->where('business_id', 'like', '%' . $qsearch . '%')
                    ->orWhere('business_name', 'like', '%' . $qsearch . '%')
                    ->orWhere('contact_name', 'like', '%' . $qsearch . '%')
                    ->orWhere('email', 'like', '%' . $qsearch . '%')
                    ->orWhere('business_phone', 'like', '%' . $qsearch . '%')
                    ->orWhere('credit_available', 'like', '%' . $qsearch . '%');
            }

            if (isset($sort_column) && !empty($sort_column) && isset($sort_order) && !empty($sort_order)) {
                $businessesRecord = $query->orderBy($sort_column, $sort_order)->get();
            } else if (isset($sort_column) && !empty($sort_column) && !isset($sort_order) && empty($sort_order)) {
                $businessesRecord = $query->orderBy($sort_column, 'DESC')->get();
            } else if (!isset($sort_column) && empty($sort_column) && isset($sort_order) && !empty($sort_order)) {
                $businessesRecord = $query->orderBy('business_id', $sort_order)->get();
            } else {
                $businessesRecord = $query->orderBy('business_id', 'desc')->get();
            }
        }
        // $query = Business::where('is_contract_signed', 'yes');
        $ymddate = date('Y-m-d');
        $formatted_date = $ymddate . " 00:00:00";
        $formatted_date1 = $ymddate . " 23:59:59";
        $businessIdArr = $businessCampIdArr = $campIdArr = $campWiseCountArr = $campWiseRouteCountArr = $businessWiseCountArr = $businessWiseRouteCountArr = array();
        foreach ($businessesRecord as $busId => $busVal) {
            $businessIdArr[] = $busVal->business_id;
        }
        if (count($businessIdArr) > 0) {
            $campaignlist = Campaign::whereIn('business_id', $businessIdArr)->get(['campaign_id', 'business_id'])->toArray();
            for ($g = 0; $g < count($campaignlist); $g++) {
                $businessCampIdArr[$campaignlist[$g]['business_id']][] = $campaignlist[$g]['campaign_id'];
                $campIdArr[] = $campaignlist[$g]['campaign_id'];
            }
        }
        $lastLeadDateArray = [];
        $lastleaddate_data = LeadRouting::leftJoin('campaign as cmp', 'lead_routing.campaign_id', 'cmp.campaign_id')
            ->leftJoin('business as bus', 'cmp.business_id', 'bus.business_id')
            ->select(
                'bus.business_id',
                DB::raw('max(lead_routing.created_at) as last_lead_date')
            )
            ->where('lead_routing.route_status', 'sold')
            ->groupBy('bus.business_id')->get()->toArray();

        foreach ($lastleaddate_data as $buskey => $businesseach) {
            $lastLeadDateArray[$businesseach['business_id']] = array("lastdate" => $businesseach['last_lead_date'], "totallead" => 0);
        }
        $startdate = date('Y-m-d')." 00:00:00";
        $enddate = date('Y-m-d')." 23:59:59";
        $totallead_data = LeadRouting::leftJoin('campaign as cmp', 'lead_routing.campaign_id', 'cmp.campaign_id')
            ->leftJoin('business as bus', 'cmp.business_id', 'bus.business_id')
            ->select(
                'bus.business_id',
                DB::raw('count(DISTINCT lead_routing.lead_id) as total_lead')
            )
            ->whereBetween('lead_routing.created_at', [$startdate, $enddate])
            ->where('lead_routing.route_status', 'sold')
            ->groupBy('bus.business_id')->get()->toArray();
        foreach ($totallead_data as $buskey => $businesseach) {
            $lastLeadDateArray[$businesseach['business_id']]['totallead'] = $businesseach['total_lead'];
        }
        // echo "<pre>business = "; print_r($lastLeadDateArray); exit();
        foreach ($businessesRecord as $buskey => $businesseach) {
            $leads_count = 0;
            $lastLeadDate = "---";
            if (isset($businessCampIdArr[$businesseach->business_id])) {
                // $leadLastData = LeadRouting::whereIn('campaign_id', $businessCampIdArr[$businesseach->business_id])->where('route_status', 'sold')->orderBy('created_at', 'desc')->get(['created_at', 'lead_routing_id'])->toArray();
                if (isset($lastLeadDateArray[$businesseach->business_id])) {
                    // $lastLeadDate = date("m/d/Y", strtotime($leadLastData[0]['created_at']));
                    $lastLeadDate = date("m/d/Y", strtotime($lastLeadDateArray[$businesseach->business_id]['lastdate']));
                    $leads_count = $lastLeadDateArray[$businesseach->business_id]['totallead'];
                }
            }
            $businesseach->leads = $leads_count;
            $businesseach->lastLeadDate = $lastLeadDate;
            $businesseach->last_lead_date = strtotime($lastLeadDate);
            $enc_id = $endDecObj->customeEncryption($businesseach->business_id);
            $businesseach->enc_id = $enc_id;
        }
        $mover_dashboard_url = Helper::checkServer()['mover_dashboard_url'];
        // echo "<pre>"; print_r($businessesRecord); exit();
        if ($sort_column == 'created_at') {
            //$finalArray = $finalArray1['data'];
            $finalArray = $businessesRecord->toArray();
            $businessSearchData = array_column($finalArray, "last_lead_date");
            // echo "<pre>"; print_r($businessSearchData); exit();
            if($sort_order == "ASC"){
                array_multisort($businessSearchData, SORT_ASC, $finalArray);
            }else{
                array_multisort($businessSearchData, SORT_DESC, $finalArray);
            }
            $businessesRecord = json_decode(json_encode($finalArray), FALSE);
            $businessesRecord = $this->convertArrayToCollection($businessesRecord,$qsearch,$sort_column,$sort_order);
            $finalArray1 = $businessesRecord->toArray();
            $finalArray1 = $this->objToArray($finalArray1);
        }else{
            // $businessesRecord = json_decode(json_encode($finalArray1), FALSE);
            // $businessesRecord = $this->convertArrayToCollection($businessesRecord,$qsearch,$sort_column,$sort_order);
            $finalArray1 = $businessesRecord->paginate(50)->withQueryString()->toArray();
        }

        $subscriptionDetail = Subscription::where('subscription_start', '>=', date('Y-m-01'))->where('subscription_end', '<=', date('Y-m-t'))->where('is_active', 'yes')->get();
        foreach ($subscriptionDetail as $subscription) {
            $subscriptionArray[$subscription->business_id] = $subscription->subscription_plan_id;
        }

        // echo "<pre>"; print_r($finalArray1); exit();
        return view("businesses.index")->with('businessesRecord', $finalArray1 )->with("mover_dashboard_url", $mover_dashboard_url)->with("subscriptionArray", $subscriptionArray);
    }

    protected function objToArray($obj)
    {
        // Not an object or array
        if (!is_object($obj) && !is_array($obj)) {
            return $obj;
        }
        // Parse array
        foreach ($obj as $key => $value) {
            $arr[$key] = $this->objToArray($value);
        }
        // Return parsed array
        return $arr;
    }

    public function convertArrayToCollection($items,$qsearch,$sort_column,$sort_order, $perPage = 50, $page = null, $options = [])
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
        $items = $items instanceof Collection ? $items : Collection::make($items);
        $path = route("business-list");
        $query = ["qsearch"=>$qsearch,'sort_column'=>$sort_column,'sort_order'=>$sort_order];
        $options = ['path' => $path, 'query' => $query];
        // $items = json_decode(json_encode($items), FALSE);
        return new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, $options);
    }

    public function createBusiness()
    {
        $businessList = Business::get();
        $mstBuyerTypeList = MstBuyerType::get();
        $lsBusinessList = LSBusinessList::get();
        $tlmBusinessList = LMBusinessList::get();
        return view("businesses.create", compact('businessList', 'mstBuyerTypeList', 'lsBusinessList', 'tlmBusinessList'));
    }

    public function saveBusiness(Request $request)
    {
        try {
            // print_r($request->all());
            $requestData = $request->all();
            $status = 0;
            $message = 'fail';
            $error = array();
            $enc_id = "";
            // $logDetails = '';
            $logArr = [];
            $rules = array(
                'ownerName' => 'required|max:50',
                'contactName' => 'required|max:50',
                'businessName' => 'required|max:50',
                // 'displayName' => 'required|max:50',
                // old code with unique business email
                // 'businessName' => 'required|unique:business,business_name|max:50',
                'address' => 'required',
                'licence_number' => 'max:50',
                'dotNumber' => 'max:50',
                'businessEmail' => 'required|email|max:50|regex:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',
                'businessPhone' => 'required|digits:10',
                'relyNumber' => 'nullable|digits:10',
                'emergencyPhone' => 'nullable|digits:10',
                'forwardNumber' => 'nullable|digits:10',
                'company_url' => 'nullable|url',

                //'businessLoginEmail' => 'required|email|max:50|regex:/^([\w\.\+]{1,})([^\W])(@)([\w]{1,})(\.[\w]{1,})+$/',
                // old code with unique business email
                'businessLoginEmail' => 'required|unique:business,movers_username|email|max:50|regex:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',
                'businessLoginPassword' => 'required|min:8|max:10',
            );
            $input = $request->only(['ownerName', 'contactName', 'businessName', 'address', 'businessEmail', 'businessPhone', 'relyNumber', 'emergencyPhone', 'forwardNumber', 'businessLoginEmail', 'businessLoginPassword','company_url']);
            $validator = Validator::make($input, $rules);
            $businessesId = 0;
            if ($validator->fails()) {
                $error =  $validator->getMessageBag()->toArray();
            } else {
                $buyerType = $request->get('buyerType');
                $paymentType = $request->get('paymentType');
                $ownerName = $request->get('ownerName');
                $contactName = $request->get('contactName');
                $businessName = $request->get('businessName');
                // $displayName = $request->get('displayName');
                $displayName = $request->get('businessName');
                $address = $request->get('address');
                $dotNumber = $request->get('dotNumber');
                $licence_number = $request->get('licence_number');
                $businessEmail = $request->get('businessEmail');
                // $businessNotificationEmail = $request->get('businessNotificationEmail');
                $businessPhone = $request->get('businessPhone');
                $company_url = $request->get('company_url');
                // $businessNotificationPhone = $request->get('businessNotificationPhone');
                $emergencyPhone = $request->get('emergencyPhone');
                $relyNumber = $request->get('relyNumber');
                $forwardNumber = $request->get('forwardNumber');
                $businessLoginEmail = $request->get('businessLoginEmail');
                $businessLoginPassword = $request->get('businessLoginPassword');
                $createdbyuser = $request->get('createdbyuser');
                $noncompetevalues = $request->get('business_toggle1');
                if (array_key_exists("business_toggle1", $requestData)) {
                    if (count($noncompetevalues) > 0) {
                        $noncompetevalues = array_unique($noncompetevalues);
                        $noncompetevalues = array_values($noncompetevalues);
                    }
                }
                $noncompetevalues_ls = $request->get('business_toggle_ls');
                $noncompetevalues_tlm = $request->get('business_toggle_tlm');
                if (array_key_exists("business_toggle_ls", $requestData)) {
                    if (count($noncompetevalues_ls) > 0) {
                        $noncompetevalues_ls = array_unique($noncompetevalues_ls);
                        $noncompetevalues_ls = array_values($noncompetevalues_ls);
                    }
                }
                if (array_key_exists("business_toggle_tlm", $requestData)) {
                    if (count($noncompetevalues_tlm) > 0) {
                        $noncompetevalues_tlm = array_unique($noncompetevalues_tlm);
                        $noncompetevalues_tlm = array_values($noncompetevalues_tlm);
                    }
                }
                $errorArr = [];
                // Businesses Phone
                $BusinessesPhoneCheck = array_unique(str_split($businessPhone));
                if (count($BusinessesPhoneCheck) === 1 && ($businessPhone != '')) {
                    $errorArr['businessPhone'][0] = "Same number repeated in businesses phone.";
                }
                // Businesses Notification Phone
                // $BusinessesNotificationPhoneCheck = array_unique(str_split($businessNotificationPhone));
                // if (count($BusinessesNotificationPhoneCheck) === 1) {
                //     $errorArr['businessNotificationPhone'][0] = "Same number repeated in businesses notification phone.";
                // }
                // emergency Phone
                $EmergencyPhoneCheck = array_unique(str_split($emergencyPhone));
                if (count($EmergencyPhoneCheck) === 1 && ($emergencyPhone != '')) {
                    $errorArr['emergencyPhone'][0] = "Same number repeated in contact phone.";
                }
                // rely Phone
                // $InterfacePhoneCheck = array_unique( str_split($relyNumber));
                // if( count($InterfacePhoneCheck) === 1 ) {
                //     $errorArr['relyNumber'][0] = "Same number repeated in rely phone.";
                // }
                // forward Phone
                $ForwardnumberPhoneCheck = array_unique(str_split($forwardNumber));
                if (count($ForwardnumberPhoneCheck) === 1 && ($forwardNumber != '')) {
                    $errorArr['forwardNumber'][0] = "Same number repeated in forward number.";
                }
                if (isset($request['logo']) && !empty($request['logo']) && $request['logo'] != 'undefined') {
                    $file = $request->file('logo');
                    $allowedExtension = ['jpg', 'jpeg', 'png', 'gif'];
                    $extension = $file->getClientOriginalExtension();
                    if (!in_array($extension, $allowedExtension)) {
                        $errorArr['logo'][0] = 'The logo must be an image and must be a file of type: jpeg, jpg, png, gif';
                    }
                }
                if (count($errorArr) > 0) {
                    $responseData = ['status' => $status, 'message' => $message, 'error' => $errorArr];
                    return response()->json($responseData);
                }
                // logo upload logic start
                $fname = '';
                if (isset($request['logo']) && !empty($request['logo']) && $request['logo'] != 'undefined') {
                    $file = $request->file('logo');
                    $path = public_path();
                    $filename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                    $filename = strlen($filename) > 20 ? substr($filename, 0, 20) : $filename;
                    $uniqueSuffix = substr(md5(time()), 0, 10);
                    $logo = $filename . '_' . $uniqueSuffix . '.webp';
                    // $split = explode(".", $filename);
                    // $logo = $split[0] . '_' . time() . '.' . $split[1];
                    // $logo = $split[0] . time() . '.webp';
                    $fname = str_replace(' ', '_', $logo);
                    $destinationPath = $path . '/images/businesslogo';
                    // $file->move($destinationPath, $fname);
                    try {
                        $imagick = new \Imagick($file->getRealPath());
                        $imagick->setImageFormat('webp');
                        $imagick->setImageCompression(\Imagick::COMPRESSION_JPEG);
                        $imagick->setImageCompressionQuality(70);
                        $imagick->resizeImage(300, 0, \Imagick::FILTER_LANCZOS, 1);
                        $imagick->writeImage($destinationPath . '/' . $fname);

                        while (filesize($destinationPath . '/' . $fname) > 10240) {
                            $imagick->setImageCompressionQuality($imagick->getImageCompressionQuality() - 10);
                            $imagick->writeImage($destinationPath . '/' . $fname);
                        }

                        $imagick->clear();
                        $imagick->destroy();
                    } catch (\ImagickException $e) {
                        $webpImage = Webp::make($file);
                        $webpImage->save($destinationPath . '/' . $fname, 5);
                    }

                    $path = public_path('/images/businesslogo');
                    $originalImageFile = $path . '/' . $fname;
                    $uploadedFile = new UploadedFile($originalImageFile, $fname);
                    $logo = 'data:image/webp;base64,' . base64_encode(file_get_contents($uploadedFile));
                    $postFields = json_encode(array('business_id' => $businessesId, 'filename' => $fname, 'image' => $logo));
                    $url        = Helper::checkServer()['vm_url'] . '/storecompanylogo';
                    $ch         = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $url);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
                    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_POST, 1);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_exec($ch);
                    if (curl_errno($ch)) {
                        echo 'Error:' . curl_error($ch);
                        exit;
                    }
                    curl_close($ch);
                }
                // logo upload logic end

                $businessesId = Business::insertGetId([
                    'business_name' => $businessName,
                    'display_name' => $displayName,
                    'buyer_type_id' => $buyerType,
                    'payment_type' => $paymentType,
                    'owner_name' => $ownerName,
                    'contact_name' => $contactName,
                    'license_number' => $licence_number,
                    'dot_number' => $dotNumber,
                    'address' => htmlentities($address),
                    'email' => $businessEmail,
                    // 'notification_email' => $businessNotificationEmail,
                    'business_phone' => $businessPhone,
                    'company_url' => $company_url,
                    // 'notification_phone' => $businessNotificationPhone,
                    'emergency_phone' => $emergencyPhone,
                    'rely_number' => $relyNumber,
                    'forward_number' => $forwardNumber,
                    'status' => 'active',
                    'logo' => $fname,
                    'credit_available' => 0,
                    'credit_reserved' => 0,
                    'is_low_fund_notification' => 'yes',
                    'low_fund_notification_email' => $businessEmail,
                    'low_fund_notification_phone' => $businessPhone,
                    'is_contract_signed' => 'no',
                    'movers_username' => $businessLoginEmail,
                    'movers_password' => Hash::make($businessLoginPassword),
                    'created_at' => date('Y-m-d H:i:s'),
                    'created_user_id' => $createdbyuser,
                    'updated_user_id' => $createdbyuser,
                ]);
                $endDecObj = new CommonFunctions;
                $enc_id = $endDecObj->customeEncryption($businessesId);
                $businessModuleId = BusinessModule::insertGetId([
                    'business_module_name' => 'Business',
                    'description' => "Business Level changes",
                    'created_at' => date('Y-m-d H:i:s'),
                ]);

                //Added By Hj On 22-06-2023 For Auto Purchase Relay Number When Create Business Start
                /*$matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
                if (strpos($matrixBaseURL_Outbound, 'linkup.software') !== false) {
                    $businessIdArr = array();
                    $businessIdArr[] = $businessesId;
                    $relayBusiness = New PurchaseRelayNumberController();
                    $relayBusiness->purchaseRelayNumber($businessIdArr);
                }*/
                //Added By Hj On 22-06-2023 For Auto Purchase Relay Number When Create Business End
                $oldBusinessesData = Business::with(['mstBuyerType'])->where('business_id', '=', $businessesId)->first();
                if (isset($buyerType) && !empty($buyerType)) {
                    $logArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'buyer_type_id',
                        'new_value' => $oldBusinessesData->mstBuyerType->buyer_type_name,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                if (isset($paymentType) && !empty($paymentType)) {
                    $logArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'payment_type',
                        'new_value' => $paymentType,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                if (isset($ownerName) && !empty($ownerName)) {
                    $logArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'owner_name',
                        'new_value' => $ownerName,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                if (isset($contactName) && !empty($contactName)) {
                    $logArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'contact_name',
                        'new_value' => $contactName,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                if (isset($businessName) && !empty($businessName)) {
                    $logArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'business_name',
                        'new_value' => $businessName,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                if (isset($displayName) && !empty($displayName)) {
                    $logArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'display_name',
                        'new_value' => $displayName,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                if (isset($licence_number) && !empty($licence_number)) {
                    $logArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'license_number',
                        'new_value' => $licence_number,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                if (isset($dotNumber) && !empty($dotNumber)) {
                    $logArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'dot_number',
                        'new_value' => $dotNumber,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                if (isset($address) && !empty($address)) {
                    $logArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'address',
                        'new_value' => $address,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                if (isset($businessEmail) && !empty($businessEmail)) {
                    $logArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'email',
                        'new_value' => $businessEmail,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                // if (isset($businessNotificationEmail) && !empty($businessNotificationEmail)) {
                //     $logArr[] = [
                //         'created_user_id' => $createdbyuser,
                //         'business_id' => $businessesId,
                //         'field_name' => 'notification_email',
                //         'new_value' => $businessNotificationEmail,
                //         'created_at' => date('Y-m-d H:i:s')
                //     ];
                // }
                if (isset($businessPhone) && !empty($businessPhone)) {
                    $logArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'business_phone',
                        'new_value' => $businessPhone,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                if (isset($company_url) && !empty($company_url)) {
                    $logArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'company_url',
                        'new_value' => $company_url,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                // if (isset($businessNotificationPhone) && !empty($businessNotificationPhone)) {
                //     $logArr[] = [
                //         'created_user_id' => $createdbyuser,
                //         'business_id' => $businessesId,
                //         'field_name' => 'notification_phone',
                //         'new_value' => $businessNotificationPhone,
                //         'created_at' => date('Y-m-d H:i:s')
                //     ];
                // }
                if (isset($emergencyPhone) && !empty($emergencyPhone)) {
                    $logArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'contact_phone',
                        'new_value' => $emergencyPhone,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                if (isset($relyNumber) && !empty($relyNumber)) {
                    $logArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'rely_number',
                        'new_value' => $relyNumber,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                if (isset($forwardNumber) && !empty($forwardNumber)) {
                    $logArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'forward_number',
                        'new_value' => $forwardNumber,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                if (isset($businessLoginEmail) && !empty($businessLoginEmail)) {
                    $logArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'movers_username',
                        'new_value' => $businessLoginEmail,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }

                // For Business Partners
                if (array_key_exists("business_toggle1", $requestData)) {
                    if (count($noncompetevalues) > 0) {
                        $logArr[] = [
                            'created_user_id' => $createdbyuser,
                            'business_id' => $businessesId,
                            'field_name' => 'partner_business',
                            'new_value' => json_encode($noncompetevalues),
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                        foreach ($noncompetevalues as $noncompetevalueskey => $noncompetevaluesvalue) {
                            BusinessPartnerMapping::create([
                                'business_id' => $businessesId,
                                'business_partner_id' => $noncompetevaluesvalue,
                                'created_at' => date('Y-m-d H:i:s')
                            ]);
                            BusinessPartnerMapping::create([
                                'business_id' => $noncompetevaluesvalue,
                                'business_partner_id' => $businessesId,
                                'created_at' => date('Y-m-d H:i:s')
                            ]);
                        }
                    }
                }
                // if (array_key_exists("business_toggle_ls", $requestData)) {
                if(isset($noncompetevalues_ls)){
                    if (count($noncompetevalues_ls) > 0) {
                        $logArr[] = [
                            'created_user_id' => $createdbyuser,
                            'business_id' => $businessesId,
                            'field_name' => 'ls_partner_business',
                            'new_value' => json_encode($noncompetevalues_ls),
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                    }
                }
                // if (array_key_exists("business_toggle_tlm", $requestData)) {
                if(isset($noncompetevalues_tlm)){
                    if (count($noncompetevalues_tlm) > 0) {
                        $logArr[] = [
                            'created_user_id' => $createdbyuser,
                            'business_id' => $businessesId,
                            'field_name' => 'tlm_partner_business',
                            'new_value' => json_encode($noncompetevalues_tlm),
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                    }
                }
                // For Business Partners

                //for logo
                if (isset($fname) && !empty($fname) && $fname != '') {
                    $logArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'logo',
                        'new_value' => $fname,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                // For Manage Business Hours Start
                $businessesHours = $this->insertBusinessHoursData($businessesId, $request, 0);
                if ($businessesHours != null) {
                    if (count($businessesHours) > 0) {
                        $logArr[] = $businessesHours[0];
                    }
                }

                //Added By BK On 11/08/2023 For Auto Create Organic and Marketplace Campaign When Create New Business
                $this->createOrganicMarketplaceCampaign($businessesId, $createdbyuser);

                // add ls partners
                $getLSBusinessListIds = [];
                if(!empty($noncompetevalues_ls) && count($noncompetevalues_ls) > 0){
                    $getLSBusinessListIds = LSBusinessList::whereIn('ls_business_list_id', $noncompetevalues_ls)->pluck('ls_business_id')->toArray();
                }

                // add tlm partners
                $getTLMBusinessListIds = [];
                if(!empty($noncompetevalues_tlm) && count($noncompetevalues_tlm) > 0){
                    $getTLMBusinessListIds = LMBusinessList::whereIn('lm_business_list_id', $noncompetevalues_tlm)->pluck('lm_business_id')->toArray();
                }

                $getNewTLMBusinessListIds = LMBusinessList::where(function ($query) use ($businessPhone, $emergencyPhone, $forwardNumber, $businessEmail, $businessLoginEmail, $address, $dotNumber){
                    $query->where('lm_business_phone', $businessPhone)->orWhere('lm_emergency_phone', $emergencyPhone)->orWhere('lm_forward_number', $forwardNumber)
                        ->orWhere('lm_email', $businessEmail)->orWhere('lm_movers_username', $businessLoginEmail)
                        ->orWhere('lm_address', strtolower(str_replace(' ', '', $address)))->orWhere('lm_dot_number', $dotNumber);
                })->pluck('lm_business_id')->toArray();

                $getTLMBusinessListIds = array_merge($getTLMBusinessListIds, $getNewTLMBusinessListIds);

                // $checkToken = MstBusinessToken::where("business_id", $businessesId)->first();
                $checkToken = MstBusinessToken::first();
                $token      = "56as342dfKdfds789JKasd2sdfsd";
                if (!empty($checkToken) ) {
                    $token = $checkToken->token;
                }
                $noncompetevalues[] = $businessesId;

                // for ls
                /*if (count($getLSBusinessListIds) > 0) {
                    $postData = ["token" => $token, 'pm_company_id' => $noncompetevalues, 'ls_company_id' => $getLSBusinessListIds];
                    $this->getLSpartneredBusiness(json_encode($postData));
                    $this->saveLSBusinesstoPM(json_encode($postData));
                }*/

                // fo tlm
                if (count($getTLMBusinessListIds) > 0) {
                    $postData = ["token" => $token, 'pm_company_id' => $noncompetevalues, 'lm_company_id' => $getTLMBusinessListIds];
                    $this->getTLMpartneredBusiness(json_encode($postData));
                    $this->saveTLMBusinesstoPM(json_encode($postData));
                }

                if (count($logArr) > 0) {
                    BusinessUpdateLog::insert($logArr);
                }
                $status = 1;
                $message = 'success';
            }
            $responseData = [
                'status' => $status,
                'message' => $message,
                'error' => $error,
                'bid' => $enc_id,
                'businessid' => $businessesId
            ];
            return json_encode($responseData);

        } catch(Exception $e) {
            $message = $e->getMessage();
        }
    }

    public function insertBusinessHoursData($businessesId, $request, $edit)
    {
        //$createdbyuser = Auth::user()->id;
        $createdbyuser = 6;
        $oldBusinessHoursData = BusinessDailyLimit::where('business_id', $businessesId)->get()->toArray();
        if ($edit == 1) {
            BusinessDailyLimit::where('business_id', $businessesId)->delete();
        }
        $businessHoursData = array();
        $old_logDetails = $new_logDetails = "";
        $logHoursArr = [];
        if (!empty($request->get('limit0')) || !empty($request->get('budget0'))) {
            $limit0 = $request->get('limit0');
            $isPauseSun = $request->get('isPauseSun');
            $budget0 = $request->get('budget0');
            $businessHoursData[] = [
                'business_id' => $businessesId,
                'day' => 7,
                'daily_limit' => $limit0,
                'budget_limit' => $budget0,
                'is_pause' => $isPauseSun,
                'created_at' => date('Y-m-d H:i:s')
            ];
        }
        if (!empty($request->get('limit1')) || !empty($request->get('budget1'))) {
            $limit1 = $request->get('limit1');
            $isPauseMon = $request->get('isPauseMon');
            $budget1 = $request->get('budget1');
            $businessHoursData[] = [
                'business_id' => $businessesId,
                'day' => 1,
                'daily_limit' => $limit1,
                'budget_limit' => $budget1,
                'is_pause' => $isPauseMon,
                'created_at' => date('Y-m-d H:i:s')
            ];
        }
        if (!empty($request->get('limit2')) || !empty($request->get('budget2'))) {
            $limit2 = $request->get('limit2');
            $isPauseTue = $request->get('isPauseTue');
            $budget2 = $request->get('budget2');
            $businessHoursData[] = [
                'business_id' => $businessesId,
                'day' => 2,
                'daily_limit' => $limit2,
                'budget_limit' => $budget2,
                'is_pause' => $isPauseTue,
                'created_at' => date('Y-m-d H:i:s')
            ];
        }
        if (!empty($request->get('limit3')) || !empty($request->get('budget3'))) {
            $limit3 = $request->get('limit3');
            $isPauseWed = $request->get('isPauseWed');
            $budget3 = $request->get('budget3');
            $businessHoursData[] = [
                'business_id' => $businessesId,
                'day' => 3,
                'daily_limit' => $limit3,
                'budget_limit' => $budget3,
                'is_pause' => $isPauseWed,
                'created_at' => date('Y-m-d H:i:s')
            ];
        }
        if (!empty($request->get('limit4')) || !empty($request->get('budget4'))) {
            $limit4 = $request->get('limit4');
            $isPauseThu = $request->get('isPauseThu');
            $budget4 = $request->get('budget4');
            $businessHoursData[] = [
                'business_id' => $businessesId,
                'day' => 4,
                'daily_limit' => $limit4,
                'budget_limit' => $budget4,
                'is_pause' => $isPauseThu,
                'created_at' => date('Y-m-d H:i:s')
            ];
        }
        if (!empty($request->get('limit5')) || !empty($request->get('budget5'))) {
            $limit5 = $request->get('limit5');
            $isPauseFri = $request->get('isPauseFri');
            $budget5 = $request->get('budget5');
            $businessHoursData[] = [
                'business_id' => $businessesId,
                'day' => 5,
                'daily_limit' => $limit5,
                'budget_limit' => $budget5,
                'is_pause' => $isPauseFri,
                'created_at' => date('Y-m-d H:i:s')
            ];
        }
        if (!empty($request->get('limit6')) || !empty($request->get('budget6'))) {
            $limit6 = $request->get('limit6');
            $isPauseSat = $request->get('isPauseSat');
            $budget6 = $request->get('budget6');
            $businessHoursData[] = [
                'business_id' => $businessesId,
                'day' => 6,
                'daily_limit' => $limit6,
                'budget_limit' => $budget6,
                'is_pause' => $isPauseSat,
                'created_at' => date('Y-m-d H:i:s')
            ];
        }
        if (count($businessHoursData) > 0) {
            $businessDailyLimitData = BusinessDailyLimit::insert($businessHoursData);
            // business timing insert in business log
            $dayArray = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
            array_unshift($dayArray, "");
            unset($dayArray[0]);
            if (count($oldBusinessHoursData) > 0) {
                for ($i = 0; $i < count($oldBusinessHoursData); $i++) {
                    $old_logDetails .= '{day:' . $dayArray[$oldBusinessHoursData[$i]['day']] . ', daily_limit: ' . $oldBusinessHoursData[$i]['daily_limit'] . ', budget_limit: ' . $oldBusinessHoursData[$i]['budget_limit'] . ', is_pause: ' . $oldBusinessHoursData[$i]['is_pause'] . '}';
                }
            }
            for ($i = 0; $i < count($businessHoursData); $i++) {
                $new_logDetails .= '{day:' . $dayArray[$businessHoursData[$i]['day']] . ', daily_limit: ' . $businessHoursData[$i]['daily_limit'] . ', budget_limit: ' . $businessHoursData[$i]['budget_limit'] . ', is_pause: ' . $businessHoursData[$i]['is_pause'] . '}';
            }

            if ($edit > 0) {
                if ($old_logDetails != $new_logDetails) {
                    $logHoursArr[] = [
                        'created_user_id' => $createdbyuser,
                        'business_id' => $businessesId,
                        'field_name' => 'business_hours',
                        'old_value' => $old_logDetails,
                        'new_value' => $new_logDetails,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
            } else {
                $logHoursArr[] = [
                    'created_user_id' => $createdbyuser,
                    'business_id' => $businessesId,
                    'field_name' => 'business_hours',
                    'new_value' => $new_logDetails,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            return $logHoursArr;
        }
    }

    public function editBusiness($bid)
    {
        $endDecObj = new CommonFunctions;
        $businessId = $endDecObj->customeDecryption($bid);
        $businessList = Business::where('business_id', "!=", $businessId)->get();
        $businessData = Business::with(['mstBuyerType', 'businessDailytLimit'])->where('business_id', $businessId)->first()->toArray();

        $noncompetevalueselected = $noncompetevalueselected_name = $noncompetevalueselected_ls = $noncompetevalueselected_name_ls = array();
        if ($businessData['business_id'] > 0) {
            $businessesnoncompete1 = BusinessPartnerMapping::where('business_id', $businessData['business_id'])->get();
            if (isset($businessesnoncompete1) > 0) {
                foreach ($businessesnoncompete1 as $noncompetekey => $noncompetevalue) {
                    $noncompetevalueselected[] = $noncompetevalue->business_partner_id;
                    $name1 = Business::where('business_id', $noncompetevalue->business_partner_id)->get('business_name')->first();
                    $noncompetevalueselected_name[] = $noncompetevalue->business_partner_id . ' ' . $name1->business_name;
                }
            }
            $businessesnoncompete2 = BusinessPartnerMapping::where('business_partner_id', $businessData['business_id'])->get();
            if (count($businessesnoncompete2) > 0) {
                foreach ($businessesnoncompete2 as $noncompetekey1 => $noncompetevalue1) {
                    $noncompetevalueselected[] = $noncompetevalue1->business_id;
                    $name2 = Business::where('business_id', $noncompetevalue1->business_id)->get('business_name')->first();
                    $noncompetevalueselected_name[] = $noncompetevalue1->business_id . ' ' . $name2->business_name;
                }
            }
        }
        $noncompetevalueselected = array_unique($noncompetevalueselected);
        $noncompetevalueselected = array_values($noncompetevalueselected);
        $noncompetevalueselected_name = array_unique($noncompetevalueselected_name);
        $noncompetevalueselected_name = array_values($noncompetevalueselected_name);

        // ls partner business
        $ls_business_data = PMLSBusiness::with(['lsBusiness'])->where('business_id', $businessData['business_id'])->get()->toArray();
        $tlm_business_data = PMLMBusiness::with(['lmBusiness'])->where('business_id', $businessData['business_id'])->get()->toArray();
        // echo "<prE>";
        // print_r($ls_business_data);
        // die;
        $getBusinessHoursData = BusinessDailyLimit::where('business_id', $businessData['business_id'])->get()->toArray();
        $busineHoursLimitDataArr = $busineHoursPauseDataArr = $busineHoursDayDataArr = $busineHoursBudgetDataArr = $enableDayLimitArr = $enableBudgetLimitArr = $busineHoursDataArr = array();

        for ($h = 0; $h < count($getBusinessHoursData); $h++) {

            if ($getBusinessHoursData[$h]['day'] == 7) {
                $keyName = "textEditSun";
                $keyNameBudget = "textbudgetEditSun";
                $keyNamePause = "checkEditFullSun";
                $keyDayCheck = "checkEditSun";
            } else if ($getBusinessHoursData[$h]['day'] == 1) {
                $keyName = "textEditMon";
                $keyNameBudget = "textbudgetEditMon";
                $keyNamePause = "checkEditFullMon";
                $keyDayCheck = "checkEditMon";
            } else if ($getBusinessHoursData[$h]['day'] == 2) {
                $keyName = "textEditTue";
                $keyNameBudget = "textbudgetEditTue";
                $keyNamePause = "checkEditFullTue";
                $keyDayCheck = "checkEditTue";
            } else if ($getBusinessHoursData[$h]['day'] == 3) {
                $keyName = "textEditWed";
                $keyNameBudget = "textbudgetEditWed";
                $keyNamePause = "checkEditFullWed";
                $keyDayCheck = "checkEditWed";
            } else if ($getBusinessHoursData[$h]['day'] == 4) {
                $keyName = "textEditThu";
                $keyNameBudget = "textbudgetEditThu";
                $keyNamePause = "checkEditFullThu";
                $keyDayCheck = "checkEditThu";
            } else if ($getBusinessHoursData[$h]['day'] == 5) {
                $keyName = "textEditFri";
                $keyNameBudget = "textbudgetEditFri";
                $keyNamePause = "checkEditFullFri";
                $keyDayCheck = "checkEditFri";
            } else if ($getBusinessHoursData[$h]['day'] == 6) {
                $keyName = "textEditSat";
                $keyNameBudget = "textbudgetEditSat";
                $keyNamePause = "checkEditFullSat";
                $keyDayCheck = "checkEditSat";
            }
            $busineHoursLimitDataArr[$keyName] = $getBusinessHoursData[$h]['daily_limit'];
            $busineHoursPauseDataArr[$keyNamePause] = $getBusinessHoursData[$h]['is_pause'];
            $busineHoursBudgetDataArr[$keyNameBudget] = $getBusinessHoursData[$h]['budget_limit'];
            $busineHoursDayDataArr[$keyDayCheck] = 0;
            $enableTextBox = 0;
            if ($getBusinessHoursData[$h]['budget_limit'] > 0 || $getBusinessHoursData[$h]['daily_limit'] > 0) {
                $enableTextBox = 1;
            }
            $busineHoursDayDataArr[$keyDayCheck] = $enableDayLimitArr[$keyName] = $enableBudgetLimitArr[$keyNameBudget] = $enableTextBox;
        }

        $busineHoursDataArr['limit'] = $busineHoursLimitDataArr;
        $busineHoursDataArr['pause'] = $busineHoursPauseDataArr;
        $busineHoursDataArr['day'] = $busineHoursDayDataArr;
        $busineHoursDataArr['budget'] = $busineHoursBudgetDataArr;
        $busineHoursDataArr['dayLimit'] = $enableDayLimitArr;
        $busineHoursDataArr['budgetLimit'] = $enableBudgetLimitArr;
        $mstBuyerTypeList = MstBuyerType::get();
        $lsBusinessList = LSBusinessList::get();
        $tlmBusinessList = LMBusinessList::get();

        $subscriptionPlanDetail = MstSubscriptionPlan::get();
        $subscriptionPlanArray = [];
        foreach($subscriptionPlanDetail as $subscriptionPlan) {
            $subscriptionPlanArray[$subscriptionPlan->subscription_plan_id] = $subscriptionPlan->subscription_plan;
        }
        $subscriptionDetail = Subscription::where('subscription_start', '>=', date('Y-m-01'))->where('subscription_end', '<=', date('Y-m-t'))->where('is_active', 'yes')->where('business_id', $businessId)->first();

        return view("businesses.edit", compact('bid', 'businessId', 'businessData', 'businessList', 'mstBuyerTypeList', 'noncompetevalueselected', 'noncompetevalueselected_name', 'ls_business_data', 'busineHoursDataArr', 'lsBusinessList', 'tlmBusinessList','tlm_business_data', 'subscriptionPlanArray', 'subscriptionDetail'));
    }

    public function gethours(Request $request)
    {
        // dd( $request->all());
        $status = 0;
        $message = 'fail';
        $busineHoursDataArr = array();
        try {

            $businessData = Business::with(['businessDailytLimit'])->where('business_id', $request['businessId'])->first()->toArray();
            // dd(count($businessData['business_dailyt_limit']));
            if (count($businessData['business_dailyt_limit']) > 0) {
                $getBusinessHoursData = BusinessDailyLimit::where('business_id', $businessData['business_id'])->get()->toArray();
                $busineHoursLimitDataArr = $busineHoursPauseDataArr = $busineHoursDayDataArr = $busineHoursBudgetDataArr = $enableDayLimitArr = $enableBudgetLimitArr = $busineHoursDataArr = array();

                for ($h = 0; $h < count($getBusinessHoursData); $h++) {

                    if ($getBusinessHoursData[$h]['day'] == 7) {
                        $keyName = "textEditSun";
                        $keyNameBudget = "textbudgetEditSun";
                        $keyNamePause = "checkEditFullSun";
                        $keyDayCheck = "checkEditSun";
                    } else if ($getBusinessHoursData[$h]['day'] == 1) {
                        $keyName = "textEditMon";
                        $keyNameBudget = "textbudgetEditMon";
                        $keyNamePause = "checkEditFullMon";
                        $keyDayCheck = "checkEditMon";
                    } else if ($getBusinessHoursData[$h]['day'] == 2) {
                        $keyName = "textEditTue";
                        $keyNameBudget = "textbudgetEditTue";
                        $keyNamePause = "checkEditFullTue";
                        $keyDayCheck = "checkEditTue";
                    } else if ($getBusinessHoursData[$h]['day'] == 3) {
                        $keyName = "textEditWed";
                        $keyNameBudget = "textbudgetEditWed";
                        $keyNamePause = "checkEditFullWed";
                        $keyDayCheck = "checkEditWed";
                    } else if ($getBusinessHoursData[$h]['day'] == 4) {
                        $keyName = "textEditThu";
                        $keyNameBudget = "textbudgetEditThu";
                        $keyNamePause = "checkEditFullThu";
                        $keyDayCheck = "checkEditThu";
                    } else if ($getBusinessHoursData[$h]['day'] == 5) {
                        $keyName = "textEditFri";
                        $keyNameBudget = "textbudgetEditFri";
                        $keyNamePause = "checkEditFullFri";
                        $keyDayCheck = "checkEditFri";
                    } else if ($getBusinessHoursData[$h]['day'] == 6) {
                        $keyName = "textEditSat";
                        $keyNameBudget = "textbudgetEditSat";
                        $keyNamePause = "checkEditFullSat";
                        $keyDayCheck = "checkEditSat";
                    }
                    $busineHoursLimitDataArr[$keyName] = $getBusinessHoursData[$h]['daily_limit'];
                    $busineHoursPauseDataArr[$keyNamePause] = $getBusinessHoursData[$h]['is_pause'];
                    $busineHoursBudgetDataArr[$keyNameBudget] = $getBusinessHoursData[$h]['budget_limit'];
                    $busineHoursDayDataArr[$keyDayCheck] = 0;
                    $enableTextBox = 0;
                    if ($getBusinessHoursData[$h]['budget_limit'] > 0 || $getBusinessHoursData[$h]['daily_limit'] > 0) {
                        $enableTextBox = 1;
                    }
                    $busineHoursDayDataArr[$keyDayCheck] = $enableDayLimitArr[$keyName] = $enableBudgetLimitArr[$keyNameBudget] = $enableTextBox;
                }

                $busineHoursDataArr['limit'] = $busineHoursLimitDataArr;
                $busineHoursDataArr['pause'] = $busineHoursPauseDataArr;
                $busineHoursDataArr['day'] = $busineHoursDayDataArr;
                $busineHoursDataArr['budget'] = $busineHoursBudgetDataArr;
                $busineHoursDataArr['dayLimit'] = $enableDayLimitArr;
                $busineHoursDataArr['budgetLimit'] = $enableBudgetLimitArr;
                $status = 1;
                $message = 'Success';
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        // echo '<pre>';
        // print_r($busineHoursDataArr );
        // die;
        $responseData = [
            'status' => $status,
            'message' => $message,
            'data' => $busineHoursDataArr
        ];

        return response()->json($responseData);
    }

    public function updateBusiness(Request $request)
    {
        $requestData = $request->all();
        $status = 0;
        $message = 'fail';
        $error = array();
        $logDetails = '';
        $logUpdateArr = [];
        $finalPasswordValue = '';
        $businessesId = $request->get('businessesId');
        $rules = array(
            'ownerName' => 'required|max:50',
            'contactName' => 'required|max:50',
            'businessName' => 'required|max:50',
            'displayName' => 'required|max:50',
            // old code with unique business email
            // 'businessName' => ['max:50|required', Rule::unique('business', 'business_name')->ignore($request->get('businessesId'), 'business_id')],
            'address' => 'required',
            'licence_number' => 'max:50',
            'dotNumber' => 'max:50',
            'businessEmail' => 'required|email|max:50|regex:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',
            'businessPhone' => 'required|digits:10',
            'relyNumber' => 'nullable|digits:10',
            'emergencyPhone' => 'nullable|digits:10',
            'forwardNumber' => 'nullable|digits:10',
            'company_url' => 'nullable|url',
            // 'company_url' => 'regex:/\b(?:(?:https?|ftp):\/\/|www\.)[-a-z0-9+&@#\/%?=|!:,.;]*[-a-z0-9+&@#\/%=|]/i'],

            //'businessLoginEmail' => 'required|max:50|email|regex:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',
            // old code with unique business email
            'businessLoginEmail' => 'required|max:50|email|regex:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/|unique:business,movers_username,' . $businessesId . ',business_id',
        );
        $input = $request->only(['ownerName', 'contactName', 'businessName', 'displayName', 'address', 'businessEmail', 'businessPhone', 'relyNumber', 'emergencyPhone', 'forwardNumber', 'businessLoginEmail','company_url']);

        $validator = Validator::make($input, $rules);
        if ($validator->fails()) {
            $error =  $validator->getMessageBag()->toArray();
        } else {
            $businessesId = $request->get('businessesId');
            $buyerType = $request->get('buyerType');
            $paymentType = $request->get('paymentType');
            $ownerName = $request->get('ownerName');
            $contactName = $request->get('contactName');
            $businessName = $request->get('businessName');
            $displayName = $request->get('displayName');
            $address = $request->get('address');
            $dotNumber = $request->get('dotNumber');
            $licence_number = $request->get('licence_number');
            $businessEmail = $request->get('businessEmail');
            // $businessNotificationEmail = $request->get('businessNotificationEmail');
            $businessPhone = $request->get('businessPhone');
            $company_url = $request->get('company_url');
            // $businessNotificationPhone = $request->get('businessNotificationPhone');
            $emergencyPhone = $request->get('emergencyPhone');
            $relyNumber = $request->get('relyNumber');
            $forwardNumber = $request->get('forwardNumber');
            $businessLoginEmail = $request->get('businessLoginEmail');
            $businessLoginPassword = $request->get('businessLoginPassword');
            $oldBusinessPassword = $request->get('oldBusinessPassword');
            $businessStatus = $request->get('businessStatus');
            $noncompetevalues = $request->get('business_toggle');
            if (array_key_exists("business_toggle", $requestData)) {
                if (count($noncompetevalues) > 0) {
                    $noncompetevalues = array_unique($noncompetevalues);
                    $noncompetevalues = array_values($noncompetevalues);
                }
            }
            $noncompetevalues_ls = $request->get('business_toggle_ls');
            $noncompetevalues_tlm = $request->get('business_toggle_tlm');
            if (array_key_exists("business_toggle_ls", $requestData)) {
                if (count($noncompetevalues_ls) > 0) {
                    $noncompetevalues_ls = array_unique($noncompetevalues_ls);
                    $noncompetevalues_ls = array_values($noncompetevalues_ls);
                }
            }
            if (array_key_exists("business_toggle_tlm", $requestData)) {
                if (count($noncompetevalues_tlm) > 0) {
                    $noncompetevalues_tlm = array_unique($noncompetevalues_tlm);
                    $noncompetevalues_tlm = array_values($noncompetevalues_tlm);
                }
            }
            $errorArr = [];
            // Businesses Phone
            $BusinessesPhoneCheck = array_unique(str_split($businessPhone));
            if (count($BusinessesPhoneCheck) === 1 && ($businessPhone != '')) {
                $errorArr['businessPhone'][0] = "Same number repeated in businesses phone.";
            }
            // Businesses Notification Phone
            // $BusinessesNotificationPhoneCheck = array_unique(str_split($businessNotificationPhone));
            // if (count($BusinessesNotificationPhoneCheck) === 1) {
            //     $errorArr['businessNotificationPhone'][0] = "Same number repeated in businesses notification phone.";
            // }
            // emergency Phone
            $EmergencyPhoneCheck = array_unique(str_split($emergencyPhone));
            if (count($EmergencyPhoneCheck) === 1 && ($emergencyPhone != '')) {
                $errorArr['emergencyPhone'][0] = "Same number repeated in contact phone.";
            }
            // rely Phone
            // $InterfacePhoneCheck = array_unique( str_split($relyNumber));
            // if( count($InterfacePhoneCheck) === 1 ) {
            //     $errorArr['relyNumber'][0] = "Same number repeated in rely phone.";
            // }
            // forward Phone
            $ForwardnumberPhoneCheck = array_unique(str_split($forwardNumber));
            if (count($ForwardnumberPhoneCheck) === 1 && ($forwardNumber != '')) {
                $errorArr['forwardNumber'][0] = "Same number repeated in forward number.";
            }

            $oldBusinessesData = Business::with(['mstBuyerType'])->where('business_id', '=', $businessesId)->first();
            if (empty($oldBusinessPassword) || $oldBusinessPassword == null) {
                if (empty($businessLoginPassword) || $businessLoginPassword == null) {
                    $errorArr['businessLoginPassword'][0] = 'The business login password is required';
                } else if (strlen($businessLoginPassword) < 8) {
                    $errorArr['businessLoginPassword'][0] = 'The business login password must be at least 8 characters.';
                } else if (strlen($businessLoginPassword) > 10) {
                    $errorArr['businessLoginPassword'][0] = 'The business login password must not be greater than 10 characters.';
                } else {
                    $finalPasswordValue = $businessLoginPassword;
                    // $mailBodyData['email'] = $txtloginemail;
                    // $mailBodyData['password'] = $finalPasswordValue;
                    // $mailBodyData['businessName'] = $business_name;
                    // $mailBodyData['contactName'] = $contact_name;
                    // send mail for reset business login password email
                    // $pageContent = $this->resetPasswordMailContents($mailBodyData);
                    // $mailBodyData = array();
                    // $mail = new TemplateEmail();
                    // $resetEmailSendOn = "<EMAIL>";
                    // $resetEmailSendOn = "<EMAIL>";
                    // $mail->setTo($resetEmailSendOn);
                    // $mail->setFrom('<EMAIL>');
                    // $mail->setSubject('Reset Password : '.$business_name);
                    // $postmarkToken = Helper::checkServer()['postmark_token'];
                    // $mail->setHtmlbody($pageContent); // set body content
                    // $sentEmail = $mail->send_email($token = $postmarkToken, '', $businessesId);
                }
            } else {
                $finalPasswordValue = $oldBusinessPassword;
                if (!empty($businessLoginPassword)) {
                    $finalPasswordValue = $businessLoginPassword;
                    // $mailBodyData['email'] = $txtloginemail;
                    // $mailBodyData['password'] = $finalPasswordValue;
                    // $mailBodyData['businessName'] = $business_name;
                    // $mailBodyData['contactName'] = $contact_name;
                    // send mail for reset business login password email
                    // $pageContent = $this->resetPasswordMailContents($mailBodyData);
                    // $mailBodyData = array();
                    // $mail = new TemplateEmail();
                    // $resetEmailSendOn = "<EMAIL>";
                    // $resetEmailSendOn = "<EMAIL>";
                    // $mail->setTo($resetEmailSendOn);
                    // $mail->setFrom('<EMAIL>');
                    // $mail->setSubject('Reset Password : '.$business_name);
                    // $postmarkToken = Helper::checkServer()['postmark_token'];
                    // $mail->setHtmlbody($pageContent); // set body content
                    // $sentEmail = $mail->send_email($token = $postmarkToken, '', $businessesId);
                }
            }
            if (isset($request['logo']) && !empty($request['logo']) && $request['logo'] != 'undefined') {
                $file = $request->file('logo');
                $allowedExtension = ['jpg','jpeg','png','gif'];
                $extension = $file->getClientOriginalExtension();
                if(!in_array($extension,$allowedExtension)){
                    $errorArr['logo'][0] = 'The logo must be an image and must be a file of type: jpeg, jpg, png, gif';
                }
            }
            if (count($errorArr) > 0) {
                $responseData = ['status' => $status, 'message' => $message, 'error' => $errorArr];
                return response()->json($responseData);
            }

            Business::where('business_id', $businessesId)
                ->update([
                    'business_name' => $businessName,
                    'display_name' => $displayName,
                    'buyer_type_id' => $buyerType,
                    'payment_type' => $paymentType,
                    'owner_name' => $ownerName,
                    'contact_name' => $contactName,
                    'license_number' => $licence_number,
                    'dot_number' => $dotNumber,
                    'address' => htmlentities($address),
                    'email' => $businessEmail,
                    // 'notification_email' => $businessNotificationEmail,
                    'business_phone' => $businessPhone,
                    'company_url' => $company_url,
                    // 'notification_phone' => $businessNotificationPhone,
                    'emergency_phone' => $emergencyPhone,
                    'rely_number' => $relyNumber,
                    'forward_number' => $forwardNumber,
                    'status' => $businessStatus,
                    // 'low_fund_notification_email' => $businessNotificationEmail,
                    // 'low_fund_notification_phone' => $businessNotificationPhone,
                    'movers_username' => $businessLoginEmail,
                    'movers_password' => Hash::make($finalPasswordValue),
                    'updated_at' => date('Y-m-d H:i:s'),
                    'updated_user_id' => Auth::user()->id,
                ]);
            // logo upload logic start

            if (isset($request['logo']) && !empty($request['logo']) && $request['logo'] != 'undefined') {
                // delete file old
                $path = public_path();
                $destinationPath = $path . '/images/businesslogo';
                $business = Business::findOrFail($businessesId);
                if (isset($business->logo) && !empty($business->logo)) {
                    $filePath = $destinationPath . '/' . $business->logo;
                    if (file_exists($filePath)) {
                        unlink($filePath);
                    }
                }
                $file = $request->file('logo');
                $filename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
                $filename = strlen($filename) > 20 ? substr($filename, 0, 20) : $filename;
                $uniqueSuffix = substr(md5(time()), 0, 10);
                $logo = $filename . '_' . $uniqueSuffix . '.webp';
                // $split = explode(".", $filename);
                // $logo = $split[0] . time() . '.webp';
                // $logo = $split[0] . time() . '.' . $split[1];
                $fname = str_replace(' ', '_', $logo);
                // $file->move($destinationPath, $fname);
                try {
                    $imagick = new \Imagick($file->getRealPath());
                    $imagick->setImageFormat('webp');
                    $imagick->setImageCompression(\Imagick::COMPRESSION_JPEG);
                    $imagick->setImageCompressionQuality(70);
                    $imagick->resizeImage(300, 0, \Imagick::FILTER_LANCZOS, 1);
                    $imagick->writeImage($destinationPath . '/' . $fname);

                    while (filesize($destinationPath . '/' . $fname) > 10240) {
                        $imagick->setImageCompressionQuality($imagick->getImageCompressionQuality() - 10);
                        $imagick->writeImage($destinationPath . '/' . $fname);
                    }

                    $imagick->clear();
                    $imagick->destroy();
                } catch (\ImagickException $e) {
                    $webpImage = Webp::make($file);
                    $webpImage->save($destinationPath . '/' . $fname, 5);
                }

                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'logo',
                    'old_value' => $business->logo,
                    'new_value' => $fname,
                    'created_at' => date('Y-m-d H:i:s')
                ];
                Business::where('business_id', $businessesId)
                    ->update([
                        'logo' => $fname
                    ]);

                $path = public_path('/images/businesslogo');
                $originalImageFile = $path . '/' . $fname;
                $uploadedFile = new UploadedFile($originalImageFile, $fname);

                $logo = 'data:image/webp;base64,' . base64_encode(file_get_contents($uploadedFile));
                $postFields = json_encode(array('business_id' => $businessesId, 'filename' => $fname, 'image' => $logo));
                $url        = Helper::checkServer()['vm_url'] . '/storecompanylogo';
                $ch         = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_exec($ch);
                if (curl_errno($ch)) {
                    echo 'Error:' . curl_error($ch);
                    exit;
                }
                curl_close($ch);
            }
            // End logo upload logic end
            $updatedData = Business::with(['mstBuyerType'])->where('business_id', $businessesId)->first();
            if (!empty($buyerType) && $oldBusinessesData->buyer_type_id != $buyerType) {
                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'buyer_type_id',
                    'old_value' => $oldBusinessesData->mstBuyerType->buyer_type_name,
                    'new_value' => $updatedData->mstBuyerType->buyer_type_name,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if (!empty($paymentType) && $oldBusinessesData->payment_type != $paymentType) {
                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'payment_type',
                    'old_value' => $oldBusinessesData->payment_type,
                    'new_value' => $paymentType,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if (!empty($ownerName) && $oldBusinessesData->owner_name != $ownerName) {
                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'owner_name',
                    'old_value' => $oldBusinessesData->owner_name,
                    'new_value' => $ownerName,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if (!empty($contactName) && $oldBusinessesData->contact_name != $contactName) {
                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'contact_name',
                    'old_value' => $oldBusinessesData->contact_name,
                    'new_value' => $contactName,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if (!empty($businessName) && $oldBusinessesData->business_name != $businessName) {
                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'business_name',
                    'old_value' => $oldBusinessesData->business_name,
                    'new_value' => $businessName,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if (!empty($displayName) && $oldBusinessesData->display_name != $displayName) {
                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'display_name',
                    'old_value' => $oldBusinessesData->display_name,
                    'new_value' => $displayName,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if (!empty($licence_number) && $oldBusinessesData->license_number != $licence_number) {
                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'license_number',
                    'old_value' => $oldBusinessesData->license_number,
                    'new_value' => $licence_number,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if (!empty($dotNumber) && $oldBusinessesData->dot_number != $dotNumber) {
                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'dot_number',
                    'old_value' => $oldBusinessesData->dot_number,
                    'new_value' => $dotNumber,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if (!empty($address) && $oldBusinessesData->address != $address) {
                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'address',
                    'old_value' => $oldBusinessesData->address,
                    'new_value' => $address,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if (!empty($businessEmail) && $oldBusinessesData->email != $businessEmail) {
                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'email',
                    'old_value' => $oldBusinessesData->email,
                    'new_value' => $businessEmail,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            // if (!empty($businessNotificationEmail) && $oldBusinessesData->notification_email != $businessNotificationEmail) {
            //     $logUpdateArr[] = [
            //         'created_user_id' => Auth::user()->id,
            //         'business_id' => $businessesId,
            //         'field_name' => 'notification_email',
            //         'old_value' => $oldBusinessesData->notification_email,
            //         'new_value' => $businessNotificationEmail,
            //         'created_at' => date('Y-m-d H:i:s')
            //     ];
            // }
            if (!empty($businessPhone) && $oldBusinessesData->business_phone != $businessPhone) {
                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'business_phone',
                    'old_value' => $oldBusinessesData->business_phone,
                    'new_value' => $businessPhone,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if ( $oldBusinessesData->company_url != $company_url) {
                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'company_url',
                    'old_value' => $oldBusinessesData->company_url,
                    'new_value' => $company_url,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            // if (!empty($businessNotificationPhone) && $oldBusinessesData->notification_phone != $businessNotificationPhone) {
            //     $logUpdateArr[] = [
            //         'created_user_id' => Auth::user()->id,
            //         'business_id' => $businessesId,
            //         'field_name' => 'notification_phone',
            //         'old_value' => $oldBusinessesData->notification_phone,
            //         'new_value' => $businessNotificationPhone,
            //         'created_at' => date('Y-m-d H:i:s')
            //     ];
            // }
            if (!empty($emergencyPhone) && $oldBusinessesData->emergency_phone != $emergencyPhone) {
                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'contact_phone',
                    'old_value' => $oldBusinessesData->emergency_phone,
                    'new_value' => $emergencyPhone,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if (!empty($relyNumber) && $oldBusinessesData->rely_number != $relyNumber) {
                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'rely_number',
                    'old_value' => $oldBusinessesData->rely_number,
                    'new_value' => $relyNumber,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if (!empty($forwardNumber) && $oldBusinessesData->forward_number != $forwardNumber) {
                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'forward_number',
                    'old_value' => $oldBusinessesData->forward_number,
                    'new_value' => $forwardNumber,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if (!empty($businessLoginEmail) && $oldBusinessesData->movers_username != $businessLoginEmail) {
                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'movers_username',
                    'old_value' => $oldBusinessesData->movers_username,
                    'new_value' => $businessLoginEmail,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }

            if (!empty($businessStatus) && $oldBusinessesData->status != $businessStatus) {
                $oldStatus = $newStatus = "";
                if ($oldBusinessesData->status == 'active') {
                    $oldStatus = "Active";
                } else if ($oldBusinessesData->status == 'pause') {
                    $oldStatus = "Pause";
                } else if ($oldBusinessesData->status == 'inactive') {
                    $oldStatus = "Inactive";
                }
                if ($businessStatus == 'active') {
                    $newStatus = "Active";
                } else if ($businessStatus == 'pause') {
                    $newStatus = "Pause";
                } else if ($businessStatus == 'inactive') {
                    $newStatus = "Inactive";
                }
                $logUpdateArr[] = [
                    'created_user_id' => Auth::user()->id,
                    'business_id' => $businessesId,
                    'field_name' => 'status',
                    'old_value' => $oldStatus,
                    'new_value' => $newStatus,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }

            // For Business Partners
            BusinessPartnerMapping::where('business_id', $businessesId)->delete();
            BusinessPartnerMapping::where('business_partner_id', $businessesId)->delete();
            // echo "<pre>noncompetevalues = ";
            // print_r(json_encode($noncompetevalues));
            // die;
            // for partner business
            $businessPartnerMappingdata = BusinessUpdateLog::where('business_id', $businessesId)->where('field_name', 'partner_business')->orderBy('business_update_log_id', 'DESC')->pluck('new_value')->toArray();

            $oldPartners = NULL;
            if (count($businessPartnerMappingdata) > 0) {
                $oldPartners = $businessPartnerMappingdata[0];
            }
            if( isset($oldPartners) || isset($noncompetevalues)){
                if (!empty($noncompetevalues) ) {
                    foreach ($noncompetevalues as $noncompetevaluesEditkey => $noncompetevaluesEditvalue) {
                        if ($businessesId == $noncompetevaluesEditvalue) {
                            continue;
                        }
                        BusinessPartnerMapping::create([
                            'business_id' => $businessesId,
                            'business_partner_id' => $noncompetevaluesEditvalue,
                            'created_at' => date('Y-m-d H:i:s')
                        ]);
                        BusinessPartnerMapping::create([
                            'business_id' => $noncompetevaluesEditvalue,
                            'business_partner_id' => $businessesId,
                            'created_at' => date('Y-m-d H:i:s')
                        ]);
                    }
                }
                if ($oldPartners != json_encode($noncompetevalues)) {
                    $logUpdateArr[] = [
                        'created_user_id' => Auth::user()->id,
                        'business_id' => $businessesId,
                        'field_name' => 'partner_business',
                        'old_value' => $oldPartners,
                        'new_value' => ($noncompetevalues != null) ? json_encode($noncompetevalues) : NULL,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
            }
            // for ls partner business
            $old_ls_partner_business = BusinessUpdateLog::where('business_id', $businessesId)->where('field_name', 'ls_partner_business')->orderBy('business_update_log_id', 'DESC')->pluck('new_value')->toArray();
            $old_ls_partners = NULL;
            if (count($old_ls_partner_business) > 0) {
                $old_ls_partners = $old_ls_partner_business[0];
            }
            if( isset($old_ls_partners) || isset($noncompetevalues_ls)){
                if ($old_ls_partners != json_encode($noncompetevalues_ls)) {
                    $logUpdateArr[] = [
                        'created_user_id' => Auth::user()->id,
                        'business_id' => $businessesId,
                        'field_name' => 'ls_partner_business',
                        'old_value' => $old_ls_partners,
                        'new_value' => ($noncompetevalues_ls != null) ? json_encode($noncompetevalues_ls) : NULL,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
            }
            // for lm partner business
            $old_lm_partner_business = BusinessUpdateLog::where('business_id', $businessesId)->where('field_name', 'tlm_partner_business')->orderBy('business_update_log_id', 'DESC')->pluck('new_value')->toArray();
            $old_lm_partners = NULL;
            if (count($old_lm_partner_business) > 0) {
                $old_lm_partners = $old_lm_partner_business[0];
            }
            if( isset($old_lm_partners) || isset($noncompetevalues_tlm)){
                if ($old_lm_partners != json_encode($noncompetevalues_tlm)) {
                    $logUpdateArr[] = [
                        'created_user_id' => Auth::user()->id,
                        'business_id' => $businessesId,
                        'field_name' => 'tlm_partner_business',
                        'old_value' => $old_lm_partners,
                        'new_value' => ($noncompetevalues_tlm != null) ? json_encode($noncompetevalues_tlm) : NULL,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
            }
            // edit ls partners
            /*$getLSBusinessListIds = [];
            if(!empty($noncompetevalues_ls) && count($noncompetevalues_ls) > 0){
                $getLSBusinessListIds = LSBusinessList::whereIn('ls_business_list_id', $noncompetevalues_ls)->pluck('ls_business_id')->toArray();
            }*/

            // edit tlm partners
            $getTLMBusinessListIds = [];
            if(!empty($noncompetevalues_tlm) && count($noncompetevalues_tlm) > 0){
                $getTLMBusinessListIds = LMBusinessList::whereIn('lm_business_list_id', $noncompetevalues_tlm)->pluck('lm_business_id')->toArray();
            }

            $getNewTLMBusinessListIds = LMBusinessList::where(function ($query) use ($businessPhone, $emergencyPhone, $forwardNumber, $businessEmail, $businessLoginEmail, $address, $dotNumber){
                $query->where('lm_business_phone', $businessPhone)->orWhere('lm_emergency_phone', $emergencyPhone)->orWhere('lm_forward_number', $forwardNumber)
                    ->orWhere('lm_email', $businessEmail)->orWhere('lm_movers_username', $businessLoginEmail)
                    ->orWhere('lm_address', strtolower(str_replace(' ', '', $address)))->orWhere('lm_dot_number', $dotNumber);
            })->pluck('lm_business_id')->toArray();

            $getTLMBusinessListIds = array_merge($getTLMBusinessListIds, $getNewTLMBusinessListIds);

            $checkToken = MstBusinessToken::first();
            $token      = "56as342dfKdfds789JKasd2sdfsd";
            if (!empty($checkToken) ) {
                $token = $checkToken->token;
            }
            $noncompetevalues[] = $businessesId;
            /*// fo ls
            if (count($getLSBusinessListIds) > 0) {
                $postData = ["token" => $token, 'pm_company_id' => $noncompetevalues, 'ls_company_id' => $getLSBusinessListIds];
                $this->getLSpartneredBusiness(json_encode($postData));
                $this->saveLSBusinesstoPM(json_encode($postData));
            }*/

            // fo tlm
            if (count($getTLMBusinessListIds) > 0) {
                $postData = ["token" => $token, 'pm_company_id' => $noncompetevalues, 'lm_company_id' => $getTLMBusinessListIds];
                $this->getTLMpartneredBusiness(json_encode($postData));
                $this->saveTLMBusinesstoPM(json_encode($postData));
            }

            $businessesHoursArr = $this->insertBusinessHoursData($businessesId, $request, 1);
            if ($businessesHoursArr != null) {
                if (count($businessesHoursArr) > 0) {
                    $logUpdateArr[] = $businessesHoursArr[0];
                }
            }
            if (count($logUpdateArr) > 0) {
                BusinessUpdateLog::insert($logUpdateArr);
            }
            $notification_status = "no";
            if ($businessStatus == 'active') {
                $notification_status = "yes";
            }
            Business::where('business_id', $businessesId)->update(['is_low_fund_notification' => $notification_status]);
            $businessModuleId = BusinessModule::insertGetId([
                'business_module_name' => 'Business',
                'description' => "Business Level changes",
                'created_at' => date('Y-m-d H:i:s'),
            ]);
            $status = 1;
            $message = 'Success';
        }
        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => $error
        ];
        return response()->json($responseData);
    }

    public function getCampaigns(Request $request)
    {
        header('Access-Control-Allow-Origin: *');
        $status = 0;
        $message = 'fail';
        $data = $campIdArr = array();
        try {
            $businessesId = $request->get('bid');
            $businessesCampaignDetails = Campaign::with('mstCampaignType', 'mstLeadType', 'mstLeadCategory', 'campaignMoving')->where('business_id', $businessesId)->orderBy('campaign_id', 'desc')->get();
            //echo "<pre>";print_r($businessesCampaignDetails);die;
            if ($businessesCampaignDetails->isEmpty()) {
                $status = 0;
            } else {
                $ymddate = date('Y-m-d');
                $ymddate = '%' . $ymddate . '%';
                $endDecObj = new CommonFunctions;
                foreach ($businessesCampaignDetails as $key1 => $campaignId) {
                    if(!in_array($campaignId->campaign_id,$campIdArr)){
                        $campIdArr[] = $campaignId->campaign_id;
                    }
                }
                $campDateArr = array();
                if(count($campIdArr) > 0){
                    $leads_countquery = LeadRouting::select('campaign_id', DB::raw('MAX(created_at) AS lastlead'))->whereIn('campaign_id', $campIdArr)->where('route_status', 'sold')->orderBy('campaign_id', 'desc')->groupBy("campaign_id")->get()->toArray();

                    for($cl=0;$cl<count($leads_countquery);$cl++){
                        $campDateArr[$leads_countquery[$cl]['campaign_id']] = array('lastleaddate' => $leads_countquery[$cl]['lastlead'], "totallead" => 0);
                    }
                    $startdate = date('Y-m-d')." 00:00:00";
                    $enddate = date('Y-m-d')." 23:59:59";
                    $totalleadcamp_data = LeadRouting::select('campaign_id', DB::raw('COUNT(lead_routing_id) AS total_lead'))->whereIn('campaign_id', $campIdArr)->where('route_status', 'sold')->whereBetween('created_at', [$startdate, $enddate])->orderBy('campaign_id', 'desc')->groupBy("campaign_id")->get()->toArray();
                    foreach ($totalleadcamp_data as $campkey => $campaign) {
                        $campDateArr[$campaign['campaign_id']]['totallead'] = $campaign['total_lead'];
                    }
                }
                //   echo "<pre>";print_r($campDateArr);die;
                foreach ($businessesCampaignDetails as $key => $campaign) {
                    $leads_count = 0;
                    $lastLeadDate = "---";
                    if(isset($campDateArr[$campaign->campaign_id])){
                        $leads_count = $campDateArr[$campaign->campaign_id]['totallead'];
                        $lastLeadDate = date("m/d/Y", strtotime($campDateArr[$campaign->campaign_id]['lastleaddate']));
                    }
                    $campaign->leads = $leads_count;
                    $campaign->lastLeadDate = $lastLeadDate;
                    $campaign->enc_id = $endDecObj->customeEncryption($campaign->campaign_id);
                }
                $status = 1;
                $message = 'success';
                $data = $businessesCampaignDetails;
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        $jsonResponse = [
            'status' => $status,
            'message' => $message,
            'data' => $data
        ];

        return response()->json($jsonResponse);
    }

    public function updateBusinessStatus(Request $request)
    {
        $input = $request->all();
        $status = 0;
        $message = 'fail';
        $error = array();
        try {
            $endDecObj = new CommonFunctions;
            $bId = $endDecObj->customeDecryption($request->input('bId'));
            if ($bId > 0) {
                $status = 1;
                $message = 'Success';
                $updateStatus = "no";
                if ($request->input('notification_status') == "no") {
                    $updateStatus = "yes";
                }
                $updateArr = array();
                $updateArr['is_low_fund_notification']  = $updateStatus;
                $updateArr['updated_at']  = date("Y-m-d H:i:s");
                Business::where('business_id', $bId)->update($updateArr);
            }
            $responseData = ['status' => $status, 'message' => $message, 'error' => $error];
            return response()->json($responseData);
        } catch (Exception $ex) {
            return redirect('/business-list');
        }
    }

    public function updateCampaignStatus(Request $request)
    {
        $input = $request->all();
        $status = 0;
        $message = 'fail';
        $error = $logArray = array();
        try {
            $endDecObj = new CommonFunctions;
            $cId = $endDecObj->customeDecryption($request->input('cId'));
            if ($cId > 0) {
                $campaignDetail = Campaign::where('campaign_id', $cId)->first();

                $status = 1;
                $message = 'Success';
                $updateStatus = "no";
                if ($request->input('status') == "no") {
                    $updateStatus = "yes";
                }
                $updateArr = array();
                $updateArr['is_active']  = $updateStatus;
                $updateArr['updated_at']  = date("Y-m-d H:i:s");
                Campaign::where('campaign_id', $cId)->update($updateArr);

                //Added log by BK on 21/07/2023
                if ($updateStatus != $campaignDetail->is_active && !empty($updateStatus)) {
                    $logArray[] = [
                        'created_user_id' => Auth::user()->id,
                        'campaign_id' => $cId,
                        'field_name' => 'is_active',
                        'old_value' => $campaignDetail->is_active,
                        'new_value' => $updateStatus,
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $request->ip()
                    ];
                }
                if (@count($logArray) > 0) {
                    CampaignUpdateLog::insert($logArray);
                }
            }
            $responseData = ['status' => $status, 'message' => $message, 'error' => $error];
            return response()->json($responseData);
        } catch (Exception $ex) {
            return redirect('/business-list');
        }
    }

    public function logBusiness($bid, Request $request)
    {
        $requestData = $request->all();
        $endDecObj = new CommonFunctions;
        $businessId = $endDecObj->customeDecryption($bid);
        $businessData = Business::with(['mstBuyerType', 'businessDailytLimit'])->where('business_id', $businessId)->first()->toArray();

        /*$sDate = $request->get('sDate');
        $eDate = $request->get('eDate');*/
        $daterange = $request->get('daterange');
        $userId = $request->get('select_user');
        $page = $request->get('page');
        $select_section = $request->get('select_section');
        $log_search = $request->get('log_search');
        $clear_search = $request->get('clear_search');

        if (isset($log_search) && $log_search == 1) {
            $dateRangeArray = explode(' - ', $daterange);
            $sDate = $dateRangeArray[0];
            $eDate = $dateRangeArray[1];
        } elseif (isset($clear_search) && $clear_search == 1) {
            $sDate = NULL;
            $eDate = NULL;
            $userId = 'all';
            $select_section = 'all';
        } else {
            $t = strtotime("-7 days");
            $sDate = date("Y-m-d", $t);
            $eDate = date("Y-m-d");
        }

        $businessUpdateLogs = BusinessUpdateLog::with(['businesses', 'users'])->where('business_id', $businessId);

        $campaignUpdateLogs = CampaignUpdateLog::with(['users', 'campaigns']);
        $campaignUpdateLogs->WhereHas('campaigns', function ($query) use ($businessId) {
            $query->where('campaign.business_id', '=', $businessId);
        });

        if (isset($sDate) && $sDate != '' && isset($eDate) && $eDate != '') {
            $valStartDate = date("Y-m-d", strtotime($sDate)) . ' 00:00:00';
            $valEndDate = date("Y-m-d", strtotime($eDate)) . ' 23:59:59';
            $businessUpdateLogs->whereBetween('created_at', [$valStartDate, $valEndDate]);
            $campaignUpdateLogs->whereBetween('created_at', [$valStartDate, $valEndDate]);
        }
        if (isset($userId) && $userId != 'all' && $userId != 'company') {
            $businessUpdateLogs->where('created_user_id', $userId);
            $campaignUpdateLogs->where('created_user_id', $userId);
        } else if (isset($userId) && $userId != 'all' && $userId == 'company') {
            $businessUpdateLogs->whereNull('created_user_id');
            $campaignUpdateLogs->whereNull('created_user_id');
        }
        $businessblogs =  $businessUpdateLogs->get();
        $totalLead = $businessUpdateLogs->count();

        $campaignLogs =  $campaignUpdateLogs->get();
        foreach ($businessblogs as $key => $log) {
            $log->sec_type = "Business";
            $dateTime = strtotime($log->created_at);
            $log->date = date("m", $dateTime) . '/' . date("d", $dateTime);
            $log->time = date("h", $dateTime) . ':' . date("i", $dateTime) . ' ' . date("A", $dateTime);
            $log->action = 'Update';
            $log->id = $log->business_update_log_id;
            $log->user = ($log->created_user_id != "") ? ucwords(strtolower($log->users->name)) :  ucwords(strtolower($log->businesses->business_name));
            $log->companyname = $log->businesses->business_name;

            if ($log->field_name == 'payment_type') {
                $oldpType = NULL;
                $newpType = "-";
                if ($log->old_value == 'pre') {
                    $oldpType = "Pre Payment";
                } elseif ($log->old_value == 'post') {
                    $oldpType = "Post Payment";
                }
                if ($log->new_value == 'pre') {
                    $newpType = "Pre Payment";
                } elseif ($log->new_value == 'post') {
                    $newpType = "Post Payment";
                }
                $log->old_value = $oldpType;
                $log->new_value = $newpType;
            }
            if ($log->field_name == 'partner_business') {
                $old_partners = $log->old_value;
                if ($old_partners != NULL) {
                    $old_partners_deocde = json_decode($old_partners, true);
                    if (count($old_partners_deocde) > 0) {
                        $business_p = Business::whereIn('business_id', $old_partners_deocde)->get(['business_id', 'business_name'])->toArray();
                        $businessNamesP = [];
                        foreach ($business_p as $key => $value) {
                            $businessNamesP[] = $value['business_id'] . ' ' . $value['business_name'];
                        }
                        $log->old_value = implode(", ", $businessNamesP);
                    }
                }

                $new_partners = $log->new_value;
                if ($new_partners  != null) {
                    $new_partners_deocde = json_decode($new_partners, true);
                    if (count($new_partners_deocde) > 0) {
                        $business_p = Business::whereIn('business_id', $new_partners_deocde)->get(['business_id', 'business_name'])->toArray();
                        $businessNamesP = [];
                        // dd($business_p);
                        foreach ($business_p as $key => $value) {
                            $businessNamesP[] = $value['business_id'] . ' ' . $value['business_name'];
                        }
                        $log->new_value = implode(", ", $businessNamesP);
                    }
                }
            }
            if ($log->field_name == 'ls_partner_business') {
                $old_partners = $log->old_value;
                if ($old_partners != NULL) {
                    $old_partners_deocde = json_decode($old_partners, true);
                    if (count($old_partners_deocde) > 0) {
                        $business_p = LSBusinessList::whereIn('ls_business_id', $old_partners_deocde)->get(['ls_business_id', 'ls_business_name'])->toArray();
                        $businessNamesP = [];
                        foreach ($business_p as $key => $value) {
                            $businessNamesP[] = $value['ls_business_id'] . ' ' . $value['ls_business_name'];
                        }
                        $log->old_value = implode(", ", $businessNamesP);
                    }
                }

                $new_partners = $log->new_value;
                if ($new_partners  != null) {
                    $new_partners_deocde = json_decode($new_partners, true);
                    if (count($new_partners_deocde) > 0) {
                        $business_p = LSBusinessList::whereIn('ls_business_id', $new_partners_deocde)->get(['ls_business_id', 'ls_business_name'])->toArray();
                        $businessNamesP = [];
                        // dd($business_p);
                        foreach ($business_p as $key => $value) {
                            $businessNamesP[] = $value['ls_business_id'] . ' ' . $value['ls_business_name'];
                        }
                        $log->new_value = implode(", ", $businessNamesP);
                    }
                }
            }
            if ($log->field_name == 'tlm_partner_business') {
                $old_partners = $log->old_value;
                if ($old_partners != NULL) {
                    $old_partners_deocde = json_decode($old_partners, true);
                    if (count($old_partners_deocde) > 0) {
                        $business_p = LMBusinessList::whereIn('lm_business_id', $old_partners_deocde)->get(['lm_business_id', 'lm_business_name'])->toArray();
                        $businessNamesP = [];
                        foreach ($business_p as $key => $value) {
                            $businessNamesP[] = $value['lm_business_id'] . ' ' . $value['lm_business_name'];
                        }
                        $log->old_value = implode(", ", $businessNamesP);
                    }
                }

                $new_partners = $log->new_value;
                if ($new_partners  != null) {
                    $new_partners_deocde = json_decode($new_partners, true);
                    if (count($new_partners_deocde) > 0) {
                        $business_p = LMBusinessList::whereIn('lm_business_id', $new_partners_deocde)->get(['lm_business_id', 'lm_business_name'])->toArray();
                        $businessNamesP = [];
                        // dd($business_p);
                        foreach ($business_p as $key => $value) {
                            $businessNamesP[] = $value['lm_business_id'] . ' ' . $value['lm_business_name'];
                        }
                        $log->new_value = implode(", ", $businessNamesP);
                    }
                }
            }
            if ($log->old_value == NULL) {
                $log->action = 'Insert';
                $log->old_value = "-";
            }
            if ($log->new_value == NULL) {
                $log->new_value = "-";
            }
        }
        foreach ($campaignLogs as $key => $log) {
            $log->sec_type = "Campaign";
            $dateTime = strtotime($log->created_at);
            $log->date = date("m", $dateTime) . '/' . date("d", $dateTime);
            $log->time = date("h", $dateTime) . ':' . date("i", $dateTime) . ' ' . date("A", $dateTime);
            $log->action = 'Update';
            $log->id = $log->campaign_update_log_id;
            $log->user = ($log->created_user_id != "") ? ucwords(strtolower($log->users->name)) :  ucwords(strtolower($log->campaigns->campaign_name));
            $log->companyname = $log->campaigns->campaign_name;
            if ($log->old_value == NULL) {
                $log->action = 'Insert';
                $log->old_value = "-";
            }
            if ($log->field_name == 'business_id') {
                $log->new_value = $businessData['business_id'] . ' - ' . $businessData['business_name'];
            }
            if ($log->field_name == 'lead_category_id') {
                $campaignCategoryDataold = MstLeadCategory::where('lead_category_id', $log->old_value)->get(['lead_category_id', 'lead_category_name'])->first();
                if ($campaignCategoryDataold != NULL && isset($campaignCategoryDataold)) {
                    $log->old_value = $campaignCategoryDataold->lead_category_id . ' - ' . $campaignCategoryDataold->lead_category_name;
                }
                $campaignCategoryDatanew = MstLeadCategory::where('lead_category_id', $log->new_value)->get(['lead_category_id', 'lead_category_name'])->first();
                $log->new_value = $campaignCategoryDatanew->lead_category_id . ' - ' . $campaignCategoryDatanew->lead_category_name;
            }
            if ($log->field_name == 'lead_type_id') {
                $campaignLeadTypeold = MstLeadType::where('lead_type_id', $log->old_value)->get(['lead_type_id', 'lead_type'])->first();
                if ($campaignLeadTypeold != NULL && isset($campaignLeadTypeold)) {
                    $log->old_value = $campaignLeadTypeold->lead_type_id . ' - ' . $campaignLeadTypeold->lead_type;
                }
                $campaignLeadTypenew = MstLeadType::where('lead_type_id', $log->new_value)->get(['lead_type_id', 'lead_type'])->first();
                $log->new_value = $campaignLeadTypenew->lead_type_id . ' - ' . $campaignLeadTypenew->lead_type;
            }
            if ($log->field_name == 'campaign_type_id') {
                $campaignCampaignTypeold = MstCampaignType::where('campaign_type_id', $log->old_value)->get(['campaign_type_id', 'campaign_type'])->first();
                if ($campaignCampaignTypeold != NULL && isset($campaignCampaignTypeold)) {
                    $log->old_value = $campaignCampaignTypeold->campaign_type_id . ' - ' . $campaignCampaignTypeold->campaign_type;
                }
                $campaignCampaignTypenew = MstCampaignType::where('campaign_type_id', $log->new_value)->get(['campaign_type_id', 'campaign_type'])->first();
                $log->new_value = $campaignCampaignTypenew->campaign_type_id . ' - ' . $campaignCampaignTypenew->campaign_type;
            }
            if ($log->new_value == NULL) {
                $log->new_value = "-";
            }
        }

        if (isset($select_section) && $select_section != '' && $select_section != 'all') {
            if ($select_section == 'business') {
                $subTotalLead = $businessblogs->sortByDesc('created_at');
                $subTotalLead = count($businessblogs->sortByDesc('created_at'));
                $businessblogs = $businessblogs->sortByDesc('created_at')->paginate($this->per_page)->withQueryString();
                $totalLead = $businessblogs->count();
            } else if ($select_section == 'campaign') {
                $subTotalLead = $campaignLogs->sortByDesc('created_at');
                $subTotalLead = count($campaignLogs->sortByDesc('created_at'));
                $businessblogs = $campaignLogs->sortByDesc('created_at')->paginate($this->per_page)->withQueryString();
                $totalLead = $campaignLogs->count();
            }
        } else {
            $subTotalLead = $businessblogs->merge($campaignLogs)->sortByDesc('created_at');
            $subTotalLead = count($businessblogs->merge($campaignLogs)->sortByDesc('created_at'));
            $businessblogs = $businessblogs->merge($campaignLogs)->sortByDesc('created_at')->paginate($this->per_page)->withQueryString();
            $totalLead = $businessblogs->merge($campaignLogs)->count();
        }
        $userDetail = User::get(["id", "name"]);
        $businessDetail = Business::where("business_id", $businessId)->first();

        $data = [
            'sDate' => $sDate,
            'eDate' => $eDate,
            'finalLogData' => $businessblogs,
            'userDetail' => $userDetail,
            'businessDetail' => $businessDetail,
            'bid' => $bid,
            'pagination_limit' => $this->per_page,
            'subTotalLead' => $subTotalLead,
            'totalLead' => $totalLead,
            'select_section' => $select_section,
            'userId' => $userId
        ];
        return view("businesses.log")->with($data);
    }

    public function monthlyStatement($bid)
    {
        $endDecObj = new CommonFunctions;
        $businessId = $endDecObj->customeDecryption($bid);
        $businessDetail = Business::where("business_id", $businessId)->first();
        return view("businesses.monthlystatement", compact('bid', 'businessDetail'));
    }

    public function monthlyStatementPDF(Request $request, $id)
    {
        $rules = [
            'leadStartDate' => 'required',
            'leadEndDate' => 'required',
        ];

        $messages = [
            'leadStartDate.required' => 'Please select start date',
            'leadEndDate.required' => 'Please select end date',
        ];

        $validator = Validator::make($request->all(), $rules, $messages);
        if ($validator->fails()) {
            return back()->withInput($request->all())->withErrors($validator->errors());
        } else {
            $endDecObj = new CommonFunctions;
            $businessId = $endDecObj->customeDecryption($id);
            //$month = date("Y-m-01", time());
            //$startdate = $request->date_monthly;
            // $startdate = '2023-02-01';
            // if ($startdate == $month) {
            //     $enddate =  date("Y-m-d", time());
            // } else {
            //     $enddate =  date("Y-m-t", strtotime($startdate));
            // }

            $startdate = date('Y-m-d', strtotime($request->leadStartDate));
            $enddate = date('Y-m-d', strtotime($request->leadEndDate));
            //dump($startdate,$enddate);


            $reqBusinessesId = "";
            $reqCampaignIds = [];
            $reqCampaignIdsStr = $appendQry = "";
            if (!empty($businessId)) {
                $reqBusinessesId = $businessId;
                // Fetch campaignIds from businessesId
                $fetchCampaignIds = Campaign::where('business_id', $reqBusinessesId)->get('campaign_id');
                if (count($fetchCampaignIds) > 0) {
                    foreach ($fetchCampaignIds as $campId) {
                        $reqCampaignIds[] = $campId->campaign_id;
                        if (count($reqCampaignIds) > 0) {
                            $reqCampaignIdsStr = implode(",", $reqCampaignIds);
                            $appendQry = " AND ( business_id = '" . $reqBusinessesId . "' or campaign_id in (" . $reqCampaignIdsStr . ") )";
                        }
                    }
                } else {
                    $appendQry = " AND ( business_id = '" . $reqBusinessesId . "')";
                }
            }

            $queryStr = "SELECT * from business_campaign_payment where is_charge_approved = 'yes' and created_at between '" . $startdate . " 00:00:00' and '" . $enddate . " 23:59:59' " . $appendQry . " AND charge_type_id IN (1,7,8,9,10,2)";
            $paymentRecord = DB::select($queryStr);
            $finalData = [];
            foreach ($paymentRecord as $payment) {
                $data = [];
                $data['id'] = $payment->business_campaign_payment_id;
                $fetchBusinessesId = $payment->business_id;
                $data['business_id'] = ($payment->business_id == "") ? '--' : $payment->business_id;
                if (empty($payment->campaign_id)) {
                    $data['campaignId'] = '--';
                    $data['campaignName'] = '--';
                } else {
                    $data['campaignId'] = $payment->campaign_id;
                    $campDetails = Campaign::where('campaign_id', $payment->campaign_id)->get(['campaign_name', 'business_id']);
                    $data['campaignName'] = $campDetails[0]->campaign_name;
                    $fetchBusinessesId = $campDetails[0]->business_id;
                }
                $data['businessesName'] = '';
                if (!empty($fetchBusinessesId)) {
                    $businessesDetails = Business::where('business_id', $fetchBusinessesId)->get(['business_name']);
                    $data['businessesName'] = $businessesDetails[0]->business_name;
                }


                $data['beforeCharge'] = $payment->balance_before_charge;
                $data['amountCharge'] = $payment->amount_charge;
                $data['afterCharge'] =  $payment->balance_after_charge;
                if ($payment->charge_type_id  == 1) {
                    $data['chargeType'] = 'Credit';
                } else if ($payment->charge_type_id  == 2) {
                    $data['chargeType'] = 'Bank Deposit';
                } else if ($payment->charge_type_id  == 3) {
                    $data['chargeType'] = 'Other Debit';
                } else if ($payment->charge_type_id  == 4) {
                    $data['chargeType'] = 'Refund';
                } else if ($payment->charge_type_id  == 5) {
                    $data['chargeType'] = 'Charge Back';
                } else if ($payment->charge_type_id  == 6) {
                    $data['chargeType'] = 'Transfer From';
                } else if ($payment->charge_type_id  == 7) {
                    $data['chargeType'] = 'Recurrent';
                } else if ($payment->charge_type_id  == 8) {
                    $data['chargeType'] = 'Bad lead';
                } else if ($payment->charge_type_id  == 9) {
                    $data['chargeType'] = 'Other Credit';
                } else if ($payment->charge_type_id  == 10) {
                    $data['chargeType'] = 'Transfer to';
                } else if ($payment->charge_type_id  == 11) {
                    $data['chargeType'] = 'Void Payment';
                } else {
                    $data['chargeType'] = '--';
                }

                if ($payment->charge_method_type == 'snapituser') {
                    $dataUser = User::where('id', $payment->charge_user_id)->first();
                    $data['chargeMethod'] = (isset($dataUser) && !empty($dataUser)) ? $dataUser['name'] : "";
                } else {
                    $dataUser = Business::where('business_id', $payment->charge_user_id)->first();
                    $data['chargeMethod'] = (isset($dataUser) && !empty($dataUser)) ? $dataUser['business_name'] : "";
                }
                $time = strtotime($payment->created_at);
                $month = date("m", $time);
                $date = date("d", $time);

                $datetime = strtotime($payment->created_at);
                $hour = date("h", $datetime);
                $minute = date("i", $datetime);
                $marid = date("a", $datetime);
                $data['dateV'] = $month . '/' . $date;
                $data['timeV'] = $hour . ':' . $minute . ' ' . strtoupper($marid);
                $data['transactionId'] = empty($payment->fatt_payment_id) ? '--' : $payment->fatt_payment_id;
                $data['status'] = ($payment->is_charge_approved == 'yes') ? 'Approved' : 'Declined';

                $data['card_num'] = '';
                if ($payment->business_cc_id > 0) {
                    $cardnum = BusinessCC::where('business_cc_id', $payment->business_cc_id)->where('status', "active")->get();
                    if (isset($cardnum) && isset($cardnum[0]->id)) {
                        $data['card_num'] = 'XXXX-XXXX-XXXX-' . $cardnum[0]->last_four;
                    }
                }

                $finalData[] = $data;
            }

            $temparray = array();
            if (count($reqCampaignIds) > 0) {

                foreach ($reqCampaignIds as $campvalue) {

                    $business_campaign_fetch = Campaign::where('campaign_id', $campvalue)->first();
                    $data_camp = array();
                    $data_camp['id'] = $campvalue;
                    $data_camp['description'] = $business_campaign_fetch->campaign_name;
                    $data_camp['cost_lead'] = 0;
                    $data_camp['cost_outbound'] = 0;
                    $data_camp['cost_inbound'] = 0;
                    $totalLeadStr = "SELECT sum(payout) payout,count(lead_routing_id) as totalleads FROM lead_routing  where route_status = 'sold' and payout > 0 and ( created_at between '" . $startdate . " 00:00:00' and '" . $enddate . " 23:59:59' ) and campaign_id in (select campaign_id from campaign where lead_type_id = 1) AND campaign_id='" . $campvalue . "'";
                    $dataSummary2 = DB::select($totalLeadStr);
                    foreach ($dataSummary2 as $summary) {
                        $data_camp['cost_lead'] = $summary->payout;
                        $data_camp['lead_quantity'] = $summary->totalleads;
                        break;
                    }
                    /*$totalOutBoundStr = "SELECT sum(payout) payout,count(lead_routing_id) as totalleads FROM lead_routing  where route_status = 'sold' and payout > 0 and ( created_at between '" . $startdate . " 00:00:00' and '" . $enddate . " 23:59:59' ) and campaign_id in (select campaign_id from campaign where lead_type_id = 1)  AND campaign_id='" . $campvalue . "'";
                    $dataSummary4 = DB::select($totalOutBoundStr);*/
                    $dataSummary4 = DB::select("SELECT SUM(ltc.payout) as payout, count(ltc.lead_transfer_id) AS totalleads FROM lead_transfer_call AS ltc JOIN `lead` AS l ON l.lead_id = ltc.lead_id WHERE ltc.calls_type = 'outbound' AND ltc.transfer_type = 'transfer' AND (ltc.created_at BETWEEN '" . $startdate . " 00:00:00' AND '" . $enddate . " 23:59:59') AND ltc.campaign_id = ".$campvalue."");

                    foreach ($dataSummary4 as $summary1) {
                        $data_camp['cost_outbound'] = $summary1->payout;
                        $data_camp['outbound_quantity'] = $summary1->totalleads;
                        break;
                    }
                    /*$totalInBoundStr = "select sum(payout) payout,count(lead_call_id) as totalleads from lead_call where payout > 0 and (created_at between '" . $startdate . " 00:00:00' and '" . $enddate . " 23:59:59') and campaign_id in (select campaign_id from campaign where lead_type_id = 1)  AND campaign_id='" . $campvalue . "'";
                    $dataSummary3 = DB::select($totalInBoundStr);*/
                    $dataSummary3 = DB::select("SELECT SUM(ltc.payout) as payout, count(ltc.lead_transfer_id) AS totalleads FROM lead_transfer_call AS ltc JOIN `lead` AS l ON l.lead_id = ltc.lead_id WHERE ltc.calls_type = 'inbound' AND ltc.transfer_type = 'transfer' AND (ltc.created_at BETWEEN '" . $startdate . " 00:00:00' AND '" . $enddate . " 23:59:59') AND ltc.campaign_id = ".$campvalue."");
                    foreach ($dataSummary3 as $summary2) {
                        $data_camp['cost_inbound'] = $summary2->payout;
                        $data_camp['inbound_quantity'] = $summary2->totalleads;
                    }

                    $temparray[] = $data_camp;
                }
            }

            $total_debit_activity = array();
            $queryStr_bus_trans = "SELECT sum(amount_charge) as amount_charge,count(business_campaign_payment_id) as quantity,business_id,campaign_id,charge_type_id from business_campaign_payment where is_charge_approved = 'yes' and created_at between '" . $startdate . " 00:00:00' and '" . $enddate . " 23:59:59'  " . $appendQry . " AND charge_type_id IN (3,4,5,6) GROUP BY charge_type_id";
            $paymentRecord_bus_trans = DB::select($queryStr_bus_trans);
            $business_trans_record = array();
            if (count($paymentRecord_bus_trans) > 0) {
                foreach ($paymentRecord_bus_trans as $bustranvalue) {
                    $data_camp1 = array();
                    $data_camp1['id'] = $bustranvalue->campaign_id ? $bustranvalue->campaign_id : $bustranvalue->business_id;
                    $data_camp1['quantity'] = $bustranvalue->quantity;
                    if (empty($bustranvalue->campaign_id)) {
                        $business_namefetch = Business::where('business_id', $bustranvalue->business_id)->first();
                        $data_camp1['description'] = $business_namefetch->business_name;
                    } else {
                        $business_campaign_fetch = Campaign::where('campaign_id', $bustranvalue->campaign_id)->first();
                        $data_camp1['description'] = $business_campaign_fetch->campaign_name;
                    }
                    $data_camp1['amount_charge'] = $bustranvalue->amount_charge;
                    if ($bustranvalue->charge_type_id == 3) {
                        $data_camp1['unit'] = 'Other Debit';
                    } else if ($bustranvalue->charge_type_id == 4) {
                        $data_camp1['unit'] = 'Refund';
                    } else if ($bustranvalue->charge_type_id == 5) {
                        $data_camp1['unit'] = 'Charge';
                    } else if ($bustranvalue->charge_type_id == 6) {
                        $data_camp1['unit'] = 'Transfer';
                    } else if ($bustranvalue->charge_type_id  == 11) {
                        $data_camp1['unit'] = 'Void Payment';
                    }
                    $total_debit_activity[] =  $data_camp1;
                }
            }

            $all_campaign_id = Campaign::where('business_id', $reqBusinessesId)->pluck('campaign_id');
            $opeing_blanace = BusinessesCampaignEomBalance::where('date', $startdate)->where(function ($q) use ($reqBusinessesId, $all_campaign_id) {
                $q->where('business_id', $reqBusinessesId); // ->orWhere('businesses_campaign_id', $all_campaign_id)
            })->sum('balance');

            $next_monthfirstdate = date('Y-m-d', strtotime('+1 month', strtotime($startdate)));
            $ending_blanace = BusinessesCampaignEomBalance::where('date', $next_monthfirstdate)->where(function ($q) use ($reqBusinessesId, $all_campaign_id) {
                $q->where('business_id', $reqBusinessesId); // ->orWhere('businesses_campaign_id', $all_campaign_id)
            })->sum('balance');

            $opening_balance = (empty($opeing_blanace) || $opeing_blanace == 0) ? '--' : '$' . sprintf('%0.2f', $opeing_blanace);
            $ending_balance = (empty($ending_blanace) || $ending_blanace == 0) ? '--' : '$' . sprintf('%0.2f', $ending_blanace);
            $businesses_details = Business::Where('business_id', $reqBusinessesId)->first();
            $businesses_details->businessesEmails_load = '';
            $businesses_details->businessesphone_load = '';
            if ($businesses_details->email != null || $businesses_details->email != '') {
                $businesses_details->businessesEmails_load = $businesses_details->email;
            }
            if ($businesses_details->business_phone != null || $businesses_details->business_phone != '') {
                $businesses_details->businessesphone_load = $businesses_details->business_phone;
            }
            $array = array(
                'finalData' => $finalData,
                'sdate' => $startdate,
                'edate' => $enddate,
                'opening_balance' => $opening_balance,
                'ending_balance' => $ending_balance,
                'businesses_details' => $businesses_details,
                // 'business_trans_record'=> $business_trans_record,
                'total_debit_activity' => $total_debit_activity,
                'campaign_calculation' => $temparray
            );
            $pdf = PDF::loadView('businesses.monthlystatementpdf', $array);
            return $pdf->stream('monthlystatement.pdf');
        }
    }

    public function notificationSetup($bid)
    {
        $endDecObj = new CommonFunctions;
        $businessId = $endDecObj->customeDecryption($bid);
        $businessDetail = Business::where("business_id", $businessId)->first();
        $smsCarrier = LeadDeliverySMSCarrier::distinct()->get(['lead_delivery_sms_carrier_id', 'carrier']);
        //dd($businessDetail->toArray());
        return view("businesses.notificationsetup", compact('bid', 'businessDetail', 'smsCarrier'));
    }

    public function lowfundsSave(Request $request)
    {
        $rules = [
            'lowfunds_status' => 'required|in:yes,no',
            'email' => 'required|email||max:50|regex:/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',
            'phone' => 'required|min:10|regex:/^([0-9\s\-\+\(\)]*)$/',
            'low_amount' => 'required|numeric',
            'dnc_status' => 'required|in:yes,no',
            /*'lead_delivery_sms_carrier_id' => 'required',*/
        ];

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return back()->withInput($request->all())->withErrors($validator->errors());
        } else {
            if ($request->phone) {
                $phone = preg_replace("/\D/", "", trim($request->phone));
                if (strlen($phone) < 10 || strlen($phone) > 10) {
                    $errorArr['phone'] = "Phone number must be a 10 digits only.";
                    return back()->withInput($request->all())->withErrors($errorArr);
                }
                $phoneCheck = array_unique(str_split($phone));
                if (count($phoneCheck) === 1) {
                    $errorArr['phone'] = "Same number repeated in phone.";
                    return back()->withInput($request->all())->withErrors($errorArr);
                }
            } else {
                $phone = null;
            }

            $endDecObj = new CommonFunctions;
            $businessId = $endDecObj->customeDecryption($request->business_id);
            $businessDetail = Business::where("business_id", $businessId)->first();
            $businessDetail->is_low_fund_notification = $request->lowfunds_status;
            $businessDetail->low_fund_notification_email = $request->email;
            //$businessDetail->lead_delivery_sms_carrier_id = $request->lead_delivery_sms_carrier_id;
            $businessDetail->low_fund_notification_phone = $phone;
            $businessDetail->low_fund_amount = $request->low_amount;
            $businessDetail->is_dnc_notification = $request->dnc_status;
            $businessDetail->save();
            return redirect()->back()->with('alert-success', 'Details updated successfully');
        }
    }

    public function businessContractsList($bid)
    {
        // S3 Bucket Access
        $s3Client = new S3Client([
            'version' => 'latest',
            'region' => 'us-east-2',
            'credentials' => [
                'key' => '********************',
                'secret' => 'V37Fs7+lJICfcEYDCn6Yn3XigUoU2XK0w1DW8Zy9',
            ]
        ]);

        $endDecObj = new CommonFunctions;
        $businessId = $endDecObj->customeDecryption($bid);
        $businessDetail = Business::where("business_id", $businessId)->first();
        $bcontractlist = Contract::where("business_id", $businessId)->orderBy('contract_id', 'desc')->get()->toArray();
        $signedcontract = Contract::where("business_id", $businessId)->where('status', 'signed')->count();
        // dd(count($contractlist));
        $contractlist = [];
        for ($u = 0; $u < count($bcontractlist); $u++) {
            $contractType = ($bcontractlist[$u]['contract_type'] == 'main') ? "signup" : $bcontractlist[$u]['contract_type'];
            $contractDataArr = array();
            $bcontractlist[$u]['enc_id'] = $endDecObj->customeEncryption($bcontractlist[$u]['contract_id']);
            $bcontractlist[$u]['action'] = $bcontractlist[$u]['enc_id'];
            $contractDataArr['id'] = $bcontractlist[$u]['contract_id'];
            $contractDataArr['business_name'] = $bcontractlist[$u]['business_name'];
            $contractDataArr['owner_name'] = $bcontractlist[$u]['owner_name'];
            $contractDataArr['business_forward_number'] = $bcontractlist[$u]['business_forward_number'];
            $contractDataArr['contract_type'] = ucfirst($contractType);
            $contractDataArr['created_at'] = $bcontractlist[$u]['created_at'];
            $contractDataArr['email'] = $bcontractlist[$u]['contract_delivery_email'];
            $contractDataArr['phone'] = $bcontractlist[$u]['phone'];
            $contractDataArr['status'] = ucfirst($bcontractlist[$u]['status']);
            $contractDataArr['action'] = $bcontractlist[$u]['action'];
            $contractDataArr['contract_delivery_email'] = $bcontractlist[$u]['contract_delivery_email'];

            $storagePath = $businessId . '/' . $bcontractlist[$u]['contract_screenshot']; //s3 bucket path
            $cmd = $s3Client->getCommand('GetObject', [
                'Bucket' => (strpos(url()->current(), 'linkup.software') !== false) ? 'pmcontract' : 'pmcontractstaging',
                'Key' => $storagePath,
                /*'ResponseContentType' => 'image/jpeg',*/
                /*'Expires' => 60,*/
            ]);
            //The period of availability
            $request = $s3Client->createPresignedRequest($cmd, '+10 minutes');

            //Get the pre-signed URL
            $signedUrl = (string) $request->getUri();

            //$contractDataArr['view_contract'] = URL::to('/contract_signed_pdf') . '/' . $bcontractlist[$u]['contract_screenshot'];
            $contractDataArr['view_contract'] = $signedUrl;
            $contractlist[] = $contractDataArr;
        }
        //echo '<pre>'; print_r($contractlist); die;
        return view("businesses.business-contracts-list", compact('bid', 'businessDetail', 'contractlist', 'signedcontract'));
    }

    public function createBusinessContract($bid)
    {
        $endDecObj = new CommonFunctions;
        $businessId = $endDecObj->customeDecryption($bid);
        $contractlist = Contract::where("business_id", $businessId)->orderBy('contract_id', 'desc')->get()->toArray();
        $businessDetail = Business::where("business_id", $businessId)->first();

        return view("businesses.create-business-contract", compact('bid', 'businessDetail'));
    }

    public function saveBusinessContract(Request $request)
    {
        $userid = Auth::user()->id;
        $contractObj = new Contract();
        $contract = $contractObj->saveContract($request, 1, $userid);
        return $contract;
    }

    public function sendMoversLoginEmail(Request $request)
    {
        $status = 0;
        $message = 'fail';
        $error = array();
        try {
            $businessDetail = Business::where("business_id", $request->businessId)->first();
            $password = $this->generateRandomString();
            $businessMoverLoginCredentialsEmailobj = new BusinessMoverLoginCredentialsEmail();
            $emailresponse = $businessMoverLoginCredentialsEmailobj->emailBusinessMoverLoginCredentialsEmail($businessDetail, $password);
            $message = $emailresponse['Message'];
            if($message != "OK"){
                $message = $message;
            }else{
                Business::where('business_id', $request->businessId)->update([
                    'movers_password' =>  Hash::make($password)
                ]);
                $status = 1;
                $message = 'Success';
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
        $responseData = ['status' => $status, 'message' => $message, 'error' => $error];
        return response()->json($responseData);
    }

    function generateRandomString($length = 10) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[random_int(0, $charactersLength - 1)];
        }
        return $randomString;
    }

    public function getBusinessNote($bid){
        $endDecObj = new CommonFunctions;
        $businessId = $endDecObj->customeDecryption($bid);

        $businessNoteData= Business::with(['businessNotes', "businessNotes.users"])->where("business_id", $businessId)->first()->toArray();
        $notesadata = [];
        if(count($businessNoteData['business_notes']) > 0){
            foreach ($businessNoteData['business_notes'] as $key => $value) {
                // $notesadata[$key]['sr_no'] = $key+1;
                $notesadata[$key]['noteid'] = $value['business_note_id'];
                $notesadata[$key]['note'] = str_replace("\\t", "", $value['note']);
                $notesadata[$key]['username'] = ucfirst($value['users']['name']);
                $notesadata[$key]['updated_at'] = date('m/d/Y H:i A',strtotime($value['updated_at'])) ;
                $notesadata[$key]['action'] =  ["note_id" => $value['business_note_id'], "notetext" =>  $value['note']];
            }
        }
        $key_values = array_column($notesadata, 'noteid');
        array_multisort($key_values, SORT_DESC, $notesadata);
        // echo "<pre>";
        // print_r($notesadata );
        // die;
        return view("businesses.note", compact('bid', 'businessNoteData', 'notesadata'));
    }

    public function saveNote(Request $request){
        $status = 0;
        $message = '';
        $error = array();
        try {
            $rules = array(
                'note' => 'required',
            );
            $input = $request->only(['note']);
            $validator = Validator::make($input, $rules);
            $businessesId = 0;
            if ($validator->fails()) {
                $error =  $validator->getMessageBag()->toArray();
            } else {
                $endDecObj = new CommonFunctions;
                $businessId = $endDecObj->customeDecryption($request['bid']);
                $note = $request['note'];
                $type = $request['type'];
                $existnotid = $request['notid'];
                if( $type == 0 ){
                    $noteData = [
                        "business_id" => $businessId,
                        "note" => $note,
                        "user_id" => Auth::user()->id,
                        "created_at" => date('Y-m-d H:i:s'),
                        "updated_at" => date('Y-m-d H:i:s')
                    ];
                    $existnotid = BusinessNote::insertGetId($noteData);
                    $message = 'Note added successfully.';
                }else {
                    if($existnotid > 0 ){
                        $updatenoteData = [
                            "note" => $note,
                            "updated_at" => date('Y-m-d H:i:s')
                        ];
                        BusinessNote::where('business_note_id', $existnotid)->update($updatenoteData);
                        $message = 'Note updated successfully.';
                    }
                }
                if($existnotid > 0 ){
                    $status = 1;
                }
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => $error
        ];
        return json_encode($responseData);
    }

    public function getRelayNumber()
    {
        /*Rely Number From Business*/
        $RelyNumber = Business::where('rely_number', '!=', "")->get(['rely_number','business_name'])->toArray();
        $LeadCallNumberArr = [];
        $allcallnumber = LeadCallNumber::pluck('phone')->toArray();
        foreach ($RelyNumber as $key => $value) {
            $phone = $value['rely_number'];
            $callit_number = $this->formatPhoneNumber($phone);
            $business_name = $value['business_name'];
            $phonenew = '1'.$phone;
            if(!in_array($phonenew, $allcallnumber)){
             $LeadCallNumberArr[] = [
                "phone" => $phonenew,
                "callit_number" => $callit_number,
                "lead_source_id" => 19,
                "is_current" => 'yes',
                "day" => '0',
                "is_sms" => 'yes',
                "lead_category_id" => 1,
                "comment" => $business_name.' business relay number',
                "status" => 'active',
                "phone_source" => 'BR',
                "provider" => 'plivo'
            ];
            }else{
                echo "already exist in lead call number".$phonenew."<br>";
            }
        }
        if(count($LeadCallNumberArr) > 0){
            $InsertedData = LeadCallNumber::insert($LeadCallNumberArr);
        }else{
            echo "Business Relay not found<br>";
        }
       /*echo "<pre>"; print_r($InsertedData); exit();*/

        /*Contact Number From Campaign Organic*/
        $ContactNumber = CampaignOrganic::where('contact_number', '!=', "")->get(['contact_number','business_name'])->toArray();
        $LeadCallNumberArrNew = [];
        $allcallnumber = LeadCallNumber::pluck('phone')->toArray();
        foreach ($ContactNumber as $key => $value) {
            $phone = $value['contact_number'];
            $callit_number = $this->formatPhoneNumber($phone);
            $business_name = $value['business_name'];
            $phonenew = '1'.$phone;
            if(!in_array($phonenew, $allcallnumber)){
            $LeadCallNumberArrNew[] = [
                "phone" => $phonenew,
                "callit_number" => $callit_number,
                "lead_source_id" => 19,
                "is_current" => 'yes',
                "day" => '0',
                "is_sms" => 'yes',
                "lead_category_id" => 1,
                "comment" => $business_name.' business organic number',
                "status" => 'active',
                "phone_source" => 'OR',
                "provider" => 'plivo'
            ];
            }else{
                echo "organic already exist in lead call number".$phonenew."<br>";
            }
        }
        if(count($LeadCallNumberArrNew) > 0){
            $InsertedDataNew = LeadCallNumber::insert($LeadCallNumberArrNew);
        }else{
            echo "Organic Relay not found<br>";
        }
        echo "Done Successfully.";die;
       /*echo "<pre>"; print_r($InsertedDataNew); exit();*/
    }

    function formatPhoneNumber($phoneNumber) {
        $formattedNumber = preg_replace('/^(\d{3})(\d{3})(\d{4})$/', '$1 $2-$3', $phoneNumber);
        return $formattedNumber;
    }

    //Added By BK On 11-08-2023 For Auto Create Organic Campaign and Marketplace
    public function createOrganicMarketplaceCampaign($businessId, $createdbyuser) {
        $campaignArray = $campaignTypeIdArray = $organicArray = $moveSizeArray = [];
        $businessDetail = Business::where('business_id', $businessId)->get()->toArray();

        $campaignTypeIdArray = [4, 7];
        /*$campaignDetail = Campaign::where('business_id', $businessId)->whereIn('campaign_type_id', $campaignTypeIdArray)->get(['campaign_id', 'campaign_type_id', 'campaign_name'])->toArray();
        for ($c=0; $c < count($campaignDetail); $c++) {
            $campaignArray[] = $campaignDetail[$c];
        }*/
        //echo '<pre>'; print_r($campaignArray); die();

        for ($ct=0; $ct < count($campaignTypeIdArray); $ct++) {
            $campaignDetail = Campaign::where('business_id', $businessId)->where('campaign_type_id', $campaignTypeIdArray[$ct])->first();
            if (!empty($campaignDetail)) {
                //already organic campaign
            } else {
                $campaignName = 'Organic';
                if ($campaignTypeIdArray[$ct] == 7) {
                    $campaignName = 'Marketplace';
                }

                $organicArray = [];
                $organicArray['business_id'] = $businessId;
                $organicArray['lead_category_id'] = 1;
                $organicArray['campaign_name'] = trim($businessDetail[0]['business_name']) . ' - ' . $campaignName;
                $organicArray['lead_type_id'] = 1;
                $organicArray['campaign_type_id'] = $campaignTypeIdArray[$ct];
                $organicArray['credit_available'] = 0;
                $organicArray['credit_reserved'] = 0;
                $organicArray['default_score'] = 0;
                $organicArray['additional_score'] = 0;
                $organicArray['lead_status'] = "0,1";
                $organicArray['payment_type'] = 0;
                $organicArray['is_active'] = 'no';
                $organicArray['free_lead'] = 0;
                $organicArray['created_user_id'] = $businessDetail[0]['created_user_id'];
                $organicArray['created_at'] = date("Y-m-d H:i:s");
                //echo '<pre>'; print_r($organicArray); die;
                $campaignId = Campaign::Create($organicArray)->campaign_id;

                $movingArray = [];
                $movingArray['campaign_id'] = $campaignId;
                $movingArray['move_type'] = 'local';
                $movingArray['min_distance'] = 200;
                $movingArray['created_user_id'] = $businessDetail[0]['created_user_id'];
                $movingArray['created_at'] = date("Y-m-d H:i:s");
                CampaignMoving::create($movingArray);

                $moveSizeIdArray = [1, 2, 3, 4, 5, 6];
                if (is_array($moveSizeIdArray) && count($moveSizeIdArray) > 0) {
                    foreach ($moveSizeIdArray as $moveSizeId) {
                        $moveSizeArray[] = [
                            'campaign_id' => $campaignId,
                            'move_size_id' => $moveSizeId,
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                    }

                    CampaignMoveSize::insert($moveSizeArray);
                }

                if($campaignTypeIdArray[$ct] == 4) {
                    $regionNameArray = array("AL","AR","AZ","CO","CT","DC","DE","FL","GA","IA","ID","IL","IN","KS","KY","LA","MA","MD","ME");
                    for($r=0; $r < count($regionNameArray); $r++){
                        $regionArray = [];
                        $regionArray['campaign_id'] = $campaignId;
                        $regionArray['location_coverage'] = 'state';
                        $regionArray['location_type'] = 'source';
                        $regionArray['value'] = $regionNameArray[$r];
                        $regionArray['created_at'] = date("Y-m-d H:i:s");
                        CampaignLocation::create($regionArray);

                        $regionArray['location_type'] = 'destination';
                        CampaignLocation::create($regionArray);
                    }

                    $hoursArray = array("day" => 1, "start_hour" => 0, "end_hour" => 23);
                    for($h=$hoursArray['day']; $h<=7; $h++){
                        for($j=$hoursArray['start_hour']; $j <= $hoursArray['end_hour']; $j++){
                            //echo $h."==".$j."<br>";
                            $scheuleArray = [];
                            $scheuleArray['campaign_id'] = $campaignId;
                            $scheuleArray['day'] = $h;
                            $scheuleArray['start_hour'] = $j;
                            $scheuleArray['end_hour'] = $j + 1;
                            $scheuleArray['daily_limit'] = 0;
                            $scheuleArray['is_pause'] = 'no';
                            $scheuleArray['status'] = 'inactive';
                            $scheuleArray['created_at'] = date("Y-m-d H:i:s");
                            CampaignScheule::create($scheuleArray);
                        }
                    }

                    $organicArray = [];
                    $organicArray['businessName'] = trim($businessDetail[0]['business_name']);
                    $organicArray['businessAddress'] = trim($businessDetail[0]['address']);
                    $organicArray['totalYear'] = 4;
                    $organicArray['ranking_score'] = 0;
                    $organicArray['forwardNumber'] = "";
                    $organicArray['callCharge'] = 60;
                    $organicArray['licence'] = trim($businessDetail[0]['dot_number']);
                    $organicArray['review'] = "5";
                    //$organicArray['servicesOffer'] = ['Full-Service Moving Solutions', 'Experienced and Professional Staff', 'Storage'];
                    $organicArray['serveArea'] = $regionNameArray;
                    $organicArray['isFullLead'] = 'yes';
                    $organicArray['isCallLead'] = 'yes';

                    $campaignObj = New CampaignController();
                    $campaignObj->insertUpdateVmOrganicData($campaignId, $organicArray, $createdbyuser);
                }
            }
        }
        //echo '<pre>'; print_r($campaignArray); die();
    }

    public function addbadges($bid)
    {
        $endDecObj = new CommonFunctions;
        $businessId = $endDecObj->customeDecryption($bid);
        $businessData = Business::with(['mstBuyerType', 'businessDailytLimit'])->where('business_id', $businessId)->first()->toArray();
        $businessBadges = BusinessBadges::where('business_id', $businessId)->get()->toArray();
        // echo "<pre>"; print_r($businessBadges); exit();
        $finalDataArr = [];
        $badgesdata = [];
        if(count($businessBadges) > 0){
            foreach ($businessBadges as $key => $value) {
                $badgesdata['business_badges_id'] = $value['business_badges_id'];
                $badgesdata['business_id'] = $value['business_id'];
                $badgesdata['business_name'] = $businessData['business_name'];

                if ($value['categoryType'] == 1) {
                    $CatType = 'Top 3 Mover';
                }else if($value['categoryType'] == 2){
                    $CatType = 'Top 3 Interstate Mover';
                }else if($value['categoryType'] == 3){
                    $CatType = 'Top 3 Affordable Mover';
                }else if($value['categoryType'] == 4){
                    $CatType = 'Best Mover';
                }else if($value['categoryType'] == 5){
                    $CatType = 'Best Interstate Mover';
                }

                $badgesdata['categoryType'] = $CatType;
                $badgesdata['backgroundType'] = ucfirst($value['backgroundType']);
                $badgesdata['year'] = $value['year'];
                $badgesdata['freetext'] =  ucfirst($value['freetext']);
                $badgesdata['badges_image'] =  $value['badges_image'];
                $badgesdata['badges_url'] = ($value['badges_url']!="") ? $value['badges_url'] : Helper::checkServer()['vm_url'];
                $badgesdata['action'] =  ["note_id" => $value['business_id'], "notetext" =>  $value['business_id']];
                $finalDataArr[] = $badgesdata;
            }
        }
        return view("businesses.addbadges", compact('bid', 'businessData', 'finalDataArr'));
    }

    public function updatebusinessbadges(Request $request)
    {
        $requestData = $request->all();
          // echo "<pre>"; print_r($requestData['logo']); exit();
        $status = 0;
        $message = 'fail';
        $error = array();
        $logUpdateArr = [];
        $errorArr = [];
        $businessbadges = [];
        $businessesId = $request->get('businessesId');
        $rules = array(
            'categoryType' => 'required|max:50',
            'backgroundType' => 'required|max:50',
            'year' => 'required|max:50',
        );
        $input = $request->only(['categoryType', 'backgroundType', 'year']);

        $validator = Validator::make($input, $rules);
        if ($validator->fails()) {
            $error =  $validator->getMessageBag()->toArray();
        } else {
            $categoryType = $request->get('categoryType');
            $backgroundType = $request->get('backgroundType');
            $year = $request->get('year');
            $freetext = $request->get('freetext');

            if (count($errorArr) > 0) {
                $responseData = ['status' => $status, 'message' => $message, 'error' => $errorArr];
                return response()->json($responseData);
            }

            $badgesUrl = "";
            $campaignDetail = Campaign::where('business_id', $businessesId)->where('campaign_type_id', 4)->first()->toArray();
            if (!empty($campaignDetail)) {
                $organicDetail = CampaignOrganic::select('business_name')->where('campaign_id', $campaignDetail['campaign_id'])->first();
                if ($organicDetail->business_name != "") {
                    $badgesUrl = Helper::checkServer()['vm_url'] . '/moving-company/' . $this->create_slug($organicDetail->business_name);
                }
            }

            $businessbadges = [
                'business_id' => $businessesId,
                'categoryType' => $categoryType,
                'backgroundType' => $backgroundType,
                'year' => $year,
                'freetext' => $freetext,
                'badges_url' => $badgesUrl,
            ];
            $business_badges_id =  BusinessBadges::insertGetId($businessbadges);
            // logo upload logic start

            $badges_image = $requestData['logo'];
               // echo "<pre>"; print_r($badges_image); exit();
            if (isset($badges_image) && !empty($badges_image) && $badges_image != 'undefined') {
                // $file = $request->file('logo');
                $path = public_path();
                $string_pieces = explode( ";base64,", $badges_image);
                $image_type_pieces = explode( "image/", $string_pieces[0] );
                $image_type = $image_type_pieces[1];
                // $fname = md5(uniqid()).'.'.$image_type;
                $fname = $businessesId . '_' . time() . '.' . $image_type;
                $destinationPath = $path . '/images/businessbadges/';
                $store_at = $destinationPath.$fname;
                $decoded_string = base64_decode( $string_pieces[1] );
                 // echo "<pre>"; print_r($decoded_string); exit();
                file_put_contents( $store_at, $decoded_string );
                 // echo "<pre>"; print_r($fname); exit();

                BusinessBadges::where('business_badges_id', $business_badges_id)
                    ->update([
                        'badges_image' => $fname
                    ]);

                $postFields = json_encode(array('business_id' => $businessesId, 'badges_id' => $business_badges_id, 'filename' => $fname, 'image' => $badges_image));
                $url        = Helper::checkServer()['vm_url'] . '/storebadges';
                $ch         = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
                curl_exec($ch);
                if (curl_errno($ch)) {
                    echo 'Error:' . curl_error($ch);
                    exit;
                }
                curl_close($ch);
            }
            // End logo upload logic end

            $status = 1;
            $message = 'Success';
        }
        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => $error
        ];
        return response()->json($responseData);
    }

    public function deleteBusinessBadge(Request $request){

        $status = 0;
        $message = 'fail';
        $error = array();
        try {

            $businessBadges = BusinessBadges::where('business_badges_id', $request->business_badges_id)->first();
            $path = public_path();
            $filename = $path . '/images/businessbadges/'.$businessBadges->badges_image;

            if (file_exists($filename)) {
                unlink($filename);
            }

            $businessDetail = BusinessBadges::where("business_badges_id", $request->business_badges_id)->delete();
            $status = 1;
            $message = 'Success';
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
        $responseData = ['status' => $status, 'message' => $message, 'error' => $error];
        return response()->json($responseData);
    }

    public function updateLastActiveDateTime()
    {
        $leadcalldata = LeadCall::selectRaw('MAX(lead_call_id) AS lead_call_id, to_number, MAX(created_at) AS created_at')
        ->groupBy('to_number')
        ->orderByDesc('lead_call_id')
        ->get()->toArray();

        $LeadCallNumberArr = [];
        foreach ($leadcalldata as $key => $value) {
            $to_number =  $value['to_number'];
            $created_at = $value['created_at'];
            $UpdateData = LeadCallNumber::where('phone', $to_number)->update(['last_active_datetime' => $created_at]);
        }
        echo "Done Successfully.";die;
    }

    public function getCampaignOrganic(Request $request) {
        echo "Stop now";die;
        $businessesCampaign = CampaignOrganic::leftJoin('campaign as c', 'campaign_organic.campaign_id', 'c.campaign_id')->select('c.campaign_id', 'c.campaign_name')->where('campaign_organic.contact_number', null)->groupBy('campaign_organic.campaign_id')->pluck('campaign_organic.campaign_id')->toArray();
        if (count($businessesCampaign) > 0) {
            echo implode(",", $businessesCampaign);
            echo "<br>";
            echo "<pre>";
            print_r($businessesCampaign);
            exit();
        }
        for ($fd = 0; $fd < count($businessesCampaign); $fd++) {
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://api.thinq.com/inbound/get-numbers?searchType=domestic&searchBy=ratecenter&quantity=1&contiguous=false&state=CA&rateCenter=&related=true',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json',
                    'Authorization: Basic ' . $commioToken . ''
                ),
            ));

            $response = curl_exec($curl);
            curl_close($curl);
            $commioNumber = json_decode($response, true);
            //echo "<pre>"; print_r($response); die;

            if (isset($commioNumber['message']) && strtolower($commioNumber['message']) == "success" && isset($commioNumber['dids'][0])) {
                $did = $commioNumber['dids'][0]['id'];
                $postFields = [
                    "order" => [
                        "tns" => [
                            [
                                "caller_id" => null,
                                "account_location_id" => null,
                                "sms_routing_profile_id" => 699,
                                "route_id" => 13835,
                                "features" => [
                                    "cnam" => false,
                                    "sms" => true,
                                    "e911" => false
                                ],
                                "did" => $did
                            ]
                        ],
                        "blocks" => []
                    ],
                ];

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://api.thinq.com/account/' . $accountId . '/origination/order/create',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => json_encode($postFields),
                    CURLOPT_HTTPHEADER => array(
                        'Content-Type: application/json',
                        'Authorization: Basic ' . $commioToken . ''
                    ),
                ));
                $response = curl_exec($curl);
                curl_close($curl);
                $purchaseNumber = json_decode($response, true);
                //echo "<pre>"; print_r($response); die;
                if (isset($purchaseNumber['status']) && strtolower($purchaseNumber['status']) == "created" && $purchaseNumber['id']) {
                    $orderId = $purchaseNumber['id'];
                    $curl = curl_init();
                    curl_setopt_array($curl, array(
                        CURLOPT_URL => 'https://api.thinq.com/account/' . $accountId . '/origination/order/complete/' . $orderId,
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => '',
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'POST',
                        CURLOPT_HTTPHEADER => array(
                            'Content-Type: application/json',
                            'Authorization: Basic ' . $commioToken . ''
                        ),
                    ));
                    $response = curl_exec($curl);
                    curl_close($curl);
                    $completeOrder = json_decode($response, true);
                    //echo "<pre>"; print_r($response); die;
                    if (isset($completeOrder['status']) && strtolower($completeOrder['status']) == "completed") {
                        //Insert into lead call number
                        $insertDataArr = array();
                        $insertDataArr['phone'] = $did;
                        $insertDataArr['callit_number'] = substr($did, 1, 3) . " " . substr($did, 4, 3) . "-" . substr($did, 7, 4);
                        $insertDataArr['lead_source_id'] = 19;
                        $insertDataArr['is_current'] = "yes";
                        $insertDataArr['day'] = 0;
                        $insertDataArr['is_sms'] = "yes";
                        $insertDataArr['lead_category_id'] = 1;
                        $business_name = $businessesCampaign[$fd];
                        if (isset($businessNameArr[$businessesCampaign[$fd]])) {
                            $business_name = $businessNameArr[$businessesCampaign[$fd]];
                        }
                        $insertDataArr['comment'] = $business_name . ' business relay number';
                        $insertDataArr['status'] = "active";
                        $insertDataArr['phone_source'] = "OR";
                        $insertDataArr['provider'] = "commio";
                        $insertDataArr['created_at'] = date("Y-m-d H:i:s");
                        LeadCallNumber::create($insertDataArr);
                        //Here Update into business
                        $updateBusinessArr = array();
                        $updateBusinessArr['contact_number'] = substr(trim($did), 1);
                        CampaignOrganic::where('campaign_id', $businessesCampaign[$fd])->update($updateBusinessArr);
                        echo $businessesCampaign[$fd] . "==" . $did . "== Business number purchased and updated<br>";
                    } else {
                        echo $businessesCampaign[$fd] . "== 3 Order Not found<br>";
                    }
                } else {
                    echo $businessesCampaign[$fd] . "== 2 Purchase Not found<br>";
                }
            } else {
                echo $businessesCampaign[$fd] . "== 1 Search Not found<br>";
            }
        }
        echo "Done";
        die;
    }

    public function releaseOrganicRelayNumber(Request $request){
        echo "Stop now";die;
        $endDecObj = new CommonFunctions;

        $getRecords = CampaignOrganic::join('campaign as c', 'campaign_organic.campaign_id', 'c.campaign_id')->select('c.campaign_id', 'c.campaign_name', 'campaign_organic.contact_number')->where('campaign_organic.contact_number', '>', 0)->where('c.is_active','=', 'no')->groupBy('campaign_organic.campaign_id')->get(['campaign_organic.campaign_id','campaign_organic.contact_number'])->toArray();

         echo "<pre>"; print_r($getRecords); exit();

        if (count($getRecords) > 0) {

            foreach ($getRecords as $key => $value) {
               $contact_number = $value['contact_number'];
               $campaign_id = $value['campaign_id'];

               $response = $endDecObj->ReleaseComioNumber($contact_number);

               if ($response == 'success') {
                   CampaignOrganic::where('campaign_id', $campaign_id)
                    ->update([
                        'contact_number' => null
                    ]);
               }

                DevDebug::create([
                    'sr_no' => 125,
                    'result' => 'disconnectCommioNumber Response: ' . json_encode($response),
                    'created_at' => date("Y-m-d H:i:s"),
                ]);

            }

            echo "Done";
        } else {
            echo 'No record found';

        }
        die;
    }

    public function getbussinesslist(){
        $status = 0;
        $getallbusiness = Business::whereIn("status",["inactive","active","pause"])->get(['business_id','business_name','business_phone','emergency_phone','forward_number','notification_phone','email','notification_email','movers_username','address','dot_number'])->toArray();
        if(count($getallbusiness) > 0 ){
            $status = 1;
        }
        $responseData = ['status' => $status,'data' => $getallbusiness];
        return response()->json($responseData);
    }

    // save ls and tlm business
    public function saveLSBusiness(){
        $status = 0;
        $result = "failed";
        $message = $message1 = "No Business found";
        try{
            // for ls business
            /*$url = Helper::checkServer()['pingpost_url'] . '/api/getBusinessesList';
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
            $curlresult = curl_exec($ch);
            $curlresponse = json_decode($curlresult, true);
            curl_close($ch);
            // echo "<pre>";print_r($curlresponse);
            $insertDataArr = [];
            if(count($curlresponse['data']) > 0 ){
                foreach ($curlresponse['data'] as $key => $value) {
                    $getexitlsbusiness = LSBusinessList::where('ls_business_id', $value['business_id'])->first();
                    if(!$getexitlsbusiness){
                        $insertDataArr[] = [
                            'ls_business_id' => $value['business_id'],
                            'ls_business_name' => $value['business_name'],
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                    }
                }
                if(count($insertDataArr) > 0 ){
                    LSBusinessList::insert($insertDataArr);
                    $message = "LS Business added successfully";
                }else{
                    $message = "No new ls business found";
                }
                $status = 1;
                $result = "success";
            }*/
            // for tlm business
            $url = Helper::checkServer()['zap_url'] . '/api/getbussinesslist';
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
            $curlresult = curl_exec($ch);
            $curlresponse = json_decode($curlresult, true);
            curl_close($ch);
            // echo "<pre>";print_r($curlresponse);
            $insertTLMDataArr = [];
            if(count($curlresponse['data']) > 0 ){
                foreach ($curlresponse['data'] as $key => $value) {
                    $getexittlmbusiness = LMBusinessList::where('lm_business_id', $value['business_id'])->first();
                    if(!$getexittlmbusiness){
                        $insertTLMDataArr[] = [
                            'lm_business_id' => $value['business_id'],
                            'lm_business_name' => $value['business_name'],
                            'lm_business_phone' => $value['business_phone'],
                            'lm_emergency_phone' => $value['emergency_phone'],
                            'lm_forward_number' => $value['forward_number'],
                            'lm_notification_phone' => $value['notification_phone'],
                            'lm_email' => $value['email'],
                            'lm_notification_email' => $value['notification_email'],
                            'lm_movers_username' => $value['movers_username'],
                            'lm_address' => $value['address'],
                            'lm_dot_number' => $value['dot_number'],
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                    } else {
                        LMBusinessList::where('lm_business_id', $value['business_id'])->update([
                            'lm_business_name' => $value['business_name'],
                            'lm_business_phone' => $value['business_phone'],
                            'lm_emergency_phone' => $value['emergency_phone'],
                            'lm_forward_number' => $value['forward_number'],
                            'lm_notification_phone' => $value['notification_phone'],
                            'lm_email' => $value['email'],
                            'lm_notification_email' => $value['notification_email'],
                            'lm_movers_username' => $value['movers_username'],
                            'lm_address' => $value['address'],
                            'lm_dot_number' => $value['dot_number']
                        ]);
                    }
                }
                if(count($insertTLMDataArr) > 0 ){
                    LMBusinessList::insert($insertTLMDataArr);
                    $message1 = "TLM Business added successfully";
                }else{
                    $message1 = "No new tlm business found";
                }
                // $status = 1;
                $result = "success";
            }
        } catch (Exception $e) {
            $message = $message1 = $e->getMessage();
            Log::info("saveLSBusiness catch = ". json_encode($message));
        }
        $responseData = ['status' => $status, 'result' => $result, 'message' => $message, 'message1' => $message1 ];
        return response()->json($responseData);
    }

    public function getLSpartneredBusiness($postData = ""){
        $status = $businessesId = 0;
        $noncompetevalues_ls = $pmlsData = $data = array();
        $error = "";
        $message = "failed";
        try {
            $elements = json_decode(file_get_contents('php://input'), true);
            if (empty($elements)) {
                $elements = json_decode($postData, true);
            }

            $token = $elements['token'];
            if (!isset($token) || $token == "") {
                throw new Exception("Token is required");
            }

            $pm_companyid_Arr = $elements['pm_company_id'];
            $ls_companyid_Arr = $elements['ls_company_id'];
            $checkToken = MstBusinessToken::where("token", $token)->first();
            if (empty($checkToken) ) {
                throw new Exception("Unauthorized");
            }

            $response = $this->pmlsDataStore($ls_companyid_Arr, $pm_companyid_Arr);
            if( $response == "success"){
                $message = $response;
                $status = 1;
            }
            $data = ['pm_company_id' => $pm_companyid_Arr, 'ls_company_id' => $ls_companyid_Arr];
        } catch (Exception $e) {
            $error = $e->getMessage();
            Log::info("getLSpartneredBusinesscatch = ". json_encode($error));
        }
        $responseData = [ 'status' => $status,'data' => $data, 'error' => $error];
        // $responseData = [ 'status' => $status,'message' => $message, 'error' => $error];
        return response()->json($responseData);
    }

    public function saveLSBusinesstoPM($request){
        $status = 0;
        $message = "failed";
        $error = "";
        $pmlsData = [];
        try{
            $request =  json_decode( $request, true );
            $pm_companyid_Arr = $request['pm_company_id'];
            $ls_companyid_Arr = $request['ls_company_id'];
            $url = Helper::checkServer()['pingpost_url'] . '/api/nonCompeteBusiness';
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request, true));

            $curlresult = curl_exec($ch);
            $curlresponse =  json_decode($curlresult, true );
            curl_close($ch);
            // echo "saveLSBusinesstoPM = "; print_r($pm_companyid_Arr ); die;
            if($curlresponse['status'] == 1 || $curlresponse['status'] == "1" ){
                /*$responsepmlsDataStore = $this->pmlsDataStore($ls_companyid_Arr, $pm_companyid_Arr);
                if( $responsepmlsDataStore == "success"){
                    $message = $responsepmlsDataStore;
                    $status = 1;
                }*/
                $message = "success";
                $status = 1;
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
            Log::info("saveLSBusinesstoPM catch = ". json_encode($error));
        }
        $responseData = ['status' => $status, 'message' => $message, 'error' => $error ];
        return response()->json($responseData);
    }

    public function pmlsDataStore($ls_companyid_Arr, $pm_companyid_Arr){
        $pmlsData = [];
        sort( $pm_companyid_Arr );
        sort( $ls_companyid_Arr );
        $newLSBusinessList = LSBusinessList::whereIn('ls_business_id', $ls_companyid_Arr)->pluck('ls_business_list_id')->toArray();
        PMLSBusiness::whereIn('business_id', $pm_companyid_Arr)->whereIn('ls_business_list_id', $ls_companyid_Arr)->delete();
        foreach ($pm_companyid_Arr as $pm_company_key => $pm_company_value) {
            foreach ($newLSBusinessList as $ls_company_key => $ls_company_value) {
                $checkRecord = PMLSBusiness::where('business_id', $pm_company_value)->where('ls_business_list_id', $ls_company_value)->first();
                if($checkRecord) {
                } else {
                    $pmlsData[] = [
                        'business_id' => $pm_company_value,
                        'ls_business_list_id' => $ls_company_value,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
            }
        }
        if (count($pmlsData) > 0) {
            PMLSBusiness::insert($pmlsData);
        }
        return "success";
    }

    // save pm -tlm businesses
    public function getTLMpartneredBusiness($postData = ""){
        $status = $businessesId = 0;
        $noncompetevalues_ls = $pmlsData = $data = array();
        $error = "";
        $message = "failed";
        try {
            $elements = json_decode(file_get_contents('php://input'), true);
            if (empty($elements)) {
                $elements = json_decode($postData, true);
            }

            $token = $elements['token'];
            if (!isset($token) || $token == "") {
                throw new Exception("Token is required");
            }
            $pm_companyid_Arr = $elements['pm_company_id'];
            $tlm_companyid_Arr = $elements['lm_company_id'];

            $checkToken = MstBusinessToken::where("token", $token)->first();
            if (empty($checkToken) ) {
                throw new Exception("Unauthorized");
            }
            $response = $this->pmtlmDataStore($tlm_companyid_Arr, $pm_companyid_Arr);
            if( $response == "success"){
                $message = $response;
                $status = 1;
            }
            $data = ['pm_company_id' => $pm_companyid_Arr, 'lm_company_id' => $tlm_companyid_Arr];
        } catch (Exception $e) {
            $error = $e->getMessage();
            Log::info("getTLMpartneredBusinesscatch = ". json_encode($error));
        }
        $responseData = [ 'status' => $status,'data' => $data, 'error' => $error];
        return response()->json($responseData);
    }

    public function saveTLMBusinesstoPM($request){
        $status = 0;
        $message = "failed";
        $error = "";
        $pmlsData = [];
        try{

            $request =  json_decode( $request, true );
            $pm_companyid_Arr = $request['pm_company_id'];
            $tlm_companyid_Arr = $request['lm_company_id'];

            $url = Helper::checkServer()['zap_url'] . '/api/getpmpartneredbusiness';

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request, true));

            $curlresult = curl_exec($ch);
            $curlresponse =  json_decode($curlresult, true );

            curl_close($ch);
            if($curlresponse['status'] == 1 || $curlresponse['status'] == "1" ){
                $responsepmlsDataStore = $this->pmtlmDataStore($tlm_companyid_Arr, $pm_companyid_Arr);
                if( $responsepmlsDataStore == "success"){
                    $message = $responsepmlsDataStore;
                    $status = 1;
                }
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
            Log::info("saveTLMBusinesstoPM catch = ". json_encode($error));
        }
        $responseData = ['status' => $status, 'message' => $message, 'error' => $error ];
        return response()->json($responseData);
    }

    public function pmtlmDataStore($tlm_companyid_Arr, $pm_companyid_Arr){
        $pmtlmData = [];
        $devDebug = new DevDebug();
        sort( $pm_companyid_Arr );
        sort( $tlm_companyid_Arr );
        $newLSBusinessList = LMBusinessList::whereIn('lm_business_id', $tlm_companyid_Arr)->pluck('lm_business_list_id')->toArray();

        // Log::info("pmtlmDataStore pm_companyid_Arr = ". json_encode($pm_companyid_Arr, true). ", pmtlmDataStore tlm_companyid_Arr = ". json_encode($tlm_companyid_Arr, true) );
        // Log::info("pmtlmDataStore newLSBusinessList = ". json_encode($newLSBusinessList, true) );
        $deleteDataIds = PMLMBusiness::whereIn('lm_business_list_id', $newLSBusinessList)->whereNotIn('business_id', $pm_companyid_Arr)->pluck('pm_lm_business_id')->toArray();
        $devDebug->devDebuglog(81, "pmtlmDataStore deleteDataIds = ". json_encode($deleteDataIds, true));
        PMLMBusiness::whereIn('pm_lm_business_id', $deleteDataIds)->delete();

        $deleteDataIds1 = PMLMBusiness::whereNotIn('lm_business_list_id', $newLSBusinessList)->whereIn('business_id', $pm_companyid_Arr)->pluck('pm_lm_business_id')->toArray();
        $devDebug->devDebuglog(81, "pmtlmDataStore deleteDataIds1 = ". json_encode($deleteDataIds1, true));
        PMLMBusiness::whereIn('pm_lm_business_id', $deleteDataIds1)->delete();


        // Log::info("pmtlmDataStore pm_companyid_Arr = ". json_encode($pm_companyid_Arr, true). ", pmtlmDataStore tlm_companyid_Arr = ". json_encode($tlm_companyid_Arr, true) );
        // Log::info("pmtlmDataStore newLSBusinessList = ". json_encode($newLSBusinessList, true) );
        // $deleteDataIds = PMLMBusiness::whereIn('lm_business_list_id', $newLSBusinessList)->whereNotIn('business_id', $pm_companyid_Arr)->pluck('pm_lm_business_id')->toArray();
        // $devDebug->devDebuglog(81, "lmpmDataStore deleteDataIds = ". json_encode($deleteDataIds, true));
        // PMLMBusiness::whereIn('pm_lm_business_id', $deleteDataIds)->delete();

        // $deleteDataIds1 = PMLMBusiness::whereNotIn('lm_business_list_id', $newLSBusinessList)->whereIn('business_id', $pm_companyid_Arr)->pluck('pm_lm_business_id')->toArray();
        // PMLMBusiness::whereIn('pm_lm_business_id', $deleteDataIds1)->delete();


        // PMLMBusiness::whereNotIn('lm_business_list_id', $newLSBusinessList)->delete();
        foreach($pm_companyid_Arr as $pm_company_id) {
            foreach($newLSBusinessList as $ls_company_id) {
                // PMLMBusiness::where('business_id', $pm_company_id)->whereIn('lm_business_list_id', $newLSBusinessList)->delete();
                $checkRecord = PMLMBusiness::where('business_id', $pm_company_id)->where('lm_business_list_id', $ls_company_id)->first();
                if($checkRecord) {
                } else {
                    $pmtlmData[] = [
                        'business_id' => $pm_company_id,
                        'lm_business_list_id' => $ls_company_id,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    // PMLMBusiness::insert($pmtlmData);
                }
            }
        }
        if(count($pmtlmData) >0 ){
            PMLMBusiness::insert($pmtlmData);
        }
        return "success";
    }

    public function fmcsaCron() {
        $dotNumber1 = 1000008;
        $url11 = "https://mobile.fmcsa.dot.gov/qc/services/carriers/".$dotNumber1."/operation-classification?webKey=653a5b0b5aa307372e8cee00e53847fb60d78155&start=1&size=10";

        $chx = curl_init();
        curl_setopt($chx, CURLOPT_URL, $url11);
        curl_setopt($chx, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($chx, CURLOPT_CUSTOMREQUEST, 'GET');

        $curlresultx = curl_exec($chx);
        echo $curlresultx;
        $curlresponsex =  json_decode($curlresultx, true );
        curl_close($chx);


        dd($curlresponsex);
        $fmcsa = FMCSA::where('STATUS',0)->take(20)->get('DOT_NUMBER')->toArray();
        for($i=0;$i<=19;$i++) {
            $data = [];
            $dotNumber = $fmcsa[$i]['DOT_NUMBER'];
            $checkData = BusinessCompany::where('dotNumber', $dotNumber)->first();
            FMCSA::where('DOT_NUMBER', $dotNumber)
                ->update(['STATUS'=>1]);

            if($checkData) {
                echo "Data added!";
            } else {

                $url1 = "https://mobile.fmcsa.dot.gov/qc/services/carriers/".$dotNumber."?webKey=653a5b0b5aa307372e8cee00e53847fb60d78155&start=1&size=10";
                $url2 = "https://mobile.fmcsa.dot.gov/qc/services/carriers/".$dotNumber."/authority?webKey=653a5b0b5aa307372e8cee00e53847fb60d78155&start=1&size=10";

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url1);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');

                $curlresult = curl_exec($ch);
                $curlresponse =  json_decode($curlresult, true );
                curl_close($ch);


                $ch1 = curl_init();
                curl_setopt($ch1, CURLOPT_URL, $url2);
                curl_setopt($ch1, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch1, CURLOPT_CUSTOMREQUEST, 'GET');

                $curlresult1 = curl_exec($ch1);
                $curlresponse1 =  json_decode($curlresult1, true );
                curl_close($ch1);

                $data =  [
                    'allowedToOperate' => $curlresponse['content']['carrier']['allowedToOperate'] ?? "",
                    'brokerAuthorityStatus' => $curlresponse['content']['carrier']['brokerAuthorityStatus'] ?? "",
                    'carrierOperationCode' => $curlresponse['content']['carrier']['carrierOperation']['carrierOperationCode'] ?? "",
                    'carrierOperationDesc' => $curlresponse['content']['carrier']['carrierOperation']['carrierOperationDesc'] ?? "",
                    'censusTypeId' => $curlresponse['content']['carrier']['censusTypeId']['censusTypeId'] ?? "",
                    'censusTypeDesc' => $curlresponse['content']['carrier']['censusTypeDesc']['censusTypeDesc'] ?? '',
                    'dbaName' => $curlresponse['content']['carrier']['dbaName'] ?? '',
                    'legalName' => $curlresponse['content']['carrier']['legalName'] ?? '',
                    'mcs150Outdated' => $curlresponse['content']['carrier']['mcs150Outdated'] ?? '',
                    'oosDate' => $curlresponse['content']['carrier']['oosDate'] ?? '',
                    'reviewDate' => $curlresponse['content']['carrier']['reviewDate'] ?? '',
                    'contractAuthorityStatus' => $curlresponse['content']['carrier']['contractAuthorityStatus'] ?? '',
                    'dotNumber' => $curlresponse['content']['carrier']['dotNumber'] ?? '',
                    'authority' => $curlresponse1['content'][0]['carrierAuthority']['authority'] ?? '',
                    'authorizedForBroker' => $curlresponse1['content'][0]['carrierAuthority']['authorizedForBroker'] ?? '',
                    'authorizedForHouseholdGoods'=> $curlresponse1['content'][0]['carrierAuthority']['authorizedForHouseholdGoods'] ?? '',
                    'docketNumber' => $curlresponse1['content'][0]['carrierAuthority']['docketNumber'] ?? '',
                    'prefix' => $curlresponse1['content'][0]['carrierAuthority']['prefix'] ?? '',
                    'vehicleInsp' => $curlresponse['content']['carrier']['vehicleInsp'] ?? "",
                ];

                BusinessCompany::create($data);
                echo "Data added!";
            }

        }
    }

    public function fmcsaCron1() {
        $fmcsa = FMCSABUSINESS::where('STATUS',0)->take(100)->get('DOT_NUMBER')->toArray();
        for($i=0;$i<=99;$i++) {
            $dotNumber = $fmcsa[$i]['DOT_NUMBER'];
            FMCSABUSINESS::where('DOT_NUMBER', $dotNumber)
                ->update(['STATUS'=>1]);

            //carriers/:dotNumber/operation-classification
            // $url1 = "https://mobile.fmcsa.dot.gov/qc/services/carriers/".$dotNumber."/operation-classification?webKey=653a5b0b5aa307372e8cee00e53847fb60d78155&start=1&size=10";

            $url2 = "https://mobile.fmcsa.dot.gov/qc/services/carriers/".$dotNumber."/cargo-carried?webKey=653a5b0b5aa307372e8cee00e53847fb60d78155&start=1&size=10";
            $ch1 = curl_init();
            curl_setopt($ch1, CURLOPT_URL, $url2);
            curl_setopt($ch1, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch1, CURLOPT_CUSTOMREQUEST, 'GET');

            $curlresult1 = curl_exec($ch1);
            $curlresponse1 =  json_decode($curlresult1, true );
            curl_close($ch1);

            // $matchingDotNumbers = '';
            $cargoCarriedContent = $curlresponse1['content'] ?? [];
            if (!is_null($curlresponse1) && is_array($cargoCarriedContent)) {
                foreach ($cargoCarriedContent as $content) {
                    if (isset($content['id']['cargoClassId'], $content['cargoClassDesc'])) {
                        $cargoClassDesc = $content['cargoClassDesc'] ?? null;
                        $cargoClassDescLower = strtolower($cargoClassDesc);
                        $specifiedValues = ['motor vehicles', 'house mover', 'household goods', 'mobile homes'];
                        if (in_array($cargoClassDescLower, $specifiedValues)) {
                            // $matchingDotNumbers = $dotNumber;
                            $data = [
                                'dot_number' => $dotNumber,
                                'cargo_class_id' => $content['id']['cargoClassId'] ?? null,
                                'cargo_class_desc' => $cargoClassDesc,
                            ];
                            FmcsaCargoCarriedData::create($data);
                        }
                    }
                }
            }
        }
        return true;
    }

    function create_slug($string){
        $replace = '-';
        $string = strtolower($string);
        $string = trim($string);
        //replace / and . with white space
        $string = preg_replace("/[\/\.]/", " ", $string);
        $string = preg_replace("/[^a-z0-9_\s-]/", "", $string);
        //remove multiple dashes or whitespaces
        $string = preg_replace("/[\s-]+/", " ", $string);
        //convert whitespaces and underscore to $replace
        $string = preg_replace("/[\s_]/", $replace, $string);
        //limit the slug size
        $string = substr($string, 0, 100);
        //slug is generated
        return $string;

    }

    public function convertBusinessLogo() {
        $businessData = Business::whereNotNull('logo')->get();
        foreach ($businessData as $value) {
            $path = public_path('/images/businesslogo');
            $originalImageFile = $path . '/' . $value->logo;
            if (empty($value->logo) || !file_exists($originalImageFile)) {
                continue;
            }
            $mimeType = mime_content_type($originalImageFile);
            if ($mimeType === 'image/webp') {
                continue;
            }
            $uploadedFile = new UploadedFile($originalImageFile, $value->logo);
            $filenameWithoutExt = pathinfo($value->logo, PATHINFO_FILENAME);
            $newLogoName = $filenameWithoutExt . '_' . time() . '.webp';
            try {
                $imagick = new \Imagick($uploadedFile->getRealPath());
                $imagick->setImageFormat('webp');
                $imagick->setImageCompression(\Imagick::COMPRESSION_JPEG);
                $imagick->setImageCompressionQuality(70);
                $imagick->resizeImage(300, 0, \Imagick::FILTER_LANCZOS, 1);
                $imagick->writeImage($path . '/' . $newLogoName);

                while (filesize($path . '/' . $newLogoName) > 10240) {
                    $currentQuality = $imagick->getImageCompressionQuality();
                    $imagick->setImageCompressionQuality(max(10, $currentQuality - 10));
                    $imagick->writeImage($path . '/' . $newLogoName);

                    if ($currentQuality <= 10) {
                        break;
                    }
                }

                $imagick->clear();
                $imagick->destroy();
                Business::where('business_id', $value->business_id)->update(['logo' => $newLogoName]);
            } catch (\ImagickException $e) {
                \Log::error('library error: ' . $e->getMessage());
                continue;
            }
            Business::where('business_id', $value->business_id)->update(['logo' => $newLogoName]);
        }
        return true;
    }

    public function sendBusinessLogoToVmo() {
        $businessData = Business::whereNotNull('logo')->get();
        foreach ($businessData as $value) {
            $path = public_path('/images/businesslogo');
            $originalImageFile = $path . '/' . $value->logo;
            if (empty($value->logo) || !file_exists($originalImageFile)) {
                continue;
            }
            $uploadedFile = new UploadedFile($originalImageFile, $value->logo);
            $logo = 'data:image/webp;base64,' . base64_encode(file_get_contents($uploadedFile));
            $postFields = json_encode(array('business_id' => $value->business_id, 'filename' => $value->logo, 'image' => $logo));
            $url        = Helper::checkServer()['vm_url'] . '/storecompanylogo';
            $ch         = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_exec($ch);
            if (curl_errno($ch)) {
                echo 'Error:' . curl_error($ch);
                continue;
            }
            curl_close($ch);
        }
        return true;
    }

}
