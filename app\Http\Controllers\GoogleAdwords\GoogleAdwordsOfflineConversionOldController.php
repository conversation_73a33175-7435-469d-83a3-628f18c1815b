<?php

namespace App\Http\Controllers\GoogleAdwords;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\GoogleAdwords\GoogleAdwardsOfflineConversion;

//use GetOpt\GetOpt;
use Google\Ads\GoogleAds\Examples\Utils\ArgumentNames;
use Google\Ads\GoogleAds\Examples\Utils\ArgumentParser;
use Google\Ads\GoogleAds\Lib\OAuth2TokenBuilder;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClient;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClientBuilder;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsException;
use Google\Ads\GoogleAds\Util\V18\ResourceNames;
use Google\Ads\GoogleAds\V18\Errors\GoogleAdsError;
use Google\Ads\GoogleAds\V18\Services\ClickConversion;
use Google\Ads\GoogleAds\V18\Services\ClickConversionResult;
use Google\Ads\GoogleAds\V18\Services\CustomVariable;
use Google\Ads\GoogleAds\V18\Services\UploadClickConversionsRequest;
use Google\Ads\GoogleAds\V18\Services\UploadClickConversionsResponse;
use Google\Ads\GoogleAds\V18\Services\GoogleAdsRow;
use Google\ApiCore\ApiException;
use DateTime;
use DB;
use Exception;
use Helper;
use Log;

class GoogleAdwordsOfflineConversionController extends Controller
{
    /*private const CUSTOMER_ID = '7000201456';
    private const CONVERSION_ACTION_ID = '7000201456';*/

    // Set exactly one of GCLID, GBRAID, or WBRAID.
    // The Google Click ID for which conversions are uploaded.
    /*private const GCLID = null;*/
    // The GBRAID identifier for an iOS app conversion.
    /*private const GBRAID = null;*/
    // The WBRAID identifier for an iOS web conversion.
    /*private const WBRAID = null;*/
    // The conversion date time in "yyyy-mm-dd hh:mm:ss+|-hh:mm" format.
    /*private const CONVERSION_DATE_TIME = '2022-03-21 02:17';
    private const CONVERSION_VALUE = '20';*/
    // Optional: Specify the conversion custom variable ID and value you want to
    // associate with the click conversion upload.
    private const CONVERSION_CUSTOM_VARIABLE_ID = null;
    private const CONVERSION_CUSTOM_VARIABLE_VALUE = null;

    public static $CONVERSION_ACTION_ID;
    public static $CUSTOMER_ID;
    public static $GCLID;
    public static $GBRAID;
    public static $WBRAID;
    public static $CONVERSION_DATE_TIME;
    public static $CONVERSION_VALUE;

    public static function main()
    {

        // Either pass the required parameters for this example on the command line, or insert them
        // into the constants above.
        /*$options = (new ArgumentParser())->parseCommandArguments([
            ArgumentNames::CUSTOMER_ID => GetOpt::REQUIRED_ARGUMENT,
            ArgumentNames::CONVERSION_ACTION_ID => GetOpt::REQUIRED_ARGUMENT,
            ArgumentNames::GCLID => GetOpt::OPTIONAL_ARGUMENT,
            ArgumentNames::GBRAID => GetOpt::OPTIONAL_ARGUMENT,
            ArgumentNames::WBRAID => GetOpt::OPTIONAL_ARGUMENT,
            ArgumentNames::CONVERSION_DATE_TIME => GetOpt::REQUIRED_ARGUMENT,
            ArgumentNames::CONVERSION_VALUE => GetOpt::REQUIRED_ARGUMENT,
            ArgumentNames::CONVERSION_CUSTOM_VARIABLE_ID => GetOpt::OPTIONAL_ARGUMENT,
            ArgumentNames::CONVERSION_CUSTOM_VARIABLE_VALUE => GetOpt::OPTIONAL_ARGUMENT
        ]);*/
        // .ini file path for PMM, MT
        $filePath = base_path("google_ads_php.ini");

        // Generate a refreshable OAuth2 credential for authentication.
        $oAuth2Credential = (new OAuth2TokenBuilder())->fromFile($filePath)->build();

        // Construct a Google Ads client configured from a properties file and the
        // OAuth2 credentials above.
        $googleAdsClient = (new GoogleAdsClientBuilder())
            ->fromFile($filePath)
            ->withOAuth2Credential($oAuth2Credential)
            ->build();


        try {
            Log::info('Offline Conversion:- 1');
            return self::runExample(
                $googleAdsClient,
                GoogleAdwordsOfflineConversionController::$CUSTOMER_ID,
                GoogleAdwordsOfflineConversionController::$CONVERSION_ACTION_ID,
                GoogleAdwordsOfflineConversionController::$GCLID,
                GoogleAdwordsOfflineConversionController::$GBRAID,
                GoogleAdwordsOfflineConversionController::$WBRAID,
                GoogleAdwordsOfflineConversionController::$CONVERSION_DATE_TIME,
                GoogleAdwordsOfflineConversionController::$CONVERSION_VALUE
                /*self::CONVERSION_CUSTOM_VARIABLE_ID,
                self::CONVERSION_CUSTOM_VARIABLE_VALUE*/
            );
        } catch (GoogleAdsException $googleAdsException) {
            Log::info('Offline Conversion:- 2');
            printf(
                "Request with ID '%s' has failed.%sGoogle Ads failure details:%s",
                $googleAdsException->getRequestId(),
                PHP_EOL,
                PHP_EOL
            );
            foreach ($googleAdsException->getGoogleAdsFailure()->getErrors() as $error) {
                /** @var GoogleAdsError $error */
                printf(
                    "\t%s: %s%s",
                    $error->getErrorCode()->getErrorCode(),
                    $error->getMessage(),
                    PHP_EOL
                );
            }
            //exit(1);
        } catch (ApiException $apiException) {
            Log::info('Offline Conversion:- 3');
            printf(
                "ApiException was thrown with message '%s'.%s",
                $apiException->getMessage(),
                PHP_EOL
            );
            //exit(1);
        }
    }

    /**
     * Runs the example.
     *
     * @param GoogleAdsClient $googleAdsClient the Google Ads API client
     * @param int $customerId the customer ID
     * @param int $conversionActionId the ID of the conversion action to upload to
     * @param string|null $gclid the GCLID for the conversion (should be newer than the number of
     *     days set on the conversion window of the conversion action). If set, GBRAID and WBRAID
     *     must be null
     * @param string|null $gbraid The GBRAID identifier for an iOS app conversion. If set, GCLID and
     *     WBRAID must be null
     * @param string|null $wbraid The WBRAID identifier for an iOS web conversion. If set, GCLID and
     *     GBRAID must be null
     * @param string $conversionDateTime the date and time of the conversion (should be after the
     *     click time). The format is "yyyy-mm-dd hh:mm:ss+|-hh:mm", e.g.
     *     “2019-01-01 12:32:45-08:00”
     * @param float $conversionValue the value of the conversion
     * @param string|null $conversionCustomVariableId the ID of the conversion custom variable to
     *     associate with the upload
     * @param string|null $conversionCustomVariableValue the value of the conversion custom
     *     variable to associate with the upload
     */
    // [START upload_offline_conversion]
    public static function runExample (GoogleAdsClient $googleAdsClient, $customerId, $conversionActionId, $gclid, $gbraid, $wbraid, $conversionDateTime, $conversionValue) {

        // query for find $CONVERSION_ACTION_ID pass $CUSTOMER_ID in query and get array Added By BK 06/04/2022
        /*$googleAdsServiceClient = $googleAdsClient->getGoogleAdsServiceClient();
        $query  = 'SELECT conversion_action.id, conversion_action.name FROM conversion_action WHERE customer.id = 5101673588';
        $stream = $googleAdsServiceClient->searchStream($customerId, $query);

        foreach($stream->iterateAllElements() as $googleAdsRow)
        {
            echo $googleAdsRow->getConversionAction()->getId().' - '.$googleAdsRow->getConversionAction()->getName().'<br>';
        } die;*/

        //echo 'runExample--' . $customerId.'-'.$conversionActionId.'-'.$gclid.'-'.$conversionDateTime.'-'.$conversionValue;
        // Verifies that exactly one of gclid, gbraid, and wbraid is specified, as required.
        // See https://developers.google.com/google-ads/api/docs/conversions/upload-clicks for details.

        try {
            Log::info('Offline Conversion:- 4');
            $nonNullFields = array_filter(
                [$gclid],
                function ($field) {
                    return !is_null($field);
                }
            );
            if (count($nonNullFields) !== 1) {
                throw new \UnexpectedValueException(
                    sprintf(
                        "Exactly 1 of gclid, gbraid or wbraid is required, but %d ID values were "
                        . "provided",
                        count($nonNullFields)
                    )
                );
            }

            //echo $conversionDateTime; die;
            // Creates a click conversion by specifying currency as USD.
            $clickConversion = new ClickConversion([
                'conversion_action' => ResourceNames::forConversionAction($customerId, $conversionActionId),
                'conversion_value' => $conversionValue,
                'conversion_date_time' => $conversionDateTime,
                'currency_code' => 'USD'
            ]);

            // Sets the single specified ID field.
            if (!is_null($gclid)) {
                $clickConversion->setGclid($gclid);
            } elseif (!is_null($gbraid)) {
                $clickConversion->setGbraid($gbraid);
            } elseif (!is_null($wbraid)) {
                $clickConversion->setWbraid($wbraid);
            }

            /*if (!is_null($conversionCustomVariableId) && !is_null($conversionCustomVariableValue)) {
                $clickConversion->setCustomVariables([new CustomVariable([
                    'conversion_custom_variable' => ResourceNames::forConversionCustomVariable(
                        $customerId,
                        $conversionCustomVariableId
                    ),
                    'value' => $conversionCustomVariableValue
                ])]);
            }*/

            // Issues a request to upload the click conversion.
            $conversionUploadServiceClient = $googleAdsClient->getConversionUploadServiceClient();
            /** @var UploadClickConversionsResponse $response */
            /*$response = $conversionUploadServiceClient->uploadClickConversions(
                $customerId,
                [$clickConversion],
                true
            );*/
            $response = $conversionUploadServiceClient->uploadClickConversions(
                // Uploads the click conversion. Partial failure should always be set to true.
                UploadClickConversionsRequest::build($customerId, [$clickConversion], true)
            );

            // Prints the status message if any partial failure error is returned.
            // Note: The details of each partial failure error are not printed here, you can refer to
            // the example HandlePartialFailure.php to learn more.
            if ($response->getPartialFailureError() !== null) {
                /*printf(
                    "Partial failures occurred: '%s'.%s",
                    $response->getPartialFailureError()->getMessage(),
                    PHP_EOL
                );*/

                $responseResult = json_encode(
                    array(
                        'api_response' => '',
                        'api_success' => 0 ,
                        'conversion_value' => '',
                        'gclid' => '',
                        'gbraid' => '',
                        'wbraid' => '',
                        'conversion_name' => '',
                        'error' => 1,
                        'error_msg' => $response->getPartialFailureError()->getMessage()
                    )
                );

                Log::info('Offline Conversion:- Partial Failure');
                //print_r($responseResult);
                return $responseResult;
            } else {
                // Prints the result if exists.

                /** @var ClickConversionResult $uploadedClickConversion */
                $success = 1;

                /*printf(
                    "Uploaded click conversion that occurred at '%s' from Google Click ID '%s' " .
                    "to '%s'.%s",
                    $uploadedClickConversion->getConversionDateTime(),
                    $uploadedClickConversion->getGclid(),
                    $uploadedClickConversion->getConversionAction(),
                    PHP_EOL
                );*/

                $responseResult = json_encode(
                    array(
                        'conversion_value' => $conversionValue,
                        'api_response' => $response,
                        'api_success' => $success,
                        'gclid' => $gclid,
                        'gbraid' => $gbraid,
                        'wbraid' => $wbraid,
                        'conversion_name' => '',
                        'error' => 0,
                        'error_msg' => ''
                    )
                );
                Log::info('Offline Conversion:- Success');
                //print_r($responseResult);
                return $responseResult;
            }
        } catch (ApiException $apiException) {
            $responseResult = json_encode(
                array(
                    'api_response' => '',
                    'api_success' => 0 ,
                    'conversion_value' => '',
                    'gclid' => '',
                    'gbraid' => '',
                    'wbraid' => '',
                    'conversion_name' => '',
                    'error' => 1,
                    'error_msg' => $apiException->getMessage()
                )
            );
            Log::info('New Offline Conversion:- Failure');
            //print_r($responseResult);
            return $responseResult;
        }
    }
    // [END upload_offline_conversion]

    public function getOfflineConversion($originName, $date, $flag) {
        //echo $originName; die;
        Log::info('Offline Conversion Start Date:- ' . $date);
        if($originName == 'VM') {
            $originId = array(19, 40, 51, 15, 55, 53); //mst_lead_source table id
        } else if($originName == 'QTM') {
            $originId = array(14, 13, 117, 118); //mst_lead_source table id
        } else if($originName == 'IS') {
            $originId = array(32, 65, 39, 64); //mst_lead_source table id
        } else if($originName == 'VMJR') {
            $originId = array(85, 92); //mst_lead_source table id
        } else if($originName == 'VMHL') {
            $originId = array(86, 93); //mst_lead_source table id
        } else if($originName == 'VMCT') {
            $originId = array(89, 94); //mst_lead_source table id
        }

        //check already added offline conversion
        $alreadyAdded = GoogleAdwardsOfflineConversion::select('lead_id')
            ->whereIn('lead_source_id', $originId)
            ->where('lead_date', $date)
            ->where('type', '!=', 'booked')
            ->get()
            ->toArray();

        //echo date('Y-m-d H:i:s', time()); die;
        $dateTime = new DateTime();
        $formattedDate = $dateTime->format('Y-m-d H:i:s');
        $hour = date('G', strtotime($formattedDate));

        /*run this cron every hours current time is less than 3 to pick yesterday and modify -2 hours but current time is greater than 3
        pick current date and modify -2 hours and avoid already added offline conversation.*/
        $leads = DB::table('lead')
            ->select('lead.lead_id', 'lead.lead_source_id', 'lead.payout','lead_landing_page.gclid', 'lead_landing_page.gclid_type', 'lead.lead_generated_at','lead_moving.from_state','lead_moving.to_state')
            ->join('lead_landing_page','lead_landing_page.lead_id','=','lead.lead_id')
            ->leftJoin('lead_moving','lead_moving.lead_id','=','lead.lead_id')
            ->where('lead.lead_generated_at', 'like', $date.'%')
            ->whereIn('lead.lead_source_id', $originId)
            ->where(DB::raw('HOUR(lead.lead_generated_at)'), ">=", $hour)
            ->where(DB::raw('HOUR(lead.lead_generated_at)'), "<=", $hour + 20)
            ->whereNotIn('lead.lead_id', $alreadyAdded)
            ->where('lead_landing_page.gclid', '<>', '')
            ->take($flag)
            ->get();

        //echo '<pre>'; print_r($leads); die;
        if (count($leads) > 0) { foreach($leads as $lead) {
            $currentDateTime = date('Y-m-d H:i:s');
            $twelveDateTime = date('Y-m-d H:i:s', strtotime('2023-03-12 02:00:00'));
            $conversionTime = date('Y-m-d H:i:s', strtotime($lead->lead_generated_at))."-04:00";

            if ($originName == "VM") {
                // old $actionId 544995790
                $actionId = 973069367; //for Local Qualified lead
                $type = 1;
                if (strcmp($lead->from_state, $lead->to_state)) {
                    $actionId = 973069127; //for LD Qualified lead
                    $type = 2;
                }
                GoogleAdwordsOfflineConversionController::$CUSTOMER_ID = 7000201456;
                GoogleAdwordsOfflineConversionController::$CONVERSION_ACTION_ID = $actionId;
            } else if($originName == "QTM") {
                // old $actionId 565368391
                $actionId = 987550224; //for Local Qualified lead
                $type = 1;
                if (strcmp($lead->from_state, $lead->to_state)) {
                    $actionId = 987548535; //for LD Qualified lead
                    $type = 2;
                }
                GoogleAdwordsOfflineConversionController::$CUSTOMER_ID = 3189698923;
                GoogleAdwordsOfflineConversionController::$CONVERSION_ACTION_ID = $actionId;
            } else if ($originName == "IS") {
                // old $actionId 565270485
                $actionId = 986657687; //for Local Qualified lead
                $type = 1;
                if (strcmp($lead->from_state, $lead->to_state)) {
                    $actionId = 987423583; //for LD Qualified lead
                    $type = 2;
                }
                GoogleAdwordsOfflineConversionController::$CUSTOMER_ID = 5101673588;
                GoogleAdwordsOfflineConversionController::$CONVERSION_ACTION_ID = $actionId;
            }

            //echo $lead->gclid;
            if ($lead->gclid_type == '0') { //for GCLID
                GoogleAdwordsOfflineConversionController::$GCLID = $lead->gclid;
            } else if ($lead->gclid_type == '1') { //for GBRAID
                GoogleAdwordsOfflineConversionController::$GBRAID = $lead->gclid;
            } else if ($lead->gclid_type == '2') { //for WBRAID
                GoogleAdwordsOfflineConversionController::$WBRAID = $lead->gclid;
            }
            //echo $conversionTime;
            GoogleAdwordsOfflineConversionController::$CONVERSION_DATE_TIME = $conversionTime;
            //echo round($lead->payout, 2);
            GoogleAdwordsOfflineConversionController::$CONVERSION_VALUE = ($lead->payout > 0) ? round($lead->payout, 2) : 0.1;

            try {
                //currently we are not submitting local & long distance lead conversion for VMJR, VMHL & VMCT. only submitting main conversion
                if ($originName == "VM" || $originName == "QTM" || $originName == "IS") {
                    $response = GoogleAdwordsOfflineConversionController::main();
                    // store response
                    $responseData = json_decode($response, true);
                    $status = 'yes';
                    if($responseData['error'] == 1) {
                        $status = 'no';
                    }

                    if ($type == 1) {
                        $type = 'local';
                    } else if ($type == 2) {
                        $type = 'long';
                    }

                    $record = [
                        'lead_source_id' => $lead->lead_source_id,
                        'lead_id' => $lead->lead_id,
                        'lead_date' => $date,
                        'response' => $response,
                        'status' => $status,
                        'type' => $type,
                        'created_at' => date('Y-m-d H:i:s')
                    ];

                    //echo '<pre>'; print_r($record); die;
                    GoogleAdwardsOfflineConversion::create($record);
                }

                /*try {
                    if ($originName == "VM") {
                        GoogleAdwordsOfflineConversionController::$CUSTOMER_ID = 7000201456;
                        GoogleAdwordsOfflineConversionController::$CONVERSION_ACTION_ID = 544995790;
                    } else if($originName == "QTM") {
                        GoogleAdwordsOfflineConversionController::$CUSTOMER_ID = 3189698923;
                        GoogleAdwordsOfflineConversionController::$CONVERSION_ACTION_ID = 565368391;
                    } else if ($originName == "IS") {
                        GoogleAdwordsOfflineConversionController::$CUSTOMER_ID = 5101673588;
                        GoogleAdwordsOfflineConversionController::$CONVERSION_ACTION_ID = 565270485;
                    } else if ($originName == "VMJR") {
                        GoogleAdwordsOfflineConversionController::$CUSTOMER_ID = 2638396915;
                        GoogleAdwordsOfflineConversionController::$CONVERSION_ACTION_ID = 1052861255;
                    } else if ($originName == "VMHL") {
                        GoogleAdwordsOfflineConversionController::$CUSTOMER_ID = 2638396915;
                        GoogleAdwordsOfflineConversionController::$CONVERSION_ACTION_ID = 1053846696;
                    } else if ($originName == "VMCT") {
                        GoogleAdwordsOfflineConversionController::$CUSTOMER_ID = 2638396915;
                        GoogleAdwordsOfflineConversionController::$CONVERSION_ACTION_ID = 1053847842;
                    }
                    //echo $lead->gclid;
                    if ($lead->gclid_type == '0') { //for GCLID
                        GoogleAdwordsOfflineConversionController::$GCLID = $lead->gclid;
                    } else if ($lead->gclid_type == '1') { //for GBRAID
                        GoogleAdwordsOfflineConversionController::$GBRAID = $lead->gclid;
                    } else if ($lead->gclid_type == '2') { //for WBRAID
                        GoogleAdwordsOfflineConversionController::$WBRAID = $lead->gclid;
                    }
                    //echo $conversionTime;
                    GoogleAdwordsOfflineConversionController::$CONVERSION_DATE_TIME = $conversionTime;
                    //echo round($lead->payout, 2);
                    GoogleAdwordsOfflineConversionController::$CONVERSION_VALUE = ($lead->payout > 0) ? round($lead->payout, 2) : 0.1;
                    $response = GoogleAdwordsOfflineConversionController::main();
                    // store response
                    $responseData = json_decode($response, true);
                    $status = 'yes';
                    if($responseData['error'] == 1) {
                        $status = 'no';
                    }

                    $newRecord = [
                        'lead_source_id' => $lead->lead_source_id,
                        'lead_id' => $lead->lead_id,
                        'lead_date' => $date,
                        'response' => $response,
                        'status' => $status,
                        'type' => 'main',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    //echo '<pre>'; print_r($record); die;
                    GoogleAdwardsOfflineConversion::create($newRecord);

                } catch (Exception $e) {
                    Log::info('New Offline Conversion:- ' . $e->getMessage());
                    echo $e->getMessage();
                }*/
            } catch (Exception $e) {
                Log::info('Offline Conversion Exception:- ' . $e->getMessage());
                echo $e->getMessage();
            }
        } } else {
            echo "No records found.";
        }
    }

    public function offlineConversion($date='') {
        //$runDate = date('Y-m-d',strtotime("-1 days"));
        //it will fetch 22 to 24 hours earliers records from lead table to submit google adwords
        $flag = 100;
        $runDate = date('Y-m-d');
        $runDate = date('Y-m-d', strtotime("-1 days"));
        if(isset($date) && !empty($date)) {
            $runDate = $date;
            $flag = 20;
        }
        //dd($runDate);
        //echo $runDate; die;
        $this->getOfflineConversion('VM', $runDate, $flag); //for VM
        $this->getOfflineConversion('QTM', $runDate, $flag); //for QTM
        $this->getOfflineConversion('IS', $runDate, $flag); //for IS
        $this->getOfflineConversion('VMJR', $runDate, $flag);//for VMJR
        $this->getOfflineConversion('VMHL', $runDate, $flag);//for VMHL
        $this->getOfflineConversion('VMCT', $runDate, $flag);//for VMCT
    }
}
