<?php

namespace App\Http\Controllers\API\Lead;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\DevDebug;
use App\Models\Outbound\LeadCallNumberAreacode;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LeadCallSpamNumberReplace extends Controller
{
    public function fetchSpamNumberData($phone) {
        try {
            $leadCallData = LeadCallNumberAreacode::query()
                    ->where('phone', $phone)
                    ->join(DB::raw('(
                        SELECT MIN(zipcode_id) as zipcode_id, areacode, state
                        FROM mst_zipcode
                        GROUP BY areacode
                    ) as mz'), 'lead_call_numbers_areacode.areacode', '=', 'mz.areacode')
                    ->select(
                        'lead_call_numbers_areacode.*',
                        'mz.state'
                    )
                    ->first();
            $newPhone = null;
            if ($leadCallData) {
                $comment = $leadCallData->Comments;
                $source = $leadCallData->MstLeadSource->lead_parent_source ?? '';
                if (!$leadCallData->areacode || !$leadCallData->state) {
                    return response()->json([
                        'status' => 'failed',
                        'message' => "Areacode and State not found",
                    ], 404);
                }

                if ($comment && str_contains(strtolower($comment), 'commion')) {
                    $commioNumber = $this->searchCommioNumber($leadCallData->areacode, $leadCallData->state);
                    if ($commioNumber->message == "success" && isset($commioNumber->dids[0])) {
                        $did = $commioNumber->dids[0]->id;
                        $purchaseNumber = $this->purchaseCommioNumber($did);
                        if ($purchaseNumber->status == "created" && $purchaseNumber->id) {
                            $orderId = $purchaseNumber->id;
                            $completeOrder = $this->completePurchaseCommioNumber($orderId);
                            if ($completeOrder->status == "completed") {
                                $newPhone = ( strlen($did) > 10 ) ? substr($did, 1, strlen($did)) : $did;
                                $this->addObNumberInTable($leadCallData->phone, $newPhone);
                                // $callerIdName = match (strtolower($source)) {
                                //     'qtm' => 'Quote The Move',
                                //     'vm' => 'Van Lines Move',
                                //     'is' => 'InterstatesMove',
                                //     default => null
                                // };
                                // $this->storeCommioCnam($newPhone, $callerIdName);
                            }
                        }
                    }
                } elseif ($comment && str_contains(strtolower($comment), 'plivo')) {
                    $plivoNumber = $this->searchPlivoNumber($leadCallData->areacode, $leadCallData->state);
                    if ($plivoNumber['status_code'] == 200 && isset($plivoNumber['data']['objects'])) {
                        $numberToBuy = $plivoNumber['data']['objects'][0]['number'] ?? null;
                        if ($numberToBuy) {
                            $purchaseResult = $this->purchasePlivoNumber($numberToBuy);
                            if ($purchaseResult['success']) {
                                $completePlivoNumber = $this->completePurchasePlivoNumber($numberToBuy);
                                if ($completePlivoNumber['success']) {
                                    $newPhone = ( strlen($numberToBuy) > 10 ) ? substr($numberToBuy, 1, strlen($numberToBuy)) : $numberToBuy;
                                    $this->addObNumberInTable($leadCallData->phone, $newPhone);
                                }
                            }
                        }
                    }
                }

                return response()->json([
                    'status' => 'success',
                    'message' => 'Phone replacement process complete',
                    'newPhone' => $newPhone
                ], 200);
            }

            return response()->json([
                'status' => 'failed',
                'message' => "Phone number not found",
            ], 404);

        } catch (Exception $ex) {
            Log::info($ex);
            return response()->json([
                'status' => 'failed',
                'message' => $ex->getMessage(),
            ], 500);
        }
    }

    public function searchCommioNumber($areacode, $state) {
        $commioToken = "milan:68758d120ac29760793bb4c940550281ec4d51e6";
        $commioToken = base64_encode($commioToken);

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.thinq.com/inbound/get-numbers?searchType=domestic&searchBy=NPA&NPA='.$areacode.'&quantity=1&contiguous=false&state='.strtoupper($state).'&related=true',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Authorization: Basic ' . $commioToken . ''
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);
        $response = json_decode($response);

        DevDebug::create([
            'sr_no' => 121,
            'result' => 'searchCommioNumber Request: ' . json_encode($response),
            'created_at' => date("Y-m-d H:i:s"),
        ]);
        return $response;
    }

    public function searchPlivoNumber($areacode, $state)
    {
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $general_variable_2 = Helper::checkServer()['general_variable_2'];
        $authId = $endDecObj->customeDecryption($general_variable_1);
        $authToken = $endDecObj->customeDecryption($general_variable_2);

        $region = strtoupper($state);
        $url = "https://api.plivo.com/v1/Account/{$authId}/PhoneNumber/?country_iso=US&type=local&services=voice,sms&pattern={$areacode}&state={$region}&quantity=1";

        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
            CURLOPT_USERPWD => "$authId:$authToken",
        ]);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $curlError = curl_error($curl);
        curl_close($curl);

        if ($curlError) {
            return [
                'success' => false,
                'message' => 'Curl error: ' . $curlError,
                'status_code' => 500,
            ];
        }

        if ($httpCode === 200) {
            $data = json_decode($response, true);
            return [
                'success' => true,
                'data' => $data,
                'status_code' => 200,
            ];
        }

        return [
            'success' => false,
            'message' => 'Plivo API error',
            'status_code' => $httpCode,
            'response' => $response,
        ];
    }

    public function purchaseCommioNumber($did) {
        $accountId = 22374;
        $commioToken = "milan:68758d120ac29760793bb4c940550281ec4d51e6";
        $commioToken = base64_encode($commioToken);
        $postFields = [
            "order" => [
                "tns" => [
                    [
                        "caller_id" => null,
                        "account_location_id" => null,
                        "sms_routing_profile_id" => 699,
                        "route_id" => 13720,
                        "features" => [
                            "cnam" => false,
                            "sms" => true,
                            "e911" => false
                        ],
                        "did" => $did
                    ]
                ],
                "blocks" => []
            ],
        ];

        DevDebug::create([
            'sr_no' => 122,
            'result' => 'purchaseCommioNumber Request: ' . json_encode($postFields),
            'created_at' => date("Y-m-d H:i:s"),
        ]);

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.thinq.com/account/' . $accountId . '/origination/order/create',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($postFields),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Authorization: Basic ' . $commioToken . ''
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);
        $response = json_decode($response);

        DevDebug::create([
            'sr_no' => 123,
            'result' => 'purchaseCommioNumber Response: ' . json_encode($response),
            'created_at' => date("Y-m-d H:i:s"),
        ]);
        return $response;
    }

    public function completePurchaseCommioNumber($orderId) {
        $accountId = 22374;
        $commioToken = "milan:68758d120ac29760793bb4c940550281ec4d51e6";
        $commioToken = base64_encode($commioToken);
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.thinq.com/account/' . $accountId . '/origination/order/complete/' . $orderId,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json',
                'Authorization: Basic ' . $commioToken . ''
            ),
        ));

        $response = curl_exec($curl);
        curl_close($curl);
        $response = json_decode($response);
        DevDebug::create([
            'sr_no' => 124,
            'result' => 'completePurchaseCommioNumber Response: ' . json_encode($response),
            'created_at' => date("Y-m-d H:i:s"),
        ]);
        return $response;
    }

    // public function storeCommioCnam($phoneNumber, $callerIdName)
    // {
    //     $accountId = 22374;
    //     $commioToken = "milan:68758d120ac29760793bb4c940550281ec4d51e6";
    //     $commioToken = base64_encode($commioToken);
    //     $url = "https://api.commio.com/V2.0/accounts/{$accountId}/cnam-storage";

    //     $data = [
    //         'telephoneNumber' => $phoneNumber,
    //         'callerIdName' => $callerIdName,
    //     ];

    //     $headers = [
    //         "Authorization: Bearer {$commioToken}",
    //         "Content-Type: application/json"
    //     ];

    //     $ch = curl_init($url);
    //     curl_setopt($ch, CURLOPT_POST, true);
    //     curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    //     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    //     curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    //     $response = curl_exec($ch);
    //     $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    //     $error = curl_error($ch);
    //     curl_close($ch);

    //     DevDebug::create([
    //         'sr_no' => 125,
    //         'result' => 'storeCommioCnam Response: ' . json_encode([
    //             'http_code' => $httpCode,
    //             'error' => $error,
    //             'response' => $response,
    //         ]),
    //         'created_at' => date("Y-m-d H:i:s"),
    //     ]);

    //     if ($error) {
    //         return [
    //             'success' => false,
    //             'error' => $error,
    //             'http_code' => $httpCode
    //         ];
    //     }

    //     return [
    //         'success' => $httpCode === 200 || $httpCode === 201,
    //         'response' => json_decode($response, true),
    //         'http_code' => $httpCode
    //     ];
    // }

    public function purchasePlivoNumber($number)
    {
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $general_variable_2 = Helper::checkServer()['general_variable_2'];
        $authId = $endDecObj->customeDecryption($general_variable_1);
        $authToken = $endDecObj->customeDecryption($general_variable_2);

        $url = "https://api.plivo.com/v1/Account/{$authId}/PhoneNumber/{$number}/";

        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
            CURLOPT_USERPWD => "$authId:$authToken",
        ]);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $curlError = curl_error($curl);
        curl_close($curl);
        Log::info("purchasePlivoNumber: " . $response);
        DevDebug::create([
            'sr_no' => 125,
            'result' => 'purchasePlivoNumber Response: ' . json_encode(json_decode($response), JSON_PRETTY_PRINT),
            'created_at' => date("Y-m-d H:i:s"),
        ]);
        if ($curlError) {
            return [
                'success' => false,
                'message' => 'Curl error: ' . $curlError,
                'status_code' => 500,
            ];
        }

        if ($httpCode === 201) {
            return [
                'success' => true,
                'message' => 'Number purchased successfully',
                'response' => json_decode($response, true),
                'status_code' => 201,
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to purchase number',
            'response' => $response,
            'status_code' => $httpCode,
        ];
    }

    public function completePurchasePlivoNumber($number)
    {
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $general_variable_2 = Helper::checkServer()['general_variable_2'];
        $authId = $endDecObj->customeDecryption($general_variable_1);
        $authToken = $endDecObj->customeDecryption($general_variable_2);
        $relayAppId = "*****************";
        $aliasName = "IS_OB";
        $para = json_encode(array('time_limit' => '3600', 'app_id' => $relayAppId, 'alias' => $aliasName));

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, 'https://api.plivo.com/v1/Account/' . $authId . '/Number/' . $number . '/');
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $para);
        curl_setopt($curl, CURLOPT_USERPWD, $authId . ':' . $authToken);
        $headers = array();
        $headers[] = 'Content-Type: application/json';
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $curlError = curl_error($curl);
        curl_close($curl);
        Log::info("completePurchasePlivoNumber: " . $response);
        DevDebug::create([
            'sr_no' => 126,
            'result' => 'completePurchasePlivoNumber Response: ' . json_encode(json_decode($response), JSON_PRETTY_PRINT),
            'created_at' => date("Y-m-d H:i:s"),
        ]);
        if ($curlError) {
            return [
                'success' => false,
                'message' => 'Curl error: ' . $curlError,
                'status_code' => 500,
            ];
        }

        if ($httpCode === 202 || $httpCode === 201) {
            return [
                'success' => true,
                'message' => 'Number purchased successfully',
                'response' => json_decode($response, true),
                'status_code' => $httpCode,
            ];
        }

        return [
            'success' => false,
            'message' => 'Failed to purchase number',
            'response' => $response,
            'status_code' => $httpCode,
        ];
    }

    public function addObNumberInTable($oldNumber, $newNumber) {
        if (strlen($newNumber) === 11 && $newNumber[0] === '1') {
            $newNumber = substr($newNumber, 1);
        }
        $newNumberwithone = '1' .$newNumber;
        $formatted = substr($newNumber, 0, 3) . ' ' .substr($newNumber, 3, 3) . '-' .substr($newNumber, 6);
        DB::statement("INSERT INTO lead_call_numbers_areacode (phone, callit_number, areacode, lead_source_id, Comments) SELECT ?, ?, areacode, lead_source_id, Comments FROM lead_call_numbers_areacode WHERE phone = ? ", [$newNumberwithone, $formatted, $oldNumber]);
        DB::statement("UPDATE lead_call_numbers_areacode SET STATUS = 'inactive', is_deleted = 'yes' WHERE phone = ? ", [$oldNumber]);
        DB::statement("UPDATE lead_call_numbers_areacode SET STATUS = 'active', is_deleted = 'no' WHERE phone = ? ", [$newNumberwithone]);
        DB::statement("INSERT INTO lead_call_number(phone, callit_number, lead_source_id, is_current, DAY, is_sms, lead_category_id, comment, status, phone_source, provider, number_type)SELECT ?, ?, lead_source_id, is_current, DAY, is_sms, lead_category_id, comment, status, phone_source, provider, number_type FROM lead_call_number WHERE lead_call_number_id = (SELECT lead_call_number_id FROM lead_call_number WHERE phone = ?)", [$newNumberwithone, $formatted, $oldNumber]);
    }

    public function releaseNumber(Request $request) {
        $phones = $request->input('phones');
        if (!$phones) {
            return response()->json(['error' => 'No phone numbers provided'], 400);
        }
        $phonesArray = is_string($phones) ? explode(',', $phones) : (array) $phones;
        $phonesArray = array_map('trim', $phonesArray);
        $phonesArray = array_filter($phonesArray);
        if (empty($phonesArray)) {
            return response()->json(['error' => 'No valid phone numbers found'], 400);
        }

        $leadCallData = DB::table('lead_call_numbers_areacode')
            ->join('lead_call_number', 'lead_call_numbers_areacode.phone', '=', 'lead_call_number.phone')
            ->whereIn('lead_call_number.phone', $phonesArray)
            ->groupBy('lead_call_numbers_areacode.phone')
            ->select('lead_call_numbers_areacode.*')
            ->get();

        $commioPhones = [];
        $plivoPhones = [];

        foreach ($leadCallData as $value) {
            $comment = strtolower($value->Comments ?? '');
            if (str_contains($comment, 'commio')) {
                $commioPhones[] = $value->phone;
            } elseif (str_contains($comment, 'plivo')) {
                $plivoPhones[] = $value->phone;
            }
        }

        $results = [];
        $deletedNumbers = [];
        if (!empty($commioPhones)) {
            $commioResults = $this->releaseCommioNumbers($commioPhones);
            $results['commio'] = $commioResults;

            foreach ($commioResults as $number => $result) {
                if ($result['status'] === 'disconnected') {
                    $deletedNumbers[] = $number;
                }
            }
        }

        if (!empty($plivoPhones)) {
            $plivoResults = $this->releasePlivoNumbers($plivoPhones);
            $results['plivo'] = $plivoResults;

            foreach ($plivoResults as $number => $result) {
                if ($result['action'] === 'deleted') {
                    $deletedNumbers[] = $number;
                }
            }
        }
        if (!empty($deletedNumbers)) {
            DB::table('lead_call_number')->whereIn('phone', $deletedNumbers)->update(['status' => 'inactive']);
        }
        return response()->json(['status' => 'completed', 'details' => $results]);
    }

    public function releaseCommioNumbers(array $phones)
    {
        $accountId = 22374;
        $username = 'milan';
        $password = '68758d120ac29760793bb4c940550281ec4d51e6';
        $commioToken = base64_encode("$username:$password");
        $results = [];

        foreach ($phones as $phone) {
            $checkUrl = "https://api.thinq.com/origination/did/search2/did/{$accountId}?add_summaries=true&id={$phone}&page=1&rows=25&sort=id:desc";

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $checkUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_USERPWD, "$username:$password");

            $checkResponse = curl_exec($ch);
            curl_close($ch);

            $checkData = json_decode($checkResponse, true);
            if (!empty($checkData['rows'])) {
                $postFields = [
                    "dids" => [$phone]
                ];

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => "https://api.thinq.com/account/{$accountId}/origination/disconnect",
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => json_encode($postFields),
                    CURLOPT_HTTPHEADER => array(
                        'Content-Type: application/json',
                        'Authorization: Basic ' . $commioToken
                    ),
                ));

                $disconnectResponse = curl_exec($curl);
                curl_close($curl);

                $disconnectData = json_decode($disconnectResponse, true);
                $results[$phone] = [
                    'status' => 'disconnected',
                    'response' => $disconnectData
                ];
            } else {
                $results[$phone] = [
                    'status' => 'not_found'
                ];
            }
        }
        return $results;
    }

    public function releasePlivoNumbers(array $phones)
    {
        $helper = new Helper;
        $serverConfig = Helper::checkServer();
        $authId = $helper->customeDecryption($serverConfig['general_variable_1']);
        $authToken = $helper->customeDecryption($serverConfig['general_variable_2']);

        $responses = [];

        foreach ($phones as $number) {
            $url = "https://api.plivo.com/v1/Account/{$authId}/Number/{$number}/";

            // Step 1: Check if number exists
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
                CURLOPT_USERPWD => "$authId:$authToken",
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/json'
                ]
            ]);
            $checkResponse = curl_exec($curl);
            $checkHttpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            if ($checkHttpCode === 200) {
                $curl = curl_init();
                curl_setopt_array($curl, [
                    CURLOPT_URL => $url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_CUSTOMREQUEST => "DELETE",
                    CURLOPT_HTTPHEADER => [
                        'Content-Type: application/json',
                        'Authorization: Basic ' . base64_encode("{$authId}:{$authToken}")
                    ]
                ]);
                $deleteResponse = curl_exec($curl);
                $deleteHttpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                $curlError = curl_error($curl);
                curl_close($curl);

                $responses[$number] = [
                    'status' => $deleteHttpCode,
                    'action' => 'deleted',
                    'response' => $deleteResponse !== false ? json_decode($deleteResponse, true) : null,
                    'error' => $deleteResponse === false ? $curlError : null
                ];
            } elseif ($checkHttpCode === 404) {
                $responses[$number] = [
                    'status' => 404,
                    'action' => 'not_found',
                    'response' => 'Number does not exist on Plivo'
                ];
            } else {
                $responses[$number] = [
                    'status' => $checkHttpCode,
                    'action' => 'check_failed',
                    'response' => $checkResponse !== false ? json_decode($checkResponse, true) : null
                ];
            }
        }

        return $responses;
    }
}
