<?php

namespace App\Models\Outbound;

use App\Models\Master\MstLeadSource;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LeadCallNumberAreacode extends Model
{
    use HasFactory;
    protected $table = "lead_call_numbers_areacode";
    protected $primaryKey = "lead_call_numbers_areacode_id";

    public $timestamps = false;

    protected $fillable = [
        "lead_call_numbers_areacode_id",
        "phone",
        "callit_number",
        "areacode",
        "lead_source_id",
        "status",
        "Comments",
        "is_deleted",
        "created_at"
    ];

    public function MstLeadSource() {
        return $this->hasOne(MstLeadSource::class, 'lead_source_id', 'lead_source_id');
    }
}
