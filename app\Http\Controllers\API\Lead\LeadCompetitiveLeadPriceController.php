<?php

namespace App\Http\Controllers\API\Lead;

use App\Http\Controllers\Controller;
use App\Models\Campaign\Campaign;
use App\Models\Campaign\CampaignCarTransport;
use App\Models\Campaign\CampaignMoving;
use App\Models\Lead\LeadTransferCall;
use App\Models\Master\MSTLeadPriceInfo;
use App\Models\Master\MSTTimeLeadPriceRange;
use Carbon\Carbon;

class LeadCompetitiveLeadPriceController extends Controller
{
    public function getCompetitiveLeadPriceRange($id) {
      try {
          $campaignData = Campaign::where('campaign_id', $id)->first();
          if ($campaignData->lead_type_id == '1') {
            $leadType = 'Lead';
          } else {
            $leadType = 'Call';
          }

          $campaign_type = $campaignData->mstCampaignType->campaign_type;
          $campaignMovingData = CampaignMoving::where('campaign_id', $id)->first();
          $campaignData['coverage'] = '';
          if ($campaignMovingData) {
            $campaignData['coverage'] = $campaignMovingData->move_type;
          }
          $leadPriceInfo = MSTLeadPriceInfo::where('lead_type', $leadType)->where('campaign_type', $campaign_type)->where('distance', ucfirst($campaignData->coverage))->first();
          $calculatedPrices = [];
          if ($leadPriceInfo) {
            $baselineValue = $leadPriceInfo->base_line_value;
            $dayOfWeek = Carbon::now()->format('l');
            if ($dayOfWeek === 'Saturday') {
              $baselineValue -= ($baselineValue * 0.20); // 20% deduction
            } elseif ($dayOfWeek === 'Sunday') {
              $baselineValue -= ($baselineValue * 0.40); // 40% deduction
            }

            $timeLeadPriceRanges = MSTTimeLeadPriceRange::all();
            foreach ($timeLeadPriceRanges as $timeRange) {
              $avgWinningBidPrice = $this->calculatePrice($baselineValue, $timeRange->avg_winning_bid);
              $competitiveRangeMinPrice = $this->calculatePrice($baselineValue, $timeRange->competitive_range_min);
              $competitiveRangeMaxPrice = $this->calculatePrice($baselineValue, $timeRange->competitive_range_max);
              $calculatedPrices[] = [
                'id' => $timeRange->id,
                'time_slot' => $timeRange->time_slot,
                'baseline_value' => $baselineValue,
                'avg_winning_bid_percentage' => $timeRange->avg_winning_bid,
                'avg_winning_bid_price' => round($avgWinningBidPrice, 2),
                'competitive_range_min_percentage' => $timeRange->competitive_range_min,
                'competitive_range_min_price' => round($competitiveRangeMinPrice, 2),
                'competitive_range_max_percentage' => $timeRange->competitive_range_max,
                'competitive_range_max_price' => round($competitiveRangeMaxPrice, 2),
                'price_range' => [
                  'min' => round($competitiveRangeMinPrice, 2),
                  'max' => round($competitiveRangeMaxPrice, 2),
                  'avg' => round($avgWinningBidPrice, 2)
                ]
              ];
            }
          }

          return response()->json([
            'status' => 'success',
            'message' => 'Competitive Lead Price Range data retrieved successfully',
            'data' => [
              'lead_price_info' => $leadPriceInfo,
              'time_wise_pricing' => $calculatedPrices,
            ]
          ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve competitive lead price range data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    private function calculatePrice($baselineValue, $percentage)
    {
        return ($baselineValue * $percentage) / 100;
    }

    public function fetchLiveTransferLeadPrice() {
      $highestPayoutLead = LeadTransferCall::where('created_at', '>=', Carbon::now()->subMinutes(30))->orderByDesc('payout')->first();
      $payout = 85;
      if ($highestPayoutLead && $highestPayoutLead->payout > 85) {
        $payout = $highestPayoutLead->payout;
      }
      MSTLeadPriceInfo::whereIn('id', [9, 10, 11, 12])->update(['base_line_value' => $payout]);
      return $payout;
    }
}