<?php

namespace App\Exports;

use App\Models\Leads\Leads;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use DB;

class RexDirectCallExport implements FromCollection, WithHeadings
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function headings(): array
    {
        //Put Here Header Name That you want in your excel sheet
        return [
            'Phone',
            'Recording URL',
            'Duration',
            'Call Date',
            'Call Time',
            'Payout',
            ''
        ];
    }

    public function collection()
    {
        $startDate  = date("Y-m-d", mktime(0, 0, 0, date("m")-1, 1));
        $endDate    = date("Y-m-d", mktime(0, 0, 0, date("m"), 0));
        $leadArray  = array();
        $leadDetail1= DB::select("SELECT ll.phone, l.recording_url, l.duration, date(l.created_at) AS date, time(l.created_at) AS time, IF(l.duration > 30 OR l.duration IS NULL, 68, 0) AS payout
                                  FROM lead_transfer_call l 
                                  LEFT JOIN prod_linkup.lead ll ON l.lead_id = ll.lead_id
                                  LEFT JOIN lead_moving lm ON l.lead_id = lm.lead_id
                                  WHERE l.campaign_id IN (SELECT c.campaign_id FROM campaign c INNER JOIN campaign_moving cm ON c.campaign_id = cm.campaign_id WHERE cm.move_type = 'long' AND c.business_id = 55) AND l.created_at >= '".$startDate." 00:00:00' AND l.created_at <= '".$endDate." 23:59:59'
                                  AND HOUR(l.created_at) >= 11 AND HOUR(l.created_at) < 19 AND WEEKDAY(l.created_at)>= 0 AND WEEKDAY(l.created_at) < 5 /*AND WEEKDAY(l.created_at) <> 4*/ 
                                  AND (lm.from_state NOT IN ('AK', 'HI') OR lm.from_state IS NULL)");
        //echo '<pre>'; print_r($leadDetail); exit;

        if (count($leadDetail1) > 0) { for ($i=0; $i < count($leadDetail1); $i++) {
            $leadArray[] = array(
                $leadDetail1[$i]->phone,
                $leadDetail1[$i]->recording_url,
                $leadDetail1[$i]->duration,
                $leadDetail1[$i]->date,
                $leadDetail1[$i]->time,
                $leadDetail1[$i]->payout,
                'Long Distance Calls, Business Hours'
            );
        } }

        $leadDetail2= DB::select("SELECT ll.phone, l.recording_url, l.duration, date(l.created_at) AS date, time(l.created_at) AS time, IF(l.duration > 30 OR l.duration IS NULL, 48, 0) AS payout, WEEKDAY(l.created_at)
                                  FROM lead_transfer_call l
                                  LEFT JOIN `lead` ll ON l.lead_id = ll.lead_id
                                  LEFT JOIN lead_moving lm ON l.lead_id = lm.lead_id
                                  WHERE l.campaign_id IN (SELECT c.campaign_id FROM campaign c INNER JOIN campaign_moving cm ON c.campaign_id = cm.campaign_id WHERE cm.move_type = 'long' AND c.business_id = 55) AND l.created_at >= '".$startDate." 00:00:00' AND l.created_at <= '".$endDate." 23:59:59'
                                  AND ( (HOUR(l.created_at) >= 13 AND HOUR(l.created_at) < 19 AND ( WEEKDAY(l.created_at) >=  5)) /*OR (HOUR(l.created_at) >= 18)*/ )
                                  AND (lm.from_state NOT IN ('AK', 'HI') OR lm.from_state IS NULL)");
        //echo '<pre>'; print_r($leadDetail); exit;

        if (count($leadDetail2) > 0) { for ($i=0; $i < count($leadDetail2); $i++) {
            $leadArray[] = array(
                $leadDetail2[$i]->phone,
                $leadDetail2[$i]->recording_url,
                $leadDetail2[$i]->duration,
                $leadDetail2[$i]->date,
                $leadDetail2[$i]->time,
                $leadDetail2[$i]->payout,
                'Long Distance Calls, Off-Hours'
            );
        } }

        $leadDetail3= DB::select("SELECT ll.phone, l.recording_url, l.duration, date(l.created_at) AS date, time(l.created_at) AS time, IF(l.duration > 75 OR l.duration IS NULL, 14, 0) AS payout
                                  FROM lead_transfer_call l 
                                  LEFT JOIN `lead` ll ON l.lead_id = ll.lead_id
                                  LEFT JOIN lead_moving lm ON l.lead_id = lm.lead_id
                                  WHERE l.campaign_id IN (SELECT c.campaign_id FROM campaign c INNER JOIN campaign_moving cm ON c.campaign_id = cm.campaign_id WHERE cm.move_type = 'local' AND c.business_id = 55) AND l.created_at >= '".$startDate." 00:00:00' AND l.created_at <= '".$endDate." 23:59:59'
                                  AND HOUR(l.created_at) >= 08 AND HOUR(l.created_at) < 21 /*AND WEEKDAY(l.created_at)>= 0 AND WEEKDAY(l.created_at) < 5*/ 
                                  AND (lm.from_state NOT IN ('AK', 'HI') OR lm.from_state IS NULL)");
        //echo '<pre>'; print_r($leadDetail); exit;

        if (count($leadDetail3) > 0) { for ($i=0; $i < count($leadDetail3); $i++) {
            $leadArray[] = array(
                $leadDetail3[$i]->phone,
                $leadDetail3[$i]->recording_url,
                $leadDetail3[$i]->duration,
                $leadDetail3[$i]->date,
                $leadDetail3[$i]->time,
                $leadDetail3[$i]->payout,
                'Local Calls, Business Hours'
            );
        } }

        /*$leadDetail4= DB::select("SELECT ll.phone, l.recording_url, l.duration, date(l.created_at) AS date, time(l.created_at) AS time, IF(l.duration > 90 OR l.duration IS NULL, 4, 0) AS payout
                                  FROM lead_transfer_call l
                                  LEFT JOIN prod_linkup.lead ll ON l.lead_id = ll.lead_id
                                  LEFT JOIN lead_moving lm ON l.lead_id = lm.lead_id
                                  WHERE l.campaign_id IN (4964, 4967) AND l.created_at >= '".$startDate." 00:00:00' AND l.created_at <= '".$endDate." 23:59:59'
                                  AND ( (HOUR(l.created_at) >= 08 AND HOUR(l.created_at) < 18 AND ( WEEKDAY(l.created_at) >= 5)) OR (HOUR(l.created_at) >= 18) )
                                  AND (lm.from_state NOT IN ('AK', 'HI') OR lm.from_state IS NULL)");
        //echo '<pre>'; print_r($leadDetail); exit;

        if (count($leadDetail4) > 0) { for ($i=0; $i < count($leadDetail4); $i++) {
            $leadArray[] = array(
                $leadDetail4[$i]->phone,
                $leadDetail4[$i]->recording_url,
                $leadDetail4[$i]->duration,
                $leadDetail4[$i]->date,
                $leadDetail4[$i]->time,
                $leadDetail4[$i]->payout,
                'Local Calls, Off-Hours'
            );
        } }*/
        //echo '<pre>'; print_r($leadArray); exit;
        return collect($leadArray);
    }
}