<?php

namespace App\Models\Business;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\User;

class LMBusinessList extends Model
{
    use HasFactory;
    protected $table = "lm_business_list";
    protected $primaryKey = "lm_business_list_id";

    public $timestamps = false;

    protected $fillable = [
        "lm_business_list_id",
        "lm_business_id",
        "lm_business_name",
        "lm_business_phone",
        "lm_emergency_phone",
        "lm_forward_number",
        "lm_notification_phone",
        "lm_email",
        "lm_notification_email",
        "lm_movers_username",
        "lm_address",
        "lm_dot_number",
        "created_at"
    ];
}
