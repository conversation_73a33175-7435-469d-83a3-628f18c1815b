<?php

namespace App\Models\PingPost;

use App\Models\PingPost\PingPostPayout;
use App\Models\Lead\LeadRoutingLog;
use App\Models\Lead\LeadRouting;
use App\Models\Lead\LeadMoving;
use App\Models\Business\Business;
use App\Models\DevDebug;
use App\Models\Lead\LeadLandingPage;
use Exception;
use DB;

class RexDirectNew
{
    private $lpCampaignId   = '6787f2c246776';
    private $lpCampaignKey  = 'Pz2ZFvyQWCBjx8L9gnmp';
    private $affKey         = '110490';
    private $lpResponse     = 'JSON';
    private $lpTest         = 1;
    private $lpPingId       = '';
    private $leadId         = 0;
    private $campaignId     = 0;

    public function ping($leadData, $campaignId) {
        $this->campaignId   = $campaignId;
        $responseData       = [];
        DevDebug::create([
            'sr_no' => 110,
            'result' => 'LeadData Array ' . json_encode($leadData),
            'created_at' => date("Y-m-d H:i:s"),
        ]);
        $leadId = $leadData[0]['lead_id'];
        try {
            //var_dump($this->checkCampaignStatus());
            if($this->isAdmissiblePing($leadId, $campaignId) && $this->checkCampaignStatus($campaignId)) {
                $getMoveInfo = LeadMoving::where('lead_id', $leadId)->get()->toArray();
                $landingPageDetails = LeadLandingPage::where('lead_id', $leadId)->get()->toArray();
                $ip = explode(',', $landingPageDetails[0]['ip']) ?? '';

                if(strpos(url()->current(), 'linkup.software') !== false) {
                    $this->lpTest = 0;
                } else if(strpos(url()->current(), '**********') !== false || strpos(url()->current(), 'stage.linkup.software') !== false) {
                    $this->lpTest = 0;
                }

                $body = array(
                    'auth' => array(
                        'lp_campaign_id' => $this->lpCampaignId,
                        'lp_campaign_key' => $this->lpCampaignKey
                    ),
                    'mode' => array(
                        'lp_test' => true
                    ),
                    'tracking' => array(
                        'lp_request_id' => '',
                        'lp_s1' => '',
                        'lp_s2' => '',
                        'lp_s3' => '',
                        'lp_s4' => '',
                        'lp_s5' => ''
                    ),
                    'lead' => array(
                        'state' =>  $getMoveInfo[0]['from_state'],
                        'zip_code' => $getMoveInfo[0]['from_zipcode'],
                        'to_zip_code' => $getMoveInfo[0]['to_zipcode'],
                        'move_date' => $this->moveDateValue($getMoveInfo[0]['move_date']),
                        'move_size' => $this->moveSizeValue($getMoveInfo[0]['move_size_id']),
                        'ip_address' => $ip[0] ?? '',
                        'affkey' => $this->affKey
                    ),
                    'tcpa' => array(
                        'trusted_form_cert_id' => $landingPageDetails[0]['trusted_form_cert_id'] ?? '',
                        'jornaya_lead_id' => ''
                    ),
                );
                //dd($body);

                try {
                    $url = 'https://go.rexlp1.com/pre-ping.do';
                    $responseData = json_decode($this->postMethod($url, json_encode($body)), true);
                } catch(Exception $e) {
                    $responseData = $e->getMessage();
                }
                //echo '<pre>'; print_r($responseData); die;

                LeadRoutingLog::create([
                    'lead_id' => $leadId,
                    'campaign_id' => $this->campaignId,
                    'request' => json_encode($body),
                    'response' => json_encode($responseData)
                ]);

                $payout = 0;
                if (isset($responseData)) {
                    $this->leadId = $leadId;
                    $this->pingId = $responseData['ping_id'];
                    $brands = $responseData['brands'] ?? [];
                    if (count($brands)) { foreach ($brands as $key => $value) {
                        $payout += $value['payout'];
                    } }

                    //need to add price in Rex Direct Response
                    $responseData['price'] = $payout;
                    PingPostPayout::create([
                        'lead_id' => $this->leadId,
                        'campaign_id' => $this->campaignId,
                        'ping_id' => $this->pingId,
                        'ping_request' => json_encode($body),
                        'ping_response' => json_encode($responseData),
                        'ping_type' => 'exclusive',
                        'is_request' => 'ping',
                        'created_at' => date("Y-m-d H:i:s"),
                        'payout' => $payout,
                        'ping_slot' => 4,
                        'is_win' => 'no',
                    ]);
                }
                return $responseData;
            }
        } catch(Exception $e) {
            DevDebug::create([
                'sr_no' => 109,
                'result' => 'RexDirect PingPost ' . $e->getMessage(),
                'created_at' => date("Y-m-d H:i:s"),
            ]);
            dd($e->getMessage());
        }

        return $responseData;
        //$promise->wait();
    }

    public function post($leadData, $campaignId) {
        $this->campaignId = $campaignId;
        $leadId = $leadData[0]['lead_id'];
        $leadName = $leadData[0]['name'];
        $email = $leadData[0]['email'];
        $phone = $leadData[0]['phone'];
        if($this->isAdmissiblePost($leadId, $campaignId) && $this->checkCampaignStatus($campaignId)) {
            // Fetch PingId from ws_payout
            $wsPayoutData = PingPostPayout::where('lead_id', $leadId)->where('campaign_id', $this->campaignId)->get(['ping_id', 'ping_response'])->first();
            $getMoveInfo = LeadMoving::where('lead_id', $leadId)->get()->toArray();
            $landingPageDetails = LeadLandingPage::where('lead_id', $leadId)->get()->toArray();
            $ip = explode(',', $landingPageDetails[0]['ip']) ?? '';
            $first_name = $last_name = '';
            $nameData = explode(" ", $leadName);
            if (isset($nameData[0]) && !empty($nameData[0])) {
                $first_name = $nameData[0];
            }
            if (isset($nameData[1]) && !empty($nameData[1])) {
                $last_name = $nameData[1];
            }

            $firstName = $lastName = 'Test';
            if(strpos(url()->current(), 'linkup.software') !== false) {
                $this->lpTest = 0;
                $firstName = $first_name;
                $lastName = $last_name;
            } else if(strpos(url()->current(), '**********') !== false || strpos(url()->current(), 'stage.linkup.software') !== false) {
                $this->lpTest = 0;
                $firstName = $first_name;
                $lastName = $last_name;
            }

            //Added by BK on 06/12/2024 as new request
            $brandsIdArray = [];
            if (isset($wsPayoutData)) {
                $responseData = json_decode($wsPayoutData->ping_response, true);
                $brands = $responseData['brands'];
                if (count($brands)) { foreach ($brands as $key => $value) {
                    $brandsIdArray[] = array(
                        'lp_brand_id' => $value['lp_brand_id']
                    );
                } }
            }

            $body = array(
                'auth' => array(
                    'lp_campaign_id' => $this->lpCampaignId,
                    'lp_campaign_key' => $this->lpCampaignKey,
                    'lp_ping_id' => $wsPayoutData->ping_id
                ),
                'mode' => array(
                    'lp_test' => true
                ),
                'tracking' => array(
                    'lp_request_id' => '',
                    'lp_s1' => '',
                    'lp_s2' => '',
                    'lp_s3' => '',
                    'lp_s4' => '',
                    'lp_s5' => ''
                ),
                'lead' => array(
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'phone_home' => $phone,
                    'email_address' => $email,
                    'address' => '',
                    'city' => $getMoveInfo[0]['from_city'],
                    'state' =>  $getMoveInfo[0]['from_state'],
                    'zip_code' => $getMoveInfo[0]['from_zipcode'],
                    'to_city' => $getMoveInfo[0]['to_city'],
                    'to_state' => $getMoveInfo[0]['to_state'],
                    'to_zip_code' => sprintf("%05d", $getMoveInfo[0]['to_zipcode']),
                    'move_date' => $this->moveDateValue($getMoveInfo[0]['move_date']),
                    'move_size' => $this->moveSizeValue($getMoveInfo[0]['move_size_id']),
                    'ip_address' => $ip[0] ?? '',
                    'landing_page_url' => $landingPageDetails[0]['landing_page'] ?? '',
                    'affkey' => $this->affKey
                ),
                'brands' => $brandsIdArray,
            );

            try {
                $url = 'https://go.rexlp1.com/pre-post.do';
                $responseData = json_decode($this->postMethod($url, json_encode($body)), true);
            } catch(Exception $e) {
                $responseData = $e->getMessage();
            }
            //echo '<pre>'; print_r($responseData); die;

            LeadRoutingLog::create([
                'lead_id' => $leadId,
                'campaign_id' => $this->campaignId,
                'request' => json_encode($body),
                'response' => json_encode($responseData)
            ]);

            if (isset($responseData['result']) && trim($responseData['result']) == 'success') {
                PingPostPayout::where(['lead_id' => $leadId, 'campaign_id' => $this->campaignId, 'ping_type' => 'ping'])->update([
                    'is_win' => "yes"
                ]);
                return true;
            }
        }
        return false;
    }

    public function moveSizeValue($moveSize = 0) {
        if($moveSize == 1) {
            return 1500;
        } else if($moveSize == 2) {
            return 2000;
        } else if($moveSize == 3) {
            return 5000;
        } else if($moveSize == 4) {
            return 10000;
        } else if($moveSize == 5) {
            return 15000;
        } else {
            return 20000;
        }
    }

    public function moveDateValue($moveDate) {
        $date               = date_create($moveDate);
        return date_format($date, "m/d/Y");
    }

    public function isAdmissiblePing($leadId, $campaignId) {
        $returnArray        = array();
        $this->campaignId   = $campaignId;
        $wsPayout           = PingPostPayout::where('lead_id', $leadId)->where('campaign_id', $this->campaignId)->where('is_request', 'ping')->get()->toArray();
        //echo "<pre>"; print_r($wsPayout); die;
        if($wsPayout) {
            return false;
        } else {
            return true;
        }
    }

    public function isAdmissiblePost($leadId, $campaignId) {
        $this->campaignId   = $campaignId;
        $wsRouting          = LeadRouting::where('lead_id', $leadId)->where('campaign_id', $this->campaignId)->first();
        if ($wsRouting) {
            return false;
        } else {
            return true;
        }
    }

    public function checkCampaignStatus($campaignId) {
        $this->campaignId   = $campaignId;
        $status             = 'yes';
        $businessCampaign   = Business::with([
            'businessCampaign' => function ($query) use ($status) {
                return $query->where('is_active', '=', $status)->where('campaign_id', $this->campaignId);
            }])->where('status', 'active')->where('buyer_type_id', 3)->get();
        //dd(count($businessesCampaign));
        if (count($businessCampaign) > 0) {
            return true;
        } else {
            return false;
        }
    }

    public static function postMethod($url, $postFields) {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_TIMEOUT, 50);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
            $response = curl_exec($ch);
            curl_close($ch);

            return $response;
        } catch (Exception $e) {
            //echo "error in recording";
        }
    }
}
