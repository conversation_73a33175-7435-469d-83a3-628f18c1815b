<?php

namespace App\Http\Controllers\API\LeadPhone;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Helpers\CommonFunctions;
use App\Models\Lead\Lead; //Added By BK On 08/12/2022 For lead table
use App\Models\Lead\LeadMoving; //Added By BK On 12/12/2022 For lead_moving table
use App\Models\Lead\LeadJunkRemoval; //Added By BK On 12/12/2022 For lead_junk_removal table
use App\Models\Lead\LeadHeavyLifting; //Added By BK On 12/12/2022 For lead_heavy_lifting table
use App\Models\Lead\LeadCarTransport; //Added By BK On 12/12/2022 For lead_car_transport table
use App\Models\Lead\LeadInvalid; //Added By SS On 08/02/2023 For lead_invalid table
use App\Models\Lead\LeadRequestLog; //Added By BK On 08/12/2022 For lead_request_log table
use App\Models\Lead\LeadLandingPage; //Added By BK On 08/12/2022 For lead_landing_page table
use App\Models\Master\MstZipcode; //Added By BK On 08/12/2022 For mst_zipcode table
use App\Models\Master\MstLeadSource; //Added By BK On 08/12/2022 For mst_lead_source table
use App\Models\Master\MstAPIToken; //Added By BK On 09/12/2022 For mst_api_token table
use App\Models\Master\MstJunkSubType; //Added By BK On 12/12/2022 For mst_junk_sub_type table
use App\Models\Master\MstHeavyLiftingType; //Added By BK On 12/12/2022 For mst_heavy_lifting_type table
use App\Models\Master\MstCarType; //Added By BK On 12/12/2022 For mst_car_type table
use App\Models\Lead\LeadOldCallBuffer;
use App\Models\Lead\LeadCallBuffer;
use App\Models\Lead\LeadFollowUp;
use App\Models\LeadEmail\LeadEmail;
use Exception;
use Illuminate\Support\Facades\DB;
use DateTime;
use Illuminate\Support\Facades\Log;
use App\Models\Master\MstMoveSize; //Added By SS On 13/03/2023 For mst_move_size table
use App\Models\Lead\LeadActivity;
use App\Models\Lead\LeadJunkRemovalSubType;
use App\Models\Master\MstTimeZone;
use App\Http\Controllers\LeadCallBuffer\CallsbufferCronController;
use App\Http\Controllers\PlivoSms\ConfirmationSms;
use App\Models\DevDebug;

class LeadPhoneInsert extends Controller
{
    private $defaultimezone;
    public function __construct() {
        $timezoneobj = new MstTimeZone();
        $this->defaultimezone = $timezoneobj->getdefaulttimezone();
    }
    public function InsertPhoneLead($postData = "")
    {
        $status = "Failure";
        $leadId = $junkSubTypeId = $heavyLiftingTypeId = $carTypeId = $campaignId = $adGroupId = $adId = 0;
        $error = $ip = $fromData = $toData = $source = $utmCampaign = $utmMedium = $adGroup = $keyword = $device = $gclid = $landingPage = "";
        $fromAreaCode = $fromCity = $fromState = $toAreaCode = $toCity = $toState = "";
        $isDuplicate = $isVerified = $isDncCall = $isDncSMS = $isDncEmail = $isRequiredAssistence = $isOperational = 'no';
        $sourceId = 19;
        try {
            $elements = json_decode(file_get_contents('php://input'), true);
            //echo "<pre>";print_r($elements);die;
            $request = file_get_contents('php://input');
            $phone1 = 0;
            if (empty($elements)) {
                $phone1 = 1;
                $elements = json_decode($postData, true);
            }
            Log::info('Lead Request: ' . json_encode($elements));

            //get all API header data pass in header
            //$header = apache_request_headers();

            $checkToken = DB::select("SELECT api_token_id, SUBSTR(source, 1) AS source, token FROM mst_api_token WHERE token = '".$elements['token']."'");
            if (empty($checkToken)) {
                throw new Exception("Unauthorized Token");
            }
            $insert_buffer = 1;
            if(isset($elements['insert_buffer'])){
                $insert_buffer = trim($elements['insert_buffer']);
            }
            //for lead_source_id get from mst_lead_source table
            if (!empty($elements['origin'])) {
                $sourceName = MstLeadSource::read($elements['origin']);
                if ($sourceName == false) {
                    //insert lead origin in mst_lead_source table
                    $sourceId = MstLeadSource::create([
                        'lead_source_name' => $elements['origin']
                    ])->lead_source_id;
                } else {
                    $sourceId = $sourceName->lead_source_id;
                }
            }

            //lead_category 1-Moving, 2-Junk Removal, 3-Heavy Lifting, 4-Car Transportation
            $categoryId = (isset($elements['lead_category'])) ? trim($elements['lead_category']) : 1; //1-Move,2-Junk Removal,3-Heavy Equipment
            $book_type_id = (isset($elements['book_type_id'])) ? trim($elements['book_type_id']) : 1;
            //phone filter validation
            $phone = preg_replace("/\D/", "", trim($elements['phone']));
            $invalid = strlen($phone) != 10  || preg_match('/^1/', $phone) || preg_match('/^.11/', $phone) || preg_match('/^...1/', $phone) || preg_match('/^....11/', $phone) || preg_match('/^.9/', $phone);
            if ($invalid == true) {
                $phone = "";
            }

            //email filter validation
            //$elements['email'] = preg_replace('/\s+/', '', $elements['email']);
            $email = '';
            if (!empty($elements['email'])) {
                $email = trim((string) preg_replace('/\s+/', '', $elements['email']));
            }
            $elements['email'] = $email;

            //move_date
            $moveDate = date("Y-m-d", strtotime("+7 days"));
            if (!empty($elements['move_date'])) {
                $moveDate = ($elements['move_date']) ? LeadMoving::getMovedate($elements['move_date']) : $moveDate;
            }

            //initial validations
            if (empty($elements['phone'])) {
                throw new Exception("Phone required");
            } elseif (filter_var($phone, FILTER_VALIDATE_REGEXP, array("options" => ["regexp" => "/[1-9]{1}[0-9]{9}/"])) === false) {
                throw new Exception("Invalid phone format");
            }

            if (!empty($elements['name'])) {
                if (preg_match("/([%\$#\*]+)/", $elements['name']) || preg_match('~[0-9]+~', $elements['name'])) {
                    throw new Exception("Invalid Name format");
                }
            } elseif (!empty($elements['email'])) {
                if (filter_var($elements['email'], FILTER_VALIDATE_EMAIL) === false) {
                    throw new Exception("Invalid email format");
                }
            } elseif (!empty($elements['move_date'])) {
                $date1 = new DateTime();
                $date2 = new DateTime($moveDate);
                if (LeadMoving::checkDateFormat($elements['move_date']) == false) {
                    throw new Exception("Invalid Move date format");
                } elseif ($date2 < $date1) {
                    throw new Exception("Move date should be greater than current date");
                }
            }

            //from_zip
            $fromZipCode = isset($elements['from_zip']) ? $elements['from_zip'] : null;
            //to_zip
            $toZipCode = isset($elements['to_zip']) ? $elements['to_zip'] : null;

            //get zipcode data from mst_zipcode table pass from_zip & to_zip both argument mendatory
            if (!empty($fromZipCode)) {

                $zipcodeData = MstZipcode::read($fromZipCode, $toZipCode);

                if (strcmp($fromZipCode, $toZipCode) && $toZipCode) {
                    if ($fromZipCode == $zipcodeData[0]['zipcode']) {
                        $fromData = $zipcodeData[0];
                    } elseif (isset($zipcodeData[1])) {
                        $fromData = $zipcodeData[1];
                    }
                    if ($toZipCode == $zipcodeData[0]['zipcode']) {
                        $toData = $zipcodeData[0];
                    } elseif (isset($zipcodeData[1])) {
                        $toData = $zipcodeData[1];
                    }
                } else {
                    $fromData = $toData = $zipcodeData[0];
                }
                // if(!empty($toData)  ){
                //     echo "not null";
                // }else{
                //     echo "null";
                // }
                // echo '<pre>zipcodeData = '; print_r($toData); die;
                //from areacode, city, state data get from mst_zipcode table
                if(!empty($fromData)){
                    if(count($fromData) > 0 ){
                        $fromAreaCode = $fromData['areacode'];
                        $fromCity = strtoupper($fromData['city']);
                        $fromState = strtoupper($fromData['state']);
                    }
                }
                //to areacode, city, state data get from mst_zipcode table
                if ($categoryId != 2 ) {
                    if(!empty($toData)){
                        if(count($toData) > 0 ){
                            $toAreaCode = $toData['areacode'];
                            $toCity = strtoupper($toData['city']);
                            $toState = strtoupper($toData['state']);
                        }
                    }
                }
            }

            //initial validations for from_zip & to_zip
            /*if ($fromData == null) {
                throw new Exception("Invalid from_zip");
            } elseif ($toData == null && $categoryId != "2") {
                throw new Exception("Invalid to_zip");
            }*/

            //name
            $name = trim(preg_replace('/\s\s+/', ' ', str_replace("\n", " ", $elements['name'])));

            //email
            $email = strtolower($elements['email']);

            if (!isset($elements['move_size']) && $elements['move_size'] == "") {
                $elements['move_size'] = 1;
            }
            //move_size filter validation
            $moveSize = preg_replace("/\D/", "", trim($elements['move_size']));

            //check isEmailDuplicate Leads
            $pos1 = strpos($elements['origin'], 'VM');
            $pos2 = strpos($elements['origin'], 'QTM');
            $pos3 = strpos($elements['origin'], 'IS');
            if ($pos1 !== false) {
                $sourceName = 'VM';
            } else if ($pos2 !== false) {
                $sourceName = 'QTM';
            } else if ($pos3 !== false) {
                $sourceName = 'IS';
            }
            if (!empty($email) && Lead::isEmailDuplicate($sourceId, $email, $categoryId, $sourceName)) {
                $isDuplicate = 'yes';
            }
            //check isPhoneDuplicate Leads
            if (Lead::isPhoneDuplicate($sourceId, $phone, $categoryId, $sourceName)) {
                $isDuplicate = 'yes';
            }

            if (!empty($elements['is_verified']) && $elements['is_verified'] == '1' || $elements['is_verified'] == 1) {
                $isVerified = 'yes';
            }
            $ip = $elements['IP'];

            //calculate distance
            $distance = 0;
            if (!empty($fromData['lat']) && !empty($toData['lat']) && !empty($fromData['long']) && !empty($toData['long'])) {
                $distance = LeadMoving::calculateDistance($fromData['lat'], $toData['lat'], $fromData['long'], $toData['long']);
            }
            //check lead distance type (ex. Local, Long)
            $leadDistanceType = "local";
            if ($categoryId != 2) {
                $leadDistanceType = LeadMoving::checkLeadDistanceType($fromState, $toState);
            }

            if (!empty($elements['is_required_assistence']) && $elements['is_required_assistence'] == '1') {
                $isRequiredAssistence = 'yes';
            }

            if (!empty($elements['is_operational']) && $elements['is_operational'] == '1') {
                $isOperational = 'yes';
            }

            //insert lead request log in lead_request_log table
            $leadRequestLogId = LeadRequestLog::create([
                'lead_request' => json_encode($elements)
            ])->lead_request_log_id;

            if($categoryId == 1){
                $findMovesize = MstMoveSize::where(['move_size_id'=> $moveSize])->first();
                if(!$findMovesize){
                    throw new Exception("Move size must be beetween 1 to 6");
                }
            }

            $generatedAt = date("Y-m-d H:i:s");
            if (isset($elements['generated_at'])) {
                $generatedAt = $elements['generated_at'];
            }
            if($name == null || strtolower($name) == "null"){
                $name = "";
            }
            //insert lead data in lead table
            $leadId = Lead::create([
                'lead_source_id' => $sourceId,
                'lead_category_id' => $categoryId,
                'timezone_id' => $this->defaultimezone,
                'lead_type' => 'phone',
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'is_duplicate' => $isDuplicate,
                'is_verified' => $isVerified,
                'is_dnc_call' => $isDncCall,
                'is_dnc_sms' => $isDncSMS,
                'is_dnc_email' => $isDncEmail,
                'lead_generated_at' => $generatedAt,
                'lead_request_log_id' => $leadRequestLogId,
                'book_type_id' => $book_type_id,
                'created_at' => date("Y-m-d H:i:s")
            ])->lead_id;

            $leadActivityObj = new LeadActivity();
            $leadActivityObj->createActivity($leadId, 1);

            //common field array
            $commonFieldArray = [];
            $commonFieldArray['lead_id'] = $leadId;
            $commonFieldArray['from_zipcode'] = $fromZipCode;
            $commonFieldArray['from_areacode'] = $fromAreaCode;
            $commonFieldArray['from_city'] = $fromCity;
            $commonFieldArray['from_state'] = $fromState;
            $commonFieldArray['created_at'] = date("Y-m-d H:i:s");

            if ($categoryId != 2) {
                $commonFieldArray['to_zipcode'] = $toZipCode;
                $commonFieldArray['to_areacode'] = $toAreaCode;
                $commonFieldArray['to_city'] = $toCity;
                $commonFieldArray['to_state'] = $toState;
                $commonFieldArray['move_type'] = $leadDistanceType;
                $commonFieldArray['distance'] = $distance;
            }

            if ($categoryId == 3 || $categoryId == 4) {
                $commonFieldArray['is_required_assistence'] = $isRequiredAssistence;
                $commonFieldArray['is_operational'] = $isOperational;
            }

            //Moving
            if ($categoryId == 1) {
                $propertyType = (!empty($elements['property_type'])) ? trim($elements['property_type']) : 'residential';

                //push in common field array
                $commonFieldArray['move_size_id'] = $moveSize;
                $commonFieldArray['move_date'] = $moveDate;
                $commonFieldArray['property_type'] = $propertyType;

                //insert record in lead_moving table
                LeadMoving::create($commonFieldArray)->lead_moving_id;
                //Junk Removal
            } else if ($categoryId == 2) {
                $junkTypeId = (!empty($elements['junk_type'])) ? trim($elements['junk_type']) : '';
                $junkSubTypeId = (!empty($elements['junk_sub_type'])) ? $elements['junk_sub_type'] : null;
                $ownerType = (!empty($elements['owner_type'])) ? trim($elements['owner_type']) : 'owner';
                $description = (!empty($elements['description'])) ? trim($elements['description']) : '';
                $propertyType = (!empty($elements['property_type'])) ? trim($elements['property_type']) : 'residential';
                if (is_null($description)) {
                    $description = "";
                }
                //push in common field array
                $commonFieldArray['junk_remove_date'] = $moveDate;
                $commonFieldArray['junk_type_id'] = $junkTypeId;
                $commonFieldArray['owner_type'] = $ownerType;
                $commonFieldArray['description'] = $description;
                $commonFieldArray['property_type'] = $propertyType;

                //insert record in lead_junk_removal table
                LeadJunkRemoval::create($commonFieldArray)->lead_junk_removal_id;
                //insert record in lead_junk_removal_sub_type table
                if( $junkSubTypeId != null ){
                    // echo "Not null";
                    $junkSubTypeIdArr = explode (",", $junkSubTypeId);
                    $junksubtypeArr = [];
                    foreach ($junkSubTypeIdArr as $key => $value) {
                        $junksubtypeArr[] = [
                            "lead_id" => $leadId,
                            "junk_sub_type_id" => $value,
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                    }
                    LeadJunkRemovalSubType::insert($junksubtypeArr);
                }
                //Heavy Lifting
            } else if ($categoryId == 3) {
                $heavyLiftingTypeId = (!empty($elements['heavy_lifting_type'])) ? trim($elements['heavy_lifting_type']) : '';
                $description = (!empty($elements['description'])) ? trim($elements['description']) : '';
                if (is_null($description)) {
                    $description = "";
                }
                //push in common field array
                $commonFieldArray['lift_date'] = $moveDate;
                $commonFieldArray['heavy_lifting_type_id'] = $heavyLiftingTypeId;
                $commonFieldArray['description'] = $description;
                //insert record in lead_heavy_lifting table
                LeadHeavyLifting::create($commonFieldArray)->lead_heavy_lifting_id;
                //Car Transportation
            } else if ($categoryId == 4) {
                $carTypeId = (!empty($elements['car_type'])) ? trim($elements['car_type']) : '';
                $transportType = ($elements['transport_type'] > 0) ? 'open' : 'enclosed';
                $vehicleCondition = (isset($elements['vehicle_condition']) && $elements['vehicle_condition'] > 0) ? 'running' : 'not running';
                $description = (!empty($elements['description'])) ? trim($elements['description']) : '';
                if (is_null($description)) {
                    $description = "";
                }
                //push in common field array
                $commonFieldArray['move_date'] = $moveDate;
                $commonFieldArray['car_type_id'] = $carTypeId;
                $commonFieldArray['transport_type'] = $transportType;
                $commonFieldArray['vehicle_condition'] = $vehicleCondition;
                $commonFieldArray['description'] = $description;

                //insert record in lead_car_transport table
                LeadCarTransport::create($commonFieldArray)->lead_car_transport_id;
            }

            $utmCampaign = (!empty($elements['utm_campaign'])) ? preg_replace('/-+/', '-', trim($elements['utm_campaign'])) : ""; //utmCampaign
            $utmMedium = (!empty($elements['utm_medium'])) ? trim($elements['utm_medium']) : ""; //utmMedium
            $utmCampaignId = (!empty($elements['camp_id'])) ? trim($elements['camp_id']) : 0; //$utmCampaignId
            $adGroupId = 0;
            if (!empty($elements['ad_grp_id'])) {
                $adGroupId = trim($elements['ad_grp_id']);
            } else if (!empty($elements['ad_group_id'])) {
                $adGroupId = trim($elements['ad_group_id']);
            }
            //$adGroupId = (!empty($elements['ad_group_id'])) ? trim($elements['ad_group_id']) : 0; //adGroupId
            $adGroup = "";
            if (!empty($elements['ad_grp'])) {
                $adGroup = trim($elements['ad_grp']);
            } else if (!empty($elements['ad_group'])) {
                $adGroup = trim($elements['ad_group']);
            }
            //$adGroup = (!empty($elements['ad_group'])) ? trim($elements['ad_group']) : ""; //adGroup
            $adId = (!empty($elements['ad_id'])) ? trim($elements['ad_id']) : ""; //adId
            $keyword = (!empty($elements['keyword'])) ? trim($elements['keyword']) : ""; //keyword
            $device = (!empty($elements['device'])) ? trim($elements['device']) : ""; //device
            $gclid = (!empty($elements['GCLID'])) ? trim($elements['GCLID']) : ""; //gclid
            $gclidType = (!empty($elements['gclid_type'])) ? trim($elements['gclid_type']) : ""; //gclid
            $landingPage = (!empty($elements['Landing_Page'])) ? trim($elements['Landing_Page']) : ""; //landingPage
            $trustedFormCertId = (!empty($elements['xxTrustedFormToken'])) ? trim($elements['xxTrustedFormToken']) : ""; //xxTrustedFormToken

            //insert lead landing page data in lead_landing_page table
            $leadLandingPageId = LeadLandingPage::create([
                'lead_id' => $leadId,
                'utm_campaign' => $utmCampaign,
                'utm_medium' => $utmMedium,
                'landing_campaign_id' => $utmCampaignId,
                'ad_group_id' => $adGroupId,
                'ad_group' => $adGroup,
                'ad_id' => $adId,
                'search_keyword' => $keyword,
                'device' => $device,
                'gclid' => $gclid,
                'gclid_type' => $gclidType,
                'landing_page' => $landingPage,
                'lead_source_id' => $sourceId,
                'ip' => $ip,
                'trusted_form_cert_id' => $trustedFormCertId,
                'created_at' => $elements['generated_at']
            ])->lead_landing_page_id;

            $status = "Success";
            $response = array(
                'status' => $status,
                'lead_id' => $leadId,
                'error' => $error
            );

            if (isset($elements['fbrouteLead']) && $elements['fbrouteLead'] > 0) {
                $confirmationsms = new ConfirmationSms();
                $confirmationsms->LeadConfirmationSms($leadId, 1, 'fb');
            }

            //insert lead response log in lead_request_log table
            LeadRequestLog::where('lead_request_log_id', $leadRequestLogId)->update([
                'lead_response' => json_encode($response)
            ]);
            if($insert_buffer > 0){
                $buffer = new CallsbufferCronController();
                $hoursCheck = date('G');
                $devdeug = new DevDebug();
                $devdeug->devDebuglog(60, 'phoneinsert hoursCheck= ' . $hoursCheck);
                if ($hoursCheck >= 11 && $hoursCheck < 21) {
                    $devdeug->devDebuglog(60, 'phoneinsert date time');
                    $buffer->SaveToBufferViaPhoneonly($leadId);
                }else{

                    $devdeug->devDebuglog(60, 'phoneinsert night time');
                    $leadArr = Lead::where('lead_id', $leadId)->find($leadId)->toArray();
                    $buffer->saveFollowupentry($leadArr, 1, 1);
                }
            }
        } catch (Exception $e) {
            $error = $e->getMessage();

            //insert exception in lead_invalid table - start
            LeadInvalid::create([
                'request' => $request,
                'response' => str_replace(array('\'', '"'), '', $error),
                'lead_source_id' => $sourceId,
                'created_at' => date("Y-m-d H:i:s")
            ]);
            //insert exception in lead_invalid table - end

            $response = array(
                'status' => $status,
                'lead_id' => $leadId,
                'error' => $error
            );
        }
        return json_encode($response);
    }


}
