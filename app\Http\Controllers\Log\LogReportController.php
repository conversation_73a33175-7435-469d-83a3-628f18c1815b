<?php

namespace App\Http\Controllers\Log;

use App\Helpers\CommonFunctions;
use App\Http\Controllers\Controller;
use App\Models\Business\Business;
use App\Models\Campaign\Campaign;
use App\Models\Campaign\CampaignUpdateLog;
use App\Models\Notification\Notification;
use App\Models\User;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Facades\Bus;

class LogReportController extends Controller
{
    public function index(Request $request)
    {
        $bidLogArray = $campaignLogArray = $businessIdArray = $businessNameArray = $campaignNameArray = $userNameArray = $campaignLogArray = [];
        $dateRange              = $request->get('daterange');

        $businessDetail         = Business::get(['business_id', 'business_name']);
        for ($c=0; $c<count($businessDetail); $c++) {
            $businessNameArray[$businessDetail[$c]->business_id] = $businessDetail[$c]->business_name;
        }

        $campaignDetail         = Campaign::get(['campaign_id', 'business_id', 'campaign_name']);
        for ($c=0; $c<count($campaignDetail); $c++) {
            $campaignNameArray[$campaignDetail[$c]->campaign_id] = $campaignDetail[$c]->campaign_name;
            $businessIdArray[$campaignDetail[$c]->campaign_id] = $campaignDetail[$c]->business_id;
        }

        $userDetail             = User::get(['id', 'name'])->toArray();
        for ($u = 0; $u < count($userDetail); $u++) {
            $userNameArray[$userDetail[$u]['id']] = $userDetail[$u]['name'];
        }

        $bidLog                 = CampaignUpdateLog::whereIn('field_name', ['payout', 'automated_inbound', 'inbound_call', 'automated_outbound', 'outbound_call'])/*->where('created_user_id', 77)*/;
        if (isset($dateRange) && $dateRange != '') {
            $dateRangeArray     = explode(' - ', $dateRange);
            $valStartDate       = date("Y-m-d", strtotime($dateRangeArray[0])) . ' 00:00:00';
            $valEndDate         = date("Y-m-d", strtotime($dateRangeArray[1])) . ' 23:59:59';
            $bidLog->whereBetween('created_at', [$valStartDate, $valEndDate]);
        } else {
            $valStartDate       = date("Y-m-d 00:00:00", strtotime(date('Y-m-d 00:00:00') . ' -2 days'));
            $valEndDate         = date("Y-m-d 23:59:59");
            $bidLog->whereBetween('created_at', [$valStartDate, $valEndDate]);
        }
        $bidLog                 = $bidLog->orderBy('created_at', 'DESC')->get();

        foreach ($bidLog as $key => $log) {
            $bidColor           = 0;
            if ($log->new_value > $log->old_value) {
                $bidColor       = 1;
            } else if ($log->new_value < $log->old_value) {
                $bidColor       = 2;
            }
            $businessId         = $businessIdArray[$log->campaign_id];
            $bidLogArray[]      = array(
                'business_id'   => $businessId,
                'business_name' => $businessNameArray[$businessId] ?? 0,
                'campaign_id'   => $log->campaign_id ?? 0,
                'campaign_name' => $campaignNameArray[$log->campaign_id] ?? '--',
                'field_name'    => $log->field_name,
                'old_value'     => $log->old_value ?? '--',
                'new_value'     => $log->new_value ?? '--',
                'bid_color'     => $bidColor,
                'created_at'    => date("m/d/Y H:i:s", strtotime($log->created_at)),
                'created_by'    => $userNameArray[$log->created_user_id] ?? '--',
                'ip'            => $log->ip ?? '--'
            );
        }
        return view("log.bidlist")->with('bidLogArray', $bidLogArray);
    }

    public function index1(Request $request)
    {
        $campaignLogArray = $businessIdArray = $businessNameArray = $campaignNameArray = $userNameArray = $campaignLogArray = [];
        $dateRange              = $request->get('daterange');

        $businessDetail         = Business::get(['business_id', 'business_name']);
        for ($c=0; $c<count($businessDetail); $c++) {
            $businessNameArray[$businessDetail[$c]->business_id] = $businessDetail[$c]->business_name;
        }

        $campaignDetail         = Campaign::get(['campaign_id', 'business_id', 'campaign_name']);
        for ($c=0; $c<count($campaignDetail); $c++) {
            $campaignNameArray[$campaignDetail[$c]->campaign_id] = $campaignDetail[$c]->campaign_name;
            $businessIdArray[$campaignDetail[$c]->campaign_id] = $campaignDetail[$c]->business_id;
        }

        $userDetail             = User::get(['id', 'name'])->toArray();
        for ($u = 0; $u < count($userDetail); $u++) {
            $userNameArray[$userDetail[$u]['id']] = $userDetail[$u]['name'];
        }
        $campaignLog            = CampaignUpdateLog::whereIn('field_name', ['is_active', 'campaign_status', 'pause_time']);
        if (isset($dateRange) && $dateRange != '') {
            $dateRangeArray     = explode(' - ', $dateRange);
            $valStartDate       = date("Y-m-d", strtotime($dateRangeArray[0])) . ' 00:00:00';
            $valEndDate         = date("Y-m-d", strtotime($dateRangeArray[1])) . ' 23:59:59';
            $campaignLog->whereBetween('created_at', [$valStartDate, $valEndDate]);
        } else {
            $valStartDate       = date("Y-m-d 00:00:00", strtotime(date('Y-m-d 00:00:00') . ' -2 days'));
            $valEndDate         = date("Y-m-d 23:59:59");
            $campaignLog->whereBetween('created_at', [$valStartDate, $valEndDate]);
        }
        $campaignLog            = $campaignLog->orderBy('created_at', 'DESC')->get();
        foreach ($campaignLog as $key => $log) {
            $oldValue           = 0; //for normal
            if ($log->old_value == 'yes' || $log->old_value == 'active') {
                $oldValue       = 1;
            }
            if ($log->old_value == 'no' && $log->field_name == 'pause_time') {
                $oldValue       = 1;
            }
            if ($log->old_value == 'yes' && $log->field_name == 'pause_time') {
                $oldValue       = 2;
            }
            if ($log->old_value == 'permenant') {
                $oldValue       = 3;
            }

            $newValue           = 0; //for normal
            if ($log->new_value == 'yes' || $log->new_value == 'active') {
                $newValue       = 1;
            }
            if ($log->new_value == 'yes' && $log->field_name == 'pause_time') {
                $newValue       = 2;
            }
            if ($log->new_value == 'no' && $log->field_name == 'pause_time') {
                $newValue       = 1;
            }
            if ($log->new_value == 'permenant') {
                $newValue       = 3;
            }
            $businessId         = $businessIdArray[$log->campaign_id];
            $campaignLogArray[] = array(
                'business_id'   => $businessId,
                'business_name' => $businessNameArray[$businessId] ?? 0,
                'campaign_id'   => $log->campaign_id ?? 0,
                'campaign_name' => $campaignNameArray[$log->campaign_id] ?? '--',
                'field_name'    => $log->field_name,
                'old_status'    => $oldValue,
                'new_status'    => $newValue,
                'created_at'    => date("m/d/Y H:i:s", strtotime($log->created_at)),
                'created_by'    => $userNameArray[$log->created_user_id] ?? '--',
                'ip'            => $log->ip ?? '--'
            );
        }

        return view("log.statuslist")->with('campaignLogArray', $campaignLogArray);
    }
}