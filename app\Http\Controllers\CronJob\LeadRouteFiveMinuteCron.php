<?php

namespace App\Http\Controllers\CronJob;

use App\Models\Seller\PPSeller;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Lead\Lead;
use App\Models\Outbound\LeadCall;
use App\Http\Controllers\Leadroute\LeadRouteController;
use App\Http\Controllers\LeadCallBuffer\CallsbufferCronController;
use App\Helpers\CommonFunctions;
use App\Models\Master\MstLeadSource;
use App\Models\Master\MstCallConfig;
use App\Models\DevDebug;
use DB;
use DateTime;
use App\Http\Controllers\PlivoSms\AfterHourSmsController;

class LeadRouteFiveMinuteCron extends Controller
{
    public function leadRouteFiveMinute() {
        //DevDebug::create(['sr_no' => 1, 'result' => 'leadRouteFiveMinute run at: ' . date('Y-m-d H:i:s'), 'created_at' => date('Y-m-d H:i:s')]);
        $leadIdArray = $callLeadIdArray = $routeLeadIdArray = $sourceNameArray = [];
        //leadroutecontroller object
        $leadRouteObj       = new LeadRouteController();
        $endDecObj          = new CommonFunctions;
        //current date time
        $currentDateTime    = date('Y-m-d H:i:s');
        $hoursCheck         = date('G');
        $minuteCheck        = date('i');
        //echo $minuteCheck; die;
        if (($hoursCheck <= 8 && $minuteCheck < 6) || ($hoursCheck >= 22 && $minuteCheck > 5)) {
            exit;
        }
        $sixmiutes          = new DateTime($currentDateTime);
        $sixmiutes->modify('-6 minutes');
        $sixmiutes          = $sixmiutes->format('Y-m-d H:i:00');

        $fivemiutes         = new DateTime($currentDateTime);
        $fivemiutes->modify('-5 minutes');
        $fivemiutes         = $fivemiutes->format('Y-m-d H:i:00');

        //get last 30 min lead
        DevDebug::create(['sr_no' => 1, 'result' => 'leadRouteFiveMinute between: ' . $sixmiutes . ' -- '. $fivemiutes, 'created_at' => date('Y-m-d H:i:s')]);
        /*$configDetail       = MstCallConfig::whereIn('call_config', ['vmlongroutelater', 'qtmlongroutelater', 'islongroutelater', 'vmlocalroutelater', 'qtmlocalroutelater', 'islocalroutelater'])->get(['status'])->toArray();
        if (strtolower($configDetail[0]['status']) == "active" || strtolower($configDetail[3]['status']) == "active") {
        $sourceNameArray[]  = 'VM';
        }
        if (strtolower($configDetail[1]['status']) == "active" || strtolower($configDetail[4]['status']) == "active") {
        $sourceNameArray[]  = 'QTM';
        }
        if (strtolower($configDetail[2]['status']) == "active" || strtolower($configDetail[5]['status']) == "active") {
        $sourceNameArray[]  = 'IS';
        }*/
        /*$leadSourceId     = MstLeadSource::whereIn('lead_parent_source', $sourceNameArray)->pluck('lead_source_id')->toArray();*/
        $sellerSourceName   = PPSeller::pluck('source_name')->whereNotIn('source_name', ['VM', 'QTM', 'IS'])->toArray();
        $leadSourceId       = MstLeadSource::whereIn('lead_parent_source', $sellerSourceName)->pluck('lead_source_id')->toArray();
        $leadDetail         = Lead::where('lead_type', 'normal')->where('created_at', '>=', $sixmiutes)->where('created_at', '<=', $fivemiutes)->where(function ($query) {
            $query->where('remaining_slot', '>', 0)->orWhereNull('remaining_slot');
        })->where('is_dnc_call', 'no')->whereNotIn('lead_source_id', $leadSourceId)->get(['lead_id', 'created_at'])->toArray();
        //echo '<pre>';print_r($leadDetail); die;
        if(isset($leadDetail) && count($leadDetail) > 0) { for($l=0; $l < count($leadDetail); $l++) {
            $leadIdArray[]  = $leadDetail[$l]['lead_id'];
        } }
        DevDebug::create(['sr_no' => 2, 'result' => 'leadRouteFiveMinute between: ' . $sixmiutes . ' -- '. $fivemiutes . ' - ' . json_encode($leadIdArray), 'created_at' => date('Y-m-d H:i:s')]);

        if(count($leadIdArray) > 0) {
            //get call detail
            $leadCallDetail = LeadCall::whereIn('lead_id', $leadIdArray)->whereIn('call_disposition_id', [2, 3, 7, 13])->pluck('lead_id')->toArray();
            if(isset($leadCallDetail) && count($leadCallDetail) > 0) { for($lc=0; $lc < count($leadCallDetail); $lc++) {
                $callLeadIdArray[] = $leadCallDetail[$lc];
            } }
            DevDebug::create(['sr_no' => 3, 'result' => 'leadRouteFiveMinute between: ' . $sixmiutes . ' -- '. $fivemiutes . ' - ' . json_encode($callLeadIdArray), 'created_at' => date('Y-m-d H:i:s')]);

            $leadIdArray    = array_values(array_diff($leadIdArray, $callLeadIdArray));
            DevDebug::create(['sr_no' => 4, 'result' => 'leadRouteFiveMinute between: ' . $sixmiutes . ' -- '. $fivemiutes . ' - ' . json_encode($leadIdArray), 'created_at' => date('Y-m-d H:i:s')]);
            /*if (count($leadDetail) > 0) {
                for($lr=0; $lr < count($leadDetail); $lr++) {
                    //check data of last 5 min which one slot
                    //is call yes
                    if (!in_array($leadDetail[$lr]['lead_id'], $callLeadIdArray)) {
                        $leadId = $endDecObj->customeEncryption($leadDetail[$lr]['lead_id']);
                        $leadRouteObj->reRouteLead($leadId, 3, 0);
                        $routeLeadIdArray['lead_id'] = $leadDetail[$lr]['lead_id'];

                        $afterHourSmsControllerObj = new AfterHourSmsController();
                        $afterHourSmsControllerObj->afterHourSMSCron($leadDetail[$lr]['lead_id']);
                    }
                }

                if (isset($routeLeadIdArray) && @count($routeLeadIdArray) > 0) {
                    echo "Success";
                    DevDebug::create(['sr_no' => 4, 'result' => 'leadRouteFiveMinute between: ' . $sixmiutes . ' -- '. $fivemiutes . ' - ' . json_encode($routeLeadIdArray), 'created_at' => date('Y-m-d H:i:s')]);
                }
            } else {
                echo "No records found";
            }*/

            foreach ($leadIdArray as $key => $value) {
                //check data of last 5 min which one slot
                //is call yes
                $leadId = $endDecObj->customeEncryption($value);
                $leadRouteObj->reRouteLead($leadId, 3, 0, 1);
                $routeLeadIdArray['lead_id'][] = $value;

                //$afterHourSmsControllerObj = new AfterHourSmsController();
                //$afterHourSmsControllerObj->afterHourSMSCron($value);
            }

            if (isset($routeLeadIdArray) && @count($routeLeadIdArray) > 0) {
                echo "Success";
                DevDebug::create(['sr_no' => 5, 'result' => 'leadRouteFiveMinute between: ' . $sixmiutes . ' -- '. $fivemiutes . ' - ' . json_encode($routeLeadIdArray), 'created_at' => date('Y-m-d H:i:s')]);
            }
        } else {
            echo "No records found";
        }
    }
}
