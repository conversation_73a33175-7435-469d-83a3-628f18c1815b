<?php

namespace App\Http\Controllers\OutboundCall;

use App\Http\Controllers\Controller;
use Auth;
use DB;
use Illuminate\Http\Request;
use App\Models\SalesRep\RepActiveCall;
use App\Models\SalesRep\CallItUserLoginLog;
use App\Models\Outbound\LeadCall;
use App\Models\Outbound\LeadCallLog;
use App\Models\Outbound\CallitSystemLogic;
//use App\Models\Outbound\CallitVoiceMessageLog;
use App\Models\Lead\Lead;
use App\Models\Lead\LeadCallBuffer;
use App\Models\Lead\LeadOldCallBuffer;
use App\Models\Lead\LeadTransferCall;
use App\Models\Lead\LeadRouting;
use App\Models\Lead\LeadMoving;
use App\Models\Lead\LeadCarTransport;
use App\Models\Lead\LeadHeavyLifting;
use App\Models\Lead\LeadFollowUp;
use App\Models\Master\MstLeadSource;
use App\Http\Controllers\LeadCallBuffer\LeadCallBufferController;
use App\Http\Controllers\API\LeadDelivery\LeadDeliveryApi;
use App\Helpers\Helper;
use App\Helpers\CommonFunctions;
use Exception;
use App\Models\Campaign\Campaign;
use App\Models\Campaign\CampaignOngoingCall;
use App\Models\Campaign\CampaignCallPayout;
use App\Models\Business\Business;
use App\Models\User;
use App\Models\Master\MstCallConfig;
use App\Models\Master\MstTimeZone;
use App\Models\Master\MstCampaignType;
use App\Models\Lead\LeadActivity;
use App\Models\B2bcalls\B2BCalls;
use DateTime;
use DateTimeZone;
use App\Http\Controllers\Lead\LeadListController;
use App\Models\Lead\LeadCustomerSmsLog;
use App\Models\Inbound\LeadIbCall;
use App\Http\Controllers\PlivoSms\FollowUpCallSms;

class OutboundCallControllerTest extends Controller {

    public function testCallR(Request $req) {
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        $called_from = "***********";
        
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $url = 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/Call/';
        $data = array(
            'from' => $called_from,
            'to' => "***********",
            'answer_url' => $matrixBaseURL_Outbound . '/testcalltocus',
            'hangup_url' => $matrixBaseURL_Outbound . '/testcallhangcus'
        );
        $data = json_encode($data);
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $general_variable_2 = Helper::checkServer()['general_variable_2'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
        $ch = curl_init($url);
        curl_setopt_array($ch, array(
            CURLOPT_RETURNTRANSFER => TRUE,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_USERPWD => $decVariable1 . ':' . $decVariable2,
            CURLOPT_FOLLOWLOCATION => 1,
            CURLOPT_HTTPHEADER => array('Content-type: application/json', 'Cache-Control:no-store', 'Content-Length: ' . strlen($data)),
            CURLOPT_TIMEOUT => 40
        ));
        $response = curl_exec($ch);
        DB::table('dev_debug')->insert(['sr_no' => 10, 'result' => "10 callit log Calltocurl call==" . $response]);
        //echo "<pre>Calltocurl==";print_r($response);die;
        //dd($response);
        curl_close($ch);
    }
    public function repAnswer(Request $request)
    {
        $repUuid = $request->input('CallUUID');
        Cache::put('rep_uuid', $repUuid, now()->addMinutes(5));

        // Return XML that keeps Rep leg active (like Wait or Speak)
        $response = '<?xml version="1.0" encoding="UTF-8"?>';
        $response .= '<Response>';
        $response .= '<Speak>Connecting you to the customer...</Speak>';
        $response .= '<Wait length="30" />';
        $response .= '</Response>';

        return response($response, 200)->header('Content-Type', 'text/xml');
    }
    public function testCallC(Request $req) {
        //DB::table('dev_debug')->insert(['sr_no' => 10, 'result' => "Test Custore ==" . $req]);
        $Parameters = $req->all();
        $callUUID = "";
        if (isset($Parameters['CallUUID'])) {
           $callUUID = $Parameters['CallUUID'];
        }
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        $called_from = "***********";
        
        // Set Up Parameters
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $url = 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/Call/';
        $data = array(
            'from' => $called_from,
            'to' => "***********",
            'answer_url' => $matrixBaseURL_Outbound . '/testcalltobridge?callUUID1=' . $callUUID,
            'hangup_url' => $matrixBaseURL_Outbound . '/testcallbh'
        );
        $data = json_encode($data);
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $general_variable_2 = Helper::checkServer()['general_variable_2'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
        $ch = curl_init($url);
        curl_setopt_array($ch, array(
            CURLOPT_RETURNTRANSFER => TRUE,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_USERPWD => $decVariable1 . ':' . $decVariable2,
            CURLOPT_FOLLOWLOCATION => 1,
            CURLOPT_HTTPHEADER => array('Content-type: application/json', 'Cache-Control:no-store', 'Content-Length: ' . strlen($data)),
            CURLOPT_TIMEOUT => 40
        ));
        $response = curl_exec($ch);
        DB::table('dev_debug')->insert(['sr_no' => 10, 'result' => "10 callit log Calltocurl call==" . $response]);
        //echo "<pre>Calltocurl==";print_r($response);die;
        //dd($response);
        curl_close($ch);
    }
    public function testCallB(Request $req) {
        $Parameters = $req->all();
        $callUUID1 = "";
        $callUUID2 = "";
        if (isset($Parameters['callUUID1'])) {
           $callUUID1 = $Parameters['callUUID1'];
        }
        if (isset($Parameters['CallUUID'])) {
           $callUUID2 = $Parameters['CallUUID'];
        }
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
       
        // Set Up Parameters
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $url = "https://api.plivo.com/v1/Account/$decVariable1/Call/Bridge/";

        $payload = json_encode([
            $callUUID1,
            $callUUID2
        ]);

        $ch = curl_init($url);
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $general_variable_2 = Helper::checkServer()['general_variable_2'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
        curl_setopt($ch, CURLOPT_USERPWD, "$decVariable1:$decVariable2");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            echo "cURL Error: " . curl_error($ch);
        } else {
            $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            echo "HTTP Status Code: $statusCode\n";
            echo "Response:\n$response\n";
        }

        curl_close($ch);
    }

}
