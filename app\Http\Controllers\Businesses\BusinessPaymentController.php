<?php

namespace App\Http\Controllers\Businesses;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\Controller;
use App\Helpers\CommonFunctions;
use App\Models\Business\Business;
use App\Models\Campaign\Campaign;
use App\Models\Business\BusinessCC;
use App\Models\Business\BusinessCampaignPayment;
use App\Models\Business\BusinessCampaingnPaymentLog;
use App\Models\Business\BusinessCampaignRecuringPayment;
use App\Models\Business\BusinessCampaignRecurringPaymentDay;
use App\Models\Campaign\CampaignFreeLead;
use App\Models\Master\MstChargeType;
use Exception;
use Auth;
use App\Helpers\Helper;
use Log;
use App\Models\Business\BusinessCCContract;
use App\Models\Contract\ContractPaymentLog;
use DB;
use App\Models\Emails\CardContractSendEmail;
use App\Models\Contract\Contract;
use App\Models\DevDebug;
use App\Models\Emails\TemplateEmail;

use App\Models\Business\BusinessUpdateLog;
use App\Models\Campaign\CampaignUpdateLog;
use App\Models\Emails\EmailLog;
use App\Models\Master\MstSubscriptionPlan;
use App\Models\Subscription\Subscription;
use App\Models\Subscription\SubscriptionUpgrade;
use App\Models\Subscription\SubscriptionRecuringPayment;
use Aws\S3\S3Client;

class BusinessPaymentController extends Controller
{
    public function paymentSetup($bid){

        header('Access-Control-Allow-Origin: *');
        $endDecObj = new CommonFunctions;
        $businessId = $endDecObj->customeDecryption($bid);
        $businessDetail = Business::where("business_id", $businessId)->first();
        //get all campaign business wise
        $campaignDetail = Campaign::where('business_id', $businessId)->get(['campaign_id', 'campaign_name', 'credit_available', 'payment_type', 'free_lead']);
        $businessRecuringPayment = $campaignRecuringPayment = [];
        $totalCampaign = count($campaignDetail);
        $businessLevel = $countCampaign = 0;

        if(count($campaignDetail) > 0) {
            $campaignIdArray = [];
            foreach($campaignDetail as $campaign) {
                $campaignIdArray[] = $campaign->campaign_id;
                if($campaign->payment_type == 1) {
                    $countCampaign++;
                }
            }
            //fetch campaign payment in business_campaign_recuring_payment table
            $campaignRecuringPaymentDetail = BusinessCampaignRecuringPayment::whereIn('campaign_id', $campaignIdArray)->get(['business_campaign_recurring_payment_id', 'campaign_id', 'recurring_type', 'is_active']);
            if ($campaignRecuringPaymentDetail) { foreach($campaignRecuringPaymentDetail as $recuringPayment) {
                $campaignRecuringPayment[$recuringPayment->campaign_id] = array('recurring_payment_id' => $recuringPayment->business_campaign_recurring_payment_id, "type" => "Amount", "status"=> $recuringPayment->is_active);
                if ($recuringPayment->recurring_type == "daily") {
                    $campaignRecuringPayment[$recuringPayment->campaign_id] = array('recurring_payment_id' => $recuringPayment->business_campaign_recurring_payment_id, "type" => "Daily", "status"=> $recuringPayment->is_active);
                }
            } }
        }

        //fetch business payment in business_campaign_recuring_payment table
        $businessRecuringPaymentDetail = BusinessCampaignRecuringPayment::where('business_id', $businessId)->get(['business_campaign_recurring_payment_id', 'business_id', 'recurring_type', 'is_active']);
        if (count($businessRecuringPaymentDetail) > 0) {
            $businessRecuringPayment[$businessId] = array('recurring_payment_id'=>$businessRecuringPaymentDetail[0]->business_campaign_recurring_payment_id, "type"=>"Amount", "status" => $businessRecuringPaymentDetail[0]->is_active);
            if($businessRecuringPaymentDetail[0]->recurring_type == "daily") {
                $businessRecuringPayment[$businessId] = array('recurring_payment_id'=>$businessRecuringPaymentDetail[0]->business_campaign_recurring_payment_id, "type"=>"Daily", "status" => $businessRecuringPaymentDetail[0]->is_active);
            }
        }

        if ($totalCampaign != $countCampaign) {
            $businessLevel = 1;
        }

        $BusinessCC = BusinessCC::with(['businessCcContract', 'business', 'contract'])->where("business_id", $businessId)->where('status', "active")->get()->toArray();
        $contractcards = Contract::where("business_id", $businessId)->whereNotNull("contract_screenshot")->get();
        $contractcardsArr =  $cardDetail = [];
        $cust_id = "";

        // S3 Bucket Access
        $s3Client = new S3Client([
            'version' => 'latest',
            'region' => 'us-east-2',
            'credentials' => [
                'key' => '********************',
                'secret' => 'V37Fs7+lJICfcEYDCn6Yn3XigUoU2XK0w1DW8Zy9',
            ]
        ]);

        if(count($BusinessCC) > 0 ){
            foreach ($BusinessCC as $key => $value) {
                $cust_id = $value['business_cc_id'];
                $screenshot = "";
                $cardDetail[$key]['business_cc_id'] = $value['business_cc_id'];
                $cardDetail[$key]['business_id'] = $value['business_id'];
                $cardDetail[$key]['card'] = $value['card'];
                $cardDetail[$key]['is_primary'] = $value['is_primary'];
                $cardDetail[$key]['is_contract_signed'] = $value['business']['is_contract_signed'];
                $screenshot = $contract_type = "";
                if(isset($value['business_cc_contract'])){
                    $contract_type = "Card Contract Signed";
                    /*$screenshot = $value['business_cc_contract']['contract_screenshot'];
                    if(isset($screenshot)){
                        $screenshot = url("card_contract_signed_pdf").'/'. $screenshot;
                    }*/
                    $storagePath = $value['business_id'] . '/' . $value['business_cc_contract']['contract_screenshot']; //s3 bucket path
                    $cmd = $s3Client->getCommand('GetObject', [
                        'Bucket' => (strpos(url()->current(), 'linkup.software') !== false) ? 'pmcontract' : 'pmcontractstaging',
                        'Key' => $storagePath,
                        /*'ResponseContentType' => 'image/jpeg',*/
                        /*'Expires' => 60,*/
                    ]);
                    //The period of availability
                    $request = $s3Client->createPresignedRequest($cmd, '+10 minutes');

                    //Get the pre-signed URL
                    $screenshot = (string) $request->getUri();
                } else if(isset($value['contract'])){
                    $contract_type = "Contract Signed";
                    /*$screenshot = $value['contract']['contract_screenshot'];
                    if(isset($screenshot)){
                        $screenshot = url("contract_signed_pdf").'/'. $screenshot;
                    }*/
                    $storagePath = $value['business_id'] . '/' . $value['contract']['contract_screenshot']; //s3 bucket path
                    $cmd = $s3Client->getCommand('GetObject', [
                        'Bucket' => (strpos(url()->current(), 'linkup.software') !== false) ? 'pmcontract' : 'pmcontractstaging',
                        'Key' => $storagePath,
                        /*'ResponseContentType' => 'image/jpeg',*/
                        /*'Expires' => 60,*/
                    ]);
                    //The period of availability
                    $request = $s3Client->createPresignedRequest($cmd, '+10 minutes');

                    //Get the pre-signed URL
                    $screenshot = (string) $request->getUri();
                }
                $cardDetail[$key]['contract_type'] = $contract_type;
                $cardDetail[$key]['contract_screenshot'] = $screenshot;
            }
        }
        // $cardDetail = array_merge($cardData, $contractcardsArr);
        // array_multisort($cardDetail );
        $omnitoken = Helper::checkServer()['omni_username'];
        // echo "<pre>";
        // print_r( $postmarkToken );
        // die;

        $subscriptionPlanDetail = MstSubscriptionPlan::get();
        $subscriptionPlanArray = [];
        foreach($subscriptionPlanDetail as $subscriptionPlan) {
            $subscriptionPlanArray[$subscriptionPlan->subscription_plan_id] = $subscriptionPlan->subscription_plan;
        }
        $subscriptionDetail = Subscription::where('subscription_start', '>=', date('Y-m-01'))->where('subscription_end', '<=', date('Y-m-t'))->where('is_active', 'yes')->where('business_id', $businessId)->first();
        $subscriptionUpgrade = SubscriptionUpgrade::where('subscription_start', '>=', date('Y-m-01', strtotime('+1 month')))->where('subscription_end', '<=', date('Y-m-t', strtotime('+1 month')))->where('business_id', $businessId)->first();
        $subscriptionRecuringPayment = SubscriptionRecuringPayment::where('business_id', $businessId)->first();
        return view("businesses.paymentsetup", compact('bid', 'businessDetail', 'campaignDetail', 'cardDetail', 'businessLevel', 'businessRecuringPayment', 'campaignRecuringPayment', 'omnitoken', 'subscriptionPlanArray', 'subscriptionDetail', 'subscriptionUpgrade', 'subscriptionRecuringPayment'));
    }

    public function paymentChangeType(Request $request) {
        $status = 0;
        $message = 'Failure';
        try{
            //0 = business level
            //1 = campaign level
            $campaignId = $request->get('campaignId');
            $campaignLevel = $request->get('campaignLevel');
            //update payment_type in campaign table
            Campaign::where('campaign_id', $campaignId)
                ->update([
                    'payment_type' => $campaignLevel
                ]);

            $status = 1;
            $message = 'Success';
        } catch(Exception $e) {
            $message = $e->getMessage();
        }

        $responseData = [
            'status' => $status,
            'message' => $message
        ];
        return response()->json($responseData);
    }

    public function insertFreeLead(Request $request) {
        $status = 0;
        $message = 'Failure';
        try {
            $campaignId = $request->get('campaignId');
            $freeLead = $request->get('freeLead');

            if (empty($freeLead)) {
                throw new Exception('Not Empty');
            }

            //store free lead in campaign_free_lead table
            CampaignFreeLead::create([
                'camapaign_id' => $campaignId,
                'free_lead' => $freeLead,
                'user_id' => Auth::user()->id,
                'created_at' =>  date('Y-m-d H:i:s')
            ]);

            //fetch old free lead from campaign table
            $oldFreeLead = Campaign::where('campaign_id', $campaignId)->get(['free_lead']);
            $freeLead += $oldFreeLead[0]->free_lead;

            //update new free lead in campaign table
            Campaign::where('campaign_id', $campaignId)
                ->update([
                    'free_lead' => $freeLead
                ]);

            $status = 1;
            $message = 'Success';
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        $responseData = [
            'status' => $status,
            'message' => $message
        ];
        return response()->json($responseData);
    }

    public function fetchCard(Request $request){
        $status = 0;
        $message = 'Failure';
        $cardList = array();
        $businessName = $campaignName = "";
        $recurringArray = $recurringDailyArray = $subscriptionRecuring = [];
        try {
            $businessCampaignId = $request->get('businessCampaignId');
            $businessCampaignType = $request->get('businessCampaignType') ?? 0;
            $businessCampaignPayment = $request->get('payment') ?? 0;
            $isSubscription = $request->get('isSubscription') ?? 0;
            $chargeTypeDetail = MstChargeType::whereIn('charge_type_id', [1, 2, 9])->get(['charge_type_id', 'charge_type']);

            //if we have campaign then we will find business from camapaign_id
            if ($businessCampaignType == 2) {
                $campaignDetail = Campaign::where('campaign_id', $businessCampaignId)->get(['campaign_id', 'business_id', 'campaign_name']);
                $businessCampaignId = $campaignDetail[0]->business_id;
                $campaignName = $campaignDetail[0]->campaign_name;
                if ($businessCampaignPayment == 1) {
                    $recuringPaymentDetail = BusinessCampaignRecuringPayment::where('campaign_id', $campaignDetail[0]->campaign_id)->first();
                }
            } else {
                if ($businessCampaignPayment == 1) {
                    $recuringPaymentDetail = BusinessCampaignRecuringPayment::where('business_id', $businessCampaignId)->first();
                }
                if ($isSubscription > 0) {
                    $subscriptionRecuring = SubscriptionRecuringPayment::where('business_id', $businessCampaignId)->first();
                }
            }

            if ($businessCampaignPayment == 1) {
                if($recuringPaymentDetail) {
                    $recurringArray['recurring_payment_id'] = $recuringPaymentDetail->business_campaign_recurring_payment_id;
                    $recurringArray['card'] = $recuringPaymentDetail->business_cc_id;
                    $recurringArray['status'] = ($recuringPaymentDetail->is_active == "yes") ? 1 : 0;
                    $recurringArray['type'] = ($recuringPaymentDetail->recurring_type == "daily") ? 1 : 2; //daily, amount based
                    $recurringArray['amount'] = $recuringPaymentDetail->amount;
                    $recurringArray['threshold'] = $recuringPaymentDetail->threshold_amount;
                    $recuringPaymentDailyDetail = BusinessCampaignRecurringPaymentDay::where('business_campaign_recurring_payment_id', $recuringPaymentDetail->business_campaign_recurring_payment_id)->get();
                    if ($recuringPaymentDailyDetail) { foreach($recuringPaymentDailyDetail as $recuringPaymentDaily) {
                        $data = [];
                        $data['day'] = $recuringPaymentDaily->day;
                        $data['time'] = $recuringPaymentDaily->payment_time;
                        $recurringDailyArray[] = $data;
                    } }
                }

            }

            $businessDetail = Business::with(['businessCC'=> function($q)  {
                $q->where('status', "active");
            }])->where('business_id', "=", $businessCampaignId)->get();
            $businessName = $businessDetail[0]->business_name;
            foreach($businessDetail as $business) {
                foreach($business->businessCC as $card) {
                    $cardList[$card->business_cc_id] = str_pad($card->card, 4, "0", STR_PAD_LEFT);
                }
            }

            if (count($cardList) == 0 ) {
                throw new Exception("Credit/Debit card not added");
            } else {
                $status = 1;
                $message = "Success";
            }

        } catch(Exception $e) {
            $message = $e->getMessage();
        }
        $responseData = [
            'status' => $status,
            'message' => $message,
            'data' => $cardList,
            'chargeTypeDetail' => $chargeTypeDetail,
            'businessName' => $businessName,
            'campaignName' => $campaignName,
            'recurringArray' => $recurringArray,
            'subscriptionRecuring' => $subscriptionRecuring,
            'recurringDailyArray' => $recurringDailyArray
        ];
        //dd($data);
        return response()->json($responseData);
    }

    public function setPrimaryCard(Request $request) {
        $status = 0;
        $message = 'Failure';
        $error = array();
        try {
            $cardId = $request->get('cardId');
            $businessId = $request->get('businessId');
            $businessDetail = Business::where('business_id', $businessId)->first();
            $oldCardDetail = BusinessCC::where('business_id', $businessId)->where('is_primary', 'yes')->first();
            $cardDetail = BusinessCC::where('business_cc_id', $cardId)->first();
            BusinessCC::where('business_cc_id', '<>', $cardId)->where('business_id', $businessId)->update([
                'is_primary' => 'no'
            ]);
            BusinessCC::where('business_cc_id', $cardId)->where('business_id', $businessId)->update([
                'is_primary' => 'yes'
            ]);

            $logArr[] = [
                'created_user_id' => Auth::user()->id,
                'business_id' => $businessId,
                'field_name' => 'primary_card',
                'old_value' => (isset($oldCardDetail)) ? 'XXXX-XXXX-XXXX-' . str_pad($oldCardDetail->card, 4, "0", STR_PAD_LEFT) : '--',
                'new_value' => 'XXXX-XXXX-XXXX-' . str_pad($cardDetail->card, 4, "0", STR_PAD_LEFT),
                'created_at' => date('Y-m-d H:i:s')
            ];
            if (count($logArr) > 0) {
                BusinessUpdateLog::insert($logArr);
            }

            //send email to movers during set new primary card
            $this->setPrimaryCardEmailNotificationTemplate($businessDetail->business_id, $businessDetail->display_name, $businessDetail->email, $cardDetail->card);

            $status         = 1;
            $message        = 'Success';

        } catch (Exception $e) {
            //dd($e->getMessage());
            $error = $e->getMessage();
        }

        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => $error
        ];
        return response()->json($responseData);
    }

    //create customer & card in db & stripe by bk 02/02/2023
    public function insertCard(Request $request) {
        $status = 0;
        $message = 'Failure';
        $error = $logArray = array();

        try {
            $lastFour = $request->get('lastFour');
            $businessId = $request->get('businessId');
            $paymentMethodId = $request->get('paymentMethodId');
            $card_exp_year = $request->get('card_exp_year');
            $customer_id = $request->get('customer_id');
            $card_type = $request->get('card_type');
            $businessDetail = Business::where('business_id', $businessId)->first();
            Business::where('business_id', $businessId)->update([
                'payment_cust_id' => $customer_id
            ]);
            BusinessCC::create([
                'business_id' => $businessId,
                'payment_cust_id' => $customer_id,
                'card' => $lastFour,
                "card_type" => $card_type,
                'payment_card_id' => $paymentMethodId,
                'created_by' => "user",
                'created_at' => date('Y-m-d H:i:s')
            ])->business_cc_id;

            //send email to movers during add new card
            $this->addCardEmailNotificationTemplate($businessDetail->business_id, $businessDetail->display_name, $businessDetail->email, $lastFour);

            $status = 1;
            $message = 'Success';

        } catch (Exception $e) {
            //dd($e->getMessage());
            $error = $e->getMessage();
        }

        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => $error
        ];
        return response()->json($responseData);
    }

    //delete card in db & stripe by bk 02/02/2023
    public function deleteCard(Request $request) {
        $cardId = $request->get('cardId');
        $businessId = $request->get('businessId');
        $status = 0;
        $message = 'Failure';
        if($cardId > 0){
            $cardDetail = BusinessCC::where('business_cc_id', $cardId)->get(['business_cc_id', 'payment_cust_id', 'payment_card_id', 'card'])->toArray();
            if(count($cardDetail) > 0){
                $businessDetail = Business::where('business_id', $businessId)->first();
                $paymentProfileId = $cardDetail[0]['payment_cust_id'];
                $paymentCardId = $cardDetail[0]['payment_card_id'];

                $ch = curl_init();
                $omniTOken = Helper::checkServer()['omni_token'];
                $curlUrl = Helper::checkServer()['omni_baseurl']."payment-method/".$paymentCardId;
                curl_setopt($ch, CURLOPT_URL, $curlUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
                curl_setopt($ch, CURLOPT_HEADER, FALSE);
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");
                curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json","Authorization: Bearer ".$omniTOken,"Accept: application/json"));
                $response = curl_exec($ch);
                curl_close($ch);
                $jsonResponse = json_decode($response, true);
                //var_dump($response);
                // echo "<pre>";print_r($jsonResponse);die;
                if(isset($jsonResponse['id']) || isset($jsonResponse['deleted_at'])){
                    BusinessCC::where('business_cc_id', $cardId)->update(['payment_card_id' => '', 'status'=> "inactive"]);

                    //send email to movers during delete new primary card
                    $this->removeCardEmailNotificationTemplate($businessDetail->business_id, $businessDetail->display_name, $businessDetail->email, $cardDetail[0]['card']);

                    $status         = 1;
                    $message        = "Success";
                }
            }
        }
        $responseData = [
            'status' => $status,
            'message' => $message
        ];
        return response()->json($responseData);
    }

    public function insertCardCharge(Request $request) {
        $error = $businessCampaignName = "";
        $status = $businessId = $campaignId = $convenienceFee = 0;
        $message = 'Failure';
        $cardreponse = null;
        $validator = Validator::make($request->all(),[
            'charge'=>'required|numeric|min:1',
            'lastFour' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json(array(
                'status' => 0,
                'message' => 'There are incorect values in the form!',
                'error' => $validator->getMessageBag()->toArray()
            ), 200);
            $this->throwValidationException(
                $request, $validator
            );
        }

        try {
            $businessCampaignId = $request->get('businessCampaignId');
            $businessCampaignType = $request->get('businessCampaignType');
            $businessCampaignCharge = $request->get('charge');
            $lastFour = $request->get('lastFour');

            if ($businessCampaignType == 2) {
                $campaignDetail = Campaign::where('campaign_id', $businessCampaignId)->get(['campaign_id', 'business_id', 'campaign_name']);
                $businessId = $campaignDetail[0]->business_id;
                $campaignId = $campaignDetail[0]->campaign_id;
                $businessCampaignName = $campaignDetail[0]->campaign_name;
            } else {
                $businessId = $businessCampaignId;
            }

            $userId = Auth::user()->id;
            $ip = $request->ip();
            $useragent = $_SERVER['HTTP_USER_AGENT'];

            if ($lastFour == 1 || $lastFour == 2 || $lastFour == 9) {
                $chargeType = $lastFour;
                $this->addCreditBankBalance($businessId, $campaignId, $userId, $businessCampaignCharge, $businessCampaignType, $chargeType, $ip, $useragent);
                $status = 1;
                $message = 'Success';
                $responseData = [
                    'status' => $status,
                    'message' => $message,
                    'error' => array('code' => "System Error", 'message' => $message)
                ];

                return response()->json($responseData);
            }
            //dd($userId);
            //fetch customer details from businesses table
            $businessDetail = Business::with(['businessCC' => function($query) {
                $query->where('status', 'active');
            }])->where('business_id', "=", $businessId)->get();

            $businessName = $businessDetail[0]->display_name;
            $businessEmail = $businessDetail[0]->email;
            if ($businessCampaignType <> 2) {
                $businessCampaignName = $businessDetail[0]->display_name;
            }

            /*foreach($businessDetail as $cardDetail) {
                foreach($cardDetail->businessCC as $card) {
                    if($lastFour == $card->card) {
                        $paymentCardId = $card->payment_card_id;
                        $paymentProfileId = $card->payment_cust_id;
                        $cardId = $card->business_cc_id;
                        break;
                    }
                }
            }*/

            $cardDetail         = BusinessCC::where('business_cc_id', $lastFour)->where('status', 'active')->first();
            if ($cardDetail->payment_card_id == "") {
                throw new Exception('Unable to process the purchase transaction.');
            }
            $paymentCardId      = $cardDetail->payment_card_id;
            $paymentProfileId   = $cardDetail->payment_cust_id;
            $cardId             = $cardDetail->business_cc_id;
            //Added by BK on 03/02/2025 convenience fee to evey payment
            if ($businessCampaignCharge > 0) {
                $convenienceFee = ($businessCampaignCharge * 3) / 100;
                $businessCampaignCharge = $businessCampaignCharge + $convenienceFee;
            }

            $omni_token         = Helper::checkServer()['omni_token'];
            $postFields         = array("payment_method_id"=>$paymentCardId, "meta"=>array("tax"=>0), "total"=>$businessCampaignCharge, "pre_auth"=>0);
            $jsonData           = json_encode($postFields);
            $ch                 = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://apiprod.fattlabs.com/charge");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
            curl_setopt($ch, CURLOPT_HEADER, FALSE);
            curl_setopt($ch, CURLOPT_POST, TRUE);
            DevDebug::create(['sr_no' => 115, 'result' => 'Payment request: ' . $jsonData, 'created_at' => date('Y-m-d H:i:s')]);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);

            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/json",
                "Authorization:Bearer ".$omni_token,
                "Accept: application/json"
            ));

            $response           = curl_exec($ch);
            curl_close($ch);
            $jsonResponse       = json_decode($response, true);
            if (array_key_exists("payment_method_id",$jsonResponse)){
                if (is_array($jsonResponse['payment_method_id'])){
                    if(count($jsonResponse['payment_method_id']) > 0){
                        $error = $jsonResponse['payment_method_id'][0];
                    }
                }
            }
            if (!empty($jsonResponse['error_description'])) {
                if (array_key_exists("error_description",$jsonResponse) ){
                    $error = $jsonResponse['error_description'];
                }
            }
            DevDebug::create(['sr_no' => 116, 'result' => 'Payment response: ' . json_encode($jsonResponse), 'created_at' => date('Y-m-d H:i:s')]);

            //Added by BK on 03/02/2025 convenience fee to evey payment
            if ($convenienceFee > 0) {
                $businessCampaignCharge = $businessCampaignCharge - $convenienceFee;
            }
            if($error == ""){
                $responseStatus = "no";
                if (isset($jsonResponse['success']) && $jsonResponse['success'] == true) {
                    $responseStatus = "yes";
                }

                if ($businessCampaignType == 2) {
                    $totalBalance = Campaign::where('campaign_id', $businessCampaignId)->get(['credit_available']);
                    $beforeCharge = 0;
                    if (count($totalBalance) > 0) {
                        $beforeCharge = (float)$totalBalance[0]->credit_available;
                    }

                    $amountCharge = (float)$businessCampaignCharge;
                    $afterCharge = $beforeCharge;
                    if($responseStatus == "yes") {
                        $afterCharge = (float)$beforeCharge + (float)$amountCharge;
                    }

                    $businessCampaignPaymentId = BusinessCampaignPayment::create([
                        'campaign_id' => $businessCampaignId,
                        'business_cc_id' => $cardId,
                        'balance_before_charge' => $beforeCharge,
                        'amount_charge' => $amountCharge,
                        'balance_after_charge' => $afterCharge,
                        'convenience_fee' => $convenienceFee,
                        'charge_type_id'=>1,
                        'charge_method_type' => 'snapituser',
                        'charge_user_id' => $userId,
                        'charge_method' => 'other',
                        'fatt_payment_id' => (isset($jsonResponse['id'])) ? $jsonResponse['id'] : '',
                        'is_charge_approved' => $responseStatus,
                        'created_at' => date('Y-m-d H:i:s'),
                        'charge_user_ip' => $ip,
                        'charge_user_agent' => $useragent
                    ])->business_campaign_payment_id;
                } else {
                    $totalBalance = Business::where('business_id', $businessCampaignId)->get(['credit_available']);
                    $beforeCharge = 0;
                    if(count($totalBalance) > 0) {
                        $beforeCharge = (float)$totalBalance[0]->credit_available;
                    }
                    $amountCharge = (float)$businessCampaignCharge;
                    $afterCharge = $beforeCharge;
                    if($responseStatus == "yes") {
                        $afterCharge = (float)$beforeCharge + (float)$amountCharge;
                    }

                    $businessCampaignPaymentId = BusinessCampaignPayment::create([
                        'business_id' => $businessCampaignId,
                        'business_cc_id' => $cardId,
                        'balance_before_charge' => $beforeCharge,
                        'amount_charge' => $amountCharge,
                        'balance_after_charge' => $afterCharge,
                        'convenience_fee' => $convenienceFee,
                        'charge_type_id' => 1,
                        'charge_method_type' => 'snapituser',
                        'charge_user_id' => $userId,
                        'charge_method' => 'other',
                        'fatt_payment_id' => (isset($jsonResponse['id'])) ? $jsonResponse['id'] : '',
                        'is_charge_approved' => $responseStatus,
                        'created_at' => date('Y-m-d H:i:s'),
                        'charge_user_ip' => $ip,
                        'charge_user_agent' => $useragent
                    ])->business_campaign_payment_id;
                }

                BusinessCampaingnPaymentLog::create([
                    'business_campaign_payment_id' => $businessCampaignPaymentId,
                    'request' => $jsonData,
                    'response' => json_encode($jsonResponse, true),
                    'created_at' => date('Y-m-d H:i:s'),
                ])->business_campaign_payment_log_id;

                //fetch total balance from businessespaymenthistory table
                if ($responseStatus == "yes") {
                    if($businessCampaignType == 2) {
                        //update balance in campaign table
                        Campaign::where('campaign_id', $businessCampaignId)
                            ->update([
                                'credit_available' => $afterCharge
                            ]);
                    } else {
                        //update balance in business table
                        Business::where('business_id', $businessCampaignId)
                            ->update([
                                'credit_available' => $afterCharge
                            ]);
                    }

                    //Added by BK on 05/02/2025 convenience fee email to evey success payment
                    $transactionId = (isset($jsonResponse['id'])) ? $jsonResponse['id'] : '';
                    $this->moversRechargeEmailNotificationTemplate($businessId, $businessName, $businessCampaignName, $businessEmail, $cardDetail->card, $businessCampaignCharge, $convenienceFee, $transactionId);
                }
                $status = 1;
                $message = 'Success';
            } else {
                if ($businessCampaignType == 2) {
                    $totalBalance = Campaign::where('campaign_id', $businessCampaignId)->get(['credit_available']);
                    $beforeCharge = 0;
                    if (count($totalBalance) > 0) {
                        $beforeCharge = (float)$totalBalance[0]->credit_available;
                    }
                    $amountCharge = (float)$businessCampaignCharge;
                    $afterCharge = $beforeCharge;

                    $businessCampaignPaymentId = BusinessCampaignPayment::create([
                        'campaign_id' => $businessCampaignId,
                        'business_cc_id' => $cardId,
                        'balance_before_charge' => $beforeCharge,
                        'amount_charge' => $amountCharge,
                        'balance_after_charge' => $afterCharge,
                        'convenience_fee' => $convenienceFee,
                        'charge_type_id'=>1,
                        'charge_method_type' => 'snapituser',
                        'charge_user_id' => $userId,
                        'charge_method' => 'other',
                        'fatt_payment_id' => (isset($jsonResponse['id'])) ? $jsonResponse['id'] : '',
                        'is_charge_approved' => "no",
                        'created_at' => date('Y-m-d H:i:s'),
                        'charge_user_ip' => $ip,
                        'charge_user_agent' => $useragent
                    ])->business_campaign_payment_id;
                } else {
                    $totalBalance = Business::where('business_id', $businessCampaignId)->get(['credit_available']);
                    $beforeCharge = 0;
                    if(count($totalBalance) > 0) {
                        $beforeCharge = (float)$totalBalance[0]->credit_available;
                    }
                    $amountCharge = (float)$businessCampaignCharge;
                    $afterCharge = $beforeCharge;

                    $businessCampaignPaymentId = BusinessCampaignPayment::create([
                        'business_id' => $businessCampaignId,
                        'business_cc_id' => $cardId,
                        'balance_before_charge' => $beforeCharge,
                        'amount_charge' => $amountCharge,
                        'balance_after_charge' => $afterCharge,
                        'convenience_fee' => $convenienceFee,
                        'charge_type_id' => 1,
                        'charge_method_type' => 'snapituser',
                        'charge_user_id' => $userId,
                        'charge_method' => 'other',
                        'fatt_payment_id' => (isset($jsonResponse['id'])) ? $jsonResponse['id'] : '',
                        'is_charge_approved' => "no",
                        'created_at' => date('Y-m-d H:i:s'),
                        'charge_user_ip' => $ip,
                        'charge_user_agent' => $useragent
                    ])->business_campaign_payment_id;
                }

                BusinessCampaingnPaymentLog::create([
                    'business_campaign_payment_id' => $businessCampaignPaymentId,
                    'request' => $jsonData,
                    'response' => json_encode($jsonResponse, true),
                    'created_at' => date('Y-m-d H:i:s'),
                ])->business_campaign_payment_log_id;

                throw new Exception('Unable to process the purchase transaction');
            }
        } catch(Exception $e) {
            $message = $e->getMessage();
        }

        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => array('code'=>"System Error", 'message' => $error),
            'cardresponse' => $cardreponse
        ];
        return response()->json($responseData);
    }

    public function addCreditBankBalance($businessId, $campaignId, $userId, $businessCampaignCharge, $businessCampaignType, $chargeType, $ip="", $useragent="", $subscription=0) {
        //dd($businesessId . ":" . $type . ":" . $businessesCampId . ":" . $userId . ":" . $userId);
        if ($businessCampaignType == 1) {
            //fetch total balance from business table
            $totalBalance = Business::where('business_id', $businessId)->get(['credit_available']);
            $beforeCharge = (float)$totalBalance[0]->credit_available;
            $amountCharge = (float)$businessCampaignCharge;
            $afterCharge = (float)$beforeCharge + (float)$amountCharge;
            $chargeMethodType = 'snapituser';
            if ($subscription > 0) {
                $beforeCharge = $afterCharge = 0;
                $chargeMethodType = 'subscription';
            }

            BusinessCampaignPayment::create([
                'business_id' => $businessId,
                'balance_before_charge' => $beforeCharge,
                'amount_charge' => $amountCharge,
                'balance_after_charge' => $afterCharge,
                'charge_type_id' => $chargeType,
                'charge_method_type' => $chargeMethodType,
                'charge_user_id' => $userId,
                'charge_method' => 'other',
                'fatt_payment_id' => NULL,
                'is_charge_approved' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'charge_user_ip' => $ip,
                'charge_user_agent' => $useragent
            ])->business_campaign_payment_id;

            //update balance in business table
            Business::where('business_id', $businessId)
                ->update([
                    'credit_available' => $afterCharge
                ]);
        } else {
            //fetch total balance from campaign table
            $totalBalance = Campaign::where('campaign_id', $campaignId)->get(['credit_available']);
            $beforeCharge = (float)$totalBalance[0]->credit_available;
            $amountCharge = (float)$businessCampaignCharge;
            $afterCharge = (float)$beforeCharge + (float)$amountCharge;
            $chargeMethodType = 'snapituser';
            if ($subscription > 0) {
                $beforeCharge = $afterCharge = 0;
                $chargeMethodType = 'subscription';
            }

            BusinessCampaignPayment::create([
                'campaign_id' => $campaignId,
                'balance_before_charge' => $beforeCharge,
                'amount_charge' => $amountCharge,
                'balance_after_charge' => $afterCharge,
                'charge_type_id' => $chargeType,
                'charge_method_type' => $chargeMethodType,
                'charge_user_id' => $userId,
                'charge_method' => 'other',
                'fatt_payment_id' => NULL,
                'is_charge_approved' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'charge_user_ip' => $ip,
                'charge_user_agent' => $useragent
            ])->business_campaign_payment_id;

            //update balance in campaign table
            Campaign::where('campaign_id', $campaignId)
                ->update([
                    'credit_available' => $afterCharge
                ]);
        }
    }

    public function fetchDebitBusiness(Request $request) {
        $status = 0;
        $message = 'Failure';
        $cardList = array();
        $businessName = $campaignName = "";
        try {
            $businessCampaignId = $request->get('businessCampaignId');
            $businessCampaignType = $request->get('businessCampaignType') ?? 0;
            $businessCampaignPayment = $request->get('payment');
            if ($businessCampaignType == 3) {
                $chargeTypeDetail = MstChargeType::whereIn('charge_type_id', [4, 11])->get(['charge_type_id', 'charge_type']);
            } else {
                $chargeTypeDetail = MstChargeType::whereIn('charge_type_id', [3, 4, 5, 6, 11])->get(['charge_type_id', 'charge_type']);
            }

            //if we have campaign then we will find business from camapaign_id
            if ($businessCampaignType == 2) {
                $campaignDetail = Campaign::where('campaign_id', $businessCampaignId)->get(['campaign_id', 'business_id', 'campaign_name']);
                $businessCampaignId = $campaignDetail[0]->business_id;
                $campaignName = $campaignDetail[0]->campaign_name;
            }

            $businessDetail = Business::with(['businessCC'])->where('business_id', "=", $businessCampaignId)->get();
            $businessName = $businessDetail[0]->business_name;
            $cardList = '';
            $status = 1;
            $message = "Success";

        } catch(Exception $e) {
            $message = $e->getMessage();
        }
        $responseData = [
            'status' => $status,
            'message' => $message,
            'data' => $cardList,
            'chargeTypeDetail' => $chargeTypeDetail,
            'businessName' => $businessName,
            'campaignName' => $campaignName,

        ];
        //dd($data);
        return response()->json($responseData);
    }

    public function fetchBusinessTransation(Request $request) {
        $status = 0;
        $message = 'Failure';
        $data = "";
        try {
            $businessCampaignId = $request->get('businessCampaignId');
            $businessCampaignType = $request->get('businessCampaignType');
            $businessCampaignPayment = $request->get('payment');
            $chargeTypeDetail = MstChargeType::whereIn('charge_type_id', [3, 4, 5, 6, 11])->get(['charge_type_id', 'charge_type']);

            //if we have campaign then we will find business from camapaign_id
            if ($businessCampaignType == 2) {
                $campaignDetail = Campaign::where('campaign_id', $businessCampaignId)->get(['campaign_id', 'business_id', 'campaign_name']);
                $businessCampaignId = $campaignDetail[0]->business_id;
                $campaignName = $campaignDetail[0]->campaign_name;
            }

            $businessDetail = Business::with(['businessCC'])->where('business_id', "=", $businessCampaignId)->get();
            $businessName = $businessDetail[0]->business_name;
            $cardList = '';
            $status = 1;
            $message = "Success";

        } catch(Exception $e) {
            $message = $e->getMessage();
        }

        try {
            $businessCampaignId = $request->get('businessCampaignId');
            $businessCampaignType = $request->get('businessCampaignType');
            $debitOption = $request->get('debitOption');

            $cardIdArray = $lastFourArray = $finalArray = $chargeTypeArr = [];
            if($debitOption == 4 || $debitOption == '4' || $debitOption == 5 || $debitOption == '5'){
                $chargeTypeArr = ['11'];
            }else if($debitOption == 11 || $debitOption == '11'){
                $chargeTypeArr = ['4'];
            }
            $fatt_paymentArr= BusinessCampaignPayment::whereIn('charge_type_id', $chargeTypeArr)->whereNotNull("fatt_payment_id")->where('is_charge_approved', 'yes')->pluck('fatt_payment_id')->toArray();
            if ($businessCampaignType == 1) {
                $cardDetail = BusinessCC::where('status', "active")->where('business_id',$businessCampaignId)->get(['business_cc_id', 'card'])->toArray();
                for($b=0; $b < count($cardDetail); $b++){
                    $cardIdArray[] = $cardDetail[$b]['business_cc_id'];
                    $lastFourArray[$cardDetail[$b]['business_cc_id']] = $cardDetail[$b]['card'];
                }
                $campaignDetail = Campaign::where('business_id', $businessCampaignId)->pluck('campaign_id')->toArray();
                // DB::enableQueryLog();
                $paymentDetail = BusinessCampaignPayment::whereIn('charge_type_id', ['1',"4"])->where('is_charge_approved', 'yes')->whereIn('business_cc_id', $cardIdArray)->where(function ($q) use ($businessCampaignId, $campaignDetail) {
                    $q->where(function ($q) use($businessCampaignId) {
                        $q->where('business_id', $businessCampaignId);
                    })->orWhere(function($q) use ($campaignDetail) {
                        $q->whereIn("campaign_id", $campaignDetail);
                    });
                })->whereNotIn("fatt_payment_id", $fatt_paymentArr)->orderby('business_campaign_payment_id', 'DESC')->get();
            } else {
                $campaignDetail = Campaign::where('campaign_id', $businessCampaignId)->get();
                $businessId = $campaignDetail[0]->business_id;
                $cardDetail = BusinessCC::where('status', "active")->where('business_id',$businessId)->get(['business_cc_id', 'card'])->toArray();
                for($b=0; $b < count($cardDetail); $b++){
                    $cardIdArray[] = $cardDetail[$b]['business_cc_id'];
                    $lastFourArray[$cardDetail[$b]['business_cc_id']] = $cardDetail[$b]['card'];
                }
                $campaignDetail = Campaign::where('business_id', $businessId)->pluck('campaign_id')->toArray();
                // DB::enableQueryLog();
                $paymentDetail = BusinessCampaignPayment::where('charge_type_id', "1")->where('is_charge_approved', 'yes')->whereIn('business_cc_id', $cardIdArray)->where(function ($q) use ($businessId, $campaignDetail) {
                    $q->where(function ($q) use($businessId) {
                        $q->where('business_id', $businessId);
                    })->orWhere(function($q) use ($campaignDetail) {
                        $q->whereIn("campaign_id", $campaignDetail);
                    });
                })->whereNotIn("fatt_payment_id", $fatt_paymentArr)->orderby('business_campaign_payment_id', 'DESC')->get();
            }

            foreach ($paymentDetail as $key => $payment){
                $lastFour = "XXXX";
                if(isset($lastFourArray[$payment->business_cc_id])){
                    $lastFour = $lastFourArray[$payment->business_cc_id];
                }
                $payment->date = date("Y-m-d",strtotime($payment->created_at));
                $payment->card = $lastFour;
                $refundAmount = BusinessCampaignPayment::where('fatt_payment_id', $payment->fatt_payment_id)->whereIn('charge_type_id', ['6','7'])->where('is_charge_approved', 'yes')->sum('amount_charge');
                if ($payment->amount_charge > $refundAmount){
                    $finalArray[] = $payment;
                }
            }

            $data = $finalArray;
            $status = 1;
            $message = 'Success';
        } catch(Exception $e) {
            $message = $e->getMessage();
        }
        $responseData = [
            'status' => $status,
            'message' => $message,
            'data' => $data
        ];
        //dd($data);
        return response()->json($responseData);
    }

    public function fetchDebitSubscriptionBusiness(Request $request) {
        $status = 0;
        $message = 'Failure';
        $data = "";
        try {
            $businessCampaignId = $request->get('businessCampaignId');
            $businessCampaignPayment = $request->get('payment');
            $chargeTypeDetail = MstChargeType::whereIn('charge_type_id', [4, 11])->get(['charge_type_id', 'charge_type']);

            $businessDetail = Business::with(['businessCC'])->where('business_id', "=", $businessCampaignId)->get();
            $businessName = $businessDetail[0]->business_name;
            $cardList = '';
            $status = 1;
            $message = "Success";

        } catch(Exception $e) {
            $message = $e->getMessage();
        }

        try {
            $businessCampaignId = $request->get('businessCampaignId');
            $businessCampaignType = $request->get('businessCampaignType') ?? 0;
            $debitOption = $request->get('debitOption');

            $cardIdArray = $lastFourArray = $finalArray = $chargeTypeArr = [];
            if($debitOption == 4 || $debitOption == '4' || $debitOption == 5 || $debitOption == '5'){
                $chargeTypeArr = ['11'];
            }else if($debitOption == 11 || $debitOption == '11'){
                $chargeTypeArr = ['4'];
            }
            $fatt_paymentArr= BusinessCampaignPayment::whereIn('charge_type_id', $chargeTypeArr)->whereNotNull("fatt_payment_id")->where('is_charge_approved', 'yes')->pluck('fatt_payment_id')->toArray();
            $cardDetail = BusinessCC::where('status', "active")->where('business_id',$businessCampaignId)->get(['business_cc_id', 'card'])->toArray();
            for($b=0; $b < count($cardDetail); $b++){
                $cardIdArray[] = $cardDetail[$b]['business_cc_id'];
                $lastFourArray[$cardDetail[$b]['business_cc_id']] = $cardDetail[$b]['card'];
            }
            $campaignDetail = Campaign::where('business_id', $businessCampaignId)->pluck('campaign_id')->toArray();
            // DB::enableQueryLog();
            $paymentDetail = BusinessCampaignPayment::whereIn('charge_type_id', ['1',"4"])->where('is_charge_approved', 'yes')->whereIn('business_cc_id', $cardIdArray)->where(function ($q) use ($businessCampaignId, $campaignDetail) {
                $q->where(function ($q) use($businessCampaignId) {
                    $q->where('business_id', $businessCampaignId);
                })->orWhere(function($q) use ($campaignDetail) {
                    $q->whereIn("campaign_id", $campaignDetail);
                });
            })->whereNotIn("fatt_payment_id", $fatt_paymentArr)->where('charge_method_type', 'subscription')->orderby('business_campaign_payment_id', 'DESC')->get();

            foreach ($paymentDetail as $key => $payment){
                $lastFour = "XXXX";
                if(isset($lastFourArray[$payment->business_cc_id])){
                    $lastFour = $lastFourArray[$payment->business_cc_id];
                }
                $payment->date = date("Y-m-d",strtotime($payment->created_at));
                $payment->card = $lastFour;
                $refundAmount = BusinessCampaignPayment::where('fatt_payment_id', $payment->fatt_payment_id)->whereIn('charge_type_id', ['6','7'])->where('is_charge_approved', 'yes')->sum('amount_charge');
                if ($payment->amount_charge > $refundAmount){
                    $finalArray[] = $payment;
                }
            }

            $data = $finalArray;
            $status = 1;
            $message = 'Success';
        } catch(Exception $e) {
            $message = $e->getMessage();
        }
        $responseData = [
            'status' => $status,
            'message' => $message,
            'data' => $data
        ];
        //dd($data);
        return response()->json($responseData);
    }

    public function fetchSameBusiness(Request $request) {
        $status = 0;
        $message = 'Failure';
        $businessArray = [];
        try {
            $businessCampaignId = $request->get('businessCampaignId');
            $businessCampaignType = $request->get('businessCampaignType');
            $typeOfBusiness = $request->get('typeOfBusiness');

            if ($businessCampaignType == 2) {
                $campaignDetail = Campaign::where('campaign_id', "=", $businessCampaignId)->get();
                $businessDetail = Business::where('business_id', $campaignDetail[0]->business_id)->get();

                $business = array();
                $business['business_campaign_id'] = $businessDetail[0]->business_id;
                $business['business_campaign_name'] = $businessDetail[0]->business_name;
                $business['business_campaign_type'] = 'b';
                $businessArray[] = $business;
                $sameCampaignDetail = Campaign::where('business_id', "=", $businessDetail[0]->business_id)->whereNotIn('campaign_id', [$businessCampaignId])->get();

                foreach ($sameCampaignDetail as $key => $value) {
                    $campaign = array();
                    $campaign['business_campaign_id'] = $value->campaign_id;
                    $campaign['business_campaign_name'] = $value->campaign_id.' '.$value->campaign_name;
                    $campaign['business_campaign_type'] = 'c';
                    $businessArray[] = $campaign;
                }
            } else {
                $businessDetail = Business::where('business_id', $businessCampaignId)->get();
                $sameCampaignDetail = Campaign::where('business_id', "=", $businessDetail[0]->business_id)->get();

                foreach ($sameCampaignDetail as $key => $value) {
                    $campaign = array();
                    $campaign['business_campaign_id'] = $value->campaign_id;
                    $campaign['business_campaign_name'] = $value->campaign_id.' '.$value->campaign_name;
                    $campaign['business_campaign_type'] = 'c';
                    $businessArray[] = $campaign;
                }
            }

            $status = 1;
            $message = "Success";
        } catch(Exception $e) {
            $message = $e->getMessage();
        }
        $responseData = [
            'status' => $status,
            'message' => $message,
            'data' => $businessArray,
        ];
        return response()->json($responseData);
    }

    public function fetchOtherBusiness(Request $request) {
        $status = 0;
        $message = 'Failure';
        $businessArray = [];
        try {
            $businessCampaignId = $request->get('businessCampaignId');
            $businessCampaignType = $request->get('businessCampaignType');

            if ($businessCampaignType == 2) {
                $campaignDetail = Campaign::where('campaign_id', "=", $businessCampaignId)->get();
                $businessDetail = Business::with(['businessCampaign'])->whereNotIn('business_id', [$campaignDetail[0]->business_id])->where('is_contract_signed', 'yes')->get();

                foreach ($businessDetail as $key => $value) {
                    if (isset($value->businessCampaign)){
                        foreach ($value->businessCampaign as $ckey => $cvalue) {
                            $campaign = array();
                            $campaign['business_campaign_id'] = $cvalue->campaign_id;
                            $campaign['business_campaign_name'] = $cvalue->campaign_id.' '.$cvalue->campaign_name;
                            $campaign['business_campaign_type'] = 'c';
                            /*$businessArray[] = $campaign;*/
                        }
                    }
                    if ($value->is_contract_signed == "yes"){
                        $business = array();
                        $business['business_campaign_id'] = $value->business_id;
                        $business['business_campaign_name'] = $value->business_id.' '.$value->business_name;
                        $business['business_campaign_type'] = 'b';
                        $businessArray[] = $business;
                    }
                }
            } else {
                $businessDetail = Business::with(['businessCampaign'])->whereNotIn('business_id', [$businessCampaignId])->where('is_contract_signed', 'yes')->get();
                foreach ($businessDetail as $key => $value) {
                    if (isset($value->businessCampaign)){
                        foreach ($value->businessCampaign as $ckey => $cvalue) {
                            $campaign = array();
                            $campaign['business_campaign_id'] = $cvalue->campaign_id;
                            $campaign['business_campaign_name'] = $cvalue->campaign_id.' '.$cvalue->campaign_name;
                            $campaign['business_campaign_type'] = 'c';
                            /*$businessArray[] = $campaign;*/
                        }
                    }
                    if ($value->is_contract_signed == "yes"){
                        $business = array();
                        $business['business_campaign_id'] = $value->business_id;
                        $business['business_campaign_name'] = $value->business_id.' '.$value->business_name;
                        $business['business_campaign_type'] = 'b';
                        $businessArray[] = $business;
                    }
                }
            }

            $status = 1;
            $message = "Success";

        } catch(Exception $e) {
            $message = $e->getMessage();
        }
        $responseData = [
            'status' => $status,
            'message' => $message,
            'data' => $businessArray,
        ];
        return response()->json($responseData);
    }

    public function insertCardDebitCharge(Request $request) {
        // dd($request->all());
        $userId = Auth::user()->id;
        $error = "";
        $status = 0;
        $message = 'Failure';
        $debitOption = $request->get('debitOption');
        $rules = array(
            // 'txtDebitCharge'=>"min:1|required_if:debitOption,3,4,5,6",
            'debitOption' => 'required',
            'debitTransation' => 'required_if:debitOption,4,5,11',
            'typeOfBusiness' => 'required_if:debitOption,6',
            'transactionBusiness' => 'required_if:debitOption,6',
        );
        if($debitOption == 3 || $debitOption == 4 || $debitOption == 5 || $debitOption == 6 ){
            $rules['txtDebitCharge'] = 'required|numeric|min:1';
        }
        // if( $debitOption == 4 || $debitOption == 5 || $debitOption == 11 ){
        //     $rules['debitTransation'] = 'required';
        // }
        $messages = array(
            'txtDebitCharge.required' => 'Please enter Amount',
            'txtDebitCharge.min' => 'Please enter at least 1 Amount',
            'debitTransation.required_if' => 'Please select Transaction',
            'typeOfBusiness.required_if' => 'Please select Type of Business',
            'transactionBusiness.required_if' => 'Please select Business'
        );
        $validator = Validator::make($request->all(), $rules, $messages);
        // echo "<pre>rules= ";print_r($validator);die;
        if ($validator->fails()) {
            return response()->json(array(
                'status' => 0,
                'message' => 'There are incorect values in the form!',
                'error' => $validator->getMessageBag()->toArray()
            ), 200);
            $this->throwValidationException(
                $request, $validator
            );
        }
        $error = null;
        try {

            $businessCampaignId = $request->get('businessCampaignId');
            $businessCampaignType = $request->get('businessCampaignType');
            $businessCampaignCharge = $request->get('txtDebitCharge');
            $debitOption = $request->get('debitOption');
            $debitTransation = $request->get('debitTransation');
            $typeOfBusiness = $request->get('typeOfBusiness');
            $transactionBusiness = $request->get('transactionBusiness');
            $transactionBusinessHiddenType = $request->get('transactionBusinessHiddenType');
            $ip = $request->ip();
            $refundStatus = [];

            if ($debitOption == 3){ //3=other debit
                $this->otherDebit($businessCampaignId, $businessCampaignType, $businessCampaignCharge, $debitOption, $userId, $ip);
                $status = 1;
                $message = 'Success';
            } else if($debitOption == 4 || $debitOption == 5 || $debitOption == 11){ //4=refund & 5=chargeback & 1=void payment
                $refundStatus = $this->refundChargeBack($businessCampaignId, $businessCampaignType, $businessCampaignCharge, $debitOption, $debitTransation, $userId, $ip);
                // echo "<pre>response= ";print_r($refundStatus);die;

            } else if($debitOption == 6){ //6=transfer from
                $this->transferFrom($businessCampaignId, $businessCampaignType, $businessCampaignCharge, $debitOption, $transactionBusiness, $transactionBusinessHiddenType, $userId, $ip);
                $status = 1;
                $message = 'Success';
            }
            if(count($refundStatus) > 0) {
                $status = $refundStatus['status'];
                $error = $refundStatus['error'];
                $message = ($status == 1 ) ? 'Success' : "Failure";
            }
        }
        catch(Exception $e){
            $message = $e->getMessage();
        }
        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => $error
        ];
        return response()->json($responseData);
    }

    public function insertCardDebitSubscriptionCharge(Request $request) {
        // dd($request->all());
        $userId = Auth::user()->id;
        $error = "";
        $status = 0;
        $message = 'Failure';
        $debitOption = $request->get('debitSubscriptionOption');
        $rules = array(
            // 'txtDebitCharge'=>"min:1|required_if:debitOption,3,4,5,6",
            'debitSubscriptionOption' => 'required',
            'debitSubscriptionTransation' => 'required_if:debitSubscriptionOption,4,11'
        );
        if($debitOption == 3 || $debitOption == 4 || $debitOption == 5 || $debitOption == 6 ){
            $rules['txtDebitSubscriptionCharge'] = 'required|numeric|min:1';
        }
        // if( $debitOption == 4 || $debitOption == 5 || $debitOption == 11 ){
        //     $rules['debitTransation'] = 'required';
        // }
        $messages = array(
            'txtDebitSubscriptionCharge.required' => 'Please enter Amount',
            'txtDebitSubscriptionCharge.min' => 'Please enter at least 1 Amount',
            'debitSubscriptionTransation.required_if' => 'Please select Transaction'
        );
        $validator = Validator::make($request->all(), $rules, $messages);
        // echo "<pre>rules= ";print_r($validator);die;
        if ($validator->fails()) {
            return response()->json(array(
                'status' => 0,
                'message' => 'There are incorect values in the form!',
                'error' => $validator->getMessageBag()->toArray()
            ), 200);
            $this->throwValidationException(
                $request, $validator
            );
        }
        $error = null;
        try {

            $businessCampaignId = $request->get('businessCampaignId');
            $businessCampaignType = 1;
            $businessCampaignCharge = $request->get('txtDebitSubscriptionCharge');
            $debitOption = $request->get('debitSubscriptionOption');
            $debitTransation = $request->get('debitSubscriptionTransation');
            $ip = $request->ip();
            $refundStatus = [];

            if($debitOption == 4 || $debitOption == 11){ //4=refund & 11=void payment
                $refundStatus = $this->refundChargeBack($businessCampaignId, $businessCampaignType, $businessCampaignCharge, $debitOption, $debitTransation, $userId, $ip, 1);
                // echo "<pre>response= ";print_r($refundStatus);die;
            }

            if(count($refundStatus) > 0) {
                $status = $refundStatus['status'];
                $error = $refundStatus['error'];
                $message = ($status == 1 ) ? 'Success' : "Failure";
            }
        }
        catch(Exception $e){
            $message = $e->getMessage();
        }
        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => $error
        ];
        return response()->json($responseData);
    }

    public function otherDebit($businessCampaignId, $businessCampaignType, $businessCampaignCharge, $debitOption, $userId, $ip="")  {
        if ($businessCampaignType == 1) {
            //fetch total balance from business table
            $businessId = $businessCampaignId;
            $totalBalance = Business::where('business_id', $businessId)->get(['credit_available']);
            $beforeCharge = $totalBalance[0]->credit_available;
            $amountCharge = $businessCampaignCharge;
            $afterCharge = $beforeCharge - $amountCharge;

            BusinessCampaignPayment::create([
                'business_id' => $businessId,
                'balance_before_charge' => $beforeCharge,
                'amount_charge' => $amountCharge,
                'balance_after_charge' => $afterCharge,
                'charge_type_id' => $debitOption,
                'charge_method_type' => 'snapituser',
                'charge_user_id' => $userId,
                'charge_method' => 'other',
                'fatt_payment_id' => NULL,
                'is_charge_approved' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'ip' => $ip
            ])->business_campaign_payment_id;

            //update balance in business table
            Business::where('business_id', $businessId)
                ->update([
                    'credit_available' => $afterCharge
                ]);
        } else {
            //fetch total balance from campaign table
            $campaignId = $businessCampaignId;
            $totalBalance = Campaign::where('campaign_id', $campaignId)->get(['credit_available']);
            $beforeCharge = $totalBalance[0]->credit_available;
            $amountCharge = $businessCampaignCharge;
            $afterCharge = $beforeCharge - $amountCharge;

            BusinessCampaignPayment::create([
                'campaign_id' => $campaignId,
                'balance_before_charge' => $beforeCharge,
                'amount_charge' => $amountCharge,
                'balance_after_charge' => $afterCharge,
                'charge_type_id' => $debitOption,
                'charge_method_type' => 'snapituser',
                'charge_user_id' => $userId,
                'charge_method' => 'other',
                'fatt_payment_id' => NULL,
                'is_charge_approved' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'ip' => $ip
            ])->business_campaign_payment_id;

            //update balance in campaign table
            Campaign::where('campaign_id', $campaignId)
                ->update([
                    'credit_available' => $afterCharge
                ]);
        }
    }

    public function refundChargeBack($businessCampaignId, $businessCampaignType, $businessCampaignCharge, $debitOption, $debitTransation, $userId, $ip, $subscription=0) {
        $refundStatus = 2;
        $errormessage = null;
        $paymentDetail = BusinessCampaignPayment::where('business_campaign_payment_id', $debitTransation)->first();
        if (isset($debitOption) && $paymentDetail->fatt_payment_id !=''){
            $token = Helper::checkServer()['omni_token'];
            $omni_baseurl = Helper::checkServer()['omni_baseurl'];
            // $url =  $omni_baseurl . 'transaction/'.$paymentDetail->fatt_payment_id.'/refund';

            $route = 'transaction/'.$paymentDetail->fatt_payment_id.'/refund';
            $url =  $omni_baseurl . 'transaction/'.$paymentDetail->fatt_payment_id.'/refund';
            $requestType = 'POST';
            $body = array(
                'total' => $businessCampaignCharge,
            );
            if( $debitOption == 11  || $debitOption == "11" ){
                $route = 'transaction/'.$paymentDetail->fatt_payment_id.'/void';
                $url =  $omni_baseurl . 'transaction/'.$paymentDetail->fatt_payment_id.'/void';
                $requestType = 'POST';
                $body = array(
                    'total' => $paymentDetail->amount_charge + $paymentDetail->convenience_fee,
                );
                $businessCampaignCharge =  $paymentDetail->amount_charge;
            }
            $endDecObj = new CommonFunctions;
            // $response = $endDecObj->guzzle_api_call($omni_baseurl, $token, $route, $requestType, $body);
            $response = $endDecObj->curlapicall($url, $token, $requestType, $body);
            $response = json_decode($response, true);
            // echo "<pre>response= ";print_r($response);die;
            // if(isset($response->success) && $response->success){
            //     $refundStatus = $response->success;
            // }

            if (array_key_exists("total",$response)){
                // echo "Hello 1";
                if (is_array($response['total'])){
                    if(count($response['total']) > 0){
                        $errormessage = $response['total'][0];
                    }
                }
            }else if (array_key_exists("type",$response)){
                // echo "Hello 2";
                if (is_array($response['type'])){
                    if(count($response['type']) > 0){
                        $errormessage = $response['type'][0];
                    }
                }
            }else if (array_key_exists("error",$response)){
                // echo "Hello 3";
                if (is_array($response['error'])){
                    if(count($response['error']) > 0){
                        $errormessage = $response['error'][0];
                    }
                }
            }
            // echo "<pre>response= ";print_r($errormessage);die;
            if (array_key_exists("success",$response)){
                // echo "Yes";
                $refundStatus = $response['success'];
            }

        }
        if ($businessCampaignType == 1) {
            //fetch total balance from business table
            $businessId = $businessCampaignId;
            $totalBalance = Business::where('business_id', $businessId)->get(['credit_available']);
            $beforeCharge = $totalBalance[0]->credit_available;
            $amountCharge = $businessCampaignCharge;
            // $afterCharge = $beforeCharge - $amountCharge;
            $afterCharge = ($refundStatus == 1) ? $beforeCharge - $amountCharge :  $beforeCharge;
            $chargeMethodType = 'snapituser';
            if ($subscription > 0) {
                $beforeCharge = $afterCharge = 0;
                $chargeMethodType = 'subscription';
            }

            BusinessCampaignPayment::create([
                'business_id' => $businessId,
                'balance_before_charge' => $beforeCharge,
                'amount_charge' => $amountCharge,
                'balance_after_charge' => $afterCharge,
                'charge_type_id' => $debitOption,
                'charge_method_type' => $chargeMethodType,
                'charge_user_id' => $userId,
                'charge_method' => 'other',
                'fatt_payment_id' => $paymentDetail->fatt_payment_id,
                'is_charge_approved' => ($refundStatus == 1) ? 'yes' : 'no',
                'created_at' => date('Y-m-d H:i:s'),
                'ip' => $ip
            ])->business_campaign_payment_id;
            if( $refundStatus == 1 ) {
                //update balance in business table
                Business::where('business_id', $businessId)
                    ->update([
                        'credit_available' => $afterCharge
                    ]);
            }
        } else {
            //fetch total balance from campaign table
            $campaignId = $businessCampaignId;
            $totalBalance = Campaign::where('campaign_id', $campaignId)->get(['credit_available']);
            $beforeCharge = $totalBalance[0]->credit_available;
            $amountCharge = $businessCampaignCharge;
            $afterCharge = ($refundStatus == 1) ? $beforeCharge - $amountCharge :  $beforeCharge;

            BusinessCampaignPayment::create([
                'campaign_id' => $campaignId,
                'balance_before_charge' => $beforeCharge,
                'amount_charge' => $amountCharge,
                'balance_after_charge' => $afterCharge,
                'charge_type_id' => $debitOption,
                'charge_method_type' => 'snapituser',
                'charge_user_id' => $userId,
                'charge_method' => 'other',
                'fatt_payment_id' => $paymentDetail->fatt_payment_id,
                'is_charge_approved' => ($refundStatus == 1) ? 'yes' : 'no',
                'created_at' => date('Y-m-d H:i:s'),
                'ip' => $ip
            ])->business_campaign_payment_id;
            if( $refundStatus == 1) {
                //update balance in campaign table
                Campaign::where('campaign_id', $campaignId)
                    ->update([
                        'credit_available' => $afterCharge
                    ]);
            }
        }
        // echo "<pre>response= ";print_r($refundStatus);die;
        $responseData = array('status' =>$refundStatus, 'error' =>$errormessage);
        return $responseData;
    }

    public function transferFrom($businessCampaignId, $businessCampaignType, $businessCampaignCharge, $debitOption, $transactionBusiness, $transactionBusinessHiddenType, $userId, $ip) {
        if ($businessCampaignType == 1) { // Transfer from business
            //fetch total balance from business table
            $businessId = $businessCampaignId;
            $totalBalance = Business::where('business_id', $businessId)->get(['credit_available']);
            $beforeCharge = $totalBalance[0]->credit_available;
            $amountCharge = $businessCampaignCharge;
            $afterCharge = $beforeCharge - $amountCharge;

            BusinessCampaignPayment::create([
                'business_id' => $businessId,
                'balance_before_charge' => $beforeCharge,
                'amount_charge' => $amountCharge,
                'balance_after_charge' => $afterCharge,
                'charge_type_id' => $debitOption,
                'charge_method_type' => 'snapituser',
                'charge_user_id' => $userId,
                'charge_method' => 'other',
                'fatt_payment_id' => NULL,
                'is_charge_approved' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'ip' => $ip
            ])->business_campaign_payment_id;

            //update balance in business table
            Business::where('business_id', $businessId)
                ->update([
                    'credit_available' => $afterCharge
                ]);
        } else {
            //fetch total balance from campaign table
            $campaignId = $businessCampaignId;
            $totalBalance = Campaign::where('campaign_id', $campaignId)->get(['credit_available']);
            $beforeCharge = $totalBalance[0]->credit_available;
            $amountCharge = $businessCampaignCharge;
            $afterCharge = $beforeCharge - $amountCharge;

            BusinessCampaignPayment::create([
                'campaign_id' => $campaignId,
                'balance_before_charge' => $beforeCharge,
                'amount_charge' => $amountCharge,
                'balance_after_charge' => $afterCharge,
                'charge_type_id' => $debitOption,
                'charge_method_type' => 'snapituser',
                'charge_user_id' => $userId,
                'charge_method' => 'other',
                'fatt_payment_id' => NULL,
                'is_charge_approved' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'ip' => $ip
            ])->business_campaign_payment_id;

            //update balance in campaign table
            Campaign::where('campaign_id', $campaignId)
                ->update([
                    'credit_available' => $afterCharge
                ]);
        }
        //transfer to business
        if ($transactionBusinessHiddenType == 'B') {
            //fetch total balance from business table
            $businessId = $transactionBusiness;
            $totalBalance = Business::where('business_id', $businessId)->get(['credit_available']);
            $beforeCharge = $totalBalance[0]->credit_available;
            $amountCharge = $businessCampaignCharge;
            $afterCharge = $beforeCharge + $amountCharge;

            BusinessCampaignPayment::create([
                'business_id' => $businessId,
                'balance_before_charge' => $beforeCharge,
                'amount_charge' => $amountCharge,
                'balance_after_charge' => $afterCharge,
                'charge_type_id' => 10,
                'charge_method_type' => 'snapituser',
                'charge_user_id' => $userId,
                'charge_method' => 'other',
                'fatt_payment_id' => NULL,
                'is_charge_approved' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'ip' => $ip
            ])->business_campaign_payment_id;

            //update balance in business table
            Business::where('business_id', $businessId)
                ->update([
                    'credit_available' => $afterCharge
                ]);
        } else {
            //fetch total balance from campaign table
            $campaignId = $transactionBusiness;
            $totalBalance = Campaign::where('campaign_id', $campaignId)->get(['credit_available']);
            $beforeCharge = $totalBalance[0]->credit_available;
            $amountCharge = $businessCampaignCharge;
            $afterCharge = $beforeCharge + $amountCharge;

            BusinessCampaignPayment::create([
                'campaign_id' => $campaignId,
                'balance_before_charge' => $beforeCharge,
                'amount_charge' => $amountCharge,
                'balance_after_charge' => $afterCharge,
                'charge_type_id' => 10,
                'charge_method_type' => 'snapituser',
                'charge_user_id' => $userId,
                'charge_method' => 'other',
                'fatt_payment_id' => NULL,
                'is_charge_approved' => 'yes',
                'created_at' => date('Y-m-d H:i:s'),
                'ip' => $ip
            ])->business_campaign_payment_id;

            //update balance in campaign table
            Campaign::where('campaign_id', $campaignId)
                ->update([
                    'credit_available' => $afterCharge
                ]);
        }
    }

    public function insertCardRecurrent(Request $request) {
        $error = "";
        $status = 0;
        $message = 'Failure';
        $logArr = $daysArr = [];

        $validator = Validator::make($request->all(),[
            'lastFour' => 'required',
            'status' => 'required',
            'type' => "required",
            'charge'=>'required|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json(array(
                'success' => 0,
                'message' => 'There are incorect values in the form!',
                'error' => $validator->getMessageBag()->toArray()
            ), 200);
            $this->throwValidationException(
                $request, $validator
            );
        }

        try {
            $businessCampaignId = $request->get('businessCampaignId');
            $businessCampaignType = $request->get('businessCampaignType');
            $businessCampaignCharge = $request->get('charge');
            $lastFour = $request->get('lastFour');
            $lastFourText = $request->get('lastFourText');
            $status = $request->get('status');
            $type = $request->get('type');
            $time = $request->get('time');
            $day = $request->get('day');
            $threshold = $request->get('threshold');

            if ($type == 1) {
                if (empty($time)) {
                    throw new Exception('Select time and day required');
                }
                if (empty($day)) {
                    throw new Exception('Select time and day required');
                }
            } else {
                if (empty($threshold)) {
                    throw new Exception('Threshold amount required');
                }
            }

            $businessId = $businessCampaignId;
            $campaignId = 0;
            if ($businessCampaignType == 2) {
                $campaignDetail = Campaign::where('campaign_id', $businessCampaignId)->get(['campaign_id', 'business_id', 'campaign_name']);
                $businessDetail = Business::where('business_id', $campaignDetail[0]->business_id)->first();
                $businessId = 0;
                $campaignId = $campaignDetail[0]->campaign_id;
                $businessCampaignName = $campaignDetail[0]->campaign_name;
            } else {
                $businessDetail = Business::where('business_id', $businessId)->first();
                $businessCampaignName = $businessDetail->display_name;
            }

            $cardNumber = BusinessCC::where('business_cc_id', $lastFour)->first();
            $userId = Auth::user()->id;

            //store data in business_campaign_recuring_payment table
            $recurringPaymentId = BusinessCampaignRecuringPayment::create([
                'business_id' => $businessId,
                'campaign_id' => $campaignId,
                'business_cc_id' => $lastFour,
                'recurring_type' => $type,
                'amount' => $businessCampaignCharge,
                'threshold_amount' => $threshold,
                'created_user_id' => $userId,
                'is_active' => ($status > 0) ? 'yes' : 'no',
                'created_at' => date('Y-m-d H:i:s')
            ])->business_campaign_recurring_payment_id;

            $daysName = [ 1 => 'Monday', 2 => 'Tuesday', 3 => 'Wednesday', 4 => 'Thursday', 5 => 'Friday', 6 => 'Saturday', 7 => 'Sunday'];
            if ($type == 1) {
                $dailyData = [];
                foreach ($day as $value) {
                    $dailyData[] = [
                        'business_campaign_recurring_payment_id' => $recurringPaymentId,
                        'day' => $value,
                        'payment_time' => $time,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    if (array_key_exists($value,$daysName)){
                        $daysArr[] = $daysName[$value];
                    }
                }
                BusinessCampaignRecurringPaymentDay::insert($dailyData);
            }

            if (isset($lastFour) && !empty($lastFour) && $lastFour != '') {
                $logArr[] = [
                    'created_user_id' => $userId,
                    'business_id' => $businessId,
                    'campaign_id' => $campaignId,
                    'field_name' => 'recurring_card',
                    'new_value' => $lastFourText,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if ( $status != '--') {
                $logArr[] = [
                    'created_user_id' => $userId,
                    'business_id' => $businessId,
                    'campaign_id' => $campaignId,
                    'field_name' => 'recurring_status',
                    'new_value' => ($status == 1 || $status == "1") ? "active" : "inactive",
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if ( $type != '--') {
                $logArr[] = [
                    'created_user_id' => $userId,
                    'business_id' => $businessId,
                    'campaign_id' => $campaignId,
                    'field_name' => 'recurring_type',
                    'new_value' => ($type == 1 || $type == "1") ? "daily" : "amount",
                    'created_at' => date('Y-m-d H:i:s')
                ];
                if ($type == 1) {
                    if ( $time != '--') {
                        $logArr[] = [
                            'created_user_id' => $userId,
                            'business_id' => $businessId,
                            'campaign_id' => $campaignId,
                            'field_name' => 'recurring_time',
                            'new_value' => $time,
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                    }
                    if (isset($day) && count($day) > 0) {

                        $logArr[] = [
                            'created_user_id' => $userId,
                            'business_id' => $businessId,
                            'campaign_id' => $campaignId,
                            'field_name' => 'recurring_day',
                            'new_value' => implode(",",$daysArr),
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                    }
                }else{
                    if (isset($threshold) && !empty($threshold) && $threshold != '') {
                        $logArr[] = [
                            'created_user_id' => $userId,
                            'business_id' => $businessId,
                            'campaign_id' => $campaignId,
                            'field_name' => 'recurring_threshold_amount',
                            'new_value' => $threshold,
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                    }
                }
            }
            if (isset($businessCampaignCharge) && !empty($businessCampaignCharge) && $businessCampaignCharge != '') {
                $logArr[] = [
                    'created_user_id' => $userId,
                    'business_id' => $businessId,
                    'campaign_id' => $campaignId,
                    'field_name' => 'recurring_amount',
                    'new_value' => $businessCampaignCharge,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            $finalLogData = [];
            if(count($logArr) > 0 ){
                if ($businessCampaignType == 2) {
                    foreach ($logArr as $logdata) {
                        unset($logdata['business_id']);
                        $finalLogData[] = $logdata;
                    }
                    CampaignUpdateLog::insert($finalLogData);
                }else{
                    foreach ($logArr as $logdata) {
                        unset($logdata['campaign_id']);
                        $finalLogData[] = $logdata;
                    }
                    BusinessUpdateLog::insert($finalLogData);
                }
            }

            //Added by BK on 05/02/2025 convenience fee email to evey success payment
            if ($type == 2 && $status > 0) {
                $this->amountBasedRecurrentEmailNotificationTemplate($businessId, $businessDetail->display_name, $businessCampaignName, $businessDetail->email, $cardNumber->card, $threshold);
            } else if ($type == 1 && $status > 0) {
                $this->dailyBasedRecurrentEmailNotificationTemplate($businessId, $businessDetail->display_name, $businessCampaignName, $businessDetail->email, $cardNumber->card, $daysArr);
            } else {
                $this->recurrentStopEmailNotificationTemplate($businessId, $businessDetail->display_name, $businessCampaignName, $businessDetail->email);
            }
            $status = 1;
            $message = 'Success';

        } catch(Exception $e) {
            $message = $e->getMessage();
        }

        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => array('code'=>"System Error", 'message' => $message)
        ];
        return response()->json($responseData);
    }

    public function editCardRecurrent(Request $request) {
        $error = "";
        $status_code = 0;
        $message = 'Failure';

        $logArr = $daysArr = $olddaysArr = [];
        $validator = Validator::make($request->all(),[
            'lastFour' => 'required',
            'status' => 'required',
            'type' => "required",
            'charge'=>'required|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json(array(
                'success' => 0,
                'message' => 'There are incorect values in the form!',
                'error' => $validator->getMessageBag()->toArray()
            ), 200);
            $this->throwValidationException(
                $request, $validator
            );
        }

        try {
            $recurringPaymentId = $request->get('recurringPaymentId');
            $businessCampaignId = $request->get('businessCampaignId');
            $businessCampaignType = $request->get('businessCampaignType');
            $businessCampaignCharge = $request->get('charge');
            $lastFour = $request->get('lastFour');
            $lastFourText = $request->get('lastFourText');
            $status = $request->get('status');
            $type = $request->get('type');
            $time = $request->get('time');
            $day = $request->get('day');
            $threshold = $request->get('threshold');

            if ($businessCampaignCharge <= 0) {
                throw new Exception("Amount to Charge more than 0");
            }
            if ($type == 1) {
                if (empty($time)) {
                    throw new Exception('Select time and day required');
                }
                if (empty($day)) {
                    throw new Exception('Select time and day required');
                }
            } else {
                if (empty($threshold)) {
                    throw new Exception('Threshold amount required');
                }
            }

            $businessId = $businessCampaignId;
            $campaignId = 0;
            if ($businessCampaignType == 2) {
                $campaignDetail = Campaign::where('campaign_id', $businessCampaignId)->get(['campaign_id', 'business_id', 'campaign_name']);
                $businessDetail = Business::where('business_id', $campaignDetail[0]->business_id)->first();
                $businessId = 0;
                $campaignId = $campaignDetail[0]->campaign_id;
                $businessCampaignName = $campaignDetail[0]->campaign_name;
            } else {
                $businessDetail = Business::where('business_id', $businessId)->first();
                $businessCampaignName = $businessDetail->display_name;
            }

            $businessCampaignRecuringPaymentData = BusinessCampaignRecuringPayment::with(['businesscampaignrecurringpaymentdayinfo','businesscc'])->where('business_campaign_recurring_payment_id', $recurringPaymentId)->first()->toArray();
            $cardNumber = BusinessCC::where('business_cc_id', $lastFour)->first();
            $userId = Auth::user()->id;
            //remove business_campaign_recurring_payment_day record
            BusinessCampaignRecurringPaymentDay::where('business_campaign_recurring_payment_id', $recurringPaymentId)->delete();

            //store data in business_campaign_recuring_payment table
            BusinessCampaignRecuringPayment::where('business_campaign_recurring_payment_id', $recurringPaymentId)
                ->update([
                    'business_id' => $businessId,
                    'campaign_id' => $campaignId,
                    'business_cc_id' => $lastFour,
                    'recurring_type' => $type,
                    'amount' => $businessCampaignCharge,
                    'threshold_amount' => ($type == 2) ? $threshold : 0,
                    'updated_user_id' => $userId,
                    'is_active' => ($status > 0) ? 'yes' : 'no',
                    'updated_at' => date('Y-m-d H:i:s')
            ]);

            $daysName = [ 1 => 'Monday', 2 => 'Tuesday', 3 => 'Wednesday', 4 => 'Thursday', 5 => 'Friday', 6 => 'Saturday', 7 => 'Sunday'];
            if ($type == 1) {
                $dailyData = [];
                foreach ($day as $value) {
                    $dailyData[] = [
                        'business_campaign_recurring_payment_id' => $recurringPaymentId,
                        'day' => $value,
                        'payment_time' => $time,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    if (array_key_exists($value,$daysName)){
                        $daysArr[] = $daysName[$value];
                    }
                }
                BusinessCampaignRecurringPaymentDay::insert($dailyData);
            }

            $oldcard = $businessCampaignRecuringPaymentData['businesscc']['card'];
            if (!empty($lastFourText) && $oldcard != $lastFourText) {
                $logArr[] = [
                    'created_user_id' => $userId,
                    'business_id' => $businessId,
                    'campaign_id' => $campaignId,
                    'field_name' => 'recurring_card',
                    'old_value' => $oldcard,
                    'new_value' => $lastFourText,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            $oldstatus = ( $businessCampaignRecuringPaymentData['is_active'] == "yes" ) ? 1 : 0;
            if ( $status != '--' && $oldstatus != $status ) {
                $logArr[] = [
                    'created_user_id' => $userId,
                    'business_id' => $businessId,
                    'campaign_id' => $campaignId,
                    'field_name' => 'recurring_status',
                    'old_value' => ($oldstatus == 1 || $oldstatus == "1") ? "active" : "inactive",
                    'new_value' => ($status == 1 || $status == "1") ? "active" : "inactive",
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            $oldtype = ( $businessCampaignRecuringPaymentData['recurring_type'] == "daily" ) ? 1 : 2;
            $oldthreshold_amount = $businessCampaignRecuringPaymentData['threshold_amount'];
            if ( $type != '--') {
                if ( $oldtype != $type ) {
                    $logArr[] = [
                        'created_user_id' => $userId,
                        'business_id' => $businessId,
                        'campaign_id' => $campaignId,
                        'field_name' => 'recurring_type',
                        'old_value' => ( $oldtype == 1 || $oldtype == "1" ) ? "daily" : "amount",
                        'new_value' => ( $type == 1 || $type == "1") ? "daily" : "amount",
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                $daysInfo = $businessCampaignRecuringPaymentData['businesscampaignrecurringpaymentdayinfo'];
                if ($type == 1) {
                    if ( $time != '--') {
                        if(count($daysInfo) > 0 ){
                            $oldtime = $daysInfo[0]['payment_time'];
                            if ( $oldtime != $time ) {
                                $logArr[] = [
                                    'created_user_id' => $userId,
                                    'business_id' => $businessId,
                                    'campaign_id' => $campaignId,
                                    'field_name' => 'recurring_time',
                                    'old_value' => $oldtime,
                                    'new_value' => $time,
                                    'created_at' => date('Y-m-d H:i:s')
                                ];
                            }
                        }else{
                            $logArr[] = [
                                'created_user_id' => $userId,
                                'business_id' => $businessId,
                                'campaign_id' => $campaignId,
                                'field_name' => 'recurring_time',
                                'old_value' => null,
                                'new_value' => $time,
                                'created_at' => date('Y-m-d H:i:s')
                            ];
                        }
                    }
                    if (isset($day) && count($day) > 0) {
                        if(count($daysInfo) > 0 ){
                            $olddays = $daysInfo;
                            $olddaysNumArr = [];
                            foreach ($olddays as $key => $value) {
                                $olddaysNumArr[] = $value['day'];
                            }
                            foreach ($olddaysNumArr as $key => $value) {
                                if (array_key_exists($value,$daysName)){
                                    $olddaysArr[] = $daysName[$value];
                                }
                            }
                            if ( implode(",",$olddaysArr) != implode(",",$daysArr)) {
                                $logArr[] = [
                                    'created_user_id' => $userId,
                                    'business_id' => $businessId,
                                    'campaign_id' => $campaignId,
                                    'field_name' => 'recurring_day',
                                    'old_value' => implode(",",$olddaysArr),
                                    'new_value' => implode(",",$daysArr),
                                    'created_at' => date('Y-m-d H:i:s')
                                ];
                            }
                        } else{
                            $logArr[] = [
                                'created_user_id' => $userId,
                                'business_id' => $businessId,
                                'campaign_id' => $campaignId,
                                'field_name' => 'recurring_day',
                                'old_value' => null,
                                'new_value' => implode(",",$daysArr),
                                'created_at' => date('Y-m-d H:i:s')
                            ];
                        }
                    }
                }else{
                    if (isset($threshold) && $oldthreshold_amount != $threshold) {
                        $logArr[] = [
                            'created_user_id' => $userId,
                            'business_id' => $businessId,
                            'campaign_id' => $campaignId,
                            'field_name' => 'recurring_threshold_amount',
                            'old_value' => $oldthreshold_amount,
                            'new_value' => $threshold,
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                    }
                }
            }

            $oldbusinessCampaignCharge = $businessCampaignRecuringPaymentData['amount'];
            if (isset($businessCampaignCharge) && $oldbusinessCampaignCharge != $businessCampaignCharge) {
                $logArr[] = [
                    'created_user_id' => $userId,
                    'business_id' => $businessId,
                    'campaign_id' => $campaignId,
                    'field_name' => 'recurring_amount',
                    'old_value' => $oldbusinessCampaignCharge,
                    'new_value' => $businessCampaignCharge,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            $finalLogData = [];

            if(count($logArr) > 0 ){
                if ($businessCampaignType == 2) {
                    foreach ($logArr as $logdata) {
                        unset($logdata['business_id']);
                        $finalLogData[] = $logdata;
                    }
                    CampaignUpdateLog::insert($finalLogData);
                }else{

                    foreach ($logArr as $logdata) {
                        unset($logdata['campaign_id']);
                        $finalLogData[] = $logdata;
                    }

                    BusinessUpdateLog::insert($finalLogData);
                }
            }

            //Added by BK on 05/02/2025 convenience fee email to evey success payment
            if ($type == 2 && $status > 0) {
                $this->amountBasedRecurrentEmailNotificationTemplate($businessId, $businessDetail->display_name, $businessCampaignName, $businessDetail->email, $cardNumber->card, $threshold);
            } else if ($type == 1 && $status > 0) {
                $this->dailyBasedRecurrentEmailNotificationTemplate($businessId, $businessDetail->display_name, $businessCampaignName, $businessDetail->email, $cardNumber->card, $daysArr);
            } else {
                $this->recurrentStopEmailNotificationTemplate($businessId, $businessDetail->display_name, $businessCampaignName, $businessDetail->email);
            }
            $status_code = 1;
            $message = 'Success';

        } catch(Exception $e) {
            $message = $e->getMessage();
        }

        $responseData = [
            'status' => $status_code,
            'message' => $message,
            'error' => array('code'=>"System Error", 'message' => $message)
        ];
        return response()->json($responseData);
    }

    public function sendContractAsEmail(Request $request) {
        DB::table('dev_debug')->insert(['sr_no' => 50, 'result' => "sendContractAsEmail start=" . json_encode($request->all(), true),'created_at' => date('Y-m-d H:i:s')]);
        $status = 0;
        $message = 'fail';
        $error = array();
        try{
            DB::table('dev_debug')->insert(['sr_no' => 50, 'result' => "sendContractAsEmail try=" . json_encode($request->all(), true),'created_at' => date('Y-m-d H:i:s')]);
            $rules = array(
                'payment_amount' => 'required|numeric',
                'future_recurrent_amount' => 'nullable|numeric',
                'ccemail' => 'required|email|max:50|regex:/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix',
            );
            $msgs = array(
                'payment_amount.required' => 'The payment amount field is required.',
                'payment_amount.numeric' => 'The payment amount field should be number only.',
                'future_recurrent_amount.numeric' => 'The recurrent amount field should be number only.',
                'ccemail.required' => 'The email field is required.',
                'ccemail.email' => 'The email must be a valid email address.',
                'ccemail.max' => 'The email must not be greater than 50 characters.',
                'ccemail.regex' => 'The email format is invalid.'
            );
            $input = $request->only(['payment_amount', 'future_recurrent_amount', "ccemail"]);
            $validator = Validator::make($input, $rules, $msgs );
            if ($validator->fails()) {
                $error =  $validator->getMessageBag()->toArray();
            } else {
                $payment_amount = $request['payment_amount'];
                $future_recurrent_amount = $request['future_recurrent_amount'];
                $payment_recurrent_type = $request['payment_recurrent_type'];
                $ccemail = $request['ccemail'];
                $encbusinessid = $request['businessid'];
                $endDecObj = new CommonFunctions;
                $businessid = $endDecObj->customeDecryption($encbusinessid);
                DB::table('dev_debug')->insert(['sr_no' => 50, 'result' => "sendContractAsEmail businessid=".$businessid,'created_at' => date('Y-m-d H:i:s')]);
                if($businessid > 0 ){
                    $businessCCContractData = [
                        "business_id" => $businessid,
                        "payment_amount" => $payment_amount,
                        "recurrent_amount" => $future_recurrent_amount,
                        "recurrent_type" => $payment_recurrent_type,
                        "email" => $ccemail,
                        "status" => 'sent',
                        "created_at" => date('Y-m-d H:i:s')
                    ];
                    $business_cc_contract_id = BusinessCCContract::create( $businessCCContractData )->business_cc_contract_id;
                    DB::table('dev_debug')->insert(['sr_no' => 50, 'result' => "sendContractAsEmail new business_cc_contract_id=".$business_cc_contract_id,'created_at' => date('Y-m-d H:i:s')]);
                    $businessDetail = Business::where('business_id', $businessid)->first();

                    if($business_cc_contract_id ){
                        $emaildata = [];
                        $emaildata['from'] = '<EMAIL>';
                        $emaildata['to'] = $ccemail;
                        $emaildata['subject'] = "Prime Credit Card Authorization: " . ucfirst($businessDetail->business_name);
                        $emaildata['business_cc_contract_id'] = $business_cc_contract_id;
                        $emaildata['business_name'] = $businessDetail->business_name;
                        $businesscontractsendEmail = new CardContractSendEmail();
                        $emailresponse = $businesscontractsendEmail->emailCardContractSend($emaildata );
                        DB::table('dev_debug')->insert(['sr_no' => 50, 'result' => "sendContractAsEmail emailresponse=". json_encode($emailresponse, true),'created_at' => date('Y-m-d H:i:s')]);
                    }
                }
                $status = 1;
                $message = 'success';
            }
        } catch(Exception $e) {
            $message = $e->getMessage();
            DB::table('dev_debug')->insert(['sr_no' => 50, 'result' => "sendContractAsEmail try=" . json_encode($message, true),'created_at' => date('Y-m-d H:i:s')]);
        }
        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => $error
        ];
        DB::table('dev_debug')->insert(['sr_no' => 50, 'result' => "sendContractAsEmail try=" . json_encode($responseData, true),'created_at' => date('Y-m-d H:i:s')]);
        return response()->json($responseData);
    }

    public function addCardEmailNotificationTemplate($businessId, $businessName, $businessEmail, $cardNumber) {
        $mail           = new TemplateEmail();
        $subject        = "New card added to your Prime Marketing account";
        $setFrom        = '<EMAIL>';
        //$setTo        = '<EMAIL>';
        $postmarkToken  = '************************************';
        $setTo          = $businessEmail;

        $mail->setTo($setTo);
        $mail->setBcc('<EMAIL>');
        $mail->setFrom($setFrom);
        $mail->setSubject($subject);

        $pageContent    = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
        <head>
            <title>Prime Marketing</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
            <style type="text/css">
                *{
                    margin: 0;
                    padding: 0;
                    font-family: Tahoma, Arial, Helvetica, sans-serif;
                    color: #444;
                }
            </style>
        </head>
        <body style="background-color: #F0F0F0;">
        <table style="width: 100%; max-width: 600px; min-width: 600px; margin: 0 auto; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
            <tr>
                <td style="background: #F0F0F0; padding: 4px 32px;">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="width: 99px;"><a href="https://primemarketing.us/" style="display: inline-block; width: 99px; height: 35px;"><img src="https://mover.primemarketing.us/dist/img/email_setup/logo.png" width="99" height="35"></a></td>
                            <td>&nbsp;</td>
                            <td style="width: 184px; text-align: right;">
                                <a class="call" href="tel:+***********" style="
                                   padding: 10px 0;
                                   display: inline-block;
                                   font-size: 18px;
                                   color: #151B26;
                                   font-weight: bold;
                                   text-decoration: none;
                                   cursor: pointer;
                                   border-radius: 5px;
                                   width: 184px;
                                   ">
                                    <table style="display: inline-block; ">
                                        <tbody><tr>
                                            <td style="padding-top: 8px; height: 30px;"><a href="tel:+***********"><img src="https://mover.primemarketing.us/dist/img/email_setup/call.png" width="30" height="28" style="display: inline-block;"></a></td>
                                            <td style="font-size: 18px;"><a href="tel:+***********" style="color: #151B26; text-decoration: none;">(*************</a></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background: #F0F0F0 url(https://mover.primemarketing.us/dist/img/email_setup/bg.png) no-repeat 0 0; width: 100%; padding: 0 30px; ">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="text-align: center; padding:24px; font-size: 16px; color: #000; font-weight: bold;">
                                Hello <b style="color: #000; text-transform: uppercase;">'.ucwords($businessName).',</b>
                            </td>
                        </tr>
                        <tr>
                            <td style="background-color: #ffffff; padding:30px 10px; border-radius: 10px; ">
                                <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                    <tr>
                                        <td style="text-align: center; color: #000; font-size: 18px; line-height: 26px;">
                                            We are writing to inform you that your <b style="color: #000;">new card<br> ending with '.sprintf("%04d", $cardNumber).'</b> has been <b style="color: #000;">successfully added</b> to <br>your Prime Marketing account on <b style="color: #000;">'.date('m/d/Y').'</b>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="height: 40px;">&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="font-size: 14px; text-align: center; color: #000; line-height: 20px;">
                                            Best Regards <br>
                                            <b style="color: #000;">Prime Marketing</b>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="height: 30px;">&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align: center;">
                                            <img src="https://mover.primemarketing.us/dist/img/email_setup/add-card.png" width="237" height="176" style="display: inline-block;">
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background-color: #F0F0F0; text-align: center; padding: 15px; font-size: 12px; color: #000; line-height: 17px;">
                    If you did not authorize this change, <br>
                    please contact customer support immediately at <a href="tel:+***********" style="color: #000; text-decoration: none;">(*************</a>
                </td>
            </tr>
            <tr>
                <td style="background-color: #ffffff; font-size: 12px; text-align: center; padding: 12px; color: #000000;">For support email us at <a href="mailto:<EMAIL>" style="color: #000000; font-weight: bold; text-decoration: none;"><EMAIL></a></td>
            </tr>
            <tr>
                <td style="background: #CE0941; padding: 13px;  text-align: center; font-size: 14px; font-family: Tahoma, arial; font-weight: normal; color: #ffffff;">
                    © ' . date('Y') .' - Prime Marketing. All rights reserved.
                </td>
            </tr>
        </table>
        </body>
        </html>';
        $mail->setHtmlbody($pageContent);
        $response = $mail->send_email($token = $postmarkToken, 'add-card');

        EmailLog::create([
            "business_id" => $businessId,
            "subject" => $subject,
            "set_to" => $setTo,
            "set_from" => $setFrom,
            "response" => json_encode($response->original['data']),
            "type" => 'add-card',
            'created_at' => date("Y-m-d H:i:s")
        ]);
        return true;
    }

    public function setPrimaryCardEmailNotificationTemplate($businessId, $businessName, $businessEmail, $cardNumber) {
        $mail           = new TemplateEmail();
        $subject        = "Card ending ".sprintf("%04d", $cardNumber)." is now set as primary at Prime Marketing";
        $setFrom        = '<EMAIL>';
        //$setTo        = '<EMAIL>';
        $postmarkToken  = '************************************';
        $setTo          = $businessEmail;

        $mail->setTo($setTo);
        $mail->setBcc('<EMAIL>');
        $mail->setFrom($setFrom);
        $mail->setSubject($subject);

        $pageContent    = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
        <head>
            <title>Prime Marketing</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
            <style type="text/css">
                *{
                    margin: 0;
                    padding: 0;
                    font-family: Tahoma, Arial, Helvetica, sans-serif;
                    color: #444;
                }
            </style>
        </head>
        <body style="background-color: #F0F0F0;">
        <table style="width: 100%; max-width: 600px; min-width: 600px; margin: 0 auto; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
            <tr>
                <td style="background: #F0F0F0; padding: 4px 32px;">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="width: 99px;"><a href="https://primemarketing.us/" style="display: inline-block; width: 99px; height: 35px;"><img src="https://mover.primemarketing.us/dist/img/email_setup/logo.png" width="99" height="35"></a></td>
                            <td>&nbsp;</td>
                            <td style="width: 184px; text-align: right;">
                                <a class="call" href="tel:+***********" style="
                                   padding: 10px 0;
                                   display: inline-block;
                                   font-size: 18px;
                                   color: #151B26;
                                   font-weight: bold;
                                   text-decoration: none;
                                   cursor: pointer;
                                   border-radius: 5px;
                                   width: 184px;
                                   ">
                                    <table style="display: inline-block; ">
                                        <tbody><tr>
                                            <td style="padding-top: 8px; height: 30px;"><a href="tel:+***********"><img src="https://mover.primemarketing.us/dist/img/email_setup/call.png" width="30" height="28" style="display: inline-block;"></a></td>
                                            <td style="font-size: 18px;"><a href="tel:+***********" style="color: #151B26; text-decoration: none;">(*************</a></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background: #F0F0F0 url(https://mover.primemarketing.us/dist/img/email_setup/bg.png) no-repeat 0 0; width: 100%; padding: 0 30px; ">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="text-align: center; padding:24px; font-size: 16px; color: #000; font-weight: bold;">
                                Hello <b style="color: #000; text-transform: uppercase;">'.ucwords($businessName).',</b>
                            </td>
                        </tr>
                        <tr>
                            <td style="background-color: #ffffff; padding:30px 10px; border-radius: 10px; ">
                                <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                    <tr>
                                        <td style="text-align: center; color: #000; font-size: 18px; line-height: 26px;">
                                            We are writing to inform you that the following <br><b style="color: #000;">card (ending with '.sprintf("%04d", $cardNumber).')</b> has been <b style="color: #000;">set as the <br>primary </b>payment method on your Prime Marketing <br>account as of <b style="color: #000;">'.date('m/d/Y').'</b>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="height: 40px;">&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="font-size: 14px; text-align: center; color: #000; line-height: 20px;">
                                            Best Regards <br>
                                            <b style="color: #000;">Prime Marketing</b>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="height: 30px;">&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align: center;">
                                            <img src="https://mover.primemarketing.us/dist/img/email_setup/primary.png" width="237" height="164" style="display: inline-block;">
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background-color: #F0F0F0; text-align: center; padding: 15px; font-size: 12px; color: #000; line-height: 17px;">
                    If you did not authorize this change, <br>
                    please contact customer support immediately at <a href="tel:+***********" style="color: #000; text-decoration: none;">(*************</a>
                </td>
            </tr>
            <tr>
                <td style="background-color: #ffffff; font-size: 12px; text-align: center; padding: 12px; color: #000000;">For support email us at <a href="mailto:<EMAIL>" style="color: #000000; font-weight: bold; text-decoration: none;"><EMAIL></a></td>
            </tr>
            <tr>
                <td style="background: #CE0941; padding: 13px;  text-align: center; font-size: 14px; font-family: Tahoma, arial; font-weight: normal; color: #ffffff;">
                    © ' . date('Y') .' - Prime Marketing. All rights reserved.
                </td>
            </tr>
        </table>
        </body>
        </html>';
        $mail->setHtmlbody($pageContent);
        $response       = $mail->send_email($token = $postmarkToken, 'primary-card');

        EmailLog::create([
            "business_id" => $businessId,
            "subject" => $subject,
            "set_to" => $setTo,
            "set_from" => $setFrom,
            "response" => json_encode($response->original['data']),
            "type" => 'primary-card',
            'created_at' => date("Y-m-d H:i:s")
        ]);
        return true;
    }

    public function removeCardEmailNotificationTemplate($businessId, $businessName, $businessEmail, $cardNumber) {
        $mail           = new TemplateEmail();
        $subject        = "Card ending ".sprintf("%04d", $cardNumber)." removed from Prime Marketing account";
        $setFrom        = '<EMAIL>';
        //$setTo        = '<EMAIL>';
        $postmarkToken  = '************************************';
        $setTo          = $businessEmail;

        $mail->setTo($setTo);
        $mail->setBcc('<EMAIL>');
        $mail->setFrom($setFrom);
        $mail->setSubject($subject);

        $pageContent    = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
        <head>
            <title>Prime Marketing</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
            <style type="text/css">
                *{
                    margin: 0;
                    padding: 0;
                    font-family: Tahoma, Arial, Helvetica, sans-serif;
                    color: #444;
                }
            </style>
        </head>
        <body style="background-color: #F0F0F0;">
        <table style="width: 100%; max-width: 600px; min-width: 600px; margin: 0 auto; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
            <tr>
                <td style="background: #F0F0F0; padding: 4px 32px;">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="width: 99px;"><a href="https://primemarketing.us/" style="display: inline-block; width: 99px; height: 35px;"><img src="https://mover.primemarketing.us/dist/img/email_setup/logo.png" width="99" height="35"></a></td>
                            <td>&nbsp;</td>
                            <td style="width: 184px; text-align: right;">
                                <a class="call" href="tel:+***********" style="
                                   padding: 10px 0;
                                   display: inline-block;
                                   font-size: 18px;
                                   color: #151B26;
                                   font-weight: bold;
                                   text-decoration: none;
                                   cursor: pointer;
                                   border-radius: 5px;
                                   width: 184px;
                                   ">
                                    <table style="display: inline-block; ">
                                        <tbody><tr>
                                            <td style="padding-top: 8px; height: 30px;"><a href="tel:+***********"><img src="https://mover.primemarketing.us/dist/img/email_setup/call.png" width="30" height="28" style="display: inline-block;"></a></td>
                                            <td style="font-size: 18px;"><a href="tel:+***********" style="color: #151B26; text-decoration: none;">(*************</a></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background: #F0F0F0 url(https://mover.primemarketing.us/dist/img/email_setup/bg.png) no-repeat 0 0; width: 100%; padding: 0 30px; ">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="text-align: center; padding:24px; font-size: 16px; color: #000; font-weight: bold;">
                                Hello <b style="color: #000; text-transform: uppercase;">'.ucwords($businessName).',</b>
                            </td>
                        </tr>
                        <tr>
                            <td style="background-color: #ffffff; padding:30px 10px; border-radius: 10px; ">
                                <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                    <tr>
                                        <td style="text-align: center; color: #000; font-size: 18px; line-height: 26px;">
                                            We are writing to inform you that a <b style="color: #000;">card ending '.sprintf("%04d", $cardNumber).'</b> <br>has been <b style="color: #000;">successfully removed</b> from your Prime <br>Marketing as of '.date('m/d/Y').'
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="height: 40px;">&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="font-size: 14px; text-align: center; color: #000; line-height: 20px;">
                                            Best Regards <br>
                                            <b style="color: #000;">Prime Marketing</b>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="height: 30px;">&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align: center;">
                                            <img src="https://mover.primemarketing.us/dist/img/email_setup/remove-card.png" width="165" height="164" style="display: inline-block;">
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
        
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background-color: #F0F0F0; text-align: center; padding: 15px; font-size: 12px; color: #000; line-height: 17px;">
                    If you did not authorize this change, <br>
                    please contact customer support immediately at <a href="tel:+***********" style="color: #000; text-decoration: none;">(*************</a>
                </td>
            </tr>
            <tr>
                <td style="background-color: #ffffff; font-size: 12px; text-align: center; padding: 12px; color: #000000;">For support email us at <a href="mailto:<EMAIL>" style="color: #000000; font-weight: bold; text-decoration: none;"><EMAIL></a></td>
            </tr>
            <tr>
                <td style="background: #CE0941; padding: 13px;  text-align: center; font-size: 14px; font-family: Tahoma, arial; font-weight: normal; color: #ffffff;">
                    © ' . date('Y') .' - Prime Marketing. All rights reserved.
                </td>
            </tr>
        </table>
        </body>
        </html>';
        $mail->setHtmlbody($pageContent);
        $response       = $mail->send_email($token = $postmarkToken, 'remove-card');

        EmailLog::create([
            "business_id" => $businessId,
            "subject" => $subject,
            "set_to" => $setTo,
            "set_from" => $setFrom,
            "response" => json_encode($response->original['data']),
            "type" => 'remove-card',
            'created_at' => date("Y-m-d H:i:s")
        ]);
        return true;
    }

    public function moversRechargeEmailNotificationTemplate($businessId, $businessName, $businessCampaignName, $businessEmail, $cardNumber, $chargeAmount, $convenienceFee, $transactionId) {
        $mail           = new TemplateEmail();
        $subject        = "PM Account Charged Successfully: Transaction Summary Inside";
        $setFrom        = '<EMAIL>';
        //$setTo        = '<EMAIL>';
        $postmarkToken  = '************************************';
        $setTo          = $businessEmail;

        $mail->setTo($setTo);
        $mail->setBcc('<EMAIL>');
        $mail->setFrom($setFrom);
        $mail->setSubject($subject);

        $pageContent    = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
        <head>
            <title>Prime Marketing</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
            <style type="text/css">
                *{
                    margin: 0;
                    padding: 0;
                    font-family: Tahoma, Arial, Helvetica, sans-serif;
                    color: #444;
                }
                @media only screen and (max-width: 599px) {
                    .call {
                        font-size: 0 !important;
                        justify-content: flex-end !important;
                    }
                }
            </style>
        </head>
        <body style="background-color: #F0F0F0;">
        <table style="width: 100%; max-width: 600px; min-width: 600px; margin: 0 auto; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
            <tr>
                <td style="background: #F0F0F0; padding: 14px 32px;">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="width: 99px;"><a href="https://primemarketing.us" target="_blank"><img src="https://mover.primemarketing.us/dist/img/email_setup/logo.png" width="99" height="35"></a></td>
                            <td>&nbsp;</td>
                            <td style="width: 184px; text-align: right;">
                                <a class="call" href="tel:+***********"
                                   style="
                                   padding: 10px 0;
                                   display: inline-block;
                                   font-size: 18px;
                                   color: #151B26;
                                   font-weight: bold;
                                   text-decoration: none;
                                   cursor: pointer;
                                   border-radius: 5px;
                                   width: 184px;
                                   ">
                                    <table style="display: inline-block; ">
                                        <tr>
                                            <td><a href="tel:+***********" ><img src="https://mover.primemarketing.us/dist/img/email_setup/call.png" width="30" height="28" style="display: inline-block;"></a></td>
                                            <td style="font-size: 18px;"><a href="tel:+***********" style="color: #151B26; text-decoration: none;">(*************</a></td>
                                        </tr>
                                    </table>
                                </a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background: #F0F0F0 url(https://mover.primemarketing.us/dist/img/email_setup/bg.png) no-repeat 0 0; width: 100%; padding: 0 30px; ">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="text-align: center; padding:24px; color: #000; font-weight: bold;">
                                Hi <b style="color: #000; text-transform: uppercase;">'.$businessName.'</b>
                            </td>
                        </tr>
                        <tr>
                            <td style="background-color: #ffffff; padding:20px; border-radius: 10px; ">
                                <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
        
                                    <tr>
                                        <td style="background: #E7E9EA; border-radius: 10px; padding: 30px; text-align: center; width: 100%;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="width: 100%; text-align: center;"><img src="https://mover.primemarketing.us/dist/img/email_setup/checked.png" width="50" height="50" alt=""></td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size: 18px; text-align: center; color: #000; font-weight: bold; padding-top: 10px;">Account is Successfully Charged!</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align: left; color: #000; font-size: 12px; line-height: 20px;">
                                            This is to inform you that your account has been charged successfully. The transaction details are as follows:
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="width: 146px;">
                                                        <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                            <tr>
                                                                <td style="color: #818181; font-size: 12px;">Business/Campaign Name</td>
                                                            </tr>
                                                            <tr>
                                                                <td style="color: #000; font-size: 12px; padding-top: 10px; font-weight: bold;">'.$businessCampaignName.'</td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                    <td style="text-align: center; width: 24px;"><img src="https://mover.primemarketing.us/dist/img/email_setup/pipe.png" width="2" height="40" alt=""></td>
                                                    <td>
                                                        <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                            <tr>
                                                                <td style="color: #818181; font-size: 12px;">Last 4 Digits of Card</td>
                                                            </tr>
                                                            <tr>
                                                                <td style="color: #000; font-size: 12px; padding-top: 10px; font-weight: bold;">xxxx xxxx xxxx '.sprintf("%04d", $cardNumber).'</td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                    <td style="text-align: center; width: 24px;"><img src="https://mover.primemarketing.us/dist/img/email_setup/pipe.png" width="2" height="40" alt=""></td>
                                                    <td>
                                                        <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                            <tr>
                                                                <td style="color: #818181; font-size: 12px;">Date/Time</td>
                                                            </tr>
                                                            <tr>
                                                                <td style="color: #000; font-size: 12px; padding-top: 10px; font-weight: bold;">'.date('m/d/Y h:i A').' EST</td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="color: #818181; width: 250px; font-size: 12px;">Transaction ID: <b style="text-align: right; font-weight: bold; font-size: 12px; color: #000;">'.$transactionId.'</b></td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="border-top: 1px solid #E7E9EA; padding: 15px 0;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #818181; width: 250px; font-size: 12px;">Charge Amount</td>
                                                    <td style="text-align: right; font-weight: bold; font-size: 12px; color: #000;">$'.number_format($chargeAmount, 2, '.', ',').'</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border-top: 1px solid #E7E9EA; padding: 15px 0;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #818181; width: 250px; font-size: 12px;">3% Convenience Fees</td>
                                                    <td style="text-align: right; font-weight: bold; font-size: 12px; color: #000;">$'.number_format($convenienceFee, 2, '.', ',').'</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border-top: 1px solid #000000;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #818181; width: 250px; font-size: 12px; padding: 15px 0;">&nbsp;</td>
                                                    <td style="text-align: right; font-weight: bold; font-size: 12px; color: #000; padding: 15px 0; border-bottom: 1px solid #000000;">
                                                        <table style="margin-left: auto;">
                                                            <tr>
                                                                <td style="color: #818181;">Total Amount Paid:</td>
                                                                <td>&nbsp;</td>
                                                                <td style="font-size: 20px; color: #000;">$'.number_format($chargeAmount + $convenienceFee, 2, '.', ',').'</td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="color: #000; font-size: 12px; line-height: 18px;">If you believe this charge to be in error or you have any questions, please reach out to our sales team at <a href="mailto:<EMAIL>" style="color: #CE0941; font-weight: bold;"><EMAIL></a> or call us immediately at <a href="tel:+***********" style="color: #000; text-decoration: none;">(*************</a>.</td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #000000; width: 250px; font-size: 12px;">Best Regards</td>
                                                </tr>
                                                <tr>
                                                    <td style="color: #000000; width: 250px; font-size: 12px; font-weight: bold;">Prime Marketing</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background-color: #F0F0F0; height: 25px;">&nbsp;</td>
            </tr>
            <tr>
                <td style="background-color: #ffffff; font-size: 12px; text-align: center; padding: 12px;">For support email us at <a href="mailto:<EMAIL>" style="color: #000000; font-weight: bold; text-decoration: none;"><EMAIL></a></td>
            </tr>
            <tr>
                <td style="background: #CE0941; padding: 15px;  text-align: center; font-size: 14px; font-family: Tahoma, arial; font-weight: normal; color: #ffffff;">
                    © ' . date('Y') . ' - Prime Marketing. All rights reserved.
                </td>
            </tr>
        </table>
        </body>
        </html>';
        $mail->setHtmlbody($pageContent);
        $response       = $mail->send_email($token = $postmarkToken, 'movers-recharge');

        EmailLog::create([
            "business_id" => $businessId,
            "subject" => $subject,
            "set_to" => $setTo,
            "set_from" => $setFrom,
            "response" => json_encode($response->original['data']),
            "type" => 'movers-recharge',
            'created_at' => date("Y-m-d H:i:s")
        ]);
        return true;
    }

    public function recurrentStopEmailNotificationTemplate($businessId, $businessName, $businessCampaignName, $businessEmail) {
        $mail           = new TemplateEmail();
        $subject        = "Update: Recurrent Payment Stopped";
        $setFrom        = '<EMAIL>';
        //$setTo        = '<EMAIL>';
        $postmarkToken  = '************************************';
        $setTo          = $businessEmail;

        $mail->setTo($setTo);
        $mail->setBcc('<EMAIL>');
        $mail->setFrom($setFrom);
        $mail->setSubject($subject);

        $pageContent    = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
        <head>
            <title>Prime Marketing</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
            <style type="text/css">
                *{
                    margin: 0;
                    padding: 0;
                    font-family: Tahoma, Arial, Helvetica, sans-serif;
                    color: #444;
                }
                @media only screen and (max-width: 599px) {
                    .call {
                        font-size: 0 !important;
                        justify-content: flex-end !important;
                    }
                }
            </style>
        </head>
        <body style="background-color: #F0F0F0;">
        <table style="width: 100%; max-width: 600px; min-width: 600px; margin: 0 auto; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
            <tr>
                <td style="background: #F0F0F0; padding: 14px 32px;">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="width: 99px;"><a href="https://primemarketing.us" target="_blank"><img src="https://mover.primemarketing.us/dist/img/email_setup/logo.png" width="99" height="35"></a></td>
                            <td>&nbsp;</td>
                            <td style="width: 184px; text-align: right;">
                                <a class="call" href="tel:+***********"
                                   style="
                                   padding: 10px 0;
                                   display: inline-block;
                                   font-size: 18px;
                                   color: #151B26;
                                   font-weight: bold;
                                   text-decoration: none;
                                   cursor: pointer;
                                   border-radius: 5px;
                                   width: 184px;
                                   ">
                                    <table style="display: inline-block; ">
                                        <tr>
                                            <td><a href="tel:+***********" ><img src="https://mover.primemarketing.us/dist/img/email_setup/call.png" width="30" height="28" style="display: inline-block;"></a></td>
                                            <td style="font-size: 18px;"><a href="tel:+***********" style="color: #151B26; text-decoration: none;">(*************</a></td>
                                        </tr>
                                    </table>
                                </a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background: #F0F0F0 url(https://mover.primemarketing.us/dist/img/email_setup/bg.png) no-repeat 0 0; width: 100%; padding: 0 30px; ">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="text-align: center; padding:24px; color: #000; font-weight: bold;">
                                Hi <b style="color: #000; text-transform: uppercase;">'.$businessName.',</b>
                            </td>
                        </tr>
                        <tr>
                            <td style="background-color: #ffffff; padding:20px; border-radius: 10px; ">
                                <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                    <tr>
                                        <td style="background: #E7E9EA; border-radius: 10px; padding: 30px; text-align: center; width: 100%;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="width: 100%; text-align: center;"><img src="https://mover.primemarketing.us/dist/img/email_setup/payment.png" width="50" height="50" alt=""></td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size: 18px; text-align: center; color: #000; font-weight: bold; padding-top: 10px;">Automated Payment Stopped</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align: left; color: #000; font-size: 12px; line-height: 20px;">
                                            We want to inform you that the recurring payment for your campaign/business has been stopped. Here are the details
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="border-bottom: 1px solid #E7E9EA; padding: 0 0 15px 0;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #818181; width: 250px; font-size: 12px;">Business/Campaign Name</td>
                                                    <td style="text-align: right; font-weight: bold; font-size: 12px; color: #000;">'.$businessCampaignName.'</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border-bottom: 1px solid #E7E9EA; padding: 15px 0;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #818181; width: 250px; font-size: 12px;">Effective Date of Stop</td>
                                                    <td style="text-align: right; font-weight: bold; font-size: 12px; color: #000;">'.date('m/d/Y').'</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="color: #000; font-size: 12px; line-height: 18px;">Please note that no automated charges will be made to your account for this business/campaign. If you have any questions regarding the action taken, contact our sales team at <a href="mailto:<EMAIL>" style="color: #CE0941; font-weight: bold;"><EMAIL></a> or call us immediately at <a href="tel:+***********" style="color: #000; text-decoration: none;">(*************</a>.</td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #000000; width: 250px; font-size: 12px;">Best Regards</td>
                                                </tr>
                                                <tr>
                                                    <td style="color: #000000; width: 250px; font-size: 12px; font-weight: bold;">Prime Marketing</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background-color: #F0F0F0; height: 25px;">&nbsp;</td>
            </tr>
            <tr>
                <td style="background-color: #ffffff; font-size: 12px; text-align: center; padding: 12px;">For support email us at <a href="mailto:<EMAIL>" style="color: #000000; font-weight: bold; text-decoration: none;"><EMAIL></a></td>
            </tr>
            <tr>
                <td style="background: #CE0941; padding: 15px;  text-align: center; font-size: 14px; font-family: Tahoma, arial; font-weight: normal; color: #ffffff;">
                    © ' . date('Y') . ' - Prime Marketing. All rights reserved.
                </td>
            </tr>
        </table>
        </body>
        </html>';
        $mail->setHtmlbody($pageContent);
        $response       = $mail->send_email($token = $postmarkToken, 'recurrent-stop');

        EmailLog::create([
            "business_id" => $businessId,
            "subject" => $subject,
            "set_to" => $setTo,
            "set_from" => $setFrom,
            "response" => json_encode($response->original['data']),
            "type" => 'recurrent-stop',
            'created_at' => date("Y-m-d H:i:s")
        ]);
        return true;
    }

    public function amountBasedRecurrentEmailNotificationTemplate($businessId, $businessName, $businessCampaignName, $businessEmail, $cardNumber, $chargeAmount) {
        $mail           = new TemplateEmail();
        $subject        = "PM Recurrent Payment Plan Activated";
        $setFrom        = '<EMAIL>';
        //$setTo        = '<EMAIL>';
        $postmarkToken  = '************************************';
        $setTo          = $businessEmail;

        $mail->setTo($setTo);
        $mail->setBcc('<EMAIL>');
        $mail->setFrom($setFrom);
        $mail->setSubject($subject);

        $pageContent    = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
        <head>
            <title>Prime Marketing</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
            <style type="text/css">
                *{
                    margin: 0;
                    padding: 0;
                    font-family: Tahoma, Arial, Helvetica, sans-serif;
                    color: #444;
                }
                @media only screen and (max-width: 599px) {
                    .call {
                        font-size: 0 !important;
                        justify-content: flex-end !important;
                    }
                }
            </style>
        </head>
        <body style="background-color: #F0F0F0;">
        <table style="width: 100%; max-width: 600px; min-width: 600px; margin: 0 auto; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
            <tr>
                <td style="background: #F0F0F0; padding: 14px 32px;">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="width: 99px;"><a href="https://primemarketing.us" target="_blank"><img src="https://mover.primemarketing.us/dist/img/email_setup/logo.png" width="99" height="35"></a></td>
                            <td>&nbsp;</td>
                            <td style="width: 184px; text-align: right;">
                                <a class="call" href="tel:+***********"
                                   style="
                                   padding: 10px 0;
                                   display: inline-block;
                                   font-size: 18px;
                                   color: #151B26;
                                   font-weight: bold;
                                   text-decoration: none;
                                   cursor: pointer;
                                   border-radius: 5px;
                                   width: 184px;
                                   ">
                                    <table style="display: inline-block; ">
                                        <tr>
                                            <td><a href="tel:+***********" ><img src="https://mover.primemarketing.us/dist/img/email_setup/call.png" width="30" height="28" style="display: inline-block;"></a></td>
                                            <td style="font-size: 18px;"><a href="tel:+***********" style="color: #151B26; text-decoration: none;">(*************</a></td>
                                        </tr>
                                    </table>
                                </a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background: #F0F0F0 url(https://mover.primemarketing.us/dist/img/email_setup/bg.png) no-repeat 0 0; width: 100%; padding: 0 30px; ">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="text-align: center; padding:24px; color: #000; font-weight: bold;">
                                Hi <b style="color: #000; text-transform: uppercase;">'.$businessName.',</b>
                            </td>
                        </tr>
                        <tr>
                            <td style="background-color: #ffffff; padding:20px; border-radius: 10px; ">
                                <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
        
                                    <tr>
                                        <td style="background: #E7E9EA; border-radius: 10px; padding: 30px; text-align: center; width: 100%;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="width: 100%; text-align: center;"><img src="https://mover.primemarketing.us/dist/img/email_setup/schedule.png" width="50" height="50" alt=""></td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size: 18px; text-align: center; color: #000; font-weight: bold; padding-top: 10px;">Scheduled Payment Activated</td>
                                                </tr>
                                            </table>
        
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align: left; color: #000; font-size: 12px; line-height: 20px;">
                                            The recurrent payment for your campaign/business has been successfully enabled. The recurrent payment is based on threshold amount set by you. Here are the details of your recurrent payment
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="border-bottom: 1px solid #E7E9EA; padding: 0 0 15px 0;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #818181; width: 250px; font-size: 12px;">Business/Campaign Name</td>
                                                    <td style="text-align: right; font-weight: bold; font-size: 12px; color: #000;">'.$businessCampaignName.'</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border-bottom: 1px solid #E7E9EA; padding: 15px 0;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #818181; width: 250px; font-size: 12px;">Payment Frequency</td>
                                                    <td style="text-align: right; font-weight: bold; font-size: 12px; color: #000;">Threshold Amount Based</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border-bottom: 1px solid #E7E9EA; padding: 15px 0;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #818181; width: 250px; font-size: 12px;">Threshold Amount</td>
                                                    <td style="text-align: right; font-weight: bold; font-size: 12px; color: #000;">$'.number_format($chargeAmount, 2, '.', ',').'</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border-bottom: 1px solid #E7E9EA; padding: 15px 0;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #818181; width: 250px; font-size: 12px;">Card Details</td>
                                                    <td style="text-align: right; font-weight: bold; font-size: 12px; color: #000;">xxxx xxxx xxxx '.sprintf("%04d", $cardNumber).'</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="color: #000; font-size: 12px; line-height: 18px;">If you have any questions or need further assistance, feel free to contact our sales team at <a href="mailto:<EMAIL>" style="color: #CE0941; font-weight: bold;"><EMAIL></a> or call us immediately at <a href="tel:+***********" style="color: #000; text-decoration: none;">(*************</a>.</td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #000000; width: 250px; font-size: 12px;">Best Regards</td>
                                                </tr>
                                                <tr>
                                                    <td style="color: #000000; width: 250px; font-size: 12px; font-weight: bold;">Prime Marketing</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
        
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background-color: #F0F0F0; height: 25px;">&nbsp;</td>
            </tr>
            <tr>
                <td style="background-color: #ffffff; font-size: 12px; text-align: center; padding: 12px;">For support email us at <a href="mailto:<EMAIL>" style="color: #000000; font-weight: bold; text-decoration: none;"><EMAIL></a></td>
            </tr>
            <tr>
                <td style="background: #CE0941; padding: 15px;  text-align: center; font-size: 14px; font-family: Tahoma, arial; font-weight: normal; color: #ffffff;">
                    © ' . date('Y') . ' - Prime Marketing. All rights reserved.
                </td>
            </tr>
        </table>
        </body>
        </html>';
        $mail->setHtmlbody($pageContent);
        $response       = $mail->send_email($token = $postmarkToken, 'amount-based');

        EmailLog::create([
            "business_id" => $businessId,
            "subject" => $subject,
            "set_to" => $setTo,
            "set_from" => $setFrom,
            "response" => json_encode($response->original['data']),
            "type" => 'amount-based',
            'created_at' => date("Y-m-d H:i:s")
        ]);
        return true;
    }

    public function dailyBasedRecurrentEmailNotificationTemplate($businessId, $businessName, $businessCampaignName, $businessEmail, $cardNumber, $chargeDay) {
        $mail           = new TemplateEmail();
        $subject        = "Your PM recurrent payments are now active";
        $setFrom        = '<EMAIL>';
        //$setTo        = '<EMAIL>';
        $postmarkToken  = '************************************';
        $setTo          = $businessEmail;

        $mail->setTo($setTo);
        $mail->setBcc('<EMAIL>');
        $mail->setFrom($setFrom);
        $mail->setSubject($subject);

        $pageContent    = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
        <head>
            <title>Prime Marketing</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
            <style type="text/css">
                *{
                    margin: 0;
                    padding: 0;
                    font-family: Tahoma, Arial, Helvetica, sans-serif;
                    color: #444;
                }
                @media only screen and (max-width: 599px) {
                    .call {
                        font-size: 0 !important;
                        justify-content: flex-end !important;
                    }
                }
            </style>
        </head>
        <body style="background-color: #F0F0F0;">
        <table style="width: 100%; max-width: 600px; min-width: 600px; margin: 0 auto; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
            <tr>
                <td style="background: #F0F0F0; padding: 14px 32px;">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="width: 99px;"><a href="https://primemarketing.us" target="_blank"><img src="https://mover.primemarketing.us/dist/img/email_setup/logo.png" width="99" height="35"></a></td>
                            <td>&nbsp;</td>
                            <td style="width: 184px; text-align: right;">
                                <a class="call" href="tel:+***********"
                                   style="
                                   padding: 10px 0;
                                   display: inline-block;
                                   font-size: 18px;
                                   color: #151B26;
                                   font-weight: bold;
                                   text-decoration: none;
                                   cursor: pointer;
                                   border-radius: 5px;
                                   width: 184px;
                                   ">
                                    <table style="display: inline-block; ">
                                        <tr>
                                            <td><a href="tel:+***********" ><img src="https://mover.primemarketing.us/dist/img/email_setup/call.png" width="30" height="28" style="display: inline-block;"></a></td>
                                            <td style="font-size: 18px;"><a href="tel:+***********" style="color: #151B26; text-decoration: none;">(*************</a></td>
                                        </tr>
                                    </table>
                                </a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background: #F0F0F0 url(https://mover.primemarketing.us/dist/img/email_setup/bg.png) no-repeat 0 0; width: 100%; padding: 0 30px; ">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="text-align: center; padding:24px; color: #000; font-weight: bold;">
                                Hi <b style="color: #000; text-transform: uppercase;">'.$businessName.',</b>
                            </td>
                        </tr>
                        <tr>
                            <td style="background-color: #ffffff; padding:20px; border-radius: 10px; ">
                                <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                    <tr>
                                        <td style="background: #E7E9EA; border-radius: 10px; padding: 30px; text-align: center; width: 100%;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="width: 100%; text-align: center;"><img src="https://mover.primemarketing.us/dist/img/email_setup/money.png" width="50" height="50" alt=""></td>
                                                </tr>
                                                <tr>
                                                    <td style="font-size: 18px; text-align: center; color: #000; font-weight: bold; padding-top: 10px;">Automated Recurrent Payment Enabled</td>
                                                </tr>
                                            </table>

                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align: left; color: #000; font-size: 12px; line-height: 20px;">
                                            The recurrent payment for your campaign/business has been successfully enabled. The recurrent payment is based on the specific number of days, as per your selected plan. Check the details below-
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="border-bottom: 1px solid #E7E9EA; padding: 0 0 15px 0;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #818181; width: 250px; font-size: 12px;">Business/Campaign Name</td>
                                                    <td style="text-align: right; font-weight: bold; font-size: 12px; color: #000;">'.$businessCampaignName.'</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border-bottom: 1px solid #E7E9EA; padding: 15px 0;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #818181; width: 250px; font-size: 12px;">Payment Frequency</td>
                                                    <td style="text-align: right; font-weight: bold; font-size: 12px; color: #000;">Daily Based</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border-bottom: 1px solid #E7E9EA; padding: 15px 0;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #818181; width: 250px; font-size: 12px;">Selected Days</td>
                                                    <td style="text-align: right; font-weight: bold; font-size: 12px; color: #000;">'.implode(', ', $chargeDay).'</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="border-bottom: 1px solid #E7E9EA; padding: 15px 0;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #818181; width: 250px; font-size: 12px;">Card Details</td>
                                                    <td style="text-align: right; font-weight: bold; font-size: 12px; color: #000;">xxxx xxxx xxxx '.sprintf("%04d", $cardNumber).'</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="color: #000; font-size: 12px; line-height: 18px;">If you have any questions or need further assistance, feel free to contact our sales team at <a href="mailto:<EMAIL>" style="color: #CE0941; font-weight: bold;"><EMAIL></a> or call us immediately at <a href="tel:+***********" style="color: #000; text-decoration: none;">(*************</a>.</td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #000000; width: 250px; font-size: 12px;">Best Regards</td>
                                                </tr>
                                                <tr>
                                                    <td style="color: #000000; width: 250px; font-size: 12px; font-weight: bold;">Prime Marketing</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background-color: #F0F0F0; height: 25px;">&nbsp;</td>
            </tr>
            <tr>
                <td style="background-color: #ffffff; font-size: 12px; text-align: center; padding: 12px;">For support email us at <a href="mailto:<EMAIL>" style="color: #000000; font-weight: bold; text-decoration: none;"><EMAIL></a></td>
            </tr>
            <tr>
                <td style="background: #CE0941; padding: 15px;  text-align: center; font-size: 14px; font-family: Tahoma, arial; font-weight: normal; color: #ffffff;">
                    © ' . date('Y') . ' - Prime Marketing. All rights reserved.
                </td>
            </tr>
        </table>
        </body>
        </html>';
        $mail->setHtmlbody($pageContent);
        $response       = $mail->send_email($token = $postmarkToken, 'daily-based');

        EmailLog::create([
            "business_id" => $businessId,
            "subject" => $subject,
            "set_to" => $setTo,
            "set_from" => $setFrom,
            "response" => json_encode($response->original['data']),
            "type" => 'daily-based',
            'created_at' => date("Y-m-d H:i:s")
        ]);
        return true;
    }

    public function subscriptionRecurrentEmailNotificationTemplate($businessId, $businessName, $businessEmail, $planName, $cardNumber) {
        $mail           = new TemplateEmail();
        $subject        = "Prime Marketing Subscription Renewal Details";
        $setFrom        = '<EMAIL>';
        //$setTo        = '<EMAIL>';
        $postmarkToken  = '************************************';
        $setTo          = $businessEmail;

        $mail->setTo($setTo);
        //$mail->setBcc('<EMAIL>');
        $mail->setFrom($setFrom);
        $mail->setSubject($subject);

        $pageContent    = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
        <head>
            <title>Prime Marketing</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
            <style type="text/css">
                *{
                    margin: 0;
                    padding: 0;
                    font-family: Tahoma, Arial, Helvetica, sans-serif;
                    color: #444;
                }
            </style>
        </head>
        <body style="background-color: #F0F0F0;">
        <table style="width: 100%; max-width: 600px; min-width: 600px; margin: 0 auto; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
            <tr>
                <td style="background: #F0F0F0; padding: 14px 32px;">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="width: 99px;"><a href="https://primemarketing.us/" target="_blank"><img src="https://mover.primemarketing.us/dist/img/email_setup/logo.png" width="99" height="35"></a></td>
                            <td>&nbsp;</td>
                            <td style="width: 184px; text-align: right;">
                                <a class="call" href="tel:+***********"
                                   style="
                                   padding: 10px 0;
                                   display: inline-block;
                                   font-size: 18px;
                                   color: #151B26;
                                   font-weight: bold;
                                   text-decoration: none;
                                   cursor: pointer;
                                   border-radius: 5px;
                                   width: 184px;
                                   ">
                                    <table style="display: inline-block; ">
                                        <tr>
                                            <td><a href="tel:+***********" style="display: block; height: 28px;" ><img src="https://mover.primemarketing.us/dist/img/email_setup/call.png" width="30" height="28" style="display: inline-block;"></a></td>
                                            <td style="font-size: 18px;"><a href="tel:+***********" style="color: #151B26; text-decoration: none;">(*************</a></td>
                                        </tr>
                                    </table>
                                </a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background: #F0F0F0 url(https://mover.primemarketing.us/dist/img/email_setup/bg.png) no-repeat 0 0; width: 100%; padding: 0 15px; ">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="text-align: center; padding:12px; font-size: 16px; color: #000;">
                                Hi <b style="color: #000;">'.ucwords($businessName).',</b>
                            </td>
                        </tr>
                        <tr>
                            <td style="background-color: #ffffff; padding:15px; border-radius: 10px; ">
                                <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                    <tr>
                                        <td style="
                                         color: #303E67;
                                         text-align: left;
                                         font-family: Tahoma;
                                         font-size: 14px;
                                         font-style: normal;
                                         font-weight: 400;
                                         line-height: 22px; padding: 2px 0;">
                                            This is to inform you that we have <b style="color: #303E67;">enabled recurrent payment</b> for your subscription. The subscription plan renewal will take place on the last day of the month for the next month\'s subscription. No action is Needed at your part.
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="background: #ECEFF5; padding: 20px; border-radius: 10px;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="color: #818181; font-size: 14px; line-height: 24px; width: 122px; padding-bottom: 6px;">
                                                        Subscription
                                                    </td>
                                                    <td style="color: #818181; font-size: 14px; line-height: 24px; padding-bottom: 6px;">
                                                        <b style="color: #303E67;">: '.$planName.'</b>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="color: #818181; font-size: 14px; line-height: 24px; width: 122px;">
                                                        Card Details
                                                    </td>
                                                    <td style="color: #818181; font-size: 14px; line-height: 24px;">
                                                        <b style="color: #303E67;">: xxxx xxxx xxxx '.sprintf("%04d", $cardNumber).'</b>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="background-color: #ffffff; font-size: 14px; line-height: 22px; text-align: left; padding: 0 0 20px 0; color: #303E67; ">If you have any questions or need further assistance, feel free to contact our sales team at <a href="mailto:<EMAIL>" style="color: #CE0941;"><EMAIL></a></td>
                                    </tr>
                                    <tr>
                                        <td style="background-color: #ffffff; font-size: 12px; text-align: left; line-height: 20px; padding: 0 0 0 0; color: #303E67; ">
                                            Best Regards,<br>
                                            <b style="color: #303E67;">Prime Marketing</b>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background-color: #F0F0F0; height: 25px;">&nbsp;</td>
            </tr>
            <tr>
                <td style="background-color: #ffffff; font-size: 12px; text-align: center; padding: 12px;">For support email us at <a href="mailto:<EMAIL>" style="color: #000000; font-weight: bold; text-decoration: none;"><EMAIL></a></td>
            </tr>
            <tr>
                <td style="background: #CE0941; padding: 13px;  text-align: center; font-size: 14px; font-family: Tahoma, arial; font-weight: normal; color: #ffffff;">
                    © ' . date('Y') . ' - Prime Marketing. All rights reserved.
                </td>
            </tr>
        </table>
        </body>
        </html>';
        $mail->setHtmlbody($pageContent);
        $response       = $mail->send_email($token = $postmarkToken, 'daily-based');

        EmailLog::create([
            "business_id" => $businessId,
            "subject" => $subject,
            "set_to" => $setTo,
            "set_from" => $setFrom,
            "response" => json_encode($response->original['data']),
            "type" => 'daily-based',
            'created_at' => date("Y-m-d H:i:s")
        ]);
        return true;
    }

    public function insertSubscriptionCharge(Request $request) {
        $error = $businessCampaignName = "";
        $status = $businessId = $campaignId = $beforeCharge = $afterCharge = $convenienceFee = 0;
        $message = 'Failure';
        $cardreponse = null;

        $subscriptionPlanId         = $request->get('subscriptionPlanId');
        $businessCampaignId         = $request->get('businessesId');
        $businessId                 = $businessCampaignId;
        $subscriptionDetail         = Subscription::where('subscription_start', '>=', date('Y-m-01'))->where('subscription_end', '<=', date('Y-m-t'))->where('is_active', 'yes')->where('business_id', $businessId)->first();
        $subscriptionPlanDetail     = MstSubscriptionPlan::where('subscription_plan_id', $subscriptionPlanId)->first();

        $subscriptionPlan           = MstSubscriptionPlan::get();
        $subscriptionPlanArray      = [];
        for ($p=0; $p<count($subscriptionPlan); $p++) {
            $subscriptionPlanArray[$subscriptionPlan[$p]->subscription_plan_id] = $subscriptionPlan[$p]->subscription_plan;
        }

        if ($subscriptionPlanId == 1 || (isset($subscriptionDetail) && $subscriptionPlanId < $subscriptionDetail->subscription_plan_id)) {
            $validator = Validator::make($request->all(),[
                'subscriptionPlanId'=>'required|gt:0'
            ]);
        } else {
            $validator = Validator::make($request->all(),[
                'subscriptionPlanId'=>'required|gt:0',
                'lastFour' => 'required'
            ]);
        }

        if ($validator->fails()) {
            return response()->json(array(
                'status' => 0,
                //'message' => 'There are incorect values in the form!',
                'error' => $validator->getMessageBag()->toArray()
            ), 200);
            $this->throwValidationException(
                $request, $validator
            );
        }

        try {
            // Upgrade date
            $upgradeDay             = date("d");  // User upgrades on the 15th of the month
            $totalDaysInMonth       = date("t");  // Assume the month has 30 days

            // Calculate the total cost for the new plan for the remaining days
            $newPlanDaysLeft        = $totalDaysInMonth - $upgradeDay + 1;  // Including the upgrade day
            $newPlanDailyRate       = $subscriptionPlanDetail->amount / $totalDaysInMonth;
            $businessCampaignCharge = $newPlanDaysLeft * $newPlanDailyRate;

            $businessCampaignType   = 1;
            $lastFour               = $request->get('lastFour');
            $userId                 = Auth::user()->id;
            $ip                     = $request->ip();
            $useragent              = $_SERVER['HTTP_USER_AGENT'];
            $businessDetail         = Business::with(['businessCC' => function($query) {
                $query->where('status', 'active');
            }])->where('business_id', "=", $businessId)->get();

            $businessName           = $businessDetail[0]->display_name;
            $businessEmail          = $businessDetail[0]->email;
            $businessCampaignName   = $businessDetail[0]->display_name;
            $subscriptionRecuringPayment = SubscriptionRecuringPayment::where('business_id', $businessId)->first();

            if ($subscriptionPlanId == 1) {
                SubscriptionRecuringPayment::where('business_id', $businessCampaignId)->update([
                    'is_active' => 'no',
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

                SubscriptionUpgrade::where('business_id', $businessCampaignId)->delete();
                SubscriptionUpgrade::create([
                    'business_id' => $businessId,
                    'subscription_plan_id' => $subscriptionPlanId,
                    'subscription_start' => date('Y-m-01', strtotime('+1 month')),
                    'subscription_end' => date('Y-m-t', strtotime('+1 month')),
                    'amount' => $subscriptionPlanDetail->amount,
                    'is_active' => 'yes',
                    'created_user_id' => $userId,
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                if (!empty($subscriptionDetail)) {
                    //Added by BK on 09/04/2025 subscription changed email to movers
                    $this->subscriptionChangedEmailNotificationTemplate($businessId, $businessName, $businessEmail, $subscriptionDetail->subscription_plan_id, $subscriptionPlanArray[$subscriptionDetail->subscription_plan_id], $subscriptionPlanId, $subscriptionPlanArray[$subscriptionPlanId]);
                }

                $responseData = [
                    'status' => 1,
                    'message' => 'Success',
                    'error' => array('code' => "System Error", 'message' => $message)
                ];
                return response()->json($responseData);
            }

            if (!empty($subscriptionDetail) && $subscriptionPlanId > 1 && $subscriptionPlanId > $subscriptionDetail->subscription_plan_id) {
                // User's subscription data
                $oldPlanPrice = $subscriptionDetail->amount;  // Old plan price in dollars
                $newPlanPrice = $subscriptionPlanDetail->amount;  // New plan price in dollars

                // Calculate how much the user has paid for the old plan so far
                $oldPlanDaysUsed = $upgradeDay - 1;  // Days used on the old plan (before the upgrade)
                $oldPlanDailyRate = $oldPlanPrice / $totalDaysInMonth;
                $oldPlanCostSoFar = $oldPlanPrice - $oldPlanDaysUsed * $oldPlanDailyRate;

                // Calculate the total cost for the new plan for the remaining days
                $newPlanDaysLeft = $totalDaysInMonth - $upgradeDay + 1;  // Including the upgrade day
                $newPlanDailyRate = $newPlanPrice / $totalDaysInMonth;
                $newPlanCostRemaining = $newPlanDaysLeft * $newPlanDailyRate;
                // Calculate how much the user owes for the upgrade
                $businessCampaignCharge = $newPlanCostRemaining - $oldPlanCostSoFar;
            }

            if (!empty($subscriptionDetail) && $subscriptionPlanId > 1 && $subscriptionPlanId < $subscriptionDetail->subscription_plan_id) {
                SubscriptionRecuringPayment::where('business_id', $businessCampaignId)->update([
                    'subscription_plan_id' => $subscriptionPlanId,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

                SubscriptionUpgrade::where('business_id', $businessCampaignId)->delete();
                SubscriptionUpgrade::create([
                    'business_id' => $businessId,
                    'subscription_plan_id' => $subscriptionPlanId,
                    'subscription_start' => date('Y-m-01', strtotime('+1 month')),
                    'subscription_end' => date('Y-m-t', strtotime('+1 month')),
                    'amount' => $subscriptionPlanDetail->amount,
                    'is_active' => 'yes',
                    'created_user_id' => $userId,
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                if (isset($subscriptionRecuringPayment)) {
                    $subscriptionCardDetail = BusinessCC::where('business_cc_id', $subscriptionRecuringPayment->business_cc_id)->where('status', 'active')->first();

                    //Added by BK on 05/04/2025 subscription recurrent email to movers
                    $this->subscriptionRecurrentEmailNotificationTemplate($businessId, $businessName, $businessEmail, $subscriptionPlanDetail->subscription_plan, $subscriptionCardDetail->card);
                }

                //Added by BK on 09/04/2025 subscription changed email to movers
                $this->subscriptionChangedEmailNotificationTemplate($businessId, $businessName, $businessEmail, $subscriptionDetail->subscription_plan_id, $subscriptionPlanArray[$subscriptionDetail->subscription_plan_id], $subscriptionPlanId, $subscriptionPlanArray[$subscriptionPlanId]);

                $responseData = [
                    'status' => 1,
                    'message' => 'Success',
                    'error' => array('code' => "System Error", 'message' => $message)
                ];
                return response()->json($responseData);
            }

            if ($lastFour == 1 || $lastFour == 2 || $lastFour == 9) {
                $chargeType = $lastFour;
                $this->addCreditBankBalance($businessId, $campaignId, $userId, $businessCampaignCharge, $businessCampaignType, $chargeType, $ip, $useragent, 1);

                if (!empty($subscriptionDetail) && $subscriptionPlanId > 1 && $subscriptionPlanId > $subscriptionDetail->subscription_plan_id) {
                    SubscriptionUpgrade::where('business_id', $businessCampaignId)->delete();

                    Subscription::where('business_id', $businessId)->update([
                        'is_active' => 'no'
                    ]);

                    SubscriptionRecuringPayment::where('business_id', $businessCampaignId)->update([
                        'subscription_plan_id' => $subscriptionPlanId,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);

                    Subscription::create([
                        'business_id' => $businessId,
                        'subscription_plan_id' => $subscriptionPlanId,
                        'subscription_start' => date('Y-m-01'),
                        'subscription_end' => date('Y-m-t'),
                        'amount' => $subscriptionPlanDetail->amount,
                        'is_active' => 'yes',
                        'created_user_id' => $userId,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);

                    //Added by BK on 09/04/2025 subscription upgrade email to movers
                    $this->subscriptionUpgradeEmailNotificationTemplate($businessId, $businessName, $businessEmail, $subscriptionDetail->subscription_plan_id, $subscriptionPlanArray[$subscriptionDetail->subscription_plan_id], $subscriptionPlanId, $subscriptionPlanArray[$subscriptionPlanId]);
                } else {
                    Subscription::create([
                        'business_id' => $businessId,
                        'subscription_plan_id' => $subscriptionPlanId,
                        'subscription_start' => date('Y-m-01'),
                        'subscription_end' => date('Y-m-t'),
                        'amount' => $subscriptionPlanDetail->amount,
                        'is_active' => 'yes',
                        'created_user_id' => $userId,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);


                    //Added by BK on 09/04/2025 subscription upgrade email to movers
                    $this->subscriptionUpgradeEmailNotificationTemplate($businessId, $businessName, $businessEmail, 1, $subscriptionPlanArray[1], $subscriptionPlanId, $subscriptionPlanArray[$subscriptionPlanId]);
                }

                if (isset($subscriptionRecuringPayment)) {
                    $this->sendSubscriptionRecurrentEmail($subscriptionRecuringPayment, $businessId, $businessName, $businessEmail, $subscriptionPlanDetail->subscription_plan);
                }

                if ($subscriptionPlanId == 2) {
                    Campaign::where('business_id', $businessId)->update([
                        'subscription_score' => 1,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                } else if ($subscriptionPlanId == 3) {
                    Campaign::where('business_id', $businessId)->update([
                        'subscription_score' => 2,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                }

                $status = 1;
                $message = 'Success';
                $responseData = [
                    'status' => $status,
                    'message' => $message,
                    'error' => array('code' => "System Error", 'message' => $message)
                ];

                return response()->json($responseData);
            }
            //dd($userId);

            $cardDetail             = BusinessCC::where('business_cc_id', $lastFour)->where('status', 'active')->first();
            if (empty($cardDetail)) {
                throw new Exception('Unable to process the purchase transaction.');
            }
            if ($cardDetail->payment_card_id == "") {
                throw new Exception('Unable to process the purchase transaction.');
            }
            $paymentCardId          = $cardDetail->payment_card_id;
            $paymentProfileId       = $cardDetail->payment_cust_id;
            $cardId                 = $cardDetail->business_cc_id;
            //Added by BK on 03/02/2025 convenience fee to evey payment
            if ($businessCampaignCharge > 0) {
                $convenienceFee = ($businessCampaignCharge * 3) / 100;
                $businessCampaignCharge = $businessCampaignCharge + $convenienceFee;
            }

            $omni_token             = Helper::checkServer()['omni_token'];
            $postFields             = array("payment_method_id"=>$paymentCardId, "meta"=>array("tax"=>0), "total"=>$businessCampaignCharge, "pre_auth"=>0);
            $jsonData               = json_encode($postFields);
            $ch                     = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://apiprod.fattlabs.com/charge");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
            curl_setopt($ch, CURLOPT_HEADER, FALSE);
            curl_setopt($ch, CURLOPT_POST, TRUE);
            DevDebug::create(['sr_no' => 201, 'result' => 'Subscription Payment request: ' . $jsonData, 'created_at' => date('Y-m-d H:i:s')]);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);

            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/json",
                "Authorization:Bearer ".$omni_token,
                "Accept: application/json"
            ));

            $response               = curl_exec($ch);
            curl_close($ch);
            $jsonResponse           = json_decode($response, true);
            if (array_key_exists("payment_method_id",$jsonResponse)){
                if (is_array($jsonResponse['payment_method_id'])){
                    if(count($jsonResponse['payment_method_id']) > 0){
                        $error      = $jsonResponse['payment_method_id'][0];
                    }
                }
            }
            if (!empty($jsonResponse['error_description'])) {
                if (array_key_exists("error_description",$jsonResponse) ){
                    $error          = $jsonResponse['error_description'];
                }
            }
            DevDebug::create(['sr_no' => 202, 'result' => 'Subscription Payment response: ' . json_encode($jsonResponse), 'created_at' => date('Y-m-d H:i:s')]);

            //Added by BK on 03/02/2025 convenience fee to evey payment
            if ($convenienceFee > 0) {
                $businessCampaignCharge = $businessCampaignCharge - $convenienceFee;
            }
            if($error == ""){
                $responseStatus = "no";
                if (isset($jsonResponse['success']) && $jsonResponse['success'] == true) {
                    $responseStatus = "yes";
                }

                $businessCampaignPaymentId = BusinessCampaignPayment::create([
                    'business_id' => $businessCampaignId,
                    'business_cc_id' => $cardId,
                    'balance_before_charge' => $beforeCharge,
                    'amount_charge' => (float)$businessCampaignCharge,
                    'balance_after_charge' => $afterCharge,
                    'convenience_fee' => $convenienceFee,
                    'charge_type_id' => 1,
                    'charge_method_type' => 'subscription',
                    'charge_user_id' => $userId,
                    'charge_method' => 'other',
                    'fatt_payment_id' => (isset($jsonResponse['id'])) ? $jsonResponse['id'] : '',
                    'is_charge_approved' => $responseStatus,
                    'created_at' => date('Y-m-d H:i:s'),
                    'charge_user_ip' => $ip,
                    'charge_user_agent' => $useragent
                ])->business_campaign_payment_id;

                BusinessCampaingnPaymentLog::create([
                    'business_campaign_payment_id' => $businessCampaignPaymentId,
                    'request' => $jsonData,
                    'response' => json_encode($jsonResponse, true),
                    'created_at' => date('Y-m-d H:i:s'),
                ])->business_campaign_payment_log_id;

                //fetch total balance from businessespaymenthistory table
                if ($responseStatus == "yes") {
                    if (!empty($subscriptionDetail) && $subscriptionPlanId > 1 && $subscriptionPlanId > $subscriptionDetail->subscription_plan_id) {
                        Subscription::where('business_id', $businessId)->update([
                            'is_active' => 'no',
                        ]);

                        SubscriptionRecuringPayment::where('business_id', $businessId)->update([
                            'subscription_plan_id' => $subscriptionPlanId,
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);

                        Subscription::create([
                            'business_id' => $businessId,
                            'subscription_plan_id' => $subscriptionPlanId,
                            'subscription_start' => date('Y-m-01'),
                            'subscription_end' => date('Y-m-t'),
                            'amount' => $subscriptionPlanDetail->amount,
                            'is_active' => 'yes',
                            'created_user_id' => $userId,
                            'created_at' => date('Y-m-d H:i:s')
                        ]);

                        //Added by BK on 09/04/2025 subscription upgrade email to movers
                        $this->subscriptionUpgradeEmailNotificationTemplate($businessId, $businessName, $businessEmail, $subscriptionDetail->subscription_plan_id, $subscriptionPlanArray[$subscriptionDetail->subscription_plan_id], $subscriptionPlanId, $subscriptionPlanArray[$subscriptionPlanId]);
                    } else {
                        Subscription::create([
                            'business_id' => $businessId,
                            'subscription_plan_id' => $subscriptionPlanId,
                            'subscription_start' => date('Y-m-01'),
                            'subscription_end' => date('Y-m-t'),
                            'amount' => $subscriptionPlanDetail->amount,
                            'is_active' => 'yes',
                            'created_user_id' => $userId,
                            'created_at' => date('Y-m-d H:i:s')
                        ]);

                        //Added by BK on 09/04/2025 subscription upgrade email to movers
                        $this->subscriptionUpgradeEmailNotificationTemplate($businessId, $businessName, $businessEmail, 1, $subscriptionPlanArray[1], $subscriptionPlanId, $subscriptionPlanArray[$subscriptionPlanId]);
                    }

                    if (isset($subscriptionRecuringPayment)) {
                        $this->sendSubscriptionRecurrentEmail($subscriptionRecuringPayment, $businessId, $businessName, $businessEmail, $subscriptionPlanDetail->subscription_plan);
                    }

                    //Added by BK on 05/02/2025 convenience fee email to evey success payment
                    $transactionId = (isset($jsonResponse['id'])) ? $jsonResponse['id'] : '';
                    $this->moversRechargeEmailNotificationTemplate($businessId, $businessName, $businessCampaignName, $businessEmail, $cardDetail->card, $businessCampaignCharge, $convenienceFee, $transactionId);

                    if ($subscriptionPlanId == 2) {
                        Campaign::where('business_id', $businessId)->update([
                            'subscription_score' => 1,
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                    } else if ($subscriptionPlanId == 3) {
                        Campaign::where('business_id', $businessId)->update([
                            'subscription_score' => 2,
                            'updated_at' => date('Y-m-d H:i:s')
                        ]);
                    }
                }
                $status = 1;
                $message = 'Success';
            } else {
                $businessCampaignPaymentId = BusinessCampaignPayment::create([
                    'business_id' => $businessCampaignId,
                    'business_cc_id' => $cardId,
                    'balance_before_charge' => $beforeCharge,
                    'amount_charge' => (float)$businessCampaignCharge,
                    'balance_after_charge' => $afterCharge,
                    'convenience_fee' => $convenienceFee,
                    'charge_type_id' => 1,
                    'charge_method_type' => 'subscription',
                    'charge_user_id' => $userId,
                    'charge_method' => 'other',
                    'fatt_payment_id' => (isset($jsonResponse['id'])) ? $jsonResponse['id'] : '',
                    'is_charge_approved' => "no",
                    'created_at' => date('Y-m-d H:i:s'),
                    'charge_user_ip' => $ip,
                    'charge_user_agent' => $useragent
                ])->business_campaign_payment_id;

                BusinessCampaingnPaymentLog::create([
                    'business_campaign_payment_id' => $businessCampaignPaymentId,
                    'request' => $jsonData,
                    'response' => json_encode($jsonResponse, true),
                    'created_at' => date('Y-m-d H:i:s'),
                ])->business_campaign_payment_log_id;

                throw new Exception('Unable to process the purchase transaction');
            }
        } catch(Exception $e) {
            $message = $e->getMessage();
        }

        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => array('code'=>"System Error", 'message' => $error),
            'cardresponse' => $cardreponse
        ];
        return response()->json($responseData);
    }

    public function insertSubscriptionRecurrent(Request $request) {
        $error = "";
        $status = 0;
        $message = 'Failure';
        $logArr = $daysArr = [];

        $validator = Validator::make($request->all(),[
            'lastFour' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json(array(
                'success' => 0,
                //'message' => 'There are incorect values in the form!',
                'error' => $validator->getMessageBag()->toArray()
            ), 200);
            $this->throwValidationException(
                $request, $validator
            );
        }

        try {
            $businessId = $request->get('businessId');
            $subscriptionPlanId = $request->get('subscriptionPlanId');
            $lastFour = $request->get('lastFour');
            $lastFourText = $request->get('lastFourText');
            $userId = Auth::user()->id;
            $subscriptionPlanDetail = MstSubscriptionPlan::where('subscription_plan_id', $subscriptionPlanId)->first();
            $businessDetail = Business::where('business_id', $businessId)->first();

            //store data in business_campaign_recuring_payment table
            SubscriptionRecuringPayment::create([
                'subscription_plan_id' => $subscriptionPlanId,
                'business_id' => $businessId,
                'business_cc_id' => $lastFour,
                'created_user_id' => $userId,
                'is_active' => 'yes',
                'created_at' => date('Y-m-d H:i:s')
            ]);

            //Added by BK on 05/04/2025 subscription recurrent email to movers
            $this->subscriptionRecurrentEmailNotificationTemplate($businessId, $businessDetail->display_name, $businessDetail->email, $subscriptionPlanDetail->subscription_plan, $lastFourText);

            $status = 1;
            $message = 'Success';

        } catch(Exception $e) {
            $message = $e->getMessage();
        }

        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => array('code'=>"System Error", 'message' => $message)
        ];
        return response()->json($responseData);
    }

    public function editSubscriptionRecurrent(Request $request) {
        $error = "";
        $status = 0;
        $message = 'Failure';

        $logArr = $daysArr = $olddaysArr = [];
        $validator = Validator::make($request->all(),[
            'lastFour' => 'required',
            'status' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json(array(
                'success' => 0,
                /*'message' => 'There are incorect values in the form!',*/
                'error' => $validator->getMessageBag()->toArray()
            ), 200);
            $this->throwValidationException(
                $request, $validator
            );
        }

        try {
            $recurringPaymentId = $request->get('recurringPaymentId');
            $businessId = $request->get('businessId');
            $subscriptionPlanId = $request->get('subscriptionPlanId');
            $lastFour = $request->get('lastFour');
            $lastFourText = $request->get('lastFourText');
            $status = $request->get('status');
            $userId = Auth::user()->id;

            if ($status == 'yes') {
                SubscriptionRecuringPayment::where('business_id', $businessId)->update([
                    'subscription_plan_id' => $subscriptionPlanId,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            //store data in business_campaign_recuring_payment table
            SubscriptionRecuringPayment::where('subscription_recuring_payment_id', $recurringPaymentId)
                ->update([
                    'business_id' => $businessId,
                    'business_cc_id' => $lastFour,
                    'updated_user_id' => $userId,
                    'is_active' => $status,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            $status = 1;
            $message = 'Success';

        } catch(Exception $e) {
            $message = $e->getMessage();
        }

        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => array('code'=>"System Error", 'message' => $message)
        ];
        return response()->json($responseData);
    }

    public function sendSubscriptionRecurrentEmail($subscriptionRecuringPayment, $businessId, $businessName, $businessEmail, $planName) {
        $subscriptionCardDetail = BusinessCC::where('business_cc_id', $subscriptionRecuringPayment->business_cc_id)->where('status', 'active')->first();

        //Added by BK on 05/04/2025 subscription recurrent email to movers
        $this->subscriptionRecurrentEmailNotificationTemplate($businessId, $businessName, $businessEmail, $planName, $subscriptionCardDetail->card);
    }

    public function subscriptionUpgradeEmailNotificationTemplate($businessId, $businessName, $businessEmail, $planIdOld, $planNameOld, $planIdNew, $planNameNew){
        $dashboardUrl   = Helper::checkServer()['mover_dashboard_url'] . '/dashboard/' . base64_encode($businessId) . '/2';
        $planImageArray1= [ 1 => 'free-old', 2 => 'silver-old', 3 => 'gold-old' ];
        $planImageArray2= [ 1 => 'free-new', 2 => 'silver-new', 3 => 'gold-new' ];
        $mail           = new TemplateEmail();
        $subject        = "Prime Marketing Subscription Upgraded";
        $setFrom        = '<EMAIL>';
        //$setTo        = '<EMAIL>';
        $postmarkToken  = '************************************';
        $setTo          = $businessEmail;

        $mail->setTo($setTo);
        //$mail->setBcc('<EMAIL>');
        $mail->setFrom($setFrom);
        $mail->setSubject($subject);

        $pageContent    = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
        <head>
            <title>Prime Marketing</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
            <style type="text/css">
                *{
                    margin: 0;
                    padding: 0;
                    font-family: Tahoma, Arial, Helvetica, sans-serif;
                    color: #444;
                }
            </style>
        </head>
        <body style="background-color: #F0F0F0;">
        <table style="width: 100%; max-width: 600px; min-width: 600px; margin: 0 auto; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
            <tr>
                <td style="background: #F0F0F0; padding: 14px 32px;">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="width: 99px;"><a href="https://primemarketing.us/" target="_blank"><img src="https://mover.primemarketing.us/dist/img/email_setup/logo.png" width="99" height="35"></a></td>
                            <td>&nbsp;</td>
                            <td style="width: 184px; text-align: right;">
                                <a class="call" href="tel:+***********"
                                   style="
                                   padding: 10px 0;
                                   display: inline-block;
                                   font-size: 18px;
                                   color: #151B26;
                                   font-weight: bold;
                                   text-decoration: none;
                                   cursor: pointer;
                                   border-radius: 5px;
                                   width: 184px;
                                   ">
                                    <table style="display: inline-block; ">
                                        <tr>
                                            <td><a href="tel:+***********" style="display: block; height: 28px;" ><img src="https://mover.primemarketing.us/dist/img/email_setup/call.png" width="30" height="28" style="display: inline-block;"></a></td>
                                            <td style="font-size: 18px;"><a href="tel:+***********" style="color: #151B26; text-decoration: none;">(*************</a></td>
                                        </tr>
                                    </table>
                                </a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background: #F0F0F0 url(https://mover.primemarketing.us/dist/img/email_setup/bg.png) no-repeat 0 0; width: 100%; padding: 0 15px; ">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="text-align: center; padding:12px; font-size: 16px; color: #000;">
                                Hi <b style="color: #000;">'.ucwords($businessName).',</b>
                            </td>
                        </tr>
                        <tr>
                            <td style="background-color: #ffffff; padding:15px; border-radius: 10px; ">
                                <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                    <tr>
                                        <td style="
                                         color: #303E67;
                                         text-align: left;
                                         font-family: Tahoma;
                                         font-size: 14px;
                                         font-style: normal;
                                         font-weight: 400;
                                         line-height: 22px; padding: 2px 0 0 0;">
                                            Thank you for upgrading! Your mover’s subscription has been successfully updated from the '.$planNameOld.' to the '.$planNameNew.', effective '.$planNameNew.'.
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="
                                         color: #303E67;
                                         text-align: left;
                                         font-family: Tahoma;
                                         font-size: 14px;
                                         font-style: normal;
                                         font-weight: 400;
                                         line-height: 22px; padding: 0;">
                                            With the upgrade in the subscription plan, you will experience some exclusive perks.
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 10px;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td style="width: 138px;">
                                                        <img src="https://mover.primemarketing.us/dist/img/email_setup/'.$planImageArray1[$planIdOld].'.png?v='.time().'" width="138" height="107" style="display: inline-block;">
                                                    </td>
                                                    <td style="padding: 0 10px; width: 20px;">
                                                        <img src="https://mover.primemarketing.us/dist/img/email_setup/arrow.png" width="20" height="20" style="display: inline-block; ">
                                                    </td>
                                                    <td style="width: 166px;">
                                                        <img src="https://mover.primemarketing.us/dist/img/email_setup/'.$planImageArray2[$planIdNew].'.png?v='.time().'" width="166" height="178" style="display: inline-block; box-shadow: 0px 12px 25px 0px rgba(0, 0, 0, 0.20); border-radius: 10px;">
                                                    </td>
                                                    <td>&nbsp;</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align: center;">
                                            <a href="'.$dashboardUrl.'" style="display: inline-block;">
                                                <img src="https://mover.primemarketing.us/dist/img/email_setup/btn-plan-details.png" width="166" height="52" alt="">
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="background-color: #ffffff; font-size: 14px; line-height: 22px; text-align: left; padding: 0 0 20px 0; color: #303E67; ">If you have any questions, please reach out to our dedicated support team.</td>
                                    </tr>
                                    <tr>
                                        <td style="background-color: #ffffff; font-size: 12px; text-align: left; line-height: 20px; padding: 0 0 0 0; color: #303E67; ">
                                            Best Regards,<br>
                                            <b style="color: #303E67;">Prime Marketing</b>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background-color: #F0F0F0; height: 25px;">&nbsp;</td>
            </tr>
            <tr>
                <td style="background-color: #ffffff; font-size: 12px; text-align: center; padding: 12px;">For support email us at <a href="mailto:<EMAIL>" style="color: #000000; font-weight: bold; text-decoration: none;"><EMAIL></a></td>
            </tr>
            <tr>
                <td style="background: #CE0941; padding: 13px;  text-align: center; font-size: 14px; font-family: Tahoma, arial; font-weight: normal; color: #ffffff;">
                    © ' . date('Y') . ' - Prime Marketing. All rights reserved.
                </td>
            </tr>
        </table>
        </body>
        </html>';
        $mail->setHtmlbody($pageContent);
        $response       = $mail->send_email($token = $postmarkToken, 'daily-based');

        EmailLog::create([
            "business_id" => $businessId,
            "subject" => $subject,
            "set_to" => $setTo,
            "set_from" => $setFrom,
            "response" => json_encode($response->original['data']),
            "type" => 'subscription-upgrade',
            'created_at' => date("Y-m-d H:i:s")
        ]);
        return true;
    }

    public function subscriptionChangedEmailNotificationTemplate($businessId, $businessName, $businessEmail, $planIdOld, $planNameOld, $planIdNew, $planNameNew, $isCron=0){
        $dashboardUrl   = Helper::checkServer()['mover_dashboard_url'] . '/dashboard/' . base64_encode($businessId) . '/2';
        $planImageArray1= [ 1 => 'free-old', 2 => 'silver-old', 3 => 'gold-old' ];
        $planImageArray2= [ 1 => 'free-new', 2 => 'silver-new', 3 => 'gold-new' ];
        $mail           = new TemplateEmail();
        $subject        = "Prime Marketing Subscription Plan Changed";
        $setFrom        = '<EMAIL>';
        //$setTo        = '<EMAIL>';
        $postmarkToken  = '************************************';
        $setTo          = $businessEmail;
        if ($isCron > 0) {
            $effectiveDate = date('m/01/Y');
        } else {
            $effectiveDate = date('m/01/Y', strtotime('+1 month'));
        }

        $mail->setTo($setTo);
        //$mail->setBcc('<EMAIL>');
        $mail->setFrom($setFrom);
        $mail->setSubject($subject);

        $pageContent    = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
        <html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
        <head>
            <title>Prime Marketing</title>
            <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
            <style type="text/css">
                *{
                    margin: 0;
                    padding: 0;
                    font-family: Tahoma, Arial, Helvetica, sans-serif;
                    color: #444;
                }
            </style>
        </head>
        <body style="background-color: #F0F0F0;">
        <table style="width: 100%; max-width: 600px; min-width: 600px; margin: 0 auto; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
            <tr>
                <td style="background: #F0F0F0; padding: 14px 32px;">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="width: 99px;"><a href="https://primemarketing.us/"><img src="https://mover.primemarketing.us/dist/img/email_setup/logo.png" width="99" height="35"></a></td>
                            <td>&nbsp;</td>
                            <td style="width: 184px; text-align: right;">
                                <a class="call" href="tel:+***********"
                                   style="
                                   padding: 10px 0;
                                   display: inline-block;
                                   font-size: 18px;
                                   color: #151B26;
                                   font-weight: bold;
                                   text-decoration: none;
                                   cursor: pointer;
                                   border-radius: 5px;
                                   width: 184px;
                                   ">
                                    <table style="display: inline-block; ">
                                        <tr>
                                            <td><a href="tel:+***********" style="display: block; height: 28px;" ><img src="https://mover.primemarketing.us/dist/img/email_setup/call.png" width="30" height="28" style="display: inline-block;"></a></td>
                                            <td style="font-size: 18px;"><a href="tel:+***********" style="color: #151B26; text-decoration: none;">(*************</a></td>
                                        </tr>
                                    </table>
                                </a>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background: #F0F0F0 url(https://mover.primemarketing.us/dist/img/email_setup/bg.png) no-repeat 0 0; width: 100%; padding: 0 15px; ">
                    <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="text-align: center; padding:12px; font-size: 16px; color: #000;">
                                Hi <b style="color: #000;">'.ucwords($businessName).',</b>
                            </td>
                        </tr>
                        <tr>
                            <td style="background-color: #ffffff; padding:15px; border-radius: 10px; ">
                                <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                    <tr>
                                        <td style="
                                         color: #303E67;
                                         text-align: left;
                                         font-family: Tahoma;
                                         font-size: 14px;
                                         font-style: normal;
                                         font-weight: 400;
                                         line-height: 22px; padding: 2px 0;">
                                            Your subscription plan has been changed from the '.$planNameOld.' to the '.$planNameNew.', effective '.$effectiveDate.'.
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 10px;">
                                            <table style="width: 100%; padding: 0; vertical-align: top; font-family: Tahoma, Arial, Helvetica, sans-serif;" cellpadding="0" cellspacing="0">
                                                <tr>
                                                    <td style="width: 138px;">
                                                        <img src="https://mover.primemarketing.us/dist/img/email_setup/'.$planImageArray1[$planIdOld].'.png?v='.time().'" width="138" height="107" style="display: inline-block;">
                                                    </td>
                                                    <td style="padding: 0 10px; width: 20px;">
                                                        <img src="https://mover.primemarketing.us/dist/img/email_setup/arrow.png" width="20" height="20" style="display: inline-block; ">
                                                    </td>
                                                    <td style="width: 166px;">
                                                        <img src="https://mover.primemarketing.us/dist/img/email_setup/'.$planImageArray2[$planIdNew].'.png?v='.time().'" width="166" height="178" style="display: inline-block; box-shadow: 0px 12px 25px 0px rgba(0, 0, 0, 0.20); border-radius: 10px;">
                                                    </td>
                                                    <td style="width: 38px;">&nbsp;</td>
                                                    <td style="background: url(https://mover.primemarketing.us/dist/img/email_setup/effective-date.png) no-repeat 0 36px; width: 138px; height: 107px; text-align: center; font-size: 16px; padding-top: 36px; color: #303E67;">
                                                        '.$effectiveDate.'
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align: center;">
                                            <a href="'.$dashboardUrl.'" style="display: inline-block;">
                                                <img src="https://mover.primemarketing.us/dist/img/email_setup/btn-plan-details.png" width="166" height="52" alt="">
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="background-color: #ffffff; font-size: 14px; line-height: 22px; text-align: left; padding: 0 0 20px 0; color: #303E67; ">If you ever wish to upgrade your plan again, feel free to reach out anytime.</td>
                                    </tr>
                                    <tr>
                                        <td style="background-color: #ffffff; font-size: 12px; text-align: left; line-height: 20px; padding: 0 0 0 0; color: #303E67; ">
                                            Best Regards,<br>
                                            <b style="color: #303E67;">Prime Marketing</b>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr>
                <td style="background-color: #F0F0F0; height: 25px;">&nbsp;</td>
            </tr>
            <tr>
                <td style="background-color: #ffffff; font-size: 12px; text-align: center; padding: 12px;">For support email us at <a href="mailto:<EMAIL>" style="color: #000000; font-weight: bold; text-decoration: none;"><EMAIL></a></td>
            </tr>
            <tr>
                <td style="background: #CE0941; padding: 13px;  text-align: center; font-size: 14px; font-family: Tahoma, arial; font-weight: normal; color: #ffffff;">
                    © ' . date('Y') . ' - Prime Marketing. All rights reserved.
                </td>
            </tr>
        </table>
        </body>
        </html>';
        $mail->setHtmlbody($pageContent);
        $response       = $mail->send_email($token = $postmarkToken, 'daily-based');

        EmailLog::create([
            "business_id" => $businessId,
            "subject" => $subject,
            "set_to" => $setTo,
            "set_from" => $setFrom,
            "response" => json_encode($response->original['data']),
            "type" => 'subscription-upgrade',
            'created_at' => date("Y-m-d H:i:s")
        ]);
        return true;
    }
}