<?php

namespace App\Http\Controllers\PlivoSms;

use App\Helpers\CommonFunctions;
use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\Business\Business;
use App\Models\Campaign\Campaign;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\Lead\Lead;
use App\Models\Lead\LeadCustomerSmsLog;
use App\Models\Lead\LeadRouting;
use App\Models\Master\MstCarType;
use App\Models\Master\MstLeadSource;
use DateTime;
use Exception;
use Illuminate\Http\Request;
use App\Models\Master\MstTimeZone;
use App\Models\Lead\LeadActivity;
use App\Models\Outbound\GeneralNumberConfig;
use App\Models\Subscription\Subscription;
use App\Models\Master\MstSubscriptionPlan;
use App\Models\Outbound\LeadCall;
use Carbon\Carbon;

class ConfirmationSms extends Controller
{

    public function confirmationSmscron(Request $request)
    {
        //This is extra api and need to redevlop base on requirement check the diffrent sources of landing page
        // dd("This is extra api and need to redevlop base on requirement check the diffrent sources of landing page");
        // cron every morning 8 AM NY time (server time)
        try {
            date_default_timezone_set('America/New_York');
            CommonFunctions::createDevDebugLog(1, 'confirmationSmscron request='.json_encode($request->all(), true));
            CommonFunctions::createDevDebugLog(1, 'confirmationSmscron is working');
            $currentdateTime = date('Y-m-d H:i:s');
            $date15 = new DateTime($currentdateTime);
            $date15->modify('-15 hours');
            $formatted_date = $date15->format('Y-m-d H:i:s');
            $loginfo = array();
            $originIds = MstLeadSource::where('lead_source_name', 'like', '%TOK%')->pluck('lead_source_id')->toArray();

            CommonFunctions::createDevDebugLog(1, 'CurrentDateTime ' . $currentdateTime . ' date15 ' . $formatted_date);

            $smsstartDate  = date("Y-m-d H:i:s", strtotime("-1 day"));
            $smsendDate    = date("Y-m-d H:i:s");
            CommonFunctions::createDevDebugLog(1, 'smsstartDate ' . $smsstartDate . ' smsendDate ' . $smsendDate);

            // $smsphoneumber = [];
            // $getSMSLogsData = LeadCustomerSmsLog::whereBetween('created_at', [$smsstartDate, $smsendDate])
            //     ->where(function ($query) {
            //         return $query->where('sms_subject', 'Lead Confirmation Message')->orWhere('sms_subject', 'Lead Confirmation Message (Cron)');
            //     })
            //     ->get()
            //     ->toArray();

            // for ($sms = 0; $sms < count($getSMSLogsData); $sms++) {
            //     $smsphoneumber[] = $getSMSLogsData[$sms]['lead_id'];
            // }
            // $smsnumbercount = (count($smsphoneumber) > 0)  ? true : false;

            // $leads = Lead::whereBetween('created_at', [$formatted_date, $currentdateTime])
            //     ->when($smsnumbercount, function ($query) use ($smsphoneumber) {
            //         return $query->whereNotIn('lead_id', $smsphoneumber);
            //     })
            //     ->where('lead_type', 'normal')
            //     ->whereNotIn('lead_source_id', $originIds)
            //     ->orderBy('lead_id', 'DESC')
            //     ->take(20)
            //     ->get()
            //     ->toArray();
            $timezoneids = [];
            $timezone = $request['timezone'];
            if( $timezone == 'est'){
                $timezoneids = MstTimeZone::where("timezone", "America/New_York")->pluck('timezone_id')->toArray();
            }else if($timezone == 'cst'){
                $timezoneids = MstTimeZone::where("timezone", "America/Chicago")->pluck('timezone_id')->toArray();
            }else if($timezone == 'mst'){
                $timezoneids = MstTimeZone::whereIn("timezone", ["America/Denver", "America/Phoenix"])->pluck('timezone_id')->toArray();
            }else{
                $timezoneids = MstTimeZone::whereNotIn("timezone", ["America/New_York", "America/Chicago", "America/Denver", "America/Phoenix"])->pluck('timezone_id')->toArray();
            }
            CommonFunctions::createDevDebugLog(1, 'timezone= ' . $timezone . ", timezoneids= " . json_encode($timezoneids, true));
            $leads = Lead::whereBetween('lead.created_at', [$formatted_date, $currentdateTime])
                ->whereIn('lead.timezone_id', $timezoneids)
                ->where('lead.is_dnc_sms', 'no')
                ->whereNotIn('lead.lead_id', function ($query) use ($smsstartDate, $smsendDate) {
                    return $query->select('lead_customer_sms_log.lead_id')
                        ->from('lead_customer_sms_log')
                        ->where('lead_customer_sms_log.lead_id', DB::raw('lead.lead_id'))
                        ->where(function ($query) {
                            return $query->where('lead_customer_sms_log.sms_subject', 'Lead Confirmation Message')->orWhere('lead_customer_sms_log.sms_subject', 'Lead Confirmation Message (Cron)');
                        })
                        ->whereBetween('lead_customer_sms_log.created_at', [$smsstartDate, $smsendDate]);
                })
                ->where('lead.lead_type', 'normal')
                ->whereNotIn('lead_source_id', $originIds)
                ->orderBy('lead.lead_id', 'ASC')
                ->take(20)
                ->get()
                ->toArray();
            //  echo "<pre>start=". $formatted_date. ", end= ". $currentdateTime." / ";
            // echo "<pre>start1=". $smsstartDate. ", end1= ". $smsendDate;
            // print_r($leads);
            // die;
            CommonFunctions::createDevDebugLog(1, 'Total ' . count($leads) . ' records found');
            CommonFunctions::createDevDebugLog(1, json_encode($leads));
            $sleepCounter = 0;
            if (count($leads) > 0) {
                foreach ($leads as $againlead) {
                    $loginfo[$againlead['lead_id']] = $this->LeadConfirmationSms($againlead['lead_id'], 1, 'cron');
                    $sleepCounter = $sleepCounter + 1;
                    if ($sleepCounter % 2 == 0) {
                        sleep(1);
                    }
                }
            } else {
                CommonFunctions::createDevDebugLog(1, "No leads to send sms");
            }

            echo json_encode($loginfo);
            exit;
        } catch (Exception $ex) {
            CommonFunctions::createDevDebugLog(1, 'Exception ==> confirmationSmscron is not working');
            CommonFunctions::createDevDebugLog(1, $ex);
        }
    }

    public function LeadConfirmationSms($leadId, $nightflag, $type = "simple")
    {
        try {
            $leadsemaildata = Lead::with(['moveInfo', 'routingInfoLeadList', 'heavyLiftMoveInfo', 'carTransMoveInfo', 'MstLeadCategory'])->where('lead_id', $leadId)->first();
            // For Do not Send SMS to unVerified Lead Start
            CommonFunctions::createDevDebugLog(1, json_encode($leadsemaildata));
            if ($leadsemaildata->is_verified == "no") {
                CommonFunctions::createDevDebugLog(1, $leadId . '==Lead Id== SMS Not send because lead is unverified.==' . $leadsemaildata->is_verified . "==Date time==" . date("Y-m-d H:i"));
                return 'SMS Not send because lead is unverified.';
            }

            $from_state = '';
            if ($leadsemaildata->lead_category_id == 1) {
                $from_state = $leadsemaildata->moveInfo->from_state;
            }
            if ($leadsemaildata->lead_category_id == 2) {
                $from_state = 'NY';
            }
            if ($leadsemaildata->lead_category_id == 3) {
                $from_state = $leadsemaildata->heavyLiftMoveInfo->from_state;
            }
            if ($leadsemaildata->lead_category_id == 4) {
                $from_state = $leadsemaildata->carTransMoveInfo->from_state;
            }
            if (($from_state == '' || $from_state == null) && $type != 'fb') {
                CommonFunctions::createDevDebugLog(1, $leadId . '==Lead Id== SMS Not send because from state not found.==  ' . $from_state . "==Date time==" . date("Y-m-d H:i"));
                return 'SMS Not send because error in from state.';
            }
            if ($type == 'fb') {
                $from_state = 'NY';
            }

            $timezone = $this->getFromstateToTimezone($from_state);
            date_default_timezone_set($timezone);
            $hours_check_cron = date('G');
            $weekDayNumber = date('N');
            CommonFunctions::createDevDebugLog(1, 'confirmationSmscron Time' . $hours_check_cron);
            /*if ($hours_check_cron <= 7 || $hours_check_cron >= 21) {
                CommonFunctions::createDevDebugLog(1, 'Night leadid='. $leadId);
                return 'Night time';
            }*/

            // //Added By HJ On 15-03-2022 For Check DNC Number
            $checkPhone = $leadsemaildata->is_dnc_sms;
            if ($checkPhone == "no") {
                //$leadsms_flag = LeadCustomerSmsLog::where('lead_id', $leadsemaildata->lead_id)->where('sms_subject', "Lead Confirmation Message")->where('lead_type', 'normal')->first();
                $leadsms_flag = LeadCustomerSmsLog::where('lead_id', $leadsemaildata->lead_id)
                    ->where(function ($query) {
                        return $query->where('sms_subject', 'Lead Confirmation Message')->orWhere('sms_subject', 'Lead Confirmation Message (Cron)');
                    })->where('lead_type', 'normal')->first();
                if ($leadsms_flag) {
                    CommonFunctions::createDevDebugLog(1, 'SMS already send');
                    return 'SMS already send.';
                }
                $origin = MstLeadSource::where('lead_source_id', $leadsemaildata->lead_source_id)->first();
                $src = '';
                $text = '';
                $pos = strpos($origin->lead_source_name, 'VM');
                $pos1 = strpos($origin->lead_source_name, 'QTM');
                $pos2 = strpos($origin->lead_source_name, 'IS');
                $pos3 = strpos($origin->lead_source_name, 'VMNL');
                $pos4 = strpos($origin->lead_source_name, 'VMO');
                if ($pos !== false || $pos3 !== false || $pos4 !== false) {
                    $callItNumber = GeneralNumberConfig::getDialNumberByAreacode($leadsemaildata->phone)['vm_areacode_outbound'];
                } else if ($pos1 !== false) {
                    $callItNumber = GeneralNumberConfig::getDialNumberByAreacode($leadsemaildata->phone)['qtm_areacode_outbound'];
                } else if ($pos2 !== false) {
                    $callItNumber = GeneralNumberConfig::getDialNumberByAreacode($leadsemaildata->phone)['ism_areacode_outbound'];
                }

                $smsString = " Reply STOP to unsubscribe. Msg & Data rates may apply.";
                if ($pos !== false) {
                    $src = '+12622176922';
                    $mymovers_link = 'https://vanlinesmove.com/dashboard?phone=' . base64_encode($leadsemaildata->phone);
                    if ($origin->lead_source_name == 'VM(MA)' || $origin->lead_source_name == 'VM(MAV)') {
                        $mymovers_link = 'https://quote.vanlinesmove.com/mymover?phone=' . base64_encode($leadsemaildata->phone) . '&isVMMA=1';
                    }

                    if ($leadsemaildata->lead_category_id == '1') {
                        // $text = 'Your vanlinesmove.com confirmation # ' . $leadsemaildata->lead_id . '. Call us at ******-455-2576 for any query. Your Movers information available on ' . $mymovers_link;
                        //$text = 'Your VanlinesMove confirmation number is ' . $leadsemaildata->lead_id . '. Dial ******-455-2576 to speak with our expert team who can assist you with personalized recommendations, offer exclusive discounts, or answer any questions you may have. We are here to go above and beyond for you. Also Your movers information are available on ' . $mymovers_link;
                        if ($hours_check_cron < 8 || $hours_check_cron >= 21) {
                            $text = "Your Vanlinesmove.com confirmation # " . $leadsemaildata->lead_id . ". You Moving quote with Vanlines Move is ready, please call ******-455-2576 in morning to get accurate quote.";
                        } else {
                            $text = "Hello " . ucwords($leadsemaildata->name) . ", your moving quote is ready! To secure the best rate, call us as soon as possible at ******-455-2576. We look forward to assisting you with your move! - Van Lines Move.";
                        }
                    } else if ($leadsemaildata->lead_category_id == '2') {
                        $text = 'Your Vanlines Move confirmation ID for Junk Removal request is # ' . $leadsemaildata->lead_id . '. Check your details now - ' . $mymovers_link . '. Call us at ******-455-2576 to talk with our concierge Team.';
                    } else if ($leadsemaildata->lead_category_id == '3') {
                        $heavyLiftType = 'Nothing';
                        if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == '1') {
                            $heavyLiftType = 'Heavy Equipment';
                        } else if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == '2') {
                            $heavyLiftType = 'Freight Shipping';
                        } else if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == '3') {
                            $heavyLiftType = 'Heavy Duty Trucks';
                        } else if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == '4') {
                            $heavyLiftType = 'Boat Transport';
                        } else if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == '5') {
                            $heavyLiftType = 'RV Shipping';
                        } else if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == '6') {
                            $heavyLiftType = 'Trailer';
                        }
                        $text = 'Your Vanlines Move confirmation ID for '.$heavyLiftType.' request is # ' . $leadsemaildata->lead_id . '. Check your details now - ' . $mymovers_link . '. Call us at ******-455-2576 to talk with our concierge Team.';
                    } else if ($leadsemaildata->lead_category_id == '4') {
                        $carTypeId  = $leadsemaildata->carTransMoveInfo->car_type_id;
                        $carTypeDetail = MstCarType::where("car_type_id", $carTypeId)->first();
                        $carType = $carTypeDetail->car_type;

                        $text = 'Your Vanlines Move confirmation ID for '.$carType.' request is # ' . $leadsemaildata->lead_id . '. Check your details now - ' . $mymovers_link . '. Call us at ******-455-2576 to talk with our concierge Team.';
                    }
                    $exitspot_lead_route = LeadRouting::where('lead_id', $leadsemaildata->lead_id)->where('route_status', 'sold')->get();

                    $exitspot = count($exitspot_lead_route);
                    if ($exitspot > 0 && $leadsemaildata->lead_category_id == '1') {
                        $lead_routing_exist_check = array();
                        if ($leadsemaildata->sold_type != 'premium') {
                            foreach ($exitspot_lead_route as $leadRvalue) {
                                $lead_routing_exist_check[] = $leadRvalue->campaign_id;
                            }
                        }

                        $exclusive_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [2,4])->get()->first();
                        if (isset($exclusive_data)) {
                            $businessesdata = Business::where('business_id', $exclusive_data->business_id)->get()->first();
                            if (isset($businessesdata) && $businessesdata->buyer_type_id != 3) {
                                $checkSubscription = $this->checkSubscription($businessesdata->business_id);
                                if ($businessesdata->rely_number != null && $checkSubscription) {
                                    $plivonumber = $this->formatPhoneNumber($businessesdata->rely_number);
                                    // $text = 'Vanlinesmove.com confirmation # ' . $leadsemaildata->lead_id . '. You are matched with ' . $businessesdata->display_name . ' and their Phone # ' . $plivonumber . '. View at ' . $mymovers_link . '.';
                                    $text = 'Your Vanlinesmove.com confirmation # ' . $leadsemaildata->lead_id . '. You Moving quote with ' . $businessesdata->display_name . ' is ready, please call +1 ' . $plivonumber . ' as they are expecting your call.';
                                    //$text = "Hello " . ucwords($leadsemaildata->name) . ", your moving quote is ready! To secure the best rate, call us as soon as possible at ******-455-2576. We look forward to assisting you with your move! - Van Lines Move.";
                                }
                            }
                        }

                        if ($leadsemaildata->sold_type == "dual") {
                            $dual_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [1, 6, 8])->pluck('business_id')->toArray();
                            if (isset($dual_data)) {
                                $businessesdata = Business::whereIn('business_id', $dual_data)->get(['business_id', 'display_name', 'rely_number']);
                                if (isset($businessesdata) && count($businessesdata) > 0) {
                                    $strDual = '';
                                    $key = 0;
                                    foreach ($businessesdata as $value) {
                                        $checkSubscription = $this->checkSubscription($value->business_id);
                                        if ($value->rely_number != null && $checkSubscription) {
                                            if ($key > 0) {
                                                $strDual.= ' and ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            } else {
                                                $strDual.= $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            }
                                            $key++;
                                        }
                                    }
                                    if (!empty($strDual)) {
                                        $text = 'Your Vanlinesmove.com confirmation # ' . $leadsemaildata->lead_id . '. Your Moving Quote with ' . $strDual . ' is ready as they are expecting your call.';
                                    }
                                }
                            }
                        }
                    }
                } else if ($pos1 !== false) {
                    $src = '+12622181970';
                    $mymovers_link = 'https://quotethemove.com/mymover?phone=' . base64_encode($leadsemaildata->phone);
                    $link = $mymovers_link;
                    $repNameArray = ['Jessica', 'Stephanie', 'Lidia', 'Silvia', 'Diana'];
                    $key = array_rand($repNameArray);
                    if ($leadsemaildata->lead_category_id == '1') {
                        // $text = 'Your confirmation # ' . $leadsemaildata->lead_id . '. for quotethemove.com request. Check your movers details now - ' . $link . '. Call us at ******-455-2577 to talk with our concierge Team.';
                        //$text = "Your confirmation # " . $leadsemaildata->lead_id . ". for quotethemove.com request. Call us at ******-455-2577 to talk with our concierge Team. Your movers' details are here - " . $link . ".";
                        $text = "Hi " . ucwords($leadsemaildata->name) . ", this is ".$repNameArray[$key]." from Quote The Move. Your moving quote is ready! Call us at your earliest convenience at ******-455-2577 to secure your moving date. Thanks!";
                    } else if ($leadsemaildata->lead_category_id == '2') {
                        $text = 'Hi ' . $leadsemaildata->name . ', your request for Junk Removal service is confirmed by quotethemove.com. CONFIRMATION NO # ' . $leadsemaildata->lead_id . '. Thank you for confirming the junk removal details. For any query contact us at www.quotethemove.com.';
                    } else if ($leadsemaildata->lead_category_id == '3') {
                        $heavyLiftType = 'Nothing';
                        if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == '1') {
                            $heavyLiftType = 'Heavy Equipment';
                        } else if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == '2') {
                            $heavyLiftType = 'Freight Shipping';
                        } else if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == '3') {
                            $heavyLiftType = 'Heavy Duty Trucks';
                        } else if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == '4') {
                            $heavyLiftType = 'Boat Transport';
                        } else if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == '5') {
                            $heavyLiftType = 'RV Shipping';
                        } else if ($leadsemaildata->heavyLiftMoveInfo->heavy_lifting_type_id == '6') {
                            $heavyLiftType = 'Trailer';
                        }
                        $text = 'Hi ' . $leadsemaildata->name . ', your request for Heavy Equipment Moving service is confirmed by quotethemove.com. CONFIRMATION NO # ' . $leadsemaildata->lead_id . '. Thank you for confirming the heavy equipment moving details. For any query contact us at www.quotethemove.com.';
                    } else if ($leadsemaildata->lead_category_id == '4') {
                        $carTypeId  = $leadsemaildata->carTransMoveInfo->car_type_id;
                        $carTypeDetail = MstCarType::where("car_type_id", $carTypeId)->first();
                        $carType = $carTypeDetail->car_type;

                        $text = 'Hi ' . $leadsemaildata->name . ', your request for Car Transportation service is confirmed by quotethemove.com. CONFIRMATION NO # ' . $leadsemaildata->lead_id . '. Thank you for confirming the car transportation details. For any query contact us at www.quotethemove.com.';
                    }
                    $exitspot_lead_route = LeadRouting::where('lead_id', $leadsemaildata->lead_id)->where('route_status', 'sold')->get();

                    $exitspot = count($exitspot_lead_route);
                    if ($exitspot > 0 && $leadsemaildata->lead_category_id == '1') {
                        $lead_routing_exist_check = array();
                        if ($leadsemaildata->sold_type != 'premium') {
                            foreach ($exitspot_lead_route as $leadRvalue) {
                                $lead_routing_exist_check[] = $leadRvalue->campaign_id;
                            }
                        }

                        $exclusive_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [1,2,6])->get()->first();
                        if (isset($exclusive_data)) {
                            $businessesdata = Business::where('business_id', $exclusive_data->business_id)->get()->first();
                            if (isset($businessesdata) && $businessesdata->buyer_type_id != 3) {
                                $checkSubscription = $this->checkSubscription($businessesdata->business_id);
                                if ($businessesdata->rely_number != null && $checkSubscription) {
                                    $plivonumber = $businessesdata->rely_number;
                                    //$text = 'Your confirmation # ' . $leadsemaildata->lead_id . '. for quotethemove.com request. Check your movers details now - ' . $link . '. Call us at ******-455-2577 to talk with our concierge Team.';
                                    $text = "Hi " . ucwords($leadsemaildata->name) . ", this is ".$repNameArray[$key]." from Quote The Move. Your moving quote is ready! Call us at your earliest convenience at ******-455-2577 to secure your moving date. Thanks!";
                                }
                            }
                        }

                        if ($leadsemaildata->sold_type == "dual") {
                            $dual_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [1, 6, 8])->pluck('business_id')->toArray();
                            if (isset($dual_data)) {
                                $businessesdata = Business::whereIn('business_id', $dual_data)->get(['business_id', 'display_name', 'rely_number']);
                                if (isset($businessesdata) && count($businessesdata) > 0) {
                                    $strDual = '';
                                    $key = 0;
                                    foreach ($businessesdata as $value) {
                                        $checkSubscription = $this->checkSubscription($value->business_id);
                                        if ($value->rely_number != null && $checkSubscription) {
                                            if ($key > 0) {
                                                $strDual.= ' and ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            } else {
                                                $strDual.= $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            }
                                            $key++;
                                        }
                                    }
                                    if (!empty($strDual)) {
                                        $text = 'Your Quotethemove.com confirmation # ' . $leadsemaildata->lead_id . '. Your Moving Quote with ' . $strDual . ' is ready as they are expecting your call.';
                                    }
                                }
                            }
                        }
                    }
                } else if ($pos2 !== false) {
                    $src = '+12622177267';
                    $mymovers_link = 'https://interstatesmover.com/mymover?phone=' . base64_encode($leadsemaildata->phone);
                    //$text = "Your interstatesmover.com confirmation # " . $leadsemaildata->lead_id . ". Call ******-524-2018 for personalized moving assistance, recommendations, and discounts.";
                    $text = "Your InterstatesMover.com confirmation # " . $leadsemaildata->lead_id . ". Call ******-524-2018 now to lock in your moving date and secure your Special Rate! We're here to make your move stress-free.";
                    $exitspot_lead_route = LeadRouting::where('lead_id', $leadsemaildata->lead_id)->where('route_status', 'sold')->get();
                    $exitspot = count($exitspot_lead_route);
                    if ($exitspot > 0) {
                        $lead_routing_exist_check = array();
                        if ($leadsemaildata->sold_type != 'premium') {
                            foreach ($exitspot_lead_route as $leadRvalue) {
                                $lead_routing_exist_check[] = $leadRvalue->campaign_id;
                            }
                        }

                        $exclusive_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->where('campaign_type_id', 2)->get()->first();
                        if (isset($exclusive_data)) {
                            $businessesdata = Business::where('business_id', $exclusive_data->business_id)->get()->first();
                            if (isset($businessesdata)) {
                                $checkSubscription = $this->checkSubscription($businessesdata->business_id);
                                if ($businessesdata->rely_number != null && $checkSubscription) {
                                    $plivonumber = $businessesdata->rely_number;
                                    // $text = 'Your interstatesmover.com confirmation # ' . $leadsemaildata->lead_id . '. You are matched with Movers ' . $businessesdata->display_name . '  and their Phone # ' . $plivonumber . '. Your Movers information available on ' . $mymovers_link;
                                    $text = "Your Movers information available on " . $mymovers_link;
                                }
                            }
                        }

                        if ($leadsemaildata->sold_type == "dual") {
                            $dual_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [1, 6, 8])->pluck('business_id')->toArray();
                            if (isset($dual_data)) {
                                $businessesdata = Business::whereIn('business_id', $dual_data)->get(['business_id', 'display_name', 'rely_number']);
                                if (isset($businessesdata) && count($businessesdata) > 0) {
                                    $strDual = '';
                                    $key = 0;
                                    foreach ($businessesdata as $value) {
                                        $checkSubscription = $this->checkSubscription($value->business_id);
                                        if ($value->rely_number != null && $checkSubscription) {
                                            if ($key > 0) {
                                                $strDual.= ' and ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            } else {
                                                $strDual.= $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            }
                                            $key++;
                                        }
                                    }
                                    if (!empty($strDual)) {
                                        $text = 'Your InterstatesMover.com confirmation # ' . $leadsemaildata->lead_id . '. Your Moving Quote with ' . $strDual . ' is ready as they are expecting your call.';
                                    }
                                }
                            }
                        }
                    }
                } else if ($pos3 !== false) {
                    $src = '+12622176922';
                    $mymovers_link = 'https://vanlinesmove.com/mymover?phone=' . base64_encode($leadsemaildata->phone);
                    // $text = 'Your vanlinesmove.com confirmation # ' . $leadsemaildata->lead_id . '. Call us at ******-455-2576 for any query. Your Movers information available on ' . $mymovers_link;
                    if ($hours_check_cron < 8 || $hours_check_cron >= 21) {
                        $text = "Your Vanlinesmove.com confirmation # " . $leadsemaildata->lead_id . ". You Moving quote with Vanlines Move is ready, please call ******-455-2576 in morning to get accurate quote.";
                    } else {
                        $text = "Hello " . ucwords($leadsemaildata->name) . ", your moving quote is ready! To secure the best rate, call us as soon as possible at ******-455-2576. We look forward to assisting you with your move! - Van Lines Move.";
                    }
                    $exitspot_lead_route = LeadRouting::where('lead_id', $leadsemaildata->lead_id)->where('route_status', 'sold')->get();
                    $exitspot = count($exitspot_lead_route);
                    if ($exitspot > 0) {
                        $lead_routing_exist_check = array();
                        if ($leadsemaildata->sold_type != 'premium') {
                            foreach ($exitspot_lead_route as $leadRvalue) {
                                $lead_routing_exist_check[] = $leadRvalue->campaign_id;
                            }
                        }

                        $exclusive_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [2, 4])->get()->first();
                        if (isset($exclusive_data)) {
                            $businessesdata = Business::where('business_id', $exclusive_data->business_id)->get()->first();
                            if (isset($businessesdata)) {
                                $checkSubscription = $this->checkSubscription($businessesdata->business_id);
                                if ($businessesdata->rely_number != null && $checkSubscription) {
                                    $plivonumber = $this->formatPhoneNumber($businessesdata->rely_number);
                                    // $text = 'Vanlinesmove.com confirmation # ' . $leadsemaildata->lead_id . '. You are matched with ' . $businessesdata->display_name . ' and their Phone # ' . $plivonumber . '. View at ' . $mymovers_link . '.';
                                    $text = 'Your Vanlinesmove.com confirmation # ' . $leadsemaildata->lead_id . '. You Moving quote with ' . $businessesdata->display_name . ' is ready, please call +1 ' . $plivonumber . ' as they are expecting your call.';
                                }
                            }
                        }

                        if ($leadsemaildata->sold_type == "dual") {
                            $dual_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [1, 6, 8])->pluck('business_id')->toArray();
                            if (isset($dual_data)) {
                                $businessesdata = Business::whereIn('business_id', $dual_data)->get(['business_id', 'display_name', 'rely_number']);
                                if (isset($businessesdata) && count($businessesdata) > 0) {
                                    $strDual = '';
                                    $key = 0;
                                    foreach ($businessesdata as $value) {
                                        $checkSubscription = $this->checkSubscription($value->business_id);
                                        if ($value->rely_number != null && $checkSubscription) {
                                            if ($key > 0) {
                                                $strDual.= ' and ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            } else {
                                                $strDual.= $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            }
                                            $key++;
                                        }
                                    }
                                    if (!empty($strDual)) {
                                        $text = 'Your Vanlinesmove.com confirmation # ' . $leadsemaildata->lead_id . '. Your Moving Quote with ' . $strDual . ' is ready as they are expecting your call.';
                                    }
                                }
                            }
                        }
                    }
                } else if ($pos4 !== false) {
                    $src = '+12622176922';
                    $mymovers_link = 'https://vanlinesmove.com/vmomymover?phone=' . base64_encode($leadsemaildata->phone);
                    // $text = 'Your vanlinesmove.com confirmation # ' . $leadsemaildata->lead_id . '. Call us at ******-455-2576 for any query. Your Movers information available on ' . $mymovers_link;
                    if ($hours_check_cron < 8 || $hours_check_cron >= 21) {
                        $text = "Your Vanlinesmove.com confirmation # " . $leadsemaildata->lead_id . ". You Moving quote with Vanlines Move is ready, please call ******-455-2576 in morning to get accurate quote.";
                    } else {
                        $text = "Hello " . ucwords($leadsemaildata->name) . ", your moving quote is ready! To secure the best rate, call us as soon as possible at ******-455-2576. We look forward to assisting you with your move! - Van Lines Move.";
                    }
                    $exitspot_lead_route = LeadRouting::where('lead_id', $leadsemaildata->lead_id)->where('route_status', 'sold')->get();
                    $exitspot = count($exitspot_lead_route);
                    if ($exitspot > 0) {
                        $lead_routing_exist_check = array();
                        if ($leadsemaildata->sold_type != 'premium') {
                            foreach ($exitspot_lead_route as $leadRvalue) {
                                $lead_routing_exist_check[] = $leadRvalue->campaign_id;
                            }
                        }

                        $exclusive_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [2, 4])->get()->first();
                        if (isset($exclusive_data)) {
                            $businessesdata = Business::where('business_id', $exclusive_data->business_id)->get()->first();
                            if (isset($businessesdata)) {
                                $checkSubscription = $this->checkSubscription($businessesdata->business_id);
                                if ($businessesdata->rely_number != null && $checkSubscription) {
                                    $plivonumber = $this->formatPhoneNumber($businessesdata->rely_number);
                                    // $text = 'Vanlinesmove.com confirmation # ' . $leadsemaildata->lead_id . '. You are matched with ' . $businessesdata->display_name . ' and their Phone # ' . $plivonumber . '. View at ' . $mymovers_link . '.';
                                    $text = 'Your Vanlinesmove.com confirmation # ' . $leadsemaildata->lead_id . '. You Moving quote with ' . $businessesdata->display_name . ' is ready, please call +1 ' . $plivonumber . ' as they are expecting your call.';
                                }
                            }
                        }

                        if ($leadsemaildata->sold_type == "dual") {
                            $dual_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [1, 6, 8])->pluck('business_id')->toArray();
                            if (isset($dual_data)) {
                                $businessesdata = Business::whereIn('business_id', $dual_data)->get(['business_id', 'display_name', 'rely_number']);
                                if (isset($businessesdata) && count($businessesdata) > 0) {
                                    $strDual = '';
                                    $key = 0;
                                    foreach ($businessesdata as $value) {
                                        $checkSubscription = $this->checkSubscription($value->business_id);
                                        if ($value->rely_number != null && $checkSubscription) {
                                            if ($key > 0) {
                                                $strDual.= ' and ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            } else {
                                                $strDual.= $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            }
                                            $key++;
                                        }
                                    }
                                    if (empty($strDual)) {
                                        $text = 'Your Vanlinesmove.com confirmation # ' . $leadsemaildata->lead_id . '. Your Moving Quote with ' . $strDual . ' is ready as they are expecting your call.';
                                    }
                                }
                            }
                        }
                    }
                } else {
                    date_default_timezone_set('America/New_York');
                    $currentDate = date("Y-m-d H:i:s");
                    LeadCustomerSmsLog::create([
                        'sms_subject' => ($type == 'cron') ? "Lead Confirmation Message (Cron)" : "Lead Confirmation Message",
                        'response' => 'other source lead ==' . $origin->lead_source_name,
                        'lead_id' => $leadsemaildata->lead_id,
                        'created_at' => $currentDate
                    ]);
                    return 'other source lead ==' . $origin->lead_source_name;
                }

                // Areawise SMS Code
                if ($pos !== false || $pos3 !== false || $pos4 !== false ) {
                    $src = GeneralNumberConfig::getDialNumberByAreacode($leadsemaildata->phone)['vm_areacode_outbound'];
                    $src = '+'.$src;
                }

                if ($hours_check_cron >= 9 && $hours_check_cron < 19 && $weekDayNumber < 6) {
                    if (strlen($callItNumber) > 10) {
                        $callItNumber = substr($callItNumber, -10);
                    }
                    $text = "Hi " . ucwords($leadsemaildata->name) . ", thanks for your moving request! Your $200 discount is reserved. We’ll call you from 📞 +1 ".$this->formatPhoneNumber($callItNumber)." to confirm. Can’t talk now? Just reply “Y” to verify — and we’ll send your quotes.";
                }

                // For Custom Encrypt and Decrypt Method Start
                $endDecObj = new Helper;
                $general_variable_3 = Helper::checkServer()['general_variable_3'];
                $general_variable_4 = Helper::checkServer()['general_variable_4'];
                $decVariable3 = $endDecObj->customeDecryption($general_variable_3);
                $decVariable4 = $endDecObj->customeDecryption($general_variable_4);
                // For Custom Encrypt and Decrypt Method End
                $to = '+1' . $leadsemaildata->phone;
                $from = $src;
                if ($text != "") {
                    $text .= " " . $smsString;
                }
                $body = $text;
                // $uri = 'https://api.thinq.com/account/22374/product/origination/sms/send';
                $str = 'milan:3f7b24833410b9c7682d8c80bcd06442d63d02ad';
                $auth_token = base64_encode($str);
                // post string (phone number format= +*********** ), case matters
                // $fields = '&To=' . urlencode($to) .
                //     '&From=' . urlencode($from) .
                //     '&Body=' . urlencode($body);

                    $fields =    array(
                        'from_did' => $from,
                        'to_did' => $to,
                        'message' => $body
                    );

                // $sms_response = $endDecObj->sms_validation($to, $decVariable3, $decVariable4); //Added By HJ On 18-01-2022 For Check SMS Number Valid Or Not
                // $request = array("To" => $to, "From" => $from, "Body" => $body, "File" => "ConfirmationSms");
                // $endDecObj->createEmailSMSValidationLog($leadId, 0, 10, json_encode($request), json_encode($sms_response), 1, 0); //Added By HJ On 18-01-2022 For Create Email and SMS Validation Log
                // if (isset($sms_response['status']) && $sms_response['status'] > 0) {
                    $jsonPostData = json_encode($fields, true);
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, 'https://api.thinq.com/account/22374/product/origination/sms/send');
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($ch, CURLOPT_POST, 1);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonPostData);

                    $headers = array();
                    $headers[] = 'Content-Type: application/json';
                    $headers[] = 'Authorization: Basic ' . $auth_token;
                    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

                    if (curl_errno($ch)) {
                        echo 'Error:' . curl_error($ch);
                    }

                    $result = curl_exec($ch);
                    curl_close($ch);
                    $response = json_decode($result);
                //DB::table('test')->insert(array('result' => $leadsemaildata->id . '-' . $result));
                $response = json_decode($result);
                date_default_timezone_set('America/New_York');
                $smsLogData = array();
                $smsLogData['lead_id'] = $leadsemaildata->lead_id;
                $smsLogData['from_number'] = $from;
                $smsLogData['to_number'] = $to;
                $smsLogData['lead_type'] = 'normal';
                $smsLogData['sms_subject'] = ($type == 'cron') ? "Lead Confirmation Message (Cron)" : "Lead Confirmation Message";
                $smsLogData['request'] = $body;
                // $smsLogData['response'] = json_encode([]);
                $smsLogData['response'] = json_encode($response);
                $smsLogData['created_at'] = date("Y-m-d H:i:s");
                // if ($response->status == 'queued') {
                //     $smsLogData['status'] = 'success';
                // }
                $smsLogData['status'] = "success";
                LeadCustomerSmsLog::create($smsLogData);
                $leadactiviyobj = new LeadActivity();
                $leadactiviyobj->createActivity($leadId, 2);
                CommonFunctions::createDevDebugLog(1, $leadId . ' Success ' . date("Y-m-d H:i"));
            } else {
                date_default_timezone_set('America/New_York');
                $currentDate = date("Y-m-d H:i:s");
                LeadCustomerSmsLog::create([
                    'sms_subject' => ($type == 'cron') ? "Lead Confirmation Message (Cron)" : "Lead Confirmation Message",
                    'response' => 'Lead Id' . $leadsemaildata->lead_id . ' and DNC SMS ' . $leadsemaildata->is_dnc_sms,
                    'lead_id' => $leadsemaildata->lead_id,
                    'created_at' => $currentDate
                ]);
                CommonFunctions::createDevDebugLog(1, $leadId . '==Lead Id== SMS Not send because DNC SMS flag is.==  ' . $checkPhone . "==Date time==" . date("Y-m-d H:i"));
                return 'SMS not send because DNC SMS flag is ' . $checkPhone;
            }
            return 'success';
        } catch (Exception $ex) {
            CommonFunctions::createDevDebugLog(1, $ex);
        }
    }

    public function getFromstateToTimezone($leadfromstate)
    {
        try {
            $tz_states = array(
                'America/Anchorage' => array('AK'),
                'America/Boise' => array('ID'),
                'America/Chicago' => array('AL', 'AR', 'IL', 'IA', 'KS', 'LA', 'MN', 'MS', 'MO', 'NE', 'OK', 'SD', 'TN', 'TX', 'WI'),
                'America/Denver' => array('CO', 'MT', 'NM', 'UT', 'WY'),
                'America/Detroit' => array('MI'),
                'America/Indiana/Indianapolis' => array('IN'),
                'America/Kentucky/Louisville' => array('KY'),
                'America/Los_Angeles' => array('CA', 'NV', 'OR', 'WA'),
                'America/New_York' => array('CT', 'DE', 'FL', 'GA', 'ME', 'MD', 'MA', 'NH', 'NJ', 'NY', 'NC', 'OH', 'PA', 'RI', 'SC', 'VT', 'VA', 'DC', 'WV'),
                'America/North_Dakota/Center' => array('ND'),
                'America/Phoenix' => array('AZ'),
                'Pacific/Honolulu' => array('HI'),
            );
            foreach ($tz_states as $key => $value) {
                if (in_array($leadfromstate, $value)) {
                    return $key;
                }
            }
        } catch (Exception $ex) {
            Log::info($ex);
        }
    }

    function formatPhoneNumber($number) {
        // Remove any non-numeric characters
        $number = preg_replace('/\D/', '', $number);

        // Check if the number has exactly 10 digits
        if (strlen($number) === 10) {
            // Format the number
            $formattedNumber = sprintf('%s-%s-%s',
                substr($number, 0, 3),  // Area code
                substr($number, 3, 3),  // Prefix
                substr($number, 6, 4)   // Line number
            );
            return $formattedNumber;
        } else {
            // Return an error or handle invalid number
            return 'Invalid number';
        }
    }

    public function checkSubscription($businessId) {
        $subscriptionPlan = Subscription::where('business_id', $businessId)
            ->where('is_active', 'yes')
            ->first();
        if($subscriptionPlan) {
            $planDetails = MstSubscriptionPlan::where('subscription_plan_id', $subscriptionPlan->subscription_plan_id)->first();
            if($planDetails) {
                if($planDetails->subscription_plan_id == 2) {
                    $callCountN = $planDetails->call_count;
                    if($callCountN > 0) {
                        $startOfMonth = Carbon::now()->startOfMonth();
                        $endOfMonth = Carbon::now()->endOfMonth();
                        $leadCallCount = LeadCall::where('transfer_type', 'businessrely')
                            ->where('business_id', $businessId)
                            ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
                            ->count();

                        if($leadCallCount >= $callCountN) {
                            return 0;
                        }
                    }
                }
            }
        } else {
            return 0;
        }

        return 1;
    }
}
