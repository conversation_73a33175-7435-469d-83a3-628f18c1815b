<?php

namespace App\Models\PingPost;

use App\Models\PingPost\PingPostPayout;
use App\Models\Seller\PingPostResponse;
use App\Models\Lead\LeadRoutingLog;
use App\Models\Lead\LeadRouting;
use App\Models\Lead\LeadMoving;
use App\Models\Business\Business;
use App\Helpers\Helper;
use Exception;
use DB;
use App\Models\Lead\LeadLandingPage;

class Zap
{
    private $apiKey     = 'kd7W5ZCo0Bh4HlQP8yMbBcpcLQ5j5R';
    private $leadId     = 0;
    private $campaignId = 0;
    private $isTest     = 0;
    private $pingId     = 0;

    public function ping($leadData, $campaignId, $remainingSlotArray, $dualSoldBusinessIdArray, $trioSoldBusinessIdArray, $premiumSoldCampIdArray) {
        $this->campaignId = $campaignId;
        $responseData = $finalArray = array();
        try {
            //echo "<pre>";print_r($leadObj);die;
            if (count($leadData) > 0) {
                $leadId = $leadData[0]['lead_id'];
                //var_dump($this->checkCampaignStatus());
                $responseData = $this->isAdmissiblePing($leadId, $campaignId);
                if (count($responseData) == 0 && $this->checkCampaignStatus($campaignId)) {
                    $getMoveInfo = LeadMoving::where('lead_id', $leadId)->get()->toArray();
                    if (count($getMoveInfo) > 0) {
                        //echo "<pre>";print_r($leadData);die;
                        if (strpos(url()->current(), 'linkup.software') !== false) {
                            $this->isTest = 0;
                        } else if (strpos(url()->current(), '54.67.5.60') !== false || strpos(url()->current(), 'tweetstage.tweetsoftware.co') !== false) {
                            $this->isTest = 1;
                        }

                        if ($leadData[0]['is_verified'] == "yes") {
                            $leadData[0]['is_verified'] = "1";
                        } else {
                            $leadData[0]['is_verified'] = "0";
                        }
                        $body = array(
                            'api_key' => $this->apiKey,
                            'lead_type' => "1",
                            'from_zip' => sprintf("%05d", $getMoveInfo[0]['from_zipcode']),
                            'to_zip' => sprintf("%05d", $getMoveInfo[0]['to_zipcode']),
                            'move_size' => $getMoveInfo[0]['move_size_id'],
                            'move_date' => date("m/d/Y", strtotime($getMoveInfo[0]['move_date'])),
                            /*'verification_method' => 1,*/
                            "is_verified" => (string)$leadData[0]['is_verified'],
                            "verification_method" => "sms",
                            "is_test" => $this->isTest,
                            "ip" => "",
                            "browser" => "",
                            "slot_available" => $remainingSlotArray,
                            "dual_sold_companies_id" => $dualSoldBusinessIdArray,
                            "trio_sold_companies_id" => $trioSoldBusinessIdArray,
                            "premium_sold_companies_id" => $premiumSoldCampIdArray
                        );
                        // zap api url
                        try {
                            $url = Helper::checkServer()['zap_url'] .'/pingsubmit';
                            $responseData = json_decode($this->postMethod($url, json_encode($body)), true);
                        } catch(Exception $e) {
                            $responseData = $e->getMessage();
                        }
                        //echo "<pre>Response Ping==<br>";print_r($responseData);die;

                        LeadRoutingLog::create([
                            'lead_id' => $leadId,
                            'campaign_id' => $this->campaignId,
                            'request' => json_encode($body),
                            'response' => json_encode($responseData),
                            'created_at' => date("Y-m-d H:i:s"),
                        ]);

                        $payout = $i = 0;
                        if (isset($responseData)) {
                            $this->leadId = $leadId;
                            $this->pingId = $responseData['ping_id']; 
                            $payout = $responseData['buying_slot_payout'];
                            /*foreach ($payout as $key => $value) {
                                $pingType = 'exclusive';
                                if ($i > 0) {
                                    $pingType = 'shared';
                                }
                                $finalArray[] = [
                                    'lead_id' => $this->leadId,
                                    'campaign_id' => $this->campaignId,
                                    'ping_id' => $this->pingId,
                                    'ping_request' => json_encode($body),
                                    'ping_response' => json_encode($responseData),
                                    'is_request' => 'ping',
                                    'created_at' => date("Y-m-d H:i:s"),
                                    'payout' => $value,
                                    'ping_slot' => $key,
                                    'ping_type' => $pingType,
                                    'is_win' => 'no',
                                ];
                                $i++;
                            }*/

                            foreach ($payout as $pingType => $slotArray) {
                                foreach ($slotArray as $pingSlot => $value) {
                                    $finalArray[] = [
                                        'lead_id' => $this->leadId,
                                        'campaign_id' => $this->campaignId,
                                        'ping_id' => $this->pingId,
                                        'ping_request' => json_encode($body),
                                        'ping_response' => json_encode($responseData),
                                        'is_request' => 'ping',
                                        'created_at' => date("Y-m-d H:i:s"),
                                        'payout' => $value,
                                        'ping_slot' => $pingSlot,
                                        'ping_type' => $pingType,
                                        'is_win' => 'no',
                                    ];
                                    $i++;
                                }
                            }
                               
                            if (count($finalArray) > 0) {
                                PingPostPayout::insert($finalArray);
                            }
                        }
                        return $responseData;
                    }
                }
            }
        } catch (Exception $e) {
            dd($e->getMessage());
        }
        return $responseData;
    }

    public function post($leadData, $campaignId, $leadType) {
        if (count($leadData) > 0) {
            $finalArray = [];
            //echo "<pre>";print_r($leadData);die;
            $leadId = $leadData[0]['lead_id'];
            $leadName = $leadData[0]['name'];
            $email = $leadData[0]['email'];
            $phone = $leadData[0]['phone'];
            $this->campaignId = $campaignId;
            if ($this->isAdmissiblePost($leadId, $campaignId) && $this->checkCampaignStatus($campaignId)) {
                // Fetch PingId from ws_payout
                $wsPayoutData = PingPostPayout::where(['lead_id' => $leadId, 'campaign_id' => $this->campaignId, 'ping_type' => $leadType])->get(['ping_post_payout_id', 'ping_id', 'ping_slot'])->toArray();
                $getMoveInfo = LeadMoving::where('lead_id', $leadId)->get()->toArray();
                //echo "<pre>";print_r($leadData);die;
                if (count($wsPayoutData) > 0 && count($getMoveInfo) > 0) {
                    $first_name = $last_name = '';
                    $nameData = explode(" ", $leadName);
                    if (isset($nameData[0]) && !empty($nameData[0])) {
                        $first_name = $nameData[0];
                    }
                    if (isset($nameData[1]) && !empty($nameData[1])) {
                        $last_name = $nameData[1];
                    }
                    $firstName = $lastName = 'Test';
                    if (strpos(url()->current(), 'linkup.software') !== false) {
                        $this->isTest = 0;
                        $firstName = $first_name;
                        $lastName = $last_name;
                    } else if (strpos(url()->current(), '54.67.5.60') !== false || strpos(url()->current(), 'tweetstage.tweetsoftware.co') !== false) {
                        $this->isTest = 1;
                        $firstName = $first_name;
                        $lastName = $last_name;
                    }

                    if ($leadData[0]['is_verified'] == "yes") {
                        $leadData[0]['is_verified'] = "1";
                    } else {
                        $leadData[0]['is_verified'] = "0";
                    }

                    $body = array(
                        'api_key' => $this->apiKey,
                        'ping_id' => $wsPayoutData[0]['ping_id'],
                        'firstname' => $firstName,
                        'lastname' => $lastName,
                        'phone' => $phone,
                        'email' => $email,
                        'from_zip' => sprintf("%05d", $getMoveInfo[0]['from_zipcode']),
                        'to_zip' => sprintf("%05d", $getMoveInfo[0]['to_zipcode']),
                        'move_date' => date("m/d/Y",strtotime($getMoveInfo[0]['move_date'])),
                        'move_size' => $getMoveInfo[0]['move_size_id'],
                        'lead_type' => "1",
                        "is_verified" => (string)$leadData[0]['is_verified'],
                        "verification_method" => "sms",
                        "is_test" => $this->isTest,
                        "sold_type" => $leadType,
                    );
                    try {
                        $url = Helper::checkServer()['zap_url'] .'/postsubmit';
                        $responseData = json_decode($this->postMethod($url, json_encode($body)), true);
                    } catch(Exception $e) {
                        $responseData = $e->getMessage();
                    }
                    //echo "<pre>Response Post==<br>";print_r($responseData);die;

                    LeadRoutingLog::create([
                        'lead_id' => $leadId,
                        'campaign_id' => $this->campaignId,
                        'request' => json_encode($body),
                        'response' => json_encode($responseData),
                        'created_at' => date("Y-m-d H:i:s"),
                    ]);

                    if (isset($responseData['status']) && trim($responseData['status']) == 'Accepted') {
                        PingPostPayout::where(['lead_id' => $leadId, 'campaign_id' => $this->campaignId, 'ping_type' => $leadType])->update([
                            'is_win' => "yes"
                        ]);
                        foreach ($responseData['lm_sold_company_id'] as $key => $value) {
                            $finalArray[] = [
                                'ping_post_payout_id' => $wsPayoutData[0]['ping_post_payout_id'],
                                'lead_id' => $leadId,
                                'campaign_id' => $key,
                                /*'campaign_name' => $responseData['LS_sold_company_id'][$key],*/
                                'payout' => $value,
                                'created_at' => date("Y-m-d H:i:s")
                            ];
                        }
                        if (count($finalArray) > 0) {
                            PingPostResponse::insert($finalArray);
                        }
                        $soldSlot = $responseData['sold_slot'];
                        $setColumn = "";
                        if ($leadType == 'shared') {
                            $setColumn = ", `remaining_slot`=(`remaining_slot`-$soldSlot)";
                        }
                        if ($leadType == 'dual') {
                            $setColumn = ", `remaining_slot`=(`remaining_slot`-$soldSlot)";
                        }
                        if ($leadType == 'trio') {
                            $setColumn = ", `remaining_slot`=(`remaining_slot`-$soldSlot)";
                        }
                        DB::select("UPDATE `lead` set `sold_type`='".$leadType."' $setColumn where `lead_id`=$leadId");
                        return true;
                    }
                }
            }
        }
        return false;
    }

    public function isAdmissiblePing($leadId, $campaignId) {
        $this->campaignId           = $campaignId;
        $returnArr                  = array();
        $payoutArr                  = array();
        $wsPayout                   = PingPostPayout::where('lead_id', $leadId)->where('campaign_id', $this->campaignId)->where('is_request', 'ping')->get()->toArray();
        //echo "<pre>"; print_r($wsPayout); die;
        if (count($wsPayout) > 0) {
            for ($i=0; $i < count($wsPayout); $i++) {
                $payoutArr[$wsPayout[$i]['ping_slot']] = $wsPayout[$i]['payout'];
            }
            $payoutArr              = json_decode($wsPayout[0]['ping_response']);
            $returnArr['status']    = "Accepted";
            $returnArr['ping_id']   = $wsPayout[0]['ping_id'];
            $returnArr['buying_slot_payout'] = $this->objectToArray($payoutArr->buying_slot_payout);
            $returnArr['error']     = "";
            return $returnArr;
        } else {
            return $returnArr;
        }
    }

    // Recursively convert objects to arrays
    function objectToArray($data) {
        if (is_object($data)) {
            $data = get_object_vars($data);
        }

        if (is_array($data)) {
            return array_map([$this, 'objectToArray'], $data);
        }

        return $data;
    }

    public function isAdmissiblePost($leadId, $campaignId) {
        $this->campaignId           = $campaignId;
        $wsRouting                  = LeadRouting::where('lead_id', $leadId)->where('campaign_id', $this->campaignId)->where('route_status', 'sold')->first();
        if ($wsRouting) {
            return false;
        } else {
            return true;
        }
    }

    public function checkCampaignStatus($campaignId) {
        $this->campaignId           = $campaignId;
        $status                     = 'yes';
        $businessCampaign           = Business::with([
            'businessCampaign' => function ($query) use ($status) {
                return $query->where('is_active', '=', $status)->where('campaign_id', $this->campaignId);
            }])->where('status', 'active')->where('buyer_type_id', 3)->get();
        //dd(count($businessesCampaign));
        if (count($businessCampaign) > 0) {
            return true;
        } else {
            return false;
        }
    }

    public static function getMethod($url, $params) {
        try {
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => $url . '?' . http_build_query($params),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
            ));
            $response = curl_exec($curl);
            curl_close($curl);
            return $response;
        } catch (Exception $e) {
            //echo "error in recording";
        }
    }

    public static function postMethod($url, $postFields) {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_TIMEOUT, 50);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
            $response = curl_exec($ch);
            curl_close($ch);

            return $response;
        } catch (Exception $e) {
            //echo "error in recording";
        }
    }
}
