<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * Indicates whether the XSRF-TOKEN cookie should be set on the response.
     *
     * @var bool
     */
    protected $addHttpCookie = true;

    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array
     */
    protected $except = [
        "outboundcallhangup",
        "outboundcallanswer",
        "pingsubmit",
        "postsubmit",
        "pingsubmitnew",
        "postsubmitnew",
        "moverlogin",
        "movervalidatebusiness",
        "moverleadcount",
        "moverbusiness",
        "movercampaign",
        "movercampaignnew",
        "movermarketplacepopup",
        "moverleaddata",
        "moverleaddatasummary",
        "movertalkedbooked",
        "getpaymentcard",
        "getbusinesspaymentcardsetup",
        "getcampaignpaymentcardsetup",
        "paymentcardcharge",
        "paymentcardadd",
        "movermarketplaceactiveinactive",
        "movermarketplacelead",
        "movermarketplaceleaddata",
        "movermarketplaceleaddatanew",
        "marketplaceleadtest",
        "movermarketplaceleaddatasummary",
        "movermarketplacedeliveryagain",
        "moverapplymarketplacediscount",
        "movermarketplaceleadcharge",
        "moverpaymenthistory",
        "moverpaymenthistorydata",
        "moverpaymenthistorypdf",
        "moverpaymenthistoryreport",
        "moverchathistory",
        "movermessagelinkupsalesrep",
        "moversetting",
        "moversettingupdate",
        "moverorganiccampaign",
        "movercampaignupdate",
        "movercampaignupdatenew",
        "movercampaignpause",
        "moverrecurrent",
        "moverrecurrentadd",
        "moverrecurrentupdate",
        "moversubscription",
        "moversetprimarycard",
        "tokoutboundanswer",
        "hangupRep",
        "callforward",
        "organiccallforward",
        "digitselectnewinbound",
        "digitselectcommon",
        "digitselectnew",
        "digitselectnewtok",
        "tokoutboundtocampaign",
        "signpayapi",
        "contractsignapi",
        "automatedinboundcall",
        "digitselect",
        "transferincomingcalltosip",
        "hangupacceptcall",
        "sellerpayout",
        "sellerleaddata",
        "sellerpostbadleadstatus",
        "inboundcallcustaccept",
        "organicinboundcallaccept",
        "smsreceivedcommio",
        "smsreceivedplivo",
        "callToCustomer",
        "answer",
        "digitselect",
        "hangup",
        "businessinterfacephone",
        "autooutboundcallhangup",
        "autooutboundcallanswer",
        "autooutbounddigitselect",
        "inboundsmsseededlead",
        "inboundcallseededlead",
        "saveDnc",
        "OBincomoingsmscommio",
        "OBincomoingsmsplivo",
        "addobnumberintable",
        "moverbusinessemailexistcheck",
        "businessresetemailsend",
        "checkbusinesstoken",
        "saveresetpassword",
        "getlspartneredbusiness",
        "whatsappchat-receive-message",
        "b2binboundcallcustaccept",
        "b2bautomatedinboundcall",
        "b2binboundhangup",
        "gettlmpartneredbusiness",
        "checkbusinesssigned",
        "signbusinesscontractapi",
        "monthlyinvoicepdf",
        "playIVR",
        "testcalltorep",
        "testcalltocus",
        "testcalltobridge",
        "testcalltoreptrans",
        "testcalltocustrans",
        "testcalltomovertrans",
        "testcalltocustconf",
        "testcalltocustconftwo",
        "conferencecall"
    ];
}
