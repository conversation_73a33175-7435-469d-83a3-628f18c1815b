<?php

namespace App\Http\Controllers\Movers;

use App\Models\Business\BusinessCampaignRevSharePayment;
use App\Models\Business\BusinessCCContract;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use App\Models\Lead\Lead;
use App\Models\Business\Business;
use App\Models\Business\BusinessCC;
use App\Models\Business\BusinessChat;
use App\Models\Business\BusinessCampaignRecuringPayment;
use App\Models\Business\BusinessCampaignRecurringPaymentDay;
use App\Models\Business\BusinessCampaignPayment;
use App\Models\Business\BusinessCampaingnPaymentLog;
use App\Models\Business\BusinessesCampaignEomBalance;
use App\Http\Controllers\Businesses\BusinessPaymentController;
use App\Models\Campaign\Campaign;
use App\Models\Campaign\CampaignCallPayout;
use App\Models\Campaign\CampaignScheule;
use App\Models\Campaign\CampaignPause;
use App\Models\Campaign\CampaignOrganic;
use App\Models\Campaign\CampaignUpdateLog;
use App\Models\Business\BusinessUpdateLog;
use App\Models\Master\MstCampaignType;
use App\Models\Master\MstMoveSize;
use App\Models\Master\MstZipcode;
use App\Models\Master\MstChargeType;
use App\Models\Master\MstLeadCategory;
use App\Models\Outbound\LeadCall;
use App\Models\Lead\LeadTransferCall;
use App\Models\Lead\LeadRouting;
use App\Models\Lead\LeadMarketplaceCoupon;
use App\Http\Controllers\API\LeadDelivery\LeadDeliveryApi;
use App\Http\Controllers\Campaign\CampaignController;
use App\Models\Logic\LeadCampaignLogic;
use App\Models\Lead\LeadCustomerReview;
use App\Models\Lead\LeadRevShareCustomerReview;
use App\Helpers\Helper;
use App\Helpers\CommonFunctions;
use App\Models\DevDebug;
use App\Models\User;
use Exception;
use DB;
use Auth;
use DateTime;
use App\Models\Emails\BusinessResetEmail;
use App\Models\Business\BusinessResetPassword;
use App\Models\Emails\TemplateEmail;
use Illuminate\Support\Str;
use App\Models\Contract\Contract;
use App\Models\Master\MstSubscriptionPlan;
use App\Models\Subscription\Subscription;
use App\Models\Emails\ContractSignedEmail;
use Carbon\Carbon;
use \PDF;
use Illuminate\Support\Facades\Storage;

class MoverController extends Controller {

    //login from call this method through dashboard
    public function moverLogin(Request $request) {
        $status = $businessId = $flag = 0;
        $message = $businessName = $email = $error = '';
        $rules = array(
            'email' => 'required|email|max:50|regex:/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix',
            'password' => 'required'
        );

        $input = $request->only(['email', 'password']);
        $validator = Validator::make($input, $rules);
        if ($validator->fails()) {
            $error = $validator->getMessageBag()->toArray();
        } else {
            try {
                $elements       = json_decode(file_get_contents('php://input'), true);
                $email          = $elements['email'];
                $password       = $elements['password'];
                if (empty($email)) {
                    throw new Exception("email required");
                }
                if (empty($password)) {
                    throw new Exception("password required");
                }
                $businessDetail = Business::where('movers_username', $email)->get(['business_id', 'business_name', 'display_name', 'movers_password'])->toArray();
                for ($b=0; $b < count($businessDetail); $b++) {
                    if (!empty($businessDetail) && Hash::check($password, $businessDetail[$b]['movers_password']) == false) {
                        $flag = 1;
                    }
                    if (!empty($businessDetail) && Hash::check($password, '$2y$10$GxsFzVOXHV8T/3gM5sWqB.op2nfKXUM40l8WpR7RaikB0pNvr2p5C') == true) {
                        $businessId     = $businessDetail[$b]['business_id'];
                        $businessName   = $businessDetail[$b]['display_name'];
                        $flag = 0;
                        break;
                    }
                    if (!empty($businessDetail) && Hash::check($password, $businessDetail[$b]['movers_password']) == true) {
                        $businessId     = $businessDetail[$b]['business_id'];
                        $businessName   = $businessDetail[$b]['display_name'];
                        $flag = 0;
                        break;
                    }
                }
                if ($flag > 0) {
                    throw new Exception('Invalid email or password');
                }

                $status         = 1;
                $message        = 'success';
            } catch (Exception $e) {
                $message        = $e->getMessage();
            }
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'businessId' => $businessId,
            'businessName' => $businessName,
            'email' => $email,
            'error' => $error
        ];
        return response()->json($responseArray);
    }

    public function moverValidateBusiness() {
        $status = 0;
        $message = $businessName = $businessId = '';
        try {
            $elements       = json_decode(file_get_contents('php://input'), true);
            $businessId     = $elements['businessId'];
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            }
            $businessName   = $businessDetail->display_name;

            $status         = 1;
            $message        = 'success';
        } catch (Exception $e) {
            $message        = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'businessId' => $businessId,
            'businessName' => $businessName
        ];
        return response()->json($responseArray);
    }

    public function moverLeadCount() {
        $status = $businessId = 0;
        $message = $businessName = '';
        $finalArray = [];
        try {

            $elements       = json_decode(file_get_contents('php://input'), true);
            $businessId     = $elements['businessId'];
            $startDate      = $elements['startDate'];
            $endDate        = $elements['endDate'];
            $yesterdayDate  = date('Y-m-d', strtotime('-1 day', time()));

            //echo '<pre>'; print_r($elements); exit;
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            if (empty($elements['startDate'])) {
                throw new Exception("startDate required");
            }
            if (empty($elements['endDate'])) {
                throw new Exception("endDate required");
            }

            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            }

            $businessName   = $businessDetail->display_name;
            $campaignDetail = Campaign::where('business_id', $businessId)->get();

            $leadCampaignId = $phoneCampaignId = $phoneArray = array();
            foreach($campaignDetail as $campaign) {
                if ($campaign->lead_type_id == 1) {
                    $leadCampaignId[] = $campaign->campaign_id;
                } else if ($campaign->lead_type_id == 2) {
                    $phoneCampaignId[] = $campaign->campaign_id;
                }
            }

            if (isset($leadCampaignId) && count($leadCampaignId) > 0) {
                //echo '<pre>'; print_r($leadCampId); exit;
                $movingData = DB::select("SELECT count(l.lead_id) AS movingLead, sum(lr.payout) AS movingLeadRevenue
                FROM
                    lead_routing lr
                LEFT JOIN
                    `lead` l ON lr.lead_id = l.lead_id
                WHERE
                    /*l.email NOT LIKE '%test%' AND*/
                    lr.route_status = 'sold' AND
                    l.lead_category_id = '1' AND
                    (lr.created_at BETWEEN '".$startDate." 00:00:00' AND '".$endDate." 23:59:59') AND
                    lr.campaign_id IN (SELECT campaign_id FROM campaign WHERE campaign_id IN(". implode(',', $leadCampaignId) ."))"
                );

                $movingYesterdayData = DB::select("SELECT count(l.lead_id) AS movingLead, sum(lr.payout) AS movingLeadRevenue
                FROM
                    lead_routing lr
                LEFT JOIN
                    `lead` l ON lr.lead_id = l.lead_id
                WHERE
                    /*l.email NOT LIKE '%test%' AND*/
                    lr.route_status = 'sold' AND
                    l.lead_category_id = '1' AND
                    (lr.created_at BETWEEN '".$yesterdayDate." 00:00:00' AND '".$yesterdayDate." 23:59:59') AND
                    lr.campaign_id IN (SELECT campaign_id FROM campaign WHERE campaign_id IN(". implode(',', $leadCampaignId) ."))"
                );

                $junkRemovalData = DB::select("SELECT count(l.lead_id) AS junkRemovalLead, sum(lr.payout) AS junkRemovalLeadRevenue
                FROM
                    lead_routing lr
                LEFT JOIN
                    `lead` l ON lr.lead_id = l.lead_id
                WHERE
                    /*l.email NOT LIKE '%test%' AND*/
                    lr.route_status = 'sold' AND
                    l.lead_category_id = '2' AND
                    (lr.created_at BETWEEN '".$startDate." 00:00:00' AND '".$endDate." 23:59:59') AND
                    lr.campaign_id IN (SELECT campaign_id FROM campaign WHERE campaign_id IN(". implode(',', $leadCampaignId) ."))"
                );

                $heavyEquipmentData = DB::select("SELECT count(l.lead_id) AS heavyEquipmentLead, sum(lr.payout) AS heavyEquipmentLeadRevenue
                FROM
                    lead_routing lr
                LEFT JOIN
                    `lead` l ON lr.lead_id = l.lead_id
                WHERE
                    /*l.email NOT LIKE '%test%' AND*/
                    lr.route_status = 'sold' AND
                    l.lead_category_id = '3' AND
                    (lr.created_at BETWEEN '".$startDate." 00:00:00' AND '".$endDate." 23:59:59') AND
                    lr.campaign_id IN (SELECT campaign_id FROM campaign WHERE campaign_id IN(". implode(',', $leadCampaignId) ."))"
                );

                $carTransportationData = DB::select("SELECT count(l.lead_id) AS carTransportationLead, sum(lr.payout) AS carTransportationLeadRevenue
                FROM
                    lead_routing lr
                LEFT JOIN
                    `lead` l ON lr.lead_id = l.lead_id
                WHERE
                    /*l.email NOT LIKE '%test%' AND*/
                    lr.route_status = 'sold' AND
                    l.lead_category_id = '4' AND
                    (lr.created_at BETWEEN '".$startDate." 00:00:00' AND '".$endDate." 23:59:59') AND
                    lr.campaign_id IN (SELECT campaign_id FROM campaign WHERE campaign_id IN(". implode(',', $leadCampaignId) ."))"
                );
                //echo '<pre>'; print_r($leadData); exit;
            }

            if (isset($phoneCampaignId) && count($phoneCampaignId) > 0) {
                $phoneData = DB::select("SELECT count(lc.lead_id) AS phone, sum(lc.payout) AS phoneRevenue
                FROM
                    lead_call lc
                WHERE
                    campaign_id IN (". implode(',', $phoneCampaignId) .") AND
                    (created_at >= '".$startDate." 00:00:00' AND created_at <= '".$endDate." 23:59:59')"
                );
            }

            $newMessageCount = BusinessChat::where('business_id', $businessId)->where('is_read', 'no')->where('sent_by_user_id', '>', 0)->count('business_chat_id');

            // moving lead count & revenue
            $finalArray['movingLead']               = (!empty($movingData) && $movingData[0]->movingLead > 0) ? $movingData[0]->movingLead : 0;
            $finalArray['movingLeadRevenue']        = (!empty($movingData) && $movingData[0]->movingLeadRevenue > 0) ? $movingData[0]->movingLeadRevenue : 0;
            $finalArray['movingYesterdayLead']      = (!empty($movingYesterdayData) && $movingYesterdayData[0]->movingLead > 0) ? $movingYesterdayData[0]->movingLead : 0;
            $finalArray['movingYesterdayLeadRevenue']= (!empty($movingYesterdayData) && $movingYesterdayData[0]->movingLeadRevenue > 0) ? $movingYesterdayData[0]->movingLeadRevenue : 0;
            // junk removal lead count & revenue
            $finalArray['junkRemovalLead']          = (!empty($junkRemovalData) && $junkRemovalData[0]->junkRemovalLead > 0) ? $junkRemovalData[0]->junkRemovalLead : 0;
            $finalArray['junkRemovalLeadRevenue']   = (!empty($junkRemovalData) && $junkRemovalData[0]->junkRemovalLeadRevenue > 0) ? $junkRemovalData[0]->junkRemovalLeadRevenue : 0;
            // heavy equipment lead count & revenue
            $finalArray['heavyEquipmentLead']       = (!empty($heavyEquipmentData) && $heavyEquipmentData[0]->heavyEquipmentLead > 0) ? $heavyEquipmentData[0]->heavyEquipmentLead : 0;
            $finalArray['heavyEquipmentLeadRevenue']= (!empty($heavyEquipmentData) && $heavyEquipmentData[0]->heavyEquipmentLeadRevenue > 0) ? $heavyEquipmentData[0]->heavyEquipmentLeadRevenue : 0;
            // car transportation lead count & revenue
            $finalArray['carTransportationLead']    = (!empty($carTransportationData) && $carTransportationData[0]->carTransportationLead > 0) ? $carTransportationData[0]->carTransportationLead : 0;
            $finalArray['carTransportationLeadRevenue']= (!empty($carTransportationData) && $carTransportationData[0]->carTransportationLeadRevenue > 0) ? $carTransportationData[0]->carTransportationLeadRevenue : 0;
            // phone lead count & revenue
            $finalArray['phoneLead']                = (!empty($phoneData) && $phoneData[0]->phone > 0) ? $phoneData[0]->phone : 0;
            $finalArray['phoneLeadRevenue']         = (!empty($phoneData) && $phoneData[0]->phoneRevenue > 0) ? $phoneData[0]->phoneRevenue : 0;
            $finalArray['newMessageCount']          = ($newMessageCount > 0) ? $newMessageCount : 0;

            $status = 1;
            $message = 'success';
        } catch (Exception $e) {
            $message  = $e->getMessage();
        }
        $responseArray = [
            'status' => $status,
            'message' => $message,
            'businessId' => $businessId,
            'businessName' => $businessName,
            'data' => $finalArray,
        ];
        return response()->json($responseArray);
    }

    public function moverBusiness() {
        $status = $businessId = $creditAvailable = $isActive = 0;
        $message = $businessName = '';
        $finalArray = $lastChargeArray = [];
        try {

            $elements           = json_decode(file_get_contents('php://input'), true);
            //echo '<pre>'; print_r($elements); exit();
            $businessId         = $elements['businessId'];
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }

            $businessDetail     = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                throw new Exception('Invalid email or password');
            } else {
                $businessName   = $businessDetail->display_name;
                $creditAvailable= $businessDetail->credit_available + $businessDetail->credit_reserved;
                $businessLevel  = Campaign::where('business_id', $businessId)->where('payment_type', 0)->count();
                if ($businessLevel > 0) {
                    $isActive   = 1;
                    $paymentDetail = DB::select("SELECT bcp.business_campaign_payment_id, bcp.business_id, bcp.amount_charge FROM `business_campaign_payment` bcp INNER JOIN (SELECT business_id, MAX(business_campaign_payment_id) AS business_campaign_payment_id FROM `business_campaign_payment` WHERE business_id = " . $businessId . " AND is_charge_approved = 'yes' GROUP BY business_id) AS max_bcp ON bcp.business_campaign_payment_id = max_bcp.business_campaign_payment_id ORDER BY bcp.business_campaign_payment_id DESC;");
                    foreach ($paymentDetail as $payment) {
                        $lastChargeArray[$payment->business_id] = $payment->amount_charge;
                    }
                }
            }
            $status = 1;
            $message = 'success';
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'businessId' => $businessId,
            'businessName' => $businessName,
            'balance' => $creditAvailable,
            'lastCharge' => $lastChargeArray[$businessId] ?? 0,
            'isActive' => $isActive
        ];
        return response()->json($responseArray);
    }

    public function moverCampaign() {
        $status = $businessId =  0;
        $message = $businessName = '';
        $finalArray = $campaignId = $leadCountArray = [];
        try {

            $elements           = json_decode(file_get_contents('php://input'), true);
            //echo '<pre>'; print_r($elements); exit();
            $startDate          = $elements['startDate'];
            $endDate            = $elements['endDate'];
            $businessId         = $elements['businessId'];
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            if (empty($elements['startDate'])) {
                throw new Exception("startDate required");
            }
            if (empty($elements['endDate'])) {
                throw new Exception("endDate required");
            }

            $leadCategory       = $elements['category'];
            //1-lead, 2-phone
            $leadType           = $elements['type'];

            $businessDetail     = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                throw new Exception('Invalid email or password');
            } else {
                $businessName   = $businessDetail->display_name;
                if ($leadCategory == "all" && $leadType == 2) {
                    $campaignDetail = Campaign::where('business_id', $businessId)->where('campaign_type_id', '<>', 7)
                        ->where(function($query) use ($leadType) {
                            $query->where('lead_type_id', $leadType)->orWhere('campaign_type_id', 4);
                        })->get();
                } else {
                    $campaignDetail = Campaign::where('business_id', $businessId)->where('campaign_type_id', '<>', 7)->where('lead_category_id', $leadCategory)->where('lead_type_id', $leadType)->get();
                }

                foreach ($campaignDetail as $campaign) {
                    $campaignId[] = $campaign->campaign_id;
                }

                $leadDetail = DB::select("SELECT campaign_id, COUNT(lead_routing_id) AS totalLead FROM lead_routing WHERE campaign_id IN (" . implode(',', $campaignId) . ") AND route_status = 'sold' AND (created_at >= '" . $startDate . " 00:00:00' AND created_at <= '" . $endDate . " 23:59:59') GROUP BY campaign_id");
                if (count($leadDetail) > 0) { foreach ($leadDetail as $lead) {
                    $leadCountArray[$lead->campaign_id] = $lead->totalLead;
                } }

                foreach ($campaignDetail as $campaign) {
                    $campaignArray['campaignName'] = $campaign->campaign_name;
                    $campaignArray['campaignId'] = $campaign->campaign_id;
                    $campaignArray['balance'] = $campaign->credit_available + $campaign->credit_reserved;
                    $campaignArray['totalLead'] = (isset($leadCountArray[$campaign->campaign_id])) ? $leadCountArray[$campaign->campaign_id] : 0;
                    $finalArray[] = $campaignArray;
                }
            }
            $status = 1;
            $message = 'success';
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        $responseArray = [
            'status' => $status,
            'message' => $message,
            'businessId' => $businessId,
            'businessName' => $businessName,
            'data' => $finalArray,
        ];
        return response()->json($responseArray);
    }

    public function moverCampaignNew() {
        $status = $businessId = $missedLeadAvg = $missedLeadAvgPrice = 0;
        $message = $businessName = $peakWindow = $peakWindowLead = '';
        $finalArray = $campaignIds = $organicIds = $lastChargeArray = $payoutsArray = $pauseArray = $leadCountArray = $peakWindowArray = $missedLeadArray = $activeCampAssocDataAr = [];
        try {
            $dayNumber              = date('N'); //(1 for Monday, 7 for Sunday)
            $elements               = json_decode(file_get_contents('php://input'), true);
            //echo '<pre>'; print_r($elements); exit();
            $businessId             = $elements['businessId'];
            $campaignId             = $elements['campaignId'];
            $fromPrice              = 1;
            $toPrice                = 18;
            $startDate = $endDate   = date('Y-m-d');
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            $leadCategory           = $elements['category'] ?? 1;
            /*$leadCategory         = $elements['category'];*/
            $businessDetail         = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                throw new Exception('Invalid email or password');
            } else {
                $businessName       = $businessDetail->display_name;

                $pauseDetail        = CampaignPause::where('is_pause', '1')->get();
                foreach ($pauseDetail as $pause) {
                    $pauseArray[$pause->campaign_id] = $pause->pause_time;
                }

                $organicDetail      = CampaignOrganic::where('is_full_lead', 'no')->get();
                foreach ($organicDetail as $organic) {
                    $organicIds[]   = $organic->campaign_id;
                }

                $subscriptionDetail = Subscription::where('subscription_start', '>=', date('Y-m-01'))->where('subscription_end', '<=', date('Y-m-t'))->where('is_active', 'yes')->where('business_id', $businessId)->first();
                if (isset($subscriptionDetail)) {
                    $subscriptionPlanDetail = MstSubscriptionPlan::where('subscription_plan_id', $subscriptionDetail->subscription_plan_id)->first();
                }

                if ($campaignId > 0) {
                    $campaignDetail = Campaign::where('business_id', $businessId)->whereNotIn('campaign_type_id', [4, 7])->where('lead_category_id', $leadCategory)->where('campaign_id', $campaignId)->get();
                } else {
                    $campaignDetail = Campaign::where('business_id', $businessId)->whereNotIn('campaign_type_id', [4, 7])->where('lead_category_id', $leadCategory)->where(function ($query) use ($pauseArray, $organicIds) {
                        if (count($organicIds) > 0) {
                            $query->where(function ($q) use ($pauseArray) {
                                $q->where('is_active', 'yes')->orWhereIn('campaign_id', array_keys($pauseArray));
                            })->whereNotIn('campaign_id', $organicIds);
                        } else {
                            $query->where('is_active', 'yes')->orWhereIn('campaign_id', array_keys($pauseArray));
                        }
                    })->get();
                }

                foreach ($campaignDetail as $campaign) {
                    $campaignIds[]  = $campaign->campaign_id;
                }

                if (count($campaignIds) > 0) {
                    $paymentDetail  = DB::select("SELECT bcp.business_campaign_payment_id, bcp.campaign_id, bcp.amount_charge FROM `business_campaign_payment` bcp INNER JOIN (SELECT campaign_id, MAX(business_campaign_payment_id) AS business_campaign_payment_id FROM `business_campaign_payment` WHERE campaign_id IN (".implode(',', $campaignIds).") AND is_charge_approved = 'yes' GROUP BY campaign_id) AS max_bcp ON bcp.business_campaign_payment_id = max_bcp.business_campaign_payment_id ORDER BY bcp.business_campaign_payment_id DESC;");
                    foreach ($paymentDetail as $payment) {
                        $lastChargeArray[$payment->campaign_id]  = $payment->amount_charge;
                    }

                    $leadDetail     = DB::select("SELECT campaign_id, COUNT(lead_routing_id) AS totalLead FROM lead_routing WHERE campaign_id IN (" . implode(',', $campaignIds) . ") AND route_status = 'sold' AND (created_at >= '" . $startDate . " 00:00:00' AND created_at <= '" . $endDate . " 23:59:59') GROUP BY campaign_id");
                    if (count($leadDetail) > 0) { foreach ($leadDetail as $lead) {
                        $leadCountArray[$lead->campaign_id] = $lead->totalLead;
                    } }

                    $leadPeakHourDetail     = DB::select("SELECT DATE_FORMAT(created_at, '%H:00:00') AS hour_slot, COUNT(lead_routing_id) AS total_leads FROM lead_routing WHERE campaign_id IN (" . implode(',', $campaignIds) . ") AND route_status = 'sold' AND created_at BETWEEN '" . $startDate . " 00:00:00' AND '" . $endDate . " 23:59:59' GROUP BY hour_slot ORDER BY total_leads DESC LIMIT 1");

                    // $leadPeakWindowDetail   = DB::select("SELECT
                    //                                     h.hour_slot,
                    //                                     COALESCE(l.total_leads, 0) AS total_leads
                    //                                 FROM (
                    //                                     SELECT '06:00:00' AS hour_slot UNION ALL
                    //                                     SELECT '07:00:00' UNION ALL
                    //                                     SELECT '08:00:00' UNION ALL
                    //                                     SELECT '09:00:00' UNION ALL
                    //                                     SELECT '10:00:00' UNION ALL
                    //                                     SELECT '11:00:00' UNION ALL
                    //                                     SELECT '12:00:00' UNION ALL
                    //                                     SELECT '13:00:00' UNION ALL
                    //                                     SELECT '14:00:00' UNION ALL
                    //                                     SELECT '15:00:00' UNION ALL
                    //                                     SELECT '16:00:00' UNION ALL
                    //                                     SELECT '17:00:00' UNION ALL
                    //                                     SELECT '18:00:00' UNION ALL
                    //                                     SELECT '19:00:00' UNION ALL
                    //                                     SELECT '20:00:00' UNION ALL
                    //                                     SELECT '21:00:00'
                    //                                 ) AS h
                    //                                 LEFT JOIN (
                    //                                     SELECT
                    //                                         DATE_FORMAT(created_at, '%H:00:00') AS hour_slot,
                    //                                         COUNT(lead_routing_id) AS total_leads
                    //                                     FROM lead_routing
                    //                                     WHERE
                    //                                         campaign_id IN (" . implode(',', $campaignIds) . ")
                    //                                         AND route_status = 'sold'
                    //                                         AND created_at BETWEEN '" . $startDate . " 00:00:00' AND '" . $endDate . " 23:59:59'
                    //                                     GROUP BY hour_slot
                    //                                 ) AS l ON h.hour_slot = l.hour_slot
                    //                                 ORDER BY h.hour_slot");
                    $times = CampaignScheule::where('campaign_id', $campaign->campaign_id)->where('day', $dayNumber)
                        ->selectRaw('MIN(start_hour) as min_time, MAX(end_hour) as max_time')
                        ->first();

                    $hours = range($times->min_time, $times->max_time);
                    $hourSlotSqlParts = [];
                    // if ($times->min_time == 0 && $times->max_time == 24) {
                    //     $restrictedHours = range(6, 21);
                    // } else {
                    //     $restrictedHours = $hours;
                    // }
                    foreach ($hours as $hour) {
                        $formatted = str_pad($hour, 2, '0', STR_PAD_LEFT) . ':00:00';
                        $hourSlotSqlParts[] = "SELECT '{$formatted}' AS hour_slot";
                    }

                    $hourSlotUnionSql = implode(' UNION ALL ', $hourSlotSqlParts);
                    $leadPeakWindowDetail = DB::select("
                                                SELECT
                                                    h.hour_slot,
                                                    COALESCE(l.total_leads, 0) AS total_leads
                                                FROM (
                                                    {$hourSlotUnionSql}
                                                ) AS h
                                                LEFT JOIN (
                                                    SELECT
                                                        DATE_FORMAT(created_at, '%H:00:00') AS hour_slot,
                                                        COUNT(lead_routing_id) AS total_leads
                                                    FROM lead_routing
                                                    WHERE
                                                        campaign_id IN (" . implode(',', $campaignIds) . ")
                                                        AND route_status = 'sold'
                                                        AND created_at BETWEEN '{$startDate} 00:00:00' AND '{$endDate} 23:59:59'
                                                    GROUP BY hour_slot
                                                ) AS l ON h.hour_slot = l.hour_slot
                                                ORDER BY h.hour_slot
                                            ");
                    if (count($leadPeakWindowDetail) > 0) { foreach ($leadPeakWindowDetail as $lead) {
                        $peakWindowArray[date('h A', strtotime($lead->hour_slot))] = $lead->total_leads;
                    } }

                    $hours = array_keys($peakWindowArray);
                    $values = array_values($peakWindowArray);

                    $maxSum = -1;
                    for ($i = 0; $i <= count($hours) - 3; $i++) {
                        $sum = $values[$i] + $values[$i + 1] + $values[$i + 2];

                        if ($sum > $maxSum) {
                            $maxSum = $sum;
                            $peakWindow = $hours[$i] . ' to ' . $hours[$i + 2];
                            $peakWindowLead = $maxSum;
                        }
                    }
                    $moveDateStart          = Carbon::now()->format('Y-m-d');
                    $moveDateEnd            = Carbon::now()->addDays(90)->format('Y-m-d');

                    /*$missedLeadDetail     = DB::select("SELECT l.*, l.created_at as lead_created_at, lm.*, o.lead_source_name
                    FROM `lead` l
                    INNER JOIN lead_moving lm ON l.lead_id = lm.lead_id
                    LEFT JOIN mst_lead_source o ON o.lead_source_id = l.lead_source_id
                    WHERE l.remaining_slot > 0 AND l.sold_type NOT IN ('exclusive','organic') AND l.created_at >= '".date('Y-m-d', strtotime('-10 days'))." 00:00:00' AND l.created_at <= '".$endDate." 23:59:59'
                    AND l.lead_id NOT IN (SELECT lead_id FROM lead_routing WHERE campaign_id IN (" . implode(',', $campaignIds) . " AND lead_routing.created_at >= '".date('Y-m-d', strtotime('-10 days'))." 00:00:00' AND lead_routing.created_at <= '".$endDate." 23:59:59') GROUP BY lead_id)
                    AND l.is_dnc_call = 'no' AND is_marketplace = 'yes' AND (lm.move_date > '".$moveDateStart."' AND lm.move_date <= '".$moveDateEnd."') ORDER BY l.lead_id DESC");
                    //echo '<pre>'; print_r($missedLeadDetail); die;
                    foreach ($missedLeadDetail as $lead) {
                        //DB::table('test')->insert(['result' => "lead start = ".$lead->lead_id]);
                        $fromData           = MstZipcode::where('zipcode', $lead->from_zipcode)->first();
                        $toData             = MstZipcode::where('zipcode', $lead->to_zipcode)->first();
                        $fromState          = $lead->from_state;
                        $toState            = $lead->to_state;
                        $fromZip            = $lead->from_zipcode;
                        $toZip              = $lead->to_zipcode;
                        $fromAreaCode       = $lead->from_areacode;
                        $toAreaCode         = $lead->to_areacode;
                        $dateTimeReceived   = $lead->created_at;

                        // Logic 1 Check CoverageType ( Local, Long Distance) [0 - Local, 1 - Long]
                        $leadCampaignLogic  = new LeadCampaignLogic();

                        // Logic 3 Check fromZip
                        $campaignIds        = $leadCampaignLogic->checkFromZip($campaignIds, $activeCampAssocDataAr, $fromZip, $toZip, $fromState, $toState, $fromAreaCode, $toAreaCode);
                        //DB::table('test')->insert(['result' => "lead start checkFromZip = ".json_encode($campaignIds)]);
                        if (count($campaignIds) == 0) {
                            continue;
                        }

                        //get price by using lead date time received & verify
                        $coverageType       = 'local';
                        if(strcmp($fromState, $toState)) {
                            $coverageType   = 'long';
                        }
                        $price              = $this->getMarketplaceLeadPrice($dateTimeReceived, $lead->is_verified);
                        //DB::table('dev_debug')->insert(['result' => "lead getMarketplaceLeadPrice = ".$price]);
                        if ($lead->remaining_slot == 4) {
                            $price          = $price + 4;
                        } else if ($lead->remaining_slot == 3) {
                            $price          = $price + 2;
                        } else if ($lead->remaining_slot == 2) {
                            $price          = $price + 1;
                        } else if ($lead->remaining_slot == 1) {
                            $price          = $price + 1;
                        }
                        if ($coverageType == 'local') {
                            $price          = ceil(($price * 60) / 100);
                        }

                        if (isset($subscriptionPlanDetail) && $subscriptionPlanDetail->dicount_percentage > 0) {
                            $price          = ($price * (100 - $subscriptionPlanDetail->dicount_percentage)) / 100;
                        }

                        if (isset($fromPrice) && isset($toPrice)) {
                            if ($price >= $fromPrice && $price <= $toPrice) {
                            } else {
                                continue;
                            }
                        }

                        $data['leadId']     = $lead->lead_id;
                        $missedLeadArray[]  = $data;

                        $leadCreatedAt      = date('Y-m-d', strtotime($lead->lead_created_at));
                        $createdDate        = new DateTime($leadCreatedAt);
                        $currentDate        = new DateTime(); // Now
                        $interval           = $createdDate->diff($currentDate);

                        $missedLeadAvg     += $interval->days ?? 0;
                        $missedLeadAvgPrice+= $price;
                    }*/
                }

                $payoutDetail       = CampaignCallPayout::whereIn('campaign_id', $campaignIds)->get();
                foreach ($payoutDetail as $payout) {
                    $payoutsArray[$payout->campaign_id] = $payout->automated_outbound;
                }
                foreach ($campaignDetail as $campaign) {
                    $startTimes     = CampaignScheule::where('campaign_id', $campaign->campaign_id)->where('day', $dayNumber)->pluck('start_hour')->toArray();
                    $endTimes       = CampaignScheule::where('campaign_id', $campaign->campaign_id)->where('day', $dayNumber)->pluck('end_hour')->toArray();

                    $timeGroups     = [];
                    $currentStart   = $startTimes[0] ?? 0;
                    $currentEnd     = $endTimes[0] ?? 0;

                    // Loop through the times and group them
                    for ($i = 1; $i < count($startTimes); $i++) {
                        // Check if the current start time is consecutive with the previous end time
                        if ($startTimes[$i] == $currentEnd) {
                            // If consecutive, update the end time
                            $currentEnd = $endTimes[$i];
                        } else {
                            // If not consecutive, push the previous group and start a new group
                            $timeGroups[] = date("h:i A", strtotime($currentStart.':00')) . ' - ' . date("h:i A", strtotime($currentEnd.':00'));
                            $currentStart = $startTimes[$i];
                            $currentEnd = $endTimes[$i];
                        }
                    }
                    // Add the last group
                    $timeGroups[]   = date("h:i A", strtotime($currentStart.':00')) . ' - ' . date("h:i A", strtotime($currentEnd.':00'));

                    $campaignArray['campaignId'] = $campaign->campaign_id;
                    $campaignArray['floorBid'] = $campaign->floor_bid;
                    $campaignArray['campaignName'] = $campaign->campaign_name;
                    $campaignArray['balance'] = $campaign->credit_available + $campaign->credit_reserved;
                    $campaignArray['isActive'] = $campaign->is_active;
                    $campaignArray['payout'] = $payoutsArray[$campaign->campaign_id] ?? 0;
                    $campaignArray['type'] = $campaign->lead_type_id;
                    $campaignArray['lastCharge'] = $lastChargeArray[$campaign->campaign_id] ?? 0;
                    $campaignArray['level'] = $campaign->payment_type;
                    $campaignArray['pauseTime'] = $pauseArray[$campaign->campaign_id] ?? 0;
                    $campaignArray['schedule'] = $timeGroups;
                    $campaignArray['totalLead'] = (isset($leadCountArray[$campaign->campaign_id])) ? $leadCountArray[$campaign->campaign_id] : 0;
                    $campaignArray['peakHour'] = (isset($leadPeakHourDetail[0]->hour_slot)) ? date('h A', strtotime($leadPeakHourDetail[0]->hour_slot)) : 0;
                    $campaignArray['peakHourLead'] = (isset($leadPeakHourDetail[0]->total_leads)) ? $leadPeakHourDetail[0]->total_leads : 0;
                    $campaignArray['peakWindowGraph'] = (isset($peakWindowArray)) ? $peakWindowArray : [];
                    $campaignArray['peakWindowLead'] = $peakWindow;
                    $campaignArray['peakWindowLeadData'] = (isset($leadCountArray[$campaign->campaign_id])) ? $leadCountArray[$campaign->campaign_id] / date('G', time()) : 0;
                    $campaignArray['missedLead'] = (isset($missedLeadArray)) ? count($missedLeadArray) : [];
                    $campaignArray['missedLeadAvg'] = ($missedLeadAvg > 0) ? @number_format($missedLeadAvg / count($missedLeadArray), 2) : 0;
                    $campaignArray['missedLeadAvgPrice'] = ($missedLeadAvgPrice > 0) ? $missedLeadAvgPrice / count($missedLeadArray) : 0;
                    $finalArray[] = $campaignArray;
                }
            }
            $status = 1;
            $message = 'success';
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        $responseArray = [
            'status' => $status,
            'message' => $message,
            'businessId' => $businessId,
            'businessName' => $businessName,
            'data' => $finalArray,
        ];
        return response()->json($responseArray);
    }

    public function moverMarketplacePopup() {
        $status = $businessId = $missedLeadAvg = 0;
        $message = $businessName = '';
        $finalArray = $campaignIds = $activeCampAssocDataAr = [];
        try {
            $dayNumber                      = date('N'); //(1 for Monday, 7 for Sunday)
            $elements                       = json_decode(file_get_contents('php://input'), true);
            //echo '<pre>'; print_r($elements); exit();
            $businessId                     = $elements['businessId'];
            $campaignId                     = $elements['campaignId'];
            $limit                          = $elements['limit'];
            $orderBy                        = $elements['orderBy']; //ASC //DESC
            $fromPrice                      = 1;
            $toPrice                        = 18;
            $startDate = $endDate           = date('Y-m-d');
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            $businessDetail                 = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                throw new Exception('Invalid email or password');
            } else {
                $businessName               = $businessDetail->display_name;
                $campaignDetail             = Campaign::where('business_id', $businessId)->where('campaign_type_id', '<>', 7)->where('campaign_id', $campaignId)->get();

                $subscriptionDetail         = Subscription::where('subscription_start', '>=', date('Y-m-01'))->where('subscription_end', '<=', date('Y-m-t'))->where('is_active', 'yes')->where('business_id', $businessId)->first();
                if (isset($subscriptionDetail)) {
                    $subscriptionPlanDetail = MstSubscriptionPlan::where('subscription_plan_id', $subscriptionDetail->subscription_plan_id)->first();
                }

                foreach ($campaignDetail as $campaign) {
                    $campaignIds[]          = $campaign->campaign_id;
                }

                if (count($campaignIds) > 0) {
                    $moveDateStart          = Carbon::now()->format('Y-m-d');
                    $moveDateEnd            = Carbon::now()->addDays(90)->format('Y-m-d');

                    $missedLeadDetail       = DB::select("SELECT l.*, l.created_at as lead_created_at, lm.*, o.lead_source_name
                    FROM `lead` l
                    INNER JOIN lead_moving lm ON l.lead_id = lm.lead_id
                    LEFT JOIN mst_lead_source o ON o.lead_source_id = l.lead_source_id
                    WHERE l.remaining_slot > 0 AND l.sold_type NOT IN ('exclusive','organic') AND l.created_at >= '".date('Y-m-d', strtotime('-30 days'))." 00:00:00' AND l.created_at <= '".$endDate." 23:59:59'
                    AND l.lead_id NOT IN (SELECT lead_id FROM lead_routing WHERE campaign_id IN (" . implode(',', $campaignIds) . ") GROUP BY lead_id)
                    AND l.is_dnc_call = 'no' AND is_marketplace = 'yes' AND (lm.move_date > '".$moveDateStart."' AND lm.move_date <= '".$moveDateEnd."') ORDER BY l.lead_id " . $orderBy);
                    //echo '<pre>'; print_r($missedLeadDetail); die;
                    foreach ($missedLeadDetail as $lead) {
                        //DB::table('test')->insert(['result' => "lead start = ".$lead->lead_id]);
                        $fromData           = MstZipcode::where('zipcode', $lead->from_zipcode)->first();
                        $toData             = MstZipcode::where('zipcode', $lead->to_zipcode)->first();
                        $fromState          = $lead->from_state;
                        $toState            = $lead->to_state;
                        $fromZip            = $lead->from_zipcode;
                        $toZip              = $lead->to_zipcode;
                        $fromAreaCode       = $lead->from_areacode;
                        $toAreaCode         = $lead->to_areacode;
                        $dateTimeReceived   = $lead->created_at;

                        // Logic 1 Check CoverageType ( Local, Long Distance) [0 - Local, 1 - Long]
                        $leadCampaignLogic  = new LeadCampaignLogic();

                        // Logic 3 Check fromZip
                        $campaignIds        = $leadCampaignLogic->checkFromZip($campaignIds, $activeCampAssocDataAr, $fromZip, $toZip, $fromState, $toState, $fromAreaCode, $toAreaCode);
                        //DB::table('test')->insert(['result' => "lead start checkFromZip = ".json_encode($campaignIds)]);
                        if (count($campaignIds) == 0) {
                            continue;
                        }

                        //get price by using lead date time received & verify
                        $coverageType       = 'local';
                        if(strcmp($fromState, $toState)) {
                            $coverageType   = 'long';
                        }
                        $price              = $this->getMarketplaceLeadPrice($dateTimeReceived, $lead->is_verified);
                        //DB::table('dev_debug')->insert(['result' => "lead getMarketplaceLeadPrice = ".$price]);
                        if ($lead->remaining_slot == 4) {
                            $price          = $price + 4;
                        } else if ($lead->remaining_slot == 3) {
                            $price          = $price + 2;
                        } else if ($lead->remaining_slot == 2) {
                            $price          = $price + 1;
                        } else if ($lead->remaining_slot == 1) {
                            $price          = $price + 1;
                        }
                        if ($coverageType == 'local') {
                            $price          = ceil(($price * 60) / 100);
                        }

                        if (isset($subscriptionPlanDetail) && $subscriptionPlanDetail->dicount_percentage > 0) {
                            $price          = ($price * (100 - $subscriptionPlanDetail->dicount_percentage)) / 100;
                        }

                        if (isset($fromPrice) && isset($toPrice)) {
                            if ($price >= $fromPrice && $price <= $toPrice) {
                            } else {
                                continue;
                            }
                        }

                        $data['leadId']     = $lead->lead_id;
                        $data['price']      = $price;
                        $finalArray[]       = $data;
                    }

                    if (count($finalArray) > 0) {
                        $finalArray         = array_slice($finalArray, 0, $limit);
                    }
                }
            }
            $status = 1;
            $message = 'success';
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        $responseArray = [
            'status' => $status,
            'message' => $message,
            'businessId' => $businessId,
            'businessName' => $businessName,
            'data' => $finalArray,
        ];
        return response()->json($responseArray);
    }

    public function moverCampaignPause (Request $request)
    {
        $status = 0;
        $message = '';
        $logArray = [];
        try {

            $businessId     = $request['businessId'];
            $campaignId     = $request['campaignId'];
            $isPause        = $request['isPause'];
            $ip             = $request['ip'] ?? '';
            $currentDateTime= date('Y-m-d H:i:s');
            $onehour        = new DateTime($currentDateTime);
            $onehour->modify('+1 hours');
            $pauseTime      = $onehour->format('H:i:00');
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            if (empty($campaignId)) {
                throw new Exception("campaignId required");
            }
            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            }

            $campaignPause = CampaignPause::where('campaign_id', $campaignId)->where('is_pause', '1')->first();
            if ($campaignPause) {
                $logArray[] = [
                    'created_user_id' => 77,
                    'campaign_id' => $campaignId,
                    'field_name' => 'pause_time',
                    'old_value' => ($campaignPause->is_pause > 0) ? 'yes' : 'no',
                    'new_value' => ($isPause > 0) ? 'yes' : 'no',
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            CampaignPause::where('campaign_id', $campaignId)->update([
                'is_pause' => '0'
            ]);

            if ($isPause > 0) {
                CampaignPause::create([
                    'campaign_id' => $campaignId,
                    'pause_time' => $pauseTime,
                    'is_pause' => $isPause,
                    'created_at' => date("Y-m-d H:i:s")
                ]);

                $logArray[] = [
                    'created_user_id' => 77,
                    'campaign_id' => $campaignId,
                    'field_name' => 'pause_time',
                    'old_value' => 'no',
                    'new_value' => 'yes',
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }

            if (count($logArray) > 0) {
                CampaignUpdateLog::insert($logArray);
            }

            Campaign::where('campaign_id', $campaignId)->update([
                'is_active' => ($isPause > 0) ? 'no' : 'yes'
            ]);

            $status     = 1;
            $message    = 'success';
        } catch (Exception $e) {
            $message        = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'error' => "",
        ];
        return response()->json($responseArray);
    }

    public function moverLeadData() {
        $status = $businessId = 0;
        $message = $cWhere = $columnName = $tableName = '';
        $finalArray = $campaigntypeArray = $movesizeArray = [];
        try {
            $elements           = json_decode(file_get_contents('php://input'), true);
            $startDate          = $elements['startDate'];
            $endDate            = $elements['endDate'];
            $leadCategory       = $elements['category'];
            //1-lead, 2-phone
            $leadType           = $elements['type'];
            $campaignId         = $elements['campaignId'];
            if (isset($elements["businessId"]) && $elements["businessId"] > 0) {
                $businessId     = $elements["businessId"];
            }
            if (empty($elements['startDate'])) {
                throw new Exception("startDate required");
            }
            if (empty($elements['endDate'])) {
                throw new Exception("endDate required");
            }
            if (empty($elements['campaignId'])) {
                throw new Exception("campaign Id required");
            }

            //get all campaign type records from mst_zipcode table
            $campaigntypeDetail = MstCampaignType::distinct()->get(['campaign_type_id', 'campaign_type']);
            for ($t = 0; $t < count($campaigntypeDetail); $t++) {
                $campaigntypeArray[$campaigntypeDetail[$t]->campaign_type_id] = $campaigntypeDetail[$t]->campaign_type;
            }

            //get all move size records from mst_move_size table
            $movesizeDetails = MstMoveSize::distinct()->get(['move_size_id', 'move_size']);
            for ($m = 0; $m < count($movesizeDetails); $m++) {
                $movesizeArray[$movesizeDetails[$m]->move_size_id] = $movesizeDetails[$m]->move_size;
            }

            if ($leadType == 1) {
                if ($leadCategory == "1") {
                    $columnName = ", lm.to_zipcode, lm.to_state, lm.move_size_id, lm.move_date, lm.distance";
                    $tableName = "lead_moving";
                } else if ($leadCategory == "2") {
                    $columnName = ", lm.junk_remove_date AS move_date";
                    $tableName = "lead_junk_removal";
                } else if ($leadCategory == "3") {
                    $columnName = ", lm.to_zipcode, lm.to_state, lm.lift_date AS move_date, lm.distance";
                    $tableName = "lead_heavy_lifting";
                } else if ($leadCategory == "4") {
                    $columnName = ", lm.to_zipcode, lm.to_state, lm.move_date, lm.distance";
                    $tableName = "lead_car_transport";
                }

                $leadDetail = DB::select("SELECT l.lead_id, l.name, l.email, l.phone, l.is_dnc_call, l.created_at, c.business_id, c.campaign_name, c.campaign_id, c.campaign_type_id, c.is_rev_share, lr.payout, lr.created_at, lm.from_zipcode, lm.from_state ".$columnName."
                    FROM lead_routing lr
                    LEFT JOIN `lead` l on lr.lead_id = l.lead_id
                    LEFT JOIN campaign c on lr.campaign_id = c.campaign_id
                    INNER JOIN ".$tableName." lm on l.lead_id = lm.lead_id
                    WHERE lr.campaign_id IN (" . $campaignId . ") AND lr.route_status = 'sold' AND (lr.created_at >= '" . $startDate . " 00:00:00' AND lr.created_at <= '" . $endDate . " 23:59:59') ORDER BY l.lead_id DESC");

                if (count($leadDetail) > 0) { foreach ($leadDetail as $lead) {
                    $data['leadId'] = $lead->lead_id;
                    $data['name'] = $lead->name;
                    $data['email'] = $lead->email;
                    $data['phone'] = $lead->phone;
                    $data['isDnc'] = $lead->is_dnc_call;
                    $data['fromZipCode'] = str_pad($lead->from_zipcode, 5, '0', STR_PAD_LEFT);
                    $data['fromState'] = $lead->from_state;
                    if ($leadCategory != "2") {
                        $data['toZipCode'] = str_pad($lead->to_zipcode, 5, '0', STR_PAD_LEFT);
                        $data['toState'] = $lead->to_state;
                    }
                    if ($leadCategory == "1") {
                        $data['moveSize'] = $movesizeArray[$lead->move_size_id];
                    }
                    $data['moveDate'] = date("m/d/y", strtotime($lead->move_date));
                    if ($leadCategory != "2") {
                        $data['distance'] = $lead->distance;
                    }
                    $dateTime = strtotime($lead->created_at);
                    $data['receivedDate'] = date("m/d/y H:i:s", strtotime($lead->created_at));
                    $data['receivedTime'] = date("h", $dateTime) . ":" . date("i", $dateTime) . " " . date("A", $dateTime);
                    $data['payout'] = $lead->payout;
                    $data['campaignName'] = $lead->campaign_name;
                    $data['isTalked'] = 1;
                    $data['isBooked'] = 1;
                    $data['isQuoted'] = 1;
                    $data['campaignId'] = $lead->campaign_id;
                    $data['campaignType'] = $campaigntypeArray[$lead->campaign_type_id];
                    $reviewDetail = LeadCustomerReview::where('lead_id', $lead->lead_id)->where('business_id', $lead->business_id)->first();
                    if (isset($reviewDetail)) {
                        if ($reviewDetail->is_customer_talked == 'yes') {
                            $data['isTalked'] = 2;
                        }
                        if ($reviewDetail->is_booked == 'yes') {
                            $data['isBooked'] = 2;
                        }
                        if ($reviewDetail->is_quoted == 'yes') {
                            $data['isQuoted'] = 2;
                        }
                    }
                    $newreviewDetail = LeadRevShareCustomerReview::where('lead_id', $lead->lead_id)->where('business_id', $lead->business_id)->first();
                    $data['bookedDate'] = $data['bookedPrice'] = 0;
                    if (isset($newreviewDetail)) {
                        $data['bookedDate'] = date('m/d/Y', strtotime($newreviewDetail->booked_date));
                        $data['bookedPrice'] = $newreviewDetail->booked_price;
                    }
                    $data['isRevShare'] = 0;
                    if ($lead->is_rev_share == 'yes') {
                        $data['isRevShare'] = 1;
                    }
                    $finalArray[] = $data;
                }
                    $status = 1;
                    $message = 'success';
                } else {
                    throw new Exception("No Data Founds");
                }
            } else if ($leadType == 2) {
                if ($leadCategory != "all") {
                    $cWhere = "AND lead_category_id = '".$leadCategory."'";
                }

                $leadDetail = DB::select("SELECT c.lead_id, c.from_number, c.call_datetime, c.recording_url AS legA, c.duration as durationA, ct.recording_url AS legB, ct.duration as durationB, ct.payout
                              FROM `lead_call` c
                              INNER JOIN `lead_transfer_call` ct ON c.lead_call_id = ct.lead_call_id
                              WHERE c.transfer_type != 'call' AND c.campaign_id IN (" . $campaignId . ") AND (c.created_at >= '" . $startDate . " 00:00:00' AND c.created_at <= '" . $endDate . " 23:59:59') $cWhere ORDER BY c.lead_call_id DESC");
                //echo "<pre>";print_r($lead_final);die;

                if (count($leadDetail) > 0) { foreach ($leadDetail as $lead) {
                    $data['leadId'] = $lead->lead_id;
                    $data['phone'] = $lead->from_number; //customer phone number
                    $data['legA'] = $lead->legA;
                    $data['durationA'] = $lead->durationA;
                    $data['legB'] = $lead->legB;
                    $data['durationB'] = $lead->durationB;
                    $dateTime = strtotime($lead->call_datetime);
                    $data['receivedDate'] = date("m/d/y", strtotime($lead->call_datetime));
                    $data['receivedTime'] = date("h", $dateTime) . ":" . date("i", $dateTime) . " " . date("A", $dateTime);
                    $data['payout'] = $lead->payout;
                    $finalArray[] = $data;
                }
                    $status = 1;
                    $message = 'success';
                } else {
                    throw new Exception("No Data Founds");
                }
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'data' => $finalArray
        ];

        return response()->json($responseArray);
    }

    public function moverLeadDataSummary() {
        $status = 0;
        $message = $cWhere = '';
        $finalArray = $campaignArray = $campaignNameArray = [];
        try {

            $elements           = json_decode(file_get_contents('php://input'), true);
            /*$email            = $elements['email'];*/
            $startDate          = $elements['startDate'];
            $endDate            = $elements['endDate'];
            $leadCategory       = $elements['category'];
            //1-lead, 2-phone
            $leadType           = $elements['type'];
            $campaignId         = $elements['campaignId'];
            $businessId         = $elements['businessId'];
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            if (empty($elements['startDate'])) {
                throw new Exception("startDate required");
            }
            if (empty($elements['endDate'])) {
                throw new Exception("endDate required");
            }
            if (empty($elements['campaignId'])) {
                throw new Exception("campaign Id required");
            }

            $businessDetail     = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                throw new Exception('Invalid email or password');
            }

            if ($leadType == 2) {
                if ($leadCategory != "all") {
                    $cWhere = "AND lead_category_id = '".$leadCategory."'";
                }

                $leadDetail = DB::select("SELECT c.lead_id, c.from_number, c.call_datetime, c.recording_url AS legA, c.duration as durationA, ct.recording_url AS legB, ct.duration as durationB, ct.payout, ct.campaign_id
                              FROM `lead_call` c
                              INNER JOIN `lead_transfer_call` ct ON c.lead_call_id = ct.lead_call_id
                              WHERE c.transfer_type != 'call' AND c.campaign_id IN (" . $campaignId . ") AND (c.created_at >= '" . $startDate . " 00:00:00' AND c.created_at <= '" . $endDate . " 23:59:59') $cWhere ORDER BY c.lead_call_id DESC");
                //echo "<pre>";print_r($lead_final);die;

                if (count($leadDetail) > 0) {
                    $campaignDetail = DB::select("SELECT campaign_id, campaign_name FROM `campaign` WHERE campaign_id IN (" . $campaignId . ")");
                    for($c=0; $c < count($campaignDetail); $c++){
                        $campaignNameArray[$campaignDetail[$c]->campaign_id] = $campaignDetail[$c]->campaign_name;
                    }
                    //echo "<pre>";print_r($campaignNameArray);die;

                    foreach ($leadDetail as $lead) {
                        $campaignName = '';
                        if(isset($campaignNameArray[$lead->campaign_id])){
                            $campaignName = $campaignNameArray[$lead->campaign_id];
                        }
                        $campaignArray[$lead->campaign_id]['type'] = $campaignName;
                        if (!isset($campaignArray[$lead->campaign_id]['totalCall']) || !isset($campaignArray[$lead->campaign_id]['totalDuration']) || !isset($campaignArray[$lead->campaign_id]['totalPayout'])) {
                            $campaignArray[$lead->campaign_id]['totalCall'] = 0;
                            $campaignArray[$lead->campaign_id]['totalDuration'] = 0;
                            $campaignArray[$lead->campaign_id]['totalPayout'] = 0;
                        }
                        $campaignArray[$lead->campaign_id]['totalCall'] += 1;
                        $campaignArray[$lead->campaign_id]['totalDuration'] += round($lead->durationB, 2);
                        $campaignArray[$lead->campaign_id]['totalPayout'] += $lead->payout;
                    }
                }
                //echo "<pre>";print_r($campaignArray);die;
                $campaignArray = array_values($campaignArray);
                if (count($campaignArray) > 0) {
                    $status = 1;
                    $message = 'success';
                } else {
                    throw new Exception("No Data Founds");
                }
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'data' => $campaignArray,
        ];
        return response()->json($responseArray);
    }

    public function getPaymentCard(Request $request) {
        $status = $businessId = 0;
        $message = $businessName = $paymentCustId = '';
        $finalArray = [];
        try {
            $businessId     = $request['businessId'];
            if (empty($businessId)) {
                throw new Exception("businessId Required");
            }

            // get Businesses Login Details
            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                throw new Exception('Invalid email or password');
            }
            $businessName   = $businessDetail->display_name;
            $paymentCustId  = $businessDetail->payment_cust_id;

            // get Card Details
            $cardDetail     = BusinessCC::where('business_id', $businessId)->where('status', "active")->get(['business_cc_id', 'payment_cust_id', 'card', 'payment_card_id', 'is_primary']);
            if (!empty($cardDetail)) { foreach ($cardDetail as $card) {

                $data['lastFour'] = 'XXXX-XXXX-XXXX-' . str_pad($card->card, 4, "0", STR_PAD_LEFT);
                $data['onlyLastFour'] = str_pad($card->card, 4, "0", STR_PAD_LEFT);
                $data['cardId'] = $card->business_cc_id;
                $data['isPrimary'] = $card->is_primary;
                $finalArray[] = $data;
            }
                $status = 1;
                $message = 'success';
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'businessId' => $businessId,
            'businessName' => $businessName,
            'paymentCustId' => $paymentCustId,
            'data' => $finalArray
        ];

        return response()->json($responseArray);
    }

    public function getBusinessPaymentCardSetup(Request $request) {
        $status = 0;
        $message = '';
        $finalData = $finalArray = [];
        try {
            $businessId         = $request['businessId'];
            if (empty($businessId)) {
                throw new Exception("businessId Required");
            }
            // get Payment details Details
            $businessDetail     = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                throw new Exception('Invalid email or password');
            }
            $businessName       = $businessDetail->display_name;
            $campaignDetail     = Campaign::where('business_id', $businessId)->where('campaign_type_id', '<>', 7)->get(['campaign_id', 'campaign_name', 'credit_available', 'payment_type', 'free_lead']);
            $totalCampaign      = count($campaignDetail);
            $businessLevel      = $countCampaign = 0;
            $businessRecuringPayment = $campaignRecuringPayment = [];

            if (count($campaignDetail) > 0) {
                $campaignIdArray  = [];
                foreach ($campaignDetail as $campaign) {
                    $campaignIdArray[] = $campaign->campaign_id;
                    if ($campaign->payment_type > 0) {
                        $countCampaign++;
                    }
                }

                //fetch campaign payment in business_campaign_recuring_payment table
                $campaignRecuringPaymentDetail = BusinessCampaignRecuringPayment::whereIn('campaign_id', $campaignIdArray)->get(['business_campaign_recurring_payment_id', 'campaign_id', 'recurring_type', 'is_active']);
                if ($campaignRecuringPaymentDetail) { foreach($campaignRecuringPaymentDetail as $recuringPayment) {
                    $campaignRecuringPayment[$recuringPayment->campaign_id] = array('recurring_payment_id' => $recuringPayment->business_campaign_recurring_payment_id, "type" => "Amount", "status"=> $recuringPayment->is_active);
                    if ($recuringPayment->recurring_type == "daily") {
                        $campaignRecuringPayment[$recuringPayment->campaign_id] = array('recurring_payment_id' => $recuringPayment->business_campaign_recurring_payment_id, "type" => "Daily", "status"=> $recuringPayment->is_active);
                    }
                } }
            }

            if ($totalCampaign != $countCampaign) {  //2 != 2
                $businessLevel = 1;
            }

            $charge = "--";
            if ($businessLevel == 1) {
                $charge = 'Charge';
            }

            //fetch business payment in business_campaign_recuring_payment table
            $businessRecuringPaymentDetail = BusinessCampaignRecuringPayment::where('business_id', $businessId)->get(['business_campaign_recurring_payment_id', 'business_id', 'recurring_type', 'is_active']);
            if (count($businessRecuringPaymentDetail) > 0) {
                $businessRecuringPayment[$businessId] = array('recurring_payment_id'=>$businessRecuringPaymentDetail[0]->business_campaign_recurring_payment_id, "type"=>"Amount", "status" => $businessRecuringPaymentDetail[0]->is_active);
                if($businessRecuringPaymentDetail[0]->recurring_type == "daily") {
                    $businessRecuringPayment[$businessId] = array('recurring_payment_id'=>$businessRecuringPaymentDetail[0]->business_campaign_recurring_payment_id, "type"=>"Daily", "status" => $businessRecuringPaymentDetail[0]->is_active);
                }
            }

            // for business level
            $businessRecuringTxt = '--';
            if ($businessLevel == 1) {
                if (count($businessRecuringPayment) > 0) {
                    if ($businessRecuringPayment[$businessId]['status'] == "yes") {
                        $businessRecuringTxt = 'Recurring ON';
                    } else {
                        $businessRecuringTxt = 'Recurring ON';
                    }
                } else {
                    $businessRecuringTxt = '--';
                }
            }

            $businessData[] = array(
                'businessId' => $businessId,
                'businessName' => isset($businessName) ? preg_replace('/\xE2\x80\x8B/', "", $businessName) : '',
                'level' => 'Business',
                'creditAvailable' => isset($businessDetail->credit_available) ? $businessDetail->credit_available : '',
                'freeLead' => '--',
                'charge' => $charge,
                'recurrentTxt' => $businessRecuringTxt,
                'type' => 1
            );

            if (!empty($campaignDetail)) { foreach ($campaignDetail as $key => $campaign) {
                $data['businessId'] = $campaign->campaign_id;
                $data['businessName'] = $campaign->campaign_name;
                $data['level'] = 'Campaign';
                $data['creditAvailable'] = $campaign->credit_available;
                $data['freeLead'] = ($campaign->free_lead > 0) ? $campaign->free_lead : '--';
                if ($campaign->payment_type > 0) {
                    $data['charge'] = 'Charge';
                } else {
                    $data['charge'] = '--';
                }

                if ($campaign->payment_type > 0) {
                    $campaignId = $campaign->campaign_id;
                    if (isset($campaignRecuringPayment[$campaignId]) && count($campaignRecuringPayment[$campaignId]) > 0) {
                        $data['recurrentTxt'] = 'Recurring ON';
                    } else {
                        $data['recurrentTxt'] = '--';
                    }
                } else {
                    $data['recurrentTxt'] = '--';
                }
                $data['type'] = 2;
                $finalData[] = $data;
            }
                //$finalArray = array_merge($businessData, $finalData);
                $finalArray = $businessData;

                $status = 1;
                $message = 'success';
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'data' => $finalArray
        ];
        return response()->json($responseArray);
    }

    public function getCampaignPaymentCardSetup(Request $request) {
        $status = 0;
        $message = '';
        $finalData = $finalArray = [];
        try {
            $businessId         = $request['businessId'];
            if (empty($businessId)) {
                throw new Exception("businessId Required");
            }
            // get Payment details Details
            $businessDetail     = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                throw new Exception('Invalid email or password');
            }
            $businessName       = $businessDetail->display_name;
            $campaignDetail     = Campaign::where('business_id', $businessId)->where('campaign_type_id', '<>', 7)->get(['campaign_id', 'campaign_name', 'credit_available', 'payment_type', 'free_lead']);
            $totalCampaign      = count($campaignDetail);
            $businessLevel      = $countCampaign = 0;
            $businessRecuringPayment = $campaignRecuringPayment = [];

            if (count($campaignDetail) > 0) {
                $campaignIdArray  = [];
                foreach ($campaignDetail as $campaign) {
                    $campaignIdArray[] = $campaign->campaign_id;
                    if ($campaign->payment_type > 0) {
                        $countCampaign++;
                    }
                }

                //fetch campaign payment in business_campaign_recuring_payment table
                $campaignRecuringPaymentDetail = BusinessCampaignRecuringPayment::whereIn('campaign_id', $campaignIdArray)->get(['business_campaign_recurring_payment_id', 'campaign_id', 'recurring_type', 'is_active']);
                if ($campaignRecuringPaymentDetail) { foreach($campaignRecuringPaymentDetail as $recuringPayment) {
                    $campaignRecuringPayment[$recuringPayment->campaign_id] = array('recurring_payment_id' => $recuringPayment->business_campaign_recurring_payment_id, "type" => "Amount", "status"=> $recuringPayment->is_active);
                    if ($recuringPayment->recurring_type == "daily") {
                        $campaignRecuringPayment[$recuringPayment->campaign_id] = array('recurring_payment_id' => $recuringPayment->business_campaign_recurring_payment_id, "type" => "Daily", "status"=> $recuringPayment->is_active);
                    }
                } }
            }

            if ($totalCampaign != $countCampaign) {  //2 != 2
                $businessLevel = 1;
            }

            $charge = "--";
            if ($businessLevel == 1) {
                $charge = 'Charge';
            }

            //fetch business payment in business_campaign_recuring_payment table
            $businessRecuringPaymentDetail = BusinessCampaignRecuringPayment::where('business_id', $businessId)->get(['business_campaign_recurring_payment_id', 'business_id', 'recurring_type', 'is_active']);
            if (count($businessRecuringPaymentDetail) > 0) {
                $businessRecuringPayment[$businessId] = array('recurring_payment_id'=>$businessRecuringPaymentDetail[0]->business_campaign_recurring_payment_id, "type"=>"Amount", "status" => $businessRecuringPaymentDetail[0]->is_active);
                if($businessRecuringPaymentDetail[0]->recurring_type == "daily") {
                    $businessRecuringPayment[$businessId] = array('recurring_payment_id'=>$businessRecuringPaymentDetail[0]->business_campaign_recurring_payment_id, "type"=>"Daily", "status" => $businessRecuringPaymentDetail[0]->is_active);
                }
            }

            // for business level
            $businessRecuringTxt = '--';
            if ($businessLevel == 1) {
                if (count($businessRecuringPayment) > 0) {
                    if ($businessRecuringPayment[$businessId]['status'] == "yes") {
                        $businessRecuringTxt = 'Recurring ON';
                    } else {
                        $businessRecuringTxt = 'Recurring ON';
                    }
                } else {
                    $businessRecuringTxt = '--';
                }
            }

            $businessData[] = array(
                'businessId' => $businessId,
                'businessName' => isset($businessName) ? preg_replace('/\xE2\x80\x8B/', "", $businessName) : '',
                'level' => 'Business',
                'creditAvailable' => isset($businessDetail->credit_available) ? $businessDetail->credit_available : '',
                'freeLead' => '--',
                'charge' => $charge,
                'recurrentTxt' => $businessRecuringTxt,
                'type' => 1
            );

            if (!empty($campaignDetail)) { foreach ($campaignDetail as $key => $campaign) {
                $data['businessId'] = $campaign->campaign_id;
                $data['businessName'] = $campaign->campaign_name;
                $data['level'] = 'Campaign';
                $data['creditAvailable'] = $campaign->credit_available;
                $data['freeLead'] = ($campaign->free_lead > 0) ? $campaign->free_lead : '--';
                if ($campaign->payment_type > 0) {
                    $data['charge'] = 'Charge';
                } else {
                    $data['charge'] = '--';
                }

                if ($campaign->payment_type > 0) {
                    $campaignId = $campaign->campaign_id;
                    if (isset($campaignRecuringPayment[$campaignId]) && count($campaignRecuringPayment[$campaignId]) > 0) {
                        $data['recurrentTxt'] = 'Recurring ON';
                    } else {
                        $data['recurrentTxt'] = '--';
                    }
                } else {
                    $data['recurrentTxt'] = '--';
                }
                $data['type'] = 2;
                $finalData[] = $data;
            }
                //$finalArray = array_merge($businessData, $finalData);
                $finalArray = $finalData;

                $status = 1;
                $message = 'success';
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'data' => $finalArray
        ];
        return response()->json($responseArray);
    }

    public function paymentCardCharge(Request $request) {
        $status = $businessId = $campaignId = $convenienceFee = 0;
        $message = 'fail';
        $error = $businessCampaignName = '';
        $validator = Validator::make($request->all(), [
            'charge' => 'required|numeric',
            'lastFour' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json(array(
                'success' => 0,
                'message' => 'There are incorect values in the form!',
                'error' => $validator->getMessageBag()->toArray()
            ), 200);
            $this->throwValidationException(
                $request, $validator
            );
        }

        try {
            //$publishableKey       = Helper::checkServer()['stripe_publishable_key'];
            //$secretKey            = Helper::checkServer()['stripe_secret_key'];
            $businessCampaignId     = $request->get('businessId');
            $businessCampaignCharge = $request->get('charge');
            $lastFour               = $request->get('lastFour');
            $businessCampaignType   = $request->get('levelType');
            //$userId               = Auth::user()->id;
            $ipAddress              = $request->get('ip');
            $userAgent              = $request->get('user_agent');
            if ($businessCampaignType == 2) {
                $campaignDetail     = Campaign::where('campaign_id', $businessCampaignId)->get(['campaign_id', 'business_id', 'campaign_name']);
                $businessId         = $campaignDetail[0]->business_id;
                $campaignId         = $campaignDetail[0]->campaign_id;
                $businessCampaignName= $campaignDetail[0]->campaign_name;
            } else {
                $businessId         = $businessCampaignId;
            }

            //fetch customer details from businesses table
            $businessDetail = Business::with(['businessCC'])
                ->where('business_id', "=", $businessId)
                ->get();

            $businessName           = $businessDetail[0]->display_name;
            $businessEmail          = $businessDetail[0]->email;
            if ($businessCampaignType <> 2) {
                $businessCampaignName= $businessDetail[0]->display_name;
            }

            $cardDetail             = BusinessCC::where('business_cc_id', $lastFour)->where('status', 'active')->first();
            if ($cardDetail->payment_card_id == "") {
                throw new Exception('Unable to process the purchase transaction.');
            }
            $paymentCardId          = $cardDetail->payment_card_id;
            $paymentProfileId       = $cardDetail->payment_cust_id;
            $cardId                 = $cardDetail->business_cc_id;
            //Added by BK on 03/02/2025 convenience fee to evey payment
            if ($businessCampaignCharge > 0) {
                $convenienceFee     = ($businessCampaignCharge * 3) / 100;
                $businessCampaignCharge = $businessCampaignCharge + $convenienceFee;
            }

            /*foreach($businessDetail as $cardDetail) {
                foreach($cardDetail->businessCC as $card) {
                    if($lastFour == $card->card) {
                        $paymentCardId = $card->payment_card_id;
                        $paymentProfileId = $card->payment_cust_id;
                        $cardId = $card->business_cc_id;
                        break;
                    }
                }
            }*/

            /*$businessPaymentObj = new BusinessPaymentController();
            $jsonResponse = $businessPaymentObj->chargeCard($businessCampaignId, $businessCampaignCharge, $paymentCardId, $paymentProfileId, $publishableKey, $secretKey);
            if(empty($jsonResponse['error']) && isset($jsonResponse['status']) && $jsonResponse['status'] == 'succeeded') {
                $responseStatus = "yes";
            } else {
                $responseStatus = "no";
            }*/
            $businessPaymentObj     = new BusinessPaymentController();
            $omni_token             = Helper::checkServer()['omni_token'];
            $postFields             = array("payment_method_id" => $paymentCardId, "meta"=>array("tax"=>0), "total" => $businessCampaignCharge, "pre_auth"=>0);
            $jsonData               = json_encode($postFields);
            $ch                     = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://apiprod.fattlabs.com/charge");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
            curl_setopt($ch, CURLOPT_HEADER, FALSE);
            curl_setopt($ch, CURLOPT_POST, TRUE);
            DevDebug::create(['sr_no' => 115, 'result' => 'Movers Payment request: ' . $jsonData, 'created_at' => date('Y-m-d H:i:s')]);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/json",
                "Authorization:Bearer ".$omni_token,
                "Accept: application/json"
            ));
            $response               = curl_exec($ch);

            curl_close($ch);
            $jsonResponse           = json_decode($response, true);
            DevDebug::create(['sr_no' => 116, 'result' => 'Movers Payment response: ' . json_encode($jsonResponse), 'created_at' => date('Y-m-d H:i:s')]);

            //Added by BK on 03/02/2025 convenience fee to evey payment
            if ($convenienceFee > 0) {
                $businessCampaignCharge = $businessCampaignCharge - $convenienceFee;
            }

            $responseStatus         = "no";
            if (isset($jsonResponse['success']) && $jsonResponse['success'] == true) {
                $responseStatus     = "yes";
            }

            if ($businessCampaignType == 2) {
                $totalBalance = Campaign::where('campaign_id', $businessCampaignId)->get(['credit_available']);
                $beforeCharge = 0;
                if (count($totalBalance) > 0) {
                    $beforeCharge = $totalBalance[0]->credit_available;
                }

                $amountCharge = $businessCampaignCharge;
                $afterCharge = $beforeCharge;
                if($responseStatus == "yes") {
                    $afterCharge = $beforeCharge + $amountCharge;
                }

                $businessCampaignPaymentId = BusinessCampaignPayment::create([
                    'campaign_id' => $businessCampaignId,
                    'business_cc_id' => $cardId,
                    'balance_before_charge' => $beforeCharge,
                    'amount_charge' => $amountCharge,
                    'balance_after_charge' => $afterCharge,
                    'convenience_fee' => $convenienceFee,
                    'charge_type_id' => 1,
                    'charge_method_type' => 'moverinterfaceuser',
                    'charge_user_id' => $businessId,
                    'charge_method' => 'other',
                    'fatt_payment_id' => (isset($jsonResponse['id'])) ? $jsonResponse['id'] : '',
                    'is_charge_approved' => $responseStatus,
                    'created_at' => date('Y-m-d H:i:s'),
                    'charge_user_ip' => $ipAddress,
                    'charge_user_agent' => $userAgent
                ])->business_campaign_payment_id;
            } else {
                $totalBalance = Business::where('business_id', $businessCampaignId)->get(['credit_available']);
                $beforeCharge = 0;
                if(count($totalBalance) > 0) {
                    $beforeCharge = $totalBalance[0]->credit_available;
                }
                $amountCharge = $businessCampaignCharge;
                $afterCharge = $beforeCharge;
                if($responseStatus == "yes") {
                    $afterCharge = $beforeCharge + $amountCharge;
                }

                $businessCampaignPaymentId = BusinessCampaignPayment::create([
                    'business_id' => $businessCampaignId,
                    'business_cc_id' => $cardId,
                    'balance_before_charge' => $beforeCharge,
                    'amount_charge' => $amountCharge,
                    'balance_after_charge' => $afterCharge,
                    'convenience_fee' => $convenienceFee,
                    'charge_type_id' => 1,
                    'charge_method_type' => 'moverinterfaceuser',
                    'charge_user_id' => $businessId,
                    'charge_method' => 'other',
                    'fatt_payment_id' => (isset($jsonResponse['id'])) ? $jsonResponse['id'] : '',
                    'is_charge_approved' => $responseStatus,
                    'created_at' => date('Y-m-d H:i:s'),
                    'charge_user_ip' => $ipAddress,
                    'charge_user_agent' => $userAgent
                ])->business_campaign_payment_id;
            }

            BusinessCampaingnPaymentLog::create([
                'business_campaign_payment_id' => $businessCampaignPaymentId,
                'request' => $jsonData,
                'response' => json_encode($jsonResponse, true),
                'created_at' => date('Y-m-d H:i:s'),
            ])->business_campaign_payment_log_id;

            //fetch total balance from businessespaymenthistory table
            if ($responseStatus == "yes") {
                if($businessCampaignType == 2) {
                    //update balance in campaign table
                    Campaign::where('campaign_id', $businessCampaignId)
                        ->update([
                            'credit_available' => $afterCharge
                        ]);
                } else {
                    //update balance in business table
                    Business::where('business_id', $businessCampaignId)
                        ->update([
                            'credit_available' => $afterCharge
                        ]);
                }

                //Added by BK on 05/02/2025 convenience fee email to evey success payment
                $transactionId = (isset($jsonResponse['id'])) ? $jsonResponse['id'] : '';
                $businessPaymentObj->moversRechargeEmailNotificationTemplate($businessId, $businessName, $businessCampaignName, $businessEmail, $cardDetail->card, $businessCampaignCharge, $convenienceFee, $transactionId);
            } else {
                if (isset($jsonResponse['message'])) {
                    throw new Exception($jsonResponse['message']);
                } else {
                    throw new Exception('Unable to process the purchase transaction.');
                }
            }
            $status = 1;
            $message = 'Success';
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => array('code' => "System Error", 'message' => $message)
        ];

        return response()->json($responseData);
    }

    public function paymentCardAdd(Request $request) {
        $status = 0;
        $message = '';
        $error = array();
        try {

            $businessId     = $request->businessId;
            $lastFour       = $request->cardNumber;
            $paymentCustId  = $request->paymentCustId;
            $paymentCardId  = $request->paymentCardId;
            $cardType       = $request->cardType;
            $expiry         = $request->expiry;
            $ip             = $request->ip;

            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                throw new Exception('Invalid email or password');
            }

            $businessCCId = BusinessCC::create([
                'business_id' => $businessId,
                'payment_cust_id' => $paymentCustId,
                'card' => $lastFour,
                'card_type' => $cardType,
                'payment_card_id' => $paymentCardId,
                'created_by' => "user",
                'created_at' => date('Y-m-d H:i:s')
            ])->business_cc_id;

            Business::where('business_id', $businessId)->update([
                'payment_cust_id' => $paymentCustId
            ]);

            $businessCCContractId = BusinessCCContract::create([
                'business_id' => $businessId,
                'business_cc_id' => $businessCCId,
                'email' => $businessDetail->email,
                'created_at' => date('Y-m-d H:i:s')
            ])->business_cc_contract_id;

            $mover_dashboard_url = Helper::checkServer()['mover_dashboard_url'];
            $cardContract = [];
            $cardContract['owner_name'] = $businessDetail->owner_name;
            $cardContract['business_name'] = $businessDetail->business_name;
            $cardContract['email'] = $businessDetail->email;
            $cardContract['payment_amount'] = '';
            $cardContract['recurrent_amount'] = '';
            $cardContract['recurrent_type'] = '';
            $cardContract['last_four'] = $lastFour;
            $cardContract['signature'] = '';
            $cardContract['matrix_url'] = Helper::checkServer()['matrixBaseURL_Outbound'];
            $cardContract['ip'] = $ip;
            $cardContract['exp_year'] = $expiry;
            $cardContract['leadstudio_logo'] = $mover_dashboard_url . '/assets/images/email-image/logo.png';
            view()->share('cardcontract', $cardContract);
            $pdf = PDF::loadView( 'contract.cardcontractpdf', $cardContract );
            //pdf save in card_contract_signed_pdf folder
            $path = public_path().'/card_contract_signed_pdf/';
            $fileName =  $businessCCContractId .'_'.rand() . '_' . time().'.'. 'pdf';
            $pdf->save($path . $fileName);

            $pdffile = public_path().'/card_contract_signed_pdf/'.$fileName;
            // echo "<pre>charge = ";
            // print_r( $pdffile);
            // die;

            //pdf storage in s3 bucket
            $filePath = $path . $fileName; //server storage folder path
            $storagePath = $businessId . '/' . $fileName; //s3 bucket path
            if (isset($fileName) && file_exists($filePath)) {
                Storage::disk('s3')->put($storagePath, file_get_contents($filePath));
            }
            BusinessCCContract::where('business_cc_contract_id', $businessCCContractId)->update([
                'contract_screenshot' => $fileName,
                "status" => "signed"
            ]);

            //send email to movers during add new card
            $businessPaymentControllerObj = new BusinessPaymentController();
            $businessPaymentControllerObj->addCardEmailNotificationTemplate($businessDetail->business_id, $businessDetail->display_name, $businessDetail->email, $lastFour);

            $status = 1;
            $message = 'Success';
        }
        catch(Exception $e){
            $message = $e->getMessage();
        }

        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => array('code'=>"System Error", 'message' => $message)
        ];
        return response()->json($responseData);
    }

    public function moverTalkedBooked() {
        $status = 0;
        $message = '';
        try {

            $elements           = json_decode(file_get_contents('php://input'), true);
            //print_r($elements); die;
            if (empty($elements['leadId'])) {
                throw new Exception("leadId required");
            }
            if (empty($elements['businessId'])) {
                throw new Exception("businessId required");
            }
            $businessId         = $elements['businessId'];
            $campaignId         = $elements['campaignId'];
            $leadId             = $elements['leadId'];
            $checkedValue       = $elements['checkedValue'];
            $type               = $elements['type'];
            $bookedDate         = $elements['bookedDate'] ?? '';
            $bookedPrice        = $elements['bookedPrice'] ?? '';
            $businessDetail     = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            } else {
                $checkLeadRecord = LeadCustomerReview::where('lead_id', $leadId)->where('business_id', $businessId)->first();
                if (isset($checkLeadRecord)) {
                    if ($type == "1" || $type == 1) {
                        $record = [
                            'is_customer_talked' => ($checkedValue == 1) ? 'yes' : 'no'
                        ];
                    } else if ($type == "2" || $type == 2) {
                        $record = [
                            'is_booked' => ($checkedValue == 1) ? 'yes' : 'no'
                        ];
                    } else if ($type == "3" || $type == 3) {
                        $record = [
                            'is_quoted' => ($checkedValue == 1) ? 'yes' : 'no'
                        ];
                    }

                    LeadCustomerReview::where('lead_id', $leadId)
                        ->where('business_id', $businessId)
                        ->update($record);
                } else {
                    $reviewArray = [];
                    $routingId = $callId = 0;
                    $routingDetail = LeadRouting::where('lead_id', $leadId)->get(['lead_routing_id'])->toArray();
                    $callDetail = LeadCall::where('lead_id', $leadId)->get(['lead_call_id'])->toArray();
                    if(count($routingDetail) > 0){
                        $routingId = $routingDetail[0]['lead_routing_id'];
                    }
                    if(count($callDetail) > 0){
                        $callId = $callDetail[0]['lead_call_id'];
                    }
                    if ($type == "1" || $type == 1) {
                        $reviewArray['is_customer_talked'] = ($checkedValue == 1) ? 'yes' : 'no';
                    } else if ($type == "2" || $type == 2){
                        $reviewArray['is_booked'] = ($checkedValue == 1) ? 'yes' : 'no';
                        $newreviewArray['is_booked'] = ($checkedValue == 1) ? 'yes' : 'no';
                    } else if ($type == "3" || $type == 3){
                        $reviewArray['is_quoted'] = ($checkedValue == 1) ? 'yes' : 'no';
                    }

                    $reviewArray['lead_routing_id'] = $routingId;
                    $reviewArray['lead_id'] = $leadId;
                    $reviewArray['lead_call_id'] = $callId;
                    $reviewArray['business_id'] = $businessId;
                    $reviewArray['quote_amt'] =
                    $reviewArray['rating'] = 0;
                    $reviewArray['comments'] = $reviewArray['is_display'] = '';
                    $reviewArray['is_display'] = 'no';
                    $reviewArray['created_at'] = date("Y-m-d H:i:s");
                    LeadCustomerReview::insert($reviewArray);

                    if (!empty($bookedDate) && !empty($bookedPrice)) {
                        $newreviewArray['lead_id'] = $leadId;
                        $newreviewArray['lead_routing_id'] = $routingId;
                        $newreviewArray['business_id'] = $businessId;
                        $newreviewArray['campaign_id'] = $campaignId;
                        $newreviewArray['booked_date'] = date("Y-m-d", strtotime($bookedDate));
                        $newreviewArray['booked_price'] = $bookedPrice;
                        $newreviewArray['created_at'] = date("Y-m-d H:i:s");
                        LeadRevShareCustomerReview::insert($newreviewArray);
                    }
                }
            }

            $status = 1;
            $message = "Success";
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message
        ];

        return response()->json($responseArray);
    }

    public function moverMarketplaceActiveInactive() {
        $status = 0;
        $message = '';
        try {
            $elements           = json_decode(file_get_contents('php://input'), true);
            if (empty($elements['businessId'])) {
                throw new Exception("businessId required");
            }
            $businessId         = $elements['businessId'];
            $businessDetail     = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            } else {
                $campaignDetail     = Campaign::where('business_id', $businessId)->where('campaign_type_id', 7)->where('is_active', 'yes')->get()->count('campaign_id');
                if ($campaignDetail > 0) {
                    $status = 1;
                    $message = 'success';
                }
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message
        ];
        return response()->json($responseArray);
    }

    public function moverMarketplaceLead(Request $request) {
        $status = $businessId = $mPercentage = 0;
        $message = $businessName = '';
        $finalArray = $stateArray = $campaignArray = [];
        try {
            $elements = json_decode(file_get_contents('php://input'), true);
            //print_r($elements); die;

            /*if (empty($elements['startDate'])) {
                throw new Exception("startDate required");
            }
            if (empty($elements['endDate'])) {
                throw new Exception("endDate required");
            }*/
            if (empty($elements['businessId'])) {
                throw new Exception("businessId required");
            }

            //$startDate        = $elements['startDate'].' 00:00:00';
            //$endDate          = $elements['endDate'].' 23:59:59';
            $businessId         = $elements['businessId'];
            $businessDetail     = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            } else {
                $businessName   = $businessDetail->display_name;
                $campaignDetail = DB::select("SELECT *,
                    CASE
                        WHEN (m.move_type='long' AND c.campaign_type_id=1) THEN 6
                        WHEN (m.move_type='long' AND c.campaign_type_id=2) THEN 5
                        WHEN (m.move_type='long' AND c.campaign_type_id=6) THEN 4
                        WHEN (m.move_type='local' AND c.campaign_type_id=1) THEN 3
                        WHEN (m.move_type='local' AND c.campaign_type_id=2) THEN 2
                        WHEN (m.move_type='local' AND c.campaign_type_id=6) THEN 1
                    END AS campaign FROM campaign c LEFT JOIN campaign_moving m ON c.campaign_id = m.campaign_id WHERE c.business_id='".$businessDetail->business_id."' AND c.campaign_type_id!=3 AND c.lead_type_id=1 AND c.is_active='yes' AND c.lead_category_id=1 ORDER BY
                    CASE
                        WHEN (m.move_type='long' AND c.campaign_type_id=1) THEN 6
                        WHEN (m.move_type='long' AND c.campaign_type_id=2) THEN 5
                        WHEN (m.move_type='long' AND c.campaign_type_id=6) THEN 4
                        WHEN (m.move_type='local' AND c.campaign_type_id=1) THEN 3
                        WHEN (m.move_type='local' AND c.campaign_type_id=2) THEN 2
                        WHEN (m.move_type='local' AND c.campaign_type_id=6) THEN 1
                    END DESC;");
                foreach ($campaignDetail as $campaign) {
                    $dataArray['campaignName'] = $campaign->campaign_name;
                    $dataArray['campaignId'] = $campaign->campaign_id;
                    $dataArray['balance'] = $campaign->credit_available + $campaign->credit_reserved;
                    if ($campaign->campaign_type_id == 7) {
                        $finalArray[] = $dataArray;
                    } else {
                        $campaignArray[] = $dataArray;
                    }
                }

                $stateDetail = MstZipcode::groupBy('state')->get();;
                foreach ($stateDetail as $state) {
                    $data1Array['stateName'] = strtoupper($state->state);
                    $stateArray[] = $data1Array;
                }

                $marketplacePercentage = LeadMarketplaceCoupon::where('start_date', '<=', date('Y-m-d', time()))->where('end_date', '>=', date('Y-m-d', time()))->whereNull('coupon_code')->first();
                if (!empty($marketplacePercentage)) {
                    $mPercentage = $marketplacePercentage->value;
                }
            }
            $status = 1;
            $message = 'success';
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'businessId' => $businessId,
            'businessName' => $businessName,
            'marketplacePercentage' => $mPercentage,
            'data' => $finalArray,
            'campaign' => $campaignArray,
            'state' => $stateArray
        ];
        return response()->json($responseArray);
    }

    public function moverMarketplaceLeadData(Request $request) {
        $status = $businessId = 0;
        $message = $where = $limit = '';
        $finalArray = $leadIdArray = $leadDetail = $routingleadIdArray = $campaignIdArray = $smsverifyleadIdArray = $activeCampAssocDataAr = [];
        try {

            $elements = json_decode(file_get_contents('php://input'), true);
            //print_r($elements); die;

            if (empty($elements['startDate'])) {
                throw new Exception("startDate required");
            }
            if (empty($elements['endDate'])) {
                throw new Exception("endDate required");
            }
            if (empty($elements['businessId'])) {
                throw new Exception("businessId required");
            }

            $startDate          = $elements['startDate'].' 00:00:00';
            $endDate            = $elements['endDate'].' 23:59:59';
            $businessId         = $elements['businessId'];
            $businessDetail     = Business::where('business_id', $businessId)->first();
            $subscriptionDetail = Subscription::where('subscription_start', '>=', date('Y-m-01'))->where('subscription_end', '<=', date('Y-m-t'))->where('is_active', 'yes')->where('business_id', $businessId)->first();
            if (isset($subscriptionDetail)) {
                $subscriptionPlanDetail = MstSubscriptionPlan::where('subscription_plan_id', $subscriptionDetail->subscription_plan_id)->first();
            }
            $currentDate        = date('Y-m-d');
            $currentDateTime    = date('Y-m-d H:i:s');
            $twomiutes          = new DateTime($currentDateTime);
            $twomiutes->modify('-2 minutes');
            $twomiutes          = $twomiutes->format('Y-m-d H:i:s');
            if ($endDate >= $currentDate) {
                $endDate        = $twomiutes;
            }
            $leadType           = $elements['leadType'];
            $fromPrice          = $elements['fromPrice'];
            $toPrice            = $elements['toPrice'];
            $limit              = $elements['limit'] ?? 0;
            $orderBy            = $elements['orderBy'] ?? 'DESC'; //ASC //DESC
            $moveDateArray      = explode(' - ', $elements['moveDate']);
            $moveDateStart      = date("Y-m-d", strtotime($moveDateArray[0]));
            $moveDateEnd        = date("Y-m-d", strtotime($moveDateArray[1]));
            $campaignIdArray = Campaign::where('business_id', $businessId)->pluck('campaign_id')->toArray();
            $campaignIds    = implode(',', $campaignIdArray);
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            } else {
                if ($leadType == '0' || $leadType == '-1' || $leadType == '-2') {
                    $campaignLocations = DB::table('campaign_location')
                        ->whereIn('campaign_id', $campaignIdArray)
                        ->get()
                        ->groupBy('campaign_id');
                } else {
                    $campaignLocations = DB::table('campaign_location')
                        ->whereIn('campaign_id', [$leadType])
                        ->get()
                        ->groupBy('campaign_id');
                }

                $sourceMap = [];
                $destinationMap = [];

                foreach ($campaignLocations as $cid => $locations) {
                    $sourceMap[$cid] = ['state' => [], 'zipcode' => [], 'areacode' => []];
                    $destinationMap[$cid] = ['state' => [], 'zipcode' => [], 'areacode' => []];

                    foreach ($locations as $loc) {
                        if ($loc->location_type === 'source') {
                            $sourceMap[$cid][$loc->location_coverage][] = $loc->value;
                        } elseif ($loc->location_type === 'destination') {
                            $destinationMap[$cid][$loc->location_coverage][] = $loc->value;
                        }
                    }
                }
                if ($leadType == '0' || $leadType == '-1' || $leadType == '-2') {

                    $leadQuery = DB::table('lead as l')
                        ->select(
                            'l.lead_id',
                            'l.name',
                            'l.created_at as lead_created_at',
                            'l.is_verified',
                            'l.remaining_slot',
                            'lm.from_state',
                            'lm.to_state',
                            'lm.from_zipcode',
                            'lm.to_zipcode',
                            'lm.from_areacode',
                            'lm.to_areacode',
                            'lm.created_at',
                            'lm.move_size_id',
                            'lm.move_date',
                            'o.lead_source_name'
                        )
                        ->join('lead_moving as lm', 'l.lead_id', '=', 'lm.lead_id')
                        ->join('mst_lead_source as o', 'o.lead_source_id', '=', 'l.lead_source_id')
                        ->whereBetween('l.created_at', [$startDate, $endDate])
                        ->where('l.remaining_slot', '>', 0)
                        ->whereNotIn('l.sold_type', ['exclusive', 'organic'])
                        ->whereNotIn('l.lead_id', function ($q) use ($campaignIdArray) {
                            $q->select('lead_id')
                                ->from('lead_routing')
                                ->whereIn('campaign_id', $campaignIdArray)
                                ->groupBy('lead_id');
                        })
                        ->where('l.is_dnc_call', 'no')
                        ->where('l.is_marketplace', 'yes')
                        ->where(function ($query) use ($sourceMap, $destinationMap) {
                            foreach ($sourceMap as $campaignId => $src) {
                                $dst = $destinationMap[$campaignId];

                                $query->orWhere(function ($q) use ($src, $dst) {
                                    $q->where(function ($q2) use ($src) {
                                        $q2->whereIn('lm.from_state', $src['state'] ?? [])
                                            ->orWhereIn('lm.from_zipcode', $src['zipcode'] ?? [])
                                            ->orWhereIn('lm.from_areacode', $src['areacode'] ?? []);
                                    })->where(function ($q3) use ($dst) {
                                        $q3->whereIn('lm.to_state', $dst['state'] ?? [])
                                            ->orWhereIn('lm.to_zipcode', $dst['zipcode'] ?? [])
                                            ->orWhereIn('lm.to_areacode', $dst['areacode'] ?? []);
                                    });
                                });
                            }
                        });

                    if ($leadType === '0') {
                        $leadQuery->whereIn('lm.move_type', ['local', 'long']);
                    } elseif ($leadType === '-1') {
                        $leadQuery->where('lm.move_type', 'local');
                    } elseif ($leadType === '-2') {
                        $leadQuery->where('lm.move_type', 'long');
                    }
                    // $leadDetail     = DB::select("SELECT l.*, lm.*, o.lead_source_name
                    //     FROM `lead` l
                    //     INNER JOIN lead_moving lm ON l.lead_id = lm.lead_id
                    //     INNER JOIN mst_lead_source o ON o.lead_source_id = l.lead_source_id
                    //     WHERE l.remaining_slot > 0 AND l.sold_type not in ('exclusive','organic') AND l.created_at >= '".$startDate."' AND l.created_at <= '".$endDate."'
                    //     AND l.lead_id NOT IN (SELECT lead_id FROM lead_routing WHERE campaign_id IN ($campaignIds) AND created_at >= '".$startDate."' AND created_at <= '".$endDate."' GROUP BY lead_id)
                    //     AND l.is_dnc_call = 'no' AND is_marketplace = 'yes' $where ORDER BY l.lead_id " . $orderBy);
                } else {
                    $campaignId = $leadType;
                    $leadQuery = DB::table('lead as l')
                        ->select(
                            'l.lead_id',
                            'l.name',
                            'l.created_at as lead_created_at',
                            'l.is_verified',
                            'l.remaining_slot',
                            'lm.from_state',
                            'lm.to_state',
                            'lm.from_zipcode',
                            'lm.to_zipcode',
                            'lm.from_areacode',
                            'lm.to_areacode',
                            'lm.created_at',
                            'lm.move_size_id',
                            'lm.move_date',
                            'o.lead_source_name'
                        )
                        ->join('lead_moving as lm', 'l.lead_id', '=', 'lm.lead_id')
                        ->join('mst_lead_source as o', 'o.lead_source_id', '=', 'l.lead_source_id')
                        ->whereBetween('l.created_at', [$startDate, $endDate])
                        ->where('l.remaining_slot', '>', 0)
                        ->whereNotIn('l.sold_type', ['exclusive', 'organic'])
                        ->whereNotIn('l.lead_id', function ($q) use ($campaignIdArray) {
                            $q->select('lead_id')
                                ->from('lead_routing')
                                ->whereIn('campaign_id', $campaignIdArray)
                                ->groupBy('lead_id');
                        })
                        ->where('lm.move_type', function ($q) use ($campaignId) {
                            $q->select('move_type')
                                ->from('campaign_moving')
                                ->where('campaign_id', $campaignId)
                                ->limit(1);
                        })
                        ->where('l.is_dnc_call', 'no')
                        ->where('l.is_marketplace', 'yes')
                        ->where(function ($query) use ($sourceMap, $destinationMap) {
                            foreach ($sourceMap as $campaignId => $src) {
                                $dst = $destinationMap[$campaignId];

                                $query->orWhere(function ($q) use ($src, $dst) {
                                    $q->where(function ($q2) use ($src) {
                                        $q2->whereIn('lm.from_state', $src['state'] ?? [])
                                            ->orWhereIn('lm.from_zipcode', $src['zipcode'] ?? [])
                                            ->orWhereIn('lm.from_areacode', $src['areacode'] ?? []);
                                    })->where(function ($q3) use ($dst) {
                                        $q3->whereIn('lm.to_state', $dst['state'] ?? [])
                                            ->orWhereIn('lm.to_zipcode', $dst['zipcode'] ?? [])
                                            ->orWhereIn('lm.to_areacode', $dst['areacode'] ?? []);
                                    });
                                });
                            }
                        });
                }
                if ($moveDateEnd > $currentDate) {
                    $leadQuery->whereBetween('lm.move_date', [$moveDateStart, $moveDateEnd]);
                }
                if (!empty($elements['fromState'])) {
                    $leadQuery->whereIn('lm.from_state', $elements['fromState']);
                }

                if (!empty($elements['fromZip'])) {
                    $qfromZip = explode(',', rtrim($elements['fromZip'], ','));
                    $leadQuery->whereIn('lm.from_zipcode', $qfromZip);
                }

                if (!empty($elements['toState'])) {
                    $leadQuery->whereIn('lm.to_state', $elements['toState']);
                }

                if (!empty($elements['toZip'])) {
                    $qtoZip = explode(',', rtrim($elements['toZip'], ','));
                    $leadQuery->whereIn('lm.to_zipcode', $qtoZip);
                }

                if (!empty($elements['moveSize'])) {
                    $leadQuery->whereIn('lm.move_size_id', $elements['moveSize']);
                }
                $leadQuery->orderBy('l.lead_id', $orderBy);

                foreach ($leadQuery->get() as $lead) {
                    //DB::table('test')->insert(['result' => "lead start = ".$lead->lead_id]);
                    $fromData = MstZipcode::where('zipcode', $lead->from_zipcode)->first();
                    $toData = MstZipcode::where('zipcode', $lead->to_zipcode)->first();
                    $fromState = $lead->from_state;
                    $toState = $lead->to_state;
                    $fromZip = $lead->from_zipcode;
                    $toZip = $lead->to_zipcode;
                    $fromAreaCode = $lead->from_areacode;
                    $toAreaCode = $lead->to_areacode;

                    $distance = 0;
                    if(!empty($fromData->lat) && !empty($toData->lat) && !empty($fromData->long) && !empty($toData->long)) {
                        $distance = CommonFunctions::calculateMileage($fromData->lat, $toData->lat, $fromData->long, $toData->long);
                    }
                    $dateTimeReceived = $lead->created_at;
                    $originName = $lead->lead_source_name;
                    $pos = strpos($originName, 'IS');
                    //check ISM & VM(MA) lead time difference
                    $difference = abs(strtotime($currentDateTime) - strtotime($dateTimeReceived)) / 60;
                    if (($originName == "VM(MA)" || $originName == "VM(MAV)" || $pos !== false) && $difference < 20) {
                        //DB::table('test')->insert(['result' => "lead start originName = ".$originName]);
                        continue;
                    }
                    //check lead already routed or not
                    /*if (in_array($lead->lead_id, $routingleadIdArray)) {
                        continue;
                    }*/
                    //check lead is DNC or not
                    /*$checkDNC = DoNotCall::where('phone', $lead->phone)->first();
                    if (isset($checkDNC)) {
                        continue;
                    }*/
                    //check lead is Booked or not
                    /*$checkBooked = LeadsBooking::where('lead_id', $lead->lead_id)->first();
                    if (isset($checkBooked)) {
                        continue;
                    }*/
                    //campaign wise lead
                    if ($leadType > 0) {
                        // Logic 1 Check CoverageType ( Local, Long Distance) [0 - Local, 1 - Long]
                        // $leadCampaignLogic = new LeadCampaignLogic();
                        //$coverageType = $logicLead->checkCoverageType($fromState, $toState);

                        // Logic 2 List all active campaign and businesses based on Logic 1
                        /*$logicCampaign = new LogicCampaign();
                        if ($lead->is_verified == 0){
                            $businessesCampaign = $logicCampaign->activeCamapignMarketplaceNonVerify($coverageType, 0, $lead->move_size, $businessesId);
                        }else{
                            $businessesCampaign = $logicCampaign->activeCamapignMarketplace($coverageType, 0, $lead->move_size, $businessesId);
                        }*/

                        $campaignIds = array();
                        /*foreach ($businessesCampaign[0]['businessesCampaign'] as $campaign) {
                            if (!in_array($campaign->id, $campaignIds)) {
                                $campaignIds[] = $campaign->id;
                            }
                        }
                        if (count($campaignIds) == 0) {
                            continue;
                        }*/
                        $campaignIds[] = $leadType;

                        // Logic 3 Check fromZip
                        // $campaignIds = $leadCampaignLogic->checkFromZip($campaignIds, $activeCampAssocDataAr, $fromZip, $toZip, $fromState, $toState, $fromAreaCode, $toAreaCode);
                        // //DB::table('test')->insert(['result' => "lead start checkFromZip = ".json_encode($campaignIds)]);
                        // if (count($campaignIds) == 0) {
                        //     continue;
                        // }
                        // $source = $sourceMap[$campaignId] ?? ['state' => [], 'zipcode' => [], 'areacode' => []];
                        // $dest   = $destinationMap[$campaignId] ?? ['state' => [], 'zipcode' => [], 'areacode' => []];
                        // $matchFound = false;
                        // if (
                        //     (in_array($lead->from_state, $source['state']) && in_array($lead->to_state, $dest['state'])) ||
                        //     (in_array($lead->from_zipcode, $source['zipcode']) && in_array($lead->to_zipcode, $dest['zipcode'])) ||
                        //     (in_array($lead->from_areacode, $source['areacode']) && in_array($lead->to_areacode, $dest['areacode'])) ||
                        //     (in_array($lead->from_state, $source['state']) && in_array($lead->to_zipcode, $dest['zipcode'])) ||
                        //     (in_array($lead->from_state, $source['state']) && in_array($lead->to_areacode, $dest['areacode'])) ||
                        //     (in_array($lead->from_zipcode, $source['zipcode']) && in_array($lead->to_state, $dest['state'])) ||
                        //     (in_array($lead->from_zipcode, $source['zipcode']) && in_array($lead->to_areacode, $dest['areacode'])) ||
                        //     (in_array($lead->from_areacode, $source['areacode']) && in_array($lead->to_state, $dest['state'])) ||
                        //     (in_array($lead->from_areacode, $source['areacode']) && in_array($lead->to_zipcode, $dest['zipcode']))
                        // ) {
                        //     $matchFound = true;
                        // }
                        // if (!$matchFound) continue;
                    }
                    //get price by using lead date time received & verify
                    $coverageType = 'local';
                    if(strcmp($fromState, $toState)) {
                        $coverageType = 'long';
                    }
                    $price = $this->getMarketplaceLeadPrice($dateTimeReceived, $lead->is_verified);
                    //DB::table('dev_debug')->insert(['result' => "lead getMarketplaceLeadPrice = ".$price]);
                    if ($lead->remaining_slot == 4) {
                        $price = $price + 4;
                    } else if ($lead->remaining_slot == 3) {
                        $price = $price + 2;
                    } else if ($lead->remaining_slot == 2) {
                        $price = $price + 1;
                    } else if ($lead->remaining_slot == 1) {
                        $price = $price + 1;
                    }
                    if ($coverageType == 'local') {
                        $price = ceil(($price * 60) / 100);
                    }

                    //offer - 50% off on local and long leads - for all marketplace leads.
                    //$discountStartDate = date('Y-m-d H:i:s', strtotime('2024-12-31 00:00:00'));
                    //$discountEndDate = date('Y-m-d H:i:s', strtotime('2025-01-01 23:59:59'));
                    //$discountStartDate = date("Y-m-d", strtotime("saturday this week")) . ' 00:00:00';
                    //$discountEndDate = date("Y-m-d", strtotime("sunday this week")) . ' 23:59:59';
                    //for day difference
                    //$timestamp2 = strtotime($currentDateTime);
                    //$timestamp1 = strtotime($dateTimeReceived);
                    //$dayDifference = abs($timestamp2 - $timestamp1) / (60 * 60 * 24); //for day difference
                    //$newPrice = 0;
                    //if ((strtotime($currentDateTime) >= strtotime($discountStartDate) && strtotime($currentDateTime) < strtotime($discountEndDate)) && $dayDifference > 1) {
                    //    $newPrice = ($price * 70) / 100; //30% off on leads older than 24 hours
                    //    //$newPrice = ($price * 50) / 100; //50% off on leads older than 24 hours
                    //}

                    $newPrice = 0;
                    if (isset($subscriptionPlanDetail) && $subscriptionPlanDetail->dicount_percentage > 0) {
                        $newPrice = ($price * (100 - $subscriptionPlanDetail->dicount_percentage)) / 100;
                    }

                    if (isset($fromPrice) && isset($toPrice)) {
                        if ($price >= $fromPrice && $price <= $toPrice) {
                        } else {
                            continue;
                        }
                    }
                    //Logic for check lead is hotdeal or not
                    $isHotDeal = 0;
                    if ($lead->remaining_slot >= 3 && $lead->remaining_slot <= 4 && $lead->is_verified == 'yes') {
                        $isHotDeal = 1;
                    } else if ($distance > 1000 && $lead->is_verified == 'yes') { //1000 Miles
                        $isHotDeal = 1;
                    } else if ($lead->move_size_id >= 3 && $lead->is_verified == 'yes') { //2 = Two Bedroom
                        $isHotDeal = 1;
                    } else if (in_array($lead->lead_id, $smsverifyleadIdArray) && $lead->is_verified == 'yes') {
                        $isHotDeal = 1;
                    }

                    $data['leadId'] = $lead->lead_id;
                    $data['name'] = $lead->name;
                    $data['fromZipCode'] = str_pad($lead->from_zipcode, 5, '0', STR_PAD_LEFT);
                    $data['fromState'] = $lead->from_state;
                    $data['toZipCode'] = str_pad($lead->to_zipcode, 5, '0', STR_PAD_LEFT);
                    $data['toState'] = $lead->to_state;
                    $data['moveSize'] = ($lead->move_size_id == 1 ) ? "Studio" : $lead->move_size_id . " BR";
                    $data['moveDate'] = date("m/d/y", strtotime($lead->move_date));
                    $data['createdDate'] = date("m/d/y", strtotime($lead->created_at));
                    $data['createdTime'] = date("h:i A", strtotime($lead->created_at));
                    $data['sortTime'] = date("H:i:s", strtotime($lead->created_at));
                    $data['isVerified'] = $lead->is_verified;
                    $data['price'] = $price;
                    $data['newPrice'] = $newPrice;
                    $data['type'] = 0;
                    $data['isHotDeal'] = $isHotDeal;
                    //DB::table('dev_debug')->insert(['result' => "lead finadata = ".json_encode($data)]);
                    $finalArray[] = $data;
                    //DB::table('test')->insert(['result' => "lead end = ".$lead->lead_id]);
                }
                if ($limit > 0 && count($finalArray) > 0) {
                    $finalArray = array_slice($finalArray, 0, $limit);
                }
                //print_r($finalData); die;
            }
            $status = 1;
            $message = 'success';
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'data' => $finalArray
        ];
        return response()->json($responseArray);
    }

    public function moverMarketplaceLeadDataSummary(Request $request)
    {
        $status = $businessId = 0;
        $message = $where = '';
        $finalArray = $movesizeArray = [];
        try {

            $elements = json_decode(file_get_contents('php://input'), true);
            //print_r($elements); die;

            if (empty($elements['startDate'])) {
                throw new Exception("startDate required");
            }
            if (empty($elements['endDate'])) {
                throw new Exception("endDate required");
            }
            if (empty($elements['businessId'])) {
                throw new Exception("businessId required");
            }

            $startDate          = $elements['startDate'].' 00:00:00';
            $endDate            = $elements['endDate'].' 23:59:59';
            $businessId         = $elements['businessId'];
            $businessDetail     = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            } else {
                //get all move size records from mst_move_size table
                $movesizeDetails = MstMoveSize::distinct()->get(['move_size_id', 'move_size']);
                for ($m = 0; $m < count($movesizeDetails); $m++) {
                    $movesizeArray[$movesizeDetails[$m]->move_size_id] = $movesizeDetails[$m]->move_size;
                }

                $campaignIdArray= Campaign::where('business_id', $businessId)->where('campaign_type_id', '7')->get(['campaign_id'])->toArray();
                //DB::enableQueryLog();
                $sqlQuery       = LeadRouting::select('lead_routing.created_at AS purchased_created_at', 'lead_routing.payout', 'lead.lead_id', 'lead.name', 'lead.email', 'lead.phone', 'lead.created_at', 'lead.created_at', 'lead_moving.from_zipcode', 'lead_moving.from_state', 'lead_moving.to_zipcode', 'lead_moving.to_state', 'lead_moving.move_size_id', 'lead_moving.move_date')
                    ->leftJoin('lead', 'lead_routing.lead_id', 'lead.lead_id')
                    ->join('lead_moving', 'lead.lead_id', '=', 'lead_moving.lead_id')
                    ->whereIn('lead_routing.campaign_id', $campaignIdArray)->where('lead_routing.route_status', 'sold')->where('lead_routing.created_at', '>=', $startDate)->where('lead_routing.created_at', '<=', $endDate);
                $leadDetails    = $sqlQuery->orderBy('lead.lead_id', 'DESC')->get()->toArray();
                //dd(\DB::getQueryLog());

                //echo $leadDetail; die;
                if (count($leadDetails) > 0) { for ($l=0; $l < count($leadDetails); $l++) {
                    $data['leadId'] = $leadDetails[$l]['lead_id'];
                    $data['name'] = $leadDetails[$l]['name'];
                    $data['email'] = $leadDetails[$l]['email'];
                    $data['phone'] = $leadDetails[$l]['phone'];
                    $data['fromZipCode'] = str_pad($leadDetails[$l]['from_zipcode'], 5, '0', STR_PAD_LEFT);
                    $data['fromState'] = $leadDetails[$l]['from_state'];
                    $data['toZipCode'] = str_pad($leadDetails[$l]['to_zipcode'], 5, '0', STR_PAD_LEFT);
                    $data['toState'] = $leadDetails[$l]['to_state'];
                    $data['moveSize'] = $movesizeArray[$leadDetails[$l]['move_size_id']];
                    $data['moveDate'] = date("m/d/y", strtotime($leadDetails[$l]['move_date']));
                    $data['createdDate'] = date("m/d/y", strtotime($leadDetails[$l]['created_at']));
                    $data['createdTime'] = date("h:i A", strtotime($leadDetails[$l]['created_at']));
                    $data['sortTime'] = date("H:i:s", strtotime($leadDetails[$l]['created_at']));
                    $data['purchasedDate'] = date("m/d/y", strtotime($leadDetails[$l]['purchased_created_at']));
                    $data['payout'] = $leadDetails[$l]['payout'];
                    $data['deliveryAgain'] = '';
                    $finalArray[] = $data;
                } }
                //print_r($finalData); die;
            }

            $status = 1;
            $message = 'success';
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'data' => $finalArray
        ];
        return response()->json($responseArray);
    }

    public function moverMarketplaceDeliveryAgain() {
        $status = 0;
        $message = '';
        try {
            $elements = json_decode(file_get_contents('php://input'), true);
            if (empty($elements['businessId'])) {
                throw new Exception("businessId required");
            }
            if (empty($elements['leadId'])) {
                throw new Exception("leadId required");
            }

            $businessId         = $elements['businessId'];
            $leadId             = $elements['leadId'];
            $businessDetail     = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            } else {
                $campaignDetail = Campaign::where('business_id', $businessId)->where('campaign_type_id', 7)->first();
                $leadRouting    = LeadRouting::where('lead_id', $leadId)->where('campaign_id', $campaignDetail->campaign_id)->first();

                $jsonArray = array(
                    "campaign_id" => $leadRouting->campaign_id,
                    "lead_id" => $leadId,
                    "cost" => $leadRouting->payout,
                    "balance" => 0,
                    "score" => $leadRouting->score
                );
                //dd($jsonArray);
                $leadDeliverApi = new LeadDeliveryApi();
                $leadDeliverApi->leadDeliveryTemplate(json_encode($jsonArray));

                $status = 1;
                $message = "Success";
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message
        ];
        return response()->json($responseArray);
    }

    public function moverApplyMarketplaceDiscount() {
        $status = $cPercentage = 0;
        $message = '';
        try {
            $elements           = json_decode(file_get_contents('php://input'), true);
            if (empty($elements['coupon_code'])) {
                throw new Exception("coupon code required");
            }
            $couponCode         = $elements['coupon_code'];
            $couponDetail       = LeadMarketplaceCoupon::where('coupon_code', $couponCode)->where('start_date', '<=', date('Y-m-d'))->where('end_date', '>=', date('Y-m-d'))->first();

            if (!$couponDetail) {
                throw new Exception('Invalid Coupon Code');
            } else {
                $status = 1;
                $message = 'success';
                $cPercentage = $couponDetail->value;
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'couponPercentage' => $cPercentage,
        ];
        return response()->json($responseArray);
    }

    public function moverMarketplaceLeadCharge(Request $request) {
        $status = $price = $mPercentage = $convenienceFee = 0;
        $message = 'Failure';
        $error = '';
        $leadIdsArray = $priceArray = $remaingSlot = [];

        $validator = Validator::make($request->all(), [
            'charge' => 'required|numeric',
            'lastFour' => 'required'
        ]);

        if ($validator->fails()) {
            return response()->json(array(
                'success' => 0,
                'message' => 'There are incorect values in the form!',
                'error' => $validator->getMessageBag()->toArray()
            ), 200);
            $this->throwValidationException(
                $request, $validator
            );
        }

        try {
            $businessId             = $request->get('businessId');
            $lastFour               = $request->get('lastFour');
            $charge                 = $request->get('charge');
            $leadId                 = $request->get('leadId');
            $couponCode             = $request->get('coupon_code');
            $ipAddress              = $request->get('ip');
            $userAgent              = $request->get('user_agent');
            $businessDetail         = Business::where('business_id', $businessId)->first();

            $marketplacePercentage  = LeadMarketplaceCoupon::where('start_date', '<=', date('Y-m-d'))->where('end_date', '>=', date('Y-m-d'))->whereNull('coupon_code')->first();
            //print_r($marketplacePercentage); die;
            $discount1              = 0;
            if (!empty($marketplacePercentage)) {
                $discount1          = $marketplacePercentage->value;
            }

            $couponDetail           = LeadMarketplaceCoupon::where('coupon_code', $couponCode)->first();
            //print_r($marketplacePercentage); die;
            $discount2              = 0;
            if (!empty($couponDetail)) {
                $discount2          = $couponDetail->value;
            }

            $mPercentage            = $discount1 + $discount2;
            for ($l=0; $l < count($leadId); $l++) {
                $leadIdsArray[]     = $leadId[$l]['leadId'];
                $priceArray[$leadId[$l]['leadId']] = $leadId[$l]['price'] - (($leadId[$l]['price'] * $mPercentage) /100);
            }

            $leadMarketplace        = Lead::whereIn('lead_id', $leadIdsArray)->get();
            unset($leadIdsArray);
            for ($m=0; $m < count($leadMarketplace); $m++) {
                if ($leadMarketplace[$m]['remaining_slot'] > 0) {
                    $remaingSlot[$leadMarketplace[$m]['lead_id']] = $leadMarketplace[$m]['remaining_slot'];
                    $leadIdsArray[] = $leadMarketplace[$m]['lead_id'];
                    $price += $priceArray[$leadMarketplace[$m]['lead_id']];
                }
            }
            $charge                 = $price;
            $cardDetail             = BusinessCC::where('business_cc_id', $lastFour)->where('status', 'active')->first();
            if ($cardDetail->payment_card_id == "") {
                throw new Exception('Unable to process the purchase transaction.');
            }
            $paymentCardId          = $cardDetail->payment_card_id;
            $cardId                 = $cardDetail->business_cc_id;

            //Added by BK on 03/02/2025 convenience fee to evey payment
            if ($charge > 0) {
                $convenienceFee     = ($charge * 3) / 100;
                $charge             = $charge + $convenienceFee;
            }

            $businessPaymentObj     = new BusinessPaymentController();
            $omni_token             = Helper::checkServer()['omni_token'];
            $ch                     = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://apiprod.fattlabs.com/charge");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
            curl_setopt($ch, CURLOPT_HEADER, FALSE);
            curl_setopt($ch, CURLOPT_POST, TRUE);
            $postFields             = array("payment_method_id" => $paymentCardId, "meta" => array("tax" => 0), "total" => $charge, "pre_auth" => 0);
            $jsonData               = json_encode($postFields);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/json",
                "Authorization:Bearer " . $omni_token,
                "Accept: application/json"
            ));

            $response               = curl_exec($ch);
            curl_close($ch);
            $jsonResponse           = json_decode($response, true);
            $responseStatus         = 0;
            $responseMessage        = '';
            if (isset($jsonResponse['success']) && $jsonResponse['success'] == true) {
                $responseStatus     = 1;
                $responseMessage    = 'Success';
            }

            //Added by BK on 03/02/2025 convenience fee to evey payment
            if ($convenienceFee > 0) {
                $charge             = $charge - $convenienceFee;
            }

            $totalBalance           = Campaign::where('business_id', $businessId)->where('campaign_type_id', 7)->get(['campaign_id', 'credit_available']);
            $campaignId             = $totalBalance[0]->campaign_id;
            $beforeCharge           = 0;
            if (count($totalBalance) > 0) {
                $beforeCharge       = $totalBalance[0]->credit_available;
            }

            $amountCharge           = $charge;
            $afterCharge            = $beforeCharge;
            if ($responseStatus == 1) {
                $afterCharge        = $beforeCharge + $amountCharge;
            }

            $businessCampaignPaymentId = BusinessCampaignPayment::create([
                'campaign_id' => $campaignId,
                'business_cc_id' => $cardId,
                'balance_before_charge' => $beforeCharge,
                'amount_charge' => $amountCharge,
                'balance_after_charge' => $afterCharge,
                'convenience_fee' => $convenienceFee,
                'charge_type_id' => 1,
                'charge_method_type' => 'moverinterfaceuser',
                'charge_user_id' => $businessId,
                'charge_method' => 'other',
                'fatt_payment_id' =>(isset($jsonResponse['id'])) ? $jsonResponse['id'] : '',
                'is_charge_approved' => ($responseStatus == 1) ? 'yes' : 'no',
                'created_at' => date('Y-m-d H:i:s'),
                'charge_user_ip' => $ipAddress,
                'charge_user_agent' => $userAgent
            ])->business_campaign_payment_id;

            BusinessCampaingnPaymentLog::create([
                'business_campaign_payment_id' => $businessCampaignPaymentId,
                'request' => $jsonData,
                'response' => json_encode($jsonResponse, true),
                'created_at' => date('Y-m-d H:i:s'),
            ]);

            if ($responseStatus == 1) {
                Campaign::where('campaign_id', $campaignId)
                    ->update([
                        'credit_available' => 0
                    ]);

                foreach ($leadIdsArray as $leadId) {
                    if ($remaingSlot[$leadId] > 0) {
                        LeadRouting::create([
                            'lead_id' => $leadId,
                            'campaign_id' => $campaignId,
                            'score' => 0,
                            'payout' => $priceArray[$leadId],
                            'route_status' => 'sold',
                            'is_free_lead' => 'no',
                            'created_at' => date('Y-m-d H:i:s')
                        ]);

                        $leadDetail = Lead::where('lead_id', $leadId)->first();
                        $updateRemaingSlot = $remaingSlot[$leadId] - 1;
                        Lead::where('lead_id', $leadDetail->lead_id)->update([
                            'payout' => $leadDetail->payout + $priceArray[$leadId],
                            'remaining_slot' => ($updateRemaingSlot > 0) ? $updateRemaingSlot : 0,
                            'is_marketplace' => ($updateRemaingSlot <= 0) ? 'no' : 'yes'
                        ]);
                        Lead::where('lead_id', $leadDetail->lead_id)->where('sold_type', 'none')->update(['sold_type' => 'premium']);
                        $jsonArray = array(
                            "campaign_id" => $campaignId,
                            "lead_id" => $leadId,
                            "cost" =>  $priceArray[$leadId],
                            "balance" => 0,
                            "score" => 0
                        );
                        //dd($jsonArray);
                        $leadDeliverApi = new LeadDeliveryApi();
                        $leadDeliverApi->leadDeliveryTemplate(json_encode($jsonArray));
                    }
                }

                //Added by BK on 05/02/2025 convenience fee email to evey success payment
                $transactionId = (isset($jsonResponse['id'])) ? $jsonResponse['id'] : '';
                $businessPaymentObj->moversRechargeEmailNotificationTemplate($businessId, $businessDetail->business_name, $businessDetail->business_name, $businessDetail->email, $cardDetail->card, $charge, $convenienceFee, $transactionId);
            } else {
                //dd($jsonResponse['response']['message']);
                if (isset($jsonResponse['message'])) {
                    throw new Exception($jsonResponse['message']);
                }
            }
            $status = 1;
            $message = 'Success';
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => array('code' => "System Error", 'message' => $message)
        ];
        return response()->json($responseData);
    }

    public function getMarketplaceLeadPrice($dateTimeReceived , $isVerified) {
        $price = 0;
        $currentdateTime = date('Y-m-d H:i:s');
        $timestamp2 = strtotime($currentdateTime);
        $timestamp1 = strtotime($dateTimeReceived);
        $minutedifference = abs($timestamp2 - $timestamp1) / 60; //for minute difference
        $hourdifference = abs($timestamp2 - $timestamp1) / (60 * 60); //for hour difference
        //echo  $difference; die;
        if ($isVerified == 'yes') {
            if ($minutedifference <= 30) {
                $price = 18;
            } else if ($minutedifference > 30 && $hourdifference <= 2) {
                $price = 16;
            } else if ($hourdifference > 2 && $hourdifference <= 4) {
                $price = 14;
            } else if ($hourdifference > 4 && $hourdifference <= 12) {
                $price = 12;
            } else if ($hourdifference > 12 && $hourdifference <= 24) {
                $price = 10;
            } else if ($hourdifference > 24) {
                $price = 8;
            }
        } else {
            if ($minutedifference <= 30) {
                $price = 9;
            } else if ($minutedifference > 03 && $hourdifference <= 2) {
                $price = 7;
            } else if ($hourdifference > 2) {
                $price = 5;
            }
        }
        return $price;
    }

    public function moverPaymentHistory(Request $request) {
        $status = $businessId = $allCredit = $allDebit = 0;
        $message = $businessName = '';
        $finalArray = $campaignId = [];
        try {
            $elements = json_decode(file_get_contents('php://input'), true);
            if (empty($elements['businessId'])) {
                throw new Exception("businessId required");
            }

            $businessId                     = $elements['businessId'];
            $businessDetail                 = Business::where('business_id', $businessId)->first();
            $businessName                   = $businessDetail->display_name;
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            } else {
                $lastMonth                  = '2024-12-31';
                for ($i = 0; $i <= 5; $i++) {
                    $currentMonth           = date("Y-m-01");
                    $startMonth             = date("Y-m-d", strtotime( date( 'Y-m-01' )." -$i months"));
                    if (strtotime($startMonth) > strtotime($lastMonth) && strtotime($startMonth) < strtotime($currentMonth)) {
                        $monthTitle         = date("M Y", strtotime( date( 'Y-m-01' )." -$i months"));
                        $startDate          = date("Y-m-d", strtotime( date( 'Y-m-01' )." -$i months")) . ' 00:00:00';
                        $endDate            = date("Y-m-t", strtotime($startDate)) . ' 23:59:59';
                        $campaignDetail     = Campaign::where('business_id', $businessId)->get(['campaign_id']);

                        foreach ($campaignDetail as $campaign) {
                            $campaignId[]   = $campaign->campaign_id;
                        }
                        if (@count($campaignId) > 0) {
                            $campaignIdStr  =  " OR campaign_id IN (".implode(',', $campaignId).")";
                        }

                        $paymentDetail      = DB::select("SELECT
                    SUM(CASE
                        WHEN charge_type_id  = 1 THEN amount_charge
                    END) AS 'CCCr',
                    SUM(CASE
                        WHEN charge_type_id  = 7 THEN amount_charge
                    END) AS 'RecurrentCr',
                    SUM(CASE
                        WHEN charge_type_id  = 8 THEN amount_charge
                    END) AS 'BadLeadCr',
                    SUM(CASE
                        WHEN charge_type_id  = 9 THEN amount_charge
                    END) AS 'OtherCr',
                    SUM(CASE
                        WHEN charge_type_id  = 10 THEN amount_charge
                    END) AS 'TransferToCr',
                    SUM(CASE
                        WHEN charge_type_id  = 3 THEN amount_charge
                    END) AS 'OtherDb',
                    SUM(CASE
                        WHEN charge_type_id  = 4 THEN amount_charge
                    END) AS 'RefundDb',
                    SUM(CASE
                        WHEN charge_type_id  = 5 THEN amount_charge
                    END) AS 'ChargeBackDb',
                    SUM(CASE
                        WHEN charge_type_id  = 6 THEN amount_charge
                    END) AS 'TransferFromDb',
                    SUM(CASE
                        WHEN charge_type_id  = 2 THEN amount_charge
                    END) AS 'BankCr',
                    SUM(CASE
                        WHEN charge_type_id  = 11 THEN amount_charge
                    END) AS 'VoidPaymentCr'
                    FROM business_campaign_payment where (business_id=".$businessId." $campaignIdStr) AND is_charge_approved = 'yes' and created_at between '" . $startDate . "' and '" . $endDate . "'");
                        foreach ($paymentDetail as $payment) {
                            $allCredit      = $payment->CCCr + $payment->RecurrentCr + $payment->BadLeadCr + $payment->OtherCr + $payment->TransferToCr + $payment->BankCr;
                            $allDebit       = $payment->OtherDb + $payment->RefundDb + $payment->ChargeBackDb + $payment->TransferFromDb + $payment->VoidPaymentCr;
                        }

                        $totalLead          = DB::select("SELECT SUM(lr.payout) as totalleadpayout FROM lead_routing AS lr WHERE lr.route_status = 'sold' AND lr.payout > 0 AND (lr.created_at BETWEEN '" . $startDate . "' and '" . $endDate . "' ) AND lr.campaign_id IN (SELECT campaign_id FROM campaign WHERE business_id=".$businessId." AND lead_type_id = 1)");
                        $totalOutBound      = DB::select("SELECT SUM(ltc.payout) as totaloutboundpayout FROM lead_transfer_call AS ltc JOIN `lead` AS l ON l.lead_id = ltc.lead_id WHERE ltc.calls_type = 'outbound' AND ltc.transfer_type = 'transfer' AND (ltc.created_at BETWEEN '" . $startDate . "' AND '" . $endDate . "') AND ltc.campaign_id IN (".implode(',', $campaignId).")");
                        $totalInBound       = DB::select("SELECT SUM(ltc.payout) as totalinboundpayout FROM lead_transfer_call AS ltc JOIN `lead` AS l ON l.lead_id = ltc.lead_id WHERE ltc.calls_type = 'inbound' AND ltc.transfer_type = 'transfer' AND (ltc.created_at BETWEEN '" . $startDate . "' AND '" . $endDate . "') AND ltc.campaign_id IN (".implode(',', $campaignId).")");

                        $allDebit          += $totalLead[0]->totalleadpayout;
                        $allDebit          += $totalOutBound[0]->totaloutboundpayout;
                        $allDebit          += $totalInBound[0]->totalinboundpayout;

                        $opeingBlanace      = BusinessesCampaignEomBalance::where('date', $startDate)->where('business_id', $businessId)->sum('balance');
                        if ($i == 0) {
                            $businessBlanace= Business::where('business_id', $businessId)->sum('credit_available');
                            $campaignBlanace= Campaign::where('business_id', $businessId)->sum('credit_available');
                            $endingBlanace  = $businessBlanace + $campaignBlanace;
                        } else {
                            $endingBlanace  = BusinessesCampaignEomBalance::where('date', $endDate)->where('business_id', $businessId)->sum('balance');
                        }

                        // moving lead count & revenue
                        $finalArray[$monthTitle] = [
                            "totalCredit"   => ($allCredit > 0) ? $allCredit : 0,
                            "totalDebit"    => ($allDebit > 0) ? $allDebit : 0,
                            "opeingBlanace" => ($opeingBlanace > 0) ? $opeingBlanace : '--',
                            "endingBlanace" => ($endingBlanace > 0) ? $endingBlanace : '--',
                        ];
                    }
                }

                $status = 1;
                $message = "Success";
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'businessId' => $businessId,
            'businessName' => $businessName,
            'data' => $finalArray,
        ];
        return response()->json($responseArray);
    }

    public function moverPaymentHistoryData(Request $request) {
        $status = $businessId = 0;
        $message = $businessName = $paymentCustId = '';
        $finalArray = $businessIdArray = $campaignIdArray = $businessArray = $campaignArray = $userIdArray = $userNameArray = $cardIdArray = $cardArray = $chargeTypeArray = $categoryArray = [];
        try {
            $businessId     = $request['businessId'];
            $chargeType     = $request['chargeType'];
            $startDate      = $request['startDate'];
            $endDate        = $request['endDate'];
            if (empty($businessId)) {
                throw new Exception("businessId Required");
            }
            if (empty($request['startDate'])) {
                throw new Exception("startDate required");
            }
            if (empty($request['endDate'])) {
                throw new Exception("endDate required");
            }

            // get Businesses Login Details
            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                throw new Exception('Invalid email or password');
            }
            $campaignDetail = Campaign::where('business_id', $businessId)->get(['campaign_id']);
            foreach ($campaignDetail as $campaign) {
                $campaignIdArray[]   = $campaign->campaign_id;
            }

            if ($chargeType == 'alldata') {
                $paymentDetail  = BusinessCampaignPayment::where(function ($query) use ($businessId, $campaignIdArray){
                    $query->where('business_id', $businessId)->orWhereIn('campaign_id', $campaignIdArray);
                })->where('created_at', '>=', $startDate . ' 00:00:00')->where('created_at', '<=', $endDate . ' 23:59:59')->orderBy('business_campaign_payment_id', 'DESC')->get();
            } else {
                $paymentDetail  = BusinessCampaignPayment::where(function ($query) use ($businessId, $campaignIdArray){
                    $query->where('business_id', $businessId)->orWhereIn('campaign_id', $campaignIdArray);
                })->where('created_at', '>=', $startDate . ' 00:00:00')->where('created_at', '<=', $endDate . ' 23:59:59')->where('charge_type_id',$chargeType)->orderBy('business_campaign_payment_id', 'DESC')->get();
            }

            foreach ($paymentDetail as $payment) {
                if ($payment->business_id > 0 && !in_array($payment->business_id, $businessIdArray)) {
                    $businessIdArray[] = $payment->business_id;
                }
                if ($payment->charge_user_id > 0 && !in_array($payment->charge_user_id, $businessIdArray)) {
                    $businessIdArray[] = $payment->charge_user_id;
                }
                if ($payment->campaign_id > 0 && !in_array($payment->campaign_id, $campaignIdArray)) {
                    $campaignIdArray[] = $payment->campaign_id;
                }
                if (($payment->charge_method_type == "snapituser" || $payment->charge_method_type == "subscription") && !in_array($payment->charge_user_id, $userIdArray)) {
                    $userIdArray[] = $payment->charge_user_id;
                }
                if ($payment->business_cc_id > 0 && !in_array($payment->business_cc_id, $cardIdArray)) {
                    $cardIdArray[] = $payment->business_cc_id;
                }
            }

            $campaignDetail = Campaign::whereIn('campaign_id', $campaignIdArray)->get(['campaign_id', 'business_id', 'campaign_name', 'lead_category_id'])->toArray();
            for ($b = 0; $b < count($campaignDetail); $b++) {
                $campaignArray[$campaignDetail[$b]['campaign_id']] = $campaignDetail[$b];
                if (!in_array($campaignDetail[$b]['business_id'], $businessIdArray)) {
                    $businessIdArray[] = $campaignDetail[$b]['business_id'];
                }
            }
            //echo "<pre>";print_r($campaignArr);die;
            $businessDetail = Business::whereIn('business_id', $businessIdArray)->get(['business_id', 'business_name'])->toArray();
            for ($c = 0; $c < count($businessDetail); $c++) {
                $businessArray[$businessDetail[$c]['business_id']] = $businessDetail[$c]['business_name'];
            }
            $userDetail     = User::whereIn('id', $userIdArray)->get(['id', 'name'])->toArray();
            for ($u = 0; $u < count($userDetail); $u++) {
                $userNameArray[$userDetail[$u]['id']] = $userDetail[$u]['name'];
            }
            $cardDetail     = BusinessCC::whereIn('business_cc_id', $cardIdArray)->get(['business_cc_id', 'card'])->toArray();
            for ($g = 0; $g < count($cardDetail); $g++) {
                $cardArray[$cardDetail[$g]['business_cc_id']] = str_pad($cardDetail[$g]['card'], 4, "0", STR_PAD_LEFT);
            }

            $chargeTypeDetail= MstChargeType::get(['charge_type_id', 'charge_type']);
            for ($ct=0; $ct < count($chargeTypeDetail); $ct++) {
                $chargeTypeArray[$chargeTypeDetail[$ct]['charge_type_id']] = $chargeTypeDetail[$ct]['charge_type'];
            }

            $categoryDetail = MstLeadCategory::get(['lead_category_id', 'lead_category_name']);
            for ($ct=0; $ct < count($categoryDetail); $ct++) {
                $categoryArray[$categoryDetail[$ct]['lead_category_id']] = $categoryDetail[$ct]['lead_category_name'];
            }

            foreach ($paymentDetail as $payment) {
                $data = [];
                $businessIdCheck = $payment->business_id;
                $data['id'] = $payment->business_campaign_payment_id;
                $data['campaignId'] = '--';
                $data['campaignName'] = '--';
                $categoryName = '--';
                $category = "0";
                if ($payment->campaign_id > 0) {
                    $data['campaignId'] = $payment->campaign_id;
                    if (isset($campaignArray[$payment->campaign_id])) {
                        $data['campaignName'] = $campaignArray[$payment->campaign_id]['campaign_name'];
                        $categoryId = $campaignArray[$payment->campaign_id]['lead_category_id'];
                        //echo $category."==".$payment->campaign_id."<br>";
                        $categoryName = $categoryArray[$categoryId];
                    }
                }
                $data['campaignCategory'] = $categoryName;
                $data['businessName'] = '';
                if ($businessIdCheck > 0) {
                    $data['businessName'] = $businessArray[$businessIdCheck];
                }
                $data['beforeCharge'] = ($payment->balance_before_charge == 0) ? '--' : '$' . sprintf('%0.2f', $payment->balance_before_charge);
                $data['amountCharge'] = ($payment->amount_charge == 0) ? '--' : '$' . sprintf('%0.2f', $payment->amount_charge);
                $data['afterCharge'] = ($payment->balance_after_charge == 0) ? '--' : '$' . sprintf('%0.2f', $payment->balance_after_charge);
                $data['totalCharge'] = (empty($payment->convenience_fee) || $payment->convenience_fee == 0) ? '--' : '$' . sprintf('%0.2f', $payment->amount_charge + $payment->convenience_fee);
                $data['chargeType'] = ($payment->charge_type_id > 0) ? $chargeTypeArray[$payment->charge_type_id] : "--";
                $data['chargeMethod'] = "--";
                if ($payment->charge_method_type == "snapituser") {
                    $chargeMethod = "";
                    if ($payment->charge_method == "recurrent") {
                        $chargeMethod = " (Recurrent)";
                    }
                    if (isset($userNameArray[$payment->charge_user_id])) {
                        $data['chargeMethod'] = $userNameArray[$payment->charge_user_id] . $chargeMethod;
                    }
                } else if ($payment->charge_method_type == "subscription") {
                    $chargeMethod = " (Subscription)";
                    if ($payment->charge_method == "recurrent") {
                        $chargeMethod = " (Subscription Recurrent)";
                    }
                    if (isset($userNameArray[$payment->charge_user_id])) {
                        $data['chargeMethod'] = $userNameArray[$payment->charge_user_id] . $chargeMethod;
                    }
                } else {
                    if (isset($businessArray[$payment->charge_user_id])) {
                        $chargeMethodType = "";
                        if ($payment->charge_method_type == "moverinterfaceuser") {
                            $chargeMethodType = " (Dashboard)";
                        } else if ($payment->charge_method_type == "moveremailsms") {
                            $chargeMethodType = " (Email/SMS)";
                        } else if ($payment->charge_method_type == "contract") {
                            $chargeMethodType = " (Contract)";
                        } else if ($payment->charge_method_type == "cardcontract") {
                            $chargeMethodType = " (Card Contract)";
                        }else if ($payment->charge_method_type == "lowfund") {
                            $chargeMethodType = " (Lowfund Email/SMS)";
                        }
                        $data['chargeMethod'] = $businessArray[$payment->charge_user_id] . $chargeMethodType;
                    }
                }
                $data['date'] = date("m/d H:i", strtotime($payment->created_at));
                $data['transactionId'] = empty($payment->fatt_payment_id) ? '--' : $payment->fatt_payment_id;
                $data['chargeTypeId'] = $payment->charge_type_id;
                $data['status'] = ($payment->is_charge_approved == "yes") ? 'Approved' : 'Declined';

                $data['cardNumber'] = '';
                if (isset($cardArray[$payment->business_cc_id])) {
                    $data['cardNumber'] = 'XXXX-XXXX-XXXX-' . $cardArray[$payment->business_cc_id];
                }
                $finalArray[] = $data;
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'data' => $finalArray
        ];

        return response()->json($responseArray);
    }

    public function moverPaymentHistoryPdf(Request $request) {
        $status = $businessId = $allCredit = $allDebit = 0;
        $message = $businessName = $businessEmail = $businessPhone = '';
        $finalArray = $leadArray = $creditArray = $debitArray = $businessIdArray = $campaignIdArray = $businessArray = $campaignArray = $userIdArray = $userNameArray = $cardIdArray = $cardArray = $chargeTypeArray = $categoryArray = [];
        try {
            $currentMonth           = date('m');
            $businessId             = $request['businessId'];
            $startDate              = $request['startDate'];
            $endDate                = $request['endDate'];
            if (empty($businessId)) {
                throw new Exception("businessId Required");
            }
            if (empty($request['startDate'])) {
                throw new Exception("startDate required");
            }
            if (empty($request['endDate'])) {
                throw new Exception("endDate required");
            }

            // get Businesses Login Details
            $businessDetail         = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                throw new Exception('Invalid email or password');
            }
            $businessName           = $businessDetail->business_name;
            $businessEmail          = $businessDetail->email;
            $businessPhone          = $businessDetail->business_phone;
            $campaignDetail         = Campaign::where('business_id', $businessId)->get(['campaign_id']);
            foreach ($campaignDetail as $campaign) {
                $campaignIdArray[]  = $campaign->campaign_id;
            }

            if (@count($campaignIdArray) > 0) {
                $campaignIdStr      =  " OR campaign_id IN (".implode(',', $campaignIdArray).")";
            }

            $paymentDetail          = DB::select("SELECT * from business_campaign_payment where (business_id=".$businessId." $campaignIdStr) AND is_charge_approved = 'yes' and created_at between '" . $startDate . "' and '" . $endDate . "' AND charge_type_id IN (1, 7, 8, 9, 10, 2)");
            foreach ($paymentDetail as $payment) {
                if ($payment->business_id > 0 && !in_array($payment->business_id, $businessIdArray)) {
                    $businessIdArray[] = $payment->business_id;
                }
                if ($payment->charge_user_id > 0 && !in_array($payment->charge_user_id, $businessIdArray)) {
                    $businessIdArray[] = $payment->charge_user_id;
                }
                if ($payment->campaign_id > 0 && !in_array($payment->campaign_id, $campaignIdArray)) {
                    $campaignIdArray[] = $payment->campaign_id;
                }
                if ($payment->charge_method_type == "snapituser" && !in_array($payment->charge_user_id, $userIdArray)) {
                    $userIdArray[] = $payment->charge_user_id;
                }
                if ($payment->business_cc_id > 0 && !in_array($payment->business_cc_id, $cardIdArray)) {
                    $cardIdArray[] = $payment->business_cc_id;
                }
            }

            $campaignDetail         = Campaign::whereIn('campaign_id', $campaignIdArray)->get(['campaign_id', 'business_id', 'campaign_name', 'lead_type_id', 'lead_category_id'])->toArray();
            for ($b = 0; $b < count($campaignDetail); $b++) {
                $campaignArray[$campaignDetail[$b]['campaign_id']] = $campaignDetail[$b];
                if (!in_array($campaignDetail[$b]['business_id'], $businessIdArray)) {
                    $businessIdArray[] = $campaignDetail[$b]['business_id'];
                }
            }
            //echo "<pre>";print_r($campaignArr);die;
            $businessDetail         = Business::whereIn('business_id', $businessIdArray)->get(['business_id', 'business_name'])->toArray();
            for ($c = 0; $c < count($businessDetail); $c++) {
                $businessArray[$businessDetail[$c]['business_id']] = $businessDetail[$c]['business_name'];
            }
            $userDetail             = User::whereIn('id', $userIdArray)->get(['id', 'name'])->toArray();
            for ($u = 0; $u < count($userDetail); $u++) {
                $userNameArray[$userDetail[$u]['id']] = $userDetail[$u]['name'];
            }
            $cardDetail             = BusinessCC::whereIn('business_cc_id', $cardIdArray)->get(['business_cc_id', 'card'])->toArray();
            for ($g = 0; $g < count($cardDetail); $g++) {
                $cardArray[$cardDetail[$g]['business_cc_id']] = $cardDetail[$g]['card'];
            }

            $chargeTypeDetail       = MstChargeType::get(['charge_type_id', 'charge_type']);
            for ($ct=0; $ct < count($chargeTypeDetail); $ct++) {
                $chargeTypeArray[$chargeTypeDetail[$ct]['charge_type_id']] = $chargeTypeDetail[$ct]['charge_type'];
            }

            $categoryDetail         = MstLeadCategory::get(['lead_category_id', 'lead_category_name']);
            for ($ct=0; $ct < count($categoryDetail); $ct++) {
                $categoryArray[$categoryDetail[$ct]['lead_category_id']] = $categoryDetail[$ct]['lead_category_name'];
            }

            foreach ($paymentDetail as $payment) {
                $data = [];
                $data['id'] = $payment->business_campaign_payment_id;
                $businessIdCheck = $payment->business_id;
                $data['businessId'] = ($payment->business_id == "") ? '--' : $payment->business_id;
                $data['campaignId'] = '--';
                $data['campaignName'] = '--';
                if ($payment->campaign_id > 0) {
                    $data['campaignId'] = $payment->campaign_id;
                    if (isset($campaignArray[$payment->campaign_id])) {
                        $data['campaignName'] = $campaignArray[$payment->campaign_id]['campaign_name'];
                        $businessIdCheck = $campaignArray[$payment->campaign_id]['business_id'];
                    }
                }
                $data['businessName'] = '';
                if ($businessIdCheck > 0) {
                    $data['businessName'] = $businessArray[$businessIdCheck];
                }

                $data['beforeCharge'] = ($payment->balance_before_charge == 0) ? '--' : $payment->balance_before_charge;
                $data['amountCharge'] = ($payment->amount_charge == 0) ? '--' : $payment->amount_charge;
                $data['afterCharge'] = ($payment->balance_after_charge == 0) ? '--' : $payment->balance_after_charge;
                $data['chargeType'] = ($payment->charge_type_id > 0) ? $chargeTypeArray[$payment->charge_type_id] : "--";
                $data['chargeMethod'] = "--";
                if ($payment->charge_method_type == "snapituser") {
                    if (isset($userNameArray[$payment->charge_user_id])) {
                        $data['chargeMethod'] = $userNameArray[$payment->charge_user_id];
                    }
                } else {
                    if (isset($businessArray[$payment->charge_user_id])) {
                        $chargeMethodType = "";
                        if ($payment->charge_method_type == "moverinterfaceuser") {
                            $chargeMethodType = "Dashboard";
                        } else if ($payment->charge_method_type == "moveremailsms") {
                            $chargeMethodType = "Email/SMS";
                        } else if ($payment->charge_method_type == "contract") {
                            $chargeMethodType = "Contract";
                        } else if ($payment->charge_method_type == "cardcontract") {
                            $chargeMethodType = "Card Contract";
                        }
                        $data['chargeMethod'] = $chargeMethodType;
                    }
                }
                $dateTime = strtotime($payment->created_at);
                $data['date'] = date("m/d H:i", $dateTime);
                $data['transactionId'] = empty($payment->fatt_payment_id) ? '--' : $payment->fatt_payment_id;
                $data['status'] = ($payment->is_charge_approved == "yes") ? 'Approved' : 'Declined';

                $data['cardNumber'] = '';
                if (isset($cardArray[$payment->business_cc_id])) {
                    $data['cardNumber'] = 'XXXX-XXXX-XXXX-' . $cardArray[$payment->business_cc_id];
                }

                $creditArray[]      = $data;
            }

            if (count($campaignIdArray) > 0) { foreach ($campaignIdArray as $campaignId) {
                $data1 = array();
                $data1['id'] = $campaignId;
                $data1['description'] = $campaignArray[$campaignId]['campaign_name'];
                $data1['leadtype'] = $campaignArray[$campaignId]['lead_type_id'];
                $data1['totallead'] = 0;
                $data1['totalleadpayout'] = 0;
                $data1['totaloutbound'] = 0;
                $data1['totaloutboundpayout'] = 0;
                $data1['totalinbound'] = 0;
                $data1['totalinboundpayout'] = 0;
                $totalLead              = DB::select("SELECT SUM(lr.payout) as totalleadpayout, count(lr.lead_routing_id) AS totallead FROM lead_routing AS lr WHERE lr.route_status = 'sold' AND lr.payout > 0 AND (lr.created_at BETWEEN '" . $startDate . "' and '" . $endDate . "' ) AND lr.campaign_id IN (SELECT campaign_id FROM campaign WHERE lead_type_id = 1) AND campaign_id = ".$campaignId."");
                $totalOutbound          = DB::select("SELECT SUM(ltc.payout) as totaloutboundpayout, count(ltc.lead_transfer_id) AS totaloutbound FROM lead_transfer_call AS ltc JOIN `lead` AS l ON l.lead_id = ltc.lead_id WHERE ltc.calls_type = 'outbound' AND ltc.transfer_type = 'transfer' AND (ltc.created_at BETWEEN '" . $startDate . "' AND '" . $endDate . "') AND ltc.campaign_id = ".$campaignId."");
                $totalInbound           = DB::select("SELECT SUM(ltc.payout) as totalinboundpayout, count(ltc.lead_transfer_id) AS totalinbound FROM lead_transfer_call AS ltc JOIN `lead` AS l ON l.lead_id = ltc.lead_id WHERE ltc.calls_type = 'inbound' AND ltc.transfer_type = 'transfer' AND (ltc.created_at BETWEEN '" . $startDate . "' AND '" . $endDate . "') AND ltc.campaign_id = ".$campaignId."");
                if (isset($totalLead) && !empty($totalLead)) {
                    $data1['totallead'] = $totalLead[0]->totallead;
                    $data1['totalleadpayout'] = ($totalLead[0]->totalleadpayout > 0) ? $totalLead[0]->totalleadpayout : 0;
                }
                if (isset($totalOutbound) && !empty($totalOutbound)) {
                    $data1['totaloutbound'] = $totalOutbound[0]->totaloutbound;
                    $data1['totaloutboundpayout'] = ($totalOutbound[0]->totaloutboundpayout > 0) ? $totalOutbound[0]->totaloutboundpayout : 0;
                }
                if (isset($totalInbound) && !empty($totalInbound)) {
                    $data1['totalinbound'] = $totalInbound[0]->totalinbound;
                    $data1['totalinboundpayout'] = ($totalInbound[0]->totalinboundpayout > 0) ? $totalInbound[0]->totalinboundpayout : 0;
                }
                $leadArray[] = $data1;
            } }

            $payment1Detail = DB::select("SELECT SUM(amount_charge) AS amountCharge, COUNT(business_campaign_payment_id) AS quantity, business_id, campaign_id, charge_type_id FROM business_campaign_payment WHERE is_charge_approved = 'yes' and created_at BETWEEN '" . $startDate . "' and '" . $endDate . "'  AND (business_id=".$businessId." $campaignIdStr) AND charge_type_id IN (3,4,5,6) GROUP BY charge_type_id");
            //print_r($payment1Detail); die;
            if (count($payment1Detail) > 0) {
                foreach ($payment1Detail as $payment) {
                    $data2 = array();
                    $data2['id'] = $payment->campaign_id ? $payment->campaign_id : $payment->business_id;
                    $data2['quantity'] = $payment->quantity;
                    if ($payment->campaign_id > 0) {
                        $campaignName = Campaign::where('campaign_id', $payment->campaign_id)->first()->campaign_name;
                        $data2['description'] = $campaignName;
                    } else {
                        $businessName = Business::where('business_id', $payment->business_id)->first()->business_name;
                        $data2['description'] = $businessName;
                    }
                    $data2['amountCharge'] = $payment->amountCharge;
                    if ($payment->charge_type_id == 3) {
                        $data2['unit'] = 'Other Debit';
                    } else if ($payment->charge_type_id == 4) {
                        $data2['unit'] = 'Refund';
                    } else if ($payment->charge_type_id == 5) {
                        $data2['unit'] = 'Charge';
                    } else if ($payment->charge_type_id == 6) {
                        $data2['unit'] = 'Transfer';
                    } else if ($payment->charge_type_id  == 11) {
                        $data2['unit'] = 'Void Payment';
                    }
                    $debitArray[] =  $data2;
                }
            }

            $opeingBlanace          = BusinessesCampaignEomBalance::where('date', $startDate)->where('business_id', $businessId)->sum('balance');
            if ($currentMonth == date('m', strtotime($startDate))) {
                $businessBlanace    = Business::where('business_id', $businessId)->sum('credit_available');
                $campaignBlanace    = Campaign::where('business_id', $businessId)->sum('credit_available');
                $endingBlanace      = $businessBlanace + $campaignBlanace;
            } else {
                $endingBlanace      = BusinessesCampaignEomBalance::where('date', $endDate)->where('business_id', $businessId)->sum('balance');
            }

            $finalArray             = [
                'startDate' => $startDate,
                'endDate' => $endDate,
                'businessName' => $businessName,
                'businessEmail' => $businessEmail,
                'businessPhone' => $businessPhone,
                'creditArray' => $creditArray,
                'debitArray' => $debitArray,
                'leadArray' => $leadArray,
                "opeingBlanace" => ($opeingBlanace > 0) ? '$' . $opeingBlanace : '--',
                "endingBlanace" => ($endingBlanace > 0) ? '$' . $endingBlanace : '--',
            ];

            $status = 1;
            $message = "Success";
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'data' => $finalArray
        ];
        return response()->json($responseArray);
    }

    public function moverChatHistory(Request $request)
    {
        $status = 0;
        $message = $businessName = '';
        $finalArray = $userNameArray = [];
        try {
            $businessId     = $request['businessId'];
            if (empty($businessId)) {
                throw new Exception("businessId Required");
            }

            // get Businesses Login Details
            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                throw new Exception('Invalid email or password');
            }
            $businessName   = $businessDetail->display_name;
            // Update unread message to read message
            BusinessChat::where('business_id', $businessId)
                ->where('sent_by_user_id', '>', 0)
                ->update([
                    'is_read' => 'yes'
                ]);

            $userDetail     = User::get(['id', 'name'])->toArray();
            for ($u = 0; $u < count($userDetail); $u++) {
                $userNameArray[$userDetail[$u]['id']] = $userDetail[$u]['name'];
            }

            $chatDetail     = BusinessChat::where('business_id', $businessId)->get();
            foreach($chatDetail as $chat) {
                $sentByName = '';
                $data = [];
                $data['time'] = strtotime($chat->created_at);
                $data['chatId'] = $chat->business_chat_id;
                $data['userId'] = $chat->sent_by_user_id;
                $data['message'] = $chat->text;
                if ($chat->sent_by_user_id == 0) {
                    $sentBy = $businessDetail->business_name;
                } else {
                    $sentBy = $userNameArray[$chat->sent_by_user_id];
                }
                $nameArray = explode(" ", $sentBy);
                foreach ($nameArray as $name) {
                    $sentByName .= mb_substr($name, 0, 1);
                }
                $data['sentBy'] = strtoupper($sentByName);
                $data['isRead'] = $chat->is_read;
                $data['createdDate'] = date('m/d/Y', strtotime($chat->created_at));
                $data['createdTime'] = date('h:i A', strtotime($chat->created_at));
                $finalArray[$data['createdDate']][] = $data;
            }

            $status = 1;
            $message = "Success";
        } catch(Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'businessName' => $businessName,
            'data'=> $finalArray
        ];

        return response()->json($responseArray);
    }

    public function moverMessageLinkUpSalesRep (Request $request)
    {
        $status = 0;
        $message = '';
        $finalArray = $userNameArray = [];
        try {
            $businessId     = $request['businessId'];
            $chatMessage    = $request['message'];
            if (empty($businessId)) {
                throw new Exception("businessId Required");
            }

            // get Businesses Login Details
            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                throw new Exception('Invalid email or password');
            }

            BusinessChat::create([
                'text' => $chatMessage,
                'sent_by_user_id' => 0,
                'business_id' => $businessId,
                'is_read' => 'no',
                'created_at' => date('Y-m-d H:i:s'),
            ]);

            $userDetail     = User::get(['id', 'name'])->toArray();
            for ($u = 0; $u < count($userDetail); $u++) {
                $userNameArray[$userDetail[$u]['id']] = $userDetail[$u]['name'];
            }

            $chatDetail     = BusinessChat::where('business_id', $businessId)->get();
            foreach($chatDetail as $chat) {
                $sentByName = '';
                $data = [];
                $data['time'] = strtotime($chat->created_at);
                $data['chatId'] = $chat->business_chat_id;
                $data['userId'] = $chat->sent_by_user_id;
                $data['message'] = $chat->text;
                if ($chat->sent_by_user_id == 0) {
                    $sentBy = $businessDetail->business_name;
                } else {
                    $sentBy = $userNameArray[$chat->sent_by_user_id];
                }
                $nameArray = explode(" ", $sentBy);
                foreach ($nameArray as $name) {
                    $sentByName .= mb_substr($name, 0, 1);
                }
                $data['sentBy'] = strtoupper($sentByName);
                $data['isRead'] = $chat->is_read;
                $data['createdDate'] = date('m/d/Y', strtotime($chat->created_at));
                $data['createdTime'] = date('h:i A', strtotime($chat->created_at));
                $finalArray[$data['createdDate']][] = $data;
            }

            $status = 1;
            $message = "Success";
        } catch(Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'data'=> $finalArray
        ];

        return response()->json($responseArray);
    }

    public function moverSetting (Request $request)
    {
        $status = 0;
        $message = $businessName = '';
        $finalArray = [];
        try {
            $elements       = json_decode(file_get_contents('php://input'), true);
            $businessId     = $elements['businessId'];
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            }
            $businessName   = $businessDetail->display_name;
            $finalArray[]   = [
                'is_low_notification' => $businessDetail->is_low_fund_notification,
                'low_fund_email' => $businessDetail->low_fund_notification_email,
                'low_fund_phone' => $businessDetail->low_fund_notification_phone,
                'low_fund_amount' => $businessDetail->low_fund_amount,
                'is_dnc_notification' => $businessDetail->is_dnc_notification,
            ];

            $status         = 1;
            $message        = 'success';
        } catch (Exception $e) {
            $message        = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'businessName' => $businessName,
            'data' => $finalArray,
        ];
        return response()->json($responseArray);
    }

    public function moverSettingUpdate (Request $request)
    {
        $status = 0;
        $message = '';
        $finalArray = [];

        $rules = [
            'lowFundStatus' => 'required|in:yes,no',
            'lowFundEmail' => 'required|email||max:50|regex:/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix',
            'lowFundPhone' => 'required|min:10|regex:/^([0-9\s\-\+\(\)]*)$/',
            'lowFundAmount' => 'required|numeric',
            'dncStatus' => 'required|in:yes,no',
        ];
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(array(
                'success' => 0,
                'message' => 'There are incorect values in the form!',
                'error' => $validator->getMessageBag()->toArray()
            ), 200);
            $this->throwValidationException(
                $request, $validator
            );
        }

        try {
            $businessId     = $request['businessId'];
            $lowFundAmount  = $request['lowFundAmount'];
            $lowFundEmail   = $request['lowFundEmail'];
            $lowFundPhone   = $request['lowFundPhone'];
            $lowFundStatus  = $request['lowFundStatus'];
            $dncStatus  = $request['dncStatus'];
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            }

            Business::where('business_id', $businessId)->update([
                'low_fund_amount' => $lowFundAmount,
                'low_fund_notification_email' => $lowFundEmail,
                'low_fund_notification_phone' => $lowFundPhone,
                'is_low_fund_notification' => $lowFundStatus,
                'is_dnc_notification' => $dncStatus
            ]);

            $status         = 1;
            $message        = 'success';
        } catch (Exception $e) {
            $message        = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'error' => "",
        ];
        return response()->json($responseArray);
    }

    public function moverOrganicCampaign (Request $request)
    {
        $status = 0;
        $message = '';
        $finalArray = [];
        try {
            $businessId     = $request['businessId'];
            if (empty($businessId)) {
                throw new Exception("businessId Required");
            }

            // get Businesses Login Details
            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                throw new Exception('Invalid email or password');
            }
            $businessName   = $businessDetail->display_name;
            $campaignId     = Campaign::where(['business_id'=>$businessId,'campaign_type_id'=>4])->pluck('campaign_id')->first();
            $organicDetail  = CampaignOrganic::where(['campaign_id'=>$campaignId])->first();
            //get all state records from mst_zipcode table
            $stateDetails   = MstZipcode::distinct()->get(['state']);
            $finalArray[]   = [
                'campaign_id' => (isset($organicDetail)) ? $organicDetail->campaign_id : "",
                'business_name' => (isset($organicDetail)) ? $organicDetail->business_name : "",
                'business_address' => (isset($organicDetail)) ? $organicDetail->business_address : "",
                'total_business_year' => (isset($organicDetail)) ? $organicDetail->total_business_year : "",
                'services_offer' => (isset($organicDetail)) ? $organicDetail->services_offer : "",
                'serve_area' => (isset($organicDetail)) ? explode(",", $organicDetail->serve_area) : "",
                'licence' => (isset($organicDetail)) ? $organicDetail->licence : "",
                'review' => (isset($organicDetail)) ? $organicDetail->review : "",
                'is_nationwide_availability' => (isset($organicDetail)) ? $organicDetail->is_nationwide_availability : "",
                'is_storage_availability' => (isset($organicDetail)) ? $organicDetail->is_storage_availability : "",
                'company_type' => (isset($organicDetail)) ? $organicDetail->company_type : "",
                'is_heavy_equipment' => (isset($organicDetail)) ? $organicDetail->is_heavy_equipment : "",
                'is_car_transportation' => (isset($organicDetail)) ? $organicDetail->is_car_transportation : "",
                'is_moving_container' => (isset($organicDetail)) ? $organicDetail->is_moving_container : "",
                'language_support' => (isset($organicDetail)) ? $organicDetail->language_support : "",
                'no_of_years' => (isset($organicDetail)) ? $organicDetail->no_of_years : "",
                'cancellation_policy' => (isset($organicDetail)) ? $organicDetail->cancellation_policy : "",
                'price_range' => (isset($organicDetail)) ? $organicDetail->price_range : "",
                'business_url' => (isset($organicDetail)) ? $organicDetail->business_url : "",
                'mc_number' => (isset($organicDetail)) ? $organicDetail->mc_number : "",
                'business_about' => (isset($organicDetail)) ? $organicDetail->business_about : "",
                'business_highlights' => (isset($organicDetail)) ? $organicDetail->business_highlights : "",
                'stateDetails' => $stateDetails,
            ];

            $status         = 1;
            $message        = 'success';
        } catch (Exception $e) {
            $message        = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'businessName' => $businessName,
            'data' => $finalArray,
        ];
        return response()->json($responseArray);
    }

    public function moverCampaignUpdate (Request $request)
    {
        $status = 0;
        $message = '';
        $finalArray = $logArray = [];
        $rules = [
            'businessName' => 'required',
            'businessAddress' => 'required',
            'totalYear' => 'required|numeric',
            'licence' => 'required',
            'servicesOffer' => 'required',
            'serveArea' => 'required',
            'nationwide_availability' => 'required|in:yes,no',
            'storage_availability' => 'required|in:yes,no',
            'organic_company_type' => 'required',
            'organic_heavy_equipment' => 'required|in:yes,no',
            'organic_car_transportation' => 'required|in:yes,no',
            'organic_moving_container' => 'required|in:yes,no',
            'organic_language_support' => 'required',
            'organic_no_of_years' => 'required|numeric',
            /*'organic_cancellation_policy' => 'required',*/
            'organic_price_range' => 'required',
            /*'organic_business_url' => 'required',*/
            'organic_mc_number' => 'required',
            /*'business_about' => 'required',
            'business_highlights' => 'required'*/
        ];
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return response()->json(array(
                'status' => 0,
                'message' => 'There are incorect values in the form!',
                'error' => $validator->getMessageBag()->toArray()
            ), 200);
            $this->throwValidationException(
                $request, $validator
            );
        }

        try {

            $businessId     = $request['businessId'];
            $campaignId     = $request['campaignId'];
            $ip             = $request['ip'] ?? '';
            $organicData    = $request->all();
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            }

            $campaignObj    = new CampaignController();
            $vmOrganicData  = $campaignObj->insertUpdateVmOrganicData($campaignId, $organicData, $businessDetail->created_user_id, $ip);
            foreach ($vmOrganicData as $vmOrganicLog) {
                $logArray[] = $vmOrganicLog;
            }
            if (count($logArray) > 0) {
                CampaignUpdateLog::insert($logArray);
            }

            $status         = 1;
            $message        = 'success';
        } catch (Exception $e) {
            $message        = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'error' => "",
        ];
        return response()->json($responseArray);
    }

    public function moverCampaignUpdateNew (Request $request)
    {
        $status = 0;
        $message = '';
        $finalArray = $logArray = [];
        try {

            $businessId     = $request['businessId'];
            $campaignId     = $request['campaignId'];
            $payout         = $request['payout'];
            $leadType       = $request['type'];
            $ip             = $request['ip'] ?? '';
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            if (empty($campaignId)) {
                throw new Exception("campaignId required");
            }
            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            }
            $callPayout     = CampaignCallPayout::where('campaign_id', $campaignId)->first();

            if ($leadType > 1) {
                CampaignCallPayout::where('campaign_id', $campaignId)->update([
                    'automated_inbound' => $payout,
                    'inbound_call' => $payout,
                    'automated_outbound' => $payout,
                    'outbound_call' => $payout
                ]);

                if (isset($callPayout)) {
                    if ( $payout != $callPayout->automated_inbound && $payout > 0 ) {
                        $logArray[] = [
                            'created_user_id' => 77,
                            'campaign_id' => $campaignId,
                            'field_name' => 'automated_inbound',
                            'old_value' => $callPayout->automated_inbound,
                            'new_value' => $payout,
                            'created_at' => date('Y-m-d H:i:s'),
                            'ip' => $ip
                        ];
                    }

                    if ( $payout != $callPayout->inbound_call && $payout > 0 ) {
                        $logArray[] = [
                            'created_user_id' => 77,
                            'campaign_id' => $campaignId,
                            'field_name' => 'inbound_call',
                            'old_value' => $callPayout->inbound_call,
                            'new_value' => $payout,
                            'created_at' => date('Y-m-d H:i:s'),
                            'ip' => $ip
                        ];
                    }

                    if ( $payout != $callPayout->automated_outbound && $payout > 0 ) {
                        $logArray[] = [
                            'created_user_id' => 77,
                            'campaign_id' => $campaignId,
                            'field_name' => 'automated_outbound',
                            'old_value' => $callPayout->automated_outbound,
                            'new_value' => $payout,
                            'created_at' => date('Y-m-d H:i:s'),
                            'ip' => $ip
                        ];
                    }

                    if ( $payout != $callPayout->outbound_call && $payout > 0 ) {
                        $logArray[] = [
                            'created_user_id' => 77,
                            'campaign_id' => $campaignId,
                            'field_name' => 'outbound_call',
                            'old_value' => $callPayout->outbound_call,
                            'new_value' => $payout,
                            'created_at' => date('Y-m-d H:i:s'),
                            'ip' => $ip
                        ];
                    }
                }
            } else {
                CampaignCallPayout::where('campaign_id', $campaignId)->update([
                    'automated_outbound' => $payout
                ]);

                if (isset($callPayout)) {
                    if ( $payout != $callPayout->automated_outbound && $payout > 0 ) {
                        $logArray[] = [
                            'created_user_id' => 77,
                            'campaign_id' => $campaignId,
                            'field_name' => 'payout',
                            'old_value' => $callPayout->automated_outbound,
                            'new_value' => $payout,
                            'created_at' => date('Y-m-d H:i:s'),
                            'ip' => $ip
                        ];
                    }
                }

                if (count($logArray) > 0) {
                    CampaignUpdateLog::insert($logArray);
                }
            }

            $status         = 1;
            $message        = 'success';
        } catch (Exception $e) {
            $message        = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'error' => "",
        ];
        return response()->json($responseArray);
    }

    public function moverRecurrent (Request $request)
    {
        $status = $campaignId = 0;
        $message = $businessCampaignName = '';
        $recurringArray = $recurringDailyArray = [];
        try {

            $businessId     = $request['businessId'];
            $campaignId     = $request['campaignId'];
            $level          = $request->get('level');
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            if ($level == 2 && empty($campaignId)) {
                throw new Exception("campaignId required");
            }
            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            }

            if ($level == 2) {
                $campaignDetail = Campaign::where('campaign_id', $campaignId)->get(['campaign_id', 'business_id', 'campaign_name']);
                $recuringPaymentDetail = BusinessCampaignRecuringPayment::where('campaign_id', $campaignDetail[0]->campaign_id)->first();
            } else {
                $recuringPaymentDetail = BusinessCampaignRecuringPayment::where('business_id', $businessId)->first();
            }

            if($recuringPaymentDetail) {
                $recurringArray['recurring_payment_id'] = $recuringPaymentDetail->business_campaign_recurring_payment_id;
                $recurringArray['card'] = $recuringPaymentDetail->business_cc_id;
                $recurringArray['status'] = ($recuringPaymentDetail->is_active == "yes") ? 1 : 0;
                $recurringArray['type'] = ($recuringPaymentDetail->recurring_type == "daily") ? 1 : 2; //daily, amount based
                $recurringArray['amount'] = $recuringPaymentDetail->amount;
                $recurringArray['threshold'] = $recuringPaymentDetail->threshold_amount ?? 0;
                $recuringPaymentDailyDetail = BusinessCampaignRecurringPaymentDay::where('business_campaign_recurring_payment_id', $recuringPaymentDetail->business_campaign_recurring_payment_id)->get();
                if ($recuringPaymentDailyDetail) { foreach($recuringPaymentDailyDetail as $recuringPaymentDaily) {
                    $data = [];
                    $data['day'] = $recuringPaymentDaily->day;
                    $data['time'] = $recuringPaymentDaily->payment_time;
                    $recurringDailyArray[] = $data;
                } }
            }

            $status         = 1;
            $message        = 'success';
        } catch (Exception $e) {
            $message        = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'recurringArray' => $recurringArray,
            'recurringDailyArray' => $recurringDailyArray,
            'error' => "",
        ];
        return response()->json($responseArray);
    }

    public function moverRecurrentAdd (Request $request)
    {
        $status = $campaignId = 0;
        $message = $businessCampaignName = '';
        $finalArray = $logArr = $daysArr = [];
        try {

            $businessId     = $request['businessId'];
            $campaignId     = $request['campaignId'];
            $level          = $request->get('level');
            $charge         = $request->get('charge');
            $lastFour       = $request->get('lastFour');
            $status         = $request->get('status');
            $type           = $request->get('type');
            $time           = $request->get('time');
            $day            = $request->get('day');
            $threshold      = $request->get('threshold');
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            if ($level == 2 && empty($campaignId)) {
                throw new Exception("campaignId required");
            }
            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            }
            $cardNumber     = BusinessCC::where('business_cc_id', $lastFour)->first();
            $lastFourText   = $cardNumber->card ?? '';
            $userId         = 6;

            if ($level == 2) {
                $campaignDetail = Campaign::where('campaign_id', $campaignId)->get(['campaign_id', 'business_id', 'campaign_name']);
                $businessId = 0;
                $campaignId = $campaignDetail[0]->campaign_id;
                $businessCampaignName = $campaignDetail[0]->campaign_name;
            } else {
                $businessCampaignName = $businessDetail->display_name;
            }

            //store data in business_campaign_recuring_payment table
            $recurringPaymentId = BusinessCampaignRecuringPayment::create([
                'business_id' => $businessId,
                'campaign_id' => $campaignId,
                'business_cc_id' => $lastFour,
                'recurring_type' => ($type == 1 || $type == "1") ? "daily" : "amount",
                'amount' => $charge,
                'threshold_amount' => ($type == 2) ? $threshold : 0,
                'created_user_id' => $userId,
                'is_active' => ($status > 0) ? 'yes' : 'no',
                'created_at' => date('Y-m-d H:i:s')
            ])->business_campaign_recurring_payment_id;

            $daysName = [ 1 => 'Monday', 2 => 'Tuesday', 3 => 'Wednesday', 4 => 'Thursday', 5 => 'Friday', 6 => 'Saturday', 7 => 'Sunday'];
            if ($type == 1) {
                $dailyData = [];
                foreach ($day as $value) {
                    $dailyData[] = [
                        'business_campaign_recurring_payment_id' => $recurringPaymentId,
                        'day' => $value,
                        'payment_time' => $time,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    if (array_key_exists($value,$daysName)){
                        $daysArr[] = $daysName[$value];
                    }
                }
                BusinessCampaignRecurringPaymentDay::insert($dailyData);
            }

            if (isset($lastFour) && !empty($lastFour) && $lastFour != '') {
                $logArr[] = [
                    'created_user_id' => $userId,
                    'business_id' => $businessId,
                    'campaign_id' => $campaignId,
                    'field_name' => 'recurring_card',
                    'new_value' => $lastFourText,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if ( $status != '--') {
                $logArr[] = [
                    'created_user_id' => $userId,
                    'business_id' => $businessId,
                    'campaign_id' => $campaignId,
                    'field_name' => 'recurring_status',
                    'new_value' => ($status == 1 || $status == "1") ? "active" : "inactive",
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            if ( $type != '--') {
                $logArr[] = [
                    'created_user_id' => $userId,
                    'business_id' => $businessId,
                    'campaign_id' => $campaignId,
                    'field_name' => 'recurring_type',
                    'new_value' => ($type == 1 || $type == "1") ? "daily" : "amount",
                    'created_at' => date('Y-m-d H:i:s')
                ];
                if ($type == 1) {
                    if ( $time != '--') {
                        $logArr[] = [
                            'created_user_id' => $userId,
                            'business_id' => $businessId,
                            'campaign_id' => $campaignId,
                            'field_name' => 'recurring_time',
                            'new_value' => $time,
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                    }
                    if (isset($day) && count($day) > 0) {
                        $logArr[] = [
                            'created_user_id' => $userId,
                            'business_id' => $businessId,
                            'campaign_id' => $campaignId,
                            'field_name' => 'recurring_day',
                            'new_value' => implode(",",$daysArr),
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                    }
                }else{
                    if (isset($threshold) && !empty($threshold) && $threshold != '') {
                        $logArr[] = [
                            'created_user_id' => $userId,
                            'business_id' => $businessId,
                            'campaign_id' => $campaignId,
                            'field_name' => 'recurring_threshold_amount',
                            'new_value' => $threshold,
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                    }
                }
            }
            if (isset($businessCampaignCharge) && !empty($businessCampaignCharge) && $businessCampaignCharge != '') {
                $logArr[] = [
                    'created_user_id' => $userId,
                    'business_id' => $businessId,
                    'campaign_id' => $campaignId,
                    'field_name' => 'recurring_amount',
                    'new_value' => $businessCampaignCharge,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            $finalLogData = [];
            if(count($logArr) > 0 ){
                if ($level == 2) {
                    foreach ($logArr as $logdata) {
                        unset($logdata['business_id']);
                        $finalLogData[] = $logdata;
                    }
                    CampaignUpdateLog::insert($finalLogData);
                }else{
                    foreach ($logArr as $logdata) {
                        unset($logdata['campaign_id']);
                        $finalLogData[] = $logdata;
                    }
                    BusinessUpdateLog::insert($finalLogData);
                }
            }

            //send email to movers during add new card
            $businessPaymentControllerObj = new BusinessPaymentController();
            if ($type == 2 && $status > 0) {
                $businessPaymentControllerObj->amountBasedRecurrentEmailNotificationTemplate($businessId, $businessDetail->display_name, $businessCampaignName, $businessDetail->email, $cardNumber->card, $threshold);
            } else if ($type == 1 && $status > 0) {
                $businessPaymentControllerObj->dailyBasedRecurrentEmailNotificationTemplate($businessId, $businessDetail->display_name, $businessCampaignName, $businessDetail->email, $cardNumber->card, $daysArr);
            } else {
                $businessPaymentControllerObj->recurrentStopEmailNotificationTemplate($businessId, $businessDetail->display_name, $businessCampaignName, $businessDetail->email);
            }

            $status         = 1;
            $message        = 'success';
        } catch (Exception $e) {
            $message        = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'error' => "",
        ];
        return response()->json($responseArray);
    }

    public function moverRecurrentUpdate (Request $request)
    {
        $status = $campaignId = 0;
        $message = $businessCampaignName = '';
        $finalArray = $logArr = $daysArr = $olddaysArr = [];
        try {

            $recurringId    = $request->get('recurringPaymentId');
            $businessId     = $request['businessId'];
            $campaignId     = $request['campaignId'];
            $level          = $request->get('level');
            $charge         = $request->get('charge');
            $lastFour       = $request->get('lastFour');
            $status         = $request->get('status');
            $type           = $request->get('type');
            $time           = $request->get('time');
            $day            = $request->get('day');
            $threshold      = $request->get('threshold');
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            if ($level == 2 && empty($campaignId)) {
                throw new Exception("campaignId required");
            }
            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            }
            $cardNumber     = BusinessCC::where('business_cc_id', $lastFour)->first();
            $lastFourText   = $cardNumber->card ?? '';
            $userId         = 6;

            if ($level == 2) {
                $campaignDetail = Campaign::where('campaign_id', $campaignId)->get(['campaign_id', 'business_id', 'campaign_name']);
                $businessId = 0;
                $campaignId = $campaignDetail[0]->campaign_id;
                $businessCampaignName = $campaignDetail[0]->campaign_name;
            } else {
                $businessCampaignName = $businessDetail->display_name;
            }

            $recuringDetail = BusinessCampaignRecuringPayment::with(['businesscampaignrecurringpaymentdayinfo','businesscc'])->where('business_campaign_recurring_payment_id', $recurringId)->first()->toArray();
            BusinessCampaignRecurringPaymentDay::where('business_campaign_recurring_payment_id', $recurringId)->delete();

            //store data in business_campaign_recuring_payment table
            BusinessCampaignRecuringPayment::where('business_campaign_recurring_payment_id', $recurringId)
                ->update([
                    'business_id' => $businessId,
                    'campaign_id' => $campaignId,
                    'business_cc_id' => $lastFour,
                    'recurring_type' => ($type == 1 || $type == "1") ? "daily" : "amount",
                    'amount' => $charge,
                    'threshold_amount' => ($type == 2) ? $threshold : 0,
                    'updated_user_id' => $userId,
                    'is_active' => ($status > 0) ? 'yes' : 'no',
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            $daysName = [ 1 => 'Monday', 2 => 'Tuesday', 3 => 'Wednesday', 4 => 'Thursday', 5 => 'Friday', 6 => 'Saturday', 7 => 'Sunday'];
            if ($type == 1) {
                $dailyData = [];
                foreach ($day as $value) {
                    $dailyData[] = [
                        'business_campaign_recurring_payment_id' => $recurringId,
                        'day' => $value,
                        'payment_time' => $time,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    if (array_key_exists($value,$daysName)){
                        $daysArr[] = $daysName[$value];
                    }
                }
                BusinessCampaignRecurringPaymentDay::insert($dailyData);
            }

            $oldcard = $recuringDetail['businesscc']['card'];
            if (!empty($lastFourText) && $oldcard != $lastFourText) {
                $logArr[] = [
                    'created_user_id' => $userId,
                    'business_id' => $businessId,
                    'campaign_id' => $campaignId,
                    'field_name' => 'recurring_card',
                    'old_value' => $oldcard,
                    'new_value' => $lastFourText,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            $oldstatus = ( $recuringDetail['is_active'] == "yes" ) ? 1 : 0;
            if ( $status != '--' && $oldstatus != $status ) {
                $logArr[] = [
                    'created_user_id' => $userId,
                    'business_id' => $businessId,
                    'campaign_id' => $campaignId,
                    'field_name' => 'recurring_status',
                    'old_value' => ($oldstatus == 1 || $oldstatus == "1") ? "active" : "inactive",
                    'new_value' => ($status == 1 || $status == "1") ? "active" : "inactive",
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            $oldtype = ( $recuringDetail['recurring_type'] == "daily" ) ? 1 : 2;
            $oldthreshold_amount = $recuringDetail['threshold_amount'];
            if ( $type != '--') {
                if ( $oldtype != $type ) {
                    $logArr[] = [
                        'created_user_id' => $userId,
                        'business_id' => $businessId,
                        'campaign_id' => $campaignId,
                        'field_name' => 'recurring_type',
                        'old_value' => ( $oldtype == 1 || $oldtype == "1" ) ? "daily" : "amount",
                        'new_value' => ( $type == 1 || $type == "1") ? "daily" : "amount",
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                $daysInfo = $recuringDetail['businesscampaignrecurringpaymentdayinfo'];
                if ($type == 1) {
                    if ( $time != '--') {
                        if(count($daysInfo) > 0 ){
                            $oldtime = $daysInfo[0]['payment_time'];
                            if ( $oldtime != $time ) {
                                $logArr[] = [
                                    'created_user_id' => $userId,
                                    'business_id' => $businessId,
                                    'campaign_id' => $campaignId,
                                    'field_name' => 'recurring_time',
                                    'old_value' => $oldtime,
                                    'new_value' => $time,
                                    'created_at' => date('Y-m-d H:i:s')
                                ];
                            }
                        }else{
                            $logArr[] = [
                                'created_user_id' => $userId,
                                'business_id' => $businessId,
                                'campaign_id' => $campaignId,
                                'field_name' => 'recurring_time',
                                'old_value' => null,
                                'new_value' => $time,
                                'created_at' => date('Y-m-d H:i:s')
                            ];
                        }
                    }
                    if (isset($day) && count($day) > 0) {
                        if(count($daysInfo) > 0 ){
                            $olddays = $daysInfo;
                            $olddaysNumArr = [];
                            foreach ($olddays as $key => $value) {
                                $olddaysNumArr[] = $value['day'];
                            }
                            foreach ($olddaysNumArr as $key => $value) {
                                if (array_key_exists($value,$daysName)){
                                    $olddaysArr[] = $daysName[$value];
                                }
                            }
                            if ( implode(",", $olddaysArr) != implode(",",$daysArr)) {
                                $logArr[] = [
                                    'created_user_id' => $userId,
                                    'business_id' => $businessId,
                                    'campaign_id' => $campaignId,
                                    'field_name' => 'recurring_day',
                                    'old_value' => implode(",",$olddaysArr),
                                    'new_value' => implode(",",$daysArr),
                                    'created_at' => date('Y-m-d H:i:s')
                                ];
                            }
                        } else{
                            $logArr[] = [
                                'created_user_id' => $userId,
                                'business_id' => $businessId,
                                'campaign_id' => $campaignId,
                                'field_name' => 'recurring_day',
                                'old_value' => null,
                                'new_value' => implode(",",$daysArr),
                                'created_at' => date('Y-m-d H:i:s')
                            ];
                        }
                    }
                }else{
                    if (isset($threshold) && $oldthreshold_amount != $threshold) {
                        $logArr[] = [
                            'created_user_id' => $userId,
                            'business_id' => $businessId,
                            'campaign_id' => $campaignId,
                            'field_name' => 'recurring_threshold_amount',
                            'old_value' => $oldthreshold_amount,
                            'new_value' => $threshold,
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                    }
                }
            }

            $oldbusinessCampaignCharge = $recuringDetail['amount'];
            if (isset($businessCampaignCharge) && $oldbusinessCampaignCharge != $businessCampaignCharge) {
                $logArr[] = [
                    'created_user_id' => $userId,
                    'business_id' => $businessId,
                    'campaign_id' => $campaignId,
                    'field_name' => 'recurring_amount',
                    'old_value' => $oldbusinessCampaignCharge,
                    'new_value' => $businessCampaignCharge,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            $finalLogData = [];

            if(count($logArr) > 0 ){
                if ($level == 2) {
                    foreach ($logArr as $logdata) {
                        unset($logdata['business_id']);
                        $finalLogData[] = $logdata;
                    }
                    CampaignUpdateLog::insert($finalLogData);
                }else{

                    foreach ($logArr as $logdata) {
                        unset($logdata['campaign_id']);
                        $finalLogData[] = $logdata;
                    }

                    BusinessUpdateLog::insert($finalLogData);
                }
            }

            //send email to movers during add new card
            $businessPaymentControllerObj = new BusinessPaymentController();
            if ($type == 2 && $status > 0) {
                $businessPaymentControllerObj->amountBasedRecurrentEmailNotificationTemplate($businessId, $businessDetail->display_name, $businessCampaignName, $businessDetail->email, $cardNumber->card, $threshold);
            } else if ($type == 1 && $status > 0) {
                $businessPaymentControllerObj->dailyBasedRecurrentEmailNotificationTemplate($businessId, $businessDetail->display_name, $businessCampaignName, $businessDetail->email, $cardNumber->card, $daysArr);
            } else {
                $businessPaymentControllerObj->recurrentStopEmailNotificationTemplate($businessId, $businessDetail->display_name, $businessCampaignName, $businessDetail->email);
            }

            $status         = 1;
            $message        = 'success';
        } catch (Exception $e) {
            $message        = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'error' => "",
        ];
        return response()->json($responseArray);
    }

    public function moverBusinessEmailExistCheck() {

        $status = 0;
        $message = "error";
        $error =  $data = [];
        try {
            $elements       = json_decode(file_get_contents('php://input'), true);
            $email          = $elements['email'];

            $businessDetail = Business::where('movers_username', $email)->get(['business_id', 'business_name', 'email', 'movers_username', 'contact_name'])->toArray();

            if (count($businessDetail) > 0) {
                $data['business_id'] = $businessDetail[0]['business_id'];
                $data['business_name'] = $businessDetail[0]['business_name'];
                $data['business_email'] = $businessDetail[0]['email'];
                $data['movers_username'] = $businessDetail[0]['movers_username'];
                $data['contact_name'] = $businessDetail[0]['contact_name'];
                $data['token'] = Str::random(60);
                $status = 1;
                $message = 'success';
            }else{
                $error['email'] = 'Login email does not exist.';
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        $responseArray = [
            'status' => $status,
            'message' => $message,
            'data' => $data,
            'error' => $error
        ];
        // echo '<pre>'; print_r($responseArray); exit;
        return response()->json($responseArray);
    }

    public function businessResetEmailSend() {

        $status = 0;
        $message = "error";
        $error = "";
        try {
            $elements = json_decode(file_get_contents('php://input'), true);
            $email = $elements['business_email'];
            $token = $elements['token'];
            $businessResetEmailObj = new BusinessResetEmail();
            $emailBusinessResetEmailData = $businessResetEmailObj->emailBusinessResetEmail($elements);
            $response = json_decode($emailBusinessResetEmailData, true);
            if($response['Message'] == 'OK' ){
                $businessResetPasswordData = [
                    "business_id" => $elements['business_id'],
                    "token" => $token,
                    "created_at" => date('Y-m-d H:i:s')
                ];

                BusinessResetPassword::create($businessResetPasswordData);
                $status = 1;
                $message = "success";
            }else{
                $error = $response['Message'];
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        $responseArray = [
            'status' => $status,
            'message' => $message,
            'error' => $error
        ];
        // echo '<pre>'; print_r($responseArray); exit;
        return response()->json($responseArray);
    }

    public function checkBusinessToken() {

        $status = $businessid = 0;
        $message = "error";
        $error = $token = "";
        try {
            $elements = json_decode(file_get_contents('php://input'), true);

            $encbid = $elements['bid'];
            $auth_token = $elements['auth_token'];
            $endDecObj = new CommonFunctions;
            $businessid = $endDecObj->customeDecryption($encbid);

            $startdate = date('Y-m-d H:i:s',strtotime("-1 hour"));
            $businesresetData = BusinessResetPassword::where('business_id', $businessid)->where('token', $auth_token)->first();
            if($businesresetData){
                if($businesresetData->created_at > $startdate && $businesresetData->status == "active" ){
                    $token = $businesresetData->token;
                    $message = "success";
                    $status = 1;
                }else{
                    $error = "This link is expired";
                }
            }else{
                $error = "Invalid Link";
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        $responseArray = [
            'status' => $status,
            'businessid' => $businessid,
            'token' => $token,
            'message' => $message,
            'error' => $error
        ];
        // echo '<pre>'; print_r($responseArray); exit;
        return response()->json($responseArray);
    }

    public function saveResetPassword() {
        $status = 0;
        $message = "error";
        $error = "";
        try {
            $elements = json_decode(file_get_contents('php://input'), true);
            $password = $elements['password'];
            $password_confirmation = $elements['password_confirmation'];
            $business_id = $elements['business_id'];
            $token = $elements['token'];
            Business::where('business_id', $business_id)->update(["movers_password" => Hash::make($password)]);
            BusinessResetPassword::where('business_id', $business_id)->where('token', $token)->update(["status" => "inactive"]);
            $status = 1;
            $message = "success";
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
        $responseArray = [
            'status' => $status,
            'message' => $message,
            'error' => $error
        ];
        // echo '<pre>'; print_r($responseArray); exit;
        return response()->json($responseArray);
    }

    public function checkBusinessSigned() {
        $status = 0;
        $message = $businessName = $businessId = '';
        $businessData = [];
        try {
            $lastcontractdate = date('Y-m-d H:i:s', strtotime('2025-04-08 00:00:00'));
            $elements       = json_decode(file_get_contents('php://input'), true);
            $businessId     = $elements['businessId'];
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            $contract = Contract::where("business_id", $businessId)->where("status", 'signed')->where("created_at" ,">", $lastcontractdate)->first();
            $businessData = Business::where('business_id', $businessId)->get()->toArray();
            $issigned = "no";
            if($contract){
                $issigned = "yes";
            }
            $status         = 1;
            $message        = 'success';
        } catch (Exception $e) {
            $message        = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'businessData' => (count($businessData) > 0 ? $businessData[0] : []),
            'issigned' => $issigned
        ];
        return response()->json($responseArray);
    }

    public function signBusinessContract() {
        $status = 0;
        $message = $businessName = $businessId = '';
        $businessData = [];
        try {
            $elements       = json_decode(file_get_contents('php://input'), true);
            $businessId     = $elements['businessId'];
            $ip             = $elements['ip'];
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            $businessData   = Business::with(["businessCC"  => function ($query) {
                $query->where('status', 'active');
            }])->where('business_id', $businessId)->first()->toArray();

            $bCardsArr = $carddata = [];
            if (count($businessData['business_c_c']) > 0) {
                foreach ($businessData['business_c_c'] as $key => $value) {
                    $carddata['lastFour'] = 'XXXX-XXXX-XXXX-' . str_pad($value['card'], 4, "0", STR_PAD_LEFT);
                    $carddata['onlyLastFour'] = str_pad($value['card'], 4, "0", STR_PAD_LEFT);
                    $carddata['cardId'] = $value['business_cc_id'];
                    $bCardsArr[] = $carddata;
                }
            }

            $newContractData = array(
                "businessName" => $businessData['business_name'],
                "businessPhoneNumber" => $businessData['business_phone'],
                "businessOwnerName" => $businessData['owner_name'],
                "businessAddress" => $businessData['address'],
                "businessEmail" => $businessData['email'],
                "businessForwardNumber" => $businessData['forward_number'],
                "contactName" => $businessData['contact_name'],
                "emergencyNumber" => $businessData['emergency_phone'],
                "notificationPhoneNumber" => (isset($businessData['notification_phone']) ? $businessData['notification_phone'] : $businessData['business_phone']),
                "notificationEmail" => (isset($businessData['notification_email']) ? $businessData['notification_email'] : $businessData['email']),
                "signupAmount" => 0,
                "paymentRecurrentAmount" => null,
                "paymentRecurrentType" => 'daily',
                "usDot" => $businessData['dot_number'],
                "localStatsLicence" => $businessData['license_number'],
                "contractDeliveryType" => null,
                "contractEmail" => $businessData['email'],
                "notes" => null,
                // category
                "movingCategory" => null,
                "junkRemovalCategory" => null,
                "heavyLiftingCategory" => null,
                "carTransportationCategory" => null,
                // moving
                "pickup_m" => null,
                "destination_m" => null,
                "contracttype_m" => null,
                "chkLong_m" => null,
                "chkLocal_m" => null,
                "chkLiveCalls_m" => null,
                "longDistanceLead_m" => null,
                "localDistanceLead_m" => null,
                "liveCalls_m" => null,
                "phoneNumberLiveTransferCall_m" => null,
                "phoneNumberDialer_m" => null,
                "leadDeliveryEmail_m" => null,
                "granotEmail_m" => null,
                // junk removal
                "pickup_j" => null,
                "destination_j" => null,
                "contracttype_j" => null,
                "chkJunkLeads_j" => null,
                "chkLiveCalls_j" => null,
                "junkLeads_j" => null,
                "liveCalls_j" => null,
                "phoneNumberLiveTransferCall_j" => null,
                "phoneNumberDialer_j" => null,
                "leadDeliveryEmail_j" => null,
                "granotEmail_j" => null,
                // heavy lifting
                "pickup_h" => null,
                "destination_h" => null,
                "contracttype_h" =>  null,
                "chkLong_h" => null,
                "chkLocal_h" => null,
                "chkLiveCalls_h" => null,
                "longDistanceLead_h" => null,
                "localDistanceLead_h" => null,
                "liveCalls_h" => null,
                "phoneNumberLiveTransferCall_h" => null,
                "phoneNumberDialer_h" => null,
                "leadDeliveryEmail_h" => null,
                "granotEmail_h" => null,
                // car transportation
                "pickup_c" => null,
                "destination_c" => null,
                "contracttype_c" =>  null,
                "chkLong_c" => null,
                "chkLocal_c" => null,
                "chkLiveCalls_c" => null,
                "longDistanceLead_c" => null,
                "localDistanceLead_c" => null,
                "liveCalls_c" => null,
                "phoneNumberLiveTransferCall_c" => null,
                "phoneNumberDialer_c" => null,
                "leadDeliveryEmail_c" => null,
                "granotEmail_c" => null
            );

            $request = new Request($newContractData);
            $contractObj = new Contract();
            $contract = $contractObj->saveContract($request, 1, 6);
            $response = $contract->getData();
            $endDecObj = new CommonFunctions;
            $contractid = $endDecObj->customeDecryption($response->cid);
            $signature_path = NULL;
            // echo "<pre>";
            // print_r($elements);
            // die;
            $signed_img = $elements['signed_img'];
            $user_agent = $elements['user_agent'];
            if($contractid > 0 ){
                // store signature
                if ($signed_img != '') {
                    list($type, $signed_img) = explode(';', $signed_img);
                    list(, $signed_img)      = explode(',', $signed_img);
                    $signed_img = base64_decode($signed_img);
                    $imgname = $contractid ."_".rand() . '_' . time() . '.png';
                    @file_put_contents(public_path() . '/contract_signature/' . $imgname, $signed_img);
                    $signature_path = $imgname;
                }

                $updatedata = [
                    'business_id' => $businessId,
                    'contract_signed_date' => date('Y-m-d H:i:s'),
                    'contract_signed_ip' => $ip,
                    /*'contract_signed_ip' => Helper::get_client_ip(),*/
                    'contract_signature' => $signature_path,
                    'contract_signed_user_agent' => $user_agent
                ];
                Contract::where('contract_id', $contractid)->update($updatedata);
                // generate and store pdf
                $contract = Contract::where("contract_id", $contractid)->first()->toArray();
                $mover_dashboard_url = Helper::checkServer()['mover_dashboard_url'];
                $contract['matrix_url'] = Helper::checkServer()['matrixBaseURL_Outbound'];
                $contract['leadstudio_logo'] = $mover_dashboard_url . '/assets/images/email-image/logo.png';
                $contract['signature'] =  '/contract_signature/'. $contract['contract_signature'];
                $contract['ip'] = $contract['contract_signed_ip'];
                $contract['user_agent'] = $contract['contract_signed_user_agent'];
                $contract['business_cards'] = $bCardsArr;
                // echo "<pre>";
                // print_r($contract);
                // die;
                view()->share('contract', $contract);

                $pdf = PDF::loadView( 'contract.businesscontractpdf',$contract);
                $path = public_path().'/contract_signed_pdf/';
                $fileName =  $contractid .'_'.rand() . '_' . time().'.'. 'pdf';
                $pdf->save($path . $fileName);
                $pdffile = public_path().'/contract_signed_pdf/'.$fileName;

                $updatedata = [
                    'contract_screenshot' => $fileName,
                    'status' => 'signed'
                ];
                Contract::where('contract_id', $contractid)->update($updatedata);
                Business::where('business_id', $businessId)->update([
                    'is_contract_signed' => 'yes',
                ]);

                $emaildata = [];
                $emaildata['from'] = '<EMAIL>';
                $emaildata['to'] = $contract['contract_delivery_email'];
                $emaildata['subject'] = "Prime Lead Agreement : " . $contract['business_name'];
                $emaildata['contract_id'] = $contractid;
                $emaildata['business_name'] = $contract['business_name'];
                $emaildata['owner_name'] = $contract['owner_name'];
                $image_content = base64_encode(file_get_contents($pdffile));
                $attchment = ['Name' => $fileName, 'Content' => $image_content, 'ContentType' => 'image/png'];
                $emaildata['attachment'] = [$attchment];
                $contractsignedEmail = new ContractSignedEmail();
                $contractsignedEmail->emailContractSigned($emaildata);

                //pdf storage in s3 bucket
                $filePath = $path . $fileName; //server storage folder path
                $storagePath = $businessId . '/' . $fileName; //s3 bucket path
                if (isset($fileName) && file_exists($filePath)) {
                    Storage::disk('s3')->put($storagePath, file_get_contents($filePath));
                }
                $status  = 1;
                $message = 'success';
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        $responseArray = [
            'status' => $status,
            'message' => $message,
        ];
        return response()->json($responseArray);
    }

    public function moverPaymentHistoryReport(Request $request) {
        $status = $businessId = 0;
        $message = $businessName = $paymentCustId = '';
        $finalArray = $businessIdArray = $campaignIdArray = $businessArray = $campaignArray = $userIdArray = $userNameArray = $cardIdArray = $cardArray = $chargeTypeArray = $categoryArray = [];
        try {
            $businessId     = $request['businessId'];
            $startDate      = $request['startDate'];
            $endDate        = $request['endDate'];
            if (empty($businessId)) {
                throw new Exception("businessId Required");
            }
            if (empty($request['startDate'])) {
                throw new Exception("startDate required");
            }
            if (empty($request['endDate'])) {
                throw new Exception("endDate required");
            }

            // get Businesses Login Details
            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                throw new Exception('Invalid email or password');
            }
            $campaignDetail = Campaign::where('business_id', $businessId)->get(['campaign_id']);
            foreach ($campaignDetail as $campaign) {
                $campaignIdArray[]   = $campaign->campaign_id;
            }

            $paymentDetail  = BusinessCampaignRevSharePayment::where(function ($query) use ($businessId, $campaignIdArray){
                $query->where('business_id', $businessId)->orWhereIn('campaign_id', $campaignIdArray);
            })->where('created_at', '>=', $startDate)->where('created_at', '<=', $endDate)->get();

            foreach ($paymentDetail as $payment) {
                if ($payment->business_id > 0 && !in_array($payment->business_id, $businessIdArray)) {
                    $businessIdArray[] = $payment->business_id;
                }
                if ($payment->charge_user_id > 0 && !in_array($payment->charge_user_id, $businessIdArray)) {
                    $businessIdArray[] = $payment->charge_user_id;
                }
                if ($payment->campaign_id > 0 && !in_array($payment->campaign_id, $campaignIdArray)) {
                    $campaignIdArray[] = $payment->campaign_id;
                }
                if ($payment->charge_method_type == "snapituser" && !in_array($payment->charge_user_id, $userIdArray)) {
                    $userIdArray[] = $payment->charge_user_id;
                }
                if ($payment->business_cc_id > 0 && !in_array($payment->business_cc_id, $cardIdArray)) {
                    $cardIdArray[] = $payment->business_cc_id;
                }
            }

            $campaignDetail = Campaign::whereIn('campaign_id', $campaignIdArray)->get(['campaign_id', 'business_id', 'campaign_name', 'lead_category_id'])->toArray();
            for ($b = 0; $b < count($campaignDetail); $b++) {
                $campaignArray[$campaignDetail[$b]['campaign_id']] = $campaignDetail[$b];
                if (!in_array($campaignDetail[$b]['business_id'], $businessIdArray)) {
                    $businessIdArray[] = $campaignDetail[$b]['business_id'];
                }
            }
            //echo "<pre>";print_r($campaignArr);die;
            $businessDetail = Business::whereIn('business_id', $businessIdArray)->get(['business_id', 'business_name'])->toArray();
            for ($c = 0; $c < count($businessDetail); $c++) {
                $businessArray[$businessDetail[$c]['business_id']] = $businessDetail[$c]['business_name'];
            }
            $userDetail     = User::whereIn('id', $userIdArray)->get(['id', 'name'])->toArray();
            for ($u = 0; $u < count($userDetail); $u++) {
                $userNameArray[$userDetail[$u]['id']] = $userDetail[$u]['name'];
            }
            $cardDetail     = BusinessCC::whereIn('business_cc_id', $cardIdArray)->get(['business_cc_id', 'card'])->toArray();
            for ($g = 0; $g < count($cardDetail); $g++) {
                $cardArray[$cardDetail[$g]['business_cc_id']] = $cardDetail[$g]['card'];
            }

            $chargeTypeDetail= MstChargeType::get(['charge_type_id', 'charge_type']);
            for ($ct=0; $ct < count($chargeTypeDetail); $ct++) {
                $chargeTypeArray[$chargeTypeDetail[$ct]['charge_type_id']] = $chargeTypeDetail[$ct]['charge_type'];
            }

            $categoryDetail = MstLeadCategory::get(['lead_category_id', 'lead_category_name']);
            for ($ct=0; $ct < count($categoryDetail); $ct++) {
                $categoryArray[$categoryDetail[$ct]['lead_category_id']] = $categoryDetail[$ct]['lead_category_name'];
            }

            foreach ($paymentDetail as $payment) {
                $data = [];
                $data['id'] = $payment->business_campaign_rev_share_payment_id;
                $businessIdCheck = $payment->business_id;
                $data['businessName'] = '';
                if ($businessIdCheck > 0) {
                    $data['businessName'] = $businessArray[$businessIdCheck];
                }
                $data['campaignId'] = '--';
                $data['campaignName'] = '--';
                $categoryName = '--';
                $category = "0";
                if ($payment->campaign_id > 0) {
                    $data['campaignId'] = $payment->campaign_id;
                    if (isset($campaignArray[$payment->campaign_id])) {
                        $data['campaignName'] = $campaignArray[$payment->campaign_id]['campaign_name'];
                        $categoryId = $campaignArray[$payment->campaign_id]['lead_category_id'];
                        //echo $category."==".$payment->campaign_id."<br>";
                        $categoryName = $categoryArray[$categoryId];
                    }
                }
                $data['campaignCategory'] = $categoryName;
                $data['amountCharge'] = ($payment->amount_charge == 0) ? '--' : '$' . sprintf('%0.2f', $payment->amount_charge);
                $data['chargeType'] = ($payment->charge_type_id > 0) ? $chargeTypeArray[$payment->charge_type_id] : "--";
                $dateTime = strtotime($payment->created_at);
                $data['date'] = date("m/d H:i", $dateTime);
                $data['transactionId'] = empty($payment->fatt_payment_id) ? '--' : $payment->fatt_payment_id;
                $data['status'] = ($payment->is_charge_approved == "yes") ? 'Approved' : 'Declined';

                $data['cardNumber'] = '';
                if (isset($cardArray[$payment->business_cc_id])) {
                    $data['cardNumber'] = 'XXXX-XXXX-XXXX-' . $cardArray[$payment->business_cc_id];
                }
                $finalArray[] = $data;
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'data' => $finalArray
        ];

        return response()->json($responseArray);
    }

    public function moverSubscription (Request $request)
    {
        $status = $businessId = $subscriptionPlanId = $subscriptionPlanAmount = 0;
        $message = $businessCampaignName = $subscriptionPlanName = $subscriptionPlanExpired = '';
        try {

            $businessId     = $request['businessId'];
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            $businessDetail = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            }

            $subscriptionDetail         = Subscription::where('subscription_start', '>=', date('Y-m-01'))->where('subscription_end', '<=', date('Y-m-t'))->where('is_active', 'yes')->where('business_id', $businessId)->first();
            if ($subscriptionDetail) {
                $subscriptionPlanDetail = MstSubscriptionPlan::where('subscription_plan_id', $subscriptionDetail->subscription_plan_id)->first();
                $subscriptionPlanId     = $subscriptionPlanDetail->subscription_plan_id;
                $subscriptionPlanName   = $subscriptionPlanDetail->subscription_plan;
                $subscriptionPlanAmount = $subscriptionPlanDetail->amount;
                $subscriptionPlanExpired= date('m/d/Y', strtotime($subscriptionDetail->subscription_end));
            } else {
                $subscriptionPlanId     = 1;
                $subscriptionPlanName   = 'Free Plan';
                $subscriptionPlanAmount = '--';
                $subscriptionPlanExpired= '';
            }

            $status         = 1;
            $message        = 'success';
        } catch (Exception $e) {
            $message        = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'businessId' => $businessId,
            'subscriptionPlanId' => $subscriptionPlanId,
            'subscriptionPlanName' => $subscriptionPlanName,
            'subscriptionPlanAmount' => $subscriptionPlanAmount,
            'subscriptionPlanExpired' => $subscriptionPlanExpired
        ];
        return response()->json($responseArray);
    }

    public function moverSetPrimaryCard (Request $request)
    {
        $status = 0;
        $message = '';
        try {

            $businessId         = $request['businessId'];
            $lastFour           = $request['lastFour'];
            if (empty($businessId)) {
                throw new Exception("businessId required");
            }
            $businessDetail     = Business::where('business_id', $businessId)->first();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            }
            $oldCardDetail      = BusinessCC::where('business_id', $businessId)->where('is_primary', 'yes')->first();
            $cardDetail         = BusinessCC::where('business_cc_id', $lastFour)->first();
            BusinessCC::where('business_cc_id', '<>', $lastFour)->where('business_id', $businessId)->update([
                'is_primary' => 'no'
            ]);
            BusinessCC::where('business_cc_id', $lastFour)->where('business_id', $businessId)->update([
                'is_primary' => 'yes'
            ]);

            $logArr[] = [
                'created_user_id' => 77,
                'business_id' => $businessId,
                'field_name' => 'primary_card',
                'old_value' => (isset($oldCardDetail)) ? 'XXXX-XXXX-XXXX-' . str_pad($oldCardDetail->card, 4, "0", STR_PAD_LEFT) : '--',
                'new_value' => 'XXXX-XXXX-XXXX-' . str_pad($cardDetail->card, 4, "0", STR_PAD_LEFT),
                'created_at' => date('Y-m-d H:i:s')
            ];
            if (count($logArr) > 0) {
                BusinessUpdateLog::insert($logArr);
            }

            //send email to movers during set new primary card
            $businessPaymentObj = new BusinessPaymentController();
            $businessPaymentObj->setPrimaryCardEmailNotificationTemplate($businessDetail->business_id, $businessDetail->display_name, $businessDetail->email, $cardDetail->card);

            $status             = 1;
            $message            = 'success';
        } catch (Exception $e) {
            $message            = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'error' => "",
        ];
        return response()->json($responseArray);
    }

    public function moverMarketplaceLeadDataNew(Request $request)
    {
        $status = 0;
        $message = '';
        $finalArray = [];
        $activeCampAssocDataAr = [];

        try {
            $elements = json_decode(file_get_contents('php://input'), true);

            if (empty($elements['startDate'])) throw new Exception("startDate required");
            if (empty($elements['endDate'])) throw new Exception("endDate required");
            if (empty($elements['businessId'])) throw new Exception("businessId required");

            $startDate       = $elements['startDate'] . ' 00:00:00';
            $endDateInput    = $elements['endDate'] . ' 23:59:59';
            $businessId      = $elements['businessId'];
            $leadType        = $elements['leadType'];
            $fromPrice       = $elements['fromPrice'] ?? null;
            $toPrice         = $elements['toPrice'] ?? null;
            $limit           = $elements['limit'] ?? 0;
            $orderBy         = $elements['orderBy'] ?? 'DESC';

            $currentDate     = date('Y-m-d');
            $currentDateTime = date('Y-m-d H:i:s');
            $twoMinutesAgo   = (new DateTime())->modify('-2 minutes')->format('Y-m-d H:i:s');
            $endDate         = ($endDateInput >= $currentDate) ? $twoMinutesAgo : $endDateInput;

            $moveDateRange   = explode(' - ', $elements['moveDate']);
            $moveDateStart   = date("Y-m-d", strtotime($moveDateRange[0] ?? ''));
            $moveDateEnd     = date("Y-m-d", strtotime($moveDateRange[1] ?? ''));

            $businessDetail = Business::find($businessId);
            if (!$businessDetail) {
                $status = 2;
                throw new Exception('Invalid email or password');
            }

            $subscription = Subscription::where('business_id', $businessId)
                ->where('is_active', 'yes')
                ->where('subscription_start', '>=', date('Y-m-01'))
                ->where('subscription_end', '<=', date('Y-m-t'))
                ->first();

            $subscriptionPlan = $subscription
                ? MstSubscriptionPlan::find($subscription->subscription_plan_id)
                : null;

            // Dynamic filters
            $where = '';

            if (strpos($leadType, '-1') !== false) {
                $where .= " AND lm.from_state = lm.to_state";
            } elseif (strpos($leadType, '-2') !== false) {
                $where .= " AND lm.from_state <> lm.to_state";
            }

            if ($moveDateEnd > $currentDate) {
                $where .= " AND lm.move_date > '$moveDateStart' AND lm.move_date <= '$moveDateEnd'";
            }

            if (!empty($elements['fromState'])) {
                $fromStates = "'" . implode("','", $elements['fromState']) . "'";
                $where .= " AND lm.from_state IN ($fromStates)";
            }

            if (!empty($elements['fromZip'])) {
                $fromZips = implode(',', explode(',', rtrim($elements['fromZip'], ',')));
                $where .= " AND lm.from_zipcode IN ($fromZips)";
            }

            if (!empty($elements['toState'])) {
                $toStates = "'" . implode("','", $elements['toState']) . "'";
                $where .= " AND lm.to_state IN ($toStates)";
            }

            if (!empty($elements['toZip'])) {
                $toZips = implode(',', explode(',', rtrim($elements['toZip'], ',')));
                $where .= " AND lm.to_zipcode IN ($toZips)";
            }

            if (!empty($elements['moveSize'])) {
                $moveSizes = implode(',', $elements['moveSize']);
                $where .= " AND lm.move_size_id IN ($moveSizes)";
            }

            $leadTypeId = $leadType;
            $campaignIdArray = Campaign::where('business_id', $businessId)->pluck('campaign_id')->toArray();
            $campaignIds    = implode(',', $campaignIdArray);
            // Main query
            $leadDetail = DB::select("
                SELECT
                    l.lead_id, l.created_at, l.is_verified, l.remaining_slot,
                    lm.from_state, lm.to_state, lm.from_zipcode, lm.to_zipcode,
                    lm.from_areacode, lm.to_areacode,
                    o.lead_source_name
                FROM `lead` l
                INNER JOIN lead_moving lm ON l.lead_id = lm.lead_id
                INNER JOIN mst_lead_source o ON o.lead_source_id = l.lead_source_id
                WHERE l.remaining_slot > 0
                AND l.sold_type NOT IN ('exclusive','organic')
                AND l.created_at BETWEEN ? AND ?
                AND l.lead_id NOT IN (
                    SELECT lead_id FROM lead_routing
                    WHERE campaign_id IN ($campaignIds) GROUP BY lead_id
                )
                AND lm.move_type = (
                    SELECT move_type FROM campaign_moving
                    WHERE campaign_id = ?
                )
                AND l.is_dnc_call = 'no'
                AND l.is_marketplace = 'yes'
                $where
                ORDER BY l.lead_id $orderBy
            ", [$startDate, $endDate, $leadTypeId]);

            foreach ($leadDetail as $lead) {
                $originName = $lead->lead_source_name ?? '';
                $ageMinutes = abs(strtotime($currentDateTime) - strtotime($lead->created_at)) / 60;

                if (
                    in_array($originName, ['VM(MA)', 'VM(MAV)']) ||
                    (strpos($originName, 'IS') !== false)
                ) {
                    if ($ageMinutes < 20) continue;
                }

                if ($leadTypeId > 0) {
                    $leadCampaignLogic = new LeadCampaignLogic();
                    $campaignCheck = $leadCampaignLogic->checkFromZip(
                        [$leadTypeId], $activeCampAssocDataAr,
                        $lead->from_zipcode, $lead->to_zipcode,
                        $lead->from_state, $lead->to_state,
                        $lead->from_areacode, $lead->to_areacode
                    );

                    if (empty($campaignCheck)) continue;
                }

                $coverageType = $lead->from_state === $lead->to_state ? 'local' : 'long';

                $price = $this->getMarketplaceLeadPrice($lead->created_at, $lead->is_verified);

                // Slot-based price adjustment
                $price += match ((int)$lead->remaining_slot) {
                    4 => 4,
                    3 => 2,
                    2, 1 => 1,
                    default => 0,
                };

                if ($coverageType === 'local') {
                    $price = ceil($price * 0.6);
                }

                $newPrice = 0;
                if ($subscriptionPlan && $subscriptionPlan->dicount_percentage > 0) {
                    $newPrice = round(($price * (100 - $subscriptionPlan->dicount_percentage)) / 100, 2);
                }

                if (
                    isset($fromPrice, $toPrice) &&
                    ($price < $fromPrice || $price > $toPrice)
                ) continue;

                $leadCreatedAt      = date('Y-m-d', strtotime($lead->created_at));
                $createdDate        = new DateTime($leadCreatedAt);
                $interval           = $createdDate->diff(new DateTime());
                $finalArray[] = [
                    'leadId' => $lead->lead_id,
                    'price' => $price,
                    'newPrice' => $newPrice,
                    'missedLeadAvg' => $interval->days ?? 0
                ];
            }

            if ($limit > 0 && count($finalArray) > $limit) {
                $finalArray = array_slice($finalArray, 0, $limit);
            }

            $status = 1;
            $message = 'success';
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        return response()->json([
            'status' => $status,
            'message' => $message,
            'data' => $finalArray
        ]);
    }

    public function marketplaceleadtest(Request $request) {
        $status = $businessId = 0;
        $message = $limit = '';
        $finalArray = $campaignIdArray = $smsverifyleadIdArray = [];
        try {

            $elements = json_decode(file_get_contents('php://input'), true);

            if (empty($elements['startDate'])) {
                throw new Exception("startDate required");
            }
            if (empty($elements['endDate'])) {
                throw new Exception("endDate required");
            }
            if (empty($elements['businessId'])) {
                throw new Exception("businessId required");
            }

            $startDate          = $elements['startDate'].' 00:00:00';
            $endDate            = $elements['endDate'].' 23:59:59';
            $businessId         = $elements['businessId'];
            $businessDetail     = Business::where('business_id', $businessId)->first();
            $subscriptionDetail = Subscription::where('subscription_start', '>=', date('Y-m-01'))->where('subscription_end', '<=', date('Y-m-t'))->where('is_active', 'yes')->where('business_id', $businessId)->first();
            if (isset($subscriptionDetail)) {
                $subscriptionPlanDetail = MstSubscriptionPlan::where('subscription_plan_id', $subscriptionDetail->subscription_plan_id)->first();
            }
            $currentDate        = date('Y-m-d');
            $currentDateTime    = date('Y-m-d H:i:s');
            $twomiutes          = new DateTime($currentDateTime);
            $twomiutes->modify('-2 minutes');
            $twomiutes          = $twomiutes->format('Y-m-d H:i:s');
            if ($endDate >= $currentDate) {
                $endDate        = $twomiutes;
            }
            $leadType           = $elements['leadType'];
            $fromPrice          = $elements['fromPrice'];
            $toPrice            = $elements['toPrice'];
            $limit              = $elements['limit'] ?? 0;
            $orderBy            = $elements['orderBy'] ?? 'DESC'; //ASC //DESC
            $moveDateArray      = explode(' - ', $elements['moveDate']);
            $moveDateStart      = date("Y-m-d", strtotime($moveDateArray[0]));
            $moveDateEnd        = date("Y-m-d", strtotime($moveDateArray[1]));
            $campaignIdArray = Campaign::where('business_id', $businessId)->pluck('campaign_id')->toArray();
            if (!$businessDetail) {
                $status = '2';
                throw new Exception('Invalid email or password');
            } else {
                $campaignLocations = DB::table('campaign_location')
                    ->whereIn('campaign_id', [$leadType])
                    ->get()
                    ->groupBy('campaign_id');

                $sourceMap = [];
                $destinationMap = [];

                foreach ($campaignLocations as $cid => $locations) {
                    $sourceMap[$cid] = ['state' => [], 'zipcode' => [], 'areacode' => []];
                    $destinationMap[$cid] = ['state' => [], 'zipcode' => [], 'areacode' => []];

                    foreach ($locations as $loc) {
                        if ($loc->location_type === 'source') {
                            $sourceMap[$cid][$loc->location_coverage][] = $loc->value;
                        } elseif ($loc->location_type === 'destination') {
                            $destinationMap[$cid][$loc->location_coverage][] = $loc->value;
                        }
                    }
                }
                $campaignId = $leadType;
                $leadQuery = DB::table('lead as l')
                        ->select(
                            'l.lead_id',
                            'l.name',
                            'l.created_at as lead_created_at',
                            'l.is_verified',
                            'l.remaining_slot',
                            'lm.from_state',
                            'lm.to_state',
                            'lm.from_zipcode',
                            'lm.to_zipcode',
                            'lm.from_areacode',
                            'lm.to_areacode',
                            'lm.created_at',
                            'lm.move_size_id',
                            'lm.move_date',
                            'o.lead_source_name'
                        )
                        ->join('lead_moving as lm', 'l.lead_id', '=', 'lm.lead_id')
                        ->join('mst_lead_source as o', 'o.lead_source_id', '=', 'l.lead_source_id')
                        ->whereBetween('l.created_at', [$startDate, $endDate])
                        ->where('l.remaining_slot', '>', 0)
                        ->whereNotIn('l.sold_type', ['exclusive', 'organic'])
                        ->whereNotIn('l.lead_id', function ($q) use ($campaignIdArray) {
                            $q->select('lead_id')
                                ->from('lead_routing')
                                ->whereIn('campaign_id', $campaignIdArray)
                                ->groupBy('lead_id');
                        })
                        ->where('lm.move_type', function ($q) use ($campaignId) {
                            $q->select('move_type')
                                ->from('campaign_moving')
                                ->where('campaign_id', $campaignId)
                                ->limit(1);
                        })
                        ->where('l.is_dnc_call', 'no')
                        ->where('l.is_marketplace', 'yes')
                        ->where(function ($query) use ($sourceMap, $destinationMap) {
                            foreach ($sourceMap as $campaignId => $src) {
                                $dst = $destinationMap[$campaignId];

                                $query->orWhere(function ($q) use ($src, $dst) {
                                    $q->where(function ($q2) use ($src) {
                                        $q2->whereIn('lm.from_state', $src['state'] ?? [])
                                            ->orWhereIn('lm.from_zipcode', $src['zipcode'] ?? [])
                                            ->orWhereIn('lm.from_areacode', $src['areacode'] ?? []);
                                    })->where(function ($q3) use ($dst) {
                                        $q3->whereIn('lm.to_state', $dst['state'] ?? [])
                                            ->orWhereIn('lm.to_zipcode', $dst['zipcode'] ?? [])
                                            ->orWhereIn('lm.to_areacode', $dst['areacode'] ?? []);
                                    });
                                });
                            }
                        });
                if ($moveDateEnd > $currentDate) {
                    $leadQuery->whereBetween('lm.move_date', [$moveDateStart, $moveDateEnd]);
                }
                if (!empty($elements['fromState'])) {
                    $leadQuery->whereIn('lm.from_state', $elements['fromState']);
                }

                if (!empty($elements['fromZip'])) {
                    $qfromZip = explode(',', rtrim($elements['fromZip'], ','));
                    $leadQuery->whereIn('lm.from_zipcode', $qfromZip);
                }

                if (!empty($elements['toState'])) {
                    $leadQuery->whereIn('lm.to_state', $elements['toState']);
                }

                if (!empty($elements['toZip'])) {
                    $qtoZip = explode(',', rtrim($elements['toZip'], ','));
                    $leadQuery->whereIn('lm.to_zipcode', $qtoZip);
                }

                if (!empty($elements['moveSize'])) {
                    $leadQuery->whereIn('lm.move_size_id', $elements['moveSize']);
                }
                $leadQuery->orderBy('l.lead_id', $orderBy);

                foreach ($leadQuery->get() as $lead) {
                    $fromData = MstZipcode::where('zipcode', $lead->from_zipcode)->first();
                    $toData = MstZipcode::where('zipcode', $lead->to_zipcode)->first();
                    $fromState = $lead->from_state;
                    $toState = $lead->to_state;
                    $distance = 0;
                    if(!empty($fromData->lat) && !empty($toData->lat) && !empty($fromData->long) && !empty($toData->long)) {
                        $distance = CommonFunctions::calculateMileage($fromData->lat, $toData->lat, $fromData->long, $toData->long);
                    }
                    $dateTimeReceived = $lead->created_at;
                    $originName = $lead->lead_source_name;
                    $pos = strpos($originName, 'IS');
                    $difference = abs(strtotime($currentDateTime) - strtotime($dateTimeReceived)) / 60;
                    if (($originName == "VM(MA)" || $originName == "VM(MAV)" || $pos !== false) && $difference < 20) {
                        continue;
                    }
                    $coverageType = 'local';
                    if(strcmp($fromState, $toState)) {
                        $coverageType = 'long';
                    }
                    $price = $this->getMarketplaceLeadPrice($dateTimeReceived, $lead->is_verified);
                    if ($lead->remaining_slot == 4) {
                        $price = $price + 4;
                    } else if ($lead->remaining_slot == 3) {
                        $price = $price + 2;
                    } else if ($lead->remaining_slot == 2) {
                        $price = $price + 1;
                    } else if ($lead->remaining_slot == 1) {
                        $price = $price + 1;
                    }
                    if ($coverageType == 'local') {
                        $price = ceil(($price * 60) / 100);
                    }

                    $newPrice = 0;
                    if (isset($subscriptionPlanDetail) && $subscriptionPlanDetail->dicount_percentage > 0) {
                        $newPrice = ($price * (100 - $subscriptionPlanDetail->dicount_percentage)) / 100;
                    }

                    if (isset($fromPrice) && isset($toPrice)) {
                        if ($price >= $fromPrice && $price <= $toPrice) {
                        } else {
                            continue;
                        }
                    }
                    $isHotDeal = 0;
                    if ($lead->remaining_slot >= 3 && $lead->remaining_slot <= 4 && $lead->is_verified == 'yes') {
                        $isHotDeal = 1;
                    } else if ($distance > 1000 && $lead->is_verified == 'yes') { //1000 Miles
                        $isHotDeal = 1;
                    } else if ($lead->move_size_id >= 3 && $lead->is_verified == 'yes') { //2 = Two Bedroom
                        $isHotDeal = 1;
                    } else if (in_array($lead->lead_id, $smsverifyleadIdArray) && $lead->is_verified == 'yes') {
                        $isHotDeal = 1;
                    }
                    $leadCreatedAt = date('Y-m-d', strtotime($lead->lead_created_at));
                    $createdDate = new DateTime($leadCreatedAt);
                    $interval = $createdDate->diff(new DateTime());
                    $data['leadId'] = $lead->lead_id;
                    $data['name'] = $lead->name;
                    $data['fromZipCode'] = str_pad($lead->from_zipcode, 5, '0', STR_PAD_LEFT);
                    $data['fromState'] = $lead->from_state;
                    $data['toZipCode'] = str_pad($lead->to_zipcode, 5, '0', STR_PAD_LEFT);
                    $data['toState'] = $lead->to_state;
                    $data['moveSize'] = ($lead->move_size_id == 1 ) ? "Studio" : $lead->move_size_id . " BR";
                    $data['moveDate'] = date("m/d/y", strtotime($lead->move_date));
                    $data['createdDate'] = date("m/d/y", strtotime($lead->created_at));
                    $data['createdTime'] = date("h:i A", strtotime($lead->created_at));
                    $data['sortTime'] = date("H:i:s", strtotime($lead->created_at));
                    $data['isVerified'] = $lead->is_verified;
                    $data['price'] = $price;
                    $data['newPrice'] = $newPrice;
                    $data['type'] = 0;
                    $data['isHotDeal'] = $isHotDeal;
                    $data['missedLeadAvg'] = $interval->days ?? 0;
                    $finalArray[] = $data;
                }
                if ($limit > 0 && count($finalArray) > 0) {
                    $finalArray = array_slice($finalArray, 0, $limit);
                }
            }
            $status = 1;
            $message = 'success';
        } catch (Exception $e) {
            $message = $e->getMessage();
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'data' => $finalArray
        ];
        return response()->json($responseArray);
    }

    public function updateLeadMovingData() {
        DB::table('lead_moving')
            ->whereColumn('from_state', 'to_state')
            ->update(['move_type' => 'local']);

        DB::table('lead_moving')
            ->whereColumn('from_state', '!=', 'to_state')
            ->update(['move_type' => 'long']);

        return true;
    }
}