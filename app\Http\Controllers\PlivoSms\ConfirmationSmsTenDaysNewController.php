<?php

namespace App\Http\Controllers\PlivoSms;

use Illuminate\Http\Request;
use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\PlivoSms\ScheduleSMS;
use App\Models\Campaign\Campaign;
use App\Models\Campaign\CampaignOrganic;
use App\Models\CronJob\CronLog;
use App\Models\Lead\Lead;
use App\Models\Lead\LeadCustomerSmsLog;
use App\Models\Lead\LeadRouting;
use App\Models\Master\MstLeadSource;
use DateTime;
use Exception;
use DB;
use App\Models\Master\MstTimeZone;
use App\Models\Lead\LeadActivity;
use App\Models\Outbound\GeneralNumberConfig;

class ConfirmationSmsTenDaysNewController extends Controller
{
    public function confirmationSmscron(Request $request){
        date_default_timezone_set('America/New_York');
        // dd("for now don't want send");
        $cronLog                = new CronLog();
        $cronLog->cronlog(4, 'request='.json_encode($request->all(), true), 'confirmationsmstendayscron');
        $cronLog->cronlog(4, 'start', 'confirmationsmstendayscron');

        $smsstartDate           = date("Y-m-d H:i:s", strtotime("-1 day"));
        $smsendDate             = date("Y-m-d H:i:s");
        $cronLog->cronlog(4, 'smsstartDate= ' . $smsstartDate . ", smsendDate= " . $smsendDate, 'confirmationsmstendayscron');
        $currentdateTime        = date('Y-m-d H:i:s');
        $date10                 = new DateTime($currentdateTime);
        $date10->modify('-10 days');
        $formatted_start_date   = $date10->format('Y-m-d 00:00:00');
        $formatted_end_date     = $date10->format('Y-m-d 23:59:59');
        $loginfo                = array();
        try {
            $cronLog->cronlog(4, 'startdate= ' . $formatted_start_date . ", enddate= " . $formatted_end_date, 'confirmationsmstendayscron');
            $timezoneids        = [];
            $timezone           = $request['timezone'];
            if( $timezone == 'est'){
                $timezoneids    = MstTimeZone::where("timezone", "America/New_York")->pluck('timezone_id')->toArray();
            }else if($timezone == 'cst'){
                $timezoneids    = MstTimeZone::where("timezone", "America/Chicago")->pluck('timezone_id')->toArray();
            }else if($timezone == 'mst'){
                $timezoneids    = MstTimeZone::whereIn("timezone", ["America/Denver", "America/Phoenix"])->pluck('timezone_id')->toArray();
            }else{
                $timezoneids    = MstTimeZone::whereNotIn("timezone", ["America/New_York", "America/Chicago", "America/Denver", "America/Phoenix"])->pluck('timezone_id')->toArray();
            }
            $cronLog->cronlog(4, 'timezone= ' . $timezone . ", timezoneids= " . json_encode($timezoneids, true), 'confirmationsmstendayscron');
            $leads = Lead::whereBetween('lead.created_at', [$formatted_start_date, $formatted_end_date])
                ->whereIn('lead.timezone_id', $timezoneids)
                ->where('lead.is_dnc_sms', 'no')
                ->whereNotIn('lead_id', function ($query) use ($smsstartDate, $smsendDate) {
                    return $query->select('lead_customer_sms_log.lead_id')
                        ->from('lead_customer_sms_log')
                        ->whereBetween('lead_customer_sms_log.created_at', [$smsstartDate, $smsendDate])
                        ->where('lead_customer_sms_log.lead_id', DB::raw('lead.lead_id'))
                        ->where('lead_customer_sms_log.sms_subject', 'Lead Confirmation Message (10 Days)');
                })->orderBy('lead.lead_id', 'ASC')
                ->take(20)
                ->get()
                ->toArray();

            // echo "<pre>start=". $formatted_start_date. ", end= ". $formatted_end_date." / ";
            // print_r($leads); die;
            $cronLog->cronlog(4, "time= ". $timezone. ', leads= ' . json_encode($leads, true), 'confirmationsmstendayscron');
            $sleepCounter = 0;
            if (count($leads) > 0) {
                foreach ($leads as $againlead) {
                    $cronLog->cronlog(4, 'SMS sending', 'confirmationsmstendayscron');
                    $loginfo[$againlead['lead_id']] = $this->LeadConfirmationSms($againlead['lead_id'], 1);
                    $sleepCounter = $sleepCounter + 1;
                    if ($sleepCounter % 2 == 0) {
                        sleep(1);
                    }
                }

                $cronLog->cronlog(4, 'cron final response= ' . json_encode($loginfo, true), 'confirmationsmstendayscron');
                echo json_encode($loginfo, true);
            } else {
                dd("No leads to send sms");
            }
        } catch (Exception $e) {
            $cronLog->cronlog(4, 'catch= ' . json_encode($e->getMessage(), true), 'confirmationsmstendayscron');
        }
        exit;
    }

    public function LeadConfirmationSms($leadId, $nightflag)
    {
        //echo 'exit;';exit;
        $cronLog = new CronLog();
        $cronLog->cronlog(3, 'LeadConfirmationSms start=' . $leadId . ", nightflag=" . $nightflag, 'confirmationsmstendayscron');

        try {
            $leadsphonedata = Lead::with(['moveInfo', 'junkMoveInfo', 'heavyLiftMoveInfo', 'carTransMoveInfo', 'routingInfoLeadList', 'MstLeadCategory'])->where('lead_id', $leadId)->first();
            $scheduleSMS = ScheduleSMS::where('sms_subject', 'Lead Confirmation Message (10 Days)')->first();
            //echo '<pre>'; print_r($scheduleSMS); die;
            if (isset($scheduleSMS->campaign_id)) {
                $campaignIds = explode(',', $scheduleSMS->campaign_id);
                $businessData = CampaignOrganic::whereIn('campaign_id', $campaignIds)->whereNotNull('contact_number')->orderByRaw("FIELD(campaign_id, $scheduleSMS->campaign_id)")->get(['business_name', 'contact_number']);
            }
            if (empty($businessData)) {
                return 'No leads to send sms';
            }
            $cronLog->cronlog(3, 'leaddata= ' . json_encode($leadsphonedata, true), 'confirmationsmstendayscron');

            $from_state = $to_state = $to_city = '';
            if ($leadsphonedata->lead_category_id == 1) {
                if (isset($leadsphonedata->moveInfo) && $leadsphonedata->moveInfo != null) {
                    $from_state = $leadsphonedata->moveInfo->from_state;
                    $to_state = $leadsphonedata->moveInfo->to_state;
                    $to_city = $leadsphonedata->moveInfo->to_city;
                }
            }
            if ($leadsphonedata->lead_category_id == 2) {
                if (isset($leadsphonedata->junkMoveInfo) && $leadsphonedata->junkMoveInfo != null) {
                    // $from_state = 'NY';
                    $from_state = $to_state = $leadsphonedata->junkMoveInfo->from_state;
                    $to_city = $leadsphonedata->junkMoveInfo->from_city;
                }
            }
            if ($leadsphonedata->lead_category_id == 3) {
                if (isset($leadsphonedata->heavyLiftMoveInfo) && $leadsphonedata->heavyLiftMoveInfo != null) {
                    $from_state = $leadsphonedata->heavyLiftMoveInfo->from_state;
                    $to_state = $leadsphonedata->heavyLiftMoveInfo->to_state;
                    $to_city = $leadsphonedata->heavyLiftMoveInfo->to_city;
                }
            }
            if ($leadsphonedata->lead_category_id == 4) {
                if (isset($leadsphonedata->carTransMoveInfo) && $leadsphonedata->carTransMoveInfo != null) {
                    $from_state = $leadsphonedata->carTransMoveInfo->from_state;
                    $to_state = $leadsphonedata->carTransMoveInfo->to_state;
                    $to_city = $leadsphonedata->carTransMoveInfo->to_city;
                }
            }
            $cronLog->cronlog(3, 'LeadConfirmationSms start=' . $leadId . ", from_state=" . $from_state, 'confirmationsmstendayscron');

            $hours_check_cron = date('G');
            $cronLog->cronlog(3, 'hour='. $hours_check_cron, 'confirmationsmssevendayscron');
            if (isset($from_state) && $from_state != '') {
                $timezoneName   = MstTimeZone::where("timezone_id", $leadsphonedata->timezone_id)->first();
                date_default_timezone_set($timezoneName->timezone);
                $hours_check_cron = date('G');
                if ($hours_check_cron < 8 || $hours_check_cron >= 20) {
                    $cronLog->cronlog(3, 'Night leadid='. $leadId, 'confirmationsmstendayscron');
                    return 'Night time';
                }
            } else if ($hours_check_cron < 8 || $hours_check_cron >= 20) {
                $cronLog->cronlog(3, 'Night leadid='. $leadId, 'confirmationsmstendayscron');
                return 'Night time';
            }
            $checkPhone = $leadsphonedata->is_dnc_sms;
            if ($checkPhone == "no") {
                $origin = MstLeadSource::where('lead_source_id', $leadsphonedata->lead_source_id)->first();
                $src = $text = $sWhere = '';
                $pos = strpos($origin->lead_source_name, 'VM');
                $pos1 = strpos($origin->lead_source_name, 'QTM');
                $pos2 = strpos($origin->lead_source_name, 'IS');
                $cronLog->cronlog(3, 'lead_source_name=' . $origin->lead_source_name, 'confirmationsmstendayscron');

                $key = 0;
                foreach ($businessData as $value) {
                    /*if ($key > 0) {
                        $sWhere.= ', ' . $value->business_name.', +1 '.$this->formatPhoneNumber($value->contact_number);
                    } else {*/
                        $sWhere.= "\n📞 " . $value->business_name.', +1 '.$this->formatPhoneNumber($value->contact_number);
                    /*}
                    $key++;*/
                }

                if ($pos !== false) {
                    $src = '7252081224';  //VM & Commio
                    if (strcmp($from_state, $to_state)) {
                        $text = "Hi " . $leadsphonedata->name . "\n⏳ Last chance to grab a deal on your move!" . $sWhere . "\n📞 Quote The Move: ******-767-2122\n Final discounted slots filling up!\n Reply STOP to unsubscribe.";
                    } else {
                        $text = "Hi " . $leadsphonedata->name . ", We have new discounted quote to your city " . ucwords(strtolower($to_city)) . ", " . strtoupper($to_state) . ". You can save up to $500. Please call us at ******-455-2576 and the quote will be provided to you over-the-phone by Van Lines Move. Reply STOP to unsubscribe. Msg & data rates may apply.";
                    }

                } else if ($pos1 !== false) {
                    $src = '4153290031'; // QTM
                    if (strcmp($from_state, $to_state)) {
                        $text = "Hi " . $leadsphonedata->name . "\n⏳ Last chance to grab a deal on your move!" . $sWhere . "\n📞 Interstates Mover: ******-566-3184\n Final discounted slots filling up!\n Reply STOP to unsubscribe.";
                    } else {
                        $text = "Just a quick follow-up from quotethemove.com: Your customized moving quotes are ready! Feel free to reach us at ******-455-2577 to discuss the next steps. Let's get moving! Reply STOP to unsubscribe. Msg & data rates may apply.";
                    }

                } else if ($pos2 !== false) {
                    $src = '5615560060'; // IS
                    if (strcmp($from_state, $to_state)) {
                        $text = "Hi " . $leadsphonedata->name . "\n⏳ Last chance to grab a deal on your move!" . $sWhere . "\n📞 Quote The Move: ******-767-2122\n Final discounted slots filling up!\n Reply STOP to unsubscribe.";
                    } else {
                        $text = "Hi " . $leadsphonedata->name . ", We found better quote for your Move to " . ucwords(strtolower($to_city)) . ", " . strtoupper($to_state) . ". Call me at ******-524-2018 to get a quote now. - Tracy from Interstatesmover. Reply STOP to unsubscribe. Msg & data rates may apply.";
                    }

                } else {
                    $currentDate = date("Y-m-d H:i:s");
                    LeadCustomerSmsLog::create([
                        'sms_subject' => 'Lead Confirmation Message (10 Days)',
                        'response' => 'other source lead ==' . $origin->lead_source_name,
                        'lead_id' => $leadsphonedata->lead_id,
                        'created_at' => $currentDate
                    ]);
                    return 'other source lead ==' . $origin->lead_source_name;
                }
                $cronLog->cronlog(4, 'src=' . $src . ', text=' . $text, 'confirmationsmstendayscron');

                // Areawise SMS Code
                if ($pos !== false) {
                    $src = GeneralNumberConfig::getDialNumberByAreacode($leadsphonedata->phone)['vm_areacode_outbound'];
                    $src = $src;
                }

                //FOR Commio API - Start
                $from = $src; //Commio
                // $from = '8886601193'; //Commio
                $to = $leadsphonedata->phone; //Commio
                $body = $text;
                $requestData = array(
                    'from_did' => $from,
                    'to_did' => $to,
                    'message' => $body
                );
                $jsonData = json_encode($requestData, true);

                $str = 'milan:3f7b24833410b9c7682d8c80bcd06442d63d02ad';
                $auth_token = base64_encode($str);

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, 'https://api.thinq.com/account/22374/product/origination/sms/send');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);

                $headers = array();
                $headers[] = 'Content-Type: application/json';
                $headers[] = 'Authorization: Basic ' . $auth_token;
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

                $result = curl_exec($ch);
                if (curl_errno($ch)) {
                    echo 'Error:' . curl_error($ch);
                }
                curl_close($ch);

                $resultData = stripslashes(trim($result));
                $response = json_decode($resultData, true);
                $cronLog->cronlog(4, 'final msg response=' . json_encode($response, true), 'confirmationsmstendayscron');
                //FOR Commio API - END

                date_default_timezone_set('America/New_York');
                $smsLogData = array();
                $smsLogData['from_number'] = $from;
                $smsLogData['to_number'] = $to;
                $smsLogData['lead_type'] = $leadsphonedata->lead_type;
                $smsLogData['sms_subject'] = "Lead Confirmation Message (10 Days)";
                $smsLogData['request'] = $body;
                //$smsLogData['response'] = json_encode([]);
                $smsLogData['response'] = json_encode($response);
                $smsLogData['lead_id'] = $leadsphonedata->lead_id;
                $smsLogData['status'] = "success";
                $smsLogData['created_at'] = date('Y-m-d H:i:s');
                $lead_customer_sms_log_id = LeadCustomerSmsLog::create($smsLogData)->lead_customer_sms_log_id;
                $cronLog->cronlog(3, 'final lead_customer_sms_log_id=' . $lead_customer_sms_log_id, 'confirmationsmstendayscron');
                $leadactiviyobj = new LeadActivity();
                $leadactiviyobj->createActivity($leadId, 2);
                return 'success';
            } else {
                $currentDate = date("Y-m-d H:i:s");
                LeadCustomerSmsLog::create([
                    'sms_subject' => 'Lead Confirmation Message (10 Days)',
                    'response' => 'Lead Id' . $leadsphonedata->lead_id . ' and DNC SMS ' . $leadsphonedata->is_dnc_sms,
                    'lead_id' => $leadsphonedata->lead_id,
                    'created_at' => $currentDate
                ]);
                $cronLog->cronlog(4, 'Lead Id' . $leadsphonedata->lead_id . '==Lead Id== SMS Not send because DNC SMS flag is.==  ' . $checkPhone . "==Date time==" . date("Y-m-d H:i"), 'confirmationsmstendayscron');
                return 'Lead Id' . $leadsphonedata->lead_id . ' and DNC SMS ' . $leadsphonedata->is_dnc_sms;
            }
        } catch (Exception $e) {
            $cronLog->cronlog(3, 'catch= ' . json_encode($e->getMessage(), true), 'confirmationsmstendayscron');
        }
        return 'success';
    }

    public function getFromstateToTimezone($leadfromstate)
    {
        $tz_states = array(
            'America/Anchorage' => array('AK'),
            'America/Boise' => array('ID'),
            'America/Chicago' => array('AL', 'AR', 'IL', 'IA', 'KS', 'LA', 'MN', 'MS', 'MO', 'NE', 'OK', 'SD', 'TN', 'TX', 'WI'),
            'America/Denver' => array('CO', 'MT', 'NM', 'UT', 'WY'),
            'America/Detroit' => array('MI'),
            'America/Indiana/Indianapolis' => array('IN'),
            'America/Kentucky/Louisville' => array('KY'),
            'America/Los_Angeles' => array('CA', 'NV', 'OR', 'WA'),
            'America/New_York' => array('CT', 'DE', 'FL', 'GA', 'ME', 'MD', 'MA', 'NH', 'NJ', 'NY', 'NC', 'OH', 'PA', 'RI', 'SC', 'VT', 'VA', 'DC', 'WV'),
            'America/North_Dakota/Center' => array('ND'),
            'America/Phoenix' => array('AZ'),
            'Pacific/Honolulu' => array('HI')
        );
        foreach ($tz_states as $key => $value) {
            if (in_array($leadfromstate, $value)) {
                return $key;
            }
        }
    }

    function formatPhoneNumber($number) {
        // Remove any non-numeric characters
        $number = preg_replace('/\D/', '', $number);

        // Check if the number has exactly 10 digits
        if (strlen($number) === 10) {
            // Format the number
            $formattedNumber = sprintf('%s-%s-%s',
                substr($number, 0, 3),  // Area code
                substr($number, 3, 3),  // Prefix
                substr($number, 6, 4)   // Line number
            );
            return $formattedNumber;
        } else {
            // Return an error or handle invalid number
            return 'Invalid number';
        }
    }
}