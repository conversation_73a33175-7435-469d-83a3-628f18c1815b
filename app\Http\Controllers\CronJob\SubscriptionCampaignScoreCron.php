<?php

namespace App\Http\Controllers\CronJob;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Businesses\BusinessPaymentController;
use App\Models\Business\Business;
use App\Models\Campaign\Campaign;
use App\Models\Master\MstSubscriptionPlan;
use App\Models\Subscription\Subscription;
use App\Models\DevDebug;
use Illuminate\Support\Facades\DB;
use Exception;

class SubscriptionCampaignScoreCron extends Controller
{
    public function subscriptionCampaignScore()
    {
        try {
            $subscriptionPlanArray  = $businessArray = $businessIdArray = [];

            $businessDetail         = Business::get(['business_id', 'display_name', 'email']);
            if (count($businessDetail) > 0) { foreach($businessDetail as $business) {
                $businessArray[$business->business_id] = $business;
            } }

            $subscriptionPlan       = MstSubscriptionPlan::get();
            for ($p=0; $p<count($subscriptionPlan); $p++) {
                $subscriptionPlanArray[$subscriptionPlan[$p]->subscription_plan_id] = $subscriptionPlan[$p]->subscription_plan;
            }

            $previousSubscription   = Subscription::where('subscription_start', '>=', date('Y-m-01', strtotime('-1 month')))->where('subscription_end', '<=', date('Y-m-t', strtotime('-1 month')))->where('is_active', 'yes')->get(['subscription_id']);
            if (count($previousSubscription) > 0) { foreach($previousSubscription as $subscription) {
                Subscription::where('subscription_id', $subscription->subscription_id)->update([
                    'is_active' => 'no',
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            } }

            $currentSubscription    = Subscription::where('subscription_start', '>=', date('Y-m-01', time()))->where('subscription_end', '<=', date('Y-m-t', time()))->where('is_active', 'yes')->get(['business_id', 'subscription_plan_id']);
            if (count($currentSubscription) > 0) { foreach($currentSubscription as $subscription) {
                if ($subscription->subscription_plan_id == 2) {
                    Campaign::where('business_id', $subscription->business_id)->update([
                        'subscription_score' => 1,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                } else if ($subscription->subscription_plan_id == 3) {
                    Campaign::where('business_id', $subscription->business_id)->update([
                        'subscription_score' => 2,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                }
                $businessIdArray[]  = $subscription->business_id;
            } }

            $removeSubscription     = Subscription::select('subscription.business_id', 'subscription.subscription_plan_id', 'subscription.created_at')
                ->join(DB::raw('(SELECT business_id, MAX(created_at) as latest_created_at FROM subscription WHERE subscription_start >= "'.date('Y-m-01', strtotime('-1 month')).'" AND subscription_end < "' . date('Y-m-t', time()) . '" GROUP BY business_id) as latest'), function($join) {
                    $join->on('subscription.business_id', '=', 'latest.business_id')->on('subscription.created_at', '=', 'latest.latest_created_at');
                })->get();

            if (count($removeSubscription) > 0) { foreach($removeSubscription as $subscription) {
                if (!in_array($subscription->business_id, $businessIdArray)) {
                    Campaign::where('business_id', $subscription->business_id)->update([
                        'subscription_score' => 0,
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);

                    //Added by BK on 09/04/2025 subscription changed email to movers
                    $businessPaymentObj = new BusinessPaymentController();
                    $businessPaymentObj->subscriptionChangedEmailNotificationTemplate($subscription->business_id, $businessArray[$subscription->business_id]->display_name, $businessArray[$subscription->business_id]->email, $subscription->subscription_plan_id, $subscriptionPlanArray[$subscription->subscription_plan_id], 1, $subscriptionPlanArray[1], 1);
                }
            } }

            echo "Success";
        } catch (Exception $e) {
            dd($e->getMessage());
        }
    }
}