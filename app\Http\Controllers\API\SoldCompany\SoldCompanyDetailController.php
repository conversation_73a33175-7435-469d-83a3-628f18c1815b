<?php

namespace App\Http\Controllers\API\SoldCompany;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\Lead\Lead;
use App\Models\Lead\LeadRouting;
use App\Models\Master\MstLeadSource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Exception;
use Illuminate\Support\Facades\Validator;

class SoldCompanyDetailController extends Controller
{

    public function getSoldCompanyDetail(Request $request)
    {
        $responsedata = [];
        $error = "";
        $status = "FAILURE";
        try {
            $elements = json_decode(file_get_contents('php://input'), true);
            if(isset($elements['lead_id'])){
                $lead_id = $elements['lead_id'];
                $findLead = Lead::where('lead_id',$lead_id)->first();
                if(!$findLead){
                    $status = "FAILURE";
                    $error = "Lead not found";
                    $responsedata = [];
                }
                // SELECT bs.business_id as business_id, bs.business_name as business_name,bs.rely_number as rely_number FROM `lead_routing` AS lr LEFT JOIN `lead` as l ON lr.lead_id = l.lead_id LEFT JOIN campaign as cmp ON lr.campaign_id= cmp.campaign_id LEFT JOIN business as bs ON cmp.business_id = bs.business_id WHERE lr.lead_id = 1442;
                $findData = LeadRouting::leftJoin('lead as l', 'lead_routing.lead_id','l.lead_id')
                ->leftJoin('campaign as cmp', 'lead_routing.campaign_id', 'cmp.campaign_id')
                ->leftJoin('business as bs', 'cmp.business_id', 'bs.business_id')
                ->select(
                    'bs.business_id as business_id', 
                    'bs.display_name as business_name',
                    'bs.rely_number as rely_number',
                    'bs.logo as logo'
                )
                ->where('lead_routing.lead_id',$lead_id)
                ->where('lead_routing.route_status','sold')
                ->get();

                $response = $this->getData($findData->toArray());
                if(count($response) > 0){
                    $status = "SUCCESS";
                    $error = "";
                    $responsedata = $response;
                }
                else{
                    $status = "FAILURE";
                    $error = "Data not found by lead id";
                    $responsedata = []; 
                }

            }else if(isset($elements['source_name']) && isset($elements['phone'])){
                $source_name = $elements['source_name'];
                $soruce_origin = MstLeadSource::where('lead_source_name', $source_name)->first();
                if(!$soruce_origin){
                    $status = "FAILURE";
                    $error = "Source data not found";
                    $responsedata = [];
                }
                $phone = preg_replace("/\D/", "", trim($elements['phone']));
                $invalid = strlen($phone) != 10  || preg_match('/^1/', $phone) || preg_match('/^.11/', $phone) || preg_match('/^...1/', $phone) || preg_match('/^....11/', $phone) || preg_match('/^.9/', $phone);
                if ($invalid == true) {
                    $phone = "";
                }

                if (filter_var($phone, FILTER_VALIDATE_REGEXP, array("options" => ["regexp" => "/[1-9]{1}[0-9]{9}/"])) === false) {
                    throw new Exception("Invalid phone.");
                }
                
                $findData = LeadRouting::leftJoin('lead as l', 'lead_routing.lead_id', 'l.lead_id')
                ->leftJoin('campaign as cmp', 'lead_routing.campaign_id', 'cmp.campaign_id')
                ->leftJoin('business as bs', 'cmp.business_id', 'bs.business_id')
                ->select(
                    'bs.business_id as business_id',
                    'bs.business_name as business_name',
                    'bs.rely_number as rely_number',
                    'bs.logo as logo'
                )
                ->where('l.phone', $phone)
                ->where('l.lead_source_id', $soruce_origin->lead_source_id)
                ->where('lead_routing.route_status', 'sold')
                ->orderBy('l.lead_id','DESC')
                ->take(1)
                ->get();
                
                $response = $this->getData($findData->toArray());
                if (count($response) > 0) {
                    $status = "SUCCESS";
                    $error = "";
                    $responsedata = $response;
                } else {
                    $status = "FAILURE";
                    $error = "Data not found by source name and phone number";
                    $responsedata = [];
                }
            }
            else{
                $status = "FAILURE";
                $error = "lead_id or (source and phone) is requested parameter";
                $responsedata = [];
                
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    
        $response = array(
            'status' => $status,
            'data' => $responsedata,
            'error' => $error
        );
        return json_encode($response);    
    }

    public function getData($findData){
        $images_url = Helper::checkServer()['images_url'];
        if (count($findData) > 0) {
            foreach ($findData as $key => $value) {
                # code...
                $responsedata[$key]['business_id'] = $value['business_id'];
                $responsedata[$key]['business_name'] = $value['business_name'];
                $responsedata[$key]['rely_number'] = ($value['rely_number'] != null) ? $value['rely_number'] : "";
                if ($value['logo'] != '' && $value['logo'] != null && file_exists(public_path() . '/images/businesslogo/' . $value['logo'])) {
                    $businesslogo = url('/') . '/images/businesslogo/' . $value['logo'];
                } else {
                    $businesslogo = "";
                }
                $responsedata[$key]['business_logo'] = $businesslogo;
            }
            return $responsedata;
        } else {
            return $responsedata = [];
        }
    }
}