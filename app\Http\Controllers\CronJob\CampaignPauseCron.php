<?php

namespace App\Http\Controllers\CronJob;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Campaign\Campaign;
use App\Models\Campaign\CampaignPause;
use App\Models\Campaign\CampaignUpdateLog;
use App\Helpers\CommonFunctions;
use App\Models\DevDebug;
use DB;
use DateTime;

class CampaignPauseCron extends Controller
{
    public function campaignPause() {
        //current date time
        $currentTime        = date('H:i:00');
        $pauseDetail        = CampaignPause::where('is_pause', '1')->where('pause_time', '>=', $currentTime)->where('pause_time', '<=', $currentTime)->get()->toArray();
        DevDebug::create(['sr_no' => 1, 'result' => 'campaignPause between: ' . $currentTime . ' -- '. $currentTime . ' - ' . json_encode($pauseDetail), 'created_at' => date('Y-m-d H:i:s')]);

        if(count($pauseDetail) > 0) {
            foreach ($pauseDetail as $pause) {
                CampaignPause::where('campaign_id', $pause['campaign_id'])->update([
                    'is_pause' => '0'
                ]);

                Campaign::where('campaign_id', $pause['campaign_id'])->update([
                    'is_active' => 'yes'
                ]);

                $logArray[] = [
                    'created_user_id' => 6,
                    'campaign_id' => $pause['campaign_id'],
                    'field_name' => 'pause_time',
                    'old_value' => 'yes',
                    'new_value' => 'no',
                    'created_at' => date('Y-m-d H:i:s')
                ];

                if (count($logArray) > 0) {
                    CampaignUpdateLog::insert($logArray);
                }
            }
            echo "Success";
        } else {
            echo "No records found";
        }
    }
}
