<?php

namespace App\Http\Controllers\PlivoSms;

use App\Helpers\CommonFunctions;
use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\Business\Business;
use App\Models\Campaign\Campaign;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Models\Lead\Lead;
use App\Models\Lead\LeadCustomerSmsLog;
use App\Models\Lead\LeadRouting;
use App\Models\Master\MstCarType;
use App\Models\Master\MstLeadSource;
use DateTime;
use Exception;
use Illuminate\Http\Request;
use App\Models\Master\MstTimeZone;
use App\Models\Lead\LeadActivity;
use App\Models\Outbound\GeneralNumberConfig;
use App\Models\Master\MstSubscriptionPlan;
use App\Models\Subscription\Subscription;
use App\Models\Outbound\LeadCall;
use Carbon\Carbon;

class ConfirmationSmsNew extends Controller
{
    public function LeadConfirmationSms($leadId, $nightflag, $type = "simple")
    {
        try {
            $leadsemaildata = Lead::with(['moveInfo', 'routingInfoLeadList', 'heavyLiftMoveInfo', 'carTransMoveInfo', 'MstLeadCategory'])->where('lead_id', $leadId)->first();
            // For Do not Send SMS to unVerified Lead Start
            CommonFunctions::createDevDebugLog(1, json_encode($leadsemaildata));
            if ($leadsemaildata->is_verified == "no") {
                CommonFunctions::createDevDebugLog(1, $leadId . '==Lead Id== SMS Not send because lead is unverified.==' . $leadsemaildata->is_verified . "==Date time==" . date("Y-m-d H:i"));
                return 'SMS Not send because lead is unverified.';
            }

            $from_state = '';
            if ($leadsemaildata->lead_category_id == 1) {
                $from_state = $leadsemaildata->moveInfo->from_state;
            }
            if ($leadsemaildata->lead_category_id == 2) {
                $from_state = 'NY';
            }
            if ($leadsemaildata->lead_category_id == 3) {
                $from_state = $leadsemaildata->heavyLiftMoveInfo->from_state;
            }
            if ($leadsemaildata->lead_category_id == 4) {
                $from_state = $leadsemaildata->carTransMoveInfo->from_state;
            }
            if (($from_state == '' || $from_state == null) && $type != 'fb') {
                CommonFunctions::createDevDebugLog(1, $leadId . '==Lead Id== SMS Not send because from state not found.==  ' . $from_state . "==Date time==" . date("Y-m-d H:i"));
                return 'SMS Not send because error in from state.';
            }
            if ($type == 'fb') {
                $from_state = 'NY';
            }

            $timezoneName   = MstTimeZone::where("timezone_id", $leadsemaildata->timezone_id)->first();
            date_default_timezone_set($timezoneName->timezone);
            $hours_check_cron = date('G');
            CommonFunctions::createDevDebugLog(1, 'confirmationSmscron Time' . $hours_check_cron);
            /*if ($hours_check_cron <= 7 || $hours_check_cron >= 21) {
                CommonFunctions::createDevDebugLog(1, 'Night leadid='. $leadId);
                return 'Night time';
            }*/

            $checkPhone = $leadsemaildata->is_dnc_sms;
            if ($checkPhone == "no") {
                $leadsms_flag = LeadCustomerSmsLog::where('lead_id', $leadsemaildata->lead_id)
                    ->where(function ($query) {
                        return $query->where('sms_subject', 'Day Routing Confirmation Message')->orWhere('sms_subject', 'Day Routing Confirmation Message (Cron)');
                    })->where('lead_type', 'normal')->first();

                if ($leadsms_flag) {
                    CommonFunctions::createDevDebugLog(1, 'SMS already send');
                    return 'SMS already send.';
                }
                $origin = MstLeadSource::where('lead_source_id', $leadsemaildata->lead_source_id)->first();
                $src = $text = '';
                $pos = strpos($origin->lead_source_name, 'VM');
                $pos1 = strpos($origin->lead_source_name, 'QTM');
                $pos2 = strpos($origin->lead_source_name, 'IS');

                $exitspot_lead_route = LeadRouting::where('lead_id', $leadsemaildata->lead_id)->where('route_status', 'sold')->get();
                $lead_routing_exist_check = array();
                foreach ($exitspot_lead_route as $leadRvalue) {
                    $lead_routing_exist_check[] = $leadRvalue->campaign_id;
                }

                $smsString = " Reply STOP to unsubscribe. Msg & Data rates may apply.";
                if ($pos !== false) {
                    $src = '+***********';

                    if ($leadsemaildata->sold_type == "exclusive") {
                        $exclusive_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [2,4])->get()->first();
                        if (isset($exclusive_data)) {
                            $businessesdata = Business::where('business_id', $exclusive_data->business_id)->get()->first();

                            if (isset($businessesdata) && $businessesdata->buyer_type_id != 3) {
                                $checkSubscription = $this->checkSubscription($businessesdata->business_id);
                                if ($businessesdata->rely_number != null && $checkSubscription) {
                                    $plivonumber = $this->formatPhoneNumber($businessesdata->rely_number);
                                    $text = 'Your Vanlinesmove.com confirmation # ' . $leadsemaildata->lead_id . '. You Moving quote with ' . $businessesdata->display_name . ' is ready, please call +1 ' . $plivonumber . ' as they are expecting your call.';
                                }
                            }
                        }
                    }

                    if ($leadsemaildata->sold_type == "dual") {
                        $dual_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [1, 6, 8])->pluck('business_id')->toArray();
                        if (isset($dual_data)) {
                            $businessIds = $this->checkSubscriptionBusiness($dual_data);
                            $businessesdata = Business::whereIn('business_id', array_values(array_diff($dual_data, $businessIds)))->get(['display_name', 'rely_number','business_id']);
                            if (isset($businessesdata) && count($businessesdata) > 0) {
                                $strDual = '';
                                $key = 0;
                                foreach ($businessesdata as $value) {
                                    if ($value->rely_number != null) {
                                        if ($key > 0) {
                                            $strDual.= ' and ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                        } else {
                                            $strDual.= $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                        }
                                        $key++;
                                    }
                                }
                                $text = 'Your Vanlinesmove.com confirmation # ' . $leadsemaildata->lead_id . '. Your Moving Quote with ' . $strDual . ' is ready as they are expecting your call.';
                            }
                        }
                    }

                    if ($leadsemaildata->sold_type == "premium" || $leadsemaildata->sold_type == "trio") {
                        $campaign_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [1, 8])->pluck('business_id')->toArray();
                        if (isset($campaign_data)) {
                            $businessIds = $this->checkSubscriptionBusiness($campaign_data);
                            $businessesdata = Business::whereIn('business_id', array_values(array_diff($campaign_data, $businessIds)))->whereNotNull('rely_number')->get(['display_name', 'rely_number']);
                            if (isset($businessesdata) && count($businessesdata) > 0) {
                                $sWhere = '';
                                $key = 0;
                                foreach ($businessesdata as $value) {
                                    if (count($businessesdata) == 4) {
                                        /*if ($key > 0) {
                                            if ($key == 3) {
                                                $sWhere.= ' and ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            } else {
                                                $sWhere.= ', ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            }
                                        } else {*/
                                            $sWhere.= "\n📞 " . $value->display_name . ": +1 " . $this->formatPhoneNumber($value->rely_number);
                                        /*}*/
                                    } else if (count($businessesdata) == 3) {
                                        /*if ($key > 0) {
                                            if ($key == 2) {
                                                $sWhere.= ' and ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            } else {
                                                $sWhere.= ', ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            }
                                        } else {*/
                                            $sWhere.= "\n📞 " . $value->display_name . ": +1 " . $this->formatPhoneNumber($value->rely_number);
                                        /*}*/
                                    } else if (count($businessesdata) == 2) {
                                        if ($key > 0) {
                                            $sWhere.= "\n📞 " . $value->display_name . ": +1 " . $this->formatPhoneNumber($value->rely_number) . "\n📞 Interstate Mover: ******-566-3184";
                                        } else {
                                            $sWhere.= "\n📞 " . $value->display_name . ": +1 " . $this->formatPhoneNumber($value->rely_number);
                                        }
                                    } else if (count($businessesdata) == 1) {
                                        $sWhere.= "\n📞 " . $value->display_name.": +1 " . $this->formatPhoneNumber($value->rely_number). "\n📞 Quote The Move: ******-767-2122\n📞 Interstate Mover: ******-566-3184";
                                    }
                                    $key++;
                                }
                                $text = "Hi " . $leadsemaildata->name . ",\n\nWe got your moving request! 🚛 These movers have your details and are ready to offer quotes:\n" . $sWhere . "\n\nCall now for the best rates! 📦\n\nReply STOP to opt out.";
                            }
                        }
                    }
                } else if ($pos1 !== false) {
                    $src = '+***********';

                    if ($leadsemaildata->sold_type == "exclusive") {
                        $exclusive_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [2,4])->get()->first();
                        if (isset($exclusive_data)) {
                            $businessesdata = Business::where('business_id', $exclusive_data->business_id)->get()->first();
                            if (isset($businessesdata) && $businessesdata->buyer_type_id != 3) {
                                $checkSubscription = $this->checkSubscription($businessesdata->business_id);
                                if ($businessesdata->rely_number != null && $checkSubscription) {
                                    $plivonumber = $this->formatPhoneNumber($businessesdata->rely_number);
                                    $text = 'Your Quotethemove.com confirmation # ' . $leadsemaildata->lead_id . '. You Moving quote with ' . $businessesdata->display_name . ' is ready, please call +1 ' . $plivonumber . ' as they are expecting your call.';
                                }
                            }
                        }
                    }

                    if ($leadsemaildata->sold_type == "dual") {
                        $dual_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [1, 6, 8])->pluck('business_id')->toArray();
                        if (isset($dual_data)) {
                            $businessIds = $this->checkSubscriptionBusiness($dual_data);
                            $businessesdata = Business::whereIn('business_id', array_values(array_diff($dual_data, $businessIds)))->get(['display_name', 'rely_number','business_id']);
                            if (isset($businessesdata) && count($businessesdata) > 0) {
                                $strDual = '';
                                $key = 0;
                                foreach ($businessesdata as $value) {
                                    if ($value->rely_number != null) {
                                        if ($key > 0) {
                                            $strDual.= ' and ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                        } else {
                                            $strDual.= $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                        }
                                        $key++;
                                    }
                                }
                                $text = 'Your Quotethemove.com confirmation # ' . $leadsemaildata->lead_id . '. Your Moving Quote with ' . $strDual . ' is ready as they are expecting your call.';
                            }
                        }
                    }

                    if ($leadsemaildata->sold_type == "premium" || $leadsemaildata->sold_type == "trio") {
                        $campaign_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [1, 8])->pluck('business_id')->toArray();
                        if (isset($campaign_data)) {
                            $businessIds = $this->checkSubscriptionBusiness($campaign_data);
                            $businessesdata = Business::whereIn('business_id', array_values(array_diff($campaign_data, $businessIds)))->whereNotNull('rely_number')->get(['display_name', 'rely_number']);
                            if (isset($businessesdata) && count($businessesdata) > 0) {
                                $sWhere = '';
                                $key = 0;
                                foreach ($businessesdata as $value) {
                                    if (count($businessesdata) == 4) {
                                        /*if ($key > 0) {
                                            if ($key == 3) {
                                                $sWhere.= ' and ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            } else {
                                                $sWhere.= ', ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            }
                                        } else {*/
                                            $sWhere.= "\n📞 " . $value->display_name . ": +1 " . $this->formatPhoneNumber($value->rely_number);
                                        /*}*/
                                    } else if (count($businessesdata) == 3) {
                                        /*if ($key > 0) {
                                            if ($key == 2) {
                                                $sWhere.= ' and ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            } else {
                                                $sWhere.= ', ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            }
                                        } else {*/
                                            $sWhere.= "\n📞 " . $value->display_name . ": +1 " . $this->formatPhoneNumber($value->rely_number);
                                        /*}*/
                                    } else if (count($businessesdata) == 2) {
                                        if ($key > 0) {
                                            $sWhere.= "\n📞 " . $value->display_name . ": +1 " . $this->formatPhoneNumber($value->rely_number) . "\n📞 Van Lines Move, ******-492-6496";
                                        } else {
                                            $sWhere.= "\n📞 " . $value->display_name . ": +1 " . $this->formatPhoneNumber($value->rely_number);
                                        }
                                    } else if (count($businessesdata) == 1) {
                                        $sWhere.= "\n📞 " . $value->display_name.": +1 " . $this->formatPhoneNumber($value->rely_number). "\n📞 Van Lines Move: ******-492-6496\n📞 Interstate Mover: ******-566-3184";
                                    }
                                    $key++;
                                }
                                $text = "Hi " . $leadsemaildata->name . ",\n\nWe got your moving request! 🚛 These movers have your details and are ready to offer quotes:\n" . $sWhere . "\n\nCall now for the best rates! 📦\n\nReply STOP to opt out.";
                            }
                        }
                    }
                } else if ($pos2 !== false) {
                    $src = '+***********';
                    if ($leadsemaildata->sold_type == "exclusive") {
                        $exclusive_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [2,4])->get()->first();
                        if (isset($exclusive_data)) {
                            $businessesdata = Business::where('business_id', $exclusive_data->business_id)->get()->first();
                            if (isset($businessesdata) && $businessesdata->buyer_type_id != 3) {
                                $checkSubscription = $this->checkSubscription($businessesdata->business_id);
                                if ($businessesdata->rely_number != null && $checkSubscription) {
                                    $plivonumber = $this->formatPhoneNumber($businessesdata->rely_number);
                                    $text = 'Your InterstatesMover.com confirmation # ' . $leadsemaildata->lead_id . '. You Moving quote with ' . $businessesdata->display_name . ' is ready, please call +1 ' . $plivonumber . ' as they are expecting your call.';
                                }
                            }
                        }
                    }

                    if ($leadsemaildata->sold_type == "dual") {
                        $dual_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [1, 6, 8])->pluck('business_id')->toArray();
                        if (isset($dual_data)) {
                            $businessIds = $this->checkSubscriptionBusiness($dual_data);
                            $businessesdata = Business::whereIn('business_id', array_values(array_diff($dual_data, $businessIds)))->get(['display_name', 'rely_number','business_id']);
                            if (isset($businessesdata) && count($businessesdata) > 0) {
                                $strDual = '';
                                $key = 0;
                                foreach ($businessesdata as $value) {
                                    if ($value->rely_number != null) {
                                        if ($key > 0) {
                                            $strDual.= ' and ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                        } else {
                                            $strDual.= $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                        }
                                        $key++;
                                    }
                                }
                                $text = 'Your InterstatesMover.com confirmation # ' . $leadsemaildata->lead_id . '. Your Moving Quote with ' . $strDual . ' is ready as they are expecting your call.';
                            }
                        }
                    }

                    if ($leadsemaildata->sold_type == "premium" || $leadsemaildata->sold_type == "trio") {
                        $campaign_data = Campaign::whereIn('campaign_id', $lead_routing_exist_check)->whereIn('campaign_type_id', [1, 8])->pluck('business_id')->toArray();
                        if (isset($campaign_data)) {
                            $businessIds = $this->checkSubscriptionBusiness($campaign_data);
                            $businessesdata = Business::whereIn('business_id', array_values(array_diff($campaign_data, $businessIds)))->whereNotNull('rely_number')->get(['display_name', 'rely_number']);
                            if (isset($businessesdata) && count($businessesdata) > 0) {
                                $sWhere = '';
                                $key = 0;
                                foreach ($businessesdata as $value) {
                                    if (count($businessesdata) == 4) {
                                        /*if ($key > 0) {
                                            if ($key == 3) {
                                                $sWhere.= ' and ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            } else {
                                                $sWhere.= ', ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            }
                                        } else {*/
                                            $sWhere.= "\n📞 " . $value->display_name . ": +1 " . $this->formatPhoneNumber($value->rely_number);
                                        /*}*/
                                    } else if (count($businessesdata) == 3) {
                                        /*if ($key > 0) {
                                            if ($key == 2) {
                                                $sWhere.= ' and ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            } else {
                                                $sWhere.= ', ' . $value->display_name.', +1 '.$this->formatPhoneNumber($value->rely_number);
                                            }
                                        } else {*/
                                            $sWhere.= "\n📞 " . $value->display_name . ": +1 " . $this->formatPhoneNumber($value->rely_number);
                                        /*}*/
                                    } else if (count($businessesdata) == 2) {
                                        if ($key > 0) {
                                            $sWhere.= "\n📞 " . $value->display_name . ": +1 " . $this->formatPhoneNumber($value->rely_number) . "\n📞 Van Lines Move, ******-492-6496";
                                        } else {
                                            $sWhere.= "\n📞 " . $value->display_name . ": +1 " . $this->formatPhoneNumber($value->rely_number);
                                        }
                                    } else if (count($businessesdata) == 1) {
                                        $sWhere.= "\n📞 " . $value->display_name.": +1 " . $this->formatPhoneNumber($value->rely_number). "\n📞 Van Lines Move: ******-492-6496\n📞 Quote The Move: ******-767-2122";
                                    }
                                    $key++;
                                }
                                $text = "Hi " . $leadsemaildata->name . ",\n\nWe got your moving request! 🚛 These movers have your details and are ready to offer quotes:\n" . $sWhere . "\n\nCall now for the best rates! 📦\n\nReply STOP to opt out.";
                            }
                        }
                    }
                } else {
                    date_default_timezone_set('America/New_York');
                    $currentDate = date("Y-m-d H:i:s");
                    LeadCustomerSmsLog::create([
                        'sms_subject' => ($type == 'cron') ? "Day Routing Confirmation Message (Cron)" : "Day Routing Confirmation Message",
                        'response' => 'other source lead ==' . $origin->lead_source_name,
                        'lead_id' => $leadsemaildata->lead_id,
                        'created_at' => $currentDate
                    ]);
                    return 'other source lead ==' . $origin->lead_source_name;
                }

                if (empty($text)) {
                    date_default_timezone_set('America/New_York');
                    LeadCustomerSmsLog::create([
                        'sms_subject' => ($type == 'cron') ? "Day Routing Confirmation Message (Cron)" : "Day Routing Confirmation Message",
                        'response' => 'Lead Id' . $leadsemaildata->lead_id . ' and No any subscription plan',
                        'lead_id' => $leadsemaildata->lead_id,
                        'created_at' => date("Y-m-d H:i:s")
                    ]);
                    return 'SMS not send';
                }

                // Areawise SMS Code
                if ($pos !== false) {
                    $src = GeneralNumberConfig::getDialNumberByAreacode($leadsemaildata->phone)['vm_areacode_outbound'];
                    $src = '+'.$src;
                }

                // For Custom Encrypt and Decrypt Method Start
                $endDecObj = new Helper;
                $general_variable_3 = Helper::checkServer()['general_variable_3'];
                $general_variable_4 = Helper::checkServer()['general_variable_4'];
                $decVariable3 = $endDecObj->customeDecryption($general_variable_3);
                $decVariable4 = $endDecObj->customeDecryption($general_variable_4);
                // For Custom Encrypt and Decrypt Method End
                $to = '+1' . $leadsemaildata->phone;
                $from = $src;
                if ($text != "" && ($leadsemaildata->sold_type == "exclusive" || $leadsemaildata->sold_type == "dual")) {
                    $text .= " " . $smsString;
                }
                $body = $text;
                // $uri = 'https://api.thinq.com/account/22374/product/origination/sms/send';
                $str = 'milan:3f7b24833410b9c7682d8c80bcd06442d63d02ad';
                $auth_token = base64_encode($str);
                // post string (phone number format= +*********** ), case matters
                // $fields = '&To=' . urlencode($to) .
                //     '&From=' . urlencode($from) .
                //     '&Body=' . urlencode($body);

                $fields =    array(
                    'from_did' => $from,
                    'to_did' => $to,
                    'message' => $body
                );

                // $sms_response = $endDecObj->sms_validation($to, $decVariable3, $decVariable4); //Added By HJ On 18-01-2022 For Check SMS Number Valid Or Not
                // $request = array("To" => $to, "From" => $from, "Body" => $body, "File" => "ConfirmationSms");
                // $endDecObj->createEmailSMSValidationLog($leadId, 0, 10, json_encode($request), json_encode($sms_response), 1, 0); //Added By HJ On 18-01-2022 For Create Email and SMS Validation Log
                // if (isset($sms_response['status']) && $sms_response['status'] > 0) {
                $jsonPostData = json_encode($fields, true);
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, 'https://api.thinq.com/account/22374/product/origination/sms/send');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonPostData);

                $headers = array();
                $headers[] = 'Content-Type: application/json';
                $headers[] = 'Authorization: Basic ' . $auth_token;
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

                if (curl_errno($ch)) {
                    echo 'Error:' . curl_error($ch);
                }

                $result = curl_exec($ch);
                curl_close($ch);
                $response = json_decode($result);
                //DB::table('test')->insert(array('result' => $leadsemaildata->id . '-' . $result));
                $response = json_decode($result);
                date_default_timezone_set('America/New_York');
                $smsLogData = array();
                $smsLogData['lead_id'] = $leadsemaildata->lead_id;
                $smsLogData['from_number'] = $from;
                $smsLogData['to_number'] = $to;
                $smsLogData['lead_type'] = 'normal';
                $smsLogData['sms_subject'] = ($type == 'cron') ? "Day Routing Confirmation Message (Cron)" : "Day Routing Confirmation Message";
                $smsLogData['request'] = $body;
                // $smsLogData['response'] = json_encode([]);
                $smsLogData['response'] = json_encode($response);
                $smsLogData['created_at'] = date("Y-m-d H:i:s");
                // if ($response->status == 'queued') {
                //     $smsLogData['status'] = 'success';
                // }
                $smsLogData['status'] = "success";
                LeadCustomerSmsLog::create($smsLogData);
                $leadactiviyobj = new LeadActivity();
                $leadactiviyobj->createActivity($leadId, 2);
                CommonFunctions::createDevDebugLog(1, $leadId . ' Success ' . date("Y-m-d H:i"));
            } else {
                date_default_timezone_set('America/New_York');
                $currentDate = date("Y-m-d H:i:s");
                LeadCustomerSmsLog::create([
                    'sms_subject' => ($type == 'cron') ? "Day Routing Confirmation Message (Cron)" : "Day Routing Confirmation Message",
                    'response' => 'Lead Id' . $leadsemaildata->lead_id . ' and DNC SMS ' . $leadsemaildata->is_dnc_sms,
                    'lead_id' => $leadsemaildata->lead_id,
                    'created_at' => $currentDate
                ]);
                CommonFunctions::createDevDebugLog(1, $leadId . '==Lead Id== SMS Not send because DNC SMS flag is.==  ' . $checkPhone . "==Date time==" . date("Y-m-d H:i"));
                return 'SMS not send because DNC SMS flag is ' . $checkPhone;
            }
            return 'success';
        } catch (Exception $ex) {
            CommonFunctions::createDevDebugLog(1, $ex);
        }
    }

    public function getFromstateToTimezone($leadfromstate)
    {
        try {
            $tz_states = array(
                'America/Anchorage' => array('AK'),
                'America/Boise' => array('ID'),
                'America/Chicago' => array('AL', 'AR', 'IL', 'IA', 'KS', 'LA', 'MN', 'MS', 'MO', 'NE', 'OK', 'SD', 'TN', 'TX', 'WI'),
                'America/Denver' => array('CO', 'MT', 'NM', 'UT', 'WY'),
                'America/Detroit' => array('MI'),
                'America/Indiana/Indianapolis' => array('IN'),
                'America/Kentucky/Louisville' => array('KY'),
                'America/Los_Angeles' => array('CA', 'NV', 'OR', 'WA'),
                'America/New_York' => array('CT', 'DE', 'FL', 'GA', 'ME', 'MD', 'MA', 'NH', 'NJ', 'NY', 'NC', 'OH', 'PA', 'RI', 'SC', 'VT', 'VA', 'DC', 'WV'),
                'America/North_Dakota/Center' => array('ND'),
                'America/Phoenix' => array('AZ'),
                'Pacific/Honolulu' => array('HI'),
            );
            foreach ($tz_states as $key => $value) {
                if (in_array($leadfromstate, $value)) {
                    return $key;
                }
            }
        } catch (Exception $ex) {
            Log::info($ex);
        }
    }

    function formatPhoneNumber($number) {
        // Remove any non-numeric characters
        $number = preg_replace('/\D/', '', $number);

        // Check if the number has exactly 10 digits
        if (strlen($number) === 10) {
            // Format the number
            $formattedNumber = sprintf('%s-%s-%s',
                substr($number, 0, 3),  // Area code
                substr($number, 3, 3),  // Prefix
                substr($number, 6, 4)   // Line number
            );
            return $formattedNumber;
        } else {
            // Return an error or handle invalid number
            return 'Invalid number';
        }
    }

    public function checkSubscription($businessId) {
        $subscriptionPlan = Subscription::where('business_id', $businessId)
                            ->where('is_active', 'yes')
                            ->first();
        if($subscriptionPlan) {
            $planDetails = MstSubscriptionPlan::where('subscription_plan_id', $subscriptionPlan->subscription_plan_id)->first();
            if($planDetails) {
                if($planDetails->subscription_plan_id == 2) {
                    $callCountN = $planDetails->call_count;
                    if($callCountN > 0) {
                        $startOfMonth = Carbon::now()->startOfMonth();
                        $endOfMonth = Carbon::now()->endOfMonth();
                        $leadCallCount = LeadCall::where('transfer_type', 'businessrely')
                        ->where('business_id', $businessId)
                        ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
                        ->count();

                        if($leadCallCount >= $callCountN) {
                           return 0;
                        }
                    }
                }
            }
        } else {
            return 0;
        }

        return 1;
    }

    public function checkSubscriptionBusiness($businessIds) {
        $businessIdsArray       = [];
        $businessData           = Business::whereIn('business_id', $businessIds)->whereNotNull('rely_number')->get(['business_id', 'display_name', 'rely_number']);
        foreach ($businessData as $value) {
            $subscriptionPlan   = Subscription::where('business_id', $value->business_id)->where('is_active', 'yes')->first();
            if ($subscriptionPlan) {
                $planDetails    = MstSubscriptionPlan::where('subscription_plan_id', $subscriptionPlan->subscription_plan_id)->first();
                if ($planDetails) {
                    if ($planDetails->subscription_plan_id == 2) {
                        $callCountN = $planDetails->call_count;
                        if ($callCountN > 0) {
                            $startOfMonth = Carbon::now()->startOfMonth();
                            $endOfMonth = Carbon::now()->endOfMonth();
                            $leadCallCount = LeadCall::where('transfer_type', 'businessrely')
                                ->where('business_id', $value->business_id)
                                ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
                                ->count();

                            if ($leadCallCount >= $callCountN) {
                                $businessIdsArray[] = $value->business_id;
                            }
                        }
                    }
                }
            } else {
                $businessIdsArray[] = $value->business_id;
            }
        }

        return $businessIdsArray;
    }
}
