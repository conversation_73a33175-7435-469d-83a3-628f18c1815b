<?php

namespace App\Http\Controllers\GoogleAdwords;

use App\Models\Campaign\Campaign;
use App\Models\Lead\LeadRouting;
use App\Models\Lead\LeadTransferCall;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\GoogleAdwords\GoogleAdwardsOfflineConversion;
use App\Models\Lead\Lead;
use App\Models\Outbound\LeadCall;
use App\Models\Lead\LeadCustomerReview;

//use GetOpt\GetOpt;
use Google\Ads\GoogleAds\Examples\Utils\ArgumentNames;
use Google\Ads\GoogleAds\Examples\Utils\ArgumentParser;
use Google\Ads\GoogleAds\Lib\OAuth2TokenBuilder;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClient;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClientBuilder;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsException;
use Google\Ads\GoogleAds\Util\V18\ResourceNames;
use Google\Ads\GoogleAds\V18\Common\Consent;
use Google\Ads\GoogleAds\V18\Common\OfflineUserAddressInfo;
use Google\Ads\GoogleAds\V18\Common\UserIdentifier;
use Google\Ads\GoogleAds\V18\Enums\ConsentStatusEnum\ConsentStatus;
use Google\Ads\GoogleAds\V18\Enums\UserIdentifierSourceEnum\UserIdentifierSource;
use Google\Ads\GoogleAds\V18\Errors\GoogleAdsError;
use Google\Ads\GoogleAds\V18\Services\ClickConversion;
use Google\Ads\GoogleAds\V18\Services\ClickConversionResult;
use Google\Ads\GoogleAds\V18\Services\UploadClickConversionsRequest;
use Google\ApiCore\ApiException;
use DateTime;
use DB;
use Exception;
use Helper;
use Log;

class GoogleAdwordsOfflineEnhancedConversionNewController extends Controller
{
    public static $CUSTOMER_ID;
    public static $CONVERSION_ACTION_ID;
    public static $CONVERSION_DATE_TIME;
    public static $CONVERSION_VALUE;
    public static $EMAIL;
    public static $PHONE;

    public static $ORDER_ID;
    public static $GCLID;
    public static $GBRAID;
    public static $WBRAID;
    public static $AD_USER_DATA_CONSENT;

    public static function main()
    {
        // .ini file path for PMM, MT
        $filePath = base_path("google_ads_php.ini");

        // Generate a refreshable OAuth2 credential for authentication.
        $oAuth2Credential = (new OAuth2TokenBuilder())->fromFile($filePath)->build();

        // Construct a Google Ads client configured from a properties file and the
        // OAuth2 credentials above.
        $googleAdsClient = (new GoogleAdsClientBuilder())
            ->fromFile($filePath)
            ->withOAuth2Credential($oAuth2Credential)
            ->build();


        try {
            Log::info('Offline Conversion:- 1');
            return self::runExample(
                $googleAdsClient,
                GoogleAdwordsOfflineEnhancedConversionNewController::$CUSTOMER_ID,
                GoogleAdwordsOfflineEnhancedConversionNewController::$CONVERSION_ACTION_ID,
                GoogleAdwordsOfflineEnhancedConversionNewController::$CONVERSION_DATE_TIME,
                GoogleAdwordsOfflineEnhancedConversionNewController::$CONVERSION_VALUE,
                GoogleAdwordsOfflineEnhancedConversionNewController::$EMAIL,
                GoogleAdwordsOfflineEnhancedConversionNewController::$PHONE,
                GoogleAdwordsOfflineEnhancedConversionNewController::$ORDER_ID,
                GoogleAdwordsOfflineEnhancedConversionNewController::$GCLID,
                GoogleAdwordsOfflineEnhancedConversionNewController::$GBRAID,
                GoogleAdwordsOfflineEnhancedConversionNewController::$WBRAID,
                GoogleAdwordsOfflineEnhancedConversionNewController::$AD_USER_DATA_CONSENT
            );
        } catch (GoogleAdsException $googleAdsException) {
            Log::info('Offline Conversion:- 2');
            printf(
                "Request with ID '%s' has failed.%sGoogle Ads failure details:%s",
                $googleAdsException->getRequestId(),
                PHP_EOL,
                PHP_EOL
            );
            foreach ($googleAdsException->getGoogleAdsFailure()->getErrors() as $error) {
                /** @var GoogleAdsError $error */
                printf(
                    "\t%s: %s%s",
                    $error->getErrorCode()->getErrorCode(),
                    $error->getMessage(),
                    PHP_EOL
                );
            }
            //exit(1);
        } catch (ApiException $apiException) {
            Log::info('Offline Conversion:- 3');
            printf(
                "ApiException was thrown with message '%s'.%s",
                $apiException->getMessage(),
                PHP_EOL
            );
            //exit(1);
        }
    }

    /**
     * Runs the example.
     *
     * @param GoogleAdsClient $googleAdsClient the Google Ads API client
     * @param int $customerId the customer ID
     * @param int $conversionActionId the ID of the conversion action associated with this
     *      conversion
     * @param string $conversionDateTime the date and time of the conversion
     *      The format is "yyyy-mm-dd hh:mm:ss+|-hh:mm", e.g. “2019-01-01 12:32:45-08:00”
     * @param float $conversionValue the value of the conversion
     * @param string|null $orderId the unique order ID (transaction ID) of the conversion
     * @param string|null $gclid the Google click ID of the conversion
     * @param int|null $adUserDataConsent the ad user data consent for the click
     */
    public static function runExample(GoogleAdsClient $googleAdsClient, $customerId, $conversionActionId, $conversionDateTime, $conversionValue, $email, $phone, $orderid, $gclid, $gbraid, $wbraid, $adUserDataConsent) {
        // [START add_user_identifiers]
        // Creates a click conversion with the specified attributes.
        $clickConversion = new ClickConversion();

        // Extract user email and phone from the raw data, normalize and hash it, then wrap it in
        // UserIdentifier objects. Creates a separate UserIdentifier object for each.
        // The data in this example is hardcoded, but in your application you might read the raw
        // data from an input file.

        // IMPORTANT: Since the identifier attribute of UserIdentifier
        // (https://developers.google.com/google-ads/api/reference/rpc/latest/UserIdentifier) is a
        // oneof
        // (https://protobuf.dev/programming-guides/proto3/#oneof-features), you must set only ONE
        // of hashedEmail, hashedPhoneNumber, mobileId, thirdPartyUserId, or addressInfo. Setting
        // more than one of these attributes on the same UserIdentifier will clear all the other
        // members of the oneof. For example, the following code is INCORRECT and will result in a
        // UserIdentifier with ONLY a hashedPhoneNumber.
        //
        // $incorrectlyPopulatedUserIdentifier = new UserIdentifier([
        //    'hashed_email' => '...',
        //    'hashed_phone_number' => '...'
        // ]);

        try {
            // Check if the number has exactly 10 digits
            $formatted = $phone;
            if (preg_match('/^(\d{3})(\d{3})(\d{4})$/', $phone, $matches)) {
                $formatted = "+1 {$matches[1]} {$matches[2]}{$matches[3]}";
            }

            //Log::info('Offline Conversion:- 4');
            $rawRecord = [
                // Email address that includes a period (.) before the Gmail domain.
                'email' => $email, //'<EMAIL>',
                // Phone number to be converted to E.164 format, with a leading '+' as required.
                'phone' => $formatted,
                // This example lets you input conversion details as arguments, but in reality you might
                // store this data alongside other user data, so we include it in this sample user
                // record.
                'orderId' => $orderid,
                'gclid' => $gclid,
                'conversionActionId' => $conversionActionId,
                'conversionDateTime' => $conversionDateTime,
                'conversionValue' => $conversionValue,
                'currencyCode' => 'USD',
                'adUserDataConsent' => $adUserDataConsent
            ];
            Log::info('Offline Conversion:- 4 -' . json_encode($rawRecord));

            // Creates a list for the user identifiers.
            $userIdentifiers = [];

            // Uses the SHA-256 hash algorithm for hashing user identifiers in a privacy-safe way, as
            // described at https://support.google.com/google-ads/answer/9888656.
            $hashAlgorithm = "sha256";

            // Creates a user identifier using the hashed email address, using the normalize and hash
            // method specifically for email addresses.
            $emailIdentifier = new UserIdentifier([
                // Uses the normalize and hash method specifically for email addresses.
                'hashed_email' => self::normalizeAndHashEmailAddress(
                    $hashAlgorithm,
                    $rawRecord['email']
                ),
                // Optional: Specifies the user identifier source.
                //'user_identifier_source' => UserIdentifierSource::FIRST_PARTY
            ]);
            $userIdentifiers[] = $emailIdentifier;

            // Checks if the record has a phone number, and if so, adds a UserIdentifier for it.
            if (array_key_exists('phone', $rawRecord)) {
                $hashedPhoneNumberIdentifier = new UserIdentifier([
                    'hashed_phone_number' => self::normalizeAndHash(
                        $hashAlgorithm,
                        $rawRecord['phone'],
                        true
                    )
                ]);
                // Adds the hashed email identifier to the user identifiers list.
                $userIdentifiers[] = $hashedPhoneNumberIdentifier;
            }

            // Adds the user identifiers to the conversion.
            $clickConversion->setUserIdentifiers($userIdentifiers);
            // [END add_user_identifiers]

            // [START add_conversion_details]
            // Adds details of the conversion.
            $clickConversion->setConversionAction(
                ResourceNames::forConversionAction($customerId, $rawRecord['conversionActionId'])
            );
            $clickConversion->setConversionDateTime($rawRecord['conversionDateTime']);
            $clickConversion->setConversionValue($rawRecord['conversionValue']);
            $clickConversion->setCurrencyCode($rawRecord['currencyCode']);

            // Sets the order ID if provided.
            if (!empty($rawRecord['orderId'])) {
                $clickConversion->setOrderId($rawRecord['orderId']);
            }

            // Sets the Google click ID (gclid) if provided.
            /*if (!empty($rawRecord['gclid'])) {
                $clickConversion->setGclid($rawRecord['gclid']);
            }*/
            if (!is_null($gclid)) {
                $clickConversion->setGclid($gclid);
            } /*elseif (!is_null($gbraid)) {
                $clickConversion->setGbraid($gbraid);
            } elseif (!is_null($wbraid)) {
                $clickConversion->setWbraid($wbraid);
            }*/

            // Sets the ad user data consent if provided.
            if (!empty($rawRecord['adUserDataConsent'])) {
                // Specifies whether user consent was obtained for the data you are uploading. See
                // https://www.google.com/about/company/user-consent-policy for details.
                $clickConversion->setConsent(
                    new Consent(['ad_user_data' => $rawRecord['adUserDataConsent']])
                );
            }
            // [END add_conversion_details]

            // [START upload_conversion]
            // Issues a request to upload the click conversion.
            $conversionUploadServiceClient = $googleAdsClient->getConversionUploadServiceClient();
            // NOTE: This request contains a single conversion as a demonstration.  However, if you have
            // multiple conversions to upload, it's best to upload multiple conversions per request
            // instead of sending a separate request per conversion. See the following for per-request
            // limits:
            // https://developers.google.com/google-ads/api/docs/best-practices/quotas#conversion_upload_service
            $response = $conversionUploadServiceClient->uploadClickConversions(
            // Enables partial failure (must be true).
                UploadClickConversionsRequest::build($customerId, [$clickConversion], true)
            );
            // [END upload_conversion]

            // Prints the status message if any partial failure error is returned.
            // Note: The details of each partial failure error are not printed here, you can refer to
            // the example HandlePartialFailure.php to learn more.
            // To review the overall health of your recent uploads, see:
            // https://developers.google.com/google-ads/api/docs/conversions/upload-summaries
            if ($response->getPartialFailureError() !== null) {
                $responseResult = json_encode(
                    array(
                        'api_response' => $response,
                        'api_success' => 0 ,
                        'conversion_value' => '',
                        'email' => '',
                        'phone' => '',
                        'gclid' => '',
                        'gbraid' => '',
                        'wbraid' => '',
                        'conversion_name' => '',
                        'error' => 1,
                        'error_msg' => $response->getPartialFailureError()->getMessage()
                    )
                );

                Log::info('Offline Conversion:- Partial Failure');
                //print_r($responseResult);
                return $responseResult;
            } else {
                // Prints the result if exists.

                /** @var ClickConversionResult $uploadedClickConversion */
                $success = 1;
                $responseResult = json_encode(
                    array(
                        'conversion_value' => $conversionValue,
                        'api_response' => $response,
                        'api_success' => $success,
                        'email' => $email,
                        'phone' => $phone,
                        'gclid' => $gclid,
                        'gbraid' => $gbraid,
                        'wbraid' => $wbraid,
                        'conversion_name' => '',
                        'error' => 0,
                        'error_msg' => ''
                    )
                );
                Log::info('Offline Conversion:- Success');
                //print_r($responseResult);
                return $responseResult;
            }
        } catch (ApiException $apiException) {
            $responseResult = json_encode(
                array(
                    'api_response' => '',
                    'api_success' => 0 ,
                    'conversion_value' => '',
                    'gclid' => '',
                    'gbraid' => '',
                    'wbraid' => '',
                    'conversion_name' => '',
                    'error' => 1,
                    'error_msg' => $apiException->getMessage()
                )
            );
            Log::info('New Offline Conversion:- Failure');
            //print_r($responseResult);
            return $responseResult;
        }
    }

    /**
     * Returns the result of normalizing and then hashing the string using the provided hash
     * algorithm. Private customer data must be hashed during upload, as described at
     * https://support.google.com/google-ads/answer/7474263.
     *
     * @param string $hashAlgorithm the hash algorithm to use
     * @param string $value the value to normalize and hash
     * @return string the normalized and hashed value
     */
    // [START normalize_and_hash]
    private static function normalizeAndHash(string $hashAlgorithm, string $value): string
    {
        // Normalizes by first converting all characters to lowercase, then trimming spaces.
        $normalized = strtolower($value);
        // Removes leading, trailing, and intermediate spaces.
        $normalized = str_replace(' ', '', $normalized);
        return hash($hashAlgorithm, strtolower(trim($normalized)));
    }

    /**
     * Returns the result of normalizing and hashing an email address. For this use case, Google
     * Ads requires removal of any '.' characters preceding "gmail.com" or "googlemail.com".
     *
     * @param string $hashAlgorithm the hash algorithm to use
     * @param string $emailAddress the email address to normalize and hash
     * @return string the normalized and hashed email address
     */
    private static function normalizeAndHashEmailAddress(
        string $hashAlgorithm,
        string $emailAddress
    ): string {
        $normalizedEmail = strtolower($emailAddress);
        $emailParts = explode("@", $normalizedEmail);
        if (
            count($emailParts) > 1
            && preg_match('/^(gmail|googlemail)\.com\s*/', $emailParts[1])
        ) {
            // Removes any '.' characters from the portion of the email address before the domain
            // if the domain is gmail.com or googlemail.com.
            $emailParts[0] = str_replace(".", "", $emailParts[0]);
            $normalizedEmail = sprintf('%s@%s', $emailParts[0], $emailParts[1]);
        }
        return self::normalizeAndHash($hashAlgorithm, $normalizedEmail);
    }
    // [END normalize_and_hash]

    public function getOfflineConversion($originName, $date, $flag) {
        //echo $originName; die;
        Log::info('Offline Conversion Start Date:- ' . $date);
        if($originName == 'VM') {
            $originId = array(19, 40, 51, 15, 55, 53); //mst_lead_source table id
        } else if($originName == 'QTM') {
            $originId = array(14, 13, 117, 118); //mst_lead_source table id
        } else if($originName == 'IS') {
            $originId = array(32, 65, 39, 64); //mst_lead_source table id
        }

        //echo date('Y-m-d H:i:s', time()); die;
        $dateTime = new DateTime();
        $formattedDate = $dateTime->format('Y-m-d H:i:s');
        $hour = date('G', strtotime($formattedDate));
        $startDate = date('Y-m-d', strtotime($date . '-30 days')) . ' 00:00:00';
        $endDate = $date . ' 23:59:59';

        //check already added offline conversion
        $alreadyAdded = GoogleAdwardsOfflineConversion::select('lead_id')
            /*->where('lead_date', $date)*/
            ->whereBetween('lead_date', [date('Y-m-d', strtotime($date . '-90 days')), $date])
            ->where('type', 'booked')
            ->get()
            ->toArray();

        $leadIdArray = [];
        //get lead_id from lead_customer_review table
        $leadCustomerReviewDetail = LeadCustomerReview::where('is_booked', 'yes')->whereBetween('created_at', [$startDate, $endDate])->whereNotIn('lead_id', $alreadyAdded)->groupBy('lead_id')->get('lead_id')->toArray();
        if (count($leadCustomerReviewDetail) > 0) { for ($r=0; $r < count($leadCustomerReviewDetail); $r++) {
            if (!in_array($leadCustomerReviewDetail[$r]['lead_id'], $leadIdArray)) $leadIdArray[] = $leadCustomerReviewDetail[$r]['lead_id'];
        } }

        //get lead_id from lead_call table
        $leadDetail = Lead::where('book_type_id', '!=', 1)->whereBetween('created_at', [$startDate, $endDate])->whereNotIn('lead_id', $alreadyAdded)->get('lead_id')->toArray();
        if (count($leadDetail) > 0) { for ($l=0; $l < count($leadDetail); $l++) {
            if (!in_array($leadDetail[$l]['lead_id'], $leadIdArray)) $leadIdArray[] = $leadDetail[$l]['lead_id'];
        } }

        //get lead_id from lead_call table
        $leadCallDetail = LeadCall::where('call_disposition_id', 8)->whereBetween('created_at', [$startDate, $endDate])->whereNotIn('lead_id', $alreadyAdded)->groupBy('lead_id')->get('lead_id')->toArray();
        if (count($leadCallDetail) > 0) { for ($c=0; $c < count($leadCallDetail); $c++) {
            if (!in_array($leadCallDetail[$c]['lead_id'], $leadIdArray)) $leadIdArray[] = $leadCallDetail[$c]['lead_id'];
        } }
        //print_r($leadIdArray); die;

        /*run this cron every hours current time is less than 3 to pick yesterday and modify -2 hours but current time is greater than 3
        pick current date and modify -2 hours and avoid already added offline conversation.*/
        if (count($leadIdArray) > 0) {
            $leads = DB::table('lead')
                ->select('lead.lead_id', 'lead.email', 'lead.phone', 'lead.lead_source_id', 'lead.payout','lead_landing_page.gclid', 'lead_landing_page.gclid_type', 'lead.lead_generated_at','lead_moving.from_state','lead_moving.to_state')
                ->join('lead_landing_page','lead_landing_page.lead_id','=','lead.lead_id')
                ->leftJoin('lead_moving','lead_moving.lead_id','=','lead.lead_id')
                ->where('lead.lead_generated_at', '>=', '2025-06-01 00:00:00')
                ->whereIn('lead.lead_source_id', $originId)
                /*->where(DB::raw('HOUR(lead.lead_generated_at)'), ">=", $hour)
                ->where(DB::raw('HOUR(lead.lead_generated_at)'), "<=", $hour + 20)*/
                ->whereIn('lead.lead_id', $leadIdArray)
                ->where('lead_landing_page.gclid', '<>', '')
                ->take($flag)
                ->get();

            //echo '<pre>'; print_r($leads); die;
            if (count($leads) > 0) { foreach($leads as $lead) {
                $conversionTime = date('Y-m-d H:i:s', strtotime($lead->lead_generated_at))."-04:00";

                try {
                    if ($originName == "VM") {
                        GoogleAdwordsOfflineEnhancedConversionNewController::$CUSTOMER_ID = 7000201456;
                        GoogleAdwordsOfflineEnhancedConversionNewController::$CONVERSION_ACTION_ID = 6685939665;//ctId getting from url
                    } else if($originName == "QTM") {
                        GoogleAdwordsOfflineEnhancedConversionNewController::$CUSTOMER_ID = 3189698923;
                        GoogleAdwordsOfflineEnhancedConversionNewController::$CONVERSION_ACTION_ID = 7177439023;//ctId getting from url
                    } else if ($originName == "IS") {
                        GoogleAdwordsOfflineEnhancedConversionNewController::$CUSTOMER_ID = 5101673588;
                        GoogleAdwordsOfflineEnhancedConversionNewController::$CONVERSION_ACTION_ID = 7177435336;//ctId getting from url
                    }

                    GoogleAdwordsOfflineEnhancedConversionNewController::$CONVERSION_DATE_TIME = $conversionTime;
                    GoogleAdwordsOfflineEnhancedConversionNewController::$CONVERSION_VALUE = ($lead->payout > 0) ? round($lead->payout, 2) : 0.1;
                    GoogleAdwordsOfflineEnhancedConversionNewController::$EMAIL = $lead->email;
                    GoogleAdwordsOfflineEnhancedConversionNewController::$PHONE = $lead->phone;

                    GoogleAdwordsOfflineEnhancedConversionNewController::$ORDER_ID = $lead->lead_id;
                    if ($lead->gclid_type == '0') { //for GCLID
                        GoogleAdwordsOfflineEnhancedConversionNewController::$GCLID = $lead->gclid;
                    } else if ($lead->gclid_type == '1') { //for GBRAID
                        GoogleAdwordsOfflineEnhancedConversionNewController::$GBRAID = $lead->gclid;
                    } else if ($lead->gclid_type == '2') { //for WBRAID
                        GoogleAdwordsOfflineEnhancedConversionNewController::$WBRAID = $lead->gclid;
                    }
                    GoogleAdwordsOfflineEnhancedConversionNewController::$AD_USER_DATA_CONSENT = null;
                    //echo $conversionTime;

                    $response = GoogleAdwordsOfflineEnhancedConversionNewController::main();
                    // store response
                    $responseData = json_decode($response, true);
                    $status = 'yes';
                    if($responseData['error'] == 1) {
                        $status = 'no';
                    }

                    $newRecord = [
                        'lead_source_id' => $lead->lead_source_id,
                        'lead_id' => $lead->lead_id,
                        'lead_date' => date('Y-m-d', strtotime($lead->lead_generated_at)),
                        'response' => $response,
                        'status' => $status,
                        'type' => 'booked',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    //echo '<pre>'; print_r($record); die;
                    GoogleAdwardsOfflineConversion::create($newRecord);

                } catch (Exception $e) {
                    Log::info('New Offline Conversion:- ' . $e->getMessage());
                    echo $e->getMessage();
                }
            } } else {
                echo "No records found.";
            }
        } else {
            echo "No records found.";
        }
    }

    public function offlineConversion($date='') {
        //$runDate = date('Y-m-d',strtotime("-1 days"));
        //it will fetch 22 to 24 hours earliers records from lead table to submit google adwords
        $flag = 100;
        $runDate = date('Y-m-d');
        $runDate = date('Y-m-d', strtotime("-1 days"));
        if(isset($date) && !empty($date)) {
            $runDate = $date;
            $flag = 20;
        }
        //dd($runDate);
        //echo $runDate; die;
        $this->getOfflineConversion('VM', $runDate, $flag);
        $this->getOfflineConversion('QTM', $runDate, $flag);
        $this->getOfflineConversion('IS', $runDate, $flag);
    }

    public function saveTalkedBooked($date='') {
        $startDate          = date('Y-m-d', strtotime("-1 days")) . ' 00:00:00';
        $endDate            = date('Y-m-d', strtotime("-1 days")) . ' 23:59:59';
        if(isset($date) && !empty($date)) {
            $startDate      = $date . ' 00:00:00';
            $endDate        = $date . ' 23:59:59';
        }
        $leadTransferCall   = LeadTransferCall::whereBetween('created_at', [$startDate, $endDate])->where('duration', '>', 500)->get();
        if (count($leadTransferCall) > 0) { foreach ($leadTransferCall as $transferCall) {
            $campaign       = Campaign::where('campaign_id', $transferCall->campaign_id)->first();
            $leadRouting    = LeadRouting::where('lead_id', $transferCall->lead_id)->where('campaign_id', $transferCall->campaign_id)->first();

            $leadId         = $transferCall->lead_id;
            $leadCallId     = $transferCall->lead_call_id;
            $businessId     = $campaign->business_id;
            $leadRoutingId  = $leadRouting->lead_routing_id;

            $checkLeadRecord= LeadCustomerReview::where('lead_id', $leadId)->where('business_id', $businessId)->first();
            if (isset($checkLeadRecord)) {
                /*LeadCustomerReview::where('lead_id', $leadId)
                    ->where('business_id', $businessId)
                    ->update([
                        'is_customer_talked' => 'yes',
                        'is_booked' => 'yes'
                    ]);*/
            } else {
                $reviewArray= [];
                $reviewArray['lead_routing_id'] = $leadRoutingId;
                $reviewArray['lead_id'] = $leadId;
                $reviewArray['lead_call_id'] = $leadCallId;
                $reviewArray['business_id'] = $businessId;
                $reviewArray['rating'] = 0;
                $reviewArray['quote_amt'] = $reviewArray['comments'] = $reviewArray['is_display'] = '';
                $reviewArray['is_display'] = 'no';
                $reviewArray['is_booked'] = 'yes';
                $reviewArray['created_at'] = $transferCall->created_at; //date("Y-m-d H:i:s");
                LeadCustomerReview::insert($reviewArray);
            }
        }
            echo "Success";
        }
        echo "No records found";
    }
}