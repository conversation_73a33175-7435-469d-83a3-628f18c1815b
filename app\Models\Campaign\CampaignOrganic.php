<?php

namespace App\Models\Campaign;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CampaignOrganic extends Model {

    use HasFactory;

    protected $table = "campaign_organic";
    protected $primaryKey = "campaign_organic_id";
    public $timestamps = false;
    protected $fillable = [
        "campaign_organic_id",
        "campaign_id",
        "business_name",
        "business_address",
        "address_locality",
        "address_region",
        "postal_code",
        "total_business_year",
        "services_offer",
        "other_services_offer",
        "contact_number",
        "forward_number",
        "call_charge",
        "serve_area",
        "licence",
        "review",
        "is_nationwide_availability",
        "is_storage_availability",
        "company_type",
        "is_heavy_equipment",
        "is_car_transportation",
        "is_moving_container",
        "language_support",
        "no_of_years",
        "cancellation_policy",
        "price_range",
        "business_url",
        "mc_number",
        "ranking_score",
        "business_about",
        "business_highlights",
        "other_business_highlights",
        "move_type",
        "moving_services",
        "is_full_lead",
        "is_call_lead",
        "created_at"
    ];

}
