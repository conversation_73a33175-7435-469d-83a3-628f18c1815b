<?php

namespace App\Http\Controllers\Plivo;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Outbound\LeadCall;
use DB;
use Exception;
use App\Helpers\Helper;
use App\Models\Master\MstCallConfig;
use App\Models\Inbound\LeadIbCall;
use App\Models\Inbound\LeadIbCallLog;
use App\Models\Outbound\LeadCallNumber;
use App\Models\Master\MstLeadSource;
use App\Models\Lead\Lead;
use App\Models\Lead\LeadRequestLog;
use App\Models\SalesRep\RepActiveCall;
use App\Http\Controllers\LeadCallBuffer\CallsbufferCronController;
use App\Http\Controllers\LeadCallBuffer\LeadCallBufferController;
use App\Models\Master\MstTimeZone;
use App\Models\Lead\LeadActivity;

class PlivoController extends Controller {

    public $digit_select = 'digitselectnew';
    private $customerNumber = "";
    private $custDialNumber = "";
    private $defaultimezone;
    function __construct() {
        $timezoneobj = new MstTimeZone();
        $this->defaultimezone = $timezoneobj->getdefaulttimezone();
    }
    public function callForward() {
        $parameters = $_REQUEST;
        //Added By HJ On 02-08-2023 For Comio Number Start
        if(isset($parameters['SIP-H-To'])){
            $parameters['To'] = substr($parameters['SIP-H-To'],6, 11);
            //$parameters['From'] = trim($parameters['CallerName'],"+");
            $parameters['From'] = substr($parameters['From'], 5, 11);
        }
        //Added By HJ On 02-08-2023 For Comio Number End
        $this->customerNumber = $parameters['From'];
        $this->custDialNumber = $parameters['To'];
        try {
            $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '1', 'Inbound Call Data==' . json_encode($parameters));
            $response = $this->addDial($parameters);
            header("Content-type: text/xml");
            echo $response;
            exit;
        } catch (Exception $ex) {
            $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '2', 'callForward Exception : ' . $ex->getMessage());
        }
    }

    public function addDial($parameters) {
        $this->customerNumber = $parameters['From'];
        $this->custDialNumber = $parameters['To'];
        $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '3', 'VM Direct connect to rep - ' . json_encode($parameters));
        try {
            $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
            $fromNumber = $parameters['From'];

            // Initialize digit_pressed from parameters, default to empty if not set
            $digit_pressed = isset($parameters['Digits']) ? $parameters['Digits'] : '';

            $getNumbers = LeadCallNumber::where('status', 'active')->where('phone', $this->custDialNumber)->get()->toArray();
            $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '4', 'LeadCallNumber data - ' . json_encode($getNumbers)."==".$this->custDialNumber);
            if (count($getNumbers) > 0) {
                $sourceNameArr = array();
                $getSourceData = MstLeadSource::get()->toArray();
                for ($s = 0; $s < count($getSourceData); $s++) {
                    $sourceNameArr[$getSourceData[$s]['lead_source_id']] = $getSourceData[$s]['lead_source_name'];
                }
                $phone = $getNumbers[0]['phone'];
                $origin = $getNumbers[0]['lead_source_id'];
                $is_current = $getNumbers[0]['is_current'];
                $status = $getNumbers[0]['status'];
                $category = $getNumbers[0]['lead_category_id'];
                $getConfigData = MstCallConfig::get()->toArray();
                $tokAvailabel = $tokInbound = $inboundcalltransfer = $enableVoicePrompt = 0;
                $movevoiceprompt = 1;
                if (count($getConfigData) > 0) {
                    for ($c = 0; $c < count($getConfigData); $c++) {
                        if (strtolower($getConfigData[$c]['call_config']) == "calitoutbound" && strtolower($getConfigData[$c]['status']) == "active") {
                            $tokAvailabel = 1;
                        }
                        if (strtolower($getConfigData[$c]['call_config']) == "callitinbound" && strtolower($getConfigData[$c]['status']) == "active") {
                            $tokInbound = 1;
                        }
                        if (strtolower($getConfigData[$c]['call_config']) == "inboundcalltransfer" && strtolower($getConfigData[$c]['status']) == "active") {
                            $inboundcalltransfer = 1;
                        }
                    }
                }
                $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '4', 'VM Tok off called - ' . $tokAvailabel . '==' . $tokInbound."===".$inboundcalltransfer."==".$sourceNameArr[$origin]);
                if (isset($sourceNameArr[$origin])) {
                    $sourceName = strtoupper(trim($sourceNameArr[$origin]));
                    if (strpos($sourceName, 'VM') !== false || strpos($sourceName, 'QTM') !== false) {
                        if ($tokInbound == 0) {
                            $voice = "vm_jr_hl_ct_tok_off.mp3";
                            if (strpos($sourceName, 'QTM') !== false) {
                                $voice = "qtm_jr_hl_ct_tok_off.mp3";
                            }
                        } else {
                            $voice = "vm_jr_hl_ct_tok_on.mp3";
                            if (strpos($sourceName, 'QTM') !== false) {
                                $voice = "qtm_jr_hl_ct_tok_on.mp3";
                            }
                        }
                        $mp3Name = "vmvoice_28_03_24.mp3";
                        if (strpos($sourceName, 'QTM') !== false) {
                            $mp3Name = "qtmvoice.mp3";
                        }
                        if ($category == 9) {
                            if ($inboundcalltransfer > 0) {
                                if ($tokInbound == 0) {
                                    $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '4', 'VM Tok off called - ' . $matrixBaseURL_Outbound . '/mp3/' . $voice);
                                    return '<Response>
                                    <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselectcommon" method="GET" timeout="30" numDigits="1" validDigits="12340">
                                        <Play loop="1">' . $matrixBaseURL_Outbound . '/mp3/' . $voice . '</Play>
                                    </GetDigits>
                                    </Response>';
                                } else {
                                    $parameters['Digits_2'] = 2;
                                    $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '5', 'VM Direct connect to rep - ' . json_encode($parameters));
                                    $response = $this->digitselectinb($parameters);
                                    header("Content-type: text/xml");
                                    echo $response;
                                    exit;
                                }
                            } else {
                                $digitValid = "123409";
                                if ($tokInbound == 0) {
                                    $digitValid = "12340";
                                    $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '6', 'VM Tok off but inboundcalltransfer off = ' . $matrixBaseURL_Outbound . '/mp3/' . $voice);
                                } else {
                                    $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '7', 'VM Tok on but inboundcalltransfer off = ' . $matrixBaseURL_Outbound . '/mp3/' . $voice);
                                }
                                return '<Response>
                                <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselectcommon" method="GET" timeout="30" numDigits="1" validDigits="' . $digitValid . '">
                                    <Play loop="1">' . $matrixBaseURL_Outbound . '/mp3/' . $voice . '</Play>
                                </GetDigits>
                                </Response>';
                            }
                        } else if ($category == 2) {
                            $digitValid = "10*";
                            if (strpos($sourceName, 'QTM') !== false) {
                                $digitValid = "19";
                            }
                            $serviceName = "Junk";
                            $callCategory = 2;
                        } else if ($category == 3) {
                            $digitValid = "10*";
                            if (strpos($sourceName, 'QTM') !== false) {
                                $digitValid = "19";
                            }
                            $serviceName = "Heavy Lift";
                            $callCategory = 3;
                        } else if ($category == 4) {
                            $digitValid = "10*";
                            if (strpos($sourceName, 'QTM') !== false) {
                                $digitValid = "19";
                            }
                            $serviceName = "Car Transportation";
                            $callCategory = 4;
                        } else {
                            $digitValid = "1290";
                            if (strpos($sourceName, 'QTM') !== false) {
                                $digitValid = "129";
                            }
                            $serviceName = "Moving";
                            $callCategory = 1;
                            if ($movevoiceprompt > 0) {
                                $enableVoicePrompt = 1;
                            }
                        }
                        $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '8', 'VM =' . $serviceName . ' Call MP3 - ' . $mp3Name);
                        if ($inboundcalltransfer > 0 || $enableVoicePrompt > 0) {
                            if ($category == 1 && ($tokInbound == 0 || $enableVoicePrompt > 0)) {
                                $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '9', 'VM inboundcalltransfer - ' . $inboundcalltransfer . ',==service name==' . $serviceName . ',=tok config==' . $tokInbound);
                                if($mp3Name == "vmvoice_28_03_24.mp3") {
                                    return '<Response>
                                    <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselectnewinbound" method="GET" timeout="30" numDigits="1" validDigits="' . $digitValid . '">
                                        <Play>' . $matrixBaseURL_Outbound . '/mp3/additional_1.mp3</Play>
                                        <Play loop="1">' . $matrixBaseURL_Outbound . '/mp3/' . $mp3Name . '</Play>
                                    </GetDigits>
                                </Response>';    
                                }
                                return '<Response>
                                <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselectnewinbound" method="GET" timeout="30" numDigits="1" validDigits="' . $digitValid . '">
                                    <Play loop="1">' . $matrixBaseURL_Outbound . '/mp3/' . $mp3Name . '</Play>
                                </GetDigits>
                            </Response>';
                            } else {
                                $parameters['Digits_2'] = 2;
                                $parameters['lead_category'] = $callCategory;
                                $this->createInboundLogs($parameters['From'], $this->custDialNumber, '10', 'VM Direct connect to rep - ' . json_encode($parameters));
                                $response = $this->digitselectinb($parameters);
                                header("Content-type: text/xml");
                                echo $response;
                                exit;
                            }
                        } else {
                            $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '11', 'VM inboundcalltransfer - ' . $inboundcalltransfer . ',==service name==' . $serviceName);
                            //if($digitValid == "1"){
                            $parameters['Digits_2'] = 2;
                            if ($tokInbound == 0) {
                                $parameters['Digits_2'] = 1;
                            }
                            $parameters['lead_category'] = $callCategory;
                            $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '12', 'VM Direct connect to rep - ' . json_encode($parameters));
                            $response = $this->digitselectinb($parameters);
                            header("Content-type: text/xml");
                            echo $response;
                            exit;
                        }
                    } else if (strpos($sourceName, 'IS') !== false) {
                        $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '14', ' addDial IS Direct connect to rep - ' . json_encode($parameters));
                        $serviceName = "Moving";
                        $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '11', 'IS inboundcalltransfer - ' . $inboundcalltransfer . ',==service name==' . $serviceName);
                        //if($digitValid == "1"){
                        $parameters['Digits_2'] = 2;
                        if ($tokInbound == 0) {
                            $parameters['Digits_2'] = 1;
                        }
                        $parameters['lead_category'] = 1;
                        $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '12', 'IS Direct connect to rep - ' . json_encode($parameters));
                        $response = $this->digitselectinb($parameters);
                        header("Content-type: text/xml");
                        echo $response;
                        exit;
                    }
                }
            }else{
                $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '12', 'Inbound call but number not found - ' . date("Y-m-d H:i:s"));
            }
        } catch (Exception $ex) {
            $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '13', 'addDial Exception : ' . $ex->getMessage());
        }
    }

    public function digitselectnewinbound() {
        $parameters = $_REQUEST;
        //Added By HJ On 02-08-2023 For Comio Number Start
        if(isset($parameters['SIP-H-To'])){
            $parameters['To'] = substr($parameters['SIP-H-To'],6, 11);
            //$parameters['From'] = trim($parameters['CallerName'],"+");
            $parameters['From'] = substr($parameters['From'], 5, 11);
        }
        //Added By HJ On 02-08-2023 For Comio Number End
        $digit_pressed = trim($parameters['Digits']);
        try {
            if ($digit_pressed == "0") {
                $originData = $this->getOriginData($parameters['To']);
                $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
                $origin = $originData['origin'];
                if (strpos($origin, 'QTM') !== false) {
                    $mp3Off = "othercallback_qtm.mp3";
                } else {
                    $mp3Off = "busyline.mp3";
                }
                $this->createInboundLogs($parameters['From'], $parameters['To'], '9', '1 new digitselectnewinbound parameter checking digit_pressed' . $digit_pressed . "==" . $mp3Off . "==Data==" . json_encode($parameters));
                return '<Response>
                    <Play>' . $matrixBaseURL_Outbound . '/mp3/' . $mp3Off . '</Play>
                </Response>';
            } else if ($digit_pressed == "*" || $digit_pressed == "9") {
                $this->createInboundLogs($parameters['From'], $parameters['To'], '9', '2 new digitselectnewinbound parameter checking digit_pressed' . $digit_pressed . "==Data==" . json_encode($parameters));
                $response = $this->addDial($parameters);
                header("Content-type: text/xml");
                echo $response;
                exit;
            } else {
                $this->createInboundLogs($parameters['From'], $parameters['To'], '9', '3 new digitselectnewinbound parameter checking digit_pressed' . $digit_pressed . "==Data==" . json_encode($parameters));
                $response = $this->digitselectinb($parameters);
                header("Content-type: text/xml");
                echo $response;
                exit;
            }
        } catch (Exception $ex) {
            $this->createInboundLogs($parameters['From'], $parameters['To'], '15', 'callForward Exception : ' . $ex->getMessage());
        }
    }

    public function digitselectcommon() {
        //$this->custDialNumber == '18774785237'
        $parameters = $_REQUEST;
        //Added By HJ On 02-08-2023 For Comio Number Start
        if(isset($parameters['SIP-H-To'])){
            $parameters['To'] = substr($parameters['SIP-H-To'],6, 11);
            //$parameters['From'] = trim($parameters['CallerName'],"+");
            $parameters['From'] = substr($parameters['From'], 5, 11);
        }
        //Added By HJ On 02-08-2023 For Comio Number End
        $getConfigData = MstCallConfig::get()->toArray();
        $tokAvailabel = $tokInbound = $inboundcalltransfer = $enableVoicePrompt = 0;
        if (count($getConfigData) > 0) {
            for ($c = 0; $c < count($getConfigData); $c++) {
                if (strtolower($getConfigData[$c]['call_config']) == "calitoutbound" && strtolower($getConfigData[$c]['status']) == "active") {
                    $tokAvailabel = 1;
                }
                if (strtolower($getConfigData[$c]['call_config']) == "callitinbound" && strtolower($getConfigData[$c]['status']) == "active") {
                    $tokInbound = 1;
                }
                if (strtolower($getConfigData[$c]['call_config']) == "inboundcalltransfer" && strtolower($getConfigData[$c]['status']) == "active") {
                    $inboundcalltransfer = 1; //Temporary Set 0
                }
            }
        }
        $getNumbers = LeadCallNumber::where('status', 'active')->where('phone', $parameters['To'])->get()->toArray();
        $origin = 19;
        $sourceName = "VM";
        if (count($getNumbers) > 0) {
            $origin = $getNumbers[0]['lead_source_id'];
        }
        $sourceNameArr = array();
        $getSourceData = MstLeadSource::get()->toArray();
        for ($s = 0; $s < count($getSourceData); $s++) {
            $sourceNameArr[$getSourceData[$s]['lead_source_id']] = $getSourceData[$s]['lead_source_name'];
        }
        if (isset($sourceNameArr[$origin])) {
            $sourceName = strtoupper(trim($sourceNameArr[$origin]));
        }
        $voice = "callforward.mp3";
        $backVoice = "othercallback.mp3";
        if (strpos($sourceName, 'QTM') !== false) {
            $voice = "callforward_qtm.mp3";
            $backVoice = "qtmcallback.mp3";
        }
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        $digit_pressed = ((int) ($parameters['Digits']));
        $this->createInboundLogs($parameters['From'], $parameters['To'], '16', $sourceName . ' digitselectcommon===' . json_encode($parameters));
        $parameters['Digits_2'] = 2;
        $parameters['lead_category'] = 1;
        if ($tokInbound == 0) {
            $parameters['Digits_2'] = 1;
        }
        if ($digit_pressed == 1 || $digit_pressed == '1' || $digit_pressed == 9 || $digit_pressed == '9') { // For Moving Service
            $this->createInboundLogs($parameters['From'], $parameters['To'], '17', $sourceName . ' Moving Service=' . $digit_pressed);
            if ($tokInbound == 0 || $enableVoicePrompt > 0) {
                if($voice == "callforward.mp3") {
                    return '<Response>
                <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselectnew" method="GET" timeout="30" numDigits="1" validDigits="12">
                    <Play>' . $matrixBaseURL_Outbound . '/mp3/additional_1.mp3</Play>
                    <Play loop="1">' . $matrixBaseURL_Outbound . '/mp3/' . $voice . '</Play>
                </GetDigits>
                </Response>';
                }
                return '<Response>
                <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselectnew" method="GET" timeout="30" numDigits="1" validDigits="12">
                    <Play loop="1">' . $matrixBaseURL_Outbound . '/mp3/' . $voice . '</Play>
                </GetDigits>
                </Response>';
            }
        } else if ($digit_pressed == 2 || $digit_pressed == '2') { //Live transfer Car Transportation
            $this->createInboundLogs($parameters['From'], $parameters['To'], '18', $sourceName . ' Car Transportation Service=' . $digit_pressed);
            $parameters['lead_category'] = 4;
        } else if ($digit_pressed == 3 || $digit_pressed == '3') { //Live transfer junk removal
            $this->createInboundLogs($parameters['From'], $parameters['To'], '19', $sourceName . ' Junk Removal Service=' . $digit_pressed);
            $parameters['lead_category'] = 2;
        } else if ($digit_pressed == 4 || $digit_pressed == '4') { //Live transfer Heavy Lifting
            $this->createInboundLogs($parameters['From'], $parameters['To'], '20', $sourceName . ' Heavy Lifting Service=' . $digit_pressed);
            $parameters['lead_category'] = 3;
        } else if ($digit_pressed == 0 || $digit_pressed == '0') {
            $this->createInboundLogs($parameters['From'], $parameters['To'], '21', $sourceName . ' For call back=' . $digit_pressed);
            $digitPress = 9;
            $this->digitselectnewtok($digitPress);
            return '<Response>
                <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselectnewtok" method="GET" timeout="30" numDigits="1" validDigits="9">
                    <Play loop="1">' . $matrixBaseURL_Outbound . '/mp3/' . $backVoice . '</Play>
                </GetDigits>
                </Response>';
        } else {
            $this->createInboundLogs($parameters['From'], $parameters['To'], '22', $sourceName . ' No any other press digit=' . $digit_pressed);
        }
        $response = $this->digitselectinb($parameters);
        header("Content-type: text/xml");
        echo $response;
        exit;
    }

    public function addDialNew($newCustomer) {
        $this->custDialNumber = $newCustomer;
        $this->createInboundLogs($newCustomer, $newCustomer, '123', 'addDialNew moving script');
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        return '<Response>
                    <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselectnew" method="GET" timeout="30" numDigits="1" validDigits="12">
                        <Play>' . $matrixBaseURL_Outbound . '/mp3/additional_1.mp3</Play>
                        <Play loop="1">' . $matrixBaseURL_Outbound . '/mp3/callforward.mp3</Play>
                    </GetDigits>
                </Response>';
    }

    public function digitselectnew() {
        $parameters = $_REQUEST;
        //Added By HJ On 02-08-2023 For Comio Number Start
        if(isset($parameters['SIP-H-To'])){
            $parameters['To'] = substr($parameters['SIP-H-To'],6, 11);
            //$parameters['From'] = trim($parameters['CallerName'],"+");
            $parameters['From'] = substr($parameters['From'], 5, 11);
        }
        //Added By HJ On 02-08-2023 For Comio Number End
        $digit_pressed = trim($parameters['Digits']);
        try {
            if ($digit_pressed == "0") {
                $originData = $this->getOriginData($parameters['To']);
                $origin = $originData['origin'];
                $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
                if (strpos($origin, 'QTM') !== false) {
                    $mp3Off = "othercallback_qtm.mp3";
                } else {
                    $mp3Off = "busyline.mp3";
                }
                $this->createInboundLogs($parameters['From'], $parameters['To'], '10', '1 new digitselectnew parameter checking digit_pressed' . $digit_pressed . "==" . $mp3Off . "==Data==" . json_encode($parameters));
                return '<Response>
                    <Play>' . $matrixBaseURL_Outbound . '/mp3/' . $mp3Off . '</Play>
                </Response>';
            } else if ($digit_pressed == "*" || $digit_pressed == "9") {
                $this->createInboundLogs($parameters['From'], $parameters['To'], '10', '2 new digitselectnew parameter checking digit_pressed' . $digit_pressed . "==Data==" . json_encode($parameters));
                $response = $this->addDial($parameters);
                header("Content-type: text/xml");
                echo $response;
                exit;
            } else {
                $this->createInboundLogs($parameters['From'], $parameters['To'], '10', '3 new digitselectnew parameter checking digit_pressed' . $digit_pressed . "==Data==" . json_encode($parameters));
                $response = $this->digitselectinb($parameters);
                header("Content-type: text/xml");
                echo $response;
                exit;
            }
        } catch (Exception $ex) {
            $this->createInboundLogs($parameters['From'], $parameters['To'], '24', 'callForward Exception : ' . $ex->getMessage());
        }
    }

    public function digitselectnewtok($digitPress = 0) {
        $parameters = $_REQUEST;
        $voice = "";
        $this->createInboundLogs($parameters['From'], $parameters['To'], '25', 'digitselectnewtok==' . json_encode($parameters) . '==voice=' . $voice);
        exit;
        $originData = $this->getOriginData($parameters['To']);
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        $voice = $originData['callback'];
        try {
            $this->createInboundLogs($parameters['From'], $parameters['To'], '26', 'digitselectnewtok==' . json_encode($parameters) . '==voice=' . $voice);
            $digit_pressed = $parameters['Digits'];
            if ($digitPress > 0) {
                $digit_pressed = 9;
            }
            if ($digit_pressed == 9 || $digit_pressed == '9') {
                $callUuid = $parameters['CallUUID'];
                $lpCallForward = new LpCallForward();
                $lpCallForward->cust_number = $parameters['From'];
                $lpCallForward->cust_dial_number = $parameters['To'];
                $lpCallForward->forward_number = 0;
                $lpCallForward->call_uuid = $callUuid;
                $lpCallForward->recording_url = '';
                $lpCallForward->businesses_campaign_id = 0;
                $lpCallForward->digit_pressed = 2;
                $lpCallForward->payout = 0;
                $lpCallForward->duration = 0;
                $lpCallForward->type = 1;
                $lpCallForward->lead_category = "0";
                $lpCallForward->save();
                $this->leadPhoneStoreDataForProcess($parameters['From'], $parameters['To']);
                //Call hangupacceptcall event here  
                $phoneOnlyData = Phoneonlys::where('call_uuid', $callUuid)->get()->first();
                $this->createInboundLogs($parameters['From'], $parameters['To'], '27', 'digitselectnewtok event called when press 9 degit : ' . $callUuid);
                return '<Response>
                          <Play>' . $matrixBaseURL_Outbound . '/mp3/' . $voice . '</Play>
                    </Response>';
            }
        } catch (Exception $e) {
            $this->createInboundLogs($parameters['From'], $parameters['To'], '28', 'digitselectnewtok Exception : ' . $e->getMessage());
            return '<Response>
                          <Play>' . $matrixBaseURL_Outbound . '/mp3/' . $voice . '</Play>
                    </Response>';
        }
    }

    public function digitselectinb($postParameters) {
        $parameters = $_REQUEST;
        //Added By HJ On 02-08-2023 For Comio Number Start
        if(isset($parameters['SIP-H-To'])){
            $parameters['To'] = substr($parameters['SIP-H-To'],6, 11);
            //$parameters['From'] = trim($parameters['CallerName'],"+");
            $parameters['From'] = substr($parameters['From'], 5, 11);
        }
        //Added By HJ On 02-08-2023 For Comio Number End
        try {
            if (isset($postParameters['Digits_2'])) {
                $parameters['Digits'] = $postParameters['Digits_2'];
            }
            $lead_category = 1;
            if (isset($postParameters['lead_category'])) {
                $lead_category = $postParameters['lead_category'];
            }
            $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
            $this->createInboundLogs($parameters['From'], $parameters['To'], '29', json_encode($parameters));
            $digit_pressed = ((int) ($parameters['Digits']));
            $callUuid = $parameters['CallUUID'];
            $getConfigData = MstCallConfig::get()->toArray();
            $tokAvailabel = $tokInbound = $inboundcalltransfer = $enableVoicePrompt = 0;
            if (count($getConfigData) > 0) {
                for ($c = 0; $c < count($getConfigData); $c++) {
                    if (strtolower($getConfigData[$c]['call_config']) == "calitoutbound" && strtolower($getConfigData[$c]['status']) == "active") {
                        $tokAvailabel = 1;
                    }
                    if (strtolower($getConfigData[$c]['call_config']) == "callitinbound" && strtolower($getConfigData[$c]['status']) == "active") {
                        $tokInbound = 1;
                    }
                    if (strtolower($getConfigData[$c]['call_config']) == "inboundcalltransfer" && strtolower($getConfigData[$c]['status']) == "active") {
                        $inboundcalltransfer = 0; //Temporary Set 0
                    }
                }
            }
            if ((!isset($parameters['Digits']) ) || (!isset($parameters['CallUUID']) )) {
                throw new Exception("Digit Not Founds");
            } // condition false - froward to default number
            if (!isset($parameters['From'])) {
                $fromNumber = $this->customerNumber;
            } else {
                $fromNumber = $parameters['From'];
                $this->custDialNumber = $parameters['To'];
            }
            $this->createInboundLogs($parameters['From'], $parameters['To'], '30', 'Digit pressed==' . $digit_pressed . '==lead_category=' . $lead_category);
            if ($callUuid != "") {
                $this->CallRecord($callUuid);
            }
            $originData = $this->getOriginData($parameters['To']);
            $origin = $originData['origin'];
            $voice = $originData['voice'];
            $this->createInboundLogs($parameters['From'], $parameters['To'], '33', 'source:' . $origin . ' and Voice prompt:' . $voice);
            $requestLogArr = array();
            $requestLogArr['lead_request'] = json_encode($parameters);
            $requestLogArr['lead_response'] = "Inbound Call Request";
            $requestLogArr['created_at'] = date('Y-m-d H:i:s');
            $lead_request_log_id = LeadRequestLog::create($requestLogArr)->lead_request_log_id;

            $this->createInboundLogs($parameters['From'], $parameters['To'], '34', 'LeadRequestLog lead_request_log_id=:' . $lead_request_log_id);
            // Find origin_id
            $originId = 130;
            $sourceIdArr = array();
            $sourceName = "VM";
            if (strpos($origin, 'QTM') !== false) {
                $sourceName = "QTM";
            } else if (strpos($origin, 'IS') !== false) {
                $sourceName = "IS";
            }
            $getVMSourceIds = MstLeadSource::where('lead_source_name', 'like', $sourceName.'%')->get(['lead_source_id','lead_source_name'])->toArray();
            for($s=0;$s<count($getVMSourceIds);$s++){
                if(!in_array($getVMSourceIds[$s]['lead_source_id'],$sourceIdArr)){
                    $sourceIdArr[] = $getVMSourceIds[$s]['lead_source_id'];
                }
                if(strtoupper($getVMSourceIds[$s]['lead_source_name']) == $sourceName."P"){
                   $originId =  $getVMSourceIds[$s]['lead_source_id'];
                }
            }
            $start_date = date('Y-m-d', strtotime("-90 days")) . ' 00:00:00';
            $end_date = date('Y-m-d 23:59:59');
            $checkExistLeadData = Lead::where("phone", $this->setTenDigitNumber($parameters['From']))->orderby('lead_id', 'DESC')->whereBetween('lead_generated_at', [$start_date, $end_date])->whereIn('lead_source_id', $sourceIdArr)->where('lead_category_id', $lead_category)->get()->take(1)->toArray();
            if (count($checkExistLeadData) > 0) {
                $lastInsertedId = $checkExistLeadData[0]['lead_id'];
            } else {
                $leadsphone = new Lead;
                $leadsphone->phone = $this->setTenDigitNumber($parameters['From']);
                $leadsphone->lead_generated_at = date('Y-m-d H:i:s');
                $leadsphone->lead_source_id = $originId;
                $leadsphone->lead_category_id = $lead_category;
                $leadsphone->timezone_id = $this->defaultimezone;
                $leadsphone->lead_type = 'phone';
                $leadsphone->email = '';
                $leadsphone->name = '';
                $leadsphone->is_duplicate = 'no';
                $leadsphone->is_verified = 'yes';
                $leadsphone->is_dnc_call = 'no';
                $leadsphone->is_dnc_sms = 'no';
                $leadsphone->is_dnc_email = 'no';
                $leadsphone->sold_type = 'none';
                $leadsphone->is_marketplace = 'no';
                $leadsphone->lead_request_log_id = $lead_request_log_id;
                $leadsphone->created_at = date('Y-m-d H:i:s');
                $leadsphone->save();
                $lastInsertedId = $leadsphone->lead_id;
            }

            $this->createInboundLogs($parameters['From'], $parameters['To'], '35', 'Lead lead_id=:' . $lastInsertedId);
            //$leadPhoneDataEvent = Lead::where('id', $lastInsertedId)->get()->first();

            $this->createInboundLogs($parameters['From'], $parameters['To'], '36', 'LeadCall save=:' . $lastInsertedId . "==" . $this->setTenDigitNumber($parameters['From']) . "==" . $this->setTenDigitNumber($parameters['To']) . "==" . $callUuid."==".json_encode($sourceIdArr)."==".$originId);
            if ($tokInbound == 1 || $tokInbound == '1') {
                $leadCall = new LeadCall;
                $leadCall->lead_id = $lastInsertedId;
                $leadCall->from_number = $this->setTenDigitNumber($parameters['From']);
                $leadCall->to_number = $parameters['To'];
                $leadCall->transfer_type = "call";
                $leadCall->call_datetime = date('Y-m-d H:i:s');
                $leadCall->user_id = 0;
                $leadCall->call_type = "inbound";
                $leadCall->call_disposition_id = 15;
                $leadCall->legA_call_uuid = $callUuid;
                $leadCall->call_count = 0;
                $leadCall->is_checked_recording = "no";
                $leadCall->campaign_score = 0;
                $leadCall->inbound_call_status = "ringing";
                $leadCall->inbound_type = "manual";
                $leadCall->created_at = date('Y-m-d H:i:s');
                $leadCall->save();
                $lastInsertedCallId = $leadCall->lead_call_id;
                $leadactiviyobj = new LeadActivity();
                $leadactiviyobj->createActivity($lastInsertedId, 5);
                $this->createInboundLogs($parameters['From'], $parameters['To'], '37', 'LeadCall lead_call_id=:' . $lastInsertedCallId);
            }
            if ($tokInbound == 0 || $tokInbound == '0') {
                //Pending now
                $buffer = new CallsbufferCronController();
                $buffer->SaveToBufferViaPhoneonly($lastInsertedId);
            } else if ($digit_pressed == 1 || $digit_pressed == '1' || $digit_pressed == 2 || $digit_pressed == '2') {
                $this->createInboundLogs($parameters['From'], $parameters['To'], '32', 'Digit pressed' . $digit_pressed);
                /*$responseXml = '<Response>
                <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselectnewtok" method="GET" timeout="30" numDigits="1" validDigits="9">
                <Play loop="6">' . $matrixBaseURL_Outbound . '/mp3/' . $voice . '</Play>
                </GetDigits>
                </Response>';*/
                $conferenceName = "Call_".$lastInsertedCallId;

                // Automatically connect a sales rep to the conference
                $this->connectSalesRepToConference($lastInsertedCallId, $lastInsertedId);

                $responseXml = '<Response>
                    <Conference
                        waitSound="https://ma.leaddial.co/plivo/media/MA-customer-support.mp3"
                        startConferenceOnEnter="true"
                        endConferenceOnExit="false"
                        enterSound="beep:1">'.$conferenceName.'</Conference>
                </Response>';

                $this->createInboundLogs($parameters['From'], $parameters['To'], '39', 'Inboundcalls XML=:' . $responseXml . '---' . $voice);
                return $responseXml;
            } else if (($digit_pressed == 9 || $digit_pressed == '9') && $sourceName == "QTM") {
                return '<Response>
                        <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselectnewinbound" method="GET" timeout="30" numDigits="1" validDigits="129">
                        <Play loop="1">' . $matrixBaseURL_Outbound . '/mp3/qtmvoice.mp3</Play>
                        </GetDigits>
                </Response>';
            } else if (($digit_pressed == 9 || $digit_pressed == '9') && $sourceName == "VM") {
                 $this->createInboundLogs($parameters['From'], $parameters['To'], '32', 'Digit pressed' . $digit_pressed);
                $responseXml = '<Response>
                <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselectnewtok" method="GET" timeout="30" numDigits="1" validDigits="9">
                <Play loop="6">' . $matrixBaseURL_Outbound . '/mp3/' . $voice . '</Play>
                </GetDigits>
                </Response>';
                $this->createInboundLogs($parameters['From'], $parameters['To'], '39', 'Inboundcalls XML=:' . $responseXml . '---' . $voice);
                return $responseXml;
            } else if ($tokInbound == 1 || $tokInbound == '1') {
                // Auto-add to conference when tokInbound is enabled and no specific digits pressed
                $this->createInboundLogs($parameters['From'], $parameters['To'], '40', 'Auto conference - tokInbound enabled, no digits pressed');
                $conferenceName = "Call_".$lastInsertedCallId;

                // Automatically connect a sales rep to the conference
                $this->connectSalesRepToConference($lastInsertedCallId, $lastInsertedId);

                $responseXml = '<Response>
                    <Conference
                        waitSound="https://ma.leaddial.co/plivo/media/MA-customer-support.mp3"
                        startConferenceOnEnter="true"
                        endConferenceOnExit="false"
                        enterSound="beep:1">'.$conferenceName.'</Conference>
                </Response>';

                $this->createInboundLogs($parameters['From'], $parameters['To'], '41', 'Auto conference XML=:' . $responseXml);
                return $responseXml;
            }
            $content = '<Response><Speak>Thank you</Speak></Response>';
            header("Content-type: text/xml");
            echo $content;
            exit;
        } catch (Exception $ex) {
            $this->createInboundLogs($parameters['From'], $parameters['To'], '37', 'digitselectinb Exception : ' . $ex->getMessage());
        }
    }

    public function getOriginData($originNumber) {
        $getNumbers = LeadCallNumber::where('status', 'active')->where('phone', $originNumber)->get()->toArray();
        DB::table('dev_debug')->insert(['sr_no' => 51, 'result' => "Origin Number==" . $originNumber]);
        $origin = 'VMP';
        $voice = "otherwait.mp3";
        $callback = "othercallback.mp3";
        if (count($getNumbers) > 0) {
            $origin = $getNumbers[0]['lead_source_id'];
            $sourceNameArr = array();
            $getSourceData = MstLeadSource::get()->toArray();
            for ($s = 0; $s < count($getSourceData); $s++) {
                $sourceNameArr[$getSourceData[$s]['lead_source_id']] = $getSourceData[$s]['lead_source_name'];
            }
            $getConfigData = MstCallConfig::get()->toArray();
            $tokAvailabel = $tokInbound = $inboundcalltransfer = $enableVoicePrompt = 0;
            if (count($getConfigData) > 0) {
                for ($c = 0; $c < count($getConfigData); $c++) {
                    if (strtolower($getConfigData[$c]['call_config']) == "calitoutbound" && strtolower($getConfigData[$c]['status']) == "active") {
                        $tokAvailabel = 1;
                    }
                    if (strtolower($getConfigData[$c]['call_config']) == "callitinbound" && strtolower($getConfigData[$c]['status']) == "active") {
                        $tokInbound = 1;
                    }
                    if (strtolower($getConfigData[$c]['call_config']) == "inboundcalltransfer" && strtolower($getConfigData[$c]['status']) == "active") {
                        $inboundcalltransfer = 0; //Temporary Set 0
                    }
                }
            }
            if (isset($sourceNameArr[$origin])) {
                $sourceName = strtoupper(trim($sourceNameArr[$origin]));
                if (strpos($sourceName, 'VM') !== false) {
                    $origin = 'VMP';
                    $voice = "otherwait.mp3";
                    if ($inboundcalltransfer > 0 && $tokInbound > 0) {
                        $voice = "vm_voice_direct_call.mp3";
                    }
                    $callback = "othercallback.mp3";
                } else if (strpos($sourceName, 'QTM') !== false) {
                    $origin = 'QTMP';
                } else if (strpos($sourceName, 'IS') !== false) {
                    $origin = 'ISP';
                } else if (strpos($sourceName, 'REXDIRECT') !== false) {
                    $voice = "otherwait.mp3";
                    $origin = 'REXDIRECTP';
                }
            }
        }
        $dataArr['origin'] = $origin;
        $dataArr['voice'] = $voice;
        $dataArr['callback'] = $callback;
        return $dataArr;
    }

    public function callForward_old() {
        $parameters = $_REQUEST;
        DB::table('dev_debug')->insert(['sr_no' => 51, 'result' => "callForward call==" . json_encode($parameters)]);
        $callerId = "+19123350788";
        $phoneNumber = "12018826345";
        if (isset($parameters['CallUUID']) && trim($parameters['CallUUID']) != "") {
            $content = '<?xml version="1.0" encoding="UTF-8"?>
                    <Response>
                        <Dial>
                          <User>sip:<EMAIL></User>
                        </Dial>
                    </Response>';
            /* <Conference>Sample_Room</Conference> */
            DB::table('dev_debug')->insert(['sr_no' => 53, 'result' => "accept call==" . $content]);
            return response($content, 200)->header('Content-Type', 'text/xml');
            try {
                $endDecObj = new Helper;
                $general_variable_5 = Helper::checkServer()['general_variable_5'];
                $general_variable_6 = Helper::checkServer()['general_variable_6'];
                $decVariable5 = $endDecObj->customeDecryption($general_variable_5);
                $decVariable6 = $endDecObj->customeDecryption($general_variable_6);
                // Call Recording
                $call_uuid = trim($parameters['CallUUID']);
                $para = json_encode(array('time_limit' => '7200'));
                $ch = curl_init();

                curl_setopt($ch, CURLOPT_URL, 'https://api.plivo.com/v1/Account/' . $decVariable5 . '/Call/' . $call_uuid . '/Record/');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $para);
                curl_setopt($ch, CURLOPT_USERPWD, $decVariable5 . ':' . $decVariable6);

                $headers = array();
                $headers[] = 'Content-Type: application/json';
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                $result = curl_exec($ch);
                if ((!empty($result)) && ($result !== null)) {
                    $res = json_decode($result, true);
                    if (!empty($res)) {
                        $recording_url = isset($res['url']) ? $res['url'] : '';
                        DB::table('dev_debug')->insert(['sr_no' => 52, 'result' => "recording_url==" . $recording_url]);
                    }
                }
            } catch (Exception $e) {
                DB::table('dev_debug')->insert(['sr_no' => 52, 'result' => "callForward error 1 call==" . $e->getMessage()]);
            }
        }
        //$this->callforwardtomain($parameters['To'], $callerId);
        /* $this->customerNumber = $parameters['From'];
          $this->custDialNumber = $parameters['To'];
          $params = array('callerId' => $parameters['From']);
          $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '1', json_encode($parameters));
          $this->createInboundLogs($this->customerNumber, $this->custDialNumber, '2', 'Inbound Call');
          $response = $this->addDial($parameters); */
        header("Content-type: text/xml");
        echo $response;
        exit;
    }

    public function hangupCallBuyer(Request $request) {
        
    }

    public function tokoutboundtocampaign(Request $request) {
        $Parameters = $request->all();
        DB::table('dev_debug')->insert(['sr_no' => 50, 'result' => "tokoutboundtocampaign call==" . json_encode($Parameters)]);
        try {
            $this->callforwardtomain(trim($Parameters['phone'], "1"), trim($Parameters['From'], "1"));
        } catch (Exception $ex) {
            DB::table('dev_debug')->insert(['sr_no' => 51, 'result' => "tokoutboundanswer error call==" . $ex->getMessage()]);
        }
    }

    public function tokoutboundanswer(Request $request) {
        //rep call recording 
        try {
            DB::table('dev_debug')->insert(['sr_no' => 50, 'result' => "tokoutboundanswer call"]);
            $Parameters = $request->all();
            DB::table('dev_debug')->insert(['sr_no' => 51, 'result' => "tokoutboundanswer call==" . $Parameters['From'] . "==" . json_encode($Parameters)]);
            $this->callforwardtomain($Parameters['lead_phone_number'], $Parameters['From']);
            $content = '<Response><Speak>Thank you</Speak></Response>';
            header("Content-type: text/xml");
            echo $content;
            exit;
            if (isset($request->From) && $request->From && isset($request->To) && $request->To) {
                $call_uuid = ( isset($request->CallUUID) ) ? $request->CallUUID : '';
                $ib_call_ins = [
                    'from_number' => isset($request->From) ? str_replace('+', '', $request->From) : 0,
                    'to_number' => isset($request->To) ? $request->To : 0,
                    'recording_url' => '',
                    'legA_call_uuid' => $call_uuid,
                    'duration' => 1,
                    'user_id' => 0,
                    'lead_id' => 1,
                    'call_count' => 1,
                    'call_disposition_id' => 1,
                    'is_checked_recording' => 'no',
                    'transfer_type' => 'call',
                    'call_datetime' => date("Y-m-d H:i:s"),
                    'created_at' => date("Y-m-d H:i:s"),
                    'call_type' => 'outbound'
                ];
                $recording_url = '';
                try {
                    // Call Recording
                    $para = json_encode(array('time_limit' => '7200'));
                    $ch = curl_init();

                    curl_setopt($ch, CURLOPT_URL, 'https://api.plivo.com/v1/Account/' . P_AUTH_ID . '/Call/' . $call_uuid . '/Record/');
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($ch, CURLOPT_POST, 1);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $para);
                    curl_setopt($ch, CURLOPT_USERPWD, P_AUTH_ID . ':' . P_AUTH_TOKEN);

                    $headers = array();
                    $headers[] = 'Content-Type: application/json';
                    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

                    $result = curl_exec($ch);

                    if ((!empty($result)) && ($result !== null)) {
                        $res = json_decode($result, true);
                        if (!empty($res)) {
                            $recording_url = isset($res['url']) ? $res['url'] : '';
                        }
                    }
                } catch (Exception $e) {
                    DB::table('dev_debug')->insert(['sr_no' => 51, 'result' => "tokoutboundanswer error 1 call==" . $e->getMessage()]);
                }
                $ib_call_ins['recording_url'] = $recording_url;
                LeadCall::create($ib_call_ins);
            }

            $content = '<?xml version="1.0" encoding="UTF-8"?>
                        <Response>
                        </Response>';
            $return = response($content, 200)->header('Content-Type', 'text/xml');
            return $return;
        } catch (Exception $ex) {
            DB::table('dev_debug')->insert(['sr_no' => 51, 'result' => "tokoutboundanswer error call==" . $ex->getMessage()]);
        }
    }

    public function hangupRep(Request $request) {
        try {
            $Parameters = $request->all();
            DB::table('dev_debug')->insert(['sr_no' => 51, 'result' => "hangupRep call==" . json_encode($Parameters)]);
        } catch (Exception $ex) {
            DB::table('dev_debug')->insert(['sr_no' => 51, 'result' => "hangupRep error call==" . $ex->getMessage()]);
        }
    }

    public function callforwardtomain($phoneNumber, $cust_dial_number) {
        //dd($phoneNumber); <Speak>Thank You for calling Moving Specialist. This call will be recorded for quality and training purpose.</Speak>
        $country_code = Helper::checkServer()['country_code'];
        $callerID = $cust_dial_number;

        $content = '<?xml version="1.0" encoding="UTF-8"?>
                    <Response>
                        <Dial callerId="' . $cust_dial_number . '">
                            <Number>1' . $phoneNumber . '</Number>
                        </Dial>
                    </Response>';

        $response = '<Response>
                        <Dial  callerId="' . $callerID . '">
                            <Number>' . $country_code . $phoneNumber . '</Number>
                        </Dial>
                    </Response>'; //for outbound
        // Echo the Response
        header("Content-type: text/xml");
        DB::table('dev_debug')->insert(['sr_no' => 9999, 'result' => "callforwardtomain response==" . $response]);
        echo $response;
        exit;
        $return = response($content, 200)->header('Content-Type', 'text/xml');
        return $return;
        //echo $response;exit;
    }

    public function hangupAcceptCall(Request $request) {
        try {
            $Parameters = $request->all();
            $callUuid = "";
            $duration = 0;
            if (isset($Parameters['CallUUID'])) {
                $callUuid = $Parameters['CallUUID'];
            }
            if (isset($Parameters['RequestUUID'])) {
                $callUuid = $Parameters['RequestUUID'];
            }
            if (isset($Parameters['Duration'])) {
                $duration = $Parameters['Duration'];
            }
            DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 hangupAcceptCall HANGUP Inbound call==" . json_encode($Parameters)]);
            if ($callUuid != "") {
                $getLeadCallData = LeadCall::where('legA_call_uuid', $callUuid)->get()->toArray();
                $lead_call_id = 0;
                $date = date('Y-m-d H:i:s');
                DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 hangupAcceptCall ==" . json_encode($getLeadCallData)."==".$callUuid."==".$duration]);
                if (count($getLeadCallData) > 0) {
                    $lead_call_id = $getLeadCallData[0]['lead_call_id'];
                    $call_disposition_id = $getLeadCallData[0]['call_disposition_id'];
                    $lastInsertedId = $getLeadCallData[0]['lead_id'];
                    $user_id = $getLeadCallData[0]['user_id'];
                    $callType = $getLeadCallData[0]['call_type'];
                    $updateStatus = $updateDuration = array();
                    $updateStatus['cust_ended_time'] = $date;
                    DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 hangupAcceptCall ==" . json_encode($updateStatus)."==".$lead_call_id]);
                    LeadIbCall::where('lead_call_id', $lead_call_id)->update($updateStatus);
                    if($duration > 0){
                        $updateDuration['duration'] = $duration;
                    }
                    //Added By HJ On 27-07-2023 For Insert Lead In Buffer In day Time else in follow up next day 8:00 AM If Not Received By Rep Start
                    if($call_disposition_id == 15){
                        $hours_check_cron = date('G');
                        if ($hours_check_cron <= 9 || $hours_check_cron >= 20) { //In Night time insert in Follow up
                            $buffercontroller = new LeadCallBufferController();
                            if($hours_check_cron > 20){
                                $follow_up_date = date('Y-m-d', strtotime('+1 day', strtotime($date)));
                            }else{
                                $follow_up_date = date("Y-m-d");
                            }
                            $follow_up_date_time = $follow_up_date . ' 11:00:00';
                            $buffercontroller->saveFollowupentryWithDispositions($lastInsertedId, $follow_up_date_time, 0);
                        }else{ //In Day Time Insert In buffer Table
                            if($callType == 'inbound') {
                                $checkLead = Lead::where('lead_id', $lastInsertedId)->first();
                                if($checkLead) {
                                    if($checkLead->lead_type == 'normal') {
                                        $buffer = new CallsbufferCronController();
                                        $buffer->SaveToBufferViaPhoneonly($lastInsertedId,"","1");
                                    } else {
                                        $buffer = new CallsbufferCronController();
                                        $buffer->SaveToBufferViaPhoneonly($lastInsertedId);
                                    }
                                }
                            } else {
                                $buffer = new CallsbufferCronController();
                                $buffer->SaveToBufferViaPhoneonly($lastInsertedId);    
                            }
                            
                        }
                        $updateDuration['inbound_call_status'] = "not_answered";
                    }
                    //Added By HJ On 27-07-2023 For Insert Lead In Buffer In day Time else in follow up next day 8:00 AM If Not Received By Rep End
                    LeadCall::where('lead_call_id', $lead_call_id)->update($updateDuration);
                }else{
                    $updateDuration = array();
                    $updateDuration['inbound_call_status'] = "not_answered";
                    LeadCall::where('legA_call_uuid', $callUuid)->update($updateDuration);
                }
                RepActiveCall::where('call_uuid', $callUuid)->delete();
            }
            $content = '<Response><Speak>Thank you</Speak></Response>';
            header("Content-type: text/xml");
            echo $content;
            exit;
        } catch (Exception $ex) {
            DB::table('dev_debug')->insert(['sr_no' => 53, 'result' => "HANGUP Inbound call Exception==" . $ex->getMessage()]);
        }
        $content = '<Response>
            <Speak>Thank you</Speak>                
        </Response>';
        header("Content-type: text/xml");
        echo $content;
        exit;
    }

    public function CallRecord($callUuid) {
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $general_variable_2 = Helper::checkServer()['general_variable_2'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
        $plivoBaseURL = 'https://api.plivo.com/v1/Account/' . $decVariable1;
        $para = json_encode(array('time_limit' => '3600'));
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $plivoBaseURL . '/Call/' . $callUuid . '/Record/');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $para);
        curl_setopt($ch, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
        $headers = array();
        $headers[] = 'Content-Type: application/json';
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $result = curl_exec($ch);
        DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 SnapIt log CallRecord ==" . "===" . $plivoBaseURL . '/Call/' . $callUuid . '/Record/' . "====" . $callUuid . "===" . $result]);
        if (curl_errno($ch)) {
            echo 'Error:' . curl_error($ch);
        }
        curl_close($ch);
    }

    public function setTenDigitNumber($phone) {
        $phoneLength = strlen(trim($phone));
        if ($phoneLength > 10) {
            $removeChr = $phoneLength - 10;
            $phone = substr(trim($phone), $removeChr);
        }
        $phoneLength = strlen(trim($phone));
        if ($phoneLength != 10) {
            $oldphone = $phone;
            $phone = 9999999999;
            $this->createInboundLogs($phone, $phone, '123', $phoneLength . "===phoneLength");
        }
        return $phone;
    }

    public function createInboundLogs($fromnumber, $tonumber, $type, $log) {
        $log = array('from_number' => $fromnumber, 'to_number' => $tonumber, 'log_srno' => $type, 'log' => $log, 'created_at' => date("Y-m-d H:i:s"));
        LeadIbCallLog::create($log);
    }

    /**
     * Connect a sales rep to the conference when customer joins inbound conference
     */
    public function connectSalesRepToConference($lead_call_id, $lead_id) {
        try {
            $this->createInboundLogs('system', 'system', '42', 'Connecting sales rep to conference for lead_call_id: ' . $lead_call_id);

            // Get available sales rep (you may need to modify this logic based on your business rules)
            $availableRep = $this->getAvailableSalesRep($lead_id);

            if ($availableRep) {
                $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
                $endDecObj = new Helper;
                $general_variable_1 = Helper::checkServer()['general_variable_1'];
                $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
                $plivoBaseURL = 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/Call/';

                $fromNumber = "***********"; // System number
                $toNumber = "1" . $availableRep['phone']; // Sales rep phone number

                $data = array(
                    'from' => $fromNumber,
                    'to' => $toNumber,
                    'answer_url' => $matrixBaseURL_Outbound . '/conferencecall?lead_call_id=' . $lead_call_id,
                    'hangup_url' => ''
                );

                $data = json_encode($data);
                $response = $this->Calltocurl($plivoBaseURL, $data);

                $this->createInboundLogs('system', $availableRep['phone'], '43', 'Sales rep call initiated: ' . $response);

                return $response;
            } else {
                $this->createInboundLogs('system', 'system', '44', 'No available sales rep found for lead_id: ' . $lead_id);
            }
        } catch (Exception $ex) {
            $this->createInboundLogs('system', 'system', '45', 'Error connecting sales rep to conference: ' . $ex->getMessage());
        }
    }

    /**
     * Get available sales rep for the lead (simplified version - you may need to enhance this)
     */
    private function getAvailableSalesRep($lead_id) {
        try {
            // This is a simplified version. You may need to implement more complex logic
            // based on your business rules (availability, skills, workload, etc.)

            // For now, let's get the first available user with appropriate permissions
            // You should modify this query based on your actual requirements
            $availableRep = DB::table('users')
                ->where('status', 'active')
                ->where('user_type', 'sales_rep') // Adjust this condition based on your user types
                ->whereNotNull('phone')
                ->first();

            if ($availableRep) {
                return [
                    'user_id' => $availableRep->id,
                    'phone' => $availableRep->phone,
                    'name' => $availableRep->name
                ];
            }

            return null;
        } catch (Exception $ex) {
            $this->createInboundLogs('system', 'system', '46', 'Error getting available sales rep: ' . $ex->getMessage());
            return null;
        }
    }

    /**
     * Make curl call to Plivo API
     */
    public function Calltocurl($url, $datatran) {
        try {
            // Call The Customer
            $endDecObj = new Helper;
            $general_variable_1 = Helper::checkServer()['general_variable_1'];
            $general_variable_2 = Helper::checkServer()['general_variable_2'];
            $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
            $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
            $ch = curl_init($url);
            curl_setopt_array($ch, array(
                CURLOPT_RETURNTRANSFER => TRUE,
                CURLOPT_POSTFIELDS => $datatran,
                CURLOPT_USERPWD => $decVariable1 . ':' . $decVariable2,
                CURLOPT_FOLLOWLOCATION => 1,
                CURLOPT_HTTPHEADER => array('Content-type: application/json', 'Cache-Control:no-store', 'Content-Length: ' . strlen($datatran)),
                CURLOPT_TIMEOUT => 40
            ));
            $response = curl_exec($ch);
            DB::table('dev_debug')->insert(['sr_no' => 6, 'result' => "6 snapIt log Calltocurl call==" . $response]);
            curl_close($ch);
            return $response;
        } catch (Exception $ex) {
            $error = $ex->getMessage();
            DB::table('dev_debug')->insert(['sr_no' => 7, 'result' => "7 snapIt log Calltocurl call catch error==" . $error]);
        }
    }

}
