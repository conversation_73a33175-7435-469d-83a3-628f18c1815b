<?php

namespace App\Http\Controllers\Report;

use App\Http\Controllers\Controller;
use App\Models\Business\Business;
use App\Models\Business\BusinessCampaignPayment;
use App\Models\Business\BusinessCampaingnPaymentLog;
use App\Models\Business\BusinessCC;
use App\Models\Business\BusinessesCampaignEomBalance;
use App\Models\Business\StaxPayment;
use App\Models\Campaign\Campaign;
use App\Models\Lead\LeadRouting;
use App\Models\Lead\LeadTransferCall;
use App\Models\Outbound\LeadCall;
use App\Models\Master\MstChargeType;
use App\Models\Master\MstLeadCategory;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

class PaymentHistoryController extends Controller
{
    function __construct()
    {
        $this->middleware('permission:paymenthistoryreport', ['only' => ['paymentHistoryReport']]);
    }

    public function paymentHistoryReport(Request $request)
    {
        return view('reports.paymenthistory');
    }

    public function paymentHistoryReportData(Request $request)
    {
        $castrevenuecheckboxvalue = $request->revenuecheckboxvalue === "true" ? true : false;
        $castleadcheckboxvalue = $request->leadcheckboxvalue === "true" ? true : false;
        $leadStartDate = date('Y-m-d 00:00:00', strtotime($request->leadStartDate));
        $leadEndDate = date('Y-m-d 23:59:59', strtotime($request->leadEndDate));
        $revenueStartDate = date('Y-m-d 00:00:00', strtotime($request->revenueStartDate));
        $revenueEndDate = date('Y-m-d 23:59:59', strtotime($request->revenueEndDate));
        $summaryData = [];
        $allCredit = $allDebit = $allCCCharge = 0;
        $chargeType = $request->chargeType;
        $category = $request->category;
        $campCategory = "alldata";
        if ($castrevenuecheckboxvalue === true) {
            //dump(true);
            $chargeTypeCondtion = "";
            if ($chargeType != 'alldata') {
                $chargeTypeCondtion = " AND charge_type_id = " . $chargeType;
            }
            $queryStr = "SELECT 
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type = 'subscription' AND fatt_payment_id is NULL THEN amount_charge
                END) AS 'CCSCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type = 'subscription' AND fatt_payment_id is NOT NULL THEN amount_charge
                END) AS 'CCssystemCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type = 'subscription' AND fatt_payment_id is NOT NULL THEN convenience_fee
                END) AS 'CCssystemFeeCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type <> 'subscription' AND fatt_payment_id is NULL THEN amount_charge
                END) AS 'CCCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type <> 'subscription' AND fatt_payment_id is NOT NULL THEN amount_charge
                END) AS 'CCsystemCr',
                SUM(CASE
                    WHEN charge_type_id  = 1 AND charge_method_type <> 'subscription' AND fatt_payment_id is NOT NULL THEN convenience_fee
                END) AS 'CCsystemFeeCr',
                SUM(CASE
                    WHEN charge_type_id  = 7 THEN amount_charge
                END) AS 'RecurrentCr',
                SUM(CASE
                    WHEN charge_type_id  = 8 THEN amount_charge
                END) AS 'BadLeadCr',
                SUM(CASE
                    WHEN charge_type_id  = 9 THEN amount_charge
                END) AS 'OtherCr',
                SUM(CASE
                    WHEN charge_type_id  = 10 THEN amount_charge
                END) AS 'TransferToCr',
                SUM(CASE
                    WHEN charge_type_id  = 3 THEN amount_charge
                END) AS 'OtherDb',
                SUM(CASE
                    WHEN charge_type_id  = 4 THEN amount_charge
                END) AS 'RefundDb',
                SUM(CASE
                    WHEN charge_type_id  = 5 THEN amount_charge
                END) AS 'ChargeBackDb',
                SUM(CASE
                    WHEN charge_type_id  = 6 THEN amount_charge
                END) AS 'TransferFromDb',
                SUM(CASE
                    WHEN charge_type_id  = 2 THEN amount_charge
                END) AS 'BankCr',
                SUM(CASE
                    WHEN charge_type_id  = 11 AND charge_method_type <> 'subscription' THEN amount_charge
                END) AS 'VoidPaymentCr'
            FROM
            business_campaign_payment where is_charge_approved = 'yes' AND `created_at` BETWEEN '" . $revenueStartDate . "' AND '" . $revenueEndDate . "' " . $chargeTypeCondtion . "";
            $dataSummary1 = DB::select($queryStr);
            foreach ($dataSummary1 as $summary) {
                //echo "<pre>";print_r($dataSummary1);die;
                $summaryData['CCSCr'] = (empty($summary->CCSCr) || $summary->CCSCr == 0) ? '--' : '$' . sprintf('%0.2f', $summary->CCSCr);
                $summaryData['CCssystemC'] = (empty($summary->CCssystemCr) || $summary->CCssystemCr == 0) ? '--' : '$' . sprintf('%0.2f',$summary->CCssystemCr);
                $summaryData['CCssystemCr'] = (empty($summary->CCssystemCr) || $summary->CCssystemCr == 0) ? '--' : '$' . sprintf('%0.2f',$summary->CCssystemCr + $summary->CCssystemFeeCr);
                $summaryData['CCCr'] = (empty($summary->CCCr) || $summary->CCCr == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary->CCCr);
                $summaryData['CCsystemC'] = (empty($summary->CCsystemCr) || $summary->CCsystemCr == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary->CCsystemCr);
                $summaryData['CCsystemCr'] = (empty($summary->CCsystemCr) || $summary->CCsystemCr == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary->CCsystemCr + $summary->CCsystemFeeCr);
                $summaryData['RecurrentCr'] = (empty($summary->RecurrentCr) || $summary->RecurrentCr == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary->RecurrentCr);
                $summaryData['BadLeadCr'] = (empty($summary->BadLeadCr) || $summary->BadLeadCr == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary->BadLeadCr);
                $summaryData['OtherCr'] = (empty($summary->OtherCr) || $summary->OtherCr == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary->OtherCr);
                $summaryData['TransferToCr'] = (empty($summary->TransferToCr) || $summary->TransferToCr == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary->TransferToCr);
                $summaryData['OtherDb'] = (empty($summary->OtherDb) || $summary->OtherDb == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary->OtherDb);
                $summaryData['RefundDb'] = (empty($summary->RefundDb) || $summary->RefundDb == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary->RefundDb);
                $summaryData['ChargeBackDb'] = (empty($summary->ChargeBackDb) || $summary->ChargeBackDb == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary->ChargeBackDb);
                $summaryData['TransferFromDb'] = (empty($summary->TransferFromDb) || $summary->TransferFromDb == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary->TransferFromDb);
                $summaryData['BankCr'] = (empty($summary->BankCr) || $summary->BankCr == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary->BankCr);
                $summaryData['VoidPaymentCr'] = (empty($summary->VoidPaymentCr) || $summary->VoidPaymentCr == 0) ? '--' : '$' . $this->setTwoDecimalPoint( $summary->VoidPaymentCr);
                $allCredit = $summary->CCCr + $summary->CCsystemCr + $summary->RecurrentCr + $summary->BadLeadCr + $summary->OtherCr + $summary->TransferToCr + $summary->BankCr;
                $allDebit = $summary->OtherDb + $summary->RefundDb + $summary->ChargeBackDb + $summary->TransferFromDb + $summary->VoidPaymentCr;
                $allCCCharge = $summary->CCCr + $summary->CCsystemCr + $summary->CCsystemFeeCr - ($summary->RefundDb + $summary->VoidPaymentCr);
            }
        } else {
            //dump(false);
            $summaryData['CCCr'] = '--';
            $summaryData['CCsystemC'] = '--';
            $summaryData['CCsystemCr'] = '--';
            $summaryData['RecurrentCr'] = '--';
            $summaryData['BadLeadCr'] = '--';
            $summaryData['OtherCr'] = '--';
            $summaryData['TransferToCr'] = '--';
            $summaryData['OtherDb'] = '--';
            $summaryData['RefundDb'] = '--';
            $summaryData['ChargeBackDb'] = '--';
            $summaryData['TransferFromDb'] = '--';
            $summaryData['BankCr'] = '--';
            $summaryData['VoidPaymentCr'] = '--';
        }

        //for lead sold revenue
        // $mainQuery = "SELECT SUM(A.payout) as totalpayout,A.*,B.campaign_id,B.lead_type_id,B.call_type,B.campaign_type_id,C.lead_id,C.created_at FROM `lead_routing` as A LEFT JOIN campaign as B ON A.campaign_id= B.campaign_id LEFT JOIN `lead` as C ON A.lead_id=C.lead_id WHERE A.route_status='sold' AND A.created_at BETWEEN '" . $leadStartDate . "' AND '" . $leadEndDate . "'  AND C.created_at BETWEEN '" . $revenueStartDate . "' AND '" . $revenueEndDate . "'";
        // $leadSoldQuery = " AND B.lead_type_id= 1";
        // //DB::enableQueryLog();
        // $dataSummary2 = DB::select($mainQuery . $leadSoldQuery);
        // // $query = DB::getQueryLog();
        // // dump($query);
        // foreach ($dataSummary2 as $summary) {
        //     $summaryData['leadCampaignSoldDb'] = (empty($summary->totalpayout) || $summary->totalpayout == 0) ? '--' : '$' . sprintf('%0.2f', $summary->totalpayout);
        //     $allDebit += $summary->totalpayout;
        // }

        if ($castrevenuecheckboxvalue === false && $castleadcheckboxvalue === false) {
            $summaryData['leadCampaignSoldDb'] = '--';
            $summaryData['callCampignSoldOutDb'] = '--';
            $summaryData['callCampignSoldInDb'] = '--';
            $summaryData['dialerCampignSoldDb'] = '--';
            $summaryData['organicCampignSoldDb'] = '--';
            $summaryData['marketplaceCampignSoldDb'] = '--';
        } else {
            //DB::enableQueryLog();
            $mainQuery = LeadRouting::leftJoin('campaign as B', 'lead_routing.campaign_id', 'B.campaign_id')
                ->leftJoin('lead AS C', 'lead_routing.lead_id', 'C.lead_id')
                ->select(
                    DB::raw('SUM(lead_routing.payout) as totalpayout'),
                    'lead_routing.*',
                    'B.campaign_id',
                    'B.lead_type_id',
                    'B.call_type',
                    'B.campaign_type_id',
                    'C.lead_id',
                    'C.created_at'
                )
                //->where('C.lead_type', '!=', 'phone')
                ->where('lead_routing.route_status', 'sold')
                ->where('B.lead_type_id', 1)
                ->whereIn('B.campaign_type_id', [1, 2, 6, 8, 9]);
            if ($castrevenuecheckboxvalue === true) {
                $mainQuery->whereBetween('lead_routing.created_at', [$revenueStartDate, $revenueEndDate]);
            }
            if ($castleadcheckboxvalue === true) {
                $mainQuery->whereBetween('C.created_at', [$leadStartDate, $leadEndDate]);
            }
            if ($category != 'alldata') {
                $mainQuery->where('C.lead_category_id', $category);
            }
            //$leadSoldQuery = $mainQuery->where('B.lead_type_id', 1);
            $leadSoldQuery = $mainQuery;
            $dataSummary2 = $leadSoldQuery->get()->toArray();
            // $query = DB::getQueryLog();
            // Log::info($query);
            foreach ($dataSummary2 as $summary) {
                $summaryData['leadCampaignSoldDb'] = (empty($summary['totalpayout']) || $summary['totalpayout'] == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary['totalpayout']);
                $allDebit += $summary['totalpayout'];
            }

            // $dateCondition = '';
            // if($castrevenuecheckboxvalue && $castleadcheckboxvalue){
            //     $dateCondition .= "AND ((ltc.created_at BETWEEN '$revenueStartDate' AND '$revenueEndDate') AND (l.created_at BETWEEN '$leadStartDate' AND '$leadEndDate'))";
            // }
            // else if($castrevenuecheckboxvalue){
            //     $dateCondition .= "AND (ltc.created_at BETWEEN '$revenueStartDate' AND '$revenueEndDate')";
            // } else if ($castleadcheckboxvalue) {
            //     $dateCondition .= "AND (l.created_at BETWEEN '$leadStartDate' AND '$leadEndDate')";
            // }

            // $categoryCondition = "";
            // if($category != 'alldata'){
            //     $categoryCondition = " AND l.lead_category_id = $category";
            // }

            //$callCampignSoldOutQuery = "SELECT SUM(aa.pay) as totaloutpayout FROM (SELECT lr.lead_id AS lid, avg(lr.payout) AS pay FROM lead_routing AS lr INNER JOIN (SELECT lead_id FROM lead_call WHERE call_type = 'outbound' GROUP BY lead_id) as lc ON lr.lead_id = lc.lead_id LEFT JOIN `lead` AS l ON l.lead_id = lr.lead_id where l.lead_id = lc.lead_id AND lr.route_status = 'sold' and lr.payout > 0 $dateCondition $categoryCondition GROUP BY lr.lead_id, lr.campaign_id) AS aa";
            // $callCampignSoldOutQuery  = "SELECT SUM(ltc.payout) as totaloutpayout FROM lead_transfer_call AS ltc JOIN `lead` AS l ON l.lead_id = ltc.lead_id WHERE ltc.calls_type = 'outbound' AND ltc.transfer_type = 'transfer' $dateCondition $categoryCondition";
            //Log::info($callCampignSoldOutQuery);
            $mainQuery2 = LeadTransferCall::join('lead_call as lc', 'lc.lead_call_id', 'lead_transfer_call.lead_call_id')
            ->join('lead AS l', 'l.lead_id', 'lc.lead_id')
            ->join('campaign AS cmp','cmp.campaign_id','lead_transfer_call.campaign_id') 
            ->select(DB::raw('SUM(lead_transfer_call.payout) as totaloutpayout'))
            ->whereIn('cmp.campaign_type_id',[1, 2, 6, 8, 9])
            ->where('lead_transfer_call.calls_type', 'outbound');
            if ($castrevenuecheckboxvalue === true) {
                $mainQuery2->whereBetween('lc.created_at', [$revenueStartDate, $revenueEndDate]);
            }
            if ($castleadcheckboxvalue === true) {
                $mainQuery2->whereBetween('l.created_at', [$leadStartDate, $leadEndDate]);
            }
            if ($category != 'alldata') {
                $mainQuery2->where('l.lead_category_id', $category);
            }
            $dataSummary3 = $mainQuery2->get()->toArray();
            // $query = DB::getQueryLog();
            // Log::info($query);
            foreach ($dataSummary3 as $summary) {
                $summaryData['callCampignSoldOutDb'] = (empty($summary['totaloutpayout']) || $summary['totaloutpayout'] == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary['totaloutpayout']);
                $allDebit += $summary['totaloutpayout'];
            }

            // $callCampignSoldInQuery = "SELECT SUM(aa.pay) as totalinpayout FROM (SELECT lr.lead_id AS lid, avg(lr.payout) AS pay FROM lead_routing AS lr INNER JOIN (SELECT lead_id FROM lead_call WHERE call_type = 'inbound' GROUP BY lead_id) as lc ON lr.lead_id = lc.lead_id LEFT JOIN `lead` AS l ON l.lead_id = lr.lead_id where l.lead_id = lc.lead_id AND lr.route_status = 'sold' and lr.payout > 0 $dateCondition $categoryCondition GROUP BY lr.lead_id, lr.campaign_id) AS aa";
            // $callCampignSoldInQuery  = "SELECT SUM(ltc.payout) as totalinpayout FROM lead_transfer_call AS ltc JOIN `lead` AS l ON l.lead_id = ltc.lead_id WHERE ltc.calls_type = 'inbound' AND ltc.transfer_type = 'transfer' $dateCondition $categoryCondition";
            $mainQuery3 = LeadTransferCall::join('lead_call as lc', 'lc.lead_call_id', 'lead_transfer_call.lead_call_id')
            ->join('lead AS l', 'l.lead_id', 'lc.lead_id')
            ->join('campaign AS cmp', 'cmp.campaign_id', 'lead_transfer_call.campaign_id') 
            ->select(DB::raw('SUM(lead_transfer_call.payout) as totalinpayout'))
            ->whereIn('cmp.campaign_type_id', [1, 2, 6, 8, 9])
            ->where('lead_transfer_call.calls_type', 'inbound');
            if ($castrevenuecheckboxvalue === true) {
                $mainQuery3->whereBetween('lc.created_at', [$revenueStartDate, $revenueEndDate]);
            }
            if ($castleadcheckboxvalue === true) {
                $mainQuery3->whereBetween('l.created_at', [$leadStartDate, $leadEndDate]);
            }
            if ($category != 'alldata') {
                $mainQuery3->where('l.lead_category_id', $category);
            }
            $dataSummary4 = $mainQuery3->get()->toArray();
            foreach ($dataSummary4 as $summary) {
                $summaryData['callCampignSoldInDb'] = (empty($summary['totalinpayout']) || $summary['totalinpayout'] == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary['totalinpayout']);
                $allDebit += $summary['totalinpayout'];
            }

            $dialerCampignSoldQuery = LeadRouting::leftJoin('campaign as B', 'lead_routing.campaign_id', 'B.campaign_id')
            ->leftJoin('lead AS C', 'lead_routing.lead_id', 'C.lead_id')
            ->select(
                DB::raw('SUM(lead_routing.payout) as totalpayout'),
                'lead_routing.*',
                'B.campaign_id',
                'B.lead_type_id',
                'B.call_type',
                'B.campaign_type_id',
                'C.lead_id',
                'C.created_at'
            )
            //->where('C.lead_type', '!=', 'phone')
            ->where('lead_routing.route_status', 'sold')
            ->where('B.lead_type_id', 2)
            ->where('B.campaign_type_id', 3);
            if ($castrevenuecheckboxvalue === true) {
                $dialerCampignSoldQuery->whereBetween('lead_routing.created_at', [$revenueStartDate, $revenueEndDate]);
            }
            if ($castleadcheckboxvalue === true) {
                $dialerCampignSoldQuery->whereBetween('C.created_at', [$leadStartDate, $leadEndDate]);
            }
            if ($category != 'alldata') {
                $dialerCampignSoldQuery->where('C.lead_category_id', $category);
            }
            $dataSummary5 = $dialerCampignSoldQuery->get()->toArray();
            foreach ($dataSummary5 as $summary) {
                $summaryData['dialerCampignSoldDb'] = (empty($summary['totalpayout']) || $summary['totalpayout'] == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary['totalpayout']);
                $allDebit += $summary['totalpayout'];
            }
            
            $organicCampignSoldDbQuery = LeadRouting::leftJoin('campaign as B', 'lead_routing.campaign_id', 'B.campaign_id')
            ->leftJoin('lead AS C', 'lead_routing.lead_id', 'C.lead_id')
            ->select(
                DB::raw('SUM(lead_routing.payout) as totalpayout'),
                'lead_routing.*',
                'B.campaign_id',
                'B.lead_type_id',
                'B.call_type',
                'B.campaign_type_id',
                'C.lead_id',
                'C.created_at'
            )
            //->where('C.lead_type', '!=', 'phone')
            ->where('lead_routing.route_status', 'sold')
            ->where('B.campaign_type_id', 4);
            if ($castrevenuecheckboxvalue === true) {
                $organicCampignSoldDbQuery->whereBetween('lead_routing.created_at', [$revenueStartDate, $revenueEndDate]);
            }
            if ($castleadcheckboxvalue === true) {
                $organicCampignSoldDbQuery->whereBetween('C.created_at', [$leadStartDate, $leadEndDate]);
            }
            if ($category != 'alldata') {
                $organicCampignSoldDbQuery->where('C.lead_category_id', $category);
            }
            $dataSummary6 = $organicCampignSoldDbQuery->get()->toArray();
            foreach ($dataSummary6 as $summary) {
                $summaryData['organicCampignSoldDb'] = (empty($summary['totalpayout']) || $summary['totalpayout'] == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary['totalpayout']);
                $allDebit += $summary['totalpayout'];
            }


            $marketplaceCampignSoldQuery = LeadRouting::leftJoin('campaign as B', 'lead_routing.campaign_id', 'B.campaign_id')
            ->leftJoin('lead AS C', 'lead_routing.lead_id', 'C.lead_id')
            ->select(
                DB::raw('SUM(lead_routing.payout) as totalpayout'),
                'lead_routing.*',
                'B.campaign_id',
                'B.lead_type_id',
                'B.call_type',
                'B.campaign_type_id',
                'C.lead_id',
                'C.created_at'
            )
            //->where('C.lead_type', '!=', 'phone')
            ->where('lead_routing.route_status', 'sold')
            ->where('B.lead_type_id', 1)
            ->where('B.campaign_type_id', 7);
            if ($castrevenuecheckboxvalue === true) {
                $marketplaceCampignSoldQuery->whereBetween('lead_routing.created_at', [$revenueStartDate, $revenueEndDate]);
            }
            if ($castleadcheckboxvalue === true) {
                $marketplaceCampignSoldQuery->whereBetween('C.created_at', [$leadStartDate, $leadEndDate]);
            }
            if ($category != 'alldata') {
                $marketplaceCampignSoldQuery->where('C.lead_category_id', $category);
            }
            $dataSummary6 = $marketplaceCampignSoldQuery->get()->toArray();
            foreach ($dataSummary6 as $summary) {
                $summaryData['marketplaceCampignSoldDb'] = (empty($summary['totalpayout']) || $summary['totalpayout'] == 0) ? '--' : '$' . $this->setTwoDecimalPoint($summary['totalpayout']);
                $allDebit += $summary['totalpayout'];
            }
        }

        $summaryData['allCredit'] = (empty($allCredit) || $allCredit == 0) ? '--' : '$' . $this->setTwoDecimalPoint($allCredit);
        $summaryData['allDebit'] = (empty($allDebit) || $allDebit == 0) ? '--' : '$' . $this->setTwoDecimalPoint($allDebit);
        $summaryData['allCCCharge'] = (empty($allCCCharge) || $allCCCharge == 0) ? '--' : '$' . $this->setTwoDecimalPoint($allCCCharge);
        $summaryData['allCredit_val'] = sprintf('%0.2f', $allCredit);
        $summaryData['allDebit_val'] = sprintf('%0.2f', $allDebit);
        $summaryData['allCCCharge_val'] = sprintf('%0.2f', $allCCCharge);

        $busall = Business::pluck('business_id')->toArray();
        //echo "<pre>";print_r($busall);die;
        if (!empty($request->get('category'))) {
            if ($category == 'alldata') {
                //$busall = Campaign::whereIn('business_id', $busall)->pluck('business_id');
            } else {
                $busall = Campaign::whereIn('business_id', $busall)->where('lead_category_id', $category)->pluck('business_id')->toArray();
            }
        } else {
            $busall = Campaign::whereIn('business_id', $busall)->where('lead_category_id', $category)->pluck('business_id')->toArray();
        }
        if(count($busall) > 0){
            $busall = array_unique($busall);
        }
        //echo "<pre>";print_r($busall);die;
        $sdate = $request->revenueStartDate;
        $edate = date('Y-m-d', strtotime('+1 day', strtotime($request->revenueEndDate)));
        /*$opeing_blanace = BusinessesCampaignEomBalance::where('date', $sdate)->where(function ($q) use ($busall, $all_campaign_id) {
            $q->whereIn('business_id', $busall); // ->orWhereIn('businesses_campaign_id', $all_campaign_id)
        })->sum('balance');*/

        //$next_day = date('Y-m-d', strtotime('+1 day', strtotime($edate)));
        $opeing_blanace = BusinessesCampaignEomBalance::where('date', $sdate)->whereIn('business_id', $busall)->sum('balance');
        $ending_blanace = BusinessesCampaignEomBalance::where('date', $edate)->whereIn('business_id', $busall)->sum('balance');
        /*$ending_blanace = BusinessesCampaignEomBalance::where('date', $edate)->where(function ($q) use ($busall, $all_campaign_id) {
            $q->whereIn('business_id', $busall); // ->orWhereIn('businesses_campaign_id', $all_campaign_id)
        })->sum('balance');*/

        $summaryData['opening_balance'] = (empty($opeing_blanace) || $opeing_blanace == 0) ? '--' : '$' . $this->setTwoDecimalPoint($opeing_blanace);
        $summaryData['ending_balance'] = (empty($ending_blanace) || $ending_blanace == 0) ? '--' : '$' . $this->setTwoDecimalPoint($ending_blanace);
        $summaryData['opening_balance_val'] = sprintf('%0.2f', $opeing_blanace);
        $summaryData['ending_balance_val'] = sprintf('%0.2f', $ending_blanace);

        $summaryData['diff_balance_val'] = sprintf('%0.2f', ($summaryData['opening_balance_val'] + $summaryData['allCredit_val']) - $summaryData['allDebit_val']);


        /*$total_Camp_balance = $total_Camp_balance1 = 0;
        $businessesRecords = Business::get();
        $businessIdArr = $businessCreditArr = array();
        foreach ($businessesRecords as $key1 => $value1) {
            $businessIdArr[] = $value1->business_id;
        }
        //echo "<pre>";print_r($businessIdArr);die;
        if ($campCategory == 'alldata') {
            $campData = Campaign::whereIn('business_id', $businessIdArr)->get(['business_id', 'credit_available', 'credit_reserved'])->toArray();
        } else {
            $campData = Campaign::whereIn('business_id', $businessIdArr)->where('lead_category_id', $campCategory)->get(['business_id', 'credit_available', 'credit_reserved'])->toArray();
        }
        //echo "<pre>";print_r($campData);die;
        for ($g = 0; $g < count($campData); $g++) {
            if (isset($businessCreditArr[$campData[$g]['business_id']]['credit_available'])) {
                $businessCreditArr[$campData[$g]['business_id']]['credit_available'] += $campData[$g]['credit_available'];
            } else {
                $businessCreditArr[$campData[$g]['business_id']]['credit_available'] = $campData[$g]['credit_available'];
            }
            if (isset($businessCreditArr[$campData[$g]['business_id']]['credit_reserved'])) {
                $businessCreditArr[$campData[$g]['business_id']]['credit_reserved'] += $campData[$g]['credit_reserved'];
            } else {
                $businessCreditArr[$campData[$g]['business_id']]['credit_reserved'] = $campData[$g]['credit_reserved'];
            }
        }
        //echo "<pre>";print_r($businessCreditArr);die;
        foreach ($businessesRecords as $key => $value) {
            $current_campaign_balance = $current_campaign_balance1 = 0;
            if (isset($businessCreditArr[$value->id]['credit_available'])) {
                $current_campaign_balance = $businessCreditArr[$value->id]['credit_available'];
            }
            if (isset($businessCreditArr[$value->id]['credit_reserved'])) {
                $current_campaign_balance1 = $businessCreditArr[$value->id]['credit_reserved'];
            }
            $total_Camp_balance += $current_campaign_balance + $value->credit_available;
            $total_Camp_balance1 += $current_campaign_balance1 + $value->credit_reserved;
        }
        $current_balance = $total_Camp_balance;
        $current_balance_new = $total_Camp_balance1 + $current_balance;
        $summaryData['current_balance_new'] = '$' . $this->setTwoDecimalPoint($current_balance_new);*/
        //Added By HJ On 14-07-2023 For Get Campaign and Business Current Balance Start
        $getCampaignBalance = Campaign::select(DB::raw("SUM(credit_available) as camp_balance"))->get()->toArray();
        $getBusinessBalance = Business::select(DB::raw("SUM(credit_available) as bus_balance"))->get()->toArray();
        $campCurrentBal = $busCurrentBal = 0;
        if(count($getCampaignBalance) > 0){
            $campCurrentBal = $getCampaignBalance[0]['camp_balance'];
        }
        if(count($getBusinessBalance) > 0){
            $busCurrentBal = $getBusinessBalance[0]['bus_balance'];
        }
        $summaryData['current_balance_new'] = '$' . $this->setTwoDecimalPoint($campCurrentBal+$busCurrentBal);
        //Added By HJ On 14-07-2023 For Get Campaign and Business Current Balance End
        if ($castleadcheckboxvalue === true) {
            $summaryData['leadStartDate'] = $leadStartDate;
            $summaryData['leadEndDate'] = $leadEndDate;
        }
        if($castrevenuecheckboxvalue === true){
            $summaryData['revenueStartDate'] = $revenueStartDate;
            $summaryData['revenueEndDate'] = $revenueEndDate;
        }
        $summaryData['timezone'] = date_default_timezone_get();
        $summaryData['currentDate'] = date('Y-m-d H:i:s');
        return response()->json($summaryData);
    }
    
    public function paymentHistoryReportDataTable(Request $request)
    {
        $castrevenuecheckboxvalue = $request->revenuecheckboxvalue === "true" ? true : false;
        if ($castrevenuecheckboxvalue === true && !empty($request->get('chargeType'))) {
            $chargeType = $request->get('chargeType');
            $campaignCategory = "alldata";
            $revenueStartDate = date('Y-m-d 00:00:00', strtotime($request->revenueStartDate));
            $revenueEndDate = date('Y-m-d 23:59:59', strtotime($request->revenueEndDate));
            

            $campaignIdArray = $finalData = [];
            //$query = "SELECT * from business_campaign_payment where created_at between '" . $startDate . " 00:00:00' and '" . $endDate . " 23:59:59' " . 
            //DB::enableQueryLog();
            if ($chargeType == 'alldata') {
                $paymentDetail = BusinessCampaignPayment::whereBetween('created_at',[$revenueStartDate,$revenueEndDate])->get();
            }
            else {
                // $query = "SELECT * from business_campaign_payment where created_at between '" . $startDate . " 00:00:00' and '" . $endDate . " 23:59:59' and charge_type_id = " . $chargeType . " " . $appendQuery;
                $paymentDetail = BusinessCampaignPayment::whereBetween('created_at', [$revenueStartDate, $revenueEndDate])->where('charge_type_id',$chargeType)->get();
            }
            $query = DB::getQueryLog();
            //dump($query);
            //dd($paymentDetail->toArray());

            $paymentIdArray = $businessIdArray = $campaignIdArray = $businessArray = $campaignArray = $userIdArray = $userNameArray = $cardIdArray = $cardArray = $chargeTypeArray = $categoryArray = $messageArray = $transactionIdArray = $settlementIdArray = $batchIdArray = array();
            foreach ($paymentDetail as $payment) {
                if ($payment->business_campaign_payment_id > 0 && !in_array($payment->business_campaign_payment_id, $paymentIdArray)) {
                    $paymentIdArray[] = $payment->business_campaign_payment_id;
                }
                if ($payment->business_id > 0 && !in_array($payment->business_id, $businessIdArray)) {
                    $businessIdArray[] = $payment->business_id;
                }
                if ($payment->charge_user_id > 0 && !in_array($payment->charge_user_id, $businessIdArray)) {
                    $businessIdArray[] = $payment->charge_user_id;
                }
                if ($payment->campaign_id > 0 && !in_array($payment->campaign_id, $campaignIdArray)) {
                    $campaignIdArray[] = $payment->campaign_id;
                }
                if (($payment->charge_method_type == "snapituser" || $payment->charge_method_type == "subscription") && !in_array($payment->charge_user_id, $userIdArray)) {
                    $userIdArray[] = $payment->charge_user_id;
                }
                if ($payment->business_cc_id > 0 && !in_array($payment->business_cc_id, $cardIdArray)) {
                    $cardIdArray[] = $payment->business_cc_id;
                }
                if (!in_array($payment->fatt_payment_id, $transactionIdArray)) {
                    $transactionIdArray[] = $payment->fatt_payment_id;
                }
            }

            $logDetail = BusinessCampaingnPaymentLog::whereIn('business_campaign_payment_id', $paymentIdArray)->get()->toArray();
            for ($s = 0; $s < count($logDetail); $s++) {
                $response = json_decode($logDetail[$s]['response'], true);
                if (isset($response['success']) && $response['success'] == true) {
                    $messageArray[$logDetail[$s]['business_campaign_payment_id']] = 'Approved';
                } else {
                    if (isset($response['message'])) {
                        $messageArray[$logDetail[$s]['business_campaign_payment_id']] = $response['message'];
                    } else {
                        $messageArray[$logDetail[$s]['business_campaign_payment_id']] = preg_replace('/[^a-zA-Z0-9\s]/', ' ', $logDetail[$s]['response']);
                    }
                }
            }

            $staxDetail = StaxPayment::whereIn('fatt_transaction_id', $transactionIdArray)->get()->toArray();
            for ($s = 0; $s < count($staxDetail); $s++) {
                $settlementIdArray[$staxDetail[$s]['fatt_transaction_id']] = $staxDetail[$s]['fatt_settlement_id'];
                $batchIdArray[$staxDetail[$s]['fatt_transaction_id']] = $staxDetail[$s]['display_batch_id'];
            }

            $campaignDetail = Campaign::whereIn('campaign_id', $campaignIdArray)->get(['campaign_id', 'business_id', 'campaign_name', 'lead_category_id'])->toArray();
            for ($b = 0; $b < count($campaignDetail); $b++) {
                $campaignArray[$campaignDetail[$b]['campaign_id']] = $campaignDetail[$b];
                if (!in_array($campaignDetail[$b]['business_id'], $businessIdArray)) {
                    $businessIdArray[] = $campaignDetail[$b]['business_id'];
                }
            }
            //echo "<pre>";print_r($campaignArr);die;
            $businessDetail = Business::whereIn('business_id', $businessIdArray)->get(['business_id', 'business_name'])->toArray();
            for ($c = 0; $c < count($businessDetail); $c++) {
                $businessArray[$businessDetail[$c]['business_id']] = $businessDetail[$c]['business_name'];
            }
            $userDetail = User::whereIn('id', $userIdArray)->get(['id', 'name'])->toArray();
            for ($u = 0; $u < count($userDetail); $u++) {
                $userNameArray[$userDetail[$u]['id']] = $userDetail[$u]['name'];
            }
            $cardDetail = BusinessCC::whereIn('business_cc_id', $cardIdArray)->get(['business_cc_id', 'card'])->toArray();
            for ($g = 0; $g < count($cardDetail); $g++) {
                $cardArray[$cardDetail[$g]['business_cc_id']] = $cardDetail[$g]['card'];
            }

            $chargeTypeDetail = MstChargeType::get(['charge_type_id', 'charge_type']);
            for ($ct = 0; $ct < count($chargeTypeDetail); $ct++) {
                $chargeTypeArray[$chargeTypeDetail[$ct]['charge_type_id']] = $chargeTypeDetail[$ct]['charge_type'];
            }

            $categoryDetail = MstLeadCategory::get(['lead_category_id', 'lead_category_name']);
            for ($ct = 0; $ct < count($categoryDetail); $ct++) {
                $categoryArray[$categoryDetail[$ct]['lead_category_id']] = $categoryDetail[$ct]['lead_category_name'];
            }

            foreach ($paymentDetail as $payment) {
                $data = [];
                $data['id'] = $payment->business_campaign_payment_id;
                $businessIdCheck = $payment->business_id;
                $data['businessId'] = ($payment->business_id == "" || $payment->business_id == 0) ? '--' : $payment->business_id;
                $data['newbusinessId'] = $payment->business_id;
                $data['campaignId'] = '--';
                $data['campaignName'] = '--';
                $categoryName = '--';
                $category = "0";
                if ($payment->campaign_id > 0) {
                    $data['campaignId'] = $payment->campaign_id;
                    if (isset($campaignArray[$payment->campaign_id])) {
                        $data['campaignName'] = $campaignArray[$payment->campaign_id]['campaign_name'];
                        $categoryId = $campaignArray[$payment->campaign_id]['lead_category_id'];
                        //echo $category."==".$payment->campaign_id."<br>";
                        $categoryName = $categoryArray[$categoryId];
                        $businessIdCheck = $campaignArray[$payment->campaign_id]['business_id'];
                    }
                }
                $data['campaignCategory'] = $categoryName;
                $data['businessName'] = '';
                if ($businessIdCheck > 0) {
                    $data['businessName'] = $businessArray[$businessIdCheck];
                }
                $data['beforeCharge'] = ($payment->balance_before_charge == 0) ? '--' : '$' . $this->setTwoDecimalPoint($payment->balance_before_charge);
                $data['amountCharge'] = ($payment->amount_charge == 0) ? '--' : '$' . $this->setTwoDecimalPoint($payment->amount_charge);
                $data['afterCharge'] = ($payment->balance_after_charge == 0) ? '--' : '$' . $this->setTwoDecimalPoint($payment->balance_after_charge);
                $data['totalCharge'] = (empty($payment->convenience_fee) || $payment->convenience_fee == 0) ? '--' : '$' . $this->setTwoDecimalPoint($payment->amount_charge + $payment->convenience_fee);
                $data['chargeType'] = ($payment->charge_type_id > 0) ? $chargeTypeArray[$payment->charge_type_id] : "--";
                $data['chargeMethod'] = "--";
                if ($payment->charge_method_type == "snapituser") {
                    $chargeMethod = "";
                    if ($payment->charge_method == "recurrent") {
                        $chargeMethod = " (Recurrent)";
                    }
                    if (isset($userNameArray[$payment->charge_user_id])) {
                        $data['chargeMethod'] = $userNameArray[$payment->charge_user_id] . $chargeMethod;
                    }
                } else if ($payment->charge_method_type == "subscription") {
                    $chargeMethod = " (Subscription)";
                    if ($payment->charge_method == "recurrent") {
                        $chargeMethod = " (Subscription Recurrent)";
                    }
                    if (isset($userNameArray[$payment->charge_user_id])) {
                        $data['chargeMethod'] = $userNameArray[$payment->charge_user_id] . $chargeMethod;
                    }
                } else {
                    if (isset($businessArray[$payment->charge_user_id])) {
                        $chargeMethodType = "";
                        if ($payment->charge_method_type == "moverinterfaceuser") {
                            $chargeMethodType = " (Dashboard)";
                        } else if ($payment->charge_method_type == "moveremailsms") {
                            $chargeMethodType = " (Email/SMS)";
                        } else if ($payment->charge_method_type == "contract") {
                            $chargeMethodType = " (Contract)";
                        } else if ($payment->charge_method_type == "cardcontract") {
                            $chargeMethodType = " (Card Contract)";
                        }else if ($payment->charge_method_type == "lowfund") {
                            $chargeMethodType = " (Lowfund Email/SMS)";
                        }
                        $data['chargeMethod'] = $businessArray[$payment->charge_user_id] . $chargeMethodType;
                    }
                }
                $time = strtotime($payment->created_at);
                $month = date("m", $time);
                $date = date("d", $time);

                $dateTime = strtotime($payment->created_at);
                $hour = date("h", $dateTime);
                $minute = date("i", $dateTime);
                $marid = date("a", $dateTime);
                // $data['date'] = $month . '/' . $date;
                // $data['time'] = $hour . ':' . $minute . ' ' . strtoupper($marid);
                $data['date'] = date('m/d/Y H:i:s',$dateTime);
                
                $data['transactionId'] = empty($payment->fatt_payment_id) ? '--' : $payment->fatt_payment_id;
                $data['settlementId'] = $settlementIdArray[$payment->fatt_payment_id] ?? '--';
                $data['batchId'] = $batchIdArray[$payment->fatt_payment_id] ?? '--';
                $data['isNotSettlement'] = 0;
                $data['chargeTypeId'] = $payment->charge_type_id;
                if (isset($settlementIdArray[$payment->fatt_payment_id]) && isset($batchIdArray[$payment->fatt_payment_id])) {
                    $data['isNotSettlement'] = 1;
                }
                $data['status'] = ($payment->is_charge_approved == "yes") ? 'Approved' : 'Declined';
                $data['message'] = $messageArray[$payment->business_campaign_payment_id] ?? '--';

                $data['cardNumber'] = '';
                if (isset($cardArray[$payment->business_cc_id])) {
                    $data['cardNumber'] = 'XXXX-XXXX-XXXX-' . $cardArray[$payment->business_cc_id];
                }
                if ($payment->campaign_id > 0 && $campaignCategory != 'alldata') {

                    //echo $payment->businesses_campaign_id."==".$campaignCategory."<br>";
                    if ($campaignCategory == $category) {
                        //echo $payment->businesses_campaign_id."==".$campaignCategory."<br>";
                        $finalData[] = $data;
                    }
                } else {
                    //echo $payment->businesses_campaign_id.'<br>';
                    $finalData[] = $data;
                }
            }

            return datatables()->of($finalData)->rawColumns(['businessName', 'campaignName','chargeMethod'])->make(true); 
        } else {
            $finaldata = [];
            return datatables()->of($finaldata)->rawColumns(['businessName', 'campaignName','chargeMethod'])->make(true); 
        }
    }

    public function setTwoDecimalPoint($value)
    {
        //return number_format($value, 2, '.', '');
        //return number_format(floatval($value), 2); --->old code
        //return number_format(floatval($value), 2, ".", "");
        return number_format($value, 2, '.', ',');
    }
}
