<?php

namespace App\Http\Controllers\API\VmOrganic;

use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Helpers\Helper;
use App\Models\Business\Business;
use App\Models\Campaign\Campaign;
use App\Models\Campaign\CampaignOrganic;
use App\Http\Controllers\Controller;
use App\Models\Logic\LeadCampaignLogic;
use App\Models\Logic\LogicDuplicateLead;
use App\Models\Master\MstLeadSource;
use App\Models\Master\MstZipcode;
use App\Helpers\CommonFunctions;
use App\Models\Campaign\CampaignMoving;
use App\Models\Campaign\CampaignJunkType;
use App\Models\Campaign\CampaignHeavyLifting;
use App\Models\Campaign\CampaignCarTransport;
use App\Http\Controllers\CronJob\PurchaseRelayNumberController;
use App\Models\Lead\LeadMatch;
use App\Models\Lead\LeadTcpaRequestLog;
use App\Models\Logic\LogicMoving;
use App\Models\Logic\LogicJunk;
use App\Models\Logic\LogicHeavyLift;
use App\Models\Logic\LogicCarTransport;
use App\Models\Lead\TcpaLog;

class OrganicBusinessList extends Controller {

    public function organicBusinessList() {
        //$businessList = Business::where('status', 'active')->where('is_contract_signed', 'yes')->orderBy('business_id', 'desc')->get()->toArray(); //Commented By HJ On 19-06-2023 For Remove is_contract_signed Condition Discuss With MV and Sir
        $businessList = Business::with('businessBadges')->where('status', 'active')->orderBy('business_id', 'desc')->get()->toArray();

        $campaignDataArr = $this->getVmOrganicCampaignData();
        $images_url = Helper::checkServer()['base_url'];
        $finalArray = array();
        $badgesArr = "";
        foreach ($businessList as $key => $data) {
            if (trim($data['logo']) != "") {
                $logoUrl = $images_url . '/images/businesslogo/' . $data['logo'];
                $data['logo'] = $logoUrl;
            }
            if (isset($campaignDataArr[$data['business_id']])) {
                $vmData = $campaignDataArr[$data['business_id']];
                for ($r = 0; $r < count($vmData); $r++) {
                    $data['vmcampaign'] = $vmData[$r];
                    $finalArray[] = $data;
                }
            } else {
                $finalArray[] = $data;
            }
        }
        foreach ($finalArray as $key => $data) {
            $badgesArr = "";
            if(count($data['business_badges']) > 0){
                $badges = array_column($data['business_badges'], 'badges_image');
                $badgesArr = $badges;
                $finalArray[$key]['badges_path'] = $images_url . '/images/businessbadges/';
            }
            $finalArray[$key]['badges'] = $badgesArr;
            unset( $finalArray[$key]['business_badges']);
        }
        $objectFromArr = json_decode(json_encode($finalArray), FALSE);
        //echo "<pre>";print_r($objectFromArr);die;
        return $objectFromArr;
    }

    public function businessdetails($id) {
        //echo "<pre>";print_r($finalArray);exit();
        $businessDetails = Business::where('business_id', $id)->where('is_contract_signed', 'yes')->orderBy('business_id', 'desc')->get()->toArray();
        //echo "<pre>";print_r($businessDetails);die;
        $campaignDataArr = $this->getVmOrganicCampaignData();
        $finalArray = array();
        for ($g = 0; $g < count($businessDetails); $g++) {
            if (isset($campaignDataArr[$businessDetails[$g]['business_id']])) {
                $vmData = $campaignDataArr[$businessDetails[$g]['business_id']];
                //echo "<pre>";print_r($vmData);die;
                for ($r = 0; $r < count($vmData); $r++) {
                    $businessDetails[$g]['vmcampaign'] = $vmData[$r];
                    $finalArray[] = $businessDetails[$g];
                }
            } else {
                $finalArray = $businessDetails;
            }
        }
        $objectFromArr = json_decode(json_encode($finalArray), FALSE);
        //echo "<pre>";print_r($objectFromArr);die;
        return $objectFromArr;
    }

    public function getVmOrganicCampaignData() {
        $campaignList = Campaign::where('campaign_type_id', 4)->where('campaign_id', '<>' , 4778)->where('is_active', 'yes')->get()->toArray();
        $finalArray = $campaignDataArr = $campaignIdArr = $vmCampaignDataArr = array();
        for ($i = 0; $i < count($campaignList); $i++) {
            $campaignIdArr[] = $campaignList[$i]['campaign_id'];
        }
        //echo "<pre>";print_r($campaignList);die;
        if (count($campaignIdArr) > 0) {
            $vmCampaignList = CampaignOrganic::whereIn('campaign_id', $campaignIdArr)->get()->toArray();
            //echo "<pre>";print_r($vmCampaignList);die;
            for ($hj = 0; $hj < count($vmCampaignList); $hj++) {
                $servicesOffer = explode(",", $vmCampaignList[$hj]['services_offer']);
                if (in_array('Others', $servicesOffer)) {
                    if ($vmCampaignList[$hj]['other_services_offer'] != "") {
                        $otherServicesOffer = explode(",", $vmCampaignList[$hj]['other_services_offer']);
                        $servicesOffer = array_merge($servicesOffer, $otherServicesOffer);
                    }
                    if (($key = array_search('Others', $servicesOffer)) !== false) {
                        unset($servicesOffer[$key]);
                        $servicesOffer = array_values($servicesOffer);
                    }
                }
                $vmCampaignList[$hj]['services_offer'] = $servicesOffer;

                $businessHighlights = explode(",", $vmCampaignList[$hj]['business_highlights']);
                if (in_array('Others', $businessHighlights)) {
                    if ($vmCampaignList[$hj]['other_business_highlights'] != "") {
                        $otherBusinessHighlights = explode(",", $vmCampaignList[$hj]['other_business_highlights']);
                        $businessHighlights = array_merge($businessHighlights, $otherBusinessHighlights);
                    }
                    if (($key = array_search('Others', $businessHighlights)) !== false) {
                        unset($businessHighlights[$key]);
                        $businessHighlights = array_values($businessHighlights);
                    }
                }
                $vmCampaignList[$hj]['business_highlights'] = $businessHighlights;
                $state = (!empty($vmCampaignList[$hj]['serve_area'])) ? explode(",", $vmCampaignList[$hj]['serve_area']) : [];
                $vmCampaignList[$hj]['serve_area'] = $state;
                $vmCampaignList[$hj]['year_established_in'] = $vmCampaignList[$hj]['no_of_years'];
                unset($vmCampaignList[$hj]['other_services_offer']);
                unset($vmCampaignList[$hj]['other_business_highlights']);
                unset($vmCampaignList[$hj]['no_of_years']);
                if ($vmCampaignList[$hj]['is_call_lead'] == 'no') {
                    unset($vmCampaignList[$hj]['contact_number']);
                }
                $vmCampaignDataArr[$vmCampaignList[$hj]['campaign_id']] = $vmCampaignList[$hj];
                //echo "<pre>";print_r($vmCampaignList[$hj]);die;
            }
        }
        for ($b = 0; $b < count($campaignList); $b++) {
            if (isset($vmCampaignDataArr[$campaignList[$b]['campaign_id']])) {
                $campaignDataArr[$campaignList[$b]['business_id']][] = $vmCampaignDataArr[$campaignList[$b]['campaign_id']];
                for ($i= 0; $i < count($campaignDataArr[$campaignList[$b]['business_id']]); $i++) {
                    $movetype = '';
                    if($campaignList[$b]['lead_category_id'] == 1 || $campaignList[$b]['lead_category_id'] == "1"){
                        $campaignmoving = CampaignMoving::where('campaign_id', $campaignDataArr[$campaignList[$b]['business_id']][$i]['campaign_id'])->pluck("move_type")->toArray();
                        $movetype = $campaignmoving[0];
                    }else if($campaignList[$b]['lead_category_id'] == 3 || $campaignList[$b]['lead_category_id'] == "3"){
                        $campaignheavy = CampaignHeavyLifting::where('campaign_id', $campaignDataArr[$campaignList[$b]['business_id']][$i]['campaign_id'])->pluck("move_type")->toArray();
                        $movetype = $campaignheavy[0];
                    }else if($campaignList[$b]['lead_category_id'] == 4 || $campaignList[$b]['lead_category_id'] == "4"){
                        $campaigncar = CampaignCarTransport::where('campaign_id', $campaignDataArr[$campaignList[$b]['business_id']][$i]['campaign_id'])->pluck("move_type")->toArray();
                        $movetype = $campaigncar[0];
                    }
                    $campaignDataArr[$campaignList[$b]['business_id']][$i]['move_type'] = $movetype ;
                }
            }
        }
        return $campaignDataArr;
    }

    public function checkCampaignCritearea() {
        $elements           = json_decode(file_get_contents('php://input'), true);
        //echo "<pre>";print_r($elements);die;

        $currentDate        = date("Y-m-d");
        $error              = "Fail";
        $status             = 0; //unqualified
        $businessCampaignId = 0;
        $flag               = 0;
        $logicDpSource      = new LogicDuplicateLead();
        $logicCampaign      = new LeadCampaignLogic();

        if (isset($elements['campaign_id']) && $elements['campaign_id'] > 0) {
            $businessCampaignId= $elements['campaign_id'];
            $origin         = $elements['origin'];
            $email          = $elements['email'];
            $phone          = $elements['phone'];
            $fromZipCode    = $elements['from_zip'];
            $toZipCode      = $elements['to_zip'];
            $moveDate       = $elements['move_date'];
            $moveSize       = $elements['move_size'];
            $validation     = 1;
            $leadDateTime   = date("Y-m-d H:i:s");
            if (empty($fromZipCode)) {
                $validation = 0;
                $error = "Please provide from zip value.";
            } else if (empty($toZipCode)) {
                $validation = 0;
                $error = "Please provide to zip value.";
            } else if (empty($moveDate)) {
                $error = "Please provide move date value.";
                $validation = 0;
            } else if (trim($moveSize) == "") {
                $validation = 0;
                $error = "Please provide move size value.";
            }
            $originName = MstLeadSource::read($origin);
            $checkDuplicateSlot = $logicDpSource->checkDuplicate($email, $phone, 0, $leadDateTime, 1);
            if (count($checkDuplicateSlot) > 0) {
                if ($checkDuplicateSlot[0] == 1) {
                    if ($checkDuplicateSlot[1] == 4) {
                        $validation = 0;
                        $error = "Duplicate Lead in 48 hours with all slot sold";
                    }
                }
            }

            if ($validation > 0) {
                if ($moveDate >= $currentDate) {
                    $coverageType = 'long';
                    $fromData = $toData = [];
                    $fromState = $toState = $fromAreaCode = $toAreaCode = "";
                    $zipcodeData = MstZipcode::read($fromZipCode, $toZipCode);
                    if (strcmp($fromZipCode, $toZipCode) && $toZipCode) {
                        if ($fromZipCode == $zipcodeData[0]['zipcode']) {
                            $fromData = $zipcodeData[0];
                        } elseif (isset($zipcodeData[1])) {
                            $fromData = $zipcodeData[1];
                        }
                        if ($toZipCode == $zipcodeData[0]['zipcode']) {
                            $toData = $zipcodeData[0];
                        } elseif (isset($zipcodeData[1])) {
                            $toData = $zipcodeData[1];
                        }
                    } else {
                        $fromData = $toData = $zipcodeData[0];
                    }
                    //from areacode, city, state data get from mst_zipcode table
                    $fromAreaCode = $fromData['areacode'];
                    $fromCity = strtoupper($fromData['city']);
                    $fromState = strtoupper($fromData['state']);
                    $toAreaCode = $toData['areacode'];
                    $toCity = strtoupper($toData['city']);
                    $toState = strtoupper($toData['state']);

                    if ($fromState == $toState) {
                        $coverageType = 'local';
                    }

                    try {

                        $campaignDetail = Campaign::where('campaign_id', $businessCampaignId)->where('is_active', 'yes')->first();
                        if (!$campaignDetail) {
                            throw new Exception("Business campaign not active.");
                        }

                        $businessDetail = Business::where('business_id', $campaignDetail->business_id)->where('status', 'active')->first();
                        if (!$businessDetail) {
                            throw new Exception("Business not active.");
                        }

                        $sqlQuery = "SELECT c.*, b.payment_type as business_payment_type, b.credit_available as business_credit_available, b.credit_reserved as business_credit_reserved, pb.business_id AS partner_business_id, pb.business_partner_id, cm.min_distance, cm.move_type FROM `campaign` c
                        INNER JOIN `business` b ON b.business_id=c.business_id
                        LEFT JOIN `business_partner_mapping` pb ON pb.business_id = b.business_id
                        LEFT JOIN `campaign_moving` cm on cm.campaign_id = c.campaign_id WHERE c.campaign_id = ".$businessCampaignId." AND b.status = 'active' AND b.buyer_type_id <> 3 AND c.is_active = 'yes' AND c.lead_category_id = 1 AND c.lead_type_id = 1";
                        $businessesCampaign = DB::select($sqlQuery);

                        $finalCampDataArr = $campUniqueIdArr = $campaignIds = $todayRouteData = $activeCampAssocDataAr = array();
                        for ($vf = 0; $vf < @count($businessesCampaign); $vf++) {
                            if (!in_array($businessesCampaign[$vf]->campaign_id, $campUniqueIdArr)) {
                                $finalCampDataArr[] = $businessesCampaign[$vf];
                                $campUniqueIdArr[] = $businessesCampaign[$vf]->campaign_id;
                            }
                        }

                        foreach ($finalCampDataArr as $key => $val) {
                            //echo $val->campaign_id."==".$coverageType."==".$val->move_type."==".$val->lead_category_id."<br>";
                            if (isset($val->move_type) && $val->move_type != $coverageType && $val->lead_category_id != 2) {
                                //echo $val->campaign_id;
                                unset($finalCampDataArr[$key]); //Uncommnent here
                            }
                        }
                        $finalCampDataArr = array_values($finalCampDataArr);

                        for ($d = 0; $d < @count($finalCampDataArr); $d++) {
                            $activeCampAssocDataAr[$finalCampDataArr[$d]->campaign_id] = $finalCampDataArr[$d];
                            //echo "<pre>";print_r($activeCampAssocDataAr);die;
                            if (!in_array($finalCampDataArr[$d]->campaign_id, $campaignIds)) {
                                //Start For Check Campaign Fund Logic
                                $payout = CommonFunctions::getCampaignPayout($finalCampDataArr[$d]->campaign_id, $coverageType, 0, 0);
                                if ($payout > 0) {
                                    //echo "<pre>";print_r($finalCampDataArr[$d]);die;
                                    if ($finalCampDataArr[$d]->business_payment_type == 'pre') { // Check if business payment_type is PrePayment
                                        if ($finalCampDataArr[$d]->payment_type == 0) { // business level balance;
                                            if (($finalCampDataArr[$d]->business_credit_available - $finalCampDataArr[$d]->business_credit_reserved) >= $payout && $payout > 0) {
                                                $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                                            }
                                        } else { // campaign level balance
                                            if (($finalCampDataArr[$d]->credit_available - $finalCampDataArr[$d]->credit_reserved) >= $payout && $payout > 0) {
                                                $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                                            }
                                        }
                                    } else { // If payment_type is PostPayment then no need to check credit availibility
                                        $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                                    }
                                }
                                //End For Check Campaign Fund Logic
                            }
                        }

                        //Added by BK on 14/05/2025
                        $organicDetail = CampaignOrganic::where('campaign_id', $businessCampaignId)->first();
                        if (isset($organicDetail) && $organicDetail->is_full_lead == 'no') {
                            $flag = 1;
                            goto L1;
                        }

                        if (count($campaignIds) > 0) {
                            //Start For Check Campaign Daily Limit,Budget Limit and Hourly Limit Logic
                            $currentDate = date('Y-m-d');
                            $currentDateTime = date('Y-m-d H:i:s');
                            $campaignIds = $logicCampaign->checkTiming($campaignIds, $currentDate, $currentDateTime, $coverageType, $todayRouteData);

                            if (count($campaignIds) > 0) {
                                //Start For Check Campaign Daily Limit,Budget Limit and Hourly Limit Logic
                                $campaignIds = $logicCampaign->checkLimit($campaignIds, $activeCampAssocDataAr, $coverageType, 0, 0);
                                if (count($campaignIds) > 0) {

                                    //check Campaign fromZip and toZip logic
                                    $campaignIds = $logicCampaign->checkFromZip($campaignIds, $activeCampAssocDataAr, $fromZipCode, $toZipCode, $fromState, $toState, $fromAreaCode, $toAreaCode);
                                    //echo "<pre>";print_r($campaignIds);die;
                                    //check duplicate buyer
                                    $campaignIds = $logicCampaign->checkDuplicateBuyer($campaignIds, $email, $phone, 0, $leadDateTime);

                                    if (count($campaignIds) > 0) {
                                        $error = "Success";
                                        $status = 1; //route to particular mover organic campaign then return status 1 = customer_intent 2
                                    } else {
                                        $flag = 1;
                                    }
                                } else {
                                    $error = "Business campaign time not available.";
                                    $flag = 1;
                                }
                            } else {
                                $error = "Business campaign limit not available.";
                                $flag = 1;
                            }
                        } else {
                            $error = "Business campaign fund/coverage not available.";
                            $flag = 1;
                        }

                        L1:
                        if ($flag > 0) {
                            //route to other mover organic campaign then return status 2 = customer_intent 3
                            $campaignIds = $this->checkOtherCampaignCritearea($businessCampaignId, 4, $fromZipCode, $toZipCode, $fromState, $toState, $fromAreaCode, $toAreaCode, $moveDate, $moveSize);
                            //check duplicate buyer
                            $campaignIds = $logicCampaign->checkDuplicateBuyer($campaignIds, $email, $phone, 0, $leadDateTime);
                            if (count($campaignIds) > 0) {
                                $error = "Success";
                                $status = 2;
                                $businessCampaignId = implode(",", $campaignIds);
                            }

                            if (count($campaignIds) == 0) {
                                //route to exclusive mover campaign then return status 3 = customer_intent 4
                                $campaignIds = $this->checkOtherCampaignCritearea($businessCampaignId, 2, $fromZipCode, $toZipCode, $fromState, $toState, $fromAreaCode, $toAreaCode, $moveDate, $moveSize);
                                //check duplicate buyer
                                $campaignIds = $logicCampaign->checkDuplicateBuyer($campaignIds, $email, $phone, 0, $leadDateTime);
                                if (count($campaignIds) > 0) {
                                    $error = "Success";
                                    $status = 3;
                                    $businessCampaignId = implode(",", $campaignIds);
                                }
                            }
                        }
                    } catch (Exception $e) {
                        $error = $e->getMessage();
                    }
                } else {
                    $error = "Move date must be greater than or equal to current date.";
                }
            }
        } else {
            $error = "Business campaign id not found";
        }

        $response = array(
            'status' => $status,
            'campaign_id' => $businessCampaignId,
            'error' => $error
        );
        return json_encode($response);
    }

    public function checkOtherCampaignCritearea($businessCampaignId, $campaignType, $fromZipCode, $toZipCode, $fromState, $toState, $fromAreaCode, $toAreaCode, $moveDate, $moveSize) {

        //echo "<pre>";print_r($elements);die;
        $campaignIds = $finalCampDataArr = $campUniqueIdArr = $campaignIds = $todayRouteData = $activeCampAssocDataAr = [];
        $coverageType = 'long';
        if ($fromState == $toState) {
            $coverageType = 'local';
        }

        $logicCampaign = new LeadCampaignLogic();
        $sqlQuery = "SELECT c.*, b.payment_type as business_payment_type, b.credit_available as business_credit_available, b.credit_reserved as business_credit_reserved, pb.business_id AS partner_business_id, pb.business_partner_id, cm.min_distance, cm.move_type FROM `campaign` c
                        INNER JOIN `business` b ON b.business_id=c.business_id
                        LEFT JOIN `business_partner_mapping` pb ON pb.business_id = b.business_id
                        LEFT JOIN `campaign_moving` cm on cm.campaign_id = c.campaign_id WHERE c.campaign_id != ".$businessCampaignId." AND c.campaign_type_id = ".$campaignType." AND b.status = 'active' AND b.buyer_type_id <> 3 AND c.is_active = 'yes' AND c.lead_category_id = 1 AND c.lead_type_id = 1";
        $businessesCampaign = DB::select($sqlQuery);

        for ($vf = 0; $vf < @count($businessesCampaign); $vf++) {
            if (!in_array($businessesCampaign[$vf]->campaign_id, $campUniqueIdArr)) {
                $finalCampDataArr[] = $businessesCampaign[$vf];
                $campUniqueIdArr[] = $businessesCampaign[$vf]->campaign_id;
            }
        }

        foreach ($finalCampDataArr as $key => $val) {
            //echo $val->campaign_id."==".$coverageType."==".$val->move_type."==".$val->lead_category_id."<br>";
            if (isset($val->move_type) && $val->move_type != $coverageType && $val->lead_category_id != 2) {
                //echo $val->campaign_id;
                unset($finalCampDataArr[$key]); //Uncommnent here
            }
        }
        $finalCampDataArr = array_values($finalCampDataArr);

        for ($d = 0; $d < @count($finalCampDataArr); $d++) {
            $activeCampAssocDataAr[$finalCampDataArr[$d]->campaign_id] = $finalCampDataArr[$d];
            //echo "<pre>";print_r($activeCampAssocDataAr);die;
            if (!in_array($finalCampDataArr[$d]->campaign_id, $campaignIds)) {
                //Start For Check Campaign Fund Logic
                $payout = CommonFunctions::getCampaignPayout($finalCampDataArr[$d]->campaign_id, $coverageType, 0, 0);
                if ($payout > 0) {
                    //echo "<pre>";print_r($finalCampDataArr[$d]);die;
                    if ($finalCampDataArr[$d]->business_payment_type == 'pre') { // Check if business payment_type is PrePayment
                        if ($finalCampDataArr[$d]->payment_type == 0) { // business level balance;
                            if (($finalCampDataArr[$d]->business_credit_available - $finalCampDataArr[$d]->business_credit_reserved) >= $payout && $payout > 0) {
                                $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                            }
                        } else { // campaign level balance
                            if (($finalCampDataArr[$d]->credit_available - $finalCampDataArr[$d]->credit_reserved) >= $payout && $payout > 0) {
                                $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                            }
                        }
                    } else { // If payment_type is PostPayment then no need to check credit availibility
                        $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                    }
                }
                //End For Check Campaign Fund Logic
            }
        }

        $currentDate = date('Y-m-d');
        $currentDateTime = date('Y-m-d H:i:s');
        $campaignIds = $logicCampaign->checkTiming($campaignIds, $currentDate, $currentDateTime, $coverageType, $todayRouteData);

        //Start For Check Campaign Daily Limit,Budget Limit and Hourly Limit Logic
        $campaignIds = $logicCampaign->checkLimit($campaignIds, $activeCampAssocDataAr, $coverageType, 0, 0);

        //check Campaign fromZip and toZip logic
        $campaignIds = $logicCampaign->checkFromZip($campaignIds, $activeCampAssocDataAr, $fromZipCode, $toZipCode, $fromState, $toState, $fromAreaCode, $toAreaCode);

        if (count($campaignIds) > 0) {
            $campaignDetail = Campaign::whereIn('campaign_id', $campaignIds)->get(['campaign_id', 'default_score', 'additional_score'])->toArray();
            foreach ($campaignDetail as $campaign) {
                $campaignIdArray[$campaign['campaign_id']] = $campaign['default_score'] + $campaign['additional_score'];
            }
            arsort($campaignIdArray);
            $campaignIds = array_keys(array_slice($campaignIdArray, 0, 1, true));
        }
        //print_r($campaignIds); die;
        return $campaignIds;
    }

    public function updateOrganicBusiness() {

        $PurchaseRelayNumberControllerObj = new PurchaseRelayNumberController();
        $PurchaseRelayNumberControllerObj->getOrganicCampaignCommio();

        $url        = Helper::checkServer()['vm_url'] . '/movingcompany';
        $ch         = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, 1);
        $response   = curl_exec($ch);
        if (curl_errno($ch)) {
            echo 'Error:' . curl_error($ch);
            exit;
        }
        curl_close($ch);
        $response   = json_decode($response, true);
        if ($response['status'] == true) {
            return true;
        }
        return false;
    }

    public function checkTCPACampaignCritearea() {
        $businessArray      = $leadMatch = $leadMoveDetails = $junkRemovalSubtypeDetails = [];

        $elements           = json_decode(file_get_contents('php://input'), true);
        //insert lead request log in lead_tcpa_request_log table
        $leadRequestLogId   = LeadTcpaRequestLog::create([
            'lead_request' => json_encode($elements),
            'created_at' => date("Y-m-d H:i:s")
        ])->lead_request_log_id;
        //echo "<pre>";print_r($elements);die;

        $currentDate        = date("Y-m-d");
        $error              = "Fail";
        $status             = 0; //unqualified
        $flag               = 0;
        $validation         = 1;
        $logicDpSource      = new LogicDuplicateLead();
        $logicCampaign      = new LeadCampaignLogic();

        $categoryId         = $elements['lead_category'] ?? 1;
        $leadId             = $elements['lead_id'] ?? '';
        $origin             = $elements['origin'] ?? '';
        $fromZipCode        = $elements['from_zip'] ?? '';
        $toZipCode          = $elements['to_zip'] ?? '';
        $moveDate           = $elements['move_date'] ?? '';

        if ($categoryId == 1) {
            $moveSize       = $elements['move_size'] ?? '';
            $leadMoveDetails= [['move_size_id' => $moveSize]];
        } else if ($categoryId == 2) {
            $junkTypeId     = trim($elements['junk_type']) ?? '';
            $junkSubTypeId  = $elements['junk_sub_type'] ?? null;
            $leadMoveDetails= [[
                'junk_type_id' => $junkTypeId
            ]];
            $junkRemovalSubtypeDetails = explode(',', $junkSubTypeId) ?? [];
        } else if ($categoryId == 3) {
            $heavyLiftingTypeId = trim($elements['heavy_lifting_type']) ?? '';
            $leadMoveDetails= [[
                'heavy_lifting_type_id' => $heavyLiftingTypeId
            ]];
        } else if ($categoryId == 4) {
            $carTypeId      = $elements['car_type'] ?? 0;
            $transportType  = ($elements['transport_type'] > 0) ? 'open' : 'enclosed';
            $leadMoveDetails= [[
                'car_type_id' => $carTypeId,
                'transport_type' => $transportType,
            ]];
        }
        $leadDateTime       = date("Y-m-d H:i:s");
        $sourceId           = 0;
        if (strpos($origin, 'VM') !== false) {
            $sourceId       = '19';
        }
        if (strpos($origin, 'QTM') !== false) {
            $sourceId       = '14';
        }
        if (strpos($origin, 'IS') !== false) {
            $sourceId       = '32';
        }

        if (empty($fromZipCode)) {
            $validation = 0;
            $error = "Please provide from zip value.";
        } else if (empty($toZipCode)) {
            $validation = 0;
            $error = "Please provide to zip value.";
        } else if (empty($moveDate)) {
            $error = "Please provide move date value.";
            $validation = 0;
        } /*else if (trim($moveSize) == "") {
            $validation = 0;
            $error = "Please provide move size value.";
        }*/

        if ($validation > 0) {
            if ($moveDate >= $currentDate) {
                $fromData = $toData = [];
                $fromState = $toState = $fromAreaCode = $toAreaCode = "";
                $zipcodeData = MstZipcode::read($fromZipCode, $toZipCode);
                if (strcmp($fromZipCode, $toZipCode) && $toZipCode) {
                    if ($fromZipCode == $zipcodeData[0]['zipcode']) {
                        $fromData = $zipcodeData[0];
                    } elseif (isset($zipcodeData[1])) {
                        $fromData = $zipcodeData[1];
                    }
                    if ($toZipCode == $zipcodeData[0]['zipcode']) {
                        $toData = $zipcodeData[0];
                    } elseif (isset($zipcodeData[1])) {
                        $toData = $zipcodeData[1];
                    }
                } else {
                    $fromData = $toData = $zipcodeData[0];
                }
                //from areacode, city, state data get from mst_zipcode table
                $fromAreaCode = $fromData['areacode'];
                $fromCity = strtoupper($fromData['city']);
                $fromState = strtoupper($fromData['state']);
                $toAreaCode = $toData['areacode'];
                $toCity = strtoupper($toData['city']);
                $toState = strtoupper($toData['state']);

                $coverageType = 'long';
                if ($fromState == $toState) {
                    $coverageType = 'local';
                }

                try {
                    if ($categoryId == 1) {
                        $moveTableName = "campaign_moving";
                        $movingFields = ",cm.min_distance,cm.move_type";
                    } else if ($categoryId == 2) {
                        $moveTableName = "campaign_junk_type";
                        $movingFields = ",cm.junk_type_id,cm1.junk_sub_type_id";
                    } else if ($categoryId == 3) {
                        $moveTableName = "campaign_heavy_lifting";
                        $movingFields = ",cm.min_distance,cm.move_type,cm.heavy_lifting_type_id,cm.is_required_assistence,cm.is_operational";
                    } else if ($categoryId == 4) {
                        $moveTableName = "campaign_car_transport";
                        $movingFields = ",cm.min_distance,cm.move_type,cm.is_required_assistence,cm.is_operational,cctt.car_type_id";
                    }

                    $sqlQuery = "SELECT c.*,b.payment_type as business_payment_type,b.credit_available as business_credit_available,b.credit_reserved as business_credit_reserved,pb.business_id AS partner_business_id,pb.business_partner_id $movingFields FROM `campaign` c 
                                INNER JOIN `business` b ON b.business_id=c.business_id 
                                LEFT JOIN `business_partner_mapping` pb ON pb.business_id = b.business_id 
                                LEFT JOIN `" . $moveTableName . "` cm on cm.campaign_id = c.campaign_id";
                    if ($categoryId == 2) {
                        $sqlQuery .= " LEFT JOIN `campaign_junk_sub_type` cm1 on cm1.campaign_id = c.campaign_id";
                    }
                    if ($categoryId == 4) {
                        $sqlQuery .= " LEFT JOIN `campaign_car_transport_type` cctt on cctt.campaign_id = c.campaign_id";
                    }
                    $sqlQuery .= " WHERE b.status='active' AND c.is_active='yes' AND c.lead_category_id='" . $categoryId . "' AND c.lead_type_id=1 AND b.buyer_type_id <> 3";
                    $this->tcpaLeadMatchLog($leadId, 'Coverage Type: ' . $coverageType . ', Lead route query == ' . $sqlQuery, 1);
                    $businessesCampaign = DB::select($sqlQuery);

                    $finalCampDataArr = $campUniqueIdArr = $campaignIds = $todayRouteData = $activeCampAssocDataAr = array();
                    for ($vf = 0; $vf < @count($businessesCampaign); $vf++) {
                        if (!in_array($businessesCampaign[$vf]->campaign_id, $campUniqueIdArr)) {
                            $finalCampDataArr[] = $businessesCampaign[$vf];
                            $campUniqueIdArr[] = $businessesCampaign[$vf]->campaign_id;
                        }
                    }

                    foreach ($finalCampDataArr as $key => $val) {
                        //echo $val->campaign_id."==".$coverageType."==".$val->move_type."==".$val->lead_category_id."<br>";
                        if (isset($val->move_type) && $val->move_type != $coverageType && $val->lead_category_id != 2) {
                            //echo $val->campaign_id;
                            unset($finalCampDataArr[$key]); //Uncommnent here
                        }
                    }
                    $finalCampDataArr = array_values($finalCampDataArr);

                    for ($d = 0; $d < @count($finalCampDataArr); $d++) {
                        $activeCampAssocDataAr[$finalCampDataArr[$d]->campaign_id] = $finalCampDataArr[$d];
                        //echo "<pre>";print_r($activeCampAssocDataAr);die;
                        if (!in_array($finalCampDataArr[$d]->campaign_id, $campaignIds)) {
                            //Start For Check Campaign Fund Logic
                            $payout = CommonFunctions::getCampaignPayout($finalCampDataArr[$d]->campaign_id, $coverageType, 0, 0);
                            if ($payout > 0) {
                                //echo "<pre>";print_r($finalCampDataArr[$d]);die;
                                if ($finalCampDataArr[$d]->business_payment_type == 'pre') { // Check if business payment_type is PrePayment
                                    if ($finalCampDataArr[$d]->payment_type == 0) { // business level balance;
                                        if (($finalCampDataArr[$d]->business_credit_available - $finalCampDataArr[$d]->business_credit_reserved) >= $payout && $payout > 0) {
                                            $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                                        }
                                    } else { // campaign level balance
                                        if (($finalCampDataArr[$d]->credit_available - $finalCampDataArr[$d]->credit_reserved) >= $payout && $payout > 0) {
                                            $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                                        }
                                    }
                                } else { // If payment_type is PostPayment then no need to check credit availibility
                                    $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                                }
                            }
                            //End For Check Campaign Fund Logic
                        }
                    }
                    //echo "<pre>dd";print_r($campaignIds);die;
                    $this->tcpaLeadMatchLog($leadId, 'Out of funds campaign == ' . implode(",", $campaignIds), 2);

                    //Start For Check Campaign Source Logic
                    $campaignIds = $logicCampaign->checkCampaignSource($campaignIds, 0, 0, $origin);
                    $this->tcpaLeadMatchLog($leadId, 'Source restriction campaign == ' . implode(",", $campaignIds), 3);

                    //Start For Check Campaign Exclude Logic
                    $campaignIds = $logicCampaign->checkCampaignExcludedDates($campaignIds, $activeCampAssocDataAr, $moveDate);
                    $this->tcpaLeadMatchLog($leadId, 'Move Date restriction campaign == ' . implode(",", $campaignIds), 4);

                    //Start For Check Campaign Daily Limit,Budget Limit and Hourly Limit Logic
                    $currentDate = date('Y-m-d');
                    $currentDateTime = date('Y-m-d H:i:s');
                    $campaignIds = $logicCampaign->checkTiming($campaignIds, $currentDate, $currentDateTime, $coverageType, $todayRouteData);
                    $this->tcpaLeadMatchLog($leadId, 'Timing campaign ==' . implode(",", $campaignIds), 5);

                    //Start For Check Campaign Daily Limit,Budget Limit and Hourly Limit Logic
                    $campaignIds = $logicCampaign->checkLimit($campaignIds, $activeCampAssocDataAr, $coverageType, 0, 0);
                    $this->tcpaLeadMatchLog($leadId, 'Limits campaign ==' . implode(",", $campaignIds), 6);

                    //check Campaign fromZip and toZip logic
                    $campaignIds = $logicCampaign->checkFromZip($campaignIds, $activeCampAssocDataAr, $fromZipCode, $toZipCode, $fromState, $toState, $fromAreaCode, $toAreaCode);
                    $this->tcpaLeadMatchLog($leadId, 'Region matches campaign == ' . implode(",", $campaignIds), 7);

                    if ($categoryId == 1) {
                        $logicMovingCampaign = new LogicMoving();
                        $campaignIds = $logicMovingCampaign->checkMovingSizeLogic($campaignIds, 0, $leadMoveDetails);
                        $this->tcpaLeadMatchLog($leadId, "Move Size matches campaign ==" . implode(",", $campaignIds), 8);
                        // echo "<pre>";print_r($campaignIds);die;
                    }
                    if ($categoryId == 2) {
                        $logicJunkCampaign = new LogicJunk();
                        $campaignIds = $logicJunkCampaign->checkJunkLogic($campaignIds, 0, $leadMoveDetails, $junkRemovalSubtypeDetails);
                        $this->tcpaLeadMatchLog($leadId, "Junk matches campaign ==" . implode(",", $campaignIds), 8);
                        //echo "<pre>";print_r($campaignIds);die;
                    }
                    if ($categoryId == 3) {
                        $logicHeavyLiftCampaign = new LogicHeavyLift();
                        $campaignIds = $logicHeavyLiftCampaign->checkHeavyLiftLogic($campaignIds, 0, $leadMoveDetails);
                        $this->tcpaLeadMatchLog($leadId, "Heavy Lift matches campaign ==" . implode(",", $campaignIds), 8);
                        //echo "<pre>";print_r($campaignIds);die;
                    }
                    if ($categoryId == 4) {
                        $logicCarCampaign = new LogicCarTransport();
                        $campaignIds = $logicCarCampaign->checkCarTransportLogic($campaignIds, 0, $leadMoveDetails);
                        $this->tcpaLeadMatchLog($leadId, "Car Transport matches campaign ==" . implode(",", $campaignIds), 8);
                        //echo "<pre>";print_r($campaignIds);die;
                    }

                    $campaignIds = $logicCampaign->checkCampaignLogic($campaignIds, $coverageType, $currentDate, $leadDateTime, 4, 0, $categoryId, 0, 0, 1, "", "", 0, 1);
                    $this->tcpaLeadMatchLog($leadId, 'campaignSortBundle campaign == ' . json_encode($campaignIds), 9);
                    if (count($campaignIds) > 0) {
                        $businessArray = DB::select("SELECT business_id, business_name FROM `business` WHERE business_id IN (SELECT business_id FROM campaign WHERE campaign_id IN (".implode(',', $campaignIds)."))");

                        foreach ($businessArray as $key => $business) {
                            $leadMatch[] = [
                                "lead_source_id" => $sourceId,
                                "lp_lead_id" => $leadId,
                                "business_id" => $business->business_id,
                                "created_at" => date('Y-m-d H:i:s')
                            ];
                        }
                        LeadMatch::insert($leadMatch);
                        //echo "<pre>";print_r($businessDetail);die;
                        $status = 1;
                        $error = "Success";
                    }
                } catch (Exception $e) {
                    $error = $e->getMessage();
                }
            } else {
                $error = "Move date must be greater than or equal to current date.";
            }
        }

        $response = array(
            'status' => $status,
            'error' => $error,
            'data' => $businessArray,
        );

        //insert lead response log in lead_tcpa_request_log table
        LeadTcpaRequestLog::where('lead_tcpa_request_log_id', $leadRequestLogId)->update([
            'lead_response' => json_encode($response)
        ]);
        return json_encode($response);
    }

    public function tcpaLeadMatchLog($leadId, $log, $srno) {
        TcpaLog::create([
            'lp_lead_id' => $leadId,
            'log' => $log,
            'log_srno' => $srno,
            'created_at' => date("Y-m-d H:i:s")
        ]);
    }
}
