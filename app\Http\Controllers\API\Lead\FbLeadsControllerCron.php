<?php

namespace App\Http\Controllers\API\Lead;

use App\Http\Controllers\Controller;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\API\LeadEmail\LeadEmailInsert;
use App\Http\Controllers\API\LeadPhone\LeadPhoneInsert;
use App\Models\Master\MstZipcode;
use App\Models\DevDebug;
use App\Helpers\Helper;
use App\Models\Lead\LeadActiveCamapaignLog;

class FbLeadsControllerCron extends Controller
{
    //public $fbToken       = 'EAAKGabnI1IgBO2QRLuJT2mH9eqauuyzVT73h2KCsKZADmHeGqU1vTzIBZApPTNW4pA7I3ZBt4anUVmSWqSWIlddZBWZCqOoG9g7guoejxOLXc4oEZAJQj7SHblrUfvQ5UnUrKfWG6svh6gZBJ2wOnzkIDRtHNr1UOfxbRZChU7qvM5pgfPgQUyZByPbuPOYZAlxKvQdTKQcQZDZD';
    public $clickupToken    = 'EAAKedGyJynQBADFJ1MsLsnRvFWiAfEKRlBIbeQYLkaHivUxI8ocpDe5KF5v8RNzdRiyEWjXJQRcDnLWQX9ump2IUny8PmpQwBdTXBBXO9SNaVsgsDTFnd09841PV6vn9o2VD6AjOIHnJfui0x2aU9T7KLWpZA80qz2wxNOozOOs0ZAw1qgod6U49SUIZCsZD';

    public function fbLeadStore()
    {
        $accessToken        = Helper::checkServer()['whatsapp_token'];
        $url                = "https://graph.facebook.com/v23.0/act_1105854376901839/ads?limit=100&effective_status=['ACTIVE']&access_token=" . $accessToken;
        $ch                 = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
        $result             = curl_exec($ch);
        curl_close($ch);
        $apiResponse        = json_decode($result, true);
        $adData             = $apiResponse['data'] ?? $apiResponse['error'];
        $devdebuglg         = new DevDebug();
        if (count($adData) > 0) { foreach ($adData as $ad) {
            try {
                $adId       = $ad['id'];
                $response1  = $this->sendFBLead($adId, 120227481911690262, 'VM(FBForm)', 'bGCI6IkciOiJI5dceyJhpJ8zI1N34fhgjHliI', 'no', 1, "Props | LG | Int | July 2025");
                // echo '<pre>response3 = '; print_r($response9);
                $devdebuglg->devDebuglog(1, "FB Cron " . json_encode($response1));
            } catch (Exception $ex) {
                $devdebuglg->devDebuglog(1, "FB Cron Exception " . json_encode($ex->getMessage()));
                Log::info("FB Cron Exception " . json_encode($ex->getMessage()));
            }
        } }
        /*try {
            //storefbleadcron - storeFBLeadCron
            //eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9 - VM TOKEN Need to change after verify with QA
            // bGCI6IkciOiJI5dceyJhpJ8zI1N34fhgjHliI - Link UP live VM Token and also staging token
            //PM - ciOiJI5dceyJhbGCI6IkpJ8zI1N34fhliIdfK
            $response1 = $this->sendFBLead('23852463658460261', 23852463658190261, 'VM(FBForm)', 'bGCI6IkciOiJI5dceyJhpJ8zI1N34fhgjHliI', 'no', 14, "VM Moving");
            // echo '<pre>response1 = '; print_r($response1);
            $devdebuglg->devDebuglog(14, "Cron 14" . json_encode($response1));
        } catch (Exception $ex) {
            $devdebuglg->devDebuglog(14, "Cron 14 Exception " . json_encode($ex->getMessage()));
            // echo '<pre>Exception1 = '; print_r($ex->getMessage());
            Log::info("Cron 14 Exception " . json_encode($ex->getMessage()));
        }*/

        /*try {
            //storenewfbleadcron - storeNewFBLeadCron
            $response2 = $this->sendFBLead('23853938023280261', 23853938023270261, 'VM(FBForm)', 'bGCI6IkciOiJI5dceyJhpJ8zI1N34fhgjHliI', 'no', 15, "VM Primary Lookalike");
            // echo '<pre>response2 = '; print_r($response2);
            $devdebuglg->devDebuglog(15, "Cron 15" . json_encode($response2));
        } catch (Exception $ex) {
            $devdebuglg->devDebuglog(15, "Cron 15 Exception " . json_encode($ex->getMessage()));
            // echo '<pre>Exception2 = '; print_r($ex->getMessage());
            Log::info("Cron 15 Exception " . json_encode($ex->getMessage()));
        }*/

        /*try {
            //storeNewFBLeadCron2
            $response4 = $this->sendFBLead('23853692531550261', 23853481900750261, 'VM(FBForm)', 'bGCI6IkciOiJI5dceyJhpJ8zI1N34fhgjHliI', 'no', 17, "VM GIF");
            // echo '<pre>response4 = '; print_r($response4);
            $devdebuglg->devDebuglog(17, "Cron 17" . json_encode($response4));
        } catch (Exception $ex) {
            $devdebuglg->devDebuglog(17, "Cron 17 Exception " . json_encode($ex->getMessage()));
            // echo '<pre>Exception4 = '; print_r($ex->getMessage());
            Log::info("Cron 17 Exception " . json_encode($ex->getMessage()));
        }*/

        /*try {
            //storeNewFBLeadCron3
            $response5 = $this->sendFBLead('23853481900850261', 23853481900750261, 'VM(FBForm)', 'bGCI6IkciOiJI5dceyJhpJ8zI1N34fhgjHliI', 'no', 18, "VM GIF");
            // echo '<pre>response5 = '; print_r($response5);
            $devdebuglg->devDebuglog(18, "Cron 18" . json_encode($response5));
        } catch (Exception $ex) {
            $devdebuglg->devDebuglog(18, "Cron 18 Exception " . json_encode($ex->getMessage()));
            // echo '<pre>Exception5 = '; print_r($ex->getMessage());
            Log::info("Cron 18 Exception " . json_encode($ex->getMessage()));
        }*/
        //error
        /*try {
            //storeNewFBLeadCron4
            $response6 = $this->sendFBLead('23853937645240261', 23853937645260261, 'VM(FBForm)', 'bGCI6IkciOiJI5dceyJhpJ8zI1N34fhgjHliI', 'no', 19, "VM Primary Truck");
            // echo '<pre>response6 = '; print_r($response6);
            $devdebuglg->devDebuglog(19, "Cron 19" . json_encode($response6));
        } catch (Exception $ex) {
            $devdebuglg->devDebuglog(19, "Cron 19 Exception " . json_encode($ex->getMessage()));
            // echo '<pre>Exception6 = '; print_r($ex->getMessage());
            Log::info("Cron 19 Exception " . json_encode($ex->getMessage()));
        }*/

        /*try {
            //storecarouselfbleadcron - storeCarouselFBLeadCron
            $response3 = $this->sendFBLead('23853336181130261', 23853336180800261, 'VM(FBForm)', 'bGCI6IkciOiJI5dceyJhpJ8zI1N34fhgjHliI', 'no', 20, "VM Carousel");
            // echo '<pre>response3 = '; print_r($response3);
            $devdebuglg->devDebuglog(20, "Cron 20" . json_encode($response3));
        } catch (Exception $ex) {
            $devdebuglg->devDebuglog(20, "Cron 20 Exception " . json_encode($ex->getMessage()));
            // echo '<pre>Exception3 = '; print_r($ex->getMessage());
            Log::info("Cron 16 Exception " . json_encode($ex->getMessage()));
        }*/

        /*try {
            //storecarouselfbleadcron - storeCarouselFBLeadCron
            $response7 = $this->sendFBLead('23857218715300261', 23857218715050261, 'VM(FBForm)', 'bGCI6IkciOiJI5dceyJhpJ8zI1N34fhgjHliI', 'no', 21, "VM FB Video");
            // echo '<pre>response3 = '; print_r($response3);
            $devdebuglg->devDebuglog(21, "Cron 21" . json_encode($response7));
        } catch (Exception $ex) {
            $devdebuglg->devDebuglog(21, "Cron 21 Exception " . json_encode($ex->getMessage()));
            // echo '<pre>Exception3 = '; print_r($ex->getMessage());
            Log::info("Cron 21 Exception " . json_encode($ex->getMessage()));
        }*/

        /*try {
            //storecarouselfbleadcron - storeCarouselFBLeadCron
            $response8 = $this->sendFBLead('23862296396240261', 23862296396220261, 'VM(FBForm)', 'bGCI6IkciOiJI5dceyJhpJ8zI1N34fhgjHliI', 'no', 22, "VM Long Distance");
            // echo '<pre>response3 = '; print_r($response3);
            $devdebuglg->devDebuglog(22, "Cron 22" . json_encode($response8));
        } catch (Exception $ex) {
            $devdebuglg->devDebuglog(22, "Cron 22 Exception " . json_encode($ex->getMessage()));
            // echo '<pre>Exception3 = '; print_r($ex->getMessage());
            Log::info("Cron 22 Exception " . json_encode($ex->getMessage()));
        }*/

        /*try {
            //storecarouselfbleadcron - storeCarouselFBLeadCron
            $response9 = $this->sendFBLead('120204841921790262', 120204841489430262, 'VM(FBForm)', 'bGCI6IkciOiJI5dceyJhpJ8zI1N34fhgjHliI', 'no', 23, "VM Reels Image");
            // echo '<pre>response3 = '; print_r($response9);
            $devdebuglg->devDebuglog(23, "Cron 23" . json_encode($response9));
        } catch (Exception $ex) {
            $devdebuglg->devDebuglog(23, "Cron 23 Exception " . json_encode($ex->getMessage()));
            // echo '<pre>Exception3 = '; print_r($ex->getMessage());
            Log::info("Cron 23 Exception " . json_encode($ex->getMessage()));
        }*/

        /*try {
            //storecarouselfbleadcron - storeCarouselFBLeadCron
            $response10 = $this->sendFBLead('120204841489540262', 120204841489430262, 'VM(FBForm)', 'bGCI6IkciOiJI5dceyJhpJ8zI1N34fhgjHliI', 'no', 24, "VM Reels Image");
            // echo '<pre>response3 = '; print_r($response9);
            $devdebuglg->devDebuglog(24, "Cron 24" . json_encode($response10));
        } catch (Exception $ex) {
            $devdebuglg->devDebuglog(24, "Cron 24 Exception " . json_encode($ex->getMessage()));
            // echo '<pre>Exception3 = '; print_r($ex->getMessage());
            Log::info("Cron 24 Exception " . json_encode($ex->getMessage()));
        }*/

        /*try {
            $response11 = $this->sendFBLead('120211049187970262', 120211042338830262, 'VM(FBForm)', 'bGCI6IkciOiJI5dceyJhpJ8zI1N34fhgjHliI', 'no', 25, "H-VMO Sep");
            $devdebuglg->devDebuglog(25, "Cron 25" . json_encode($response11));
        } catch (Exception $ex) {
            $devdebuglg->devDebuglog(25, "Cron 25 Exception " . json_encode($ex->getMessage()));
            Log::info("Cron 25 Exception " . json_encode($ex->getMessage()));
        }*/
    }

    public function sendFBLead($ad_id, $campaign_id, $source, $source_token, $is_clickup, $cron_id, $campaign_name)
    {
        $devdebuglg = new DevDebug();
        $accessToken = Helper::checkServer()['whatsapp_token'];
        $clickupAccessToken = $this->clickupToken;
        $para = array("filtering" => array("field" => "time_created", "operator" => "GREATER_THAN", "value" => 1648897785));
        $pageContent = http_build_query($para);
        //echo $pageContent;die;
        $from_date = time();
        $to_date = time();
        $ch = curl_init();
        if($is_clickup == 'yes'){
            $url = "https://graph.facebook.com/v23.0/$ad_id/leads?access_token=" . $accessToken;
        }
        else{
            $url = "https://graph.facebook.com/v23.0/$ad_id/leads?access_token=" . $accessToken;
        }

        //https://graph.facebook.com/v20.0/23852463658460261/leads?access_token=EAAKGabnI1IgBALmjDntwpIFwqv3klozMxj0iZCtEIyLPVGeOW90w1rGvezolpj574PGWEFVtzth2xyADGrvk9t19uaZByv0ZA3gW1VpbsbAyKZA9cYLq3rOxoS0OXxBbL6ZAw7lDWUX9ScBMvjXv2e0ZAhQvuzr08BFW0FTcWAOnogwhlYdDCHTvnZBZBoP2EI0ZD
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
        //curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($para));
        //curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        //curl_setopt($ch, CURLOPT_POSTFIELDS, $pageContent);
        //curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/x-www-form-urlencoded'));
        $result = curl_exec($ch);
        curl_close($ch);

        $apiResponse = json_decode($result, true);
        // echo '<pre>cronid = '.$cron_id.'adId= '.$ad_id.', apiResponse='; print_r($apiResponse); // die;
        if (isset($apiResponse['data'])) {
            if ($is_clickup == 'no') {
                $checkToken = DB::select("SELECT api_token_id, SUBSTR(source, 1) AS source, token FROM mst_api_token WHERE token = '$source_token'");
                if (empty($checkToken)) {
                    throw new Exception("Unauthorized Token");
                }
            }

            $leadData = $apiResponse['data'];
            $origin = $source;
            $defaultZip = null;
            for ($i = 0; $i < count($leadData); $i++) {
                date_default_timezone_set('America/New_York');
                $fbId = $leadData[$i]['id'];
                $newCreatedTime = str_replace("+0000", "", str_replace("T", " ", $leadData[$i]['created_time']));
                $created_time = date("Y-m-d H:i:s", strtotime($newCreatedTime));
                $currentDate = date("Y-m-d H:i:s", strtotime("+4 hour"));
                $backDateTime = date("Y-m-d H:i:s", strtotime("-10 minutes", strtotime($currentDate)));
                //dump($fbId . '==FB Lead route Check Time Lead Org Time: ' . $newCreatedTime . "==,Lead Coverted Time: " . $created_time . "==,Current System Time: " . $currentDate . "==,10 min back time: " . $backDateTime);

                $devdebuglg->devDebuglog($cron_id, $fbId . '==FB Lead route Check Time Lead Org Time: ' . $newCreatedTime . "==,Lead Coverted Time: " . $created_time . "==,Current System Time: " . $currentDate . "==,10 min back time: " . $backDateTime);
                if ($created_time >= $backDateTime && $created_time < $currentDate) {
                    //dump("YES");
                    $devdebuglg->devDebuglog($cron_id, $fbId . '==FB Lead route Data ' . json_encode($leadData[$i]));
                    $fbId = $leadData[$i]['id'];
                    $field_data = $leadData[$i]['field_data'];
                    if ($is_clickup == 'yes') {
                        $leadDataArr = array();
                    } else {
                        $leadDataArr = array(
                            "origin" => $origin,
                            "utm_campaign" => $campaign_name,
                            "utm_medium" => "",
                            "camp_id" => $campaign_id,
                            "ad_grp_id" => "",
                            "ad_id" => $ad_id,
                            "ag" => "",
                            "keyword" => "",
                            "device" => "",
                            "move_size" => 1,
                            "is_verified" => 1,
                            "IP" => '127.0.0.1',
                            "GCLID" => "",
                            "Landing_Page" => "Facebook",
                            "token" => $source_token,
                            "generated_at" => date("Y-m-d H:i:s"),
                            "lead_id" => 0
                        );
                    }
                    //echo "<pre>";print_r($leadDataArr);die;
                    //echo "<pre>";print_r($field_data);die;
                    $routeLeadArr = array();
                    $currentDate = date('Y-m-d');
                    $phoneLead = 0;
                    $emailLead = 0;
                    for ($g = 0; $g < count($field_data); $g++) {
                        $fields = $field_data[$g]['name'];
                        if ($is_clickup == "no") {
                            /*if ($fields == "enter_your_from_zip_") {
                                $leadDataArr['from_zip'] =  sprintf("%05d", $field_data[$g]['values'][0]);
                                if ($leadDataArr['from_zip'] == 0 || empty($leadDataArr['from_zip'])) {
                                    $leadDataArr['from_zip'] = $defaultZip;
                                    $routeLeadArr[] = 1;
                                    $phoneLead = 1;
                                }
                            }*/

                            if ($fields == "enter_your_from_zip_") {
                                if (is_numeric($field_data[$g]['values'][0])) {
                                    $leadDataArr['from_zip'] = sprintf("%05d", $field_data[$g]['values'][0]);
                                } else {
                                    //$fromZipName = str_replace(' ', '-', $field_data[$g]['values'][0]);
                                    $fromZipName = preg_replace('/[^A-Za-z0-9\-]/', ' ', $field_data[$g]['values'][0]);
                                    $fromZipName = explode(' ', $fromZipName);
                                    $fromCity = '';
                                    for ($i=0; $i < count($fromZipName); $i++) {
                                        $fromCity .= ($i > 0) ? ' ' . strtolower($fromZipName[$i]) : strtolower($fromZipName[$i]);
                                        $fromZip = MstZipcode::where(function ($query) use ($fromCity) {
                                            $query->where('zipcode', $fromCity)->orWhere('city', $fromCity);
                                        })->pluck('zipcode')->first();
                                        if (!empty($fromZip)) {
                                            break;
                                        }
                                    }
                                    if (!empty($fromZip)) {
                                        $leadDataArr['from_zip'] =  $fromZip;
                                    } else {
                                        $leadDataArr['from_zip'] = 0;
                                    }
                                }
                                //$leadDataArr['from_zip'] = $defaultZip;
                                if (isset($leadDataArr['from_zip']) && $leadDataArr['from_zip'] == 0 /*|| filter_var($leadDataArr['from_zip'], FILTER_VALIDATE_INT) === false*/) {
                                    $leadDataArr['from_zip'] = $defaultZip;
                                    $routeLeadArr[] = 1;
                                    $phoneLead = 1;
                                }
                            }

                            /*if ($fields == "enter_your_to_zip") {
                                $leadDataArr['to_zip'] = sprintf("%05d", $field_data[$g]['values'][0]);
                                if ($leadDataArr['to_zip'] == 0 || empty($leadDataArr['to_zip'])) {
                                    $leadDataArr['to_zip'] = $defaultZip;
                                    $routeLeadArr[] = 1;
                                    $phoneLead = 1;
                                }
                            }*/

                            if ($fields == "enter_your_to_zip") {
                                if (is_numeric($field_data[$g]['values'][0])) {
                                    $leadDataArr['to_zip'] = sprintf("%05d", $field_data[$g]['values'][0]);
                                } else {
                                    //$toZipName = str_replace(' ', '-', $field_data[$g]['values'][0]);
                                    $toZipName = preg_replace('/[^A-Za-z0-9\-]/', ' ', $field_data[$g]['values'][0]);
                                    $toZipName = explode(' ', $toZipName);
                                    $toCity = '';
                                    for ($i=0; $i < count($toZipName); $i++) {
                                        $toCity .= ($i > 0) ? ' ' . strtolower($toZipName[$i]) : strtolower($toZipName[$i]);
                                        $toZip = MstZipcode::where(function ($query) use ($toCity) {
                                            $query->where('zipcode', $toCity)->orWhere('city', $toCity);
                                        })->pluck('zipcode')->first();
                                        if (!empty($toZip)) {
                                            break;
                                        }
                                    }
                                    if (!empty($toZip)) {
                                        $leadDataArr['to_zip'] =  $toZip;
                                    } else {
                                        $leadDataArr['to_zip'] = 0;
                                    }
                                }
                                //$leadDataArr['to_zip'] = $defaultZip;
                                if (isset($leadDataArr['to_zip']) && $leadDataArr['to_zip'] == 0 /*|| filter_var($leadDataArr['to_zip'], FILTER_VALIDATE_INT) === false*/) {
                                    $leadDataArr['to_zip'] = $defaultZip;
                                    $routeLeadArr[] = 1;
                                    $phoneLead = 1;
                                }
                            }

                            /*if ($fields == "enter_your_from_state") {
                                $leadDataArr['move_date'] = date("Y-m-d", strtotime($field_data[$g]['values'][0]));
                                if ($leadDataArr['move_date'] == "1969-12-31") {
                                    $leadDataArr['move_date'] = $currentDate;
                                    //$routeLeadArr[] = 1; // Set Default Move Date is current so route lead at a time
                                    //$phoneLead = 1;
                                }
                                if ($leadDataArr['move_date'] < $currentDate) {
                                    $routeLeadArr[] = 1;
                                    $phoneLead = 1;
                                }
                            }*/
                            if ($fields == "select_your_moving_date") {
                                $leadDataArr['move_date'] = date("Y-m-d", strtotime($currentDate. ' + 2 days'));
                                if (strtotime($field_data[$g]['values'][0]) == "in_about_15_days") {
                                    $leadDataArr['move_date'] = date("Y-m-d", strtotime($currentDate. ' + 10 days'));
                                } else if (strtotime($field_data[$g]['values'][0]) == "in_about_a_month") {
                                    $leadDataArr['move_date'] = date("Y-m-d", strtotime($currentDate. ' + 20 days'));
                                } else if (strtotime($field_data[$g]['values'][0]) == "in_about_2_months") {
                                    $leadDataArr['move_date'] = date("Y-m-d", strtotime($currentDate. ' + 45 days'));
                                } else if (strtotime($field_data[$g]['values'][0]) == "after_2_months_from_now") {
                                    $leadDataArr['move_date'] = date("Y-m-d", strtotime($currentDate. ' + 75 days'));
                                }
                            }
                            if ($fields == "what_type_of_move_") {
                                if (isset($field_data[$g]['values'][0]) && $field_data[$g]['values'][0] != "") {
                                    if (strpos($field_data[$g]['values'][0], '_') !== false) {
                                        $moveSize = explode("_", $field_data[$g]['values'][0]);
                                        // echo "move_size1 = "; print_r( $moveSize);
                                        if (count($moveSize) > 0) {
                                            if($moveSize[0] == "5+"){
                                                $leadDataArr['move_size'] = 6;
                                            }else{
                                                $leadDataArr['move_size'] = $moveSize[0] + 1;
                                            }

                                        }
                                    } else {
                                        $leadDataArr['move_size'] = "1";
                                    }
                                }
                            }
                            if (strtoupper($fields) == "FULL_NAME") {
                                $leadDataArr['name'] = $field_data[$g]['values'][0];
                            }
                            if (strtoupper($fields) == "EMAIL") {
                                $leadDataArr['email'] = $field_data[$g]['values'][0];
                            }
                            if (strtoupper($fields) == "PHONE_NUMBER") {
                                $phone = $field_data[$g]['values'][0];
                                $phone = preg_replace('/[^0-9]/', '', $phone);
                                $phoneLength = strlen($phone);
                                if ($phoneLength > 10) {
                                    $phone = substr($phone, -10);
                                }
                                if (strlen($phone) < 10) {
                                    $emailLead = 1;
                                }
                                $invalidPhone = strlen($phone) != 10  || preg_match('/^1/', $phone) || preg_match('/^.11/', $phone) || preg_match('/^...1/', $phone) || preg_match('/^....11/', $phone) || preg_match('/^.9/', $phone);
                                if ($invalidPhone == true) {
                                    $emailLead = 1;
                                }

                                $leadDataArr['phone'] = $phone;
                            }
                            $leadDataArr['lead_category'] = 1;
                            $leadDataArr['fbrouteLead'] = 0;
                        }

                        if ($is_clickup == "yes") {
                            if (strtoupper($fields) == "COMPANY_NAME") {
                                $leadDataArr['company_name'] = $field_data[$g]['values'][0];
                            }
                            if (strtoupper($fields) == "PLEASE_MENTION_REQUIRE_SERVICES_FOR_LEADS._EG._MOVING_SERVICE,_JUNK_REMOVAL,_CAR_TRANSPORTION_OR_HEAVY_EQUIPMENT_") {
                                $leadDataArr['category'] = $field_data[$g]['values'][0];
                            }
                            if (strtoupper($fields) == "FULL_NAME") {
                                $leadDataArr['name'] = $field_data[$g]['values'][0];
                            }
                            if (strtoupper($fields) == "CONTACT_EMAIL") {
                                $leadDataArr['email'] = $field_data[$g]['values'][0];
                            }
                            if (strtoupper($fields) == "CONTACT_NUMBER") {
                                $phone = $field_data[$g]['values'][0];
                                $leadDataArr['phone'] = preg_replace('/[^0-9]/', ' ', $phone);
                            }
                        }
                    }
                    // echo "<pre>";
                    // print_r($leadDataArr);
                    // die;
                    if ($is_clickup == 'yes') {
                        $listid = "35847782";
                        $uri = 'https://api.clickup.com/api/v2/list/' . $listid . '/task/';
                        $auth = array(
                            "authorization: pk_10583605_OAFCZ2LB8DXVIFIXDNBVYH5MAIVGZOW3",
                            "content-type: application/json"
                        );
                        $curdate = strtotime(now());
                        $curdate = $curdate * 1000;
                        $data = array(
                            'name' => $leadDataArr['name'],
                            'status' => 'Open',
                            "start_date" => $curdate,
                            'custom_fields' =>
                            array(
                                0 =>
                                array(
                                    'id' => 'f5fb82f9-5597-4431-a542-b73964a6a888',
                                    'value' => '+1 ' . strval(preg_replace("~.*(\d{3})[^\d]{0,7}(\d{3})[^\d]{0,7}(\d{4}).*~", "$1 $2 $3", $leadDataArr["phone"])) . '',
                                ),
                                1 =>
                                array(
                                    'id' => '5a163b5d-595b-44ac-b845-85553cf86010',
                                    'value' => $leadDataArr['email'],
                                ),
                                2 =>
                                array(
                                    'id' => 'e00233ee-80f5-4907-8453-795a99d8a462',
                                    'value' => $leadDataArr['company_name'],
                                ),
                                3 =>
                                array(
                                    'id' => '0a123838-20fc-44ff-a974-f1d7e7f03570',
                                    'value' => $leadDataArr['category'],
                                ),
                                4 =>
                                array(
                                    'id' => 'e08fbbb4-c838-4b60-96aa-cef6ab3c6f23',
                                    'value' => 'FBForm',
                                ),
                            ),
                        );
                        // echo '<pre>';
                        // print_r($data);
                        // die;
                        // var_dump($data);
                        $res = curl_init($uri);
                        curl_setopt($res, CURLOPT_POST, TRUE);
                        curl_setopt($res, CURLOPT_RETURNTRANSFER, TRUE);
                        curl_setopt($res, CURLOPT_SSL_VERIFYPEER, FALSE);
                        curl_setopt($res, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
                        curl_setopt($res, CURLOPT_HTTPHEADER, $auth); // authenticate
                        curl_setopt($res, CURLOPT_POSTFIELDS, json_encode($data));
                        $result = curl_exec($res);
                        //send to active campaign
                        $this->sendEmailToActiveCampaign($leadDataArr);

                        //DB::table('test')->insert(['result' => $fbId . '==B2B FB Lead Send to Clickup ' . $result]);
                    } else {
                        if (in_array(1, $routeLeadArr)) {
                            $leadDataArr['fbrouteLead'] = 1;
                        }
                        $devdebuglg->devDebuglog($cron_id, $fbId . '==FB Lead route Data ' . json_encode($leadData[$i]));
                        // echo "<pre>";
                        // print_r($leadDataArr);
                        // die;
                        // echo "<br>====================================================<br>";
                        // dump($emailLead, $phoneLead);
                        $lead_json = json_encode($leadDataArr);
                        $devdebuglg->devDebuglog($cron_id, $fbId . '==json_encode data= ' . $lead_json);
                        if ($emailLead > 0) {
                            //dump("EMAIL");
                            $leadEmailStoreObj = new LeadEmailInsert();
                            $response = $leadEmailStoreObj->InsertEmailLead(json_encode($leadDataArr));
                            $devdebuglg->devDebuglog($cron_id, $fbId . '==Email Lead==' . $phoneLead . ',FB Lead route Response ' . $response);
                        } else if ($phoneLead > 0) {
                            $leadPhoneStorObj = new LeadPhoneInsert();
                            $response = $leadPhoneStorObj->InsertPhoneLead(json_encode($leadDataArr));
                            $devdebuglg->devDebuglog($cron_id, $fbId . '==Phone Lead==' . $phoneLead . ',FB Lead route Response ' . $response);
                            // echo '<pre>phone response = '; print_r($response);
                        } else {
                            //dump("SIMPLE");
                            $leadStorObj = new LeadInsert();
                            $response = $leadStorObj->InsertLead(json_encode($leadDataArr));
                            $devdebuglg->devDebuglog($cron_id, $fbId . '==Lead==' . $phoneLead . ',FB Lead route Response ' . $response);
                            $response_decode = json_decode($response, true);
                            if( $response_decode['error'] == "Invalid to_zip" || $response_decode['error'] == "Invalid from_zip"){
                                $leadDataArr['fbrouteLead'] = 1;
                                $leadPhoneStorObj = new LeadPhoneInsert();
                                $response1 = $leadPhoneStorObj->InsertPhoneLead(json_encode($leadDataArr));
                                $devdebuglg->devDebuglog($cron_id, $fbId . '==new Phone Lead==' . $phoneLead . ', new FB Lead route Response ' . $response1);
                            }

                            // echo '<pre>lead Exception1 = '; print_r($response);
                        }
                        // echo '<pre>lead Exception1 = '; print_r($response);
                    }
                } else {
                    //dump("no");
                    $devdebuglg->devDebuglog($cron_id, $fbId . '==FB Lead route Not Match Time ' . $newCreatedTime . "==" . $created_time . "==" . $currentDate . "==" . $backDateTime);
                }
            }
            return ['status' => 'success'];
        } else if ($apiResponse['error']) {
            return ['status' => 'fail', 'error' => $apiResponse['error']];
        } else {
            return ['status' => 'fail', 'error' => $apiResponse];
        }
    }

    public function sendEmailToActiveCampaign($data)
    {
        //echo '<pre>'; print_r($data); die;
        $url            = 'https://movingally.activehosted.com';
        $listId         = 4;

        $splitname      = explode(' ', $data['name']);
        if (count($splitname) >= 1) {
            $first_name = $splitname[0];
            $lastnamearray = array();
            foreach ($splitname as $keyl => $valuel) {
                if ($keyl != 0) {
                    $lastnamearray[] = $valuel;
                }
            }
            $last_name  = implode(' ', $lastnamearray);
        } else {
            $first_name = $splitname[0];
            $last_name  = '';
        }
        $emailresult['result'] = "valid";

        if (
            $emailresult['result'] == "valid" ||
            $emailresult['result'] == "catchall" ||
            $emailresult['status'] == "general_failure" ||
            $emailresult['status'] == "auth_failure" ||
            $emailresult['status'] == "temp_unavail" ||
            $emailresult['status'] == "throttle_triggered" ||
            $emailresult['status'] == "bad_referrer"
        ) {

            $params     = array(

                // the API Key can be found on the "Your Settings" page under the "API" tab.
                // replace this with your API Key
                'api_key'                   => 'a833c253b6c30a9bc6e929b4e312b0c63fe7cc51fb5e948ce028f83d429cf5b3e968e0f5',

                // this is the action that adds a contact
                'api_action'                => 'contact_sync',

                // define the type of output you wish to get back
                // possible values:
                // - 'xml'  :      you have to write your own XML parser
                // - 'json' :      data is returned in JSON format and can be decoded with
                //                 json_decode() function (included in PHP since 5.2.0)
                // - 'serialize' : data is returned in a serialized format and can be decoded with
                //                 a native unserialize() function
                'api_output'                => 'serialize',
            );

            // here we define the data we are posting in order to perform an update
            $postFields     = array(
                'email'                     => $data['email'],
                'first_name'                => $first_name,
                'last_name'                 => $last_name,
                'phone'                     => $data['phone'],
                //'customer_acct_name'      => $leadsemaildata->name,
                //'ip4'                     => '127.0.0.1',

                // any custom fields
                // 'field[345,0]'           => 'field value', // where 345 is the field ID
                //'field[%SOURCE%,0]'       => 'MA (FBForm)',
                'field[%COMPANY_NAME%,0]'   => $data['company_name'],

                // assign to lists:
                'p[' . $listId . ']'            => $listId, // example list ID (REPLACE '123' WITH ACTUAL LIST ID, IE: p[5] = 5)
                'status[' . $listId . ']'       => 1, // 1: active, 2: unsubscribed (REPLACE '123' WITH ACTUAL LIST ID, IE: status[5] = 1)
                //'form'                    => 1001, // Subscription Form ID, to inherit those redirection settings
                //'noresponders[123]'       => 1, // uncomment to set "do not send any future responders"
                //'sdate[123]'              => '2009-12-07 06:00:00', // Subscribe date for particular list - leave out to use current date/time
                // use the folowing only if status=1
                //'instantresponders['.$listId.']' => 1, // set to 0 to if you don't want to sent instant autoresponders
                //'lastmessage[123]'        => 1, // uncomment to set "send the last broadcast campaign"
                //'p[]'                     => 345, // some additional lists?
                //'status[345]'             => 1, // some additional lists?
            );
            //echo '<pre>'; print_r(json_encode($postFields)); die;
            // LeadActiveCamapaignLog::create([
            //     'lead_id'                   => 0,
            //     'log'                       => json_encode($postFields)
            // ]);
            // This section takes the input fields and converts them to the proper format
            $query = "";
            foreach ($params as $key => $value) $query .= urlencode($key) . '=' . urlencode($value) . '&';
            $query = rtrim($query, '& ');

            // This section takes the input data and converts it to the proper format
            $data = "";
            foreach ($postFields as $key => $value) $data .= urlencode($key) . '=' . urlencode($value) . '&';
            $data = rtrim($data, '& ');

            // clean up the url
            $url = rtrim($url, '/ ');

            //echo $data;
            //exit(0);

            // define a final API request - GET
            //echo $query; die;
            $api = $url . '/admin/api.php?' . $query;

            $request = curl_init($api); // initiate curl object
            curl_setopt($request, CURLOPT_HEADER, 0); // set to 0 to eliminate header info from response
            curl_setopt($request, CURLOPT_RETURNTRANSFER, 1); // Returns response data instead of TRUE(1)
            curl_setopt($request, CURLOPT_POSTFIELDS, $data); // use HTTP POST to send form data
            //curl_setopt($request, CURLOPT_SSL_VERIFYPEER, FALSE); // uncomment if you get no gateway response and are using HTTPS
            curl_setopt($request, CURLOPT_FOLLOWLOCATION, true);

            $response = (string)curl_exec($request); // execute curl post and store results in $response
            // additional options may be required depending upon your server configuration
            // you can find documentation on curl options at http://www.php.net/curl_setopt
            curl_close($request); // close curl object
            if (!$response) {
                throw new Exception("Nothing was returned. Do you have a connection to Email Marketing server?");
            }

            // This line takes the response and breaks it into an array using:
            // JSON decoder
            $result = json_decode($response);
            // unserializer
            $result = unserialize($response);

            // LeadActiveCamapaignLog::create([
            //     'lead_id'                   => 0,
            //     'log'                       => json_encode($result)
            // ]);
            // return $result;
        } else {
            // LeadActiveCamapaignLog::create([
            //     'lead_id'                   => 0,
            //     'log'                       => 'Request not send due to invalid email'
            // ]);
        }
    }
}
