<?php

namespace App\Http\Controllers\Emails;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Emails\TemplateEmail;
use App\Exports\CountrywideRelocationLeadExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Emails\EmailLog;

use PDF;
use Exception;
use DB;
use Log;

class PrimeMarketingCountrywideRelocationInvoiceEmailController extends Controller
{
    public function primeCountrywideRelocationInvoiceEmailSend()
    {
        // Store on a different disk (e.g. s3)
        $startDate      = date("Y-m-d", mktime(0, 0, 0, date("m")-1, 1));
        $endDate        = date("Y-m-d", mktime(0, 0, 0, date("m"), 0));
        $month          = date('F', strtotime(date('F') . " last month"));
        $year           = date('Y', strtotime(date('Y') . " last month"));
        $csvfileName    = 'CountrywideRelocation_'.$month.'_'.$year.'.xlsx';
        $pdffileName    = 'CountrywideRelocation_'.$month.'_'.$year.'.pdf';
        //Excel::store(new CountrywideRelocationLeadExport(), $csvfileName, 'export');
        //Excel::store(new USAVanLinesLeadExport(), '/export/' . $csvfileName);

        $leadDetail     = DB::select("SELECT bus.email, bus.business_name,
                                      COUNT(DISTINCT CASE WHEN (cm.move_type = 'local' ) AND lr.route_status='sold' THEN lr.lead_routing_id ELSE NULL END) as local_leads,
                                      COUNT(DISTINCT CASE WHEN (cm.move_type = 'long' ) AND lr.route_status = 'sold' THEN lr.lead_routing_id ELSE NULL END) as long_leads,
                                      IFNULL(SUM(CASE WHEN lr.route_status='sold' THEN lr.payout END), 0) as total_amount
                                      FROM
                                      campaign
                                      LEFT JOIN (SELECT business_name, email, business_id from business) AS bus ON campaign.business_id = bus.business_id
                                      LEFT JOIN lead_routing AS lr ON lr.campaign_id = campaign.campaign_id
                                      LEFT JOIN (SELECT campaign_id, move_type from campaign_moving) AS cm ON campaign.campaign_id = cm.campaign_id
                                      WHERE
                                      lr.campaign_id IN (5316, 5315, 5314, 5313, 5271, 5140, 5139, 1783, 1214, 1198, 1197, 1196)  AND (lr.created_at >= '".$startDate." 00:00:00' AND lr.created_at <= '".$endDate." 23:59:59')
                                      GROUP BY campaign.campaign_id");
        $leadArray      = [];
        $invoiceMonth   = 0;
        $totalLeads     = 0;
        $totalAmount    = 0;
        if (isset($leadDetail) && !empty($leadDetail)) {
            foreach ($leadDetail as $lead) {
                //$invoiceMonth = $lead->month;
                $totalLeads += $lead->long_leads;
                $totalAmount += $lead->total_amount;
            }
            $leadArray  = array(
                'month' => date('m', strtotime("-1 months")),
                'total_leads' => 6267, //$totalLeads,
                'total_amount' => 6267 * 2 //$totalAmount
            );
        }
        view()->share('leadDetail', $leadArray);
        $pdf            = PDF::loadView('exports.countrywiderelocationinvoicepdf', $leadArray);
        $path           = public_path('/export/');
        $pdf->save($path . $pdffileName);

        // dd('sent');
        //send email
        $this->sendEmail(array($csvfileName, $pdffileName), $leadArray);

        //remove image from export directory
        //$this->destroy(array($csvfileName, $pdffileName));
        echo "Invoice email sent successfully."; exit();
    }

    public function sendEmail ($fileName, $leadDetail)
    {
        $subject        = 'Countrywide Relocation LLC Invoice and lead list - ' . date('F', mktime(0, 0, 0, $leadDetail['month'], 10)) . ' ' . date('Y', strtotime(date('Y') . " last month"));
        //$subject      = 'Countrywide Relocation LLC Invoice of Booked Jobs';
        $setFrom        = '<EMAIL>';
        $setTo          = '<EMAIL>';
        //$setTo        = '<EMAIL>';
        $postmarkToken  = '************************************';

        $mailBodyData   = array();
        $mail           = new TemplateEmail();


        $html           = 'Hello Countrywide Relocation LLC,<br><br> We want to let you know that Prime Marketing has sent you a Monthly Purchased Leads <strong>Invoice of $' . @number_format($leadDetail['total_leads'] * 2, 2) . '</strong> for <strong>' . date('F', mktime(0, 0, 0, $leadDetail['month'], 10)) . ', ' . date('Y', strtotime(date('Y') . " last month")) . '</strong>.<br><br> Please find the attached invoice and purchased lead details.<br><br> Please don\'t hesitate to get in <NAME_EMAIL> if you have any questions or need clarifications.<br><br> Best regards,<br> Client Support,<br> Prime Marketing LLC,<br> <a href="http://mover.primemarketing.us/">www.primemarketing.us</a>';
        //$html         = 'Hello Countrywide Relocation LLC,<br><br> We want to let you know that Prime Marketing has sent you a Booked Jobs <strong>Invoice of $' . @number_format(200 * 100, 2) . '</strong> till <strong>' . date('F', mktime(0, 0, 0, 7, 10)) . ', ' . date('Y') . '</strong>.<br><br> Please find the attached invoice and purchased lead details.<br><br> Please don\'t hesitate to get in <NAME_EMAIL> if you have any questions or need clarifications.<br><br> Best regards,<br> Client Support,<br> Prime Marketing LLC,<br> <a href="http://mover.primemarketing.us/">www.primemarketing.us</a>';
        // send email
        $mail->setTo($setTo);
        //$mail->setBcc('<EMAIL>');
        $mail->setFrom($setFrom);
        $mail->setSubject($subject);
        $mail->setHtmlbody($html);
        $excelContent   = base64_encode(file_get_contents(public_path() . '/export/' . $fileName[0]));
        $pdfContent     = base64_encode(file_get_contents(public_path() . '/export/' . $fileName[1]));
        //$attchment[]  = ['Name' => $fileName[0], 'Content' => $excelContent, 'ContentType' => 'application/excel'];
        $attchment[]    = ['Name' => $fileName[1], 'Content' => $pdfContent, 'ContentType' => 'application/octet-stream'];
        //echo '<pre>'; print_r($attchment); die;
        $mail->setAttachment($attchment);
        $response = $mail->send_email($token = $postmarkToken, 'invoice');

        EmailLog::create([
            "business_id" => 525,
            "subject" => $subject,
            "set_to" => $setTo,
            "set_from" => $setFrom,
            "response" => json_encode($response->original['data']),
            "type" => 'invoice',
        ]);
    }

    public function destroy($fileName)
    {
        for ($i=0; $i < count($fileName); $i++) {
            $path = app_path("export/" . $fileName[$i]);
            unlink($path);
        }
    }
}
