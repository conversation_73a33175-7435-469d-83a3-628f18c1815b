<?php 

namespace App\Models\Emails;
use Illuminate\Database\Eloquent\Model;   
use App\Helpers\Helper;
use App\Models\Emails\EmailLog;
use App\Models\Master\MstMoveSize;
use App\Models\Lead\LeadActivity;
class DNCLeadFailedEmail extends Model {
     
    public function emailDNCLeadFailedEmail($emaildata, $emailresponse ){ 
        
        $dncfor = [];
        $postmarkey = Helper::checkServer()['postmark_token'];
        $business_id = $emaildata['business_id'];
        $lead_id = $emaildata['lead_id'];
        $campaign_name = ucfirst($emaildata['campaign_name']);
        $business_name = ucfirst($emaildata['business_name']);
        $failed_to = $emaildata['failed_to'];
        $emailMessage= $emailresponse['Message'];        
        // echo "<pre>emailDNCLeadFailedEmail = ";
        // print_r(  $emailresponse );
        // die;    
        $contents = "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='ISO-8859-1'>
            <title>Linkup</title>
        </head>
        <body>
        <table>
            <tr><td>DNC notification email failed for following.</td></tr>
            <tr>
                <td>
                    <table>
                        <tr><td width='200px'>Business Name</td><td> : </td><td>". $business_name ."</td></tr>
                        <tr><td width='200px'>Business DNC Email</td><td> : </td><td>". $failed_to ."</td></tr>
                        <tr><td width='200px'>Campaign Name</td><td> : </td><td>". $campaign_name ."</td></tr>
                        <tr style='vertical-align: top;'><td width='200px'>Error</td><td> : </td><td>". $emailMessage ."</td></tr>
                    </table>
                </td>
            </tr>
        </table>
        </body>
        </html>";
       
        // $json = json_encode(array(
        //     'From' => $from,
        //     'To' => $to, 
        //     'Subject' => $subject, 
        //     'HtmlBody' => $contents      
        // ));
        // $from = "<EMAIL>";
        $from = "<EMAIL>";
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        $to = "<EMAIL>,<EMAIL>";
        if (strpos($matrixBaseURL_Outbound, '127.0.0.1') !== false || strpos($matrixBaseURL_Outbound, 'tweetstage.tweetsoftware.co') !== false) {
            $to = "<EMAIL>,<EMAIL>";
        } else if (strpos($matrixBaseURL_Outbound, 'linkup.software') !== false) {
            $to = "<EMAIL>,<EMAIL>,<EMAIL>";
        }
        $subject = "DNC Notification Email Failed: ".  $business_name;
        $json = json_encode(array(
            'From' => $from,
            'To' => $to, 
            'Subject' => $subject,
            'HtmlBody' => $contents,
            'Headers' => [],
            'Attachments' => []
        ));
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.postmarkapp.com/email');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Accept: application/json',
            'Content-Type: application/json',
            'X-Postmark-Server-Token: ' . $postmarkey
        ));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
        $result = curl_exec($ch);
        $response = json_decode($result, true); 
        $status = 'failed';      
        if($response['Message'] == 'OK' ){           
            $status = 'success'; 
        } 
        $leadactiviyobj = new LeadActivity();
        //$leadactiviyobj->createActivity($lead_id, 4);
        EmailLog::create([
            "business_id" => $business_id,
            "subject" => $subject,
            "set_to" => $to,
            "set_from" => $from,
            "response" => $result,
            "type" => "dncemail_failed",
            'created_at' => date('Y-m-d H:i:s')
        ]);
        return $status;
    }
  
}
