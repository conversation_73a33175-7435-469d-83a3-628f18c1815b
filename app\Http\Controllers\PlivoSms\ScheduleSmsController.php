<?php

namespace App\Http\Controllers\PlivoSms;

use App\Helpers\CommonFunctions;
use App\Http\Controllers\Controller;
use App\Models\Campaign\Campaign;
use App\Models\User;
use App\Models\PlivoSms\ScheduleSMS;
use App\Models\Campaign\CampaignOrganic;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ScheduleSmsController extends Controller
{
    public function view (Request $request)
    {
        $campaignArray = $scheduleSMSArray = [];
        $scheduleSMSDetail = ScheduleSMS::get();
        $campaignDetail = Campaign::where('campaign_type_id', 4)->get(['campaign_id', 'campaign_name']);
        for ($c=0; $c<count($campaignDetail); $c++) {
            $campaignArray[$campaignDetail[$c]->campaign_id] = $campaignDetail[$c]->campaign_name;
        }
        //echo '<pre>'; print_r($campaignArray); die;
        foreach ($scheduleSMSDetail as $key => $scheduleSMS) {
            $campaignStr            = '';
            if ($scheduleSMS->campaign_id != "") {
                $campaignIds        = explode(',', $scheduleSMS->campaign_id);
                foreach ($campaignIds as $key => $campaignId) {
                    if ($key > 0) {
                        $campaignStr.= ', ' . $campaignId . '-' . $campaignArray[$campaignId];
                    } else {
                        $campaignStr.= $campaignId . '-' . $campaignArray[$campaignId];
                    }
                }
            }
            $scheduleSMSArray[]     = array(
                'schedule_sms_id'   => $scheduleSMS->schedule_sms_id,
                'sms_subject'       => $scheduleSMS->sms_subject,
                'campaign_id'       => $campaignStr,
                'created_at'        => date("m/d/Y H:i:s", strtotime($scheduleSMS->created_at))
            );
        }
        return view("schedulesms.list")->with('scheduleSMSArray', $scheduleSMSArray)->with('campaignDetail', $campaignDetail);
    }

    public function insert(Request $request) {
        $error = "";
        $status = 0;
        $message = 'Failure';

        if (isset($request->campaignIds) && count($request->campaignIds) > 0) {
            $validator = Validator::make($request->all(),[
                'smsSubject' => 'required'
            ]);
        } else {
            $validator = Validator::make($request->all(),[
                'smsSubject' => 'required',
                'campaignIds'=>'required|gt:0'
            ]);
        }

        if ($validator->fails()) {
            return response()->json(array(
                'success' => 0,
                /*'message' => 'There are incorect values in the form!',*/
                'error' => $validator->getMessageBag()->toArray()
            ), 200);
            $this->throwValidationException(
                $request, $validator
            );
        }

        try {
            ScheduleSMS::create([
                'sms_subject' => $request->smsSubject,
                'campaign_id' => implode(',', array_unique($request->campaignIds)),
                'created_at' => date('Y-m-d H:i:s')
            ]);

            $status = 1;
            $message = 'Success';
        } catch(Exception $e) {
            $message = $e->getMessage();
        }

        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => array('code'=>"System Error", 'message' => $message)
        ];
        return response()->json($responseData);
    }

    public function edit(Request $request) {
        $status = 0;
        $message = 'Failure';
        $businessArray = $campaignArray = [];
        try {
            $scheduleSmsId = $request->scheduleSmsId;
            $scheduleSMSDetail = ScheduleSMS::where('schedule_sms_id', $scheduleSmsId)->first();
            $campaignDetail = Campaign::where('campaign_type_id', 4)->get(['campaign_id', 'campaign_name']);
            for ($c=0; $c<count($campaignDetail); $c++) {
                $campaignArray[$campaignDetail[$c]->campaign_id] = $campaignDetail[$c]->campaign_name;
            }

            $businessArray[] = [
                'scheduleSmsId' => $scheduleSMSDetail->schedule_sms_id,
                'smsSubject'    => $scheduleSMSDetail->sms_subject,
                'campaignId'    => ($scheduleSMSDetail->campaign_id != "") ? explode(',', $scheduleSMSDetail->campaign_id) : [],
            ];

            $status = 1;
            $message = "Success";
        } catch(Exception $e) {
            $message = $e->getMessage();
        }
        $responseData = [
            'status' => $status,
            'message' => $message,
            'data' => $businessArray,
            'campaignArray' => $campaignArray,
        ];
        return response()->json($responseData);
    }

    public function update(Request $request) {
        $error = "";
        $status = 0;
        $message = 'Failure';

        if (isset($request->campaignIds) && count($request->campaignIds) > 0) {
            $validator = Validator::make($request->all(),[
                'smsSubject' => 'required'
            ]);
        } else {
            $validator = Validator::make($request->all(),[
                'smsSubject' => 'required',
                'campaignIds'=>'required|gt:0'
            ]);
        }

        if ($validator->fails()) {
            return response()->json(array(
                'success' => 0,
                /*'message' => 'There are incorect values in the form!',*/
                'error' => $validator->getMessageBag()->toArray()
            ), 200);
            $this->throwValidationException(
                $request, $validator
            );
        }

        try {
            $scheduleSmsId = $request->scheduleSmsId;
            ScheduleSMS::where('schedule_sms_id', $scheduleSmsId)->update([
                'sms_subject' => $request->smsSubject,
                'campaign_id' => implode(',', array_unique($request->campaignIds))
            ]);

            $status = 1;
            $message = 'Success';
        } catch(Exception $e) {
            $message = $e->getMessage();
        }

        $responseData = [
            'status' => $status,
            'message' => $message,
            'error' => array('code'=>"System Error", 'message' => $message)
        ];
        return response()->json($responseData);
    }

    public function delete(Request $request)
    {
        $status = 0;
        $message = 'fail';
        $error = array();
        try {
            // remove data
            ScheduleSMS::where("schedule_sms_id", $request->scheduleSmsId)->delete();
            $status = 1;
            $message = 'Deleted Successfully';
            $responseData = ['status' => $status, 'message' => $message, 'error' => $error];
            return response()->json($responseData);
        } catch (Exception $ex) {
            dd($ex);
            return redirect('/schedulesms-list');
        }
    }

    public function preview(Request $request) {
        $status = 0;
        $message = 'fail';
        $smsSubject = $text = '';
        $error = array();
        try {
            // remove data
            $scheduleSmsId = $request->scheduleSmsId;
            $scheduleSMS = ScheduleSMS::where('schedule_sms_id', $scheduleSmsId)->first();
            $smsSubject = $scheduleSMS->sms_subject;
            if (isset($scheduleSMS->campaign_id)) {
                $campaignIds = explode(',', $scheduleSMS->campaign_id);
                $businessData = CampaignOrganic::whereIn('campaign_id', $campaignIds)->whereNotNull('contact_number')->orderByRaw("FIELD(campaign_id, $scheduleSMS->campaign_id)")->get(['business_name', 'contact_number']);
            } else {
                $responseArray = [
                    'status' => $status,
                    'message' => $message,
                    'data' => $text,
                    'error' => $error
                ];
                return response()->json($responseArray);
            }

            if ($scheduleSMS->sms_subject == 'Spot Routing SMS') {
                $key = 0;
                $sWhere = "";
                foreach ($businessData as $value) {
                    if (count($businessData) == 2) {
                        if ($key > 0) {
                            $sWhere.= "<br>📞 " . $value['business_name'] . ": +1 " . $this->formatPhoneNumber($value['contact_number']) . "<br>📞 Quote The Move: ******-767-2122";
                        } else {
                            $sWhere.= "<br>📞 " . $value['business_name'] . ": +1 " . $this->formatPhoneNumber($value['contact_number']);
                        }
                    } else if (count($businessData) == 1) {
                        $sWhere.= "<br>📞 " . $value['business_name'] . ": +1 " . $this->formatPhoneNumber($value['contact_number']) . "<br>📞 Quote The Move: ******-767-2122<br>📞 Interstates Mover: ******-566-3184";
                    }
                    $key++;
                }
                $text = "Still moving to Chattanooga?<br>These movers are ready with discounted quotes:" . $sWhere . "<br>Call today for your best deal! 📦<br>Reply STOP to unsubscribe.";
            } else if ($scheduleSMS->sms_subject == 'Lead Confirmation Message (3 Days)') {
                $sWhere = "";
                foreach ($businessData as $value) {
                    $sWhere.= "<br>📞 " . $value->business_name.": +1 ".$this->formatPhoneNumber($value->contact_number);
                }
                $text = "Hi Lawrence Dailey<br>⚠️ Prices going up soon! Book now to lock your discounted quote:" . $sWhere . "<br>📞 Interstates Mover: ******-566-3184<br>Reply STOP to opt out.";
            } else if ($scheduleSMS->sms_subject == 'Lead Confirmation Message (5 Days)') {
                $sWhere = "";
                foreach ($businessData as $value) {
                    $sWhere.= "<br>📞 " . $value->business_name.": +1 ".$this->formatPhoneNumber($value->contact_number);
                }
                $text = "Hi Lawrence Dailey<br>🎉 Movers are booking up fast in Chattanooga! These 5-star movers still have openings:" . $sWhere . "<br>📞 Interstates Mover: ******-566-3184<br> Call for your custom quote now!<br> Reply STOP to unsubscribe.";
            } else if ($scheduleSMS->sms_subject == 'Lead Confirmation Message (10 Days)') {
                $sWhere = "";
                foreach ($businessData as $value) {
                    $sWhere.= "<br>📞 " . $value->business_name.": +1 ".$this->formatPhoneNumber($value->contact_number);
                }
                $text = "Hi Lawrence Dailey<br>⏳ Last chance to grab a deal on your move!" . $sWhere . "<br>📞 Quote The Move: ******-767-2122<br> Final discounted slots filling up!<br> Reply STOP to unsubscribe.";
            } else if ($scheduleSMS->sms_subject == 'Lead Confirmation Message (15 Days)') {
                $sWhere = "";
                foreach ($businessData as $value) {
                    $sWhere.= "<br>📞 " . $value->business_name.": +1 ".$this->formatPhoneNumber($value->contact_number);
                }
                $text = "Hi Lawrence Dailey<br>🚨 Last-minute cancellation = YOUR DEAL! Save now on your move to Chattanooga:" . $sWhere . "<br>📞 Interstates Mover: ******-566-3184<br> Call now before it’s gone.<br> Reply STOP to opt out.";
            } else if ($scheduleSMS->sms_subject == 'One Hour SMS') {
                $key = 0;
                $sWhere = "";
                foreach ($businessData as $value) {
                    if (count($businessData) == 2) {
                        if ($key > 0) {
                            $sWhere.= "<br>📞 " . $value['business_name'] . ": +1 " . $this->formatPhoneNumber($value['contact_number']) . "<br>📞 Quote The Move: ******-767-2122";
                        } else {
                            $sWhere.= "<br>📞 " . $value['business_name'] . ": +1 " . $this->formatPhoneNumber($value['contact_number']);
                        }
                    } else if (count($businessData) == 1) {
                        $sWhere.= "<br>📞 " . $value['business_name'] . ": +1 " . $this->formatPhoneNumber($value['contact_number']) . "<br>📞 Quote The Move: ******-767-2122<br>📞 Interstates Mover: ******-566-3184";
                    } else {
                        $sWhere.= "<br>📞 Quote The Move: ******-767-2122<br>📞 Interstates Mover: ******-566-3184";
                    }
                    $key++;
                }
                $text = "Hi Lawrence Dailey<br>You may qualify for up to $500 off your move to Chattanooga! 💰<br>Call now before rates change:" . $sWhere . "<br>Reply STOP to opt out.";
            }

            $status = 1;
            $message = 'Success';
        } catch (Exception $ex) {
            dd($ex);
        }

        $responseArray = [
            'status' => $status,
            'message' => $message,
            'subject' => $smsSubject,
            'data' => $text,
            'error' => $error
        ];
        return response()->json($responseArray);
    }

    function formatPhoneNumber($number) {
        // Remove any non-numeric characters
        $number = preg_replace('/\D/', '', $number);

        // Check if the number has exactly 10 digits
        if (strlen($number) === 10) {
            // Format the number
            $formattedNumber = sprintf('%s-%s-%s',
                substr($number, 0, 3),  // Area code
                substr($number, 3, 3),  // Prefix
                substr($number, 6, 4)   // Line number
            );
            return $formattedNumber;
        } else {
            // Return an error or handle invalid number
            return 'Invalid number';
        }
    }
}