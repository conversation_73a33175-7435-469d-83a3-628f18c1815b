<?php

namespace App\Http\Controllers\API\LeadEmail;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Helpers\CommonFunctions;
use App\Models\Lead\LeadActiveCamapaignLog;
use App\Models\Lead\LeadInvalid; //Added By SG On 08/02/2023 For lead_invalid table
use App\Models\Lead\LeadMoving; //Added By BK On 03/01/2023 For lead table
use App\Models\LeadEmail\LeadEmail; //Added By BK On 03/01/2023 For lead_email table
use App\Models\LeadEmail\LeadEmailMoving; //Added By BK On 03/01/2023 For lead_email_moving table
use App\Models\LeadEmail\LeadEmailJunkRemoval; //Added By BK On 03/01/2023 For lead_email_junk_removal table
use App\Models\LeadEmail\LeadEmailHeavyLifting; //Added By BK On 03/01/2023 For lead_email_heavy_lifting table
use App\Models\LeadEmail\LeadEmailCarTransport; //Added By BK On 12/12/2022 For lead_car_transport table
use App\Models\Lead\LeadRequestLog; //Added By BK On 08/12/2022 For lead_request_log table
use App\Models\LeadEmail\LeadEmailActiveCamapaignLog;
use App\Models\LeadEmail\LeadEmailLandingPage; //Added By BK On 08/12/2022 For lead_landing_page table
use App\Models\Master\MstZipcode; //Added By BK On 08/12/2022 For mst_zipcode table
use App\Models\Master\MstLeadSource; //Added By BK On 08/12/2022 For mst_lead_source table
use App\Models\Master\MstAPIToken; //Added By BK On 09/12/2022 For mst_api_token table
use App\Models\Master\MstJunkSubType; //Added By BK On 12/12/2022 For mst_junk_sub_type table
use App\Models\Master\MstHeavyLiftingType; //Added By BK On 12/12/2022 For mst_heavy_lifting_type table
use App\Models\Master\MstCarType; //Added By BK On 12/12/2022 For mst_car_type table
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use DateTime;
use App\Models\LeadEmail\LeadEmailJunkRemovalSubType;

class LeadEmailInsert extends Controller {

    public function InsertEmailLead($postData = "") {
        $status = "Failure";
        $leadEmailId = $sourceId = $junkSubTypeId = $heavyLiftingTypeId = $carTypeId = $campaignId = $adGroupId = 0;
        $error = $ip = $fromData = $toData = $source = $utmCampaign = $utmMedium = $adGroup = $keyword = $device = $gclid = $landingPage = "";
        $isDuplicate = $isVerified = $isDncCall = $isDncSMS = $isDncEmail = $isRequiredAssistence = $isOperational = 'no';

        try {
            $elements = json_decode(file_get_contents('php://input'), true);
            // Log::info('Lead Email Request: ' . json_encode($elements));

            $request = file_get_contents('php://input');
            if (empty($elements)) {
                $elements = json_decode($postData, true);
            }
            //get all API header data pass in header
            //$header = apache_request_headers();
            //check origin position
            $checkToken = DB::select("SELECT api_token_id, SUBSTR(source, 1) AS source, token FROM mst_api_token WHERE token = '".$elements['token']."'");
            if (empty($checkToken)) {
                throw new Exception("Unauthorized Token");
            }

            //for lead_source_id get from mst_lead_source table
            if (!empty($elements['origin'])) {
                $sourceName = MstLeadSource::read($elements['origin']);
                if ($sourceName == false) {
                    //insert lead origin in mst_lead_source table
                    $sourceId = MstLeadSource::create([
                                'lead_source_name' => $elements['origin']
                            ])->lead_source_id;
                } else {
                    $sourceId = $sourceName->lead_source_id;
                }
            }

            //lead_category 1-Moving, 2-Junk Removal, 3-Heavy Lifting, 4-Car Transportation
            $categoryId = 1;
            if (!empty($elements['lead_category'])) {
                $categoryId = $elements['lead_category'];
            }

            //email filter validation
            $elements['email'] = preg_replace('/\s+/', '', $elements['email']);

            //move_date
            $moveDate = LeadMoving::getMovedate($elements['move_date']);
            $date1 = new DateTime();
            $date2 = new DateTime($moveDate);

            //initial validations
            /*if (empty($elements['name'])) {
                throw new Exception("Name required");
            } elseif (preg_match("/([%\$#\*]+)/", $elements['name']) || preg_match('~[0-9]+~', $elements['name'])) {
                throw new Exception("Invalid Name format");
            } else*/if (empty($elements['email'])) {
                throw new Exception("Email required");
            } elseif (filter_var($elements['email'], FILTER_VALIDATE_EMAIL) === false) {
                throw new Exception("Invalid email format");
            } elseif (empty($elements['from_zip'])) {
                throw new Exception("From zip required");
            } elseif (strlen($elements['from_zip']) < 5) {
                throw new Exception("From zip must be 5 digit only");
            } elseif (empty($elements['to_zip']) && $categoryId != "2") {
                throw new Exception("To zip required");
            } elseif (strlen($elements['to_zip']) < 5 && $categoryId != "2") {
                throw new Exception("To zip must be 5 digit only");
            } elseif (empty($elements['move_date'])) {
                throw new Exception("Move date error");
            } elseif (LeadMoving::checkDateFormat($elements['move_date']) == false) {
                throw new Exception("Invalid Move date format");
            } elseif ($date2 < $date1) {
                throw new Exception("Move date should be greater than current date");
            }

            //from_zip
            $fromZipCode = $elements['from_zip'];
            //to_zip
            $toZipCode = $elements['to_zip'];
            //get zipcode data from mst_zipcode table pass from_zip & to_zip both argument mendatory
            $zipcodeData = MstZipcode::read($fromZipCode, $toZipCode);
            if (strcmp($fromZipCode, $toZipCode) && $toZipCode) {
                if ($fromZipCode == $zipcodeData[0]['zipcode']) {
                    $fromData = $zipcodeData[0];
                } elseif (isset($zipcodeData[1])) {
                    $fromData = $zipcodeData[1];
                }
                if ($toZipCode == $zipcodeData[0]['zipcode']) {
                    $toData = $zipcodeData[0];
                } elseif (isset($zipcodeData[1])) {
                    $toData = $zipcodeData[1];
                }
            } else {
                $fromData = $toData = $zipcodeData[0];
            }

            //initial validations for from_zip & to_zip
            if ($fromData == null) {
                throw new Exception("Invalid from_zip");
            } elseif ($toData == null && $categoryId != "2") {
                throw new Exception("Invalid to_zip");
            }

            //name
            $name = trim(preg_replace('/\s\s+/', ' ', str_replace("\n", " ", $elements['name'])));

            //email
            $email = strtolower($elements['email']);

            //move_size filter validation
            $moveSize = preg_replace("/\D/", "", trim($elements['move_size']));

            //from areacode, city, state data get from mst_zipcode table
            $fromAreaCode = $fromData['areacode'];
            $fromCity = strtoupper($fromData['city']);
            $fromState = strtoupper($fromData['state']);

            //to areacode, city, state data get from mst_zipcode table
            //to areacode, city, state data get from mst_zipcode table
            if ($categoryId != "2") {
                $toAreaCode = $toData['areacode'];
                $toCity = strtoupper($toData['city']);
                $toState = strtoupper($toData['state']);
            }

            //check isDuplicate Leads
            $pos1 = strpos($elements['origin'], 'VM');
            $pos2 = strpos($elements['origin'], 'QTM');
            $pos3 = strpos($elements['origin'], 'IS');
            if ($pos1 !== false) {
                $sourceName = 'VM';
            } else if ($pos2 !== false) {
                $sourceName = 'QTM';
            } else if ($pos3 !== false) {
                $sourceName = 'IS';
            }
            if (LeadEmail::isDuplicateEmail($email, $sourceId, $elements['is_verified'], $categoryId, $sourceName)) {
                throw new Exception("Lead recieved, but was not recorded since it was a duplicate");
            }

            //check isEmailDuplicate Leads
            if (LeadEmail::isEmailDuplicate($sourceId, $email, $categoryId, $sourceName)) {
                $isDuplicate = 'yes';
            }

            if (!empty($elements['is_verified']) && $elements['is_verified'] == '1') {
                $isVerified = 'yes';
            }
            $ip = $elements['IP'];

            //calculate distance
            $distance = 0;
            if (!empty($fromData['lat']) && !empty($toData['lat']) && !empty($fromData['long']) && !empty($toData['long'])) {
                $distance = LeadMoving::calculateDistance($fromData['lat'], $toData['lat'], $fromData['long'], $toData['long']);
            }
            //check lead distance type (ex. Local, Long)
            $leadDistanceType = "local";
            if ($categoryId != "2") {
                $leadDistanceType = LeadMoving::checkLeadDistanceType($fromState, $toState);
            }

            if (!empty($elements['is_required_assistence']) && $elements['is_required_assistence'] == '1') {
                $isRequiredAssistence = 'yes';
            }

            if (!empty($elements['is_operational']) && $elements['is_operational'] == '1') {
                $isOperational = 'yes';
            }

            //insert lead request log in lead_request_log table
            $leadRequestLogId = LeadRequestLog::create([
                        'lead_request' => json_encode($elements)
                    ])->lead_request_log_id;

            $generatedAt = date("Y-m-d H:i:s");
            if (isset($elements['generated_at'])) {
                $generatedAt = $elements['generated_at'];
            }

            //insert lead data in lead table
            $leadEmailId = LeadEmail::create([
                        'lead_source_id' => $sourceId,
                        'lead_category_id' => $categoryId,
                        'name' => $name,
                        'email' => $email,
                        'is_duplicate' => $isDuplicate,
                        'is_dnc_email' => $isDncEmail,
                        'lead_generated_at' => $generatedAt,
                        'lead_request_log_id' => $leadRequestLogId,
                        'created_at' => date("Y-m-d H:i:s")
                    ])->lead_email_id;

            //common field array
            $commonFieldArray = [];
            $commonFieldArray['lead_email_id'] = $leadEmailId;
            $commonFieldArray['from_zipcode'] = $fromZipCode;
            $commonFieldArray['from_areacode'] = $fromAreaCode;
            $commonFieldArray['from_city'] = $fromCity;
            $commonFieldArray['from_state'] = $fromState;
            $commonFieldArray['created_at'] = date("Y-m-d H:i:s");

            if ($categoryId != "2") {
                $commonFieldArray['to_zipcode'] = $toZipCode;
                $commonFieldArray['to_areacode'] = $toAreaCode;
                $commonFieldArray['to_city'] = $toCity;
                $commonFieldArray['to_state'] = $toState;
                $commonFieldArray['move_type'] = $leadDistanceType;
                $commonFieldArray['distance'] = $distance;
            }

            if ($categoryId == "3" || $categoryId == "4") {
                $commonFieldArray['is_required_assistence'] = $isRequiredAssistence;
                $commonFieldArray['is_operational'] = $isOperational;
            }

            //Moving
            if ($categoryId == "1") {
                $propertyType = (!empty($elements['property_type'])) ? trim($elements['property_type']) : 'residential';

                //push in common field array
                $commonFieldArray['move_size_id'] = $moveSize;
                $commonFieldArray['move_date'] = $moveDate;
                $commonFieldArray['property_type'] = $propertyType;

                //insert record in lead_moving table
                LeadEmailMoving::create($commonFieldArray)->lead_email_moving_id;
                //Junk Removal
            } else if ($categoryId == "2") {
                $junkTypeId = (!empty($elements['junk_type'])) ? trim($elements['junk_type']) : '';
                $junkSubTypeId = (!empty($elements['junk_sub_type'])) ? $elements['junk_sub_type'] : null;
                $ownerType = (!empty($elements['owner_type'])) ? trim($elements['owner_type']) : 'owner';
                $description = (!empty($elements['description'])) ? trim($elements['description']) : '';
                $propertyType = (!empty($elements['property_type'])) ? trim($elements['property_type']) : 'residential';

                //push in common field array
                $commonFieldArray['junk_remove_date'] = $moveDate;
                $commonFieldArray['junk_type_id'] = $junkTypeId;
                $commonFieldArray['owner_type'] = $ownerType;
                $commonFieldArray['description'] = $description;
                $commonFieldArray['property_type'] = $propertyType;

                //insert record in lead_junk_removal table
                LeadEmailJunkRemoval::create($commonFieldArray)->lead_email_junk_removal_id;
                //insert record in lead_email_junk_removal_sub_type table
                if( $junkSubTypeId != null ){
                    // echo "Not null";
                    $junkSubTypeIdArr = explode (",", $junkSubTypeId);
                    $junksubtypeArr = [];
                    foreach ($junkSubTypeIdArr as $key => $value) {
                        $junksubtypeArr[] = [
                            "lead_email_id" => $leadEmailId,
                            "junk_sub_type_id" => $value,
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                    }
                    LeadEmailJunkRemovalSubType::insert($junksubtypeArr);
                }
                //Heavy Lifting
            } else if ($categoryId == "3") {
                $heavyLiftingTypeId = (!empty($elements['heavy_lifting_type'])) ? trim($elements['heavy_lifting_type']) : '';
                //push in common field array
                $commonFieldArray['lift_date'] = $moveDate;
                $commonFieldArray['heavy_lifting_type_id'] = $heavyLiftingTypeId;

                //insert record in lead_heavy_lifting table
                LeadEmailHeavyLifting::create($commonFieldArray)->lead_email_heavy_lifting_id;
                //Car Transportation
            } else if ($categoryId == "4") {
                $carTypeId = (!empty($elements['car_type'])) ? trim($elements['car_type']) : '';
                $transportType = ($elements['transport_type'] > 0) ? 'open' : 'enclosed';
                $vehicleCondition = (isset($elements['vehicle_condition']) && $elements['vehicle_condition'] > 0) ? 'running' : 'not running';
                //push in common field array
                $commonFieldArray['move_date'] = $moveDate;
                $commonFieldArray['car_type_id'] = $carTypeId;
                $commonFieldArray['transport_type'] = $transportType;
                $commonFieldArray['vehicle_condition'] = $vehicleCondition;

                //insert record in lead_car_transport table
                LeadEmailCarTransport::create($commonFieldArray)->lead_email_car_transport_id;
            }

            $utmCampaign = (!empty($elements['utm_campaign'])) ? preg_replace('/-+/', '-', trim($elements['utm_campaign'])) : ""; //utmCampaign
            $utmMedium = (!empty($elements['utm_medium'])) ? trim($elements['utm_medium']) : ""; //utmMedium
            $utmCampaignId = (!empty($elements['camp_id'])) ? trim($elements['camp_id']) : 0; //$utmCampaignId
            $adGroupId = 0;
            if (!empty($elements['ad_grp_id'])) {
                $adGroupId = trim($elements['ad_grp_id']);
            } else if (!empty($elements['ad_group_id'])) {
                $adGroupId = trim($elements['ad_group_id']);
            }
            //$adGroupId = (!empty($elements['ad_group_id'])) ? trim($elements['ad_group_id']) : 0; //adGroupId
            $adGroup = "";
            if (!empty($elements['ad_grp'])) {
                $adGroup = trim($elements['ad_grp']);
            } else if (!empty($elements['ad_group'])) {
                $adGroup = trim($elements['ad_group']);
            }
            //$adGroup = (!empty($elements['ad_group'])) ? trim($elements['ad_group']) : ""; //adGroup
            $keyword = (!empty($elements['keyword'])) ? trim($elements['keyword']) : ""; //keyword
            $device = (!empty($elements['device'])) ? trim($elements['device']) : ""; //device
            $gclid = (!empty($elements['GCLID'])) ? trim($elements['GCLID']) : ""; //gclid
            $gclidType = (!empty($elements['gclid_type'])) ? trim($elements['gclid_type']) : ""; //gclid
            $landingPage = (!empty($elements['Landing_Page'])) ? trim($elements['Landing_Page']) : ""; //landingPage
            $trustedFormCertId = (!empty($elements['xxTrustedFormToken'])) ? trim($elements['xxTrustedFormToken']) : ""; //xxTrustedFormToken

            //insert lead landing page data in lead_landing_page table
            $leadLandingPageId = LeadEmailLandingPage::create([
                        'lead_email_id' => $leadEmailId,
                        'utm_campaign' => $utmCampaign,
                        'utm_medium' => $utmMedium,
                        'landing_campaign_id' => $utmCampaignId,
                        'ad_group_id' => $adGroupId,
                        'ad_group' => $adGroup,
                        'search_keyword' => $keyword,
                        'device' => $device,
                        'gclid' => $gclid,
                        'gclid_type' => $gclidType,
                        'landing_page' => $landingPage,
                        'lead_source_id' => $sourceId,
                        'ip' => $ip,
                        'trusted_form_cert_id' => $trustedFormCertId,
                        'created_at' => $elements['generated_at']
                    ])->lead_landing_page_id;

            $this->leademailNophoneSendtoactiveCampaign($leadEmailId, $elements['lead_id']);
            $status = "Success";
            $response = array(
                'status' => $status,
                'lead_id' => $leadEmailId,
                'error' => $error
            );

            //insert lead response log in lead_request_log table
            LeadRequestLog::where('lead_request_log_id', $leadRequestLogId)->update([
                'lead_response' => json_encode($response)
            ]);
        } catch (Exception $e) {
            $error = $e->getMessage();

            //insert exception in lead_invalid table - start
            LeadInvalid::create([
                'request' => $request,
                'response' => str_replace(array('\'', '"'), '', $error),
                'lead_source_id' => $sourceId,
                'created_at' => date("Y-m-d H:i:s")
            ]);
            //insert exception in lead_invalid table - end

            $response = array(
                'status' => $status,
                'lead_id' => $leadEmailId,
                'error' => $error
            );
        }
        return json_encode($response);
    }

    public function leademailNophoneSendtoactiveCampaign($leadId, $element_lead_id)
    {
        //$leadId = 1;
        $sourceName = '';
        // $listid = 4; // active campaigin no phone list id.
        $element_lead_id = base64_encode($element_lead_id);
        $enctypted_leadId = base64_encode($leadId);
        $url = 'https://primemarketing15119.api-us1.com';
        $leadsemaildata = LeadEmail::with(['LeadEmailMoving', 'LeadEmailJunkRemoval', 'LeadEmailHeavyLifting', 'LeadEmailCarTransport', 'MstLeadSource'])->where('lead_email_id', $leadId)->get()->first();
        //echo '<pre>'; print_r($leadsemaildata->toArray()); die;
        $origin = MstLeadSource::where('lead_source_id', $leadsemaildata->lead_source_id)->get()->first();
        $listid = 0;
        $originName = "";
        $pos = strpos($origin->lead_source_name, 'VM');
        $pos1 = strpos($origin->lead_source_name, 'QTM');
        $pos2 = strpos($origin->lead_source_name, 'IS');
        if ($pos !== false) {
            $listid = 35;
            /*if ($leadsemaildata->lead_category_id == "1") {
                $url = 'https://primemarketing17812.api-us1.com';
            }*/
            if ($leadsemaildata->lead_category_id == "2") {
                $listid = 21;
            } else if ($leadsemaildata->lead_category_id == "3") {
                $listid = 23;
            } else if ($leadsemaildata->lead_category_id == "4") {
                $listid = 25;
            }
            $originName = "VM";
        } else if ($pos1 !== false) {
            $listid = 7;
            if ($leadsemaildata->lead_category_id == "2") {
                $listid = 27;
            } else if ($leadsemaildata->lead_category_id == "3") {
                $listid = 29;
            } else if ($leadsemaildata->lead_category_id == "4") {
                $listid = 31;
            }
            $originName = "QTM";
        } else if ($pos2 !== false) {
            $listid = 14;
            if ($leadsemaildata->lead_category_id == "2") {
                $listid = 21;
            } else if ($leadsemaildata->lead_category_id == "3") {
                $listid = 23;
            } else if ($leadsemaildata->lead_category_id == "4") {
                $listid = 25;
            }
            $originName = "IS";
        }

        if ($leadsemaildata->lead_category_id == "1") {
            $move_sizeLabel = 0;
        if ($leadsemaildata->LeadEmailMoving->move_size_id == 1) {
                $move_sizeLabel = 'Studio';
        } else if ($leadsemaildata->LeadEmailMoving->move_size_id == 2) {
                $move_sizeLabel = '1 Bedroom';
        } else if ($leadsemaildata->LeadEmailMoving->move_size_id == 3) {
                $move_sizeLabel = '2 Bedrooms';
        } else if ($leadsemaildata->LeadEmailMoving->move_size_id == 4) {
                $move_sizeLabel = '3 Bedrooms';
        } else if ($leadsemaildata->LeadEmailMoving->move_size_id == 5) {
                $move_sizeLabel = '4 Bedrooms';
        } else if ($leadsemaildata->LeadEmailMoving->move_size_id == 6) {
                $move_sizeLabel = '5+ Bedrooms';
            }
        } else if ($leadsemaildata->lead_category_id == "2") {
            $junRemovalType = "Nothing";
            if ($leadsemaildata->LeadEmailJunkRemoval->junk_type_id == "1") {
                $junRemovalType = 'Haul Waste';
            } else if ($leadsemaildata->LeadEmailJunkRemoval->junk_type_id == "2") {
                $junRemovalType = 'Appliance Removal';
            } else if ($leadsemaildata->LeadEmailJunkRemoval->junk_type_id == "3") {
                $junRemovalType = 'Waste Dump';
            }
        } else if ($leadsemaildata->lead_category_id == "3") {
            $heavyLiftingType = "Nothing";
            if ($leadsemaildata->LeadEmailHeavyLifting->heavy_lifting_type_id == "1") {
                $heavyLiftingType = 'Heavy Equipment';
            } else if ($leadsemaildata->LeadEmailHeavyLifting->heavy_lifting_type_id == "2") {
                $heavyLiftingType = 'Freight Shipping';
            } else if ($leadsemaildata->LeadEmailHeavyLifting->heavy_lifting_type_id == "3") {
                $heavyLiftingType = 'Heavy Duty Trucks';
            } else if ($leadsemaildata->LeadEmailHeavyLifting->heavy_lifting_type_id == "4") {
                $heavyLiftingType = 'Boat Transport';
            } else if ($leadsemaildata->LeadEmailHeavyLifting->heavy_lifting_type_id == "5") {
                $heavyLiftingType = 'RV Shipping';
            } else if ($leadsemaildata->LeadEmailHeavyLifting->heavy_lifting_type_id == "6") {
                $heavyLiftingType = 'Trailer';
            }
        } else if ($leadsemaildata->lead_category_id == "4") {
            $carTypeId  = $leadsemaildata->LeadEmailCarTransport->car_type_id;
            $carTypeDetail = MstCarType::where("car_type_id", $carTypeId)->first();
            $carType = $carTypeDetail->car_type;
            $transportType = ucfirst($leadsemaildata->LeadEmailCarTransport->transport_type);
        }
        $splitname = explode(' ', $leadsemaildata->name);
        if (count($splitname) >= 1) {
            $first_name = $splitname[0];
            $lastnamearray = array();
            foreach ($splitname as $keyl => $valuel) {
                if ($keyl != 0) {
                    $lastnamearray[] = $valuel;
                }
            }
            $last_name  = implode(' ', $lastnamearray);
        } else {
            $first_name = $splitname[0];
            $last_name  = '';
        }
        $sourceName = $leadsemaildata->MstLeadSource->lead_source_name;
        $params = array(
            // the API Key can be found on the "Your Settings" page under the "API" tab.
            // replace this with your API Key
            'api_key'                       => '48bc7240813d9983486c007bc65bd4b2d7b8c4e46d0bf6ce8831abd8ed594b8e50fc1705',
            // this is the action that adds a contact
            'api_action'                    => 'contact_sync',
            // define the type of output you wish to get back
            // possible values:
            // - 'xml'  :      you have to write your own XML parser
            // - 'json' :      data is returned in JSON format and can be decoded with
            //                 json_decode() function (included in PHP since 5.2.0)
            // - 'serialize' : data is returned in a serialized format and can be decoded with
            //                 a native unserialize() function
            'api_output'                    => 'serialize',
        );
        // here we define the data we are posting in order to perform an update

        if ($leadsemaildata->lead_category_id == "1") {
            $post = array(
                'email'                     => $leadsemaildata->email,
                'first_name'                => ucwords(strtolower($first_name)),
                'last_name'                 => ucwords(strtolower($last_name)),
                'phone'                     => '',
                //'customer_acct_name'      => $leadsemaildata->name,
                'tags'                      => 'api',
                //'ip4'                     => '127.0.0.1',
                // any custom fields
                // 'field[345,0]'           => 'field value', // where 345 is the field ID
                'field[FIRSTNAME,0]'        => ucwords(strtolower($first_name)), // using the personalization tag instead (make sure to encode the key)
                'field[LASTNAME,0]'         => ucwords(strtolower($last_name)),
                'field[EMAIL,0]'            => $leadsemaildata->email,
                'field[%UNIQUE_KEY%,0]'     => $element_lead_id,
                'field[%SOURCE%,0]'         => $sourceName,
                'field[%PHONE_VERIFIED%,0]' => $leadsemaildata->is_verified,
                'field[%FROM_ZIP%,0]'       => $leadsemaildata->LeadEmailMoving->from_zipcode,
                'field[%TO_ZIP%,0]'         => $leadsemaildata->LeadEmailMoving->to_zipcode,
                'field[%MOVE_DATE%,0]'      => $leadsemaildata->LeadEmailMoving->move_date,
                'field[%MOVE_TYPE%,0]'      => $leadsemaildata->LeadEmailMoving->move_type,
                'field[%MOVE_SIZE%,0]'      => $move_sizeLabel,
                'field[%CONFIRMATION_NUMBER%,0]' => $leadsemaildata->lead_email_moving_id,
                'field[%FROM_CITY%,0]'      => $leadsemaildata->LeadEmailMoving->from_city,
                'field[%TO_CITY%,0]'        => $leadsemaildata->LeadEmailMoving->to_city,
                'field[%FROM_STATE%,0]'     => $leadsemaildata->LeadEmailMoving->from_state,
                'field[%TO_STATE%,0]'       => $leadsemaildata->LeadEmailMoving->to_state,
                // assign to lists:
                'p[' . $listid . ']'            => $listid, // example list ID (REPLACE '123' WITH ACTUAL LIST ID, IE: p[5] = 5)
                'status[' . $listid . ']'       => 1, // 1: active, 2: unsubscribed (REPLACE '123' WITH ACTUAL LIST ID, IE: status[5] = 1)
                //'form'                    => 1001, // Subscription Form ID, to inherit those redirection settings
                //'noresponders[123]'       => 1, // uncomment to set "do not send any future responders"
                //'sdate[123]'              => '2009-12-07 06:00:00', // Subscribe date for particular list - leave out to use current date/time
                // use the folowing only if status=1
                'instantresponders[' . $listid . ']' => 1, // set to 0 to if you don't want to sent instant autoresponders
                //'lastmessage[123]'        => 1, // uncomment to set "send the last broadcast campaign"

                //'p[]'                     => 345, // some additional lists?
                //'status[345]'             => 1, // some additional lists?
            );
        } else if ($leadsemaildata->lead_category_id == "2") {
            $post = array(
                'email'                     => $leadsemaildata->email,
                'first_name'                => ucwords(strtolower($first_name)),
                'last_name'                 => ucwords(strtolower($last_name)),
                'phone'                     => '',
                //'customer_acct_name'      => $leadsemaildata->name,
                'tags'                      => 'api',
                //'ip4'                     => '127.0.0.1',
                // any custom fields
                // 'field[345,0]'           => 'field value', // where 345 is the field ID
                'field[FIRSTNAME,0]'        => ucwords(strtolower($first_name)), // using the personalization tag instead (make sure to encode the key)
                'field[LASTNAME,0]'         => ucwords(strtolower($last_name)),
                'field[EMAIL,0]'            => $leadsemaildata->email,
                'field[%UNIQUE_KEY%,0]'     => $element_lead_id,
                'field[%SOURCE%,0]'         => $sourceName,
                'field[%PHONE_VERIFIED%,0]' => $leadsemaildata->is_verified,
                'field[%ZIP_CODE%,0]'       =>  $leadsemaildata->LeadEmailJunkRemoval->from_zipcode,
                'field[%DATE_OF_JUNK_REMOVAL%,0]' => $leadsemaildata->LeadEmailJunkRemoval->junk_remove_date,
                'field[%WASTE_MATERIAL_REMOVAL_TYPE%,0]' => $junRemovalType,
                'field[%TWEET_ENCRY_ID%,0]' => $enctypted_leadId,
                // assign to lists:
                'p[' . $listid . ']'            => $listid, // example list ID (REPLACE '123' WITH ACTUAL LIST ID, IE: p[5] = 5)
                'status[' . $listid . ']'       => 1, // 1: active, 2: unsubscribed (REPLACE '123' WITH ACTUAL LIST ID, IE: status[5] = 1)
                //'form'                    => 1001, // Subscription Form ID, to inherit those redirection settings
                //'noresponders[123]'       => 1, // uncomment to set "do not send any future responders"
                //'sdate[123]'              => '2009-12-07 06:00:00', // Subscribe date for particular list - leave out to use current date/time
                // use the folowing only if status=1
                'instantresponders[' . $listid . ']' => 1, // set to 0 to if you don't want to sent instant autoresponders
                //'lastmessage[123]'        => 1, // uncomment to set "send the last broadcast campaign"

                //'p[]'                     => 345, // some additional lists?
                //'status[345]'             => 1, // some additional lists?
            );
        } else if ($leadsemaildata->lead_category_id == "3") {
            $post = array(
                'email'                     => $leadsemaildata->email,
                'first_name'                => ucwords(strtolower($first_name)),
                'last_name'                 => ucwords(strtolower($last_name)),
                'phone'                     => '',
                //'customer_acct_name'      => $leadsemaildata->name,
                'tags'                      => 'api',
                //'ip4'                     => '127.0.0.1',
                // any custom fields
                // 'field[345,0]'           => 'field value', // where 345 is the field ID
                'field[FIRSTNAME,0]'        => ucwords(strtolower($first_name)), // using the personalization tag instead (make sure to encode the key)
                'field[LASTNAME,0]'         => ucwords(strtolower($last_name)),
                'field[EMAIL,0]'            => $leadsemaildata->email,
                'field[%UNIQUE_KEY%,0]'     => $element_lead_id,
                'field[%SOURCE%,0]'         => $sourceName,
                'field[%PHONE_VERIFIED%,0]' => $leadsemaildata->is_verified,
                'field[%ZIP_CODE%,0]'       => $leadsemaildata->LeadEmailHeavyLifting->from_zipcode,
                'field[%HAULING_FROM%,0]'   => $leadsemaildata->LeadEmailHeavyLifting->from_zipcode,
                'field[%HAULING_TO%,0]'     => $leadsemaildata->LeadEmailHeavyLifting->to_zipcode,
                'field[%HAULING_DATE%,0]'   => $leadsemaildata->LeadEmailHeavyLifting->lift_date,
                'field[%HEAVY_LIFTING_TYPE%,0]' => $heavyLiftingType,
                'field[%TWEET_ENCRY_ID%,0]' => $enctypted_leadId,
                // assign to lists:
                'p[' . $listid . ']'            => $listid, // example list ID (REPLACE '123' WITH ACTUAL LIST ID, IE: p[5] = 5)
                'status[' . $listid . ']'       => 1, // 1: active, 2: unsubscribed (REPLACE '123' WITH ACTUAL LIST ID, IE: status[5] = 1)
                //'form'                    => 1001, // Subscription Form ID, to inherit those redirection settings
                //'noresponders[123]'       => 1, // uncomment to set "do not send any future responders"
                //'sdate[123]'              => '2009-12-07 06:00:00', // Subscribe date for particular list - leave out to use current date/time
                // use the folowing only if status=1
                'instantresponders[' . $listid . ']' => 1, // set to 0 to if you don't want to sent instant autoresponders
                //'lastmessage[123]'        => 1, // uncomment to set "send the last broadcast campaign"

                //'p[]'                     => 345, // some additional lists?
                //'status[345]'             => 1, // some additional lists?
            );
        } else if ($leadsemaildata->lead_category_id == "4") {
            $post = array(
                'email'                     => $leadsemaildata->email,
                'first_name'                => ucwords(strtolower($first_name)),
                'last_name'                 => ucwords(strtolower($last_name)),
                'phone'                     => '',
                //'customer_acct_name'      => $leadsemaildata->name,
                'tags'                      => 'api',
                //'ip4'                     => '127.0.0.1',
                // any custom fields
                // 'field[345,0]'           => 'field value', // where 345 is the field ID
                'field[FIRSTNAME,0]'        => ucwords(strtolower($first_name)), // using the personalization tag instead (make sure to encode the key)
                'field[LASTNAME,0]'         => ucwords(strtolower($last_name)),
                'field[EMAIL,0]'            => $leadsemaildata->email,
                'field[%UNIQUE_KEY%,0]'     => $element_lead_id,
                'field[%SOURCE%,0]'         => $sourceName,
                'field[%PHONE_VERIFIED%,0]' => $leadsemaildata->is_verified,
                'field[%ZIP_CODE%,0]'       => $leadsemaildata->LeadEmailCarTransport->from_zipcode,
                'field[%TRANSPORT_CAR_FROM%,0]' => $leadsemaildata->LeadEmailCarTransport->from_zipcode,
                'field[%TRANSPORT_CAR_TO%,0]' => $leadsemaildata->LeadEmailCarTransport->to_zipcode,
                'field[%CAR_MOVE_DATE%,0]'  => $leadsemaildata->LeadEmailCarTransport->move_date,
                'field[%CAR_MADE%,0]'       => $carType,
                'field[%TRAILER_TYPE_OPENCLOSED%,0]' => $transportType,
                'field[%TWEET_ENCRY_ID%,0]' => $enctypted_leadId,
                // assign to lists:
                'p[' . $listid . ']'            => $listid, // example list ID (REPLACE '123' WITH ACTUAL LIST ID, IE: p[5] = 5)
                'status[' . $listid . ']'       => 1, // 1: active, 2: unsubscribed (REPLACE '123' WITH ACTUAL LIST ID, IE: status[5] = 1)
                //'form'                    => 1001, // Subscription Form ID, to inherit those redirection settings
                //'noresponders[123]'       => 1, // uncomment to set "do not send any future responders"
                //'sdate[123]'              => '2009-12-07 06:00:00', // Subscribe date for particular list - leave out to use current date/time
                // use the folowing only if status=1
                'instantresponders[' . $listid . ']' => 1, // set to 0 to if you don't want to sent instant autoresponders
                //'lastmessage[123]'        => 1, // uncomment to set "send the last broadcast campaign"

                //'p[]'                     => 345, // some additional lists?
                //'status[345]'             => 1, // some additional lists?
            );
        }

        LeadEmailActiveCamapaignLog::create([
            'lead_id' => $leadsemaildata->lead_email_id,
            'log'     => json_encode($post)
        ]);
        // This section takes the input fields and converts them to the proper format
        $query = "";
        foreach ($params as $key => $value) $query .= urlencode($key) . '=' . urlencode($value) . '&';
        $query = rtrim($query, '& ');

        // This section takes the input data and converts it to the proper format
        $data = "";
        foreach ($post as $key => $value) $data .= urlencode($key) . '=' . urlencode($value) . '&';
        $data = rtrim($data, '& ');
        // clean up the url
        $url = rtrim($url, '/ ');
        //echo $data;
        //exit(0);
        // define a final API request - GET
        $api = $url . '/admin/api.php?' . $query;
        $request = curl_init($api); // initiate curl object
        curl_setopt($request, CURLOPT_HEADER, 0); // set to 0 to eliminate header info from response
        curl_setopt($request, CURLOPT_RETURNTRANSFER, 1); // Returns response data instead of TRUE(1)
        curl_setopt($request, CURLOPT_POSTFIELDS, $data); // use HTTP POST to send form data
        //curl_setopt($request, CURLOPT_SSL_VERIFYPEER, FALSE); // uncomment if you get no gateway response and are using HTTPS
        curl_setopt($request, CURLOPT_FOLLOWLOCATION, true);
        $response = (string)curl_exec($request); // execute curl post and store results in $response
        // additional options may be required depending upon your server configuration
        // you can find documentation on curl options at http://www.php.net/curl_setopt
        curl_close($request); // close curl object
        if (!$response) {
            throw new Exception("Nothing was returned. Do you have a connection to Email Marketing server?");
        }
        // This line takes the response and breaks it into an array using:
        // JSON decoder
        //$result = json_decode($response);
        // unserializer
        $result = unserialize($response);
        LeadEmailActiveCamapaignLog::create(['lead_id' => $leadsemaildata->lead_email_id, 'log' => json_encode($result)]);
        return $result;
    }

}
