<?php

namespace App\Http\Controllers\Report;


use App\Http\Controllers\Controller;
use App\Models\Lead\Lead;
use App\Models\Master\MstLeadCategory;
use App\Models\Lead\LeadCustomerReview;
use App\Models\Businesses\Business;
use App\Models\Campaign\Campaign;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

class HourlyReportController extends Controller
{
    function __construct()
    {
        $this->middleware('permission:hourlyreport', ['only' => ['index']]);
        // $this->middleware('permission:business-review-report', ['only' => ['index', 'getReviewReportData', 'getReviewReportDataDtails']]);
    }

    public function index(Request $request) {

        $daterange = $request->get('daterange');
        $distanceType = $request->get('distanceType');
        $movingSize = $request->get('movingSize');
        $LeadsDataArr = array();
        $LeadsDataArr = $this->getBusinessCampaignData($daterange, $distanceType, $movingSize );
        return view("reports.hourlyreport")->with("leadData", $LeadsDataArr);
    }


    function getBusinessCampaignData($daterange, $distanceType, $movingSize) {
        $sWhere = "";
        $where  = "";
        $date_filter = "";
        $moving_size_filter = "";

        if ($daterange != "") {
            $dateRangeArray = explode(' - ', $daterange);
            $startDate = date("Y-m-d", strtotime($dateRangeArray[0])) . ' 00:00:00';
            $endDate = date("Y-m-d", strtotime($dateRangeArray[1])) . ' 23:59:59';
            $date_filter = "`l`.created_at BETWEEN '$startDate' AND '$endDate'";
        }else{
            $startDate = date("Y-m-d") . ' 00:00:00';
            $endDate = date("Y-m-d") . ' 23:59:59';
            $date_filter = "`l`.created_at BETWEEN '$startDate' AND '$endDate'";
        }

        if ($movingSize != "") {
            $moving_size_filter = " AND lmi.move_size_id = '$movingSize'";
        }

        $LeadData = DB::select(
            "SELECT  l.lead_id AS LeadID ,
                    l.created_at AS LeadDate ,
                    hour(l.created_at ) AS LeadHour ,
                    l.payout AS Revenue ,l.is_verified AS IsVerified ,
                    -- COUNT(DISTINCT CASE WHEN oc.transfer_type='transfer' THEN l.lead_call_id  END) AS LiveTransferLead,
                    -- COUNT(DISTINCT CASE WHEN oc1.transfer_type='forward' THEN l.lead_call_id  END) AS CallForwardLead,
                    -- case when lmi.from_state=lmi.to_state then 'local' when lmi.from_state!=lmi.to_state then 'long' end as LeadDistance,
                    COUNT(DISTINCT l.lead_id) AS leadcount,
                    /*COUNT(DISTINCT CASE WHEN lm.lm_local_long='local' OR lhl.lhl_local_long='local' OR lct.lct_local_long='local' OR ljr.lead_id IS NOT NULL THEN l.lead_id ELSE NULL END) as state_local,*/
                    COUNT(DISTINCT CASE WHEN
                    (lm.lm_local_long='local' OR CASE WHEN l.lead_category_id = 1 AND lm.lead_id IS NULL THEN l.lead_id ELSE NULL END) OR
                    (lhl.lhl_local_long='local' OR CASE WHEN l.lead_category_id = 3 AND lhl.lead_id IS NULL THEN l.lead_id ELSE NULL END) OR
                    (lct.lct_local_long='local' OR CASE WHEN l.lead_category_id = 4 AND lct.lead_id IS NULL THEN l.lead_id ELSE NULL END) OR
                    ljr.lead_id IS NOT NULL THEN l.lead_id ELSE NULL END) as state_local,

                    COUNT(DISTINCT CASE WHEN
                    (lm.lm_local_long='local' OR CASE WHEN l.lead_category_id = 1 AND lm.lead_id IS NULL THEN l.lead_id ELSE NULL END) AND l.is_verified='yes' OR
                    (lhl.lhl_local_long='local' OR CASE WHEN l.lead_category_id = 3 AND lhl.lead_id IS NULL THEN l.lead_id ELSE NULL END) AND l.is_verified='yes' OR
                    (lct.lct_local_long='local' OR CASE WHEN l.lead_category_id = 4 AND lct.lead_id IS NULL THEN l.lead_id ELSE NULL END) AND l.is_verified='yes' OR
                    ljr.lead_id IS NOT NULL THEN l.lead_id ELSE NULL END) as IsVerifiedLocal,

                    COUNT(DISTINCT CASE WHEN
                    lm.lm_local_long='long' OR
                    lhl.lhl_local_long='long' OR
                    lct.lct_local_long='long' THEN l.lead_id ELSE NULL END) AS state_long,

                    COUNT(DISTINCT CASE WHEN
                    lm.lm_local_long='long' AND l.is_verified='yes' OR
                    lhl.lhl_local_long='long' AND l.is_verified='yes' OR
                    lct.lct_local_long='long' THEN l.lead_id ELSE NULL END) AS IsVerifiedLong,

                    IFNULL(SUM(CASE WHEN (
                        (lm.lm_local_long='local' OR CASE WHEN l.lead_category_id = 1 AND lm.lead_id IS NULL THEN l.lead_id ELSE NULL END) OR
                        (lhl.lhl_local_long='local' OR CASE WHEN l.lead_category_id = 3 AND lhl.lead_id IS NULL THEN l.lead_id ELSE NULL END) OR
                        (lct.lct_local_long='local' OR CASE WHEN l.lead_category_id = 4 AND lm.lead_id IS NULL THEN l.lead_id ELSE NULL END) OR
                        lct.lead_id IS NOT NULL) AND lr.route_status = 'sold' THEN lr.payout ELSE NULL END), 0) as LocalRevenue,

                    IFNULL(SUM(CASE WHEN (
                        lm.lm_local_long='long' OR
                        lhl.lhl_local_long='long' OR
                        lct.lct_local_long='long') AND lr.route_status = 'sold' THEN lr.payout ELSE NULL END), 0) as LongRevenue,

                COUNT(DISTINCT CASE WHEN l.is_verified='yes' THEN l.lead_id ELSE NULL END) AS IsVerified,
                IFNULL(SUM(CASE WHEN lr.route_status = 'sold' THEN lr.payout ELSE NULL END),0) AS AvarageRevenue,
                COUNT(DISTINCT CASE WHEN lmi.move_size_id = 1 AND lm.lm_local_long='local' THEN l.lead_id ELSE NULL END) AS MoveSize1CountLocal,
                COUNT(DISTINCT CASE WHEN lmi.move_size_id = 2 AND lm.lm_local_long='local' THEN l.lead_id ELSE NULL END) AS MoveSize2CountLocal,
                COUNT(DISTINCT CASE WHEN lmi.move_size_id = 3 AND lm.lm_local_long='local' THEN l.lead_id ELSE NULL END) AS MoveSize3CountLocal,
                COUNT(DISTINCT CASE WHEN lmi.move_size_id = 4 AND lm.lm_local_long='local' THEN l.lead_id ELSE NULL END) AS MoveSize4CountLocal,
                COUNT(DISTINCT CASE WHEN lmi.move_size_id = 5 AND lm.lm_local_long='local' THEN l.lead_id ELSE NULL END) AS MoveSize5CountLocal,
                COUNT(DISTINCT CASE WHEN lmi.move_size_id = 6 AND lm.lm_local_long='local' THEN l.lead_id ELSE NULL END) AS MoveSize6CountLocal,

                COUNT(DISTINCT CASE WHEN lmi.move_size_id = 1 AND lm.lm_local_long='long' THEN l.lead_id ELSE NULL END) AS MoveSize1CountLong,
                COUNT(DISTINCT CASE WHEN lmi.move_size_id = 2 AND lm.lm_local_long='long' THEN l.lead_id ELSE NULL END) AS MoveSize2CountLong,
                COUNT(DISTINCT CASE WHEN lmi.move_size_id = 3 AND lm.lm_local_long='long' THEN l.lead_id ELSE NULL END) AS MoveSize3CountLong,
                COUNT(DISTINCT CASE WHEN lmi.move_size_id = 4 AND lm.lm_local_long='long' THEN l.lead_id ELSE NULL END) AS MoveSize4CountLong,
                COUNT(DISTINCT CASE WHEN lmi.move_size_id = 5 AND lm.lm_local_long='long' THEN l.lead_id ELSE NULL END) AS MoveSize5CountLong,
                COUNT(DISTINCT CASE WHEN lmi.move_size_id = 6 AND lm.lm_local_long='long' THEN l.lead_id ELSE NULL END) AS MoveSize6CountLong

            FROM `lead` l
            LEFT JOIN lead_routing AS lr ON lr.lead_id = l.lead_id
            LEFT JOIN lead_moving AS lmi ON l.lead_id = lmi.lead_id
            -- LEFT JOIN (select * from lead_call where transfer_type='transfer') AS oc ON oc.lead_id=l.lead_id
            -- LEFT JOIN (select * from lead_call where transfer_type='forward') AS oc1 ON oc1.lead_id=l.lead_id
            LEFT JOIN mst_lead_source AS o ON l.lead_source_id=o.lead_source_id
            LEFT JOIN (SELECT lead_id,CASE WHEN ((from_state = to_state) OR (from_state IS NULL AND to_state IS NULL) OR (from_state='' AND to_state='')) THEN 'local' ELSE 'long' END AS lm_local_long from lead_moving) AS lm ON l.lead_id = lm.lead_id
            LEFT JOIN (SELECT lead_id from lead_junk_removal WHERE from_state IS NOT NULL) AS ljr ON l.lead_id = ljr.lead_id
            LEFT JOIN (SELECT lead_id,CASE WHEN ((from_state = to_state) OR (from_state IS NULL AND to_state IS NULL) OR (from_state='' AND to_state='')) THEN 'local' ELSE 'long' END AS lhl_local_long from lead_heavy_lifting) AS lhl ON l.lead_id = lhl.lead_id
            LEFT JOIN (SELECT lead_id,CASE WHEN ((from_state = to_state) OR (from_state IS NULL AND to_state IS NULL) OR (from_state='' AND to_state='')) THEN 'local' ELSE 'long' END AS lct_local_long from lead_car_transport) AS lct ON l.lead_id = lct.lead_id

            WHERE
            $date_filter
            $moving_size_filter

            GROUP BY hour(l.created_at )
            ORDER BY LeadHour",
        );

            //   echo "<pre>";print_r($LeadData);die;
        $dataArr = array();
        $finalDataArr = [];

        $call_query = DB::select("SELECT  l.lead_id AS LeadID ,
                     l.created_at AS LeadDate ,
                     hour(l.created_at ) AS LeadHour ,
                     COUNT(DISTINCT CASE WHEN oc.transfer_type='transfer' THEN oc.lead_call_id ELSE NULL END) AS LiveTransferLead,
                     COUNT(DISTINCT CASE WHEN oc.transfer_type='forward' THEN oc.lead_call_id  ELSE NULL END) AS CallForwardLead
            FROM `lead` l
            LEFT JOIN (select lead_id,transfer_type,lead_call_id from lead_transfer_call where transfer_type IN ('transfer','forward')) AS oc ON oc.lead_id=l.lead_id
            WHERE
            $date_filter
            GROUP BY hour(l.created_at )
            ORDER BY LeadHour");
        foreach ($call_query as $key1 => $value2) {
            $call_array[$value2->LeadHour] = ['live_transfer_lead'=> $value2->LiveTransferLead,'call_forward_lead'=>$value2->CallForwardLead];
        }


        foreach ($LeadData as $key => $value) {
            $LeadHour = "";
            if ($value->LeadHour == 0) {
                $LeadHour = '12 AM';
            }elseif ($value->LeadHour < 12) {
                $LeadHour = $value->LeadHour.' AM';
            }elseif ($value->LeadHour > 12) {
                $LeadHour = ($value->LeadHour-12).' PM';
            }else{
                $LeadHour = '12 PM';
            }
            if($distanceType == "local"){
                $dataArr['Leads'] = $dataArr['LocalLeads'] = $value->state_local;
                $dataArr['LongLeads'] = 0;
                $dataArr['LocalRevenue'] = $value->LocalRevenue;
                $dataArr['LongRevenue'] = 0;
                $dataArr['AvarageRevenue'] = ( $value->state_local > 0 ? number_format(($value->LocalRevenue/$value->state_local),2) : 0);
                $dataArr['VerifiedRatio'] = ( $value->state_local > 0 ? number_format(((100*$value->IsVerifiedLocal)/$value->state_local),2) : 0);
                $dataArr['MoveSize1Count'] = $value->MoveSize1CountLocal;
                $dataArr['MoveSize2Count'] = $value->MoveSize2CountLocal;
                $dataArr['MoveSize3Count'] = $value->MoveSize3CountLocal;
                $dataArr['MoveSize4Count'] = $value->MoveSize4CountLocal;
                $dataArr['MoveSize5Count'] = $value->MoveSize5CountLocal;
                $dataArr['MoveSize6Count'] = $value->MoveSize6CountLocal;
            } else if($distanceType == "long"){
                $dataArr['LocalLeads'] = 0;
                $dataArr['Leads'] = $dataArr['LongLeads'] = $value->state_long;
                $dataArr['LocalRevenue'] = 0;
                $dataArr['LongRevenue'] = $value->LongRevenue;
                $dataArr['AvarageRevenue'] = ($value->state_local > 0 && $value->state_long > 0 ? number_format(($value->LongRevenue/$value->state_long), 2) : 0);
                $dataArr['VerifiedRatio'] = ($value->state_local > 0 && $value->state_long > 0 ? number_format(((100*$value->IsVerifiedLong)/$value->state_long), 2) : 0);
                $dataArr['MoveSize1Count'] = $value->MoveSize1CountLong;
                $dataArr['MoveSize2Count'] = $value->MoveSize2CountLong;
                $dataArr['MoveSize3Count'] = $value->MoveSize3CountLong;
                $dataArr['MoveSize4Count'] = $value->MoveSize4CountLong;
                $dataArr['MoveSize5Count'] = $value->MoveSize5CountLong;
                $dataArr['MoveSize6Count'] = $value->MoveSize6CountLong;
            }else{
                $dataArr['LocalLeads'] = $value->state_local;
                $dataArr['LongLeads'] = $value->state_long;
                $dataArr['Leads'] = $value->leadcount;
                $dataArr['LocalRevenue'] = $value->LocalRevenue;
                $dataArr['LongRevenue'] = $value->LongRevenue;
                $dataArr['AvarageRevenue'] = ( $value->state_local > 0 ? number_format(($value->AvarageRevenue/$value->leadcount),2) : 0);
                $dataArr['VerifiedRatio'] = ( $value->state_local > 0 ? number_format(((100*$value->IsVerified)/$value->leadcount),2) : 0);
                $dataArr['MoveSize1Count'] = $value->MoveSize1CountLocal + $value->MoveSize1CountLong;
                $dataArr['MoveSize2Count'] = $value->MoveSize2CountLocal + $value->MoveSize2CountLong;
                $dataArr['MoveSize3Count'] = $value->MoveSize3CountLocal + $value->MoveSize3CountLong;
                $dataArr['MoveSize4Count'] = $value->MoveSize4CountLocal + $value->MoveSize4CountLong;
                $dataArr['MoveSize5Count'] = $value->MoveSize5CountLocal + $value->MoveSize5CountLong;
                $dataArr['MoveSize6Count'] = $value->MoveSize6CountLocal + $value->MoveSize6CountLong;
            }
            $dataArr['LeadHour'] = $LeadHour;
            // $dataArr['Leads'] = $value->leadcount;
            // $dataArr['LocalLeads'] = $value->state_local;
            // $dataArr['LongLeads'] = $value->state_long;
            // $dataArr['LocalRevenue'] = $value->LocalRevenue;
            // $dataArr['LongRevenue'] = $value->LongRevenue;
            // $dataArr['AvarageRevenue'] = number_format(($value->AvarageRevenue/$value->leadcount),2);
            // $dataArr['LiveTransfer'] = $value->LiveTransferLead;
            // $dataArr['CallForward'] = $value->CallForwardLead;
            $dataArr['LiveTransfer'] = $call_array[$value->LeadHour]['live_transfer_lead'];
            $dataArr['CallForward'] = $call_array[$value->LeadHour]['call_forward_lead'];
            // $dataArr['VerifiedRatio'] = number_format(((100*$value->IsVerified)/$value->leadcount),2);

            $finalDataArr[] = $dataArr;
        }
         // echo "<pre>";print_r($finalDataArr);die;
         // exit();

        return $finalDataArr;
    }



}