<?php

namespace App\Http\Controllers\Seller;

use App\Models\Campaign\Campaign;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Master\MstZipcode;
use App\Models\Master\MstLeadSource;
use App\Models\Lead\LeadMoving;
use App\Models\Logic\LeadCampaignLogic;
use App\Helpers\CommonFunctions;
use App\Models\Seller\PPSeller;
use App\Models\Seller\PPPing;
use App\Models\Seller\PPLogs;
use App\Models\Seller\PPMatchedCompany;
use App\Models\Business\LSBusinessList;
use App\Models\Business\PMLSBusiness;
use App\Models\Business\LMBusinessList;
use App\Models\Business\PMLMBusiness;
use App\Models\Business\BusinessPartnerMapping;
use App\Models\DevDebug;
use App\Http\Controllers\Seller\SellerBiddingPercentageController;
use Exception;
use Auth;
use DB;
use Helper;

class PingSubmitNewController extends Controller
{
    public function index()
    {
        $error = $moveSize = $moveDate = $ipaddress = $browser = "";
        $status = "Rejected";
        $campaign_type = $pingId = $logsId = $exclusiveBuyerPayout = $dualBuyerPayout = $trioBuyerPayout = $sharedBuyerPayout = 0;
        $campaignIds = $activeCampAssocDataAr = $todayRouteData = $pingMatchedCompany = $dualCampaignIds = $trioCampaignIds = $sharedCampaignIds = array();
        $dateCurrent = date('Y-m-d');
        $dateTimeCurrent = date('Y-m-d H:i:s');
        try {
            $elements = json_decode(file_get_contents('php://input'), true);

            $logsId = PPLogs::create([
                "request"       => json_encode($elements),
                "type"          => 0,
                "request_time"  => date('Y-m-d H:i:s')
            ])->id;

            //api_key validation
            $apiKey = $elements['api_key'] ?? '';
            $sellerArray = $this->checkSellerApiKey(trim($apiKey));
            if ($sellerArray['seller_id'] == 0) {
                throw new Exception("Unauthorized");
            }
            $sellerId = $sellerArray['seller_id'];
            $sellerDetail = PPSeller::where('seller_id', $sellerId)->first();
            //Get Source Name Detail
            $originId = MstLeadSource::where('lead_source_name', $sellerDetail->source_name)->get('lead_source_id')->first();
            //move_size validation
            $moveSize = preg_replace("/\D/", "", trim($elements['move_size']));
            $moveSize = trim($elements['move_size']);
            //move_date
            $moveDate = LeadMoving::getMovedate($elements['move_date']);
            //from_zip
            $fromZip = $elements['from_zip'];
            $checkFromZip = MstZipcode::where("zipcode", $fromZip)->count();
            if ($checkFromZip == 0) {
                throw new Exception("Invalid From Zip");
            }
            //to_zip
            $toZip = $elements['to_zip'];
            $checkToZip = MstZipcode::where("zipcode", $toZip)->count();
            if ($checkToZip == 0) {
                throw new Exception("Invalid To Zip");
            }
            //is_verified
            $isVerified = $elements['is_verified'];
            $leadType = $elements['lead_type'];
            $verificationMethod = $elements['verification_method'];
            $ipaddress = $elements['ip'] ?? '';
            $browser = $elements['browser'] ?? '';
            $slotAvailable = $elements['slot_available'] ?? [];
            $dualSoldCompaniesId = $elements['dual_sold_companies_id'] ?? [];
            $trioSoldCompaniesId = $elements['trio_sold_companies_id'] ?? [];
            $premiumSoldCompaniesId = $elements['premium_sold_companies_id'] ?? [];

            // Merge all company IDs into one array
            $allSoldCompaniesId = array_merge(
                $premiumSoldCompaniesId,
                $dualSoldCompaniesId,
                $trioSoldCompaniesId
            );

            // Remove duplicates just in case
            $allSoldCompaniesId = array_unique($allSoldCompaniesId);

            $lsNonCompeteId = [];
            if(count($allSoldCompaniesId) > 0) {
                $lsBusinessId = LSBusinessList::whereIn('ls_business_id', $allSoldCompaniesId)->pluck('ls_business_list_id')->toArray();
                $lsNonCompeteId = PMLSBusiness::whereIn('ls_business_list_id', $lsBusinessId)->pluck('business_id')->toArray();
            }

            $lmNonCompeteId = [];
            if(count($allSoldCompaniesId) > 0) {
                $lmBusinessId = LMBusinessList::whereIn('lm_business_id', $allSoldCompaniesId)->pluck('lm_business_list_id')->toArray();
                $lmNonCompeteId = PMLMBusiness::whereIn('lm_business_list_id', $lmBusinessId)->pluck('business_id')->toArray();
            }

            $zipcodeData = MstZipcode::read($fromZip, $toZip);
            if (strcmp($fromZip, $toZip) && $toZip) {
                if ($fromZip == $zipcodeData[0]['zipcode']) {
                    $fromData = $zipcodeData[0];
                } elseif (isset($zipcodeData[1])) {
                    $fromData = $zipcodeData[1];
                }
                if ($toZip == $zipcodeData[0]['zipcode']) {
                    $toData = $zipcodeData[0];
                } elseif (isset($zipcodeData[1])) {
                    $toData = $zipcodeData[1];
                }
            } else {
                $fromData = $toData = $zipcodeData[0];
            }
            $fromCity = strtoupper($fromData['city']);
            $fromState = strtoupper($fromData['state']);
            $toCity = strtoupper($toData['city']);
            $toState = strtoupper($toData['state']);
            $fromAreaCode = $fromData['areacode'];
            $toAreaCode = $toData['areacode'];

            //print_r($ping); die;
            // Logic 1 Check CoverageType (Local, Long Distance)
            if (strcmp($fromState, $toState)) {
                $coverageType = "long";
            } else {
                $coverageType = "local";
            }

            //calculate distance
            $lead_distance = 0;
            if (!empty($fromData['lat']) && !empty($toData['lat']) && !empty($fromData['long']) && !empty($toData['long'])) {
                $lead_distance = LeadMoving::calculateDistance($fromData['lat'], $toData['lat'], $fromData['long'], $toData['long']);
            }

            // Logic 2 List all active campaign and businesses based on Logic 1
            $logicCampaign = new LeadCampaignLogic();
            $sqlForCampaign = "SELECT c.*,b.payment_type as business_payment_type,b.credit_available as business_credit_available,b.credit_reserved as business_credit_reserved,pb.business_id AS partner_business_id,pb.business_partner_id, cm.min_distance, cm.move_type FROM `campaign` c
                INNER JOIN `business` b ON b.business_id=c.business_id
                LEFT JOIN `business_partner_mapping` pb ON pb.business_id = b.business_id
                LEFT JOIN `campaign_moving` cm on cm.campaign_id = c.campaign_id";
            $sqlForCampaign .= " WHERE b.status='active' AND b.buyer_type_id <> 3 AND c.is_active='yes'
                AND c.lead_category_id=1 AND c.lead_type_id=1";
            if ($isVerified == "yes" || $isVerified == "1" || $isVerified == 1) {
                $sqlForCampaign .= " AND c.lead_status LIKE '%1%'";
                $isVerified = '1';
            } else {
                $sqlForCampaign .= " AND c.lead_status LIKE '%0%'";
                $isVerified = '0';
            }

            //Comment By BK on 05/06/2025 as per discussion with DS, MA, and client
            if(count($lsNonCompeteId) > 0) {
                $partnerBusinessId = BusinessPartnerMapping::whereIn('business_id', $lsNonCompeteId)->pluck('business_partner_id')->toArray();
                //print_r($partnerBusinessId);
                //print_r($lsNonCompeteId);
                $mergePartnerBusinessId = array_merge($partnerBusinessId, $lsNonCompeteId);
                //print_r($mergeLsParterBusiness);
                //dd('1');
                $mergeLSNonCompeteId = collect($mergePartnerBusinessId)->implode(',');
                //$sqlForCampaign .= " AND b.business_id NOT IN (". $mergeLSNonCompeteId .")";
            }

            if(count($lmNonCompeteId) > 0) {
                $partnerBusinessId = BusinessPartnerMapping::whereIn('business_id', $lmNonCompeteId)->pluck('business_partner_id')->toArray();
                //print_r($partnerBusinessId);
                //print_r($lmNonCompeteId);
                $mergePartnerBusinessId = array_merge($partnerBusinessId, $lmNonCompeteId);
                //print_r($mergeLsParterBusiness);
                //dd('1');
                $mergeLMNonCompeteId = collect($mergePartnerBusinessId)->implode(',');
                //$sqlForCampaign .= " AND b.business_id NOT IN (". $mergeLMNonCompeteId .")";
            }
            $matchCampaignData = DB::select($sqlForCampaign);

            $finalCampDataArr = $campUniqueIdArr = array();
            for ($vf = 0; $vf < @count($matchCampaignData); $vf++) {
                if (!in_array($matchCampaignData[$vf]->campaign_id, $campUniqueIdArr)) {
                    $finalCampDataArr[] = $matchCampaignData[$vf];
                    $campUniqueIdArr[] = $matchCampaignData[$vf]->campaign_id;
                }
            }

            foreach ($finalCampDataArr as $key => $val) {
                //echo $val->campaign_id."==".$coverageType."==".$val->move_type."==".$val->lead_category_id."<br>";
                if (isset($val->move_type) && $val->move_type != $coverageType && $val->lead_category_id != 2) {
                    //echo $val->campaign_id;
                    unset($finalCampDataArr[$key]); //Uncommnent here
                }
            }
            $finalCampDataArr = array_values($finalCampDataArr);

            for ($d = 0; $d < @count($finalCampDataArr); $d++) {
                $activeCampAssocDataAr[$finalCampDataArr[$d]->campaign_id] = $finalCampDataArr[$d];
                if (isset($finalCampDataArr[$d]->min_distance) && $lead_distance < $finalCampDataArr[$d]->min_distance) {
                    //echo $finalCampDataArr[$d]->campaign_id."<br>";
                    continue;
                }
                //echo "<pre>";print_r($activeCampAssocDataAr);die;
                if (!in_array($finalCampDataArr[$d]->campaign_id, $campaignIds)) {
                    //Start For Check Campaign Fund Logic
                    $payout = CommonFunctions::getCampaignPayout($finalCampDataArr[$d]->campaign_id, $coverageType, 0, 0);
                    if ($payout > 0) {
                        //echo "<pre>";print_r($finalCampDataArr[$d]);die;
                        if ($finalCampDataArr[$d]->business_payment_type == 'pre') { // Check if business payment_type is PrePayment
                            if ($finalCampDataArr[$d]->payment_type == 0) { // business level balance;
                                if (($finalCampDataArr[$d]->business_credit_available - $finalCampDataArr[$d]->business_credit_reserved) >= $payout && $payout > 0) {
                                    $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                                }
                            } else { // campaign level balance
                                if (($finalCampDataArr[$d]->credit_available - $finalCampDataArr[$d]->credit_reserved) >= $payout && $payout > 0) {
                                    $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                                }
                            }
                        } else { // If payment_type is PostPayment then no need to check credit availibility
                            $campaignIds[] = $finalCampDataArr[$d]->campaign_id;
                        }
                    }
                    //End For Check Campaign Fund Logic
                }
            }

            //Start For Check Campaign Source Logic
            $campaignIds = $logicCampaign->checkCampaignSource($campaignIds, 0, $originId->lead_source_id);

            //Start For Check Campaign Exclude Logic
            $campaignIds = $logicCampaign->checkCampaignExcludedDates($campaignIds, $activeCampAssocDataAr, $moveDate);
            //echo '<pre>'; print_r($campaignIds); die;
            //Start For Check Campaign Per Hour Lead Route Limit Logic
            $campaignIds = $logicCampaign->checkTiming($campaignIds, $dateCurrent, $dateTimeCurrent, $coverageType, $todayRouteData);

            //Start For Check Campaign Daily Limit,Budget Limit and Hourly Limit Logic
            $campaignIds = $logicCampaign->checkLimit($campaignIds, $activeCampAssocDataAr, $coverageType, 0, 0);

            $campaignIds = $logicCampaign->checkFromZip($campaignIds, $activeCampAssocDataAr, $fromZip, $toZip, $fromState, $toState, $fromAreaCode, $toAreaCode);

            //Added By BK on 12/06/2025
            $sharedCampaignIds = $dualCampaignIds = $trioCampaignIds = $campaignIds;

            $campaignIds = $logicCampaign->checkCampaignLogic($campaignIds, $coverageType, $dateCurrent, $dateTimeCurrent, 4, 0, 1, 0, 0, 1);
            //print_r($campaignIds); die;

            //for dual campaign fetch
            if(isset($slotAvailable[1]) && $slotAvailable[1] > 0) {
                //Added By BK on 05/06/2025 as per discussed with DS, MA
                $partnerCampaignId = Campaign::whereIn('business_id', $lsNonCompeteId)->where('lead_type_id', 1)->whereIn('campaign_type_id', [1, 2, 6, 8])->pluck('campaign_id')->toArray();
                if (count($partnerCampaignId) > 0) {
                    $dualCampaignIds = array_values(array_diff($dualCampaignIds, $partnerCampaignId));
                }

                $partnerCampaignId = Campaign::whereIn('business_id', $lmNonCompeteId)->where('lead_type_id', 1)->whereIn('campaign_type_id', [1, 2, 6, 8])->pluck('campaign_id')->toArray();
                if (count($partnerCampaignId) > 0) {
                    $dualCampaignIds = array_values(array_diff($dualCampaignIds, $partnerCampaignId));
                }

                $dualCampaignIds = $logicCampaign->checkCampaignLogic($dualCampaignIds, $coverageType, $dateCurrent, $dateTimeCurrent, $slotAvailable[1], 0, 1, 0, 0, 1, "", "", 0, 0, 6);
                //print_r($newCampaignIds); die;
                DevDebug::create([
                    'sr_no' => 98,
                    'result' => 'LinkUp ping-post dual campaign: ' . json_encode($dualCampaignIds),
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            } else {
                $dualCampaignIds = [];
            }

            //for trio campaign fetch
            if(isset($slotAvailable[2]) && $slotAvailable[2] > 0) {
                //Added By BK on 05/06/2025 as per discussed with DS, MA
                $partnerCampaignId = Campaign::whereIn('business_id', $lsNonCompeteId)->where('lead_type_id', 1)->whereIn('campaign_type_id', [1, 2, 6, 8])->pluck('campaign_id')->toArray();
                if (count($partnerCampaignId) > 0) {
                    $trioCampaignIds = array_values(array_diff($trioCampaignIds, $partnerCampaignId));
                }

                $partnerCampaignId = Campaign::whereIn('business_id', $lmNonCompeteId)->where('lead_type_id', 1)->whereIn('campaign_type_id', [1, 2, 6, 8])->pluck('campaign_id')->toArray();
                if (count($partnerCampaignId) > 0) {
                    $trioCampaignIds = array_values(array_diff($trioCampaignIds, $partnerCampaignId));
                }

                $trioCampaignIds = $logicCampaign->checkCampaignLogic($trioCampaignIds, $coverageType, $dateCurrent, $dateTimeCurrent, $slotAvailable[2], 0, 1, 0, 0, 1, "", "", 0, 0, 8);
                //print_r($newCampaignIds); die;
                DevDebug::create([
                    'sr_no' => 98,
                    'result' => 'LinkUp ping-post trio campaign: ' . json_encode($trioCampaignIds),
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            } else {
                $trioCampaignIds = [];
            }

            //for premium campaign fetch
            if(isset($slotAvailable[3]) && $slotAvailable[3] > 0) {
                //Added By BK on 05/06/2025 as per discussed with DS, MA
                $partnerCampaignId = Campaign::whereIn('business_id', $lsNonCompeteId)->where('lead_type_id', 1)->whereIn('campaign_type_id', [1, 2, 6, 8])->pluck('campaign_id')->toArray();
                if (count($partnerCampaignId) > 0) {
                    $sharedCampaignIds = array_values(array_diff($sharedCampaignIds, $partnerCampaignId));
                }

                $partnerCampaignId = Campaign::whereIn('business_id', $lmNonCompeteId)->where('lead_type_id', 1)->whereIn('campaign_type_id', [1, 2, 6, 8])->pluck('campaign_id')->toArray();
                if (count($partnerCampaignId) > 0) {
                    $sharedCampaignIds = array_values(array_diff($sharedCampaignIds, $partnerCampaignId));
                }

                $sharedCampaignIds = $logicCampaign->checkCampaignLogic($sharedCampaignIds, $coverageType, $dateCurrent, $dateTimeCurrent, $slotAvailable[3], 0, 1, 0, 0, 1, "", "", 0, 0, 1);
                //print_r($newCampaignIds); die;
                DevDebug::create([
                    'sr_no' => 98,
                    'result' => 'LinkUp ping-post premium campaign: ' . json_encode($sharedCampaignIds),
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            } else {
                $sharedCampaignIds = [];
            }

            DevDebug::create([
                'sr_no' => 98,
                'result' => json_encode($campaignIds),
                'created_at' => date('Y-m-d H:i:s')
            ]);

            //Added By BK on 13/02/2024
            $exclusivePayout = 0;
            if (@count($campaignIds) > 0) { foreach($campaignIds as $campaignId => $score) {
                $newPayout = CommonFunctions::getCampaignPayout($campaignId, $coverageType, 0, 0);
                $pingMatchedCompany[] = [
                    'pm_sold_company_id' => $campaignId,
                    'payout' => $newPayout,
                    'lead_type' => 'exclusive',
                    'is_win' => 'no'
                ];
                $exclusivePayout += $newPayout;
            } }

            //echo $leadType; die;
            $percentageObj = new SellerBiddingPercentageController();
            $percentage = $percentageObj->sellerBiddingPercentage($coverageType);
            $status = "Accepted";
            $exclusiveBuyerPayout = ($exclusivePayout * ($percentage + $sellerDetail->bidding_percentage)) / 100;

            //Added By BK on 12/06/2025
            $dualPayout = 0;
            if(isset($slotAvailable[1]) && $slotAvailable[1] > 0) {
                if (@count($dualCampaignIds) > 0) { foreach($dualCampaignIds as $campaignId => $score) {
                    $newPayout = CommonFunctions::getCampaignPayout($campaignId, $coverageType, 0, 0);
                    $pingMatchedCompany[] = [
                        'pm_sold_company_id' => $campaignId,
                        'payout' => $newPayout,
                        'lead_type' => 'dual',
                        'is_win' => 'no'
                    ];
                    $dualPayout += $newPayout;
                } }
            }
            $dualBuyerPayout = ($dualPayout * ($percentage + $sellerDetail->bidding_percentage)) / 100;

            //Added By BK on 12/06/2025
            $trioPayout = 0;
            if(isset($slotAvailable[2]) && $slotAvailable[2] > 0) {
                if (@count($trioCampaignIds) > 0) { foreach($trioCampaignIds as $campaignId => $score) {
                    $newPayout = CommonFunctions::getCampaignPayout($campaignId, $coverageType, 0, 0);
                    $pingMatchedCompany[] = [
                        'pm_sold_company_id' => $campaignId,
                        'payout' => $newPayout,
                        'lead_type' => 'trio',
                        'is_win' => 'no'
                    ];
                    $trioPayout += $newPayout;
                } }
            }
            $trioBuyerPayout = ($trioPayout * ($percentage + $sellerDetail->bidding_percentage)) / 100;

            //Added By BK on 13/02/2024
            $sharedPayout = 0;
            if(isset($slotAvailable[3]) && $slotAvailable[3] > 0) {
                if (@count($sharedCampaignIds) > 0) { foreach($sharedCampaignIds as $campaignId => $score) {
                    $newPayout = CommonFunctions::getCampaignPayout($campaignId, $coverageType, 0, 0);
                    $pingMatchedCompany[] = [
                        'pm_sold_company_id' => $campaignId,
                        'payout' => $newPayout,
                        'lead_type' => 'shared',
                        'is_win' => 'no'
                    ];
                    $sharedPayout += $newPayout;
                } }
            }
            $sharedBuyerPayout = ($sharedPayout * ($percentage + $sellerDetail->bidding_percentage)) / 100;

            //Check Lead Sold Count For MYM Selller
            //Logic for Check Dynamic Local Lead Limit for Seller Added By BK on 13/02/2024
            if ($coverageType=="local" && $sellerDetail->daily_local_limit > 0) {
                $originId = MstLeadSource::where('lead_source_name', $sellerDetail->source_name)->get('lead_source_id')->first();
                /*$totlaLead= Leads::with(['moveInfo' => function($query) {
                    $query->where('from_state', '=', 'to_state');
                }])->where('origin_id', $originId->id)->whereBetween('date_received', [$dateCurrent, $dateCurrent])->count('*');*/
                $totlaLead = DB::select("SELECT COUNT(l.lead_id) AS total_lead FROM `lead` l INNER JOIN `lead_moving` lm ON l.lead_id = lm.lead_id WHERE l.lead_source_id = '".$originId->lead_source_id."' AND lm.from_state = lm.to_state AND l.lead_generated_at BETWEEN '".$dateCurrent."' AND '".$dateCurrent."'");
                if ($totlaLead[0]->total_lead >= $sellerDetail->daily_local_limit) {
                    $exclusiveBuyerPayout = $sharedBuyerPayout = $dualBuyerPayout = $trioBuyerPayout = 0;
                }
            }

            //when Daily Local Limit Status is Inactive leadPayout=0 Added By BK on 13/02/2024
            if ($coverageType=="local" && $sellerDetail->daily_local_limit_status == 0) {
                $exclusiveBuyerPayout = $sharedBuyerPayout = $dualBuyerPayout = $trioBuyerPayout = 0;
            }

            //Logic for Check Dynamic Local Lead Limit for Seller Added By BK on 13/02/2024
            if ($coverageType=="long" && $sellerDetail->daily_long_limit > 0) {
                $originId = MstLeadSource::where('lead_source_name', $sellerDetail->source_name)->get('lead_source_id')->first();
                $totlaLead = DB::select("SELECT COUNT(l.lead_id) AS total_lead FROM `lead` l INNER JOIN `lead_moving` lm ON l.lead_id = lm.lead_id WHERE l.lead_source_id = '".$originId->lead_source_id."' AND lm.from_state <> lm.to_state AND l.lead_generated_at BETWEEN '".$dateCurrent."' AND '".$dateCurrent."'");
                if ($totlaLead[0]->total_lead >= $sellerDetail->daily_long_limit) {
                    $exclusiveBuyerPayout = $sharedBuyerPayout = $dualBuyerPayout = $trioBuyerPayout = 0;
                }
            }

            //when Daily Long Limit Status is Inactive leadPayout=0 Added By BK on 31/01/2023
            if ($coverageType=="long" && $sellerDetail->daily_long_limit_status == 0) {
                $exclusiveBuyerPayout = $sharedBuyerPayout = $dualBuyerPayout = $trioBuyerPayout = 0;
            }

            //echo $exclusiveBuyerPayout.'--'.$sharedBuyerPayout; die;
            $pingId = PPPing::create([
                "api_key"               => $apiKey,
                "seller_id"             => $sellerId,
                "from_zip"              => $fromZip,
                "to_zip"                => $toZip,
                "move_size"             => $moveSize,
                "move_date"             => date('Y-m-d', strtotime($moveDate)),
                "exclusive_buyer_amount"=> $exclusiveBuyerPayout,
                "exclusive_payout"      => $exclusivePayout,
                "dual_buyer_amount"     => $dualBuyerPayout,
                "dual_payout"           => $dualPayout,
                "trio_buyer_amount"     => $trioBuyerPayout,
                "trio_payout"           => $trioPayout,
                "shared_buyer_amount"   => $sharedBuyerPayout,
                "shared_payout"         => $sharedPayout,
                "result"                => $status,
                "is_verified"           => $isVerified,
                "verified_method"       => $verificationMethod,
                "lead_type"             => ($leadType == '1') ? 'exclusive' : 'shared',
                "available_slots"       => 0,
                "created_at"            => date('Y-m-d H:i:s'),
            ])->id;

            //echo $exclusiveBuyerPayout; die;
            //print_r($campaignSortBundle); die;
            if (count($pingMatchedCompany) > 0) { for($i=0; $i < count($pingMatchedCompany); $i++) {
                $pingMatchedCompany[$i]['ping_id'] = $pingId;
                $pingMatchedCompany[$i]['created_at'] = date('Y-m-d H:i:s');
                //print_r($pingMatchedCompany); die;
                PPMatchedCompany::create($pingMatchedCompany[$i]);
            } }

        } catch (Exception $e) {
            $error = $e->getMessage();
        }

        $responseArray = [
            'status'                    => $status,
            'ping_id'                   => $pingId,
            //'buying_slot_payout'      => array('4' => $exclusiveBuyerPayout, count($sharedCampaignIds) => $sharedBuyerPayout, count($dualCampaignIds) => $dualBuyerPayout, count($trioCampaignIds) => $trioBuyerPayout),
            'buying_slot_payout'        => array("exclusive" => ['4' => $exclusiveBuyerPayout], "dual" => [count($dualCampaignIds) => $dualBuyerPayout], "trio" => [count($trioCampaignIds) => $trioBuyerPayout], "shared" => [count($sharedCampaignIds) => $sharedBuyerPayout]),
            'error'                     => $error
        ];

        PPLogs::where('logs_id', $logsId)->update([
            "type_id"                   => $pingId,
            "response"                  => json_encode($responseArray),
            "response_time"             => date('Y-m-d H:i:s'),
            "ip"                        => $ipaddress,
            "browser"                   => $browser
        ]);
        return response()->json($responseArray);
    }

    /* Start checkSellerApiKey function will return integer */
    public function checkSellerApiKey ($apiKey=''){
        $seller['seller_id'] = 0;
        if(!empty($apiKey)){
                $sellerDetail = PPSeller::where('api_key', $apiKey)->where('is_active', 1)->first();
            if(!empty($sellerDetail)){
                $seller['seller_id'] = $sellerDetail->seller_id;
            }
        }
        return $seller;
    }
    /* End checkAccountToaken function will return integer */
}
