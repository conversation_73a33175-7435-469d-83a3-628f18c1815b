<?php

namespace App\Models\Seller;

use Illuminate\Database\Eloquent\Model;

class PPPing extends Model
{
    /**
     * @var string
     */
    protected $table = 'pp_ping';

    /**
     * @var bool
     */
    public $timestamps = false;

    /**
     * @var array
     */
    protected $fillable = [
        'ping_id',
        'api_key',
        'seller_id',
        'from_zip',
        'to_zip',
        'move_size',
        'move_date',
        'exclusive_buyer_amount',
        'exclusive_payout',
        'dual_buyer_amount',
        'dual_payout',
        'trio_buyer_amount',
        'trio_payout',
        'shared_buyer_amount',
        'shared_payout',
        'result',
        'is_verified',
        'verified_method',
        'lead_type',
        'available_slots',
        'created_at'
    ];
}
