<?php

namespace App\Http\Controllers\Plivo;

use App\Http\Controllers\Controller;
use DateTime;
use DB;
use Exception;
use Illuminate\Http\Request;
use App\Helpers\Helper;
use App\Helpers\CommonFunctions;
use App\Models\Campaign\Campaign;
use App\Models\Logic\LeadCampaignLogic;
use App\Models\Outbound\LeadCall;
use App\Models\Outbound\LeadCallNumber;
use App\Models\Outbound\LeadCallNumberAreacode;
use App\Models\Lead\Lead;
use App\Models\Lead\LeadRequestLog;
use App\Models\Lead\LeadMoving;
use App\Models\Lead\LeadRouting;
use App\Models\Lead\LeadTransferCall;
use App\Models\Master\MstCampaignType;
use App\Models\Master\MstCallConfig;
use App\Models\Master\MstLeadSource;
use App\Models\Inbound\SourceIncomingNumber;
use App\Models\Inbound\LeadIbCall;
use App\Http\Controllers\LeadCallBuffer\CallsbufferCronController;
use App\Http\Controllers\Plivo\PlivoController;
use App\Models\Inbound\LeadIbCallLog;
use App\Models\Lead\LeadActivity;
use App\Models\Master\MstTimeZone;
use App\Models\Business\Business;
use App\Models\DevDebug;

class PlivoCallInboundAutomated extends Controller {

    private $customerNumber = "";
    private $custDialNumber = "";
    private $defaultimezone;

    function __construct() {
        $this->middleware('permission:ai-phone-leadlist', ['only' => ['phoneautomatedleadlist']]);
        $timezoneobj = new MstTimeZone();
        $this->defaultimezone = $timezoneobj->getdefaulttimezone();
    }

    public function automatedinboundcall() {
        $parameters = $_REQUEST;
        //Added By HJ On 03-08-2023 For Comio Number Start
        if (isset($parameters['SIP-H-To'])) {
            $parameters['To'] = substr($parameters['SIP-H-To'], 6, 11);
            //$parameters['From'] = trim($parameters['CallerName'], "+");
            $parameters['From'] = substr($parameters['From'], 5, 11);
        }
        //Added By HJ On 03-08-2023 For Comio Number End
        $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '1', '1 SnapIt Log automatedinboundcall 1==' . json_encode($parameters));
        $this->customerNumber = $parameters['From'];
        $this->custDialNumber = $parameters['To'];
        try {
            $response = $this->addDial($parameters);
            header("Content-type: text/xml");
            echo $response;
            exit;
        } catch (Exception $ex) {
            $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '2', '2 SnapIt Log callForward Exception : ' . $ex->getMessage());
        }
    }

    public function addDial($parameters) {
        $this->customerNumber = $parameters['From'];
        $this->custDialNumber = $parameters['To'];
        $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '3', '3 SnapIt Log addDial - ' . json_encode($parameters));
        try {
            $getNumbers = LeadCallNumber::where('status', 'active')->where('phone', $this->custDialNumber)->get()->toArray();
            if (count($getNumbers) > 0) {
                $objCall = new PlivoController();
                $getConfigData = MstCallConfig::where("status", "active")->get()->toArray();
                $tokAvailabel = $tokInbound = $inboundcalltransfer = $enableVoicePrompt = $movevoiceprompt = $inboundautotransfer = $isinboundtodialup = $vminboundtodialup = $qtminboundtodialup = 0;
                if (count($getConfigData) > 0) {
                    for ($c = 0; $c < count($getConfigData); $c++) {
                        if (strtolower($getConfigData[$c]['call_config']) == "calitoutbound" && strtolower($getConfigData[$c]['status']) == "active") {
                            $tokAvailabel = 1;
                        }
                        if (strtolower($getConfigData[$c]['call_config']) == "callitinbound" && strtolower($getConfigData[$c]['status']) == "active") {
                            $tokInbound = 1;
                        }
                        if (strtolower($getConfigData[$c]['call_config']) == "inboundcalltransfer" && strtolower($getConfigData[$c]['status']) == "active") {
                            $inboundcalltransfer = 1;
                        }
                        if (strtolower($getConfigData[$c]['call_config']) == "movevoiceprompt" && strtolower($getConfigData[$c]['status']) == "active") {
                            $movevoiceprompt = 1;
                        }
                        if (strtolower($getConfigData[$c]['call_config']) == "inboundautotransfer" && strtolower($getConfigData[$c]['status']) == "active") {
                            $inboundautotransfer = 1;
                        }
                        if (strtolower($getConfigData[$c]['call_config']) == "isinboundtodialup" && strtolower($getConfigData[$c]['status']) == "active") {
                            $isinboundtodialup = 1;
                        }
                        if (strtolower($getConfigData[$c]['call_config']) == "vminboundtodialup" && strtolower($getConfigData[$c]['status']) == "active") {
                            $vminboundtodialup = 1;
                        }
                        if (strtolower($getConfigData[$c]['call_config']) == "qtminboundtodialup" && strtolower($getConfigData[$c]['status']) == "active") {
                            $qtminboundtodialup = 1;
                        }
                    }
                }
                $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '4', "4 SnapIt Log addDial=" . json_encode($getConfigData, true) . "==config Outbound=" . $tokAvailabel . "==Inbound=" . $tokInbound . "==inboundcalltransfer=" . $inboundcalltransfer . "==movevoiceprompt=" . $movevoiceprompt);
                $checkObNumber = LeadCallNumberAreacode::where('status', 'active')->where('phone', $this->custDialNumber)->get()->toArray();
                if ($tokInbound == 1) {
                    $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
                    $sourceNameArr = array();
                    $getSourceData = MstLeadSource::get()->toArray();
                    for ($s = 0; $s < count($getSourceData); $s++) {
                        $sourceNameArr[$getSourceData[$s]['lead_source_id']] = $getSourceData[$s]['lead_source_name'];
                    }
                    $phone = $getNumbers[0]['phone'];
                    $origin = $getNumbers[0]['lead_source_id'];
                    $is_current = $getNumbers[0]['is_current'];
                    $status = $getNumbers[0]['status'];
                    $category = $getNumbers[0]['lead_category_id'];
                    $moveType = $getNumbers[0]['move_type'];
                    if (isset($sourceNameArr[$origin])) {
                        $sourceName = strtoupper(trim($sourceNameArr[$origin]));
                        $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '5', "5 SnapIt Log addDial =" . json_encode($getSourceData, true) . "==Phone=" . $phone . "==Source=" . $sourceName . "==category=" . $category);

                        // For SMS Source Call for VM, QTM and ISM
                        if($getNumbers[0]['phone_source'] == "SMS" && $tokInbound == 1) {
                            $serviceName = "Moving";
                            $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '13', '13 SnapIt Log addDial IS inboundcalltransfer - ' . $inboundcalltransfer . ',==service name==' . $serviceName);
                            //if($digitValid == "1"){
                            $parameters['Digits_2'] = 2;
                            if ($tokInbound == 0) {
                                $parameters['Digits_2'] = 1;
                            }
                            $parameters['lead_category'] = 1;
                            $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '14', '14 SnapIt Log addDial IS Direct connect to rep - ' . json_encode($parameters));
                            $response = $objCall->digitselectinb($parameters);
                            header("Content-type: text/xml");
                            echo $response;
                            exit;    
                        }
                        

                        if (strpos($sourceName, 'VM') !== false || strpos($sourceName, 'QTM') !== false) {
                            if ($tokInbound == 0) {
                                $voice = "vm_jr_hl_ct_tok_off.mp3";
                                if (strpos($sourceName, 'QTM') !== false) {
                                    $voice = "qtm_jr_hl_ct_tok_off.mp3";
                                }
                            } else {
                                $voice = "vm_jr_hl_ct_tok_on.mp3";
                                if (strpos($sourceName, 'QTM') !== false) {
                                    $voice = "qtm_jr_hl_ct_tok_on.mp3";
                                }
                            }
                            $mp3Name = "vmvoice_28_03_24.mp3";
                            if (strpos($sourceName, 'QTM') !== false) {
                                $mp3Name = "qtmvoice.mp3";
                            }
                            if ($category == 9) {
                                if ($inboundcalltransfer > 0) {
                                    if ($tokInbound == 0) {
                                        $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '4', '4 SnapIt Log addDial - ' . $matrixBaseURL_Outbound . '/mp3/' . $voice);
                                        return '<Response>
                                        <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselectcommon" method="GET" timeout="30" numDigits="1" validDigits="12340">
                                            <Play loop="1">' . $matrixBaseURL_Outbound . '/mp3/' . $voice . '</Play>
                                        </GetDigits>
                                        </Response>';
                                    } else {
                                        $parameters['Digits_2'] = 2;
                                        $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '5', '5 SnapIt Log addDial - ' . json_encode($parameters));
                                        $response = $objCall->digitselectinb($parameters);
                                        header("Content-type: text/xml");
                                        echo $response;
                                        exit;
                                    }
                                } else {
                                    $digitValid = "123409";
                                    if ($tokInbound == 0) {
                                        $digitValid = "12340";
                                        $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '6', '5 SnapIt Log addDial = ' . $matrixBaseURL_Outbound . '/mp3/' . $voice);
                                    } else {
                                        $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '7', '7 SnapIt Log addDial = ' . $matrixBaseURL_Outbound . '/mp3/' . $voice);
                                    }
                                    return '<Response>
                                <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselectcommon" method="GET" timeout="30" numDigits="1" validDigits="' . $digitValid . '">
                                    <Play loop="1">' . $matrixBaseURL_Outbound . '/mp3/' . $voice . '</Play>
                                </GetDigits>
                                </Response>';
                                }
                            } else if ($category == 2) {
                                $digitValid = "10*";
                                if (strpos($sourceName, 'QTM') !== false) {
                                    $digitValid = "19";
                                }
                                $serviceName = "Junk";
                                $callCategory = 2;
                            } else if ($category == 3) {
                                $digitValid = "10*";
                                if (strpos($sourceName, 'QTM') !== false) {
                                    $digitValid = "19";
                                }
                                $serviceName = "Heavy Lift";
                                $callCategory = 3;
                            } else if ($category == 4) {
                                $digitValid = "10*";
                                if (strpos($sourceName, 'QTM') !== false) {
                                    $digitValid = "19";
                                }
                                $serviceName = "Car Transportation";
                                $callCategory = 4;
                            } else {
                                $digitValid = "1290";
                                if (strpos($sourceName, 'QTM') !== false) {
                                    $digitValid = "129";
                                }
                                $serviceName = "Moving";
                                $callCategory = 1;
                                if ($movevoiceprompt > 0) {
                                    $enableVoicePrompt = 1;
                                }
                            }
                            $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '8', '8 SnapIt Log addDial =' . $serviceName . ' Call MP3 - ' . $mp3Name . "===enableVoicePrompt==" . $enableVoicePrompt);
                            if ($inboundcalltransfer > 0 || $enableVoicePrompt > 0) {
                                if ($category == 1 && ($tokInbound == 0 || $enableVoicePrompt > 0)) {
                                    $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '9', '9 SnapIt Log addDial - ' . $inboundcalltransfer . ',==service name==' . $serviceName . ',=tok config==' . $tokInbound."=inboundautotransfer==".$inboundautotransfer."==checkObNumber data=".json_encode($checkObNumber));
                                    if($inboundautotransfer == 1 && count($checkObNumber) > 0){
                                        $parameters['Digits_2'] = 2;
                                        $response = $objCall->digitselectinb($parameters);
                                        header("Content-type: text/xml");
                                        echo $response;
                                        exit;
                                    }else{
                                        if (strpos($sourceName, 'QTM') !== false && $qtminboundtodialup == 0) {
                                            if($mp3Name == "vmvoice_28_03_24.mp3") {
                                                return '<Response>
                                                    <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselect" method="GET" timeout="30" numDigits="1" validDigits="' . $digitValid . '">
                                                        <Play>' . $matrixBaseURL_Outbound . '/mp3/additional_1.mp3</Play>
                                                        <Play loop="1">' . $matrixBaseURL_Outbound . '/mp3/' . $mp3Name . '</Play>
                                                    </GetDigits>
                                                </Response>';
                                            }
                                            return '<Response>
                                                <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselect" method="GET" timeout="30" numDigits="1" validDigits="' . $digitValid . '">
                                                    <Play loop="1">' . $matrixBaseURL_Outbound . '/mp3/' . $mp3Name . '</Play>
                                                </GetDigits>
                                            </Response>';
                                        } else if(strpos($sourceName, 'VM') !== false && $vminboundtodialup == 0) {
                                            if($mp3Name == "vmvoice_28_03_24.mp3") {
                                                return '<Response>
                                                    <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselect" method="GET" timeout="30" numDigits="1" validDigits="' . $digitValid . '">
                                                        <Play>' . $matrixBaseURL_Outbound . '/mp3/additional_1.mp3</Play>
                                                        <Play loop="1">' . $matrixBaseURL_Outbound . '/mp3/' . $mp3Name . '</Play>
                                                    </GetDigits>
                                                </Response>';
                                            }
                                            return '<Response>
                                                <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselect" method="GET" timeout="30" numDigits="1" validDigits="' . $digitValid . '">
                                                    <Play loop="1">' . $matrixBaseURL_Outbound . '/mp3/' . $mp3Name . '</Play>
                                                </GetDigits>
                                            </Response>';
                                        } else {
                                            $parameters['Digits_2'] = 2;
                                            $response = $objCall->digitselectinb($parameters);
                                            header("Content-type: text/xml");
                                            echo $response;
                                            exit;
                                        }
                                        
                                    }
                                } else {
                                    $parameters['Digits_2'] = 2;
                                    $parameters['lead_category'] = $callCategory;
                                    $this->createInboundTableLogs($parameters['From'], $this->custDialNumber, '10', '10 SnapIt Log addDial - ' . json_encode($parameters));
                                    $response = $objCall->digitselectinb($parameters);
                                    header("Content-type: text/xml");
                                    echo $response;
                                    exit;
                                }
                            } else {
                                $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '11', '11 SnapIt Log addDial - ' . $inboundcalltransfer . ',==service name==' . $serviceName);
                                //if($digitValid == "1"){
                                $parameters['Digits_2'] = 2;
                                if ($tokInbound == 0) {
                                    $parameters['Digits_2'] = 1;
                                }
                                $parameters['lead_category'] = $callCategory;
                                $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '12', '12 SnapIt Log addDial - ' . json_encode($parameters));
                                $response = $objCall->digitselectinb($parameters);
                                header("Content-type: text/xml");
                                echo $response;
                                exit;
                            }
                        } else if (strpos($sourceName, 'IS') !== false) {
                            $serviceName = "Moving";
                            $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '13', '13 SnapIt Log addDial IS inboundcalltransfer - ' . $inboundcalltransfer . ',==service name==' . $serviceName);
                            if ($inboundcalltransfer > 0) {
                                if ($tokInbound == 0 || $isinboundtodialup == 0) {
                                     return '<Response>
                                        <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselect" method="GET" timeout="30" numDigits="120*9" validDigits="120*9">
                                            <Play>' . $matrixBaseURL_Outbound . '/mp3/additional_1.mp3</Play>
                                            <Play loop="10">' . $matrixBaseURL_Outbound . '/mp3/callforward.mp3</Play>
                                        </GetDigits>
                                    </Response>';
                                } else {
                                    $parameters['Digits_2'] = 2;
                                    if ($tokInbound == 0) {
                                        $parameters['Digits_2'] = 1;
                                    }
                                    $parameters['lead_category'] = 1;
                                    $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '12', '12 SnapIt Log addDial - ' . json_encode($parameters));
                                    $response = $objCall->digitselectinb($parameters);
                                    header("Content-type: text/xml");
                                    echo $response;
                                    exit;
                                }
                            }
                            //if($digitValid == "1"){
                            /*$parameters['Digits_2'] = 2;
                            if ($tokInbound == 0) {
                                $parameters['Digits_2'] = 1;
                            }
                            $parameters['lead_category'] = 1;
                            $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '14', '14 SnapIt Log addDial IS Direct connect to rep - ' . json_encode($parameters));
                            $response = $objCall->digitselectinb($parameters);
                            header("Content-type: text/xml");
                            echo $response;
                            exit;*/
                           
                        } else if (strpos($sourceName, 'REXDIRECT') !== false) {
                            $serviceName = "Moving";
                            $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '13', '13 SnapIt Log addDial REXDIRECT inboundcalltransfer - ' . $inboundcalltransfer . ',==service name==' . $serviceName);
                            $parameters['Digits_2'] = 2;
                            if($moveType == 'long') {
                                $parameters['Digits_2'] = 1;
                            }
                            if ($tokInbound == 0) {
                                $parameters['Digits_2'] = 1;
                                if($moveType == 'local') {
                                    $parameters['Digits_2'] = 2;
                                }
                            }
                            $parameters['lead_category'] = 1;
                            $parameters['Digits'] = $parameters['Digits_2'];
                            $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
                            $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '14', '14 SnapIt Check Config - ' . json_encode($checkObNumber)."==".$inboundautotransfer);
                            $response = $this->digitselectForRexdirect($parameters);
                            header("Content-type: text/xml");
                            echo $response;
                            exit;
                        }
                    }
                } else {
                    $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
                    $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '14', '14 SnapIt Check Config - ' . json_encode($checkObNumber)."==".$inboundautotransfer);
                    if($inboundautotransfer == 1 && count($checkObNumber) > 0){
                        $parameters['Digits_2'] = 2;
                        $response = $objCall->digitselectinb($parameters);
                        header("Content-type: text/xml");
                        echo $response;
                        exit;
                    }else{
                        $response = '<Response>
                            <GetDigits action="' . $matrixBaseURL_Outbound . '/digitselect" method="GET" timeout="30" numDigits="1" validDigits="120*9">
                                <Play>' . $matrixBaseURL_Outbound . '/mp3/additional_1.mp3</Play>
                                <Play loop="10">' . $matrixBaseURL_Outbound . '/mp3/callforward.mp3</Play>
                            </GetDigits>
                        </Response>';
                        header("Content-type: text/xml");
                        $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '15', '15 SnapIt Log addDial inbound config off - ' . $response);
                        return $response;
                    }

                }
            } else {
                $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '4', '4 SnapIt Log addDial number not found - ' . json_encode($getNumbers));
                return '<Response>
                    <Speak>Thank you</Speak>
                  </Response>';
            }
        } catch (Exception $e) {
            $this->createInboundTableLogs($this->customerNumber, $this->custDialNumber, '4', "4 SnapIt Log automatedinboundcall catch=" . $e->getMessage());
        }
    }

    public function digitselect() {
        // dd("asd");
        $parameters = $_REQUEST;
        //Added By HJ On 03-08-2023 For Comio Number Start
        if (isset($parameters['SIP-H-To'])) {
            $parameters['To'] = substr($parameters['SIP-H-To'], 6, 11);
            //$parameters['From'] = trim($parameters['CallerName'], "+");
            $parameters['From'] = substr($parameters['From'], 5, 11);
        }
        //Added By HJ On 03-08-2023 For Comio Number End
        $this->createInboundTableLogs($parameters['From'], $parameters['To'], '1', '1 SnapIt Log digitselect ==' . json_encode($parameters));
        $digit_pressed = ((int) ($parameters['Digits']));
        $datetime = $parameters['SessionStart'];
        $callUuid = $parameters['CallUUID'];
        // $digit_pressed = ((int) ("1"));
        // $datetime = "2023-04-21 04:50:49.948402";
        $digit_pressed_ex = trim($parameters['Digits']);
        try {
            $this->customerNumber = $fromNumber = $parameters['From'];
            $this->custDialNumber = $toNumber = $parameters['To'];
            //Added by BK on 10-01-2023 For pass callerName parameter in xml as per discuss with MA
            $callerName = '';
            if ($toNumber == 18334080606 || $toNumber == "18334080606") {
                $callerName = 'callerName="'.$toNumber.'"';
            }
            //Added By HJ On 28-07-2023 For Get Particular Number's Source When Create New Lead Based On Dial Number Start
            $objCall = new PlivoController();
            $originData = $objCall->getOriginData($parameters['To']);
            $originName = $originData['origin'];
            $lead_category = 1;
            $originId = 130;
            $sourceIdArr = array();
            $sourceName = "VM";
            if (strpos($originName, 'QTM') !== false) {
                $sourceName = "QTM";
            } else if (strpos($originName, 'IS') !== false) {
                $sourceName = "IS";
            }
            //Added By HJ On 28-07-2023 For Get Particular Number's Source When Create New Lead Based On Dial Number End
            $getVMSourceIds = MstLeadSource::where('lead_source_name', 'like', $sourceName . '%')->get(['lead_source_id', 'lead_source_name'])->toArray();
            for ($s = 0; $s < count($getVMSourceIds); $s++) {
                if (!in_array($getVMSourceIds[$s]['lead_source_id'], $sourceIdArr)) {
                    $sourceIdArr[] = $getVMSourceIds[$s]['lead_source_id'];
                }
                if (strtoupper($getVMSourceIds[$s]['lead_source_name']) == $sourceName . "P") {
                    $originId = $getVMSourceIds[$s]['lead_source_id'];
                }
            }
            $getConfigData = MstCallConfig::where("status", "active")->get()->toArray();
            $tokInbound = 0;
            if (count($getConfigData) > 0) {
                for ($c = 0; $c < count($getConfigData); $c++) {
                    if (strtolower($getConfigData[$c]['call_config']) == "callitinbound" && strtolower($getConfigData[$c]['status']) == "active") {
                        $tokInbound = 1;
                    }
                }
            }
            if ((!isset($digit_pressed) ) || (!isset($callUuid) )) {
                throw new Exception("Digit Not Founds");
            }
            $this->createInboundTableLogs($parameters['From'], $parameters['To'], '2', '2 SnapIt Log digitselect ==' . $digit_pressed_ex . "==Data==" . json_encode($parameters) . "==" . $originName . "==" . $sourceName . "==" . json_encode($sourceIdArr));
            $getCampData = $campaignsId = [];
            $coverageType = "";
            if ($digit_pressed_ex == "*" || $digit_pressed_ex == "9") {
                if(($digit_pressed_ex == "9" || $digit_pressed_ex == 9 ) && $sourceName == "VM") {
                    $campaignsId = []; //Commented BY HJ On 14-06-2023 For Insert Lead In Buffer when Digit Press 2 As Per Discuss with MV and Sir.
                    $coverageType = "local";
                    $this->createInboundTableLogs($parameters['From'], $parameters['To'], '3', '3 SnapIt Log digitselect parameter checking digit' . $digit_pressed . "==inbound==" . $tokInbound . "==Data==" . json_encode($campaignsId));
                    if (($campaignsId == null || count($campaignsId) == 0) && $tokInbound == 1) {
                        $response = $objCall->digitselectinb($parameters);
                        header("Content-type: text/xml");
                        echo $response;
                        exit;
                    }
                }
                $this->createInboundTableLogs($parameters['From'], $parameters['To'], '3', '3 SnapIt Log digitselect parameter checking digit_pressed' . $digit_pressed_ex . "==Data==" . json_encode($parameters));
                $response = $this->addDial($parameters);
                header("Content-type: text/xml");
                echo $response;
                exit;
            } else if ($digit_pressed == "1" || $digit_pressed == 1) {
                $campaignsId = $this->matchedcallcampaigns("long", $datetime, $this->customerNumber, $this->custDialNumber, $callUuid, $lead_category);
                $coverageType = "long";
                $this->createInboundTableLogs($parameters['From'], $parameters['To'], '3', '3 SnapIt Log digitselect parameter checking inbound' . $tokInbound . "==Data==" . json_encode($campaignsId));
                if (($campaignsId == null || count($campaignsId) == 0) && $tokInbound == 1) {
                    $response = $objCall->digitselectinb($parameters);
                    header("Content-type: text/xml");
                    echo $response;
                    exit;
                }
            } else if ($digit_pressed == "2" || $digit_pressed == 2)  {
                $campaignsId = $this->matchedcallcampaigns("local", $datetime, $this->customerNumber, $this->custDialNumber, $callUuid, $lead_category); //Commented BY HJ On 14-06-2023 For Insert Lead In Buffer when Digit Press 2 As Per Discuss with MV and Sir.
                $coverageType = "local";
                $this->createInboundTableLogs($parameters['From'], $parameters['To'], '3', '3 SnapIt Log digitselect parameter checking digit' . $digit_pressed . "==inbound==" . $tokInbound . "==Data==" . json_encode($campaignsId));
                if (($campaignsId == null || count($campaignsId) == 0) && $tokInbound == 1) {
                    $response = $objCall->digitselectinb($parameters);
                    header("Content-type: text/xml");
                    echo $response;
                    exit;
                }
            } 
            // $LeadCall = LeadCall::where('legA_call_uuid', $callUuid)->get()->first();
            // if (!empty($LeadCall)) {
            //Added By HJ On 04-01-2022 For Custom Encrypt and Decrypt Method Start
            $endDecObj = new Helper;
            $general_variable_1 = Helper::checkServer()['general_variable_1'];
            $general_variable_2 = Helper::checkServer()['general_variable_2'];
            $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
            $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
            //echo $decVariable1."==".$decVariable2;die;
            //Added By HJ On 04-01-2022 For Custom Encrypt and Decrypt Method End
            // Fetch recording url
            $para = json_encode(array('time_limit' => '3600'));
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/Call/' . $callUuid . '/Record/');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $para);
            curl_setopt($ch, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
            $headers = array();
            $headers[] = 'Content-Type: application/json';
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            $result = curl_exec($ch);
            if (curl_errno($ch)) {
                //echo 'Error:' . curl_error($ch);
            }
            curl_close($ch);
            // }
            $forward_number = "";
            $this->createInboundTableLogs($parameters['From'], $parameters['To'], '3', "3 SnapIt Log digitselect coveragetype= " . $coverageType . ", campaigns= " . json_encode($campaignsId, true));
            $country_code = Helper::checkServer()['tok_country_code'];
            if ($campaignsId != null && count($campaignsId) > 0) {
                foreach ($campaignsId as $key => $value) {
                    $getCampData = Campaign::where('campaign_id', $key)->get(['campaign_id', 'forward_number'])->first()->toArray();
                    $forward_number = $getCampData['forward_number'];
                    $this->createInboundTableLogs($parameters['From'], $parameters['To'], '4', "4 SnapIt Log digitselect forward_number= " . $forward_number);
                    $getLeadCallData = LeadCall::where('legA_call_uuid', $callUuid)->get()->toArray();
                    $lead_call_id = 0;
                    if (count($getLeadCallData) > 0) {
                        $lead_call_id = $getLeadCallData[0]['lead_call_id'];
                        $insertIbData = array();
                        $insertIbData['lead_call_id'] = $lead_call_id;
                        $insertIbData['created_at'] = date('Y-m-d H:i:s');
                        $insertIbData['cust_received_time'] = date('Y-m-d H:i:s');
                        LeadIbCall::create($insertIbData);
                        $leadactiviyobj = new LeadActivity();
                        $leadactiviyobj->createActivity($getLeadCallData[0]['lead_id'], 6);
                    }
                    $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
                    $response = '<?xml version="1.0" encoding="UTF-8"?>
                                    <Response>
                                        <Dial callerId="' . $fromNumber . '" '.$callerName.' callbackUrl="' . $matrixBaseURL_Outbound . '/inboundcallcustaccept?lead_call_id=' . $lead_call_id . '">
                                            <Number>' . $country_code . $forward_number . '</Number>
                                        </Dial>
                                    </Response>';
                    //header("Content-type: text/xml");
                    $this->createInboundTableLogs($parameters['From'], $parameters['To'], '5', "5 SnapIt Log digitselect forward xml= " . $response . ", campaigns= " . $key);
                    return response($response, 200)->header('Content-Type', 'text/xml');
                }
            } else {
                // echo "Hello";
                $custNumber = $this->setTenDigitNumber($this->customerNumber);
                //Added By HJ On 19-05-2023 For Check Lead Exists Or Not When Create New Lead Start - Diascuss With MV
                $start_date = date('Y-m-d', strtotime("-90 days")) . ' 00:00:00';
                $end_date = date('Y-m-d 23:59:59');
                $checkExistLeadData = Lead::where("phone", $custNumber)->orderby('lead_id', 'DESC')->whereBetween('lead_generated_at', [$start_date, $end_date])->whereIn('lead_source_id', $sourceIdArr)->where('lead_category_id', $lead_category)->get()->take(1)->toArray();
                //Added By HJ On 19-05-2023 For Check Lead Exists Or Not When Create New Lead End - Diascuss With MV
                $this->createInboundLogs(5, "5 SnapIt Log digitselect No campaign found.");
                if (count($checkExistLeadData) > 0) {
                    $leadid = $checkExistLeadData[0]['lead_id'];
                    $this->createInboundLogs(6, "6 SnapIt Log digitselect found exists lead= " . $leadid . "==" . $lead_category . "==" . $originId . "==" . json_encode($sourceIdArr) . "==" . $start_date . "==" . $end_date . "==" . $custNumber);
                } else {
                    $leaddata = array(
                        'lead_source_id' => $originId,
                        'lead_category_id' => $lead_category,
                        'timezone_id' => $this->defaultimezone,
                        'lead_type' => "phone",
                        'phone' => $custNumber,
                        'email' => "",
                        'name' => "",
                        'is_verified' => "yes",
                        'payout' => 0,
                        "lead_generated_at" => date('Y-m-d H:i:s'),
                        "created_at" => date('Y-m-d H:i:s'),
                    );

                    $leadRequestLogId = LeadRequestLog::create([
                                'lead_request' => json_encode($leaddata)
                            ])->lead_request_log_id;
                    // $leadRequestLogId = 1307;
                    $leaddata['lead_request_log_id'] = $leadRequestLogId;
                    $leadid = Lead::create($leaddata)->lead_id;
                    $leadactiviyobj = new LeadActivity();
                    $leadactiviyobj->createActivity($leadid, 1);
                    $this->createInboundLogs(6, "6 SnapIt Log digitselect No campaign new leadid= " . $leadid);
                    $commonFieldArray = [];
                    $commonFieldArray['lead_id'] = $leadid;
                    $commonFieldArray['from_zipcode'] = null;
                    $commonFieldArray['from_areacode'] = null;
                    $commonFieldArray['from_city'] = null;
                    $commonFieldArray['from_state'] = null;
                    $commonFieldArray['to_zipcode'] = null;
                    $commonFieldArray['to_areacode'] = null;
                    $commonFieldArray['to_city'] = null;
                    $commonFieldArray['to_state'] = null;
                    $commonFieldArray['move_type'] = "local";
                    $commonFieldArray['distance'] = 1;
                    $commonFieldArray['property_type'] = "residential";
                    $commonFieldArray['move_size_id'] = 1;
                    $commonFieldArray['move_date'] = date("Y-m-d", strtotime("+7 days"));
                    $commonFieldArray['created_at'] = date("Y-m-d H:i:s");
                    $detailid = LeadMoving::create($commonFieldArray)->lead_moving_id;
                    $this->createInboundLogs(6, "6 SnapIt Log digitselect No campaign new leadid= " . $leadid . "==" . $lead_category . "==" . $originId . "==" . $start_date . "==" . $end_date . "==" . $custNumber);
                }
                $leadcalldata = array(
                    "lead_id" => $leadid,
                    "from_number" => $custNumber,
                    "to_number" => $this->custDialNumber,
                    "transfer_type" => "call",
                    "call_datetime" => date("Y-m-d H:i:s"),
                    "user_id" => 0,
                    "call_type" => "inbound",
                    "call_disposition_id" => 9,
                    "legA_call_uuid" => $callUuid,
                    "call_count" => 1,
                    "campaign_id" => 0,
                    "payout" => 0,
                    "campaign_score" => 0,
                    "inbound_type" => "automatic",
                    "created_at" => date('Y-m-d H:i:s'),
                );

                $leadcallid = LeadCall::create($leadcalldata)->lead_call_id;
                $leadactiviyobj = new LeadActivity();
                $leadactiviyobj->createActivity($leadid, 5);
                $buffer = new CallsbufferCronController();
                //$buffer->SaveToBufferViaPhoneonly($leadid);

                //$buffer = new CallsbufferCronController();
                // $buffer->SaveToBufferViaPhoneonly($lpCallForwardNew->lead_id);
                $hoursCheck = date('G');
                $devdeug = new DevDebug();
                $devdeug->devDebuglog(60, 'digitselect hoursCheck= ' . $hoursCheck);
                if ($hoursCheck >= 11 && $hoursCheck < 21) {
                    $devdeug->devDebuglog(61, 'digitselect date time');
                    $buffer->SaveToBufferViaPhoneonly($leadid);
                }else{

                    $devdeug->devDebuglog(62, 'digitselect night time');
                    $leadArr = Lead::where('lead_id', $leadid)->first()->toArray();
                    if ($leadArr['lead_type'] == 'phone') {
                        $buffer->saveFollowupentry($leadArr, 1, 1);
                    }else{
                        $buffer->saveFollowupentry($leadArr, 1);
                    }
                }


                $response = '<Response>
                    <Speak>Thank you for calling. Our representative will call you back.</Speak>
                </Response>';
                header("Content-type: text/xml");
                $this->createInboundTableLogs($parameters['From'], $parameters['To'], '7', "6 SnapIt Log digitselect forward_number null== " . $response . ", lead_call_id= " . $leadcallid);
                return $response;
            }
            $content = '<Response><Speak>Thank you</Speak></Response>';
            header("Content-type: text/xml");
            $this->createInboundTableLogs($parameters['From'], $parameters['To'], '7', "7 SnapIt Log digitselect final response== " . $content);
            echo $content;
            exit;
        } catch (Exception $e) {
            $this->createInboundTableLogs($parameters['From'], $parameters['To'], '8', "8 SnapIt Log digitselect catch=" . $e->getMessage());
        }
    }

    public function digitselectForRexdirect($postParameters="") {
        // dd("asd");
        $parameters = $_REQUEST;
        //Added By HJ On 03-08-2023 For Comio Number Start
        if (isset($parameters['SIP-H-To'])) {
            $parameters['To'] = substr($parameters['SIP-H-To'], 6, 11);
            //$parameters['From'] = trim($parameters['CallerName'], "+");
            $parameters['From'] = substr($parameters['From'], 5, 11);
        }
        //Added By HJ On 03-08-2023 For Comio Number End
        $this->createInboundTableLogs($parameters['From'], $parameters['To'], '1', '1 SnapIt Log digitselect ==' . json_encode($parameters));
        $digit_pressed = ((int) ($postParameters['Digits']));
        $datetime = $parameters['SessionStart'];
        $callUuid = $parameters['CallUUID'];
        // $digit_pressed = ((int) ("1"));
        // $datetime = "2023-04-21 04:50:49.948402";
        $digit_pressed_ex = trim($postParameters['Digits']);
        $parameters['Digits'] = $digit_pressed_ex;
        $parameters['Digits_2'] = $digit_pressed_ex;
        try {
            $this->customerNumber = $fromNumber = $parameters['From'];
            $this->custDialNumber = $toNumber = $parameters['To'];
            //Added by BK on 10-01-2023 For pass callerName parameter in xml as per discuss with MA
            $callerName = '';
            if ($toNumber == 18334080606 || $toNumber == "18334080606") {
                $callerName = 'callerName="'.$toNumber.'"';
            }
            //Added By HJ On 28-07-2023 For Get Particular Number's Source When Create New Lead Based On Dial Number Start
            $objCall = new PlivoController();
            $originData = $objCall->getOriginData($parameters['To']);
            $originName = $originData['origin'];
            $lead_category = 1;
            $originId = 130;
            $sourceIdArr = array();
            $sourceName = "VM";
            if (strpos($originName, 'QTM') !== false) {
                $sourceName = "QTM";
            } else if (strpos($originName, 'IS') !== false) {
                $sourceName = "IS";
            } else if(strpos($originName, 'REXDIRECT') !== false) {
                $sourceName = "REXDIRECT";
            }
            //Added By HJ On 28-07-2023 For Get Particular Number's Source When Create New Lead Based On Dial Number End
            $getVMSourceIds = MstLeadSource::where('lead_source_name', 'like', $sourceName . '%')->get(['lead_source_id', 'lead_source_name'])->toArray();
            for ($s = 0; $s < count($getVMSourceIds); $s++) {
                if (!in_array($getVMSourceIds[$s]['lead_source_id'], $sourceIdArr)) {
                    $sourceIdArr[] = $getVMSourceIds[$s]['lead_source_id'];
                }
                if (strtoupper($getVMSourceIds[$s]['lead_source_name']) == $sourceName . "P") {
                    $originId = $getVMSourceIds[$s]['lead_source_id'];
                }
            }
            $getConfigData = MstCallConfig::where("status", "active")->get()->toArray();
            $tokInbound = 0;
            if (count($getConfigData) > 0) {
                for ($c = 0; $c < count($getConfigData); $c++) {
                    if (strtolower($getConfigData[$c]['call_config']) == "callitinbound" && strtolower($getConfigData[$c]['status']) == "active") {
                        $tokInbound = 1;
                    }
                }
            }
            if ((!isset($digit_pressed) ) || (!isset($callUuid) )) {
                throw new Exception("Digit Not Founds");
            }
            $this->createInboundTableLogs($parameters['From'], $parameters['To'], '2', '2 SnapIt Log digitselect ==' . $digit_pressed_ex . "==Data==" . json_encode($parameters) . "==" . $originName . "==" . $sourceName . "==" . json_encode($sourceIdArr));
            $getCampData = $campaignsId = [];
            $coverageType = "";
            if ($digit_pressed == "1" || $digit_pressed == 1) {
                $campaignsId = $this->matchedcallcampaigns("long", $datetime, $this->customerNumber, $this->custDialNumber, $callUuid, $lead_category);
                $coverageType = "long";
                $this->createInboundTableLogs($parameters['From'], $parameters['To'], '3', '3 SnapIt Log digitselect parameter checking inbound' . $tokInbound . "==Data==" . json_encode($campaignsId));
                if (($campaignsId == null || count($campaignsId) == 0) && $tokInbound == 1) {
                    $response = $objCall->digitselectinb($parameters);
                    header("Content-type: text/xml");
                    echo $response;
                    exit;
                }
            } else if ($digit_pressed == "2" || $digit_pressed == 2) {
                $campaignsId = $this->matchedcallcampaigns("local", $datetime, $this->customerNumber, $this->custDialNumber, $callUuid, $lead_category); //Commented BY HJ On 14-06-2023 For Insert Lead In Buffer when Digit Press 2 As Per Discuss with MV and Sir.
                $coverageType = "local";
                $this->createInboundTableLogs($parameters['From'], $parameters['To'], '3', '3 SnapIt Log digitselect parameter checking digit' . $digit_pressed . "==inbound==" . $tokInbound . "==Data==" . json_encode($campaignsId));
                if (($campaignsId == null || count($campaignsId) == 0) && $tokInbound == 1) {
                    $response = $objCall->digitselectinb($parameters);
                    header("Content-type: text/xml");
                    echo $response;
                    exit;
                }
            }
            // $LeadCall = LeadCall::where('legA_call_uuid', $callUuid)->get()->first();
            // if (!empty($LeadCall)) {
            //Added By HJ On 04-01-2022 For Custom Encrypt and Decrypt Method Start
            $endDecObj = new Helper;
            $general_variable_1 = Helper::checkServer()['general_variable_1'];
            $general_variable_2 = Helper::checkServer()['general_variable_2'];
            $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
            $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
            //echo $decVariable1."==".$decVariable2;die;
            //Added By HJ On 04-01-2022 For Custom Encrypt and Decrypt Method End
            // Fetch recording url
            $para = json_encode(array('time_limit' => '3600'));
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/Call/' . $callUuid . '/Record/');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $para);
            curl_setopt($ch, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
            $headers = array();
            $headers[] = 'Content-Type: application/json';
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            $result = curl_exec($ch);
            if (curl_errno($ch)) {
                //echo 'Error:' . curl_error($ch);
            }
            curl_close($ch);
            // }
            $forward_number = "";
            $this->createInboundTableLogs($parameters['From'], $parameters['To'], '3', "3 SnapIt Log digitselect coveragetype= " . $coverageType . ", campaigns= " . json_encode($campaignsId, true));
            $country_code = Helper::checkServer()['tok_country_code'];
            if ($campaignsId != null && count($campaignsId) > 0) {
                foreach ($campaignsId as $key => $value) {
                    $getCampData = Campaign::where('campaign_id', $key)->get(['campaign_id', 'forward_number'])->first()->toArray();
                    $forward_number = $getCampData['forward_number'];
                    $this->createInboundTableLogs($parameters['From'], $parameters['To'], '4', "4 SnapIt Log digitselect forward_number= " . $forward_number);
                    $getLeadCallData = LeadCall::where('legA_call_uuid', $callUuid)->get()->toArray();
                    $lead_call_id = 0;
                    if (count($getLeadCallData) > 0) {
                        $lead_call_id = $getLeadCallData[0]['lead_call_id'];
                        $insertIbData = array();
                        $insertIbData['lead_call_id'] = $lead_call_id;
                        $insertIbData['created_at'] = date('Y-m-d H:i:s');
                        $insertIbData['cust_received_time'] = date('Y-m-d H:i:s');
                        LeadIbCall::create($insertIbData);
                        $leadactiviyobj = new LeadActivity();
                        $leadactiviyobj->createActivity($getLeadCallData[0]['lead_id'], 6);
                    }
                    $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
                    $response = '<?xml version="1.0" encoding="UTF-8"?>
                                    <Response>
                                        <Dial callerId="' . $fromNumber . '" '.$callerName.' callbackUrl="' . $matrixBaseURL_Outbound . '/inboundcallcustaccept?lead_call_id=' . $lead_call_id . '">
                                            <Number>' . $country_code . $forward_number . '</Number>
                                        </Dial>
                                    </Response>';
                    header("Content-type: text/xml");
                    $this->createInboundTableLogs($parameters['From'], $parameters['To'], '5', "5 SnapIt Log digitselect forward xml= " . $response . ", campaigns= " . $key);
                    return $response;
                }
            } else {
                // echo "Hello";
                $custNumber = $this->setTenDigitNumber($this->customerNumber);
                //Added By HJ On 19-05-2023 For Check Lead Exists Or Not When Create New Lead Start - Diascuss With MV
                $start_date = date('Y-m-d', strtotime("-90 days")) . ' 00:00:00';
                $end_date = date('Y-m-d 23:59:59');
                $checkExistLeadData = Lead::where("phone", $custNumber)->orderby('lead_id', 'DESC')->whereBetween('lead_generated_at', [$start_date, $end_date])->whereIn('lead_source_id', $sourceIdArr)->where('lead_category_id', $lead_category)->get()->take(1)->toArray();
                //Added By HJ On 19-05-2023 For Check Lead Exists Or Not When Create New Lead End - Diascuss With MV
                $this->createInboundLogs(5, "5 SnapIt Log digitselect No campaign found.");
                if (count($checkExistLeadData) > 0) {
                    $leadid = $checkExistLeadData[0]['lead_id'];
                    $this->createInboundLogs(6, "6 SnapIt Log digitselect found exists lead= " . $leadid . "==" . $lead_category . "==" . $originId . "==" . json_encode($sourceIdArr) . "==" . $start_date . "==" . $end_date . "==" . $custNumber);
                } else {
                    $leaddata = array(
                        'lead_source_id' => $originId,
                        'lead_category_id' => $lead_category,
                        'timezone_id' => $this->defaultimezone,
                        'lead_type' => "phone",
                        'phone' => $custNumber,
                        'email' => "",
                        'name' => "",
                        'is_verified' => "yes",
                        'payout' => 0,
                        "lead_generated_at" => date('Y-m-d H:i:s'),
                        "created_at" => date('Y-m-d H:i:s'),
                    );

                    $leadRequestLogId = LeadRequestLog::create([
                                'lead_request' => json_encode($leaddata)
                            ])->lead_request_log_id;
                    // $leadRequestLogId = 1307;
                    $leaddata['lead_request_log_id'] = $leadRequestLogId;
                    $leadid = Lead::create($leaddata)->lead_id;
                    $leadactiviyobj = new LeadActivity();
                    $leadactiviyobj->createActivity($leadid, 1);
                    $this->createInboundLogs(6, "6 SnapIt Log digitselect No campaign new leadid= " . $leadid);
                    $commonFieldArray = [];
                    $commonFieldArray['lead_id'] = $leadid;
                    $commonFieldArray['from_zipcode'] = null;
                    $commonFieldArray['from_areacode'] = null;
                    $commonFieldArray['from_city'] = null;
                    $commonFieldArray['from_state'] = null;
                    $commonFieldArray['to_zipcode'] = null;
                    $commonFieldArray['to_areacode'] = null;
                    $commonFieldArray['to_city'] = null;
                    $commonFieldArray['to_state'] = null;
                    $commonFieldArray['move_type'] = "local";
                    $commonFieldArray['distance'] = 1;
                    $commonFieldArray['property_type'] = "residential";
                    $commonFieldArray['move_size_id'] = 1;
                    $commonFieldArray['move_date'] = date("Y-m-d", strtotime("+7 days"));
                    $commonFieldArray['created_at'] = date("Y-m-d H:i:s");
                    $detailid = LeadMoving::create($commonFieldArray)->lead_moving_id;
                    $this->createInboundLogs(6, "6 SnapIt Log digitselect No campaign new leadid= " . $leadid . "==" . $lead_category . "==" . $originId . "==" . $start_date . "==" . $end_date . "==" . $custNumber);
                }
                $leadcalldata = array(
                    "lead_id" => $leadid,
                    "from_number" => $custNumber,
                    "to_number" => $this->custDialNumber,
                    "transfer_type" => "call",
                    "call_datetime" => date("Y-m-d H:i:s"),
                    "user_id" => 0,
                    "call_type" => "inbound",
                    "call_disposition_id" => 9,
                    "legA_call_uuid" => $callUuid,
                    "call_count" => 1,
                    "campaign_id" => 0,
                    "payout" => 0,
                    "campaign_score" => 0,
                    "inbound_type" => "automatic",
                    "created_at" => date('Y-m-d H:i:s'),
                );

                $leadcallid = LeadCall::create($leadcalldata)->lead_call_id;
                $leadactiviyobj = new LeadActivity();
                $leadactiviyobj->createActivity($leadid, 5);
                $buffer = new CallsbufferCronController();
                //$buffer->SaveToBufferViaPhoneonly($leadid);

                //$buffer = new CallsbufferCronController();
                // $buffer->SaveToBufferViaPhoneonly($lpCallForwardNew->lead_id);
                $hoursCheck = date('G');
                $devdeug = new DevDebug();
                $devdeug->devDebuglog(60, 'digitselect hoursCheck= ' . $hoursCheck);
                if ($hoursCheck >= 11 && $hoursCheck < 21) {
                    $devdeug->devDebuglog(61, 'digitselect date time');
                    $buffer->SaveToBufferViaPhoneonly($leadid);
                }else{

                    $devdeug->devDebuglog(62, 'digitselect night time');
                    $leadArr = Lead::where('lead_id', $leadid)->first()->toArray();
                    if ($leadArr['lead_type'] == 'phone') {
                        $buffer->saveFollowupentry($leadArr, 1, 1);
                    }else{
                        $buffer->saveFollowupentry($leadArr, 1);
                    }
                }


                $response = '<Response>
                    <Speak>Thank you for calling. Our representative will call you back.</Speak>
                </Response>';
                header("Content-type: text/xml");
                $this->createInboundTableLogs($parameters['From'], $parameters['To'], '7', "6 SnapIt Log digitselect forward_number null== " . $response . ", lead_call_id= " . $leadcallid);
                return $response;
            }
            $content = '<Response><Speak>Thank you</Speak></Response>';
            header("Content-type: text/xml");
            $this->createInboundTableLogs($parameters['From'], $parameters['To'], '7', "7 SnapIt Log digitselect final response== " . $content);
            echo $content;
            exit;
        } catch (Exception $e) {
            $this->createInboundTableLogs($parameters['From'], $parameters['To'], '8', "8 SnapIt Log digitselect catch=" . $e->getMessage());
        }
    }


    public function matchedcallcampaigns($coverageType, $datetime, $customerNumber, $custDialNumber, $callUuid, $lead_category) {
        $customerNumber = $this->setTenDigitNumber($customerNumber);
        $this->createInboundLogs(7, "7 SnapIt Log matchedcallcampaigns coverageType= " . $coverageType . " datetime = " . $datetime);
        try {
            $this->createInboundLogs(8, "8 SnapIt Log matchedcallcampaigns try coverageType= " . $coverageType);
            $sameCustomer_plivo = $this->getSameCustomer_plivo($coverageType, $customerNumber, $custDialNumber);
            //echo "<pre>"; print_r($sameCustomer_plivo); die;
            $this->createInboundLogs(9, '9 SnapIt Log matchedcallcampaigns' . json_encode($sameCustomer_plivo, true));
            $logicCampaign = new LeadCampaignLogic();

            $conditionchecked = 1;
            $dateCurrent = date('Y-m-d');
            $dateTimeCurrent = date("Y-m-d H:i:s");
            $routeArr = $activeCampAssocDataAr = $checkExclusiveCampaign = $checkCallCampaign = $campaignSortBundle = $getCampData = $callcampIds = $getBusinessData = array();
            $valStartDate = date('Y-m-d H:i:s', strtotime($dateTimeCurrent . '-3 days'));
            $objCall = new PlivoController();
            if ($coverageType == "local") {
                $originData = $objCall->getOriginData($custDialNumber);
                $originName = $originData['origin'];
               
                $sourceName = "IS";
                $callcampIds = array(3327, 2964);
                if (strpos($originName, 'IS') !== false) {
                    $callcampIds = array(3327); // Prod
                    //$callcampIds = array(671, 541); // Staging
                }
                //$callcampIds = array(75);
            } else {
                //Added By HJ On 14-09-2023 For Call to Same Business Exclusive Call Campaign without payout If lead already routes to Exclusive campaign Start
                $getBusinessData = DB::select("SELECT c.business_id from `lead_routing` lr INNER JOIN `lead` l ON lr.lead_id=l.lead_id INNER JOIN `campaign` c ON lr.campaign_id=c.campaign_id WHERE l.phone='" . $customerNumber . "' AND lr.created_at > '" . $valStartDate . "' AND lr.route_status = 'sold' AND c.campaign_type_id=2 AND c.is_active='yes'");
                //echo "<pre>asd"; print_r($getBusinessData); die;
                foreach ($getBusinessData as $key => $val) {
                    if (!in_array($val->business_id, $checkExclusiveCampaign)) {
                        $checkExclusiveCampaign[] = $val->business_id;
                    }
                }
                if (count($checkExclusiveCampaign) == 0) {
                    $callcampIds = $logicCampaign->getactivecallcampaigns($coverageType, $lead_category);
                }
            }
            try {
               
                $originData = $objCall->getOriginData($custDialNumber);
                $originName = $originData['origin'];
               
                $sourceName = "IS";
                if (strpos($originName, 'IS') !== false) {
                    $callcampIds = $logicCampaign->checkCampaignSourceIS($callcampIds,32);
                }     
            } catch(Exception $e) {
                $this->createInboundLogs(10, "10 SnapIt Log Error= " . $e->getMessage());
            }
            

            $this->createInboundLogs(10, "10 SnapIt Log matchedcallcampaigns getactivecallcampaigns= " . $coverageType . ", callcampIds= " . json_encode($callcampIds, true));
            $routeToSameExclusive = 0;
            if (count($checkExclusiveCampaign) > 0) {
                $checkCallCampaign = Campaign::whereIn('business_id', $checkExclusiveCampaign)->where('lead_type_id', 2)->where('is_active', 'yes')->groupBy('campaign_id')->pluck('campaign_id')->toArray();
                $leadRouting = LeadRouting::whereIn('campaign_id', $checkCallCampaign)->where('route_status', 'sold')->get()->toArray();
                for ($r = 0; $r < count($leadRouting); $r++) {
                    if ($dateCurrent == date('Y-m-d', strtotime($leadRouting[$r]['created_at']))) {
                        $routeArr[$leadRouting[$r]['campaign_id']][] = $leadRouting[$r];
                    }
                }
                $checkCallCampaign = $logicCampaign->checkTiming($checkCallCampaign, $dateCurrent, $dateTimeCurrent, $coverageType, $routeArr);
                // echo "<pre>";print_r($checkCallCampaign);die;

                $this->createInboundLogs(12, "12 SnapIt Log matchedcallcampaigns checkTiming Exclusive = " . json_encode($checkCallCampaign, true));
                if (count($checkCallCampaign) > 0) {
                    $getCampDatas = Campaign::whereIn('campaign_id', $checkCallCampaign)->get();
                    for ($x = 0; $x < count($getCampDatas); $x++) {
                        $activeCampAssocDataAr[$getCampDatas[$x]['campaign_id']] = $getCampDatas[$x];
                    }
                }
                $this->createInboundLogs(13, "13 SnapIt Log matchedcallcampaigns activeCampAssocDataAr Exclusive = " . json_encode($activeCampAssocDataAr, true));
                $checkCallCampaign = $logicCampaign->checkLimit($checkCallCampaign, $activeCampAssocDataAr, $coverageType, 1, 1);
                if (count($checkCallCampaign) > 0) {
                    $sameCustomer_plivo = array(2, $checkCallCampaign[0]);
                    $routeToSameExclusive = 1;
                }
            } else {
                $callcampIds = $logicCampaign->checkFund($callcampIds, $coverageType);
                if (count($callcampIds) == 0 && $sameCustomer_plivo[0] != 2) {
                    throw new Exception("Out of funds campaign");
                }
                // echo "<pre>"; print_r($sameCustomer_plivo); die;
                $this->createInboundLogs(11, "11 SnapIt Log matchedcallcampaigns checkFund = " . json_encode($callcampIds, true));
                // if(count($callcampIds) > 0 ){
                $leadRouting = LeadRouting::whereIn('campaign_id', $callcampIds)->where('route_status', 'sold')->get()->toArray();
                for ($r = 0; $r < count($leadRouting); $r++) {
                    if ($dateCurrent == date('Y-m-d', strtotime($leadRouting[$r]['created_at']))) {
                        $routeArr[$leadRouting[$r]['campaign_id']][] = $leadRouting[$r];
                    }
                }
                $callcampIds = $logicCampaign->checkTiming($callcampIds, $dateCurrent, $dateTimeCurrent, $coverageType, $routeArr);
                // echo "<pre>";print_r($callcampIds);die;
                if (count($callcampIds) == 0 && $sameCustomer_plivo[0] != 2) {
                    throw new Exception("No Timing campaigns");
                }
                $this->createInboundLogs(12, "12 SnapIt Log matchedcallcampaigns checkTiming = " . json_encode($callcampIds, true));
                if (count($callcampIds) > 0) {
                    $getCampDatas = Campaign::whereIn('campaign_id', $callcampIds)->get();
                    for ($x = 0; $x < count($getCampDatas); $x++) {
                        $activeCampAssocDataAr[$getCampDatas[$x]['campaign_id']] = $getCampDatas[$x];
                    }
                }
                $this->createInboundLogs(13, "13 SnapIt Log matchedcallcampaigns activeCampAssocDataAr = " . json_encode($activeCampAssocDataAr, true));
                $callcampIds = $logicCampaign->checkLimit($callcampIds, $activeCampAssocDataAr, $coverageType, 1, 1);
                if (count($callcampIds) == 0 && $sameCustomer_plivo[0] != 2) {
                    throw new Exception("No Limit campaigns");
                }
                // echo "<pre>"; print_r($callcampIds); die;
            }
            $this->createInboundLogs(14, "14 SnapIt Log matchedcallcampaigns same exclusive = " . json_encode($getBusinessData) . "==campidArr=" . json_encode($checkExclusiveCampaign) . "==" . json_encode($checkCallCampaign) . "==" . $customerNumber . "==" . $valStartDate . "==" . json_encode($sameCustomer_plivo));
            //Added By HJ On 14-09-2023 For Call to Same Business Exclusive Call Campaign without payout If lead already routes to Exclusive campaign End
            if ($routeToSameExclusive > 0) {
                $conditionchecked = 4;
                $campaignSortBundle = array($sameCustomer_plivo[1] => 1);
            } else {
                //Added By Kiran On 13-09-2023 For Check Same Customer's Lead Same Business Lead routed or not in 7 Days Start
                if (count($callcampIds) > 0) {
                    $businessIds = Campaign::whereIn('campaign_id', $callcampIds)->groupBy('business_id')->pluck('business_id')->toArray();
                    $getRecords = LeadRouting::join('campaign as c', 'lead_routing.campaign_id', 'c.campaign_id')
                                    ->join('lead AS l', 'l.lead_id', 'lead_routing.lead_id')
                                    ->whereIn('c.business_id', $businessIds)
                                    ->where('c.is_active', 'yes')
                                    ->where('lead_routing.created_at', '>', $valStartDate)
                                    ->where('lead_routing.route_status', "sold")
                                    ->where('l.phone', $customerNumber)->pluck('c.business_id')->toArray();

                    $commonElements = array_intersect($businessIds, $getRecords);
                    $getRoutedCampaignIds = array();
                    if (count($commonElements) > 0) {
                        $getRoutedCampaignIds = Campaign::whereIn('business_id', $commonElements)->where('is_active', 'yes')->pluck('campaign_id')->toArray();
                        foreach ($getRoutedCampaignIds as $key => $value) {
                            if (in_array($value, $callcampIds)) {
                                if (($key = array_search($value, $callcampIds)) !== false) {
                                    unset($callcampIds[$key]);
                                }
                            }
                        }
                        $callcampIds = array_values($callcampIds);
                    }
                    $this->createInboundLogs(14, "14 SnapIt Log check duplicate logic = " . json_encode($callcampIds, true) . "=businessIds=" . json_encode($businessIds) . "==valEndDate=" . $dateTimeCurrent . "=valStartDate=" . $valStartDate . "=getRecords=" . json_encode($getRecords) . "=commonElements=" . json_encode($commonElements) . "=getRoutedCampaignIds=" . json_encode($getRoutedCampaignIds));
                }
                //Added By Kiran On 13-09-2023 For Check Same Customer's Lead Same Business Lead routed or not in 7 Days End
                $this->createInboundLogs(14, "14 SnapIt Log matchedcallcampaigns checkLimit = " . json_encode($callcampIds, true));
            }
            if ($sameCustomer_plivo[0] == 2 && $conditionchecked != 4) {
                $conditionchecked = 2;
                $campaignSortBundle = array($sameCustomer_plivo[1] => 1);
            } else if ($sameCustomer_plivo[0] == 1 && $conditionchecked != 4) {
                $conditionchecked = 3;
                $callcampIds = array($sameCustomer_plivo[1]);
                $campaignSortBundle = $logicCampaign->checkCampaignLogic($callcampIds, $coverageType, $dateCurrent, $dateTimeCurrent, 4, 0, 0, 1, 0, 0, $customerNumber, $custDialNumber);
            } else if ($conditionchecked != 4) {
                $campaignSortBundle = $logicCampaign->checkCampaignLogic($callcampIds, $coverageType, $dateCurrent, $dateTimeCurrent, 4, 0, 0, 1, 0, 0, $customerNumber, $custDialNumber);
            }
            $this->createInboundLogs(15, "15 SnapIt Log matchedcallcampaigns campaignSortBundle " . $conditionchecked . "= " . json_encode($campaignSortBundle, true));
            $custNumber = $this->setTenDigitNumber($customerNumber);
            //Added By HJ On 28-07-2023 For Get Particular Number's Source When Create New Lead Based On Dial Number Start
            $objCall = new PlivoController();
            $originData = $objCall->getOriginData($custDialNumber);
            $originName = $originData['origin'];
            $lead_category = 1;
            $originId = 130;
            $sourceIdArr = array();
            $sourceName = "VM";
            if (strpos($originName, 'QTM') !== false) {
                $sourceName = "QTM";
            } else if (strpos($originName, 'IS') !== false) {
                $sourceName = "IS";
            } else if (strpos($originName, 'REXDIRECT') !== false) {
                $sourceName = "REXDIRECT";
            }
            //Added By HJ On 28-07-2023 For Get Particular Number's Source When Create New Lead Based On Dial Number End
            $getVMSourceIds = MstLeadSource::where('lead_source_name', 'like', $sourceName . '%')->get(['lead_source_id', 'lead_source_name'])->toArray();
            for ($s = 0; $s < count($getVMSourceIds); $s++) {
                if (!in_array($getVMSourceIds[$s]['lead_source_id'], $sourceIdArr)) {
                    $sourceIdArr[] = $getVMSourceIds[$s]['lead_source_id'];
                }
                if (strtoupper($getVMSourceIds[$s]['lead_source_name']) == $sourceName . "P") {
                    $originId = $getVMSourceIds[$s]['lead_source_id'];
                }
            }
            // echo "<pre>campaignSortBundle = "; print_r($campaignSortBundle); die;
            if (count($campaignSortBundle) > 0) {
                foreach ($campaignSortBundle as $key => $value) {
                    $getCampData = Campaign::where('campaign_id', $key)->first();
                    $this->createInboundLogs(16, "16 SnapIt Log matchedcallcampaigns campaignSortBundle finalcamp=" . $key . ", campData= " . json_encode($getCampData, true));
                    if ($getCampData) {
                        $forward_number = $getCampData->forward_number;
                        //$payout = CommonFunctions::getCampaignPayout($key, $coverageType, 0, 1);
                        //For Get Campaign Payout As Per Critearea //Parameter (1-Camp Id),(2-Coverage 0-local,1-long),(3-tok 1-tok,0-automated),(4-call type inbound-1,outbound-0)
                        $payout = $free_lead = $campScore = 0;
                        if (isset($value['payout'])) {
                            $payout = $value['payout'];
                        }
                        if (isset($value['free_lead'])) {
                            $free_lead = $value['free_lead'];
                        }
                        if (isset($value['score'])) {
                            $campScore = $value['score'];
                        }
                        $this->createInboundLogs(17, "17 SnapIt Log matchedcallcampaigns campaignSortBundle finalcamp 1=" . $key . ", payout=" . $payout);
                        if ($sameCustomer_plivo[0] == 2) {
                            $payout = 0;
                        }
                        $this->createInboundLogs(18, "18 SnapIt Log matchedcallcampaigns payout applied=" . $payout);
                        //Added By HJ On 19-05-2023 For Check Lead Exists Or Not When Create New Lead Start - Diascuss With MV
                        $start_date = date('Y-m-d', strtotime("-90 days")) . ' 00:00:00';
                        $end_date = date('Y-m-d 23:59:59');
                        $checkExistLeadData = Lead::where("phone", $custNumber)->orderby('lead_id', 'DESC')->whereBetween('lead_generated_at', [$start_date, $end_date])->whereIn('lead_source_id', $sourceIdArr)->where('lead_category_id', $lead_category)->get()->take(1)->toArray();
                        //Added By HJ On 19-05-2023 For Check Lead Exists Or Not When Create New Lead End - Diascuss With MV
                        //$existlead = Lead::where("phone", $custNumber)->first(); // Commented By HJ On 19-05-2023 and Checked Lead As Per Above
                        $this->createInboundLogs(19, "19 SnapIt Log matchedcallcampaigns lead found= " . json_encode($checkExistLeadData));
                        $sold_type = "none";
                        if (count($checkExistLeadData) > 0) {
                            //$leadid = $existlead->lead_id;
                            $leadid = $checkExistLeadData[0]['lead_id'];
                            $sold_type = $checkExistLeadData[0]['sold_type'];
                            // $updateData = ['payout'=>$payout, "updated_at" => date('Y-m-d H:i:s')];
                            // Lead::where("lead_id", $leadid)->update($updateData);
                            $this->createInboundLogs(20, "20 SnapIt Log matchedcallcampaigns campaignSortBundle update leadid= " . $leadid);
                        } else {

                            $leaddata = array(
                                'lead_source_id' => $originId,
                                'lead_category_id' => $lead_category,
                                'timezone_id' => $this->defaultimezone,
                                'lead_type' => "phone",
                                'phone' => $custNumber,
                                'email' => "",
                                'name' => "",
                                'is_verified' => "yes",
                                'payout' => $payout,
                                "lead_generated_at" => date('Y-m-d H:i:s'),
                                "created_at" => date('Y-m-d H:i:s'),
                            );

                            $leadRequestLogId = LeadRequestLog::create([
                                        'lead_request' => json_encode($leaddata)
                                    ])->lead_request_log_id;
                            // $leadRequestLogId = 1307;
                            $leaddata['lead_request_log_id'] = $leadRequestLogId;
                            $leadid = Lead::create($leaddata)->lead_id;
                            $leadactiviyobj = new LeadActivity();
                            $leadactiviyobj->createActivity($leadid, 1);
                            $this->createInboundLogs(21, "21 SnapIt Log matchedcallcampaigns campaign new leadid= " . $leadid);
                            $commonFieldArray = [];
                            $commonFieldArray['lead_id'] = $leadid;
                            $commonFieldArray['from_zipcode'] = null;
                            $commonFieldArray['from_areacode'] = null;
                            $commonFieldArray['from_city'] = null;
                            $commonFieldArray['from_state'] = null;
                            $commonFieldArray['to_zipcode'] = null;
                            $commonFieldArray['to_areacode'] = null;
                            $commonFieldArray['to_city'] = null;
                            $commonFieldArray['to_state'] = null;
                            $commonFieldArray['move_type'] = "local";
                            $commonFieldArray['distance'] = 1;
                            $commonFieldArray['property_type'] = "residential";
                            $commonFieldArray['move_size_id'] = 1;
                            $commonFieldArray['move_date'] = date("Y-m-d");
                            $commonFieldArray['created_at'] = date("Y-m-d H:i:s");
                            $detailid = LeadMoving::create($commonFieldArray)->lead_moving_id;

                            $this->createInboundLogs(22, "22 SnapIt Log matchedcallcampaigns campaignSortBundle new leadid= " . $leadid);
                        }
                        // $dateTimeCurrent = date('Y-m-d H:i:s', strtotime($datetime));
                        if ($leadid > 0) {
                            $leadcalldata = array(
                                "lead_id" => $leadid,
                                "from_number" => $custNumber,
                                "to_number" => $custDialNumber,
                                "transfer_number" => $forward_number,
                                "transfer_type" => "transfer",
                                "call_datetime" => date("Y-m-d H:i:s"),
                                "user_id" => 0,
                                "call_type" => "inbound",
                                "call_disposition_id" => 1,
                                "legA_call_uuid" => $callUuid,
                                "call_count" => 1,
                                "campaign_id" => $key,
                                "payout" => $payout,
                                "campaign_score" => $campScore,
                                "inbound_call_status" => "answered",
                                "inbound_type" => "automatic",
                                "created_at" => date('Y-m-d H:i:s'),
                            );

                            $leadcallid = LeadCall::create($leadcalldata)->lead_call_id;
                            $leadactiviyobj = new LeadActivity();
                            $leadactiviyobj->createActivity($leadid, 5);
                            $this->createInboundLogs(23, "23 SnapIt Log matchedcallcampaigns campaignSortBundle LeadCall= " . $leadcallid);
                            //echo "<pre>";print_r($getCampaignRouting);die;
                            $idFreeLead = "no";
                            if ($free_lead > 0) {
                                $idFreeLead = "yes";
                            }
                            $creditAvailable = $getCampData->credit_available;
                            if ($getCampData->payment_type == 0) {
                                $businessesRecord = Business::where('business_id', $getCampData->business_id)->get(['credit_available', 'business_id'])->toArray();
                                if (count($businessesRecord) > 0) {
                                    $creditAvailable = $businessesRecord[0]['credit_available'];
                                }
                            }
                            $insertRouting = array();
                            $insertRouting['lead_id'] = $leadid;
                            $insertRouting['campaign_id'] = $key;
                            $insertRouting['payout'] = $payout;
                            $insertRouting['score'] = $campScore;
                            $insertRouting['route_status'] = 'sold';
                            $insertRouting['is_free_lead'] = $idFreeLead;
                            $insertRouting['balance_after_charge'] = $creditAvailable;
                            $insertRouting['payment_type'] = $getCampData->payment_type;
                            $insertRouting['created_at'] = date('Y-m-d H:i:s');
                            $leadRoutingId = LeadRouting::create($insertRouting)->lead_routing_id;
                            CommonFunctions::insertLeadCallRoutingData($leadid, $leadcallid, $leadRoutingId); //Added By HJ On 25-07-2023 For Insert Data Into New Table lead_call_routing
                            $this->createInboundLogs(24, "24 SnapIt Log matchedcallcampaigns campaignSortBundle leadrouting= " . json_encode($leadRoutingId, true));
                            $leadRouting = LeadRouting::where('lead_id', $leadid)->where('route_status', 'sold')->sum('payout');
                            $this->createInboundLogs(25, "25 SnapIt Log matchedcallcampaigns campaignSortBundle totalpayout= " . $leadRouting);
                            $getCampaignTypeData = MstCampaignType::where('campaign_type_id', $getCampData->campaign_type_id)->get()->toArray();
                            //Added By HJ On 18-07-2023 For Insert Sold type as per transfer campaign Start
                            $soldType = "premium";
                            if (count($getCampaignTypeData) > 0) {
                                $soldType = strtolower($getCampaignTypeData[0]['campaign_type']);
                                if (strpos($soldType, 'dual') !== false) {
                                    $soldType = "dual";
                                } else if (strpos($soldType, 'organic') !== false) {
                                    $soldType = "organic";
                                }
                            }
                            $updatePayoutArr = array();
                            if (strtolower($sold_type) == "none") {
                                $updatePayoutArr['sold_type'] = $soldType;
                            }
                            if ($leadRouting) {
                                $updatePayoutArr['payout'] = $leadRouting;
                            } else {
                                $updatePayoutArr['payout'] = 0;
                            }
                            $updatePayoutArr['updated_at'] = date('Y-m-d H:i:s');
                            $this->createInboundLogs(26, "26 SnapIt Log matchedcallcampaigns campaignSortBundle sold_type= " . json_encode($updatePayoutArr));
                            Lead::where('lead_id', $leadid)->update($updatePayoutArr);
                            //Added By HJ On 18-07-2023 For Insert Sold type as per transfer campaign End
                            $transferArr = array();
                            $transferArr['lead_id'] = $leadid;
                            $transferArr['campaign_id'] = $key;
                            $transferArr['lead_call_id'] = $leadcallid;
                            $transferArr['calls_type'] = "inbound";
                            $transferArr['call_uuid'] = $callUuid;
                            $transferArr['payout'] = $payout;
                            $transferArr['is_checked_rec'] = "no";
                            $transferArr['transfer_type'] = "transfer";
                            $transferArr['lead_category_id'] = $lead_category;
                            $transferArr['created_at'] = date('Y-m-d H:i:s');
                            LeadTransferCall::create($transferArr);
                        }
                    }
                }
            }
            return $campaignSortBundle;
        } catch (Exception $e) {
            DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 SnapIt Log matchedcallcampaigns catch error=" . $e->getMessage(), 'created_at' => date('Y-m-d H:i:s')]);
        }
        // echo "<pre>finalCamId = "; print_r(  $finalCamId);die;
    }

    public function getSameCustomer_plivo($coverageType, $cust_number, $cust_dial_number) {
        $this->createInboundLogs(1, '1 SnapIt Log getSameCustomer_plivo cust_number= ' . $cust_number . ", cust_dial_number =" . $cust_dial_number);
        $currentdate = date('Y-m-d H:i:s');
        $date48 = new DateTime($currentdate);
        $date48->modify('-72 hours');
        $before_72hours_date = $date48->format('Y-m-d H:i:s');

        $inbound_entry = LeadCall::where('from_number', $cust_number)->where('to_number', $cust_dial_number)->whereIn("call_type", ["inbound","outbound"])->whereBetween('created_at', [$before_72hours_date, $currentdate])->orderby('lead_call_id', 'DESC')->get()->toArray();
        //echo $before_72hours_date."==".$currentdate;die;
        $this->createInboundLogs(2, '2 SnapIt Log getSameCustomer_plivo inbound_entry =' . json_encode($inbound_entry, true));
        $freeCampArr = $applyCampArr = array();
        if (count($inbound_entry) > 0) {
            // echo "Yes";
            $this->createInboundLogs(3, '3 SnapIt Log getSameCustomer_plivo inbound_entry 1=' . json_encode($inbound_entry, true));
            for ($g = 0; $g < count($inbound_entry); $g++) {
                $last_call = strtotime($inbound_entry[$g]['created_at']);
                $timestamp2 = strtotime($currentdate);
                $difference_between_call = abs($timestamp2 - $last_call) / (60 * 60);
                $this->createInboundLogs(4, '4 SnapIt Log getSameCustomer_plivo 2 =' . $inbound_entry[$g]['campaign_id'] . ", difference_between_call= " . $difference_between_call);
                if ($difference_between_call < 2) {
                    // echo "<pre>";print_r($difference_between_call); // only check if existing entry is within 2 hours? and yes then call to same company.
                    // check last 2 min call
                    $appliedPayout = $inbound_entry[$g]['payout']; //Added By HJ On 28-06-2023 For Checked Last Called Payout
                    $minutes = abs($last_call - time()) / 60;
                    $this->createInboundLogs(5, '5 SnapIt Log getSameCustomer_plivo 3=' . $difference_between_call . ", minute:" . $minutes);

                    $getCampaignStatus = Campaign::with(['businessInfo' => function ($query) {
                                    return $query->where('status', "active");
                                }, "campaignMoving", "campaignJunkType", "campaignHeavyLifting", "campaignCarTransport"])->where('campaign_id', $inbound_entry[$g]['campaign_id'])->where('is_active', "yes")->get()->toArray();
                    $this->createInboundLogs(6, '6 SnapIt Log getSameCustomer_plivo 4 =' . json_encode($getCampaignStatus, true));

                    $moveinfo = [];
                    if ($getCampaignStatus != null) {
                        if (count($getCampaignStatus) > 0) {
                            if ($getCampaignStatus[0]['lead_category_id'] == 1 || $getCampaignStatus[0]['lead_category_id'] == "1") {
                                $moveinfo = $getCampaignStatus[0]['campaign_moving'][0];
                            } else if ($getCampaignStatus[0]['lead_category_id'] == 2 || $getCampaignStatus[0]['lead_category_id'] == "2") {
                                $moveinfo = $getCampaignStatus[0]['campaign_junk_type'][0];
                            } else if ($getCampaignStatus[0]['lead_category_id'] == 3 || $getCampaignStatus[0]['lead_category_id'] == "3") {
                                $moveinfo = $getCampaignStatus[0]['campaign_heavy_lifting'][0];
                            } else if ($getCampaignStatus[0]['lead_category_id'] == 4 || $getCampaignStatus[0]['lead_category_id'] == "4") {
                                $moveinfo = $getCampaignStatus[0]['campaign_car_transport'][0];
                            }
                        }
                    }
                    if ($moveinfo != null) {
                        if (count($moveinfo) > 0) {
                            if ($moveinfo['move_type'] == $coverageType)
                                if (isset($getCampaignStatus[0]['business_info']['business_id']) && count($getCampaignStatus) > 0) {
                                    $inboundsecondslimit = MstCallConfig::where('call_config', 'inbound_payout_after_second')->first()->value;
                                    if($inbound_entry[$g]['call_type'] == 'outbound') {
                                        $freeCampArr[] = $inbound_entry[$g]['campaign_id'];
                                    } else {
                                        if ($appliedPayout > 0 && $inbound_entry[$g]['duration'] > $inboundsecondslimit) {
                                            //return array(2, $inbound_entry[$g]['campaign_id']);
                                            $freeCampArr[] = $inbound_entry[$g]['campaign_id'];
                                        } else {
                                            //return array(1, $inbound_entry[$g]['campaign_id']);
                                            $applyCampArr[] = $inbound_entry[$g]['campaign_id'];
                                        }
                                    }
                                    
                                }
                        }
                    }
                }
                // die;
            }
        }
        //Added By HJ On 11-08-2023 For Checked Campaign Last Applied Payout Or Not Start
        if (count($freeCampArr) > 0) {
            return array(2, $freeCampArr[0]);
        }
        if (count($applyCampArr) > 0) {
            return array(1, $applyCampArr[0]);
        }
        //Added By HJ On 11-08-2023 For Checked Campaign Last Applied Payout Or Not End
        return array(0, 0);
    }

    public function setTenDigitNumber($phone) {
        $phoneLength = strlen(trim($phone));
        if ($phoneLength > 10) {
            $removeChr = $phoneLength - 10;
            $phone = substr(trim($phone), $removeChr);
        }
        //Added By HJ On 16/06/2022 For Check Customer Number Not Valid Start
        $phoneLength = strlen(trim($phone));
        if ($phoneLength != 10) {
            $oldphone = $phone;
            $phone = 9999999999;
            $this->createInboundLogs(1, "1 SnapIt Log setTenDigitNumber Phone=" . $phone . "==phoneLength==" . $phoneLength);
        }
        //Added By HJ On 16/06/2022 For Check Customer Number Not Valid End
        return $phone;
    }

    public function createInboundLogs($sr_no, $result) {
        DB::table('dev_debug')->insert(['sr_no' => $sr_no, 'result' => $result, 'created_at' => date('Y-m-d H:i:s')]);
    }

    public function createInboundTableLogs($fromnumber, $tonumber, $type, $log) {
        $log = array('from_number' => $fromnumber, 'to_number' => $tonumber, 'log_srno' => $type, 'log' => $log, 'created_at' => date("Y-m-d H:i:s"));
        LeadIbCallLog::create($log);
    }

    public function phoneautomatedleadlist(Request $request) {
        $this->createInboundLogs(1, '1 SnapIt Log phoneautomatedleadlist=' . json_encode($request->all(), true));
        // echo "<pre>";print_r($request->all());
        // $origins = MstLeadSource::get()->toArray();
        $origins = SourceIncomingNumber::get()->toArray();
        // echo "<pre>";print_r($origins); die;
        $daterange = $request->get('daterange');
        $origin = $request->get('origin');
        if (isset($daterange) && $daterange != '') {
            $dateRangeArray = explode(' - ', $daterange);
            $valStartDate = date("Y-m-d", strtotime($dateRangeArray[0])) . ' 00:00:00';
            $valEndDate = date("Y-m-d", strtotime($dateRangeArray[1])) . ' 23:59:59';
        } else {
            $valStartDate = date("Y-m-d 00:00:00");
            $valEndDate = date("Y-m-d 23:59:59");
        }
        $this->createInboundLogs(2, '2 SnapIt Log phoneautomatedleadlist StartDate=' . $valStartDate);
        $this->createInboundLogs(3, '3 SnapIt Log phoneautomatedleadlist EndDate=' . $valEndDate);
        $leadCallData = LeadCall::with(["leadData", "leadData.moveInfo", "leadData.junkMoveInfo", "leadData.heavyLiftMoveInfo", "leadData.carTransMoveInfo", "routingCallCampaignInfo", "routingCallCampaignInfo.businessInfo", "routingCallCampaignInfo.campaignMoving", "routingCallCampaignInfo.campaignHeavyLifting", "routingCallCampaignInfo.campaignCarTransport", "calldesposition", "businessData"]);
        // $leadCallData->WhereHas('leadData', function ($query) use ($origin) {
        $phone_origins = [];
        $where = '';
        if ($origin != '' || $origin != null) {
            $origin_incoming = SourceIncomingNumber::where('incoming_phone', "LIKE", "%" . $origin . "%")->get()->toArray();
            if (count($origin_incoming) > 0) {
                foreach ($origin_incoming as $key => $value) {
                    $phone_origins[$value['incoming_phone']] = $value['lead_source_name'];
                }
            }
            $where = "lc.to_number = '" . $origin . "' AND";
            $leadCallData = $leadCallData->where("to_number", "LIKE", "%" . $origin . "%");
        } else {
            $origin_incoming = SourceIncomingNumber::get()->toArray();
            if (count($origin_incoming) > 0) {
                foreach ($origin_incoming as $key => $value) {
                    $phone_origins[$value['incoming_phone']] = $value['lead_source_name'];
                }
            }
        }
        // echo "<pre>hi  =";print_r($phone_origins ); die;

        $leadCallData->where("call_type", "inbound")->whereBetween(DB::raw('DATE(created_at)'), array($valStartDate, $valEndDate));
        $leadCalls = $leadCallData->orderBy('lead_call_id', 'DESC')->get()->toArray();
        // echo "<pre>";print_r($leadCalls); die;
        $this->createInboundLogs(4, '4 SnapIt Log phoneautomatedleadlist leadCalls=' . json_encode($leadCalls, true));
        $leadCallData = [];
        if (count($leadCalls) > 0) {
            foreach ($leadCalls as $key => $value) {
                // table for lead call

                $leadinfo = [];
                $coverageType = "local";
                $businessname = $campaignname = $call_dispositionname = "--";
                if ($value['routing_call_campaign_info'] != null) {
                    if (count($value['routing_call_campaign_info']) > 0) {
                        if (count($value['routing_call_campaign_info']['business_info']) > 0) {
                            $businessname = ucfirst($value['routing_call_campaign_info']['business_info']['business_name']);
                        }
                        $campaignname = ucfirst($value['routing_call_campaign_info']['campaign_name']);
                        $moveinfo = [];
                        if ($value['routing_call_campaign_info']['lead_category_id'] == 1 || $value['routing_call_campaign_info']['lead_category_id'] == "1") {
                            $moveinfo = $value['routing_call_campaign_info']['campaign_moving'];
                        } else if ($value['routing_call_campaign_info']['lead_category_id'] == 3 || $value['routing_call_campaign_info']['lead_category_id'] == "3") {
                            $moveinfo = $value['routing_call_campaign_info']['campaign_heavy_lifting'];
                        } else if ($value['routing_call_campaign_info']['lead_category_id'] == 4 || $value['routing_call_campaign_info']['lead_category_id'] == "4") {
                            $moveinfo = $value['routing_call_campaign_info']['campaign_car_transport'];
                        }
                        if ($value['routing_call_campaign_info']['lead_category_id'] != 2 || $value['routing_call_campaign_info']['lead_category_id'] != "2") {
                            if ($moveinfo != null) {
                                // $fromState = $moveinfo['from_state'];
                                // $toState =  $moveinfo['to_state'];
                                // if (strcmp($fromState, $toState)) {
                                //     $coverageType = "long";
                                // }
                                $coverageType = $moveinfo[0]['move_type'];
                            }
                        }
                    }
                }

                if ($value['calldesposition'] != null) {
                    if (count($value['calldesposition']) > 0) {
                        $call_dispositionname = ucfirst($value['calldesposition']['call_disposition']);
                    }
                }

                $leadinfo['lead_call_id'] = $value['lead_call_id'];
                $leadinfo['lead_id'] = $value['lead_id'];
                $leadinfo['from_number'] = ($value['from_number'] != NULL && $value['from_number'] != "") ? $value['from_number'] : '--';
                $leadinfo['to_number'] = ($value['to_number'] != NULL && $value['to_number'] != "") ? $this->setTenDigitNumber($value['to_number']) : '--';
                $leadinfo['transfer_number'] = ($value['transfer_number'] != NULL && $value['transfer_number'] != "") ? $value['transfer_number'] : '--';
                $leadinfo['transfer_type'] = $value['transfer_type'];
                $leadinfo['call_datetime'] = $value['call_datetime'];
                $leadinfo['user_id'] = $value['user_id'];
                $leadinfo['duration'] = ($value['duration'] != NULL && $value['duration'] != "") ? $value['duration'] : '--';
                $leadinfo['recording_url'] = ($value['recording_url'] != NULL && $value['recording_url'] != "") ? $value['recording_url'] : '--';
                $leadinfo['call_type'] = ($value['call_type'] != NULL && $value['call_type'] != "") ? $value['call_type'] : '--';
                $leadinfo['call_disposition_id'] = ($value['call_disposition_id'] != NULL && $value['call_disposition_id'] != "") ? $value['call_disposition_id'] : '--';
                $leadinfo['legA_call_uuid'] = ($value['legA_call_uuid'] != NULL && $value['legA_call_uuid'] != "") ? $value['legA_call_uuid'] : '--';
                $leadinfo['call_count'] = ($value['call_count'] != NULL && $value['call_count'] != "") ? $value['call_count'] : '--';
                $leadinfo['is_checked_recording'] = $value['is_checked_recording'];
                $leadinfo['campaign_id'] = ($value['campaign_id'] != NULL && $value['campaign_id'] != "") ? $value['campaign_id'] : '--';
                $leadinfo['payout'] = ($value['payout'] != NULL && $value['payout'] != "") ? '$' . number_format($value['payout'], 2) : '--';
                $leadinfo['campaign_score'] = ($value['campaign_score'] != NULL && $value['campaign_score'] != "") ? $value['campaign_score'] : '--';
                $leadinfo['created_at'] = $value['created_at'];

                $call_datetime = strtotime($value['call_datetime']);

                $leadinfo['businessname'] = $businessname;
                // $leadinfo['date'] = date("m/d", $call_datetime);
                // $leadinfo['time'] = date("h:i a", $call_datetime);
                $leadinfo['datetime'] = date("m/d/Y h:i:s", $call_datetime);
                $leadinfo['campaignname'] = $campaignname;
                $leadinfo['lead_category_id'] = $value['lead_data']['lead_category_id'];
                $leadinfo['coverage_type'] = $coverageType;
                $leadinfo['call_disposition'] = $call_dispositionname;
                // $leadinfo['source'] = $phone_origins[$value['to_number']];

                $leadCallData[] = $leadinfo;
            }
        }
        // echo "<pre>";print_r($leadCallData);die;
        $this->createInboundLogs(5, "5 SnapIt Log phoneautomatedleadlist final leadcalls=" . json_encode($leadCallData, true));
        // lead origin data
        $leadoriginData = [];
        if (count($leadCallData) > 0) {
            // asort($leadCallData);
            foreach ($leadCallData as $k => $row) {
                $leadoriginData[$row['to_number']][$k] = $row;
            }
        }
        $this->createInboundLogs(6, "6 SnapIt Log phoneautomatedleadlist gruopby tonumner=" . json_encode($leadoriginData, true));
        $totalorigindata = [];
        $source = "";
        $dataFound = $totalLocalCalls = $totalLongCalls = $liveTransferCount = $businessCallCount = $totalliveTransferCount = $totalallCalls = 0;
        foreach ($leadoriginData as $key => $value) {
            $local_calls = $long_calls = $livetransfer_calls = [];
            if (count($value) > 0) {
                $dataFound = 1;
                foreach ($value as $key1 => $value1) {
                    if ($value1['coverage_type'] == "local") {
                        $local_calls[] = $value1;
                    }
                    if ($value1['coverage_type'] == "long") {
                        $long_calls[] = $value1;
                    }
                    if ($value1['transfer_number'] != "--" && $value1['call_disposition_id'] == 1) {
                        $livetransfer_calls[] = $value1;
                    }
                    $source = $value1['to_number'];
                    if (isset($phone_origins[$value1['to_number']])) {
                        $source = $phone_origins[$value1['to_number']];
                    }
                }
            }

            $totalorigindata[$key]['source'] = $source;
            $totalorigindata[$key]['cust_dial_number'] = $key;
            $totalorigindata[$key]['local_calls'] = count($local_calls);
            $totalorigindata[$key]['long_calls'] = count($long_calls);
            $totalorigindata[$key]['livetransfer_calls'] = count($livetransfer_calls);
            $totalorigindata[$key]['total_calls'] = count($local_calls) + count($long_calls);

            $totalLocalCalls += count($local_calls);
            $totalLongCalls += count($long_calls);
            $totalliveTransferCount += count($livetransfer_calls);
            $totalallCalls += count($local_calls) + count($long_calls);
            // echo "<pre>";print_r($livetransfer_calls);
        }
        $this->createInboundLogs(7, "7 SnapIt Log phoneautomatedleadlist all origindata=" . json_encode($totalorigindata, true));
        $this->createInboundLogs(8, "8 SnapIt Log phoneautomatedleadlist totalLocalCalls=" . $totalLocalCalls . ", totalLongCalls=" . $totalLongCalls . ", totalliveTransferCount=" . $totalliveTransferCount . ", totalallCalls=" . $totalallCalls);
        $getBusinessCount = LeadCall::where('transfer_type', 'businessrely')->where("call_type", "inbound")->whereBetween(DB::raw('DATE(created_at)'), array($valStartDate, $valEndDate))->count();
        $this->createInboundLogs(9, "9 SnapIt Log phoneautomatedleadlist getBusinessCount=" . $getBusinessCount);

        $data = [
            "leadcalls" => $leadCallData,
            'origins' => $origins,
            "totalorigindata" => $totalorigindata,
            'totalLocalCalls' => $totalLocalCalls,
            'totalLongCalls' => $totalLongCalls,
            'totalliveTransferCount' => $totalliveTransferCount,
            'totalallCalls' => $totalallCalls,
            'businessCallCount' => $getBusinessCount
        ];
        $this->createInboundLogs(11, "11 SnapIt Log phoneautomatedleadlist alldata=" . json_encode($data, true));
        // echo "<pre>";print_r($totalallCalls);
        // die;
        return view('Lead.phoneautomatedleadlist')->with($data);
    }

    public function inboundCallCustAccept(Request $request) {
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];

        $currentDateTime = date('Y-m-d H:i:s');
        try {
            $lead_call_id = $request->get('lead_call_id');
            $fetchLeadCall = LeadCall::where('lead_call_id', $lead_call_id)->first();
            if($fetchLeadCall) {
                $legACallUUID = $fetchLeadCall->legA_call_uuid;
                $params = array(
                        'call_uuid' => $legACallUUID,
                        'urls' => 'https://linkup:<EMAIL>/mp3/additional_2.mp3',
                        'legs' => 'both'
                );
                $value = json_encode( $params );
                $url = 'https://api.plivo.com/v1/Account/MAMJBLMDG2ZTE3MGJIMW/Call/'.$legACallUUID.'/Play/';
                $this->curl_call($url, $value);   
            }

            if ($lead_call_id > 0) {
                $checkCallData = LeadIbCall::where('lead_call_id', $lead_call_id)->whereNull('cust_received_time')->get()->toArray();
                DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 SnapIt Log inboundCallCustAccept called= " . $lead_call_id . "===Ib Data=" . json_encode($checkCallData), 'created_at' => $currentDateTime]);
                if (count($checkCallData) > 0) {
                    DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 SnapIt Log inboundCallCustAccept start= " . $lead_call_id, 'created_at' => $currentDateTime]);
                    LeadIbCall::where('lead_call_id', $lead_call_id)->update(['cust_received_time' => $currentDateTime]);
                    DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 SnapIt Log inboundCallCustAccept end= " . $lead_call_id . "==Ib Data cust_received_time" . json_encode($checkCallData), 'created_at' => $currentDateTime]);
                }
            } else {
                DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 SnapIt Log inboundCallCustAccept called= " . $lead_call_id . "===post Data=" . json_encode($request->all(), true), 'created_at' => $currentDateTime]);
            }
        } catch (Exception $ex) {
            DB::table('dev_debug')->insert(['sr_no' => 4, 'result' => "4 SnapIt Log inboundCallCustAccept catch error= " . $ex->getMessage(), 'created_at' => $currentDateTime]);
        }
    }

     public function playIVR(Request $request) {
        $currentDateTime = date('Y-m-d H:i:s');
        try {
                $legACallUUID = $request->get('call_uuid');
                if($request->get('call_id') !== null) {
                    $conferenceName = "Call_".$request->get('call_id');
                    //$conferenceName = "Call_".$legACallUUID;
                        $responseX = '<Response>
                                    <Conference
                                        waitSound="ring"
                                        startConferenceOnEnter="true"
                                        endConferenceOnExit="false"
                                    >'.$conferenceName.'</Conference>
                                </Response>';
                        // Echo the Response
                        header("Content-type: text/xml");
                        //DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 snapIt log callforwardtomain call==" . $response]);
                        echo $responseX;
                }
                
                $params = array(
                        'call_uuid' => $legACallUUID,
                        'urls' => 'https://linkup:<EMAIL>/mp3/additional_2.mp3',
                        'legs' => 'both'
                );
                $value = json_encode( $params );
                $url = 'https://api.plivo.com/v1/Account/MAMJBLMDG2ZTE3MGJIMW/Call/'.$legACallUUID.'/Play/';
                $this->curl_call($url, $value); 
        } catch (Exception $ex) {
            DB::table('dev_debug')->insert(['sr_no' => 4, 'result' => "4 SnapIt Log IVR catch error= " . $ex->getMessage(), 'created_at' => $currentDateTime]);
        }
    }

    public function curl_call($url, $datatran) {
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $general_variable_2 = Helper::checkServer()['general_variable_2'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
        //echo $decVariable1."===".$decVariable2;die;
        //Added By HJ On 06-01-2022 For Custom Encrypt and Decrypt Method End
        $ch = curl_init($url);
        curl_setopt_array($ch, array(
            CURLOPT_RETURNTRANSFER => TRUE,
            CURLOPT_POSTFIELDS => $datatran,
            CURLOPT_USERPWD => $decVariable1 . ':' . $decVariable2,
            CURLOPT_FOLLOWLOCATION => 1,
            CURLOPT_HTTPHEADER => array('Content-type: application/json', 'Cache-Control:no-store', 'Content-Length: ' . strlen($datatran)),
            CURLOPT_TIMEOUT => 40
        ));
        $response = curl_exec($ch);
        //print_r($response);die;
        curl_close($ch);
        // echo "<pre>";
        // print_r($response);
        // echo "<br>";
        DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 SnapIt Log curl_call=" . $response, 'created_at' => date('Y-m-d H:i:s')]);
        return $response;
    }

}
