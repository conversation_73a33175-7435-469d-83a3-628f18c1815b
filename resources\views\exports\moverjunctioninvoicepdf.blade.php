<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
    <title>PrimeMarketing</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
</head>
<body>
<table style="width: 100%; max-width: 100%; margin: 0 auto; padding: 0; vertical-align: top;" cellpadding="0" cellspacing="0">
    <tr>
        <td style="background: #ce0941; font-family: 'Open Sans', sans-serif; font-size: 20px; line-height: 24px; height: 50px; vertical-align: middle; text-align: center;  color: #FFFFFF; font-weight: normal;">Invoice</td>
    </tr>
    <tr>
        <td height="20"></td>
    </tr>
    <tr>
        <td style="text-align: right"><img src="https://mover.primemarketing.us/dist/img/email_setup/logo.png"></td>
    </tr>
    <tr>
        <td height="20"></td>
    </tr>
    <tr>
        <td style="vertical-align: top;">
            <table style="width: 100%; margin: 0 auto; padding: 0; vertical-align: top;" cellpadding="0" cellspacing="0">
                <tr>
                    <td>
                        <table>
                            <tr>
                                <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 20px; padding: 5px 0 5px 0;  color: #000000; border-bottom: #b7b7b7 1px solid; font-weight: bold;">Prime Marketing LLC</td>
                            </tr>
                            <tr>
                                <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 20px; padding: 5px 0 5px 0;  color: #000000; font-weight: normal;">401, Ryland St. Ste 200 A, Reno, NV 89502</td>
                            </tr>
                            <tr>
                                <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 20px; padding: 5px 0 5px 0;  color: #000000; font-weight: normal;">Phone : <a href="tel:+***********" style="color: #000000; text-decoration: none;">+1**************</a></td>
                            </tr>
                            <tr>
                                <td height="10"></td>
                            </tr>
                            <tr>
                                <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 20px; padding: 5px 0 5px 0;  color: #000000;  border-bottom: #b7b7b7 1px solid;  font-weight: bold;">BILL TO</td>
                            </tr>
                            <tr>
                                <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 20px; padding: 5px 0 5px 0;  color: #000000; font-weight: normal;">Mover Junction</td>
                            </tr>
                            <tr>
                                <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 20px; padding: 5px 0 5px 0;  color: #000000; font-weight: normal;">Email : <EMAIL></td>
                            </tr>
                        </table>
                    </td>
                    <td width="40"></td>
                    <td style="vertical-align: top;">
                        <table style="width: 100%; margin: 0 auto; padding: 0; vertical-align: top;" cellpadding="0" cellspacing="0">
                            <tr>
                                <td style="text-align: right; font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 20px; padding: 5px 0 5px 0;  color: #000000; font-weight: normal;">Invoice Date : {{ date('m') }}/01/{{ date('Y') }}</td>
                            </tr>
                            <tr>
                                <td style="text-align: right; font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 20px; padding: 5px 0 5px 0;  color: #000000; font-weight: normal;">Invoice : # {{ sprintf("%02d", $leadDetail['month']) }}{{ date('Y', strtotime(date('Y') . " last month")) }}10</td>
                            </tr>
                            <tr>
                                <td></td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <tr>
        <td height="20"></td>
    </tr>
    <tr>
        <td>
            <table style="width: 100%; margin: 0 auto; padding: 0; vertical-align: top; border: #eeeced 1px solid;" cellpadding="0" cellspacing="0">
                <tr>
                    <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; background: #ce0941; line-height: 24px; border-left: #eeeced 1px solid; border-bottom: #eeeced 1px solid; padding:10px; color: #ffffff; font-weight: normal;">DESCRIPTION</td>
                    <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; background: #ce0941; line-height: 24px; border-left: #eeeced 1px solid; border-bottom: #eeeced 1px solid; padding:10px; color: #ffffff; font-weight: normal;">QTY</td>
                    {{--<td style="font-family: 'Open Sans', sans-serif; font-size: 16px; background: #ce0941; line-height: 24px; border-left: #eeeced 1px solid; border-bottom: #eeeced 1px solid; padding:10px; color: #ffffff; font-weight: normal;">UNIT PRICE</td>--}}
                    <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; background: #ce0941; line-height: 24px; border-left: #eeeced 1px solid; border-bottom: #eeeced 1px solid; padding:10px; color: #ffffff; font-weight: normal; text-align: right;">TOTAL</td>
                </tr>
                <tr>
                    <?php $previousMonthStartDate = date('Y', strtotime(date('Y') . " last month")) . '-' . sprintf("%02d", $leadDetail['month']) . '-01'; ?>
                    <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px; border-left: #eeeced 1px solid; border-bottom: #eeeced 1px solid; padding:10px; color: #000000; font-weight: normal; text-align: left;">Total Leads from {{ sprintf("%02d", $leadDetail['month']) }}/16/{{ date('Y', strtotime(date('Y') . " last month")) }} to 07/31/2025{{--{{ date("m/t/Y", strtotime($previousMonthStartDate)) }}--}}</td>
                    <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px; border-left: #eeeced 1px solid; border-bottom: #eeeced 1px solid; padding:10px; color: #000000; font-weight: normal; text-align: left;">{{ $leadDetail['total_leads'] }}</td>
                    {{--<td style="font-family: 'Open Sans', sans-serif; font-size:16px; line-height: 24px; border-left: #eeeced 1px solid; border-bottom: #eeeced 1px solid; padding:10px; color: #000000; font-weight: normal; text-align: left;">18</td>--}}
                    <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px; border-left: #eeeced 1px solid; border-bottom: #eeeced 1px solid; padding:10px; color: #000000; font-weight: normal; text-align: right;">{{ @number_format($leadDetail['total_amount'], 2) }}</td>
                </tr>
                {{--<tr>
                    <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px; border-left: #eeeced 1px solid; border-bottom: #eeeced 1px solid; padding:10px; color: #000000; font-weight: normal; text-align: left;">Total Calls from {{ sprintf("%02d", $leadDetail['month']) }}/01/{{ date('Y', strtotime(date('Y') . " last month")) }} to {{ date("m/t/Y", strtotime($previousMonthStartDate)) }}</td>
                    <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px; border-left: #eeeced 1px solid; border-bottom: #eeeced 1px solid; padding:10px; color: #000000; font-weight: normal; text-align: left;">12</td>
                    --}}{{--<td style="font-family: 'Open Sans', sans-serif; font-size:16px; line-height: 24px; border-left: #eeeced 1px solid; border-bottom: #eeeced 1px solid; padding:10px; color: #000000; font-weight: normal; text-align: left;">18</td>--}}{{--
                    <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px; border-left: #eeeced 1px solid; border-bottom: #eeeced 1px solid; padding:10px; color: #000000; font-weight: normal; text-align: right;">{{ @number_format(420, 2) }}</td>
                </tr>--}}
                {{--<tr>
                    <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px; border-left: #eeeced; border-bottom: #eeeced 1px solid; padding:10px;color: #000000; font-weight: normal;">10% credit for bad lead</td>
                    <td style="padding:10px; border-left: #eeeced 1px solid; border-bottom: #eeeced 1px solid;"></td>
                    --}}{{--<td style="padding:10px; border-left: #eeeced 1px solid; border-bottom: #eeeced 1px solid;"></td>--}}{{--
                    <td style="padding:10px; border-left: #eeeced 1px solid; border-bottom: #eeeced 1px solid; text-align:right;">-{{ @number_format(($leadDetail['total_amount']*10/100), 2) }}</td>
                </tr>--}}
                <tr>
                    <td colspan="2" style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px; border-left: #eeeced 1px solid; border-bottom: #eeeced 1px solid; padding:10px; color: #000000; font-weight: normal; background: #eeeced; text-align: right;">Balance Due</td>
                    <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px; border-left: #eeeced 1px solid; border-bottom: #eeeced 1px solid; padding:10px; color: #ce0941; font-weight: bold; text-align: right; background: #eeeced;">${{ @number_format($leadDetail['total_amount'], 2) }}/ -</td>
                </tr>
            </table>
        </td>
    </tr>
    <tr>
        <td height="20"></td>
    </tr>
    <tr>
        <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px;  color: #000000; font-weight: normal;">Electronic Payment Instructions</td>
    </tr>
    <tr>
        <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px;  color: #000000; font-weight: normal;"><strong>Bank</strong> : Wells Fargo</td>
    </tr>
    <tr>
        <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px;  color: #000000; font-weight: normal;"><strong>ABA ACH Routing</strong> : *********</td>
    </tr>
    <tr>
        <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px;  color: #000000; font-weight: normal;"><strong>ABA Wire Routing</strong> : *********</td>
    </tr>
    <tr>
        <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px;  color: #000000; font-weight: normal;"><strong>Account Number</strong> : **********</td>
    </tr>
    <tr>
        <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px;  color: #000000; font-weight: normal;"><strong>Beneficiary Name</strong> : Prime Marketing LLC</td>
    </tr>
    <tr>
        <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px;  color: #000000; font-weight: normal;"><strong>Beneficiary Address</strong> : 13005 W Llano Dr, Litchfield Park AZ, 85340-5108</td>
    </tr>
    <tr>
        <td height="10"></td>
    </tr>
    <tr>
        <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px;  color: #000000; font-weight: normal;">If you have any questions, please contact</td>
    </tr>
    <tr>
        <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px;  color: #000000; font-weight: normal;">Prime Marketing Sales Team</td>
    </tr>
    <tr>
        <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px;  color: #000000; font-weight: bold;">Phone : <a href="tel:+***********" style="color: #000000; text-decoration: none;">+1**************</a></td>
    </tr>
    <tr>
        <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px;  color: #000000; font-weight: normal;"><strong>Email</strong> : <EMAIL></td>
    </tr>
    <tr>
        <td style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px;  color: #000000; font-weight: bold;">Thank You For Your Business!</td>
    </tr>
    <tr>
        <td height="40"></td>
    </tr>
    <tr>
        <td height="40" style="font-family: 'Open Sans', sans-serif; font-size: 16px; line-height: 24px; color: #fff; font-weight: normal; background: #000000; text-align: center;">© {{ date('Y') }} - Prime Marketing. All rights reserved. </td>
    </tr>
</table>
</body>
</html>