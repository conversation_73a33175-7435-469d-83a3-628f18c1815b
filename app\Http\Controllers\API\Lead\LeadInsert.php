<?php

namespace App\Http\Controllers\API\Lead;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Leadroute\LeadRouteController;
use App\Http\Controllers\LeadCallBuffer\CallsbufferCronController;
use Illuminate\Http\Request;
use App\Helpers\CommonFunctions;
use App\Http\Controllers\PlivoSms\ConfirmationSms;
use App\Models\Lead\Lead; //Added By BK On 08/12/2022 For lead table
use App\Models\Lead\LeadMoving; //Added By BK On 12/12/2022 For lead_moving table
use App\Models\Lead\LeadJunkRemoval; //Added By BK On 12/12/2022 For lead_junk_removal table
use App\Models\Lead\LeadHeavyLifting; //Added By BK On 12/12/2022 For lead_heavy_lifting table
use App\Models\Lead\LeadCarTransport; //Added By BK On 12/12/2022 For lead_car_transport table
use App\Models\Lead\LeadRequestLog; //Added By BK On 08/12/2022 For lead_request_log table
use App\Models\Lead\LeadLandingPage; //Added By BK On 08/12/2022 For lead_landing_page table
use App\Models\Master\MstZipcode; //Added By BK On 08/12/2022 For mst_zipcode table
use App\Models\Master\MstLeadSource; //Added By BK On 08/12/2022 For mst_lead_source table
use App\Models\Master\MstAPIToken; //Added By BK On 09/12/2022 For mst_api_token table
use App\Models\Lead\Logs;
use App\Models\Lead\LeadIntent;
use Exception;
use DB;
use Log;
use DateTime;
use App\Models\Lead\LeadActivity;
use App\Models\Lead\LeadInvalid; //Added By SS On 08/02/2023 For lead_invalid table
use App\Models\Master\MstMoveSize; //Added By SS On 13/03/2023 For mst_move_size table
use App\Models\Master\MstTimeZone;
use App\Models\Lead\LeadJunkRemovalSubType;
use App\Http\Controllers\Chat\WhatsAppChatController;
use App\Models\Master\MstCallConfig;
use App\Http\Controllers\PlivoSms\AfterHourSmsController;
use App\Models\Lead\LeadFollowUp;
use App\Models\Lead\LeadCallBuffer;
use App\Models\Campaign\Campaign;
use App\Models\Business\Business;
use App\Models\Lead\LeadRouting;
use App\Models\Outbound\GeneralNumberConfig;

class LeadInsert extends Controller
{

    public function InsertLead($postData = "")
    {
        $status = "Failure";
        $leadId = $junkSubTypeId = $heavyLiftingTypeId = $carTypeId = $campaignId = $customerIntent = $adGroupId = $adId = $isWhatsApp = $isRoute = $callItNumber = 0;
        $error = $ip = $fromData = $toData = $source = $utmCampaign = $utmMedium = $adGroup = $keyword = $device = $gclid = $landingPage = $ppCampaignId = "";
        $isDuplicate = $isVerified = $isDncCall = $isDncSMS = $isDncEmail = $isRequiredAssistence = $isOperational = 'no';
        $sourceId = 19;
        $matchCompany = $whatsAppArray = [];
        try {
            $elements = json_decode(file_get_contents('php://input'), true);
            $request = file_get_contents('php://input');
            $phone1 = 0;
            if (empty($elements)) {
                $phone1 = 1;
                $elements = json_decode($postData, true);
                $requested_json = json_encode($elements);
            } else {
                $requested_json = $request;
            }

            if (!empty($postData)) {
                $ppData = json_decode($postData, true);
                if (isset($ppData['is_ping_post']) && $ppData['is_ping_post'] > 0) {
                    $elements = json_decode($postData, true);
                }
            }

            if (empty($elements['origin'])) {
                $requested_json = $requested_json == "null" ? '{}' : $requested_json;
                throw new Exception("Origin required");
            }
            // echo "<pre>test="; print_r($elements ); die;
            //for lead_source_id get from mst_lead_source table
            if (!empty($elements['origin'])) {
                $sourceName = MstLeadSource::read($elements['origin']);
                if ($sourceName == false) {
                    //insert lead origin in mst_lead_source table
                    $sourceId = MstLeadSource::create(['lead_source_name' => $elements['origin']])->lead_source_id;
                } else {
                    $sourceId = $sourceName->lead_source_id;
                }
            }


            //get all API header data pass in header
            //$header = apache_request_headers();
            //$checkToken = MstAPIToken::checkToken($source, $elements['token']);
            $checkToken = DB::select("SELECT api_token_id, SUBSTR(source, 1) AS source, token FROM mst_api_token WHERE token = '".$elements['token']."'");
            if (empty($checkToken)) {
                throw new Exception("Unauthorized Token");
            }

            //lead_category 1-Moving, 2-Junk Removal, 3-Heavy Lifting, 4-Car Transportation
            $categoryId = 1;
            if (!empty($elements['lead_category'])) {
                $categoryId = $elements['lead_category'];
            }

            //phone filter validation
            $phone = preg_replace("/\D/", "", trim($elements['phone']));
            $invalid = strlen($phone) != 10  || preg_match('/^1/', $phone) || preg_match('/^.11/', $phone) || preg_match('/^...1/', $phone) || preg_match('/^....11/', $phone) || preg_match('/^.9/', $phone);
            if ($invalid == true) {
                $phone = "";
            }

            //email filter validation
            $elements['email'] = preg_replace('/\s+/', '', $elements['email']);

            //move_date
            $moveDate = LeadMoving::getMovedate($elements['move_date']);
            $date1 = new DateTime();
            $date2 = new DateTime($moveDate);

            //initial validations
            if (empty($elements['name'])) {
                throw new Exception("Name required");
            } elseif (preg_match("/([%\$#\*]+)/", $elements['name']) || preg_match('~[0-9]+~', $elements['name'])) {
                throw new Exception("Invalid Name format");
            } elseif (empty($elements['email'])) {
                throw new Exception("Email required");
            } elseif (filter_var($elements['email'], FILTER_VALIDATE_EMAIL) === false) {
                throw new Exception("Invalid email format");
            } elseif (empty($elements['from_zip'])) {
                throw new Exception("From zip required");
            } elseif (strlen($elements['from_zip']) < 5) {
                throw new Exception("From zip must be 5 digit only");
            } elseif (empty($elements['to_zip']) && $categoryId != 2) {
                throw new Exception("To zip required");
            } elseif (strlen($elements['to_zip']) < 5 && $categoryId != 2) {
                throw new Exception("To zip must be 5 digit only");
            } elseif (empty($elements['phone'])) {
                throw new Exception("Phone required");
            } elseif (filter_var($phone, FILTER_VALIDATE_REGEXP, array("options" => ["regexp" => "/[1-9]{1}[0-9]{9}/"])) === false && !isset($elements['fbrouteLead'])) {
                throw new Exception("Invalid phone format");
            } elseif (empty($elements['move_date'])) {
                throw new Exception("Move date error");
            } elseif (LeadMoving::checkDateFormat($elements['move_date']) == false) {
                throw new Exception("Invalid Move date format");
            } elseif ($date2 < $date1) {
                throw new Exception("Move date should be greater than current date");
            }

            //from_zip
            $fromZipCode = $elements['from_zip'];
            //to_zip
            $toZipCode = $elements['to_zip'];
            //get zipcode data from mst_zipcode table pass from_zip & to_zip both argument mendatory
            $zipcodeData = MstZipcode::read($fromZipCode, $toZipCode);
            if (strcmp($fromZipCode, $toZipCode) && $toZipCode) {
                if (isset($zipcodeData[0]) && $fromZipCode == $zipcodeData[0]['zipcode']) {
                    $fromData = $zipcodeData[0];
                } elseif (isset($zipcodeData[1])) {
                    $fromData = $zipcodeData[1];
                }
                if (isset($zipcodeData[0]) && $toZipCode == $zipcodeData[0]['zipcode']) {
                    $toData = $zipcodeData[0];
                } elseif (isset($zipcodeData[1])) {
                    $toData = $zipcodeData[1];
                }
            } else {
                if (isset($zipcodeData[0])) {
                    $fromData = $toData = $zipcodeData[0];
                }
            }

            //initial validations for from_zip & to_zip
            if ($fromData == null) {
                throw new Exception("Invalid from_zip");
            } elseif ($toData == null && $categoryId != 2) {
                throw new Exception("Invalid to_zip");
            }

            //name
            $name = trim(preg_replace('/\s\s+/', ' ', str_replace("\n", " ", $elements['name'])));

            //email
            $email = strtolower($elements['email']);

            //move_size filter validation
            $moveSize = preg_replace("/\D/", "", trim($elements['move_size']));
            $callBufferEntry = $customerIntent = 1;
            //from areacode, city, state data get from mst_zipcode table
            if(!empty($fromData)){
                if(count($fromData) > 0 ){
                    $fromAreaCode = $fromData['areacode'];
                    $fromCity = strtoupper($fromData['city']);
                    $fromState = strtoupper($fromData['state']);
                }
            }
            //to areacode, city, state data get from mst_zipcode table
            if ($categoryId != 2) {
                if(!empty($toData)){
                    if(count($toData) > 0 ){
                        $toAreaCode = $toData['areacode'];
                        $toCity = strtoupper($toData['city']);
                        $toState = strtoupper($toData['state']);
                    }
                }
            }
            //check isDuplicate Leads
            $pos1 = strpos($elements['origin'], 'VM');
            $pos2 = strpos($elements['origin'], 'QTM');
            $pos3 = strpos($elements['origin'], 'IS');
            $pos4 = strpos($elements['origin'], 'NEX');
            if ($pos1 !== false) {
                $sourceName = 'VM';
                $callItNumber = GeneralNumberConfig::getDialNumberByAreacode($phone)['vm_areacode_outbound'];
            } else if ($pos2 !== false) {
                $sourceName = 'QTM';
                $callItNumber = GeneralNumberConfig::getDialNumberByAreacode($phone)['qtm_areacode_outbound'];
            } else if ($pos3 !== false) {
                $sourceName = 'IS';
                $callItNumber = GeneralNumberConfig::getDialNumberByAreacode($phone)['ism_areacode_outbound'];
            }

            $ip = $elements['IP'];

            //calculate distance
            $distance = 0;
            if (!empty($fromData['lat']) && !empty($toData['lat']) && !empty($fromData['long']) && !empty($toData['long'])) {
                $distance = LeadMoving::calculateDistance($fromData['lat'], $toData['lat'], $fromData['long'], $toData['long']);
            }
            //check lead distance type (ex. Local, Long)
            $leadDistanceType = 'local';
            if ($categoryId != "2") {
                $leadDistanceType = LeadMoving::checkLeadDistanceType($fromState, $toState);
            }
            if (!empty($elements['is_required_assistence']) && $elements['is_required_assistence'] == '1') {
                $isRequiredAssistence = 'yes';
            }

            if (!empty($elements['is_operational']) && $elements['is_operational'] == '1') {
                $isOperational = 'yes';
            }

            //insert lead request log in lead_request_log table
            $leadRequestLogId = LeadRequestLog::create([
                'lead_request' => json_encode($elements)
            ])->lead_request_log_id;

            $leadRouteCron = new LeadRouteController();
            if ($elements['origin'] == 'VM(V)' || $elements['origin'] == 'QTM(V)' || $elements['origin'] == 'IS(V)') {
                $leadDetails = Lead::where(function ($query) use ($email, $phone){
                    $query->where('lead.email', $email)->orWhere('lead.phone', $phone);
                })->where('is_verified', 'no')->first();

                if ($leadDetails) {
                    Lead::where('lead_id', $leadDetails->lead_id)->update([
                        'is_verified' => 'yes'
                    ]);

                    $endDecObj = new CommonFunctions;
                    $leadId = $endDecObj->customeEncryption($leadDetails->lead_id);
                    $matchCompany = $leadRouteCron->reRouteLead($leadId, 3, 0, 1);

                    $status = "Success";
                    $response = array(
                        'status' => $status,
                        'lead_id' => $leadId,
                        'error' => $error,
                        'match' => $matchCompany,
                        'callit_number' => substr($callItNumber, -10)
                    );

                    //insert lead response log in lead_request_log table
                    LeadRequestLog::where('lead_request_log_id', $leadRequestLogId)->update([
                        'lead_response' => json_encode($response)
                    ]);

                    return json_encode($response);
                }
            }

            if (Lead::isDuplicate($email, $sourceId, $elements['is_verified'], $phone, $categoryId, $sourceName) /*&& $elements['origin'] != 'VM(V)' && $elements['origin'] != 'QTM(V)' && $elements['origin'] != 'IS(V)'*/) {
                throw new Exception("Lead recieved, but was not recorded since it was a duplicate");
            }

            //check isEmailDuplicate Leads
            if (Lead::isEmailDuplicate($sourceId, $email, $categoryId, $sourceName) /*&& $elements['origin'] != 'VM(V)' && $elements['origin'] != 'QTM(V)' && $elements['origin'] != 'IS(V)'*/) {
                $isDuplicate = 'yes';
            }
            //check isPhoneDuplicate Leads
            if (Lead::isPhoneDuplicate($sourceId, $phone, $categoryId, $sourceName) /*&& $elements['origin'] != 'VM(V)' && $elements['origin'] != 'QTM(V)' && $elements['origin'] != 'IS(V)'*/) {
                $isDuplicate = 'yes';
            }

            if (!empty($elements['is_verified']) && $elements['is_verified'] == '1') {
                $isVerified = 'yes';
            }

            if($categoryId == 1){
                $movesizeDetails = MstMoveSize::where(['move_size_id'=> $moveSize])->first();
                if(!$movesizeDetails){
                    throw new Exception("Move size must be beetween 1 to 6");
                }
            }

            $timezoneId = MstTimeZone::read(strtoupper($fromData['state']));

            $generatedAt = date("Y-m-d H:i:s");
            if (isset($elements['generated_at'])) {
                $generatedAt = $elements['generated_at'];
            }
            $book_type_id = 1;
            if (isset($elements['book_type_id'])) {
                $book_type_id = $elements['book_type_id'];
            }

            //insert lead data in lead table
            $leadId = Lead::create([
                'lead_source_id' => $sourceId,
                'lead_category_id' => $categoryId,
                'timezone_id' => $timezoneId,
                'lead_type' => 'normal',
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'is_duplicate' => $isDuplicate,
                'is_verified' => $isVerified,
                'is_dnc_call' => $isDncCall,
                'is_dnc_sms' => $isDncSMS,
                'is_dnc_email' => $isDncEmail,
                'lead_generated_at' => $generatedAt,
                'lead_request_log_id' => $leadRequestLogId,
                'book_type_id' => $book_type_id,
                'lp_lead_id' => $elements['lead_id'] ?? 0,
                'created_at' => date("Y-m-d H:i:s")
            ])->lead_id;

            $leadactiviyobj = new LeadActivity();
            $leadactiviyobj->createActivity($leadId, 1);

            //common field array
            $commonFieldArray = [];
            $commonFieldArray['lead_id'] = $leadId;
            $commonFieldArray['from_zipcode'] = $fromZipCode;
            $commonFieldArray['from_areacode'] = $fromAreaCode;
            $commonFieldArray['from_city'] = $fromCity;
            $commonFieldArray['from_state'] = $fromState;
            $commonFieldArray['created_at'] = date("Y-m-d H:i:s");

            if ($categoryId != "2") {
                $commonFieldArray['to_zipcode'] = $toZipCode;
                $commonFieldArray['to_areacode'] = $toAreaCode;
                $commonFieldArray['to_city'] = $toCity;
                $commonFieldArray['to_state'] = $toState;
                $commonFieldArray['move_type'] = $leadDistanceType;
                $commonFieldArray['distance'] = $distance;
            }

            if ($categoryId == "3" || $categoryId == "4") {
                $commonFieldArray['is_required_assistence'] = $isRequiredAssistence;
                $commonFieldArray['is_operational'] = $isOperational;
            }

            //Moving
            if ($categoryId == 1) {
                $propertyType = (!empty($elements['property_type'])) ? trim($elements['property_type']) : 'residential';

                //push in common field array
                $commonFieldArray['move_size_id'] = $moveSize;
                $commonFieldArray['move_date'] = $moveDate;
                $commonFieldArray['property_type'] = $propertyType;

                //insert record in lead_moving table
                LeadMoving::create($commonFieldArray)->lead_moving_id;
                //Junk Removal
            } else if ($categoryId == 2) {
                $junkTypeId = (!empty($elements['junk_type'])) ? trim($elements['junk_type']) : '';
                $junkSubTypeId = (!empty($elements['junk_sub_type'])) ? $elements['junk_sub_type'] : null;
                $ownerType = (!empty($elements['owner_type'])) ? trim($elements['owner_type']) : 'owner';
                $description = (!empty($elements['description'])) ? trim($elements['description']) : '';
                $propertyType = (!empty($elements['property_type'])) ? trim($elements['property_type']) : 'residential';

                //push in common field array
                $commonFieldArray['junk_remove_date'] = $moveDate;
                $commonFieldArray['junk_type_id'] = $junkTypeId;
                $commonFieldArray['owner_type'] = $ownerType;
                $commonFieldArray['description'] = $description;
                $commonFieldArray['property_type'] = $propertyType;

                //insert record in lead_junk_removal table
                LeadJunkRemoval::create($commonFieldArray)->lead_junk_removal_id;
                //insert record in lead_junk_removal_sub_type table
                if( $junkSubTypeId != null ){
                    // echo "Not null";
                    $junkSubTypeIdArr = explode (",", $junkSubTypeId);
                    $junksubtypeArr = [];
                    foreach ($junkSubTypeIdArr as $key => $value) {
                        $junksubtypeArr[] = [
                            "lead_id" => $leadId,
                            "junk_sub_type_id" => $value,
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                    }
                    LeadJunkRemovalSubType::insert($junksubtypeArr);
                }
                //Heavy Lifting
            } else if ($categoryId == 3) {
                $heavyLiftingTypeId = (!empty($elements['heavy_lifting_type'])) ? trim($elements['heavy_lifting_type']) : '';
                //push in common field array
                $commonFieldArray['lift_date'] = $moveDate;
                $commonFieldArray['heavy_lifting_type_id'] = $heavyLiftingTypeId;

                //insert record in lead_heavy_lifting table
                LeadHeavyLifting::create($commonFieldArray)->lead_heavy_lifting_id;
                //Car Transportation
            } else if ($categoryId == 4) {
                $carTypeId = (!empty($elements['car_type'])) ? trim($elements['car_type']) : '';
                $transportType = ($elements['transport_type'] > 0) ? 'open' : 'enclosed';
                $vehicleCondition = (isset($elements['vehicle_condition']) && $elements['vehicle_condition'] > 0) ? 'running' : 'not running';
                //push in common field array
                $commonFieldArray['move_date'] = $moveDate;
                $commonFieldArray['car_type_id'] = $carTypeId;
                $commonFieldArray['transport_type'] = $transportType;
                $commonFieldArray['vehicle_condition'] = $vehicleCondition;

                //insert record in lead_car_transport table
                LeadCarTransport::create($commonFieldArray)->lead_car_transport_id;
            }
            $utmCampaign = (!empty($elements['utm_campaign'])) ? preg_replace('/-+/', '-', trim($elements['utm_campaign'])) : ""; //utmCampaign
            $utmMedium = (!empty($elements['utm_medium'])) ? trim($elements['utm_medium']) : ""; //utmMedium
            $utmCampaignId = (!empty($elements['camp_id'])) ? trim($elements['camp_id']) : 0; //$campaignId
            $adGroupId = 0;
            if (!empty($elements['ad_grp_id'])) {
                $adGroupId = trim($elements['ad_grp_id']);
            } else if (!empty($elements['ad_group_id'])) {
                $adGroupId = trim($elements['ad_group_id']);
            }
            //$adGroupId = (!empty($elements['ad_group_id'])) ? trim($elements['ad_group_id']) : 0; //adGroupId
            $adGroup = "";
            if (!empty($elements['ad_grp'])) {
                $adGroup = trim($elements['ad_grp']);
            } else if (!empty($elements['ad_group'])) {
                $adGroup = trim($elements['ad_group']);
            }
            //$adGroup = (!empty($elements['ad_group'])) ? trim($elements['ad_group']) : ""; //adGroup
            $adId = (!empty($elements['ad_id'])) ? trim($elements['ad_id']) : ""; //adId
            $keyword = (!empty($elements['keyword'])) ? trim($elements['keyword']) : ""; //keyword
            $device = (!empty($elements['device'])) ? trim($elements['device']) : ""; //device
            $gclid = (!empty($elements['GCLID'])) ? trim($elements['GCLID']) : ""; //gclid
            $gclidType = (!empty($elements['gclid_type'])) ? trim($elements['gclid_type']) : "0"; //gclid
            $landingPage = (!empty($elements['Landing_Page'])) ? trim($elements['Landing_Page']) : ""; //landingPage
            $trustedFormCertId = (!empty($elements['xxTrustedFormToken'])) ? trim($elements['xxTrustedFormToken']) : ""; //xxTrustedFormToken

            //campaign_id
            if (!empty($elements['campaign_id'])) {
                $campaignId = $elements['campaign_id'];
            }
            if (!empty($elements['linkup_campaign_id'])) {
                $campaignId = $elements['linkup_campaign_id'];
            }
            //customer_intent
            if (isset($elements['customer_intent'])) {
                $customerIntent = trim((string) $elements['customer_intent']);
            }
            //pp_campaign_id
            $ppCampaignId = $elements['pp_campaign_id'] ?? '';

            //insert lead landing page data in lead_landing_page table
            $leadLandingPageId = LeadLandingPage::create([
                'lead_id' => $leadId,
                'utm_campaign' => $utmCampaign,
                'utm_medium' => $utmMedium,
                'landing_campaign_id' => $utmCampaignId,
                'ad_group_id' => $adGroupId,
                'ad_group' => $adGroup,
                'ad_id' => $adId,
                'search_keyword' => $keyword,
                'device' => $device,
                'gclid' => $gclid,
                'gclid_type' => $gclidType,
                'landing_page' => $landingPage,
                'lead_source_id' => $sourceId,
                'ip' => $ip,
                'trusted_form_cert_id' => $trustedFormCertId,
                'created_at' => $elements['generated_at']
            ])->lead_landing_page_id;

            if (isset($elements['customer_intent'])) {
                LeadIntent::create([
                    'lead_id' => $leadId,
                    'campaign_id' => $campaignId,
                    'intent' => $customerIntent,
                    'created_at' => date("Y-m-d H:i:s")
                ]);
            }

            //insert whatsApp related data in whatsApp table
            if (isset($elements['is_whatsapp']) && $elements['is_whatsapp'] > 0) {
                $isWhatsApp = 1;
            }
            if ($isWhatsApp > 0 && isset($elements['whatsapp_number']) && $elements['whatsapp_number']!="") {
                $whatsAppArray = [
                    'source_name' => 'vm',
                    'alias' => ucwords($elements['name']),
                    'whatsapp_number' => $elements['whatsapp_number'],
                ];
                $whatsAppChatObj = new WhatsAppChatController();
                $whatsAppChatObj->whatsAppChatInsertApi($whatsAppArray);
            }

            //Added By HJ On 14-04-2022 For FB Lead Route Start
            if ((isset($elements['fbrouteLead']) && $elements['fbrouteLead'] > 0 ) || strtolower($elements['origin']) == "test" || (isset($elements['isLeadSeeded']) && $elements['isLeadSeeded'] > 0 ))  {
                $customerIntent = $callBufferEntry = 0;
            }

            if ($pos4 === false) {
                $confirmationsms = new ConfirmationSms();
                $confirmationsms->LeadConfirmationSms($leadId, 1);
            }

            //Added By HJ On 14-04-2022 For FB Lead Route ENd
            $weekDayNumber = date('N');
            $currentDateTime = date('Y-m-d H:i:s');
            $startDateTime = date("Y-m-d") . ' 08:00:00';
            $endDateTime = date("Y-m-d") . ' 22:00:00';
            if ($customerIntent > 0) {
                $this->LeadRoutelog($leadId, 'leadStore LeadId = ' . $leadId . ' , coverageType = ' . $leadDistanceType . ', customer_intent = 1 and isVerified==' . $isVerified, 1);
                $endDecObj = new CommonFunctions;
                $leadIdEncrypt = $endDecObj->customeEncryption($leadId);
                $configDetail = MstCallConfig::whereIn('call_config', ['vmlongroutelater', 'qtmlongroutelater', 'islongroutelater', 'vmlocalroutelater', 'qtmlocalroutelater', 'islocalroutelater'])->get(['status'])->toArray();
                if($ppCampaignId != '') {
                    $leadRouteCron->reRouteLead($leadIdEncrypt, 4, $ppCampaignId);
                    $isRoute = 1;
                } else {
                    if ((strtotime($currentDateTime) >= strtotime($startDateTime) && strtotime($currentDateTime) < strtotime($endDateTime) && $pos1 !== false && $weekDayNumber < 6 && strtolower($configDetail[0]['status']) == "active" && $leadDistanceType == "long")
                        || (strtotime($currentDateTime) >= strtotime($startDateTime) && strtotime($currentDateTime) < strtotime($endDateTime) && $pos2 !== false && $weekDayNumber < 6 && strtolower($configDetail[1]['status']) == "active" && $leadDistanceType == "long")
                        || (strtotime($currentDateTime) >= strtotime($startDateTime) && strtotime($currentDateTime) < strtotime($endDateTime) && $pos3 !== false && $weekDayNumber < 6 && strtolower($configDetail[2]['status']) == "active" && $leadDistanceType == "long")
                        || (strtotime($currentDateTime) >= strtotime($startDateTime) && strtotime($currentDateTime) < strtotime($endDateTime) && $pos1 !== false && $weekDayNumber < 6 && strtolower($configDetail[3]['status']) == "active" && $leadDistanceType == "local")
                        || (strtotime($currentDateTime) >= strtotime($startDateTime) && strtotime($currentDateTime) < strtotime($endDateTime) && $pos2 !== false && $weekDayNumber < 6 && strtolower($configDetail[4]['status']) == "active" && $leadDistanceType == "local")
                        || (strtotime($currentDateTime) >= strtotime($startDateTime) && strtotime($currentDateTime) < strtotime($endDateTime) && $pos3 !== false && $weekDayNumber < 6 && strtolower($configDetail[5]['status']) == "active" && $leadDistanceType == "local")) {
                    } else {
                        if ($isVerified == 'yes' || $isVerified == 2) {
                            $this->LeadRoutelog($leadId, 'leadStore LeadId = ' . $leadId . ' , call reRouteLead==' . $isVerified, 2);
                            $matchCompany = $leadRouteCron->reRouteLead($leadIdEncrypt, 1, $campaignId);
                        } else {
                            $hoursCheck = date('G');
                            $this->LeadRoutelog($leadId, 'leadStore LeadId = ' . $leadId . ' , call check hour ==' . $hoursCheck, 3);
                            //all unverified lead should route at a time condition comment by BK 25/04/2023 discuss with MV
                            /*if ($hoursCheck <= 9 || $hoursCheck >= 20) {*/
                            $this->LeadRoutelog($leadId, 'leadStore LeadId = ' . $leadId . ' , call reRouteLead 2 ==' . $isVerified, 4);
                            $matchCompany = $leadRouteCron->reRouteLead($leadIdEncrypt, 1, $campaignId);
                            /*}*/
                        }
                        $isRoute = 1;
                    }
                }
                //echo $hoursCheck;
                //echo '<pre>';print_r($matchCompany);die;
            }

            $status = "Success";
            //SendJobtoActivecampaign::dispatch($leadId,$leadId)->onQueue('SendJobtoActivecampaign');
            if ($pos4 === false) {
                if ($leadId > 0) {
                    $routeLeadToActiveCampaignEmail = new RoutedleadSendToActivecampaign();
                    $routeLeadToActiveCampaignEmail->leadSendtoactiveCampaign($leadId, $elements['lead_id']);
                }
                //SendJobtoConfirmationsms::dispatch($leadId)->onQueue('SendJobtoConfirmationsms');

                //Spot Routing SMS
                if ($isRoute > 0) {
                    //$afterHourSmsControllerObj = new AfterHourSmsController();
                    //$afterHourSmsControllerObj->afterHourSMSCron($leadId);
                }

                if ($callBufferEntry > 0) {
                    $isVmmaOrigin = 0;
                    date_default_timezone_set('America/New_York');
                    $hoursCheck = date('G');
                    $buffer = new CallsbufferCronController();
                    // $hoursCheck = 11;
                    $this->LeadRoutelog($leadId, 'hoursCheck= ' . $hoursCheck, 5);

                    // Check Nextify Sold or Not : If sold then not add to call buffer 2025-03-27 
                    $stopBufferFollowup = 0;
                    $leadRoutingData = LeadRouting::where('lead_id', $leadId)->where('route_status', 'sold')->get()->toArray();
                    if (count($leadRoutingData) > 0) {
                        $campaignIdFinal = $routedCampIdArr = $routedCampDataArr = $routedBusinessIdArr = $routedBusinessDataArr = array();
                        for ($g = 0; $g < count($leadRoutingData); $g++) {
                            $routedCampIdArr[] = $leadRoutingData[$g]['campaign_id'];
                        }
                        if (count($routedCampIdArr) > 0) {
                            $getCampData = Campaign::whereIn('campaign_id', $routedCampIdArr)->get(['campaign_id', 'business_id'])->toArray();
                            for ($c = 0; $c < count($getCampData); $c++) {
                                /*$routedCampDataArr[$getCampData[$c]['campaign_id']] = $getCampData[$c];*/
                                /*if (!in_array($getCampData[$c]['business_id'], $routedBusinessIdArr)) {*/
                                    $routedBusinessIdArr[] = $getCampData[$c]['business_id'];
                                /*}*/
                            }
                        }
                        if (count($routedBusinessIdArr) > 0) {
                            if(in_array(993, $routedBusinessIdArr)) {  // 993 for prod and 1048 for staging
                                $stopBufferFollowup = 1;
                                $leadactiviyobj1 = new LeadActivity();
                                $followupLeadData = LeadFollowUp::where('lead_id', $leadId)->first();
                                if(isset( $followupLeadData )){
                                    $leadactiviyobj1->createActivity($leadId, 13);
                                }
                                $bufferLeadData = LeadCallBuffer::where('lead_id', $leadId)->first();
                                if(isset( $bufferLeadData )) {
                                    $leadactiviyobj1->createActivity($leadId, 8);
                                }
                                LeadCallBuffer::where('lead_id', $leadId)->delete();
                                LeadFollowUp::where('lead_id', $leadId)->delete();
                            }

                            if(in_array(1117, $routedBusinessIdArr)) {  // 1117 for prod and 1174 for staging
                                $stopBufferFollowup = 1;
                                $leadactiviyobj1 = new LeadActivity();
                                $followupLeadData = LeadFollowUp::where('lead_id', $leadId)->first();
                                if(isset( $followupLeadData )){
                                    $leadactiviyobj1->createActivity($leadId, 13);
                                }
                                $bufferLeadData = LeadCallBuffer::where('lead_id', $leadId)->first();
                                if(isset( $bufferLeadData )) {
                                    $leadactiviyobj1->createActivity($leadId, 8);
                                }
                                LeadCallBuffer::where('lead_id', $leadId)->delete();
                                LeadFollowUp::where('lead_id', $leadId)->delete();
                            }
                        }
                    }
                    // Code End

                    $timeZoneDetail = MstTimeZone::where('timezone_id', $timezoneId)->get()->toArray();
                    date_default_timezone_set($timeZoneDetail[0]['timezone']);
                    $hoursCheck = date('G', time());

                    if($stopBufferFollowup == 0) {
                        if ($hoursCheck >= 8 && $hoursCheck <= 19) {
                            $this->LeadRoutelog($leadId, 'save to buffer localTime= ' . $hoursCheck, 5);
                            $buffer->saveBufferLead($leadId, $isVmmaOrigin);
                        } else {
                            $leadArr = Lead::where('lead_id', $leadId)->find($leadId)->toArray();
                            $this->LeadRoutelog($leadId, 'save to followup leadArr = ' . json_encode($leadArr, true), 5);
                            $buffer->saveFollowupentry($leadArr, 1);
                        }
                    }
                    date_default_timezone_set('America/New_York');
                    /*if ($hoursCheck >= 11 && $hoursCheck < 21) {
                        $this->LeadRoutelog($leadId, 'save to buffer=', 5);
                        $buffer->saveBufferLead($leadId, $isVmmaOrigin);
                    } else {
                        $leadArr = Lead::where('lead_id', $leadId)->find($leadId)->toArray();
                        if ($hoursCheck >= 8 && $hoursCheck < 11) {
                            $this->LeadRoutelog($leadId, 'save to buffer est=', 5);
                            $buffer->saveBufferLead($leadId, $isVmmaOrigin);
                        } else {
                            $this->LeadRoutelog($leadId, 'save to followup leadArr = ' . json_encode($leadArr, true), 5);
                            $buffer->saveFollowupentry($leadArr, 1);
                        }
                    }*/
                } else {
                    $this->LeadRoutelog($leadId, 'FB lead some missing so or Junk Lead = ' . $leadId . ' , callBufferEntry = ' . $callBufferEntry, 5);
                }
            }

            $response = array(
                'status' => $status,
                'lead_id' => $leadId,
                'error' => $error,
                'match' => $matchCompany,
                'callit_number' => substr($callItNumber, -10)
            );

            //insert lead response log in lead_request_log table
            LeadRequestLog::where('lead_request_log_id', $leadRequestLogId)->update([
                'lead_response' => json_encode($response)
            ]);
        } catch (Exception $e) {
            $error = $e->getMessage();
            //insert exception in lead_invalid table - start
            LeadInvalid::create([
                'request' => $requested_json,
                'response' => str_replace(array('\'', '"'), '', $error),
                'lead_source_id' => $sourceId,
                'created_at' => date("Y-m-d H:i:s")
            ]);
            //insert exception in lead_invalid table - end

            $response = array(
                'status' => $status,
                'lead_id' => $leadId,
                'error' => $error,
                'match' => $matchCompany,
                'callit_number' => substr($callItNumber, -10)
            );
        }
        if (isset($elements['is_ping_post']) && $elements['is_ping_post'] > 0) {
            return json_encode($response);
        }
        return json_encode($response);

        //Added By HJ On 14-04-2022 For FB Lead Route Start
        if (!isset($elements['fbrouteLead'])) {
            die();
        }
        //Added By HJ On 14-04-2022 For FB Lead Route End
    }

    public function LeadRoutelog($leadid, $log, $type)
    {
        Logs::create([
            'lead_id' => $leadid,
            'log' => $log,
            'log_srno' => $type,
            'created_at' => date("Y-m-d H:i:s")
        ]);
    }
}
