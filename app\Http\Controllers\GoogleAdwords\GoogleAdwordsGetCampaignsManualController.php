<?php

namespace App\Http\Controllers\GoogleAdwords;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

//use GetOpt\GetOpt;
use Google\Ads\GoogleAds\Examples\Utils\ArgumentNames;
use Google\Ads\GoogleAds\Examples\Utils\ArgumentParser;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClient;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClientBuilder;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsException;
use Google\Ads\GoogleAds\Lib\OAuth2TokenBuilder;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsServerStreamDecorator;
use Google\Ads\GoogleAds\V18\Errors\GoogleAdsError;
use Google\Ads\GoogleAds\V18\Services\GoogleAdsRow;
use Google\Ads\GoogleAds\V18\Services\SearchGoogleAdsStreamRequest;
use Google\ApiCore\ApiException;
use Google\Ads\GoogleAds\V18\Resources\BiddingStrategy;
use Google\Ads\GoogleAds\V18\Enums\BiddingStrategyTypeEnum\BiddingStrategyType;

class GoogleAdwordsGetCampaignsManualController extends Controller
{
    //public static $CUSTOMER_ID;

    public static function main ($originName, $campaignIds)
    {
        //echo phpinfo(); die;
        // .ini file path for MT, PMM
        $filePath = base_path("google_ads_php.ini");
        if ($originName == "VM") {
            $CUSTOMER_ID = 7000201456;
        } else if($originName == "QTM") {
            $CUSTOMER_ID = 3189698923;
        } else if ($originName == "IS") {
            $CUSTOMER_ID = 5101673588;
        } else if ($originName == "PL") {
            $CUSTOMER_ID = 8305774076;
        }

        // Either pass the required parameters for this example on the command line, or insert them
        // into the constants above.
        /*$options = (new ArgumentParser())->parseCommandArguments([
            ArgumentNames::CUSTOMER_ID => GoogleAdwordsGetCampaignsNewController::$CUSTOMER_ID
        ]);*/

        // Generate a refreshable OAuth2 credential for authentication.
        $oAuth2Credential = (new OAuth2TokenBuilder())->fromFile($filePath)->build();

        // Construct a Google Ads client configured from a properties file and the
        // OAuth2 credentials above.
        $googleAdsClient = (new GoogleAdsClientBuilder())
            ->fromFile($filePath)
            ->withOAuth2Credential($oAuth2Credential)
            ->build();

        try {
            $allCampaign = self::getCampaigns(
                $googleAdsClient,
                $CUSTOMER_ID,
                $campaignIds
            );

            return $allCampaign;
        } catch (GoogleAdsException $googleAdsException) {
            printf(
                "Request with ID '%s' has failed.%sGoogle Ads failure details:%s",
                $googleAdsException->getRequestId(),
                PHP_EOL,
                PHP_EOL
            );
            foreach ($googleAdsException->getGoogleAdsFailure()->getErrors() as $error) {
                /** @var GoogleAdsError $error */
                printf(
                    "\t%s: %s%s",
                    $error->getErrorCode()->getErrorCode(),
                    $error->getMessage(),
                    PHP_EOL
                );
            }
            exit(1);
        } catch (ApiException $apiException) {
            printf(
                "ApiException was thrown with message '%s'.%s",
                $apiException->getMessage(),
                PHP_EOL
            );
            exit(1);
        }
    }

    /**
     * Runs the example.
     *
     * @param GoogleAdsClient $googleAdsClient the Google Ads API client
     * @param int $customerId the customer ID
     */
    public static function getCampaigns (GoogleAdsClient $googleAdsClient, $customerId, $campaignIds)
    {
        $googleAdsServiceClient = $googleAdsClient->getGoogleAdsServiceClient();
        $sWhere = "";
        if (count($campaignIds) > 0) {
            $sWhere .= "AND campaign.id IN (" . implode(',', $campaignIds) . ")";
        }
        // Creates a query that retrieves all campaigns.
        $query = 'SELECT campaign.id, campaign.name, campaign.advertising_channel_type, campaign.bidding_strategy, campaign.bidding_strategy_type, campaign.target_cpa.target_cpa_micros, campaign.maximize_conversions.target_cpa_micros, campaign.maximize_conversion_value.target_roas FROM campaign WHERE campaign.status IN (\'ENABLED\') '.$sWhere.' ORDER BY campaign.id';
        // Issues a search stream request.
        /** @var GoogleAdsServerStreamDecorator $stream */
        $stream =
            //$googleAdsServiceClient->searchStream($customerId, $query);
            $googleAdsServiceClient->searchStream(
                SearchGoogleAdsStreamRequest::build($customerId, $query)
            );
        // Iterates over all rows in all messages and prints the requested field values for
        // the campaign in each row.
        $campaignArray = array();
        foreach ($stream->iterateAllElements() as $googleAdsRow) {

            /*$bidStrategyId = explode('/', $googleAdsRow->getCampaign()->getBiddingStrategy());
            if (!empty($bidStrategyId[3])) {
                $query1 = 'SELECT bidding_strategy.id, bidding_strategy.name, bidding_strategy.type, bidding_strategy.target_cpa.target_cpa_micros FROM bidding_strategy WHERE bidding_strategy.id = ' . $bidStrategyId[3];
                //$stream1 = $googleAdsServiceClient->searchStream($customerId, $query1);
                $stream1 = $googleAdsServiceClient->searchStream(
                    SearchGoogleAdsStreamRequest::build($customerId, $query1)
                );
            }*/

            $campaign = (object)array();
            $campaign->campaign_id = $googleAdsRow->getCampaign()->getId();
            $campaign->campaign_name = $googleAdsRow->getCampaign()->getName();
            $campaign->bidding_strategy = $googleAdsRow->getCampaign()->getBiddingStrategy();
            $campaign->advertising_channel_type = GoogleAdwordsGetCampaignsManualController::advertisingChannelTypeName($googleAdsRow->getCampaign()->getAdvertisingChannelType());
            $biddingStrategyType = GoogleAdwordsGetCampaignsManualController::biddingStrategyTypeName($googleAdsRow->getCampaign()->getBiddingStrategyType());
            $campaign->bidding_strategy_type = $biddingStrategyType;
            $targetCpa = 0;
            if ($biddingStrategyType == "TARGET_CPA") {
                if (!empty($googleAdsRow->getCampaign()->getTargetCpa())) {
                    $targetCpa = $googleAdsRow->getCampaign()->getTargetCpa()->getTargetCpaMicros() / 1000000;
                }
            } elseif ($biddingStrategyType == "MAXIMIZE_CONVERSIONS") {
                $maximizeConversions = $googleAdsRow->getCampaign()->getMaximizeConversions();
                if ($maximizeConversions !== null && $maximizeConversions->getTargetCpaMicros() !== null) {
                    $targetCpa = $maximizeConversions->getTargetCpaMicros() / 1000000;
                }
            } elseif ($biddingStrategyType == "MAXIMIZE_CONVERSION_VALUE") {
                $maximizeConversionValue = $googleAdsRow->getCampaign()->getMaximizeConversionValue();
                if ($maximizeConversionValue !== null && $maximizeConversionValue->getTargetRoas() !== null) {
                    $targetRoas = $maximizeConversionValue->getTargetRoas();
                    $targetCpa = $targetRoas * 100;
                }
            }
            $campaign->target_cpa = $targetCpa;
            /*if (!empty($bidStrategyId[3])) {
                foreach ($stream1->iterateAllElements() as $googleAdsRow1) {
                    if (!empty($googleAdsRow1->getBiddingStrategy()->getTargetCpa())) {
                        $campaign->target_cpa = $googleAdsRow1->getBiddingStrategy()->getTargetCpa()->getTargetCpaMicros() / 1000000;
                    } else {
                        $campaign->target_cpa = 0;
                    }
                }
            }*/
            $campaignArray[] = $campaign;
        }
        //echo '<pre>'; print_r($campaignArray); die;
        return $campaignArray;
    }

    public static function biddingStrategyTypeName($key) {
        $array = [
            'UNSPECIFIED',
            'UNKNOWN',
            'ENHANCED_CPC',
            'MANUAL_CPC',
            'MANUAL_CPM',
            'PAGE_ONE_PROMOTED',
            'TARGET_CPA',
            'TARGET_OUTRANK_SHARE',
            'TARGET_ROAS',
            'TARGET_SPEND',
            'MAXIMIZE_CONVERSIONS',
            'MAXIMIZE_CONVERSION_VALUE',
            'PERCENT_CPC',
            'MANUAL_CPV',
            'TARGET_CPM',
            'TARGET_IMPRESSION_SHARE',
            'COMMISSION',
            'INVALID',
            'MANUAL_CPA'
        ];
        return $array[$key] ?? '--';
    }

    public static function advertisingChannelTypeName($key) {
        $array = [
            'UNSPECIFIED',
            'UNKNOWN',
            'SEARCH',
            'DISPLAY',
            'SHOPPING',
            'HOTEL',
            'VIDEO',
            'MULTI_CHANNEL',
            'LOCAL',
            'SMART',
            'PERFORMANCE_MAX',
            'LOCAL_SERVICES',
            'DISCOVERY',
            'TRAVEL'
        ];
        return $array[$key] ?? '--';
    }
}
