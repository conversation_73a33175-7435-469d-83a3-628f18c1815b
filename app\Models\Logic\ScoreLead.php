<?php

namespace App\Models\Logic;

use App\Models\Business\BusinessCampaignRecuringPayment;
use App\Models\Campaign\CampaignCallPayout;
use App\Models\Subscription\Subscription;
use Illuminate\Database\Eloquent\Model;
use App\Models\Lead\LeadRouting;
use App\Models\Business\Business;
use App\Models\Campaign\Campaign;
use DB;
use App\Models\Campaign\CampaignLocation;
use App\Models\Master\MstZipcode;
use App\Models\Outbound\LeadCall;

class ScoreLead extends Model {

    // Point 1
    public function countScoreFromRegion() {
        $scoreArray = [];
        $winCampaignIds = [];
        $leadState = CampaignLocation::where('location_type', 'source')
                ->where('location_coverage', 'state')
                ->groupBy('campaign_id')
                ->select('campaign_id', DB::raw('count(*) as total'))
                ->get();

        foreach ($leadState as $state) {
            $score = 50;
            if ($state->total >= 50) {
                $score = 0;
            } else {
                $score -= $state->total;
            }
            $scoreArray[$state->campaign_id] = $score;
            $winCampaignIds[] = $state->campaign_id;
        }

        $businessesCampaign = Campaign::whereNotIn('campaign_id', $winCampaignIds)->get();
        //Added By HJ On 24-08-2022 For Optimize Code Start
        $stateValue = MstZipcode::distinct()->get(['state', 'zipcode'])->toArray();
        $zipCodeDataArr = array();
        for ($z = 0; $z < count($stateValue); $z++) {
            $zipCodeDataArr[$stateValue[$z]['zipcode']] = $stateValue[$z]['state'];
        }
        // echo "<pre>";print_r($zipCodeDataArr);die;
        //Added By HJ On 24-08-2022 For Optimize Code End
        $campIdArr = [];
        foreach ($businessesCampaign as $campaign) {
            $campIdArr[] = $campaign->campaign_id;
        }
        $leadZip = $zipArray = $stateArr = [];
        if (count($campIdArr) > 0) {
            $leadZip = CampaignLocation::where('location_type', 'source')
            ->where('location_coverage', 'zipcode')
            ->whereIn('campaign_id', $campIdArr)
            ->get(['value'])->toArray();
            if (count($leadZip) > 0) {
                foreach ($leadZip as $zip) {
                    $zipArray[] = $zip['value'];
                }
            }
        }
        $stateCount = 0;
        if (count($zipArray) > 0) {
            foreach ($zipCodeDataArr as $zip => $state) {

                if (in_array($zip, $zipArray) && !in_array($state, $stateArr)) {
                    $stateArr[] = $state;
                }
            }
        }
        foreach ($businessesCampaign as $campaign) {
            if (count($zipArray) > 0) {
                $scoreArray[$campaign->campaign_id] = 50 - $stateCount;
            } else {
                $scoreArray[$campaign->campaign_id] = 50;
            }
        }
        // echo "<pre>";print_r($scoreArray);
        // die;
        return $scoreArray;
    }

    // Point 2
    public function countScoreMoveSize() {
        // Need table for future code -- temporary we add 20 score in all campaign
        $scoreArray = [];
        $businessesCampaignMoveSize = Campaign::with(['campaignMoveSize'])->where('lead_category_id', 1)->get()->toArray();
        $scoreArray = [];
        if(count($businessesCampaignMoveSize) > 0){
            foreach ($businessesCampaignMoveSize as $campaign) {
                $score = 0;
                $movesizeArr = [];
                foreach ($campaign['campaign_move_size'] as $movesize) {
                    $movesizeArr[] = $movesize['move_size_id'];
                }
                if( in_array(2, $movesizeArr)){
                    $score = 10;
                }
                if (in_array(3, $movesizeArr) ||
                    in_array(4, $movesizeArr) ||
                    in_array(5, $movesizeArr) ||
                    in_array(6, $movesizeArr)) {
                    $score = 20;
                }
                $scoreArray[$campaign['campaign_id']] =  $score;
            }
        }
        // echo "<pre>";print_r($scoreArray); die;
        return $scoreArray;
    }

    // Point 3
    public function countScoreMoveDate() {
        // Need table for future code -- temporary we add 20 score in all campaign
        $scoreArray = [];
        $businessesCampaignMoveDate = Campaign::get();
        foreach ($businessesCampaignMoveDate as $moveDate) {
            $score = 20;
            $scoreArray[$moveDate->campaign_id] = $score;
        }
        // echo "<pre>";print_r($scoreArray); die;
        return $scoreArray;
    }

    // Point 4
//    public function countScoreRecurrentPayment() {
//        // Need flow for future code -- temporary we add 30 score in all campaign
//        $scoreArray = [];
//        $businessesCampaignRecurrentPayment = Campaign::get();
//        foreach ($businessesCampaignRecurrentPayment as $recurrentPayment) {
//            $score = 30;
//            $scoreArray[$recurrentPayment->campaign_id] = $score;
//        }
//        // echo "<pre>";print_r($scoreArray); die;
//        return $scoreArray;
//    }
    // Point 5 for campaign score logic
    /*
    public function countScoreRemainingTimeCampaign() {
        $campaigns = Campaign::get();
        $scoreArray = array();
        foreach ($campaigns as $campaignId => $campaign) {

            $campaignId = $campaign->campaign_id;
            $campdate = $campaign->created_at;
            $leadDate = date("Y-m-d", strtotime($campdate));
            $date = strtotime($leadDate);
            $dayNumber = date("N", $date);

            // Find Hour
            $hour = strtotime($campdate);
            $hourNumber = date('H', $hour);
            $businessesCampaignHours = DB::select("select end_hour from campaign_scheule l1 where day = " . $dayNumber . " and campaign_id = " . $campaignId . " and end_hour = (select max(end_hour) from campaign_scheule l1 where day = " . $dayNumber . " and campaign_id = " . $campaignId . ")");
            $scoreArray[$campaignId] = $campaign->default_score + $campaign->additional_score;
            if (count($businessesCampaignHours) > 0) {
                $endHour = $businessesCampaignHours[0]->end_hour;
                if ($endHour > $hourNumber) {
                    $differentHour = $endHour - $hourNumber;
                    if ($differentHour < 2) {
                        $scoreArray[$campaignId] = 20;
                    } else if ($differentHour < 6) {
                        $scoreArray[$campaignId]  = 10;
                    }
                }
            }
        }
        // echo "<pre>"; print_r($scoreArray); die;
        return $scoreArray;
    }
    */
    // Point 5
    public function countScoreRemainingTime($leadDate, $leadTime, $campaignIds) {
        $scoreArray = array();
        $date = strtotime($leadDate);
        $dayNumber = date("N", $date);
        // Find Hour
        $hour = strtotime($leadTime);
        $hourNumber = date('H', $hour);
        foreach ($campaignIds as $campaignId => $score) {
            $businessesCampaignHours = DB::select("select end_hour from campaign_scheule l1 where day = " . $dayNumber . " and campaign_id = " . $campaignId . " and end_hour = (select max(end_hour) from campaign_scheule l1 where day = " . $dayNumber . " and campaign_id = " . $campaignId . ")");
            //echo "<pre>";print_r($businessesCampaignHours);die;
            $scoreArray[$campaignId] = $score;
            if (count($businessesCampaignHours) > 0) {
                $endHour = $businessesCampaignHours[0]->end_hour;
                if ($endHour > $hourNumber) {
                    $differentHour = $endHour - $hourNumber;
                    if ($differentHour < 2) {
                        $scoreArray[$campaignId] += 20;
                    } else if ($differentHour < 6) {
                        $scoreArray[$campaignId] += 10;
                    }
                }
            }
        }
        return $scoreArray;
    }
    // Point 6
    public function countScoreLeadLimit() {
        $scoreArray = [];
        $businessesCampaignLimit = Campaign::get();
        foreach ($businessesCampaignLimit as $limit) {
            $score = 20;
            $value = $limit->daily_lead_limit;
            // if ($value >= 50) {
            if ($value >= 50 ) {
                $score = 20;
            }else if ($value < 50 && $value >= 20 ) {
                $score = 10;
            }else if ($value < 20) {
                $score = 0;
            }
            $scoreArray[$limit->campaign_id] = $score;
        }
        // echo "<pre>";print_r($scoreArray);die;
        return $scoreArray;
    }
    // Point 7
    public function countScoreYesterdayLeadSpot() {
        $scoreArray = [];
        $businessesCampaignLimit = Campaign::get();
        foreach ($businessesCampaignLimit as $limit) {

            $score = 0;
            $value = $limit->daily_lead_limit;
            $businessesCampaignId = $limit->campaign_id;

            $searchDate = date('Y-m-d', strtotime("-1 days"));
            $yesterdaystart = $searchDate . ' 00:00:00';
            $yesterdayend = $searchDate . ' 23:59:59';
            $leadRouting = LeadRouting::where('route_status', 'sold')
                    ->whereBetween('created_at', [$yesterdaystart , $yesterdayend])
                    ->where('campaign_id', $businessesCampaignId)
                    ->groupBy('campaign_id')
                    ->select('lead_routing_id', 'campaign_id', DB::raw('count(*) as total'))
                    ->get()->toArray();

                    // dd($leadRouting );
            foreach ($leadRouting as $routingData) {
                $score = 0;
                $remainSlot = $value - $routingData['total'];
                if ($remainSlot > 5) {
                    $score = 20;
                }else if ($routingData['total'] >= 1 && $routingData['total'] <= 5) {
                    $score = 10;
                }

            }
            $scoreArray[$businessesCampaignId] = $score;
        }
        // echo "<pre>";print_r($scoreArray); die;
        return $scoreArray;
    }
    // Point 8
    /*public function countScoreLeadPrice() {
        $scoreArray = $winCampaignIds = array();
        $businessesCampaignPayout = Campaign::where('lead_type_id', 1)->get();
        // $businessesCampaignPayout = Campaign::where('lead_type_id', 1)->toSql();
        foreach ($businessesCampaignPayout as $payout) {
            $amount = $score = 0;
            if (!empty($payout->lead_payout)) {
                $amount = $payout->lead_payout;
            }
            if ($amount > 15) {
                // $newcal = $amount - 15;
                // $score = 40 + ($newcal * 5);
                $score = 40;
            }
            if ($amount > 14 && $amount <= 15) {
                $score = 35;
            }
            if ($amount > 13 && $amount <= 14) {
                $score = 30;
            }
            if ($amount > 12 && $amount <= 13) {
                $score = 25;
            }
            if ($amount > 11 && $amount <= 12) {
                $score = 20;
            }
            if ($amount > 10 && $amount <= 11) {
                $score = 15;
            }
            if ($amount > 9 && $amount <= 10) {
                $score = 10;
            }
            if ($amount >= 8 && $amount <= 9) {
                $score = 5;
            }
            if ($amount < 8) {
                $score = 0;
            }
            $scoreArray[$payout->campaign_id] = $score;
            $winCampaignIds[] = $payout->campaign_id;
        }

        $businessesCampaign = Campaign::whereNotIn('campaign_id', $winCampaignIds)->get();
        foreach ($businessesCampaign as $campaign) {
            $scoreArray[$campaign->campaign_id] = 0;
        }
        // echo "<pre>";print_r($scoreArray); die;
        return $scoreArray;
    }*/

    // test Lead
    public function countScoreLastLead($campaignIds) {
        $scoreArray = $lastLeadArray = array();
        $date = date("Y-m-d H:i:s");
        /*foreach ($campaignIds as $campaignId => $score) {
            //$campaignLastEntry = DB::select("SELECT TIMESTAMPDIFF(MINUTE, created_at, NOW()) AS Minute,lead_routing_id FROM lead_routing WHERE campaign_id = " . $campaignId . " AND payout > 0 AND route_status = 'sold' ORDER BY 1 ASC LIMIT 1");
            $campaignLastEntry = DB::select("SELECT TIMESTAMPDIFF(MINUTE, created_at, ".DB::raw("'$date'").") AS Minute,lead_routing_id FROM lead_routing WHERE campaign_id = ".$campaignId." AND payout > 0 AND route_status = 'sold' ORDER BY 1 ASC LIMIT 1");
            //echo "<pre>";print_r($campaignLastEntry);die;
            DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 snapIt Log countScoreLastLead route lead first= " . $campaignId."==".$score."==".json_encode($campaignLastEntry), 'created_at' => date("Y-m-d H:i:s")]);
            $scoreArray[$campaignId] = $score;
            $updateScore = 0;
            if (count($campaignLastEntry) > 0) {
                $minute = $campaignLastEntry[0]->Minute;
                if ($minute >= 30) {
                    $updateScore = ceil(($minute / 30) * 10);
                    $scoreArray[$campaignId] += $updateScore;
                }
            }
            DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 snapIt Log countScoreLastLead route lead second= " . $campaignId."==".$updateScore."==".json_encode($scoreArray), 'created_at' => date("Y-m-d H:i:s")]);
            //$this->createInboundLogs($campaignId, $campaignId, '68', $score . 'lead_routing = ' . json_encode($scoreArray));
        }*/
        $campaignIdsArray = array_keys($campaignIds);
        $campaignIdsStr = implode(',', $campaignIdsArray);
        $campaignLastLead = DB::select("select campaign_id,TIMESTAMPDIFF(MINUTE, created_at, ".DB::raw("'$date'").") AS Minute from ( SELECT campaign_id, max(created_at) as created_at FROM lead_routing WHERE campaign_id IN ($campaignIdsStr) AND payout > 0 AND route_status = 'sold' group by campaign_id) a");
        //echo "<pre>";print_r($campaignLastLead);die;
        for ($c=0; $c < count($campaignLastLead); $c++) {
            $lastLeadArray[$campaignLastLead[$c]->campaign_id] = $campaignLastLead[$c]->Minute;
        }

        foreach ($campaignIds as $campaignId => $score) {
            $scoreArray[$campaignId] = $score;
            $updateScore = 0;
            if (array_key_exists($campaignId, $lastLeadArray) && $lastLeadArray[$campaignId] > 0) {
                $minute = $lastLeadArray[$campaignId];
                if ($minute >= 30) {
                    $updateScore = ceil(($minute / 30) * 10);
                    $scoreArray[$campaignId] += $updateScore;
                }
            }
            //DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 snapIt Log countScoreLastLead route lead second= " . $campaignId."==".$updateScore."==".json_encode($scoreArray), 'created_at' => date("Y-m-d H:i:s")]);
            //$this->createInboundLogs($campaignId, $campaignId, '68', $score . 'lead_routing = ' . json_encode($scoreArray));
        }
        return $scoreArray;
    }

    // for campaign score logic
    /*
    public function countScoreLastLeadCampaign() {
        $scoreArray = array();
        $campIdArr = [];
        $leadRoutingCampIds = LeadRouting::with(['routingCampaignInfo'])->groupBy('campaign_id')->get()->toArray();
        foreach ($leadRoutingCampIds as $campaign ) {
            if($campaign['routing_campaign_info']['campaign_type_id'] == 1){
                $campaignLastEntry = DB::select("SELECT TIMESTAMPDIFF(MINUTE, created_at, NOW()) AS Minute FROM lead_routing
                                    WHERE campaign_id = " . $campaign['campaign_id'] . " AND payout > 0 ORDER BY 1 ASC LIMIT 1");
                // echo  $campaign['campaign_id']  . " = <pre>"; print_r($campaignLastEntry);
                if (count($campaignLastEntry) > 0) {
                    $minute = $campaignLastEntry[0]->Minute;
                    if ($minute >= 30) {
                        $updateScore = ceil(($minute / 30) * 10);
                        $scoreArray[$campaign['campaign_id']] = $updateScore;
                    }else {
                        $scoreArray[$campaignId] = $campaign['default_score'] + $campaign['additional_score'];
                    }
                }else {
                    $scoreArray[$campaignId] = $campaign['default_score'] + $campaign['additional_score'];
                }
            }
        }
        // echo "<pre>";print_r($scoreArray ); die;
        return $scoreArray;
    }
    */
    // Point 9
    public function countScoreLastLeadOld($campaignIds) {

        $scoreArray = [];
        $endSearchDate = date('Y-m-d H:i:s', strtotime("-60 minutes"));
        $startSearchDate = date('Y-m-d H:i:s');
        foreach ($campaignIds as $campaignId => $score) {
            $leadRouting = LeadRouting::where('status', 1)
                    ->where('created_at', '>', $endSearchDate)
                    ->where('created_at', '<', $startSearchDate)
                    ->where('businesses_campaign_id', $campaignId)
                    ->select('businesses_campaign_id')
                    ->distinct()
                    ->get();
            if (count($leadRouting) > 0) {
                $scoreArray[$campaignId] = $score;
            } else {
                $scoreArray[$campaignId] = $score + 50;
            }
        }
        return $scoreArray;
    }
    // Point 9
    public function countScoreLastPhoneLead($campaignIds) {
        $scoreArray = [];
        $endSearchDate = date('Y-m-d H:i:s', strtotime("-60 minutes"));
        $startSearchDate = date('Y-m-d H:i:s');
        foreach ($campaignIds as $campaignId => $score) {
            $leadRouting = LeadCall::where('call_disposition_id', 1)
                    ->where('created_at', '>', $endSearchDate)
                    ->where('created_at', '<', $startSearchDate)
                    ->where('campaign_id', $campaignId)
                    ->where('call_type', 'inbound')
                    ->where('payout', '>', 0)
                    ->select('campaign_id')
                    ->distinct()
                    ->get();
            if (count($leadRouting) > 0) {
                $scoreArray[$campaignId] = $score;
            } else {
                $scoreArray[$campaignId] = $score + 50;
            }
        }
        return $scoreArray;
    }
    // Point 10
    /*public function countScoreCampaignFund($campaignIds) {
        $scoreArray = $campDataArr = $businessIdArr = $businessDataArr = array();
        $campIdArr = array_keys($campaignIds);
        $businessesCampaignFund = Campaign::whereIn('campaign_id', $campIdArr)->get(['campaign_id','business_id','payment_type','credit_available','credit_reserved'])->toArray();
        for($f=0;$f<count($businessesCampaignFund);$f++){
            $campDataArr[$businessesCampaignFund[$f]['campaign_id']] = $businessesCampaignFund[$f];
            if($businessesCampaignFund[$f]['payment_type'] == 0 && !in_array($businessesCampaignFund[$f]['business_id'],$businessIdArr)){
                $businessIdArr[] = $businessesCampaignFund[$f]['business_id'];
            }
        }
        if(count($businessIdArr) > 0){
            $businessesData = Business::whereIn('business_id', $businessIdArr)->get(['business_id','credit_available', 'credit_reserved'])->toArray();
            for($c=0;$c<count($businessesData);$c++){
               $businessDataArr[$businessesData[$c]['business_id']] =  $businessesData[$c];
            }
        }
        //echo "<pre>";print_r($businessDataArr);die;
        foreach ($campaignIds as $campaignId => $score) {
            if($campDataArr[$campaignId]){
                $businessesCampaignFund = $campDataArr[$campaignId];
                if(isset($businessDataArr[$businessesCampaignFund['business_id']])){
                    $businessesData = $businessDataArr[$businessesCampaignFund['business_id']];
                    $balance = $businessesData['credit_available'] + $businessesData['credit_reserved'];
                } else {
                    $balance = $businessesCampaignFund['credit_available'] + $businessesCampaignFund['credit_reserved'];
                }
                if ($balance >= 500) {
                    $score += 30;
                } else if ($balance >= 400) {
                    $score += 25;
                } else if ($balance >= 300) {
                    $score += 20;
                } else if ($balance >= 200) {
                    $score += 15;
                } else if ($balance >= 100) {
                    $score += 10;
                } else {
                    $score += 0;
                }
                $scoreArray[$campaignId] = $score;
            }
        }
        return $scoreArray;
    }*/
    // for campaign score logic
    /*
    public function countScoreCampaignFundCampaign() {
        $businessesCampaignFund = Campaign::get(['campaign_id','business_id','payment_type','credit_available','credit_reserved'])->toArray();
        $scoreArray = $campDataArr = $businessIdArr = $businessDataArr = array();

        foreach ($businessesCampaignFund as $campaign) {
            $campDataArr[$campaign['campaign_id']] = $campaign ;
            if($campaign['payment_type'] == 0 && !in_array($campaign['business_id'],$businessIdArr)){
                $businessIdArr[] = $campaign['business_id'];
            }
        }
        if(count($businessIdArr) > 0){
            $businessesData = Business::whereIn('business_id', $businessIdArr)
            ->get(['business_id','credit_available', 'credit_reserved'])->toArray();
            foreach ($businessesData as $business) {
                $businessDataArr[$business['business_id']] = $business;
            }
        }
        foreach ($businessesCampaignFund as $campaign) {
            if($campDataArr[$campaign['campaign_id']]){
                $campaignFund = $campDataArr[$campaign['campaign_id']];

                if($campaign['payment_type'] == 0){
                    $businessesData = $businessDataArr[$campaign['business_id']];
                    $balance = $businessesData['credit_available'] + $businessesData['credit_reserved'];
                } else {
                    $balance = $campaign['credit_available'] + $campaign['credit_reserved'];
                }

                $score = 0;
                if ($balance >= 500) {
                    $score = 30;
                } else if ($balance >= 400) {
                    $score = 25;
                } else if ($balance >= 300) {
                    $score = 20;
                } else if ($balance >= 200) {
                    $score = 15;
                } else if ($balance >= 100) {
                    $score = 10;
                } else {
                    $score = 0;
                }
                $scoreArray[$campaign['campaign_id']] = $score;
            }
        }
        //  echo "<pre>";print_r($scoreArray );
        // die;
        return $scoreArray;
    }
    */
    public function array_merge_recursive_numeric_score() {
        // dd("asdasdasd");
        // Gather all arrays
        $arrays = func_get_args();
        // echo "<pre>";print_r($arrays); die;
        // If there's only one array, it's already merged
        if (count($arrays) == 1) {
            return $arrays[0];
        }

        // Remove any items in $arrays that are NOT arrays
        foreach ($arrays as $key => $array) {
            if (!is_array($array)) {
                unset($arrays[$key]);
            }
        }

        // We start by setting the first array as our final array.
        // We will merge all other arrays with this one.
        $final = array_shift($arrays);

        foreach ($arrays as $b) {

            foreach ($final as $key => $value) {

                // If $key does not exist in $b, then it is unique and can be safely merged
                if (!isset($b[$key])) {

                    $final[$key] = $value;
                } else {

                    // If $key is present in $b, then we need to merge and sum numeric values in both
                    if (is_numeric($value) && is_numeric($b[$key])) {
                        // If both values for these keys are numeric, we sum them
                        $final[$key] = $value + $b[$key];
                    } else if (is_array($value) && is_array($b[$key])) {
                        // If both values are arrays, we recursively call ourself
                        $final[$key] = array_merge_recursive_numeric($value, $b[$key]);
                    } else {
                        // If both keys exist but differ in type, then we cannot merge them.
                        // In this scenario, we will $b's value for $key is used
                        $final[$key] = $b[$key];
                    }
                }
            }

            // Finally, we need to merge any keys that exist only in $b
            foreach ($b as $key => $value) {
                if (!isset($final[$key])) {
                    $final[$key] = $value;
                }
            }
        }

        return $final;
    }

    public function createInboundLogs($fromnumber, $tonumber, $type, $log) {
        $log = array(
            'fromnumber' => $fromnumber,
            'tonumber' => $tonumber,
            'type' => $type,
            'log' => $log,
        );
        InboundCallsLog::create($log);
    }

    //Added By BK on 28/05/2025 as per discussion with client
    public function countScoreLeadPrice($campaignIds) {
        $scoreArray = $payoutArray = array();
        $campaignIdsArray = array_keys($campaignIds);
        $campaignCallPayout = CampaignCallPayout::whereIn('campaign_id', $campaignIdsArray)->get(['campaign_id','automated_outbound'])->toArray();
        for($f=0; $f<count($campaignCallPayout); $f++){
            if ($campaignCallPayout[$f]['automated_outbound'] > 100) {
                $payoutArray[$campaignCallPayout[$f]['campaign_id']] = 100;
            } else {
                $payoutArray[$campaignCallPayout[$f]['campaign_id']] = $campaignCallPayout[$f]['automated_outbound'];
            }
        }

        //echo "<pre>";print_r($payoutArray);die;
        foreach ($campaignIds as $campaignId => $score) {
            // Ensure scoreArray key is initialized
            $scoreArray[$campaignId] = $scoreArray[$campaignId] ?? 0;
            $score += $payoutArray[$campaignId] ?? 0;
            // Add payout value (or 0 if not present)
            $scoreArray[$campaignId] = $score;
        }
        //echo "<pre>";print_r($scoreArray);die;
        return $scoreArray;
    }

    public function countScoreSubscription() {
        $scoreArray = $campaignArray = $businessIdArray = $subscriptionArray = array();
        $campaign = Campaign::get(['campaign_id', 'business_id'])->toArray();
        for($f=0; $f<count($campaign); $f++){
            $campaignArray[$campaign[$f]['campaign_id']] = $campaign[$f]['business_id'];
            if (!in_array($campaign[$f]['business_id'], $businessIdArray)) {
                $businessIdArray[] = $campaign[$f]['business_id'];
            }
        }

        if(count($businessIdArray) > 0){
            $subscription = Subscription::where('subscription_start', '>=', date('Y-m-01'))->where('subscription_end', '<=', date('Y-m-t'))->where('is_active', 'yes')->whereIn('business_id', $businessIdArray)->get(['business_id', 'subscription_plan_id'])->toArray();
            for($s=0; $s<count($subscription); $s++){
                $subscriptionArray[$subscription[$s]['business_id']] = $subscription[$s]['subscription_plan_id'];
            }
        }

        for($f=0; $f<count($campaign); $f++){
            if (isset($subscriptionArray[$campaignArray[$campaign[$f]['campaign_id']]]) && $subscriptionArray[$campaignArray[$campaign[$f]['campaign_id']]] == 1) {
                $scoreArray[$campaign[$f]['campaign_id']] = 0;
            } else if (isset($subscriptionArray[$campaignArray[$campaign[$f]['campaign_id']]]) && $subscriptionArray[$campaignArray[$campaign[$f]['campaign_id']]] == 2) {
                $scoreArray[$campaign[$f]['campaign_id']] = 1;
            } else if (isset($subscriptionArray[$campaignArray[$campaign[$f]['campaign_id']]]) && $subscriptionArray[$campaignArray[$campaign[$f]['campaign_id']]] == 3) {
                $scoreArray[$campaign[$f]['campaign_id']] = 2;
            } else {
                $scoreArray[$campaign[$f]['campaign_id']] = 0;
            }
        }
        return $scoreArray;
    }

    public function countScoreRecurrentPayment($campaignIds) {
        // Need flow for future code -- temporary we add 30 score in all campaign
        $scoreArray = $campaignIdArray = $recuringPaymentArray = array();
        $campaignIdsArray = array_keys($campaignIds);
        $campaign = Campaign::whereIn('campaign_id', $campaignIdsArray)->get()->toArray();
        for($f=0; $f<count($campaign); $f++){
            $campaignIdArray[] = $campaign[$f]['campaign_id'];
        }

        if(count($campaignIdArray) > 0){
            $campaignRecuringPayment = BusinessCampaignRecuringPayment::whereIn('campaign_id', $campaignIdArray)->get(['campaign_id', 'is_active'])->toArray();
            for($r=0; $r<count($campaignRecuringPayment); $r++){
                $recuringPaymentArray[$campaignRecuringPayment[$r]['campaign_id']] = $campaignRecuringPayment[$r]['is_active'];
            }
        }

        foreach ($campaignIds as $campaignId => $score) {
            // Ensure scoreArray key is initialized
            $scoreArray[$campaignId] = $scoreArray[$campaignId] ?? 0;
            if (isset($recuringPaymentArray[$campaignId]) && $recuringPaymentArray[$campaignId] == 'yes') {
                $score += 1;
            } else {
                $score += 0;
            }
            // Add payout value (or 0 if not present)
            $scoreArray[$campaignId] = $score;
        }
        return $scoreArray;
    }

    public function countScoreCampaignFund($campaignIds) {
        $scoreArray = $campDataArr = $businessIdArr = $businessDataArr = array();
        $campIdArr = array_keys($campaignIds);
        $businessesCampaignFund = Campaign::whereIn('campaign_id', $campIdArr)->get(['campaign_id','business_id','payment_type','credit_available','credit_reserved'])->toArray();
        for($f=0;$f<count($businessesCampaignFund);$f++){
            $campDataArr[$businessesCampaignFund[$f]['campaign_id']] = $businessesCampaignFund[$f];
            if($businessesCampaignFund[$f]['payment_type'] == 0 && !in_array($businessesCampaignFund[$f]['business_id'], $businessIdArr)){
                $businessIdArr[] = $businessesCampaignFund[$f]['business_id'];
            }
        }
        if(count($businessIdArr) > 0){
            $businessesData = Business::whereIn('business_id', $businessIdArr)->get(['business_id','credit_available', 'credit_reserved'])->toArray();
            for($c=0;$c<count($businessesData);$c++){
                $businessDataArr[$businessesData[$c]['business_id']] =  $businessesData[$c];
            }
        }
        //echo "<pre>";print_r($businessDataArr);die;
        foreach ($campaignIds as $campaignId => $score) {
            if($campDataArr[$campaignId]){
                $businessesCampaignFund = $campDataArr[$campaignId];
                if(isset($businessDataArr[$businessesCampaignFund['business_id']])){
                    $businessesData = $businessDataArr[$businessesCampaignFund['business_id']];
                    $balance = $businessesData['credit_available'] + $businessesData['credit_reserved'];
                } else {
                    $balance = $businessesCampaignFund['credit_available'] + $businessesCampaignFund['credit_reserved'];
                }
                if ($balance >= 500) {
                    $score += 1;
                } else {
                    $score += 0;
                }
                $scoreArray[$campaignId] = $score;
            }
        }
        //echo "<pre>";print_r($scoreArray);die;
        return $scoreArray;
    }
}
