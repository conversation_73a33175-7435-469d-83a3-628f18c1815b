<?php

namespace App\Http\Controllers\GoogleAdwords;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Google\Ads\GoogleAds\Lib\OAuth2TokenBuilder;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClient;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClientBuilder;
use Google\Ads\GoogleAds\V18\Services\AssetGroupAssetOperation;
use Google\Ads\GoogleAds\V18\Services\MutateAssetGroupAssetsRequest;
use Google\Ads\GoogleAds\V18\Enums\AdGroupAdStatusEnum\AdGroupAdStatus;
use Google\Ads\GoogleAds\V18\Resources\AdGroupAd;
use Google\Ads\GoogleAds\V18\Services\AdGroupAdOperation;
use Google\Ads\GoogleAds\V18\Services\GoogleAdsRow;
use Google\Ads\GoogleAds\V18\Services\SearchGoogleAdsRequest;
use Google\Ads\GoogleAds\V18\Enums\AssetFieldTypeEnum\AssetFieldType;
use Google\Ads\GoogleAds\V18\Services\RemoveAutomaticallyCreatedAssetsRequest;
use Google\Ads\GoogleAds\V18\Common\AssetsWithFieldType;
use Google\Ads\GoogleAds\V18\Services\SearchGoogleAdsStreamRequest;
use Google\Ads\GoogleAds\Util\V18\ResourceNames;
use Google\Ads\GoogleAds\V18\Services\AdGroupAssetOperation;
use Google\Ads\GoogleAds\V18\Services\MutateAdGroupAssetsRequest;
use Google\Ads\GoogleAds\V18\Enums\AdGroupAssetStatusEnum\AdGroupAssetStatus;
use Google\Ads\GoogleAds\V18\Enums\AssetSourceEnum\AssetSource; // Import AssetSource
use Google\ApiCore\ApiException;
use DB;
use Log;

class GoogleAdwordsRemoveHeadlineController extends Controller
{
    // Replace with your actual values
    const CUSTOMER_ID = '7000201456'; // Your customer ID
    const AD_GROUP_ID = 170600608962;
    const ASSET_ID = 193643673205;
    const FIELD_TYPE = AssetFieldType::AD_IMAGE; // The field type of the asset you're unlinking

    public static function main()
    {
        // Construct a GoogleAdsClient object.
        // .ini file path for PMM, MT
        $filePath = base_path("google_ads_php.ini");

        // Generate a refreshable OAuth2 credential for authentication.
        $oAuth2Credential = (new OAuth2TokenBuilder())->fromFile($filePath)->build();

        // Construct a Google Ads client configured from a properties file and the
        // OAuth2 credentials above.
        $googleAdsClient = (new GoogleAdsClientBuilder())
            ->fromFile($filePath)
            ->withOAuth2Credential($oAuth2Credential)
            ->build();

        $googleAdsServiceClient = $googleAdsClient->getGoogleAdsServiceClient();
        $query = "SELECT ad_group_asset.asset, ad_group_asset.field_type, ad_group_asset.resource_name, asset.id,asset.name,asset.type,ad_group_asset.source
                  FROM ad_group_asset
                  WHERE ad_group_asset.ad_group = 'customers/7000201456/adGroups/170600608962'";

        /*$query = "SELECT
                    ad_group_asset.resource_name
                  FROM
                    ad_group_asset
                  WHERE
                    ad_group_asset.ad_group = 'customers/7000201456/adGroups/124493143848'
                    AND ad_group_asset.asset = 'customers/7000201456/assets/43338673297'
                    AND ad_group_asset.field_type = 'SITELINK'";*/

        $response = $googleAdsServiceClient->searchStream(
                SearchGoogleAdsStreamRequest::build(self::CUSTOMER_ID, $query)
            );

        $operations = [];
        foreach ($response->iterateAllElements() as $row) {
            $asset = $row->getAsset();
            $assetId = $asset->getId();
            $resourceName = $row->getAdGroupAsset()->getResourceName();
            $field_type = $row->getAdGroupAsset()->getFieldType();
            printf("Found Auto-generated Headline Asset: %s %s (%s)\n", $field_type,$assetId, $resourceName);
            //printf("Found Auto-generated Headline Asset: %s %s (%s)\n", $resourceName);

        }dd();
        //echo '<pre>'; print_r($operations); die;

        self::runExample(
            $googleAdsClient,
            self::CUSTOMER_ID,
            self::AD_GROUP_ID,
            self::ASSET_ID,
            self::FIELD_TYPE
        );
    }

    /**
     * Runs the example.
     *
     * @param GoogleAdsClient $googleAdsClient the Google Ads API client
     * @param int $customerId the customer ID
     * @param int $adGroupId the ad group ID
     * @param int $assetId the asset ID to remove from the ad group
     * @param string $fieldType the field type of the asset (e.g., 'HEADLINE')
     */
    public static function runExample(
        GoogleAdsClient $googleAdsClient,
        $customerId,
        $adGroupId,
        $assetId,
        $fieldType
    ) {
        // Build the AdGroupAsset resource name for the specific association to remove.
        // Note: The fieldType is crucial here to uniquely identify the AdGroupAsset.
        $adGroupAssetResourceName = ResourceNames::forAdGroupAsset(
            $customerId,
            $adGroupId,
            $assetId,
            $fieldType
        );

        print_r("Resource Name being removed: " . $adGroupAssetResourceName . PHP_EOL);

        // Create the remove operation.
        $operation = new AdGroupAssetOperation();
        $operation->setRemove($adGroupAssetResourceName);

        if (empty($operation)) {
            echo "No MANUAL assets found to remove.\n";
            return;
        }

        // Create the MutateAdGroupAssetsRequest object.
        $request = new MutateAdGroupAssetsRequest([
            'customer_id' => (string)$customerId,
            'operations' => [$operation]
        ]);

        $adGroupAssetServiceClient = $googleAdsClient->getAdGroupAssetServiceClient();
        try {
            // Execute the mutate request using mutateAdGroupAssets.
            $response = $adGroupAssetServiceClient->mutateAdGroupAssets(
                $request
            );

            printf("Successfully unlinked AdGroupAsset with resource name: '%s'%s",
                $adGroupAssetResourceName,
                PHP_EOL
            );
            foreach ($response->getResults() as $result) {
                printf("  Result resource name: '%s'%s", $result->getResourceName(), PHP_EOL);
            }
        } catch (\Google\Ads\GoogleAds\V14\Errors\GoogleAdsException $e) {
            printf("Request failed with status '%s' and message '%s'.%s",
                $e->getStatus()->getCode(),
                $e->getMessage(),
                PHP_EOL
            );
            printf("Detailed errors:%s", PHP_EOL);
            foreach ($e->getErrors() as $error) {
                printf("  Error code: %s%s", $error->getErrorCode()->getErrorCode()->name(), PHP_EOL);
                printf("  Message: %s%s", $error->getMessage(), PHP_EOL);
                if ($error->getLocation()) {
                    foreach ($error->getLocation()->getFieldPaths() as $fieldPath) {
                        printf("    Field path: %s%s", $fieldPath->getFieldPath(), PHP_EOL);
                    }
                }
            }
            exit(1);
        } catch (\Exception $e) {
            // This will catch any other unexpected exceptions
            printf("An unexpected exception occurred: %s%s", $e->getMessage(), PHP_EOL);
            exit(1);
        }
    }

    public function removeHeadline() {
        //$this->main('VM'); //for VM
        //$this->main('QTM'); //for QTM
        //$this->main('IS'); //for IS
        GoogleAdwordsRemoveHeadlineController::main();
    }
}
