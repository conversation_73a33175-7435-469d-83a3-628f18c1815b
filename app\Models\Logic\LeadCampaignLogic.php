<?php

namespace App\Models\Logic;

use App\Models\Campaign\CampaignCallPayout;
use App\Models\Master\MstLeadSource;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Helpers\CommonFunctions;
use App\Http\Controllers\Leadroute\LeadRouteController;
use App\Http\Controllers\Setting\SettingController;
use App\Models\Campaign\Campaign;
use App\Models\Campaign\CampaignScheule;
use App\Models\Campaign\CampaignLocation;
use App\Models\Campaign\CampaignCustom;
use App\Models\Business\Business;
use App\Models\Business\BusinessPartnerMapping;
use App\Models\Logic\ScoreLead;
use App\Models\Lead\LeadRouting;
use App\Models\Lead\Lead;
use App\Models\Lead\Logs;
use Illuminate\Support\Facades\DB;
use DateTime;
use App\Models\Logic\LogicJunk;
use App\Models\Logic\LogicHeavyLift;
use App\Models\Logic\LogicCarTransport;
use App\Models\PingPost\PrimeMarketing;
use App\Models\Master\MstCallConfig;
use App\Models\Campaign\CampaignMoving;
use App\Models\Campaign\CampaignLeadSource;
use App\Models\Campaign\CampaignJunkType;
use App\Models\Campaign\CampaignHeavyLifting;
use App\Models\Campaign\CampaignCarTransport;
use App\Models\Outbound\LeadCall;
use App\Models\Campaign\CampaignOngoingCall;
use App\Models\Campaign\CampaignOrganic;
use App\Models\PingPost\PingPostPayout;
use App\Models\PingPost\RexDirect;
use App\Models\PingPost\LeadStudio;
use App\Models\PingPost\ETNAmerica;
use App\Models\PingPost\Zap;
use App\Models\PingPost\MoverJunction;

class LeadCampaignLogic extends Model {

    use HasFactory;

    public function checkCampaignLogic($campaignIds, $coverageType, $date, $datetime, $remainSlot, $leadId, $lead_category_id, $call, $directRoute = 0, $isPingPost = 0, $cust_number = "", $cust_dial_number = "", $campaignType = 0, $isTCPA = 0, $isPingPostType = 0) {
        //Added By BK On 29/02/2024 For get campaign payout
        $payoutField = "automated_outbound";
        if ($call > 0) { // For Inbound
            $payoutField = "automated_inbound";
        }
        $campaignPayout = CampaignCallPayout::whereIn('campaign_id', $campaignIds)->get(['campaign_id', $payoutField])->toArray();
        $campaignPayoutArray = [];
        for ($p=0; $p < count($campaignPayout); $p++) {
            $campaignPayoutArray[$campaignPayout[$p]['campaign_id']] = $campaignPayout[$p][$payoutField];
        }
        //Ended get campaign payout
        //echo "<pre>";print_r($campaignIds);die;
        $scoreLeadPriceObj = new ScoreLead();
        $partnerLogic = new LogicPartnerBusiness();
        $rexDirectObj = new RexDirect(); //Added By BK On 07/06/2023 For Wholesaler Ping Post API
        $leadStudioObj = new LeadStudio(); //Added By BK On 07/06/2023 For Wholesaler Ping Post API
        $etnAmericaObj = new ETNAmerica(); //Added By BK On 23/10/2023 For Wholesaler Ping Post API
        $zapObj = new Zap(); //Added By BK On 30/10/2023 For Wholesaler Ping Post API
        $moverJunctionObj = new MoverJunction(); //Added By BK On 28/07/2025 For Wholesaler Ping Post API
        $minPayout = 3;
        $nonWsBuyerAmount = 0;
        $primeMarketingBusinessId = 32; //Put Here Primemarketing Business Id of Live DB
        $studioBusinessId = 829; //Add Here Studio Business Id For Ping Post ApI
        $zapBusinessId = 993;
        $moverJunctionBusinessId = 1117;

        //get moving ally business campaign Added By BK On 17/02/2023
        $movingAllyCampaignIdArray = [];
        $isMovingAllyCampaign = 0;
        $campaignDetail = Campaign::where('business_id', 462)->where('lead_type_id', 1)->whereIn('campaign_type_id', [1, 2, 6])->get(['campaign_id']);
        if (count($campaignDetail) > 0) { foreach ($campaignDetail as $campaign) {
            $movingAllyCampaignIdArray[] = $campaign->campaign_id;
        } }

        $payoutType = 'outbound_call';
        if ($coverageType == 1) {
            $minPayout = 5;
        }
        if ($isPingPost == 0) {
            $this->LeadRoutelog($leadId, 'lead id and camp ids for check in checkCampaignLogic()  = ' . json_encode($campaignIds), 31);
        }
        $leadRouteSlot = 4;
        $getLeadRouteSlot = MstCallConfig::where('call_config', 'leadroute3slot')->get()->toArray();
        if (count($getLeadRouteSlot) > 0) {
            if (strtolower($getLeadRouteSlot[0]['status']) == "active") {
                $leadRouteSlot = 3;
            }
        }
        //Added by BK on 23/07/2024
        if ($lead_category_id == 4) {
            $leadRouteSlot = 6;
        }
        // Find Maximum amount from Exclusive or Premium or Dual Slot
        $premiumAmount = $newpremiumAmount = $exclusiveAmount = $newexclusiveAmount = $dualSlotAmount = $newDualSlotAmount = $trioAmount = $newtrioAmount = $customAmount = $newcustomAmount = 0;
        // For Premium
        $premiumCampaignIds = $campAssocDataArr = $businessIdArr = $businessesCampaignPremium = $businessesCampaignExclusive = $businessesCampaignDualSlot = $businessesCampaignTrio = $businessesCampaignCustom = $allTypeCampIds = $campTypeArr = $exclusiveCampaignIds = $dualSlotCampaignIds = $trioCampaignIds = $customCampaignIds = $pingPostIdArr = $campaignBuyerType = $sharedCampaignIds = $dualCampaignIds = $trioCampaignIds = $top4 = $top3 = $top2 = $custom = $premiumSoldBusinessIdArray = $dualSoldBusinessIdArray = $trioSoldBusinessIdArray = array();
        $getAllCampData = Campaign::whereIn('campaign_id', $campaignIds)->get(['campaign_id', 'default_score', 'additional_score', 'subscription_score', 'credit_available', 'credit_reserved', 'payment_type', 'business_id', 'campaign_type_id', 'free_lead'])->toArray();
        //echo "<pre>";print_r($getAllCampData);die;
        for ($v = 0; $v < count($getAllCampData); $v++) {
            $campAssocDataArr[$getAllCampData[$v]['campaign_id']] = $getAllCampData[$v];
            /* if (!in_array($getAllCampData[$v]['business_id'], $businessIdArr)) { */
            $businessIdArr[$getAllCampData[$v]['campaign_id']] = $getAllCampData[$v]['business_id'];
            /* } */
            if ($getAllCampData[$v]['campaign_type_id'] == 1) {
                $businessesCampaignPremium[] = $getAllCampData[$v];
            } else if ($getAllCampData[$v]['campaign_type_id'] == 2) {
                $businessesCampaignExclusive[] = $getAllCampData[$v];
            } else if ($getAllCampData[$v]['campaign_type_id'] == 6) {
                $businessesCampaignDualSlot[] = $getAllCampData[$v];
            } else if ($getAllCampData[$v]['campaign_type_id'] == 8) {
                $businessesCampaignTrio[] = $getAllCampData[$v];
            } else if ($getAllCampData[$v]['campaign_type_id'] == 9) {
                $businessesCampaignCustom[] = $getAllCampData[$v];
            }
            $campTypeArr[$getAllCampData[$v]['campaign_id']] = $getAllCampData[$v]['campaign_type_id'];
            $allTypeCampIds[$getAllCampData[$v]['campaign_id']] = $getAllCampData[$v]['default_score'] + $getAllCampData[$v]['additional_score'] + $getAllCampData[$v]['subscription_score'];
            if ($primeMarketingBusinessId == $getAllCampData[$v]['business_id']) {
                $pingPostIdArr[] = $getAllCampData[$v]['campaign_id'];
            }
        }
        //echo "<pre>";print_r($pingPostIdArr);die;
        $businessesData = Business::whereIn('business_id', $businessIdArr)->get(['payment_type', 'credit_available', 'credit_reserved', 'business_id', 'buyer_type_id'])->toArray();
        //echo "<pre>";print_r($businessesData);die;
        $businessesDataArr = $routedDataArr = array();
        for ($x = 0; $x < count($businessesData); $x++) {
            $businessesDataArr[$businessesData[$x]['business_id']] = $businessesData[$x];
            foreach ($businessIdArr as $campId => $businessId) {
                if ($businessId == $businessesData[$x]['business_id']) {
                    $campaignBuyerType[$campId] = $businessesData[$x]['buyer_type_id'];
                }
            }
        }
        //echo "<pre>";print_r($businessesDataArr);die;
        if ($isPingPost == 0) {
            $this->LeadRoutelog($leadId, 'businessesCampaignPremium = ' . json_encode($businessesCampaignPremium), 32);
        }
        $getRoutingData = LeadRouting::where('lead_id', $leadId)->get()->toArray();
        for ($fd = 0; $fd < count($getRoutingData); $fd++) {
            //echo $getRoutingData[$fd]['campaign_id'];die;
            $routedDataArr[$getRoutingData[$fd]['lead_id']][$getRoutingData[$fd]['campaign_id']] = $getRoutingData[$fd]['lead_routing_id'];
        }
        //echo "<pre>";print_r($routedDataArr);die;
        if (count($businessesCampaignPremium) > 0) {
            //Comment By BK on 28/05/2025 as per discussion with client
            /*$allTypeCampIds = $scoreLeadPriceObj->countScoreRemainingTime($date, $datetime, $allTypeCampIds);
            if ($isPingPost == 0) {
                $this->LeadRoutelog($leadId, 'countScoreRemainingTime = ' . json_encode($allTypeCampIds), 33);
            }
            if ($call == 0) {
                $allTypeCampIds = $scoreLeadPriceObj->countScoreLastLead($allTypeCampIds);
            } else {
                $allTypeCampIds = $scoreLeadPriceObj->countScoreLastPhoneLead($allTypeCampIds);
                //echo "<pre>";print_r($allTypeCampIds);die;
            }
            if ($isPingPost == 0) {
                $this->LeadRoutelog($leadId, 'Lead Type= ' . $call . ' : countScoreLastLead or countScoreLastPhoneLead = ' . json_encode($allTypeCampIds), 34);
            }
            $allTypeCampIds = $scoreLeadPriceObj->countScoreCampaignFund($allTypeCampIds);
            if ($isPingPost == 0) {
                $this->LeadRoutelog($leadId, 'countScoreCampaignFund =' . json_encode($allTypeCampIds), 35);
            }*/

            //Added By BK on 28/05/2025 as per discussion with client
            $allTypeCampIds = $scoreLeadPriceObj->countScoreLeadPrice($allTypeCampIds);
            if ($isPingPost == 0) {
                $this->LeadRoutelog($leadId, 'countScoreLeadPrice = ' . json_encode($allTypeCampIds), 33);
            }

            $allTypeCampIds = $scoreLeadPriceObj->countScoreRecurrentPayment($allTypeCampIds);
            if ($isPingPost == 0) {
                $this->LeadRoutelog($leadId, 'countScoreRecurrentPayment =' . json_encode($allTypeCampIds), 35);
            }

            $allTypeCampIds = $scoreLeadPriceObj->countScoreCampaignFund($allTypeCampIds);
            if ($isPingPost == 0) {
                $this->LeadRoutelog($leadId, 'countScoreCampaignFund =' . json_encode($allTypeCampIds), 35);
            }

            if ($lead_category_id == 2) {
                $logicJunk = new LogicJunk();
                $allTypeCampIds = $logicJunk->junkScoreCalculate($allTypeCampIds);
                $this->LeadRoutelog($leadId, 'junkScoreCalculate =' . json_encode($allTypeCampIds), 36);
                //echo "<pre>";print_r($allTypeCampIds);die;
            }
            if ($lead_category_id == 3) {
                $logicHeavyLift = new LogicHeavyLift();
                $allTypeCampIds = $logicHeavyLift->heavyLiftScoreCalculate($allTypeCampIds);
                $this->LeadRoutelog($leadId, 'junkScoreCalculate =' . json_encode($allTypeCampIds), 36);
                //echo "<pre>Score==";print_r($allTypeCampIds);die;
            }
            if ($lead_category_id == 4) {
                $logicCarTransport = new LogicCarTransport();
                $allTypeCampIds = $logicCarTransport->carTransportScoreCalculate($allTypeCampIds);
                $this->LeadRoutelog($leadId, 'carTransportScoreCalculate =' . json_encode($allTypeCampIds), 37);
                //echo "<pre>";print_r($allTypeCampIds);die;
            }

            $allTypeCampIds = $partnerLogic->partnerBunisessLogic($allTypeCampIds, $coverageType, $call);
            if ($isPingPost == 0) {
                $this->LeadRoutelog($leadId, 'partnerBunisessLogic =' . json_encode($allTypeCampIds), 38);
            }
            //echo "<pre>";print_r($allTypeCampIds);die;
        }
        //echo "<pre>";print_r($allTypeCampIds);die;
        foreach ($allTypeCampIds as $key => $val) {
            if (isset($campTypeArr[$key])) {
                if ($campTypeArr[$key] == 1) {
                    $premiumCampaignIds[$key] = $val;
                } else if ($campTypeArr[$key] == 2) {
                    $exclusiveCampaignIds[$key] = $val;
                } else if ($campTypeArr[$key] == 6) {
                    $dualSlotCampaignIds[$key] = $val;
                } else if ($campTypeArr[$key] == 8) {
                    $trioCampaignIds[$key] = $val;
                } else if ($campTypeArr[$key] == 9) {
                    $customCampaignIds[$key] = $val;
                }
            }
        }
        $pingPayoutPm = new PrimeMarketing();
        $getLeadData = Lead::where('lead_id', $leadId)->get()->toArray();
        /* if (count($pingPostIdArr) > 0) {
          $getLeadData = Lead::where('lead_id', $leadId)->get()->toArray();
          } */

        if ($isPingPostType == 0 || $isPingPostType == 1) { //$isPingPostType=1 (Premium)
            arsort($premiumCampaignIds);
            if (count($premiumCampaignIds) > 0) {
                // Store possible matches in lead_matches table
                if ($isPingPost == 0) {
                    if ($leadId > 0) {
                        $dataInsert = array();
                        foreach ($premiumCampaignIds as $campId => $campScore) {
                            /* if (in_array($campId, $pingPostIdArr)) {
                              $pingResponse = $pingPayoutPm->pingPrimeMarketing($getLeadData, $campId);
                              $payoutMatch = 0;
                              if (isset($pingResponse['payout'])) {
                              $payoutMatch = $pingResponse['payout'];
                              }
                              //echo "<pre>";print_R($payoutMatch);die;
                              } else {
                              $payoutMatch = CommonFunctions::getCampaignPayout($campId, $coverageType, 0, $call);
                              } */
                            $businessesCampaign = $campAssocDataArr[$campId];
                            $creditAvailable = 0;
                            if ($businessesCampaign['payment_type'] == 0) {
                                $businessesData = $businessesDataArr[$businessesCampaign['business_id']];
                                $creditAvailable = $businessesData['credit_available'];
                            } else {
                                $creditAvailable = $businessesCampaign['credit_available'];
                            }

                            //$payoutMatch = CommonFunctions::getCampaignPayout($campId, $coverageType, 0, $call);
                            if (!isset($routedDataArr[$leadId][$campId])) {
                                $dataInsert[] = [
                                    'lead_id' => $leadId,
                                    'campaign_id' => $campId,
                                    'score' => $campScore,
                                    'payout' => $campaignPayoutArray[$campId] ?? 0,
                                    'route_status' => "unsold",
                                    'is_free_lead' => "no",
                                    'created_at' => date("Y-m-d H:i:s"),
                                    'balance_after_charge' => $creditAvailable,
                                    'payment_type' => $businessesCampaign['payment_type']
                                ];
                            }
                        }
                        //echo "<pre>";print_r($dataInsert);die;
                        if (count($dataInsert) > 0) {
                            LeadRouting::insert($dataInsert);
                        }
                    }
                }

                $getRoutingData = LeadRouting::where('lead_id', $leadId)->get()->toArray();
                for ($fd = 0; $fd < count($getRoutingData); $fd++) {
                    //echo $getRoutingData[$fd]['campaign_id'];die;
                    $routedDataArr[$getRoutingData[$fd]['lead_id']][$getRoutingData[$fd]['campaign_id']] = $getRoutingData[$fd]['lead_routing_id'];
                }
                // Remove Duplicate businesses' campaign
                $keys = array_keys($premiumCampaignIds);
                $data = Campaign::having('total', '>', 1)->groupBy('business_id')->select('business_id', DB::raw('count(campaign_id) as total'))->whereIn('campaign_id', $keys)->get()->toArray();
                foreach ($data as $camp) {
                    $dupCampaignList = Campaign::where('business_id', $camp['business_id'])->whereIn('campaign_id', $keys)->get(['campaign_id'])->toArray(); //It remove from loop
                    $dupData = [];
                    foreach ($dupCampaignList as $campList) {
                        $dupData[$campList['campaign_id']] = $premiumCampaignIds[$campList['campaign_id']];
                    }
                    asort($dupData);
                    $cDup = 1;
                    $removeRecordDup = [];
                    foreach ($dupData as $kDup => $vDup) {
                        if ($cDup == count($dupData)) {
                            break;
                        }
                        $removeRecordDup[$kDup] = $vDup;
                        $cDup++;
                    }
                    $premiumCampaignIds = array_diff_key($premiumCampaignIds, $removeRecordDup);
                }
            }
            if ($isPingPost == 0) {
                $this->LeadRoutelog($leadId, 'duplicate camp ids = ' . json_encode($premiumCampaignIds), 39);
            }
            arsort($premiumCampaignIds);
            // Funds again check before deduct from the business or campaign
            $new_premiumCampaignIds = $top4newpayoutwiseload = array();
            //echo "<pre>";print_r($premiumCampaignIds);die;
            foreach ($premiumCampaignIds as $campkey => $campvaluescore) {
                /* if (in_array($campkey, $pingPostIdArr)) {
                  $pingResponse = $pingPayoutPm->pingPrimeMarketing($getLeadData, $campkey);
                  $payout = 0;
                  if (isset($pingResponse['payout'])) {
                  $payout = $pingResponse['payout'];
                  }
                  //echo "<pre>";print_R($payout);die;
                  } else {
                  $payout = CommonFunctions::getCampaignPayout($campkey, $coverageType, 0, $call);
                  } */
                //$payout = CommonFunctions::getCampaignPayout($campkey, $coverageType, 0, $call); //Comment By BK On 29/02/2024 as per discussion with MA and DS
                $payout = $campaignPayoutArray[$campkey] ?? 0;
                if (isset($campAssocDataArr[$campkey]) && $payout > 0) {
                    $campaignnew = $campAssocDataArr[$campkey];
                    if (isset($businessesDataArr[$campaignnew['business_id']]) && $businessesDataArr[$campaignnew['business_id']]['payment_type'] == 'pre') { // Check if business payment_type is PrePayment
                        //$businessesData_1 = $businessesDataArr[$campaignnew['business_id']];
                        if ($campaignnew['payment_type'] == 0) {
                            $getBusinessCredit = Business::where('business_id', $campaignnew['business_id'])->get()->toArray();
                            if (count($getBusinessCredit) > 0) {
                                if (($getBusinessCredit[0]['credit_available'] - $getBusinessCredit[0]['credit_reserved']) >= $payout) {
                                    $new_premiumCampaignIds[$campkey] = $campvaluescore;
                                    $top4newpayoutwiseload[$campkey] = $payout;
                                }
                            }
                        } else {
                            $getCampignCredit = Campaign::where('campaign_id', $campkey)->get()->toArray();
                            if (count($getCampignCredit) > 0) {
                                if (($getCampignCredit[0]['credit_available'] - $getCampignCredit[0]['credit_reserved']) >= $payout) {
                                    $new_premiumCampaignIds[$campkey] = $campvaluescore;
                                    $top4newpayoutwiseload[$campkey] = $payout;
                                }
                            }
                        }
                    } else {
                        $new_premiumCampaignIds[$campkey] = $campvaluescore;
                        $top4newpayoutwiseload[$campkey] = $payout;
                    }
                }
            }
            $premiumCampaignIds = $new_premiumCampaignIds;
            if ($isPingPost == 0) {
                $this->LeadRoutelog($leadId, 'credit_available checked camp ids = ' . json_encode($premiumCampaignIds), 40);
            }
            $top4 = array_slice($premiumCampaignIds, 0, $remainSlot, true);
            //Log::info('top4reguler:TOTAL'.json_encode($top4));
            arsort($top4newpayoutwiseload);
            if ($isPingPost == 0) {
                $this->LeadRoutelog($leadId, 'remin slot :' . $remainSlot . ' ==lead id and payout camp ids = ' . json_encode($top4newpayoutwiseload), 41);
            }
            if (count($top4) > 0) {
                foreach ($top4 as $campaignId => $score) {
                    /* if (in_array($campaignId, $pingPostIdArr)) {
                      $pingResponse = $pingPayoutPm->pingPrimeMarketing($getLeadData, $campaignId);
                      $leadPayout = 0;
                      if (isset($pingResponse['payout'])) {
                      $leadPayout = $pingResponse['payout'];
                      }
                      //echo "<pre>";print_R($payout);die;
                      } else {
                      $leadPayout = CommonFunctions::getCampaignPayout($campaignId, $coverageType, 0, $call);
                      } */
                    //$leadPayout = CommonFunctions::getCampaignPayout($campaignId, $coverageType, 0, $call); //Comment By BK On 29/02/2024 as per discussion with MA and DS
                    $leadPayout = $campaignPayoutArray[$campaignId] ?? 0;
                    $premiumAmount += $leadPayout;
                    $newpremiumAmount += $leadPayout;
                }
            }

            if ($isTCPA > 0) {
                return array_keys($top4);
            }

            // Ping started to shared and exclusive in common Added By BK On 20/09/2023
            // premium 2 companies available in Prime Marketing so there are 2 slot available
            if (count($top4) > 0) {
                $premiumSoldCampIdArray = array_keys($top4);
                foreach ($premiumSoldCampIdArray as $key => $value)
                    if (!in_array($businessIdArr[$value], $premiumSoldBusinessIdArray)) {
                        $premiumSoldBusinessIdArray[] = $businessIdArr[$value];
                    }
            }
            //$premiumSoldCampIdArray = (count($top4) > 0) ? array_keys($top4) : [];
            $premiumRemainingSlot = (count($top4) < 4) ? $remainSlot - count($top4) : 0;
        }

        // For Trio Slot
        if (/*$remainSlot == $leadRouteSlot*/ ($isPingPostType == 0 || $isPingPostType == 8)) { //$isPingPostType=8 (Trio)
            arsort($trioCampaignIds);
            if (count($trioCampaignIds) > 0) {
                // Store possible matches in lead_matches table
                if ($isPingPost == 0) {
                    if ($leadId > 0) {
                        $dataInsert = array();
                        foreach ($trioCampaignIds as $campId => $campScore) {
                            $businessesCampaign = $campAssocDataArr[$campId];
                            $creditAvailable = 0;
                            if ($businessesCampaign['payment_type'] == 0) {
                                $businessesData = $businessesDataArr[$businessesCampaign['business_id']];
                                $creditAvailable = $businessesData['credit_available'];
                            } else {
                                $creditAvailable = $businessesCampaign['credit_available'];
                            }

                            //$payoutMatch = CommonFunctions::getCampaignPayout($campId, $coverageType, 0, $call); //Comment By BK On 29/02/2024 as per discussion with MA and DS
                            if (!isset($routedDataArr[$leadId][$campId])) {
                                $dataInsert[] = [
                                    'lead_id' => $leadId,
                                    'campaign_id' => $campId,
                                    'score' => $campScore,
                                    'payout' => $campaignPayoutArray[$campId] ?? 0,
                                    'route_status' => "unsold",
                                    'is_free_lead' => "no",
                                    'created_at' => date("Y-m-d H:i:s"),
                                    'balance_after_charge' => $creditAvailable,
                                    'payment_type' => $businessesCampaign['payment_type']
                                ];
                            }
                        }
                        //echo "<pre>";print_r($dataInsert);die;
                        if (count($dataInsert) > 0) {
                            LeadRouting::insert($dataInsert);
                        }
                    }
                }

                $getRoutingData = LeadRouting::where('lead_id', $leadId)->get()->toArray();
                for ($fd = 0; $fd < count($getRoutingData); $fd++) {
                    //echo $getRoutingData[$fd]['campaign_id'];die;
                    $routedDataArr[$getRoutingData[$fd]['lead_id']][$getRoutingData[$fd]['campaign_id']] = $getRoutingData[$fd]['lead_routing_id'];
                }

                // Remove Duplicate businesses' campaign
                $keys = array_keys($trioCampaignIds);
                $data = Campaign::having('total', '>', 1)->groupBy('business_id')->select('business_id', DB::raw('count(campaign_id) as total'))->whereIn('campaign_id', $keys)->get()->toArray();
                foreach ($data as $camp) {
                    $dupCampaignList = Campaign::where('business_id', $camp['business_id'])->whereIn('campaign_id', $keys)->get(['campaign_id'])->toArray(); //It remove from loop
                    $dupData = [];
                    foreach ($dupCampaignList as $campList) {
                        $dupData[$campList['campaign_id']] = $trioCampaignIds[$campList['campaign_id']];
                    }
                    asort($dupData);
                    $cDup = 1;
                    $removeRecordDup = [];
                    foreach ($dupData as $kDup => $vDup) {
                        if ($cDup == count($dupData)) {
                            break;
                        }
                        $removeRecordDup[$kDup] = $vDup;
                        $cDup++;
                    }
                    $trioCampaignIds = array_diff_key($trioCampaignIds, $removeRecordDup);
                }
            }
            if ($isPingPost == 0) {
                $this->LeadRoutelog($leadId, 'duplicate camp ids = ' . json_encode($trioCampaignIds), 105);
            }

            arsort($trioCampaignIds);
            // Funds again check before deduct from the business or campaign
            $new_trioCampaignIds = $top3newpayoutwiseload = array();
            foreach ($trioCampaignIds as $campkey => $campvaluescore) {
                //$payout = CommonFunctions::getCampaignPayout($campkey, $coverageType, 0, $call); //Comment By BK On 29/02/2024 as per discussion with MA and DS
                $payout = $campaignPayoutArray[$campkey] ?? 0;
                if (isset($campAssocDataArr[$campkey]) && $payout > 0) {
                    $campaignnew = $campAssocDataArr[$campkey];
                    if (isset($businessesDataArr[$campaignnew['business_id']]) && $businessesDataArr[$campaignnew['business_id']]['payment_type'] == 'pre') { // Check if business payment_type is PrePayment
                        //$businessesData_1 = $businessesDataArr[$campaignnew['business_id']];
                        if ($campaignnew['payment_type'] == 0) {
                            $getBusinessCredit = Business::where('business_id', $campaignnew['business_id'])->get()->toArray();
                            if (count($getBusinessCredit) > 0) {
                                if (($getBusinessCredit[0]['credit_available'] - $getBusinessCredit[0]['credit_reserved']) >= $payout) {
                                    $new_trioCampaignIds[$campkey] = $campvaluescore;
                                    $top3newpayoutwiseload[$campkey] = $payout;
                                }
                            }
                        } else {
                            $getCampignCredit = Campaign::where('campaign_id', $campkey)->get()->toArray();
                            if (count($getCampignCredit) > 0) {
                                if (($getCampignCredit[0]['credit_available'] - $getCampignCredit[0]['credit_reserved']) >= $payout) {
                                    $new_trioCampaignIds[$campkey] = $campvaluescore;
                                    $top3newpayoutwiseload[$campkey] = $payout;
                                }
                            }
                        }
                    } else {
                        $new_trioCampaignIds[$campkey] = $campvaluescore;
                        $top3newpayoutwiseload[$campkey] = $payout;
                    }
                }
            }
            $trioCampaignIds = $new_trioCampaignIds;
            if ($isPingPost == 0) {
                $this->LeadRoutelog($leadId, 'credit_available checked camp ids = ' . json_encode($trioCampaignIds), 106);
            }

            //when remove duplicate businesses campaign after count 1 or 2 than push premium campaign in trio slot array
            if ((count($trioCampaignIds) == 1 || count($trioCampaignIds) == 2 && $isPingPostType == 0)) {
                $businessPartnerIdArray = $newtop4 = $newtop4Payout = [];
                //get trio slot campaign business id
                $trioBusinessId = array_keys($trioCampaignIds);
                //get trio slot partner business
                $businessPartner = BusinessPartnerMapping::whereIn('business_id',
                    function($query) use ($trioBusinessId){
                        $query->select('business_id')
                            ->from('campaign')
                            ->whereIn('campaign_id', $trioBusinessId);
                    })->get(['business_partner_id'])->toArray();
                for ($n = 0; $n < count($businessPartner); $n++) {
                    $businessPartnerIdArray[] = $businessPartner[$n]['business_partner_id'];
                }

                //remove campaign from same business or partner company
                foreach ($top4 as $campId => $t4) {
                    if ($businessIdArr[$trioBusinessId[0]] == $businessIdArr[$campId] || (isset($trioBusinessId[1]) && $businessIdArr[$trioBusinessId[1]] == $businessIdArr[$campId]) || in_array($businessIdArr[$campId], $businessPartnerIdArray)) {
                        continue;
                    }
                    if ($t4 == @max($top4)) { //if score same choose highest payout
                        //$newtop4Payout[$campId] = CommonFunctions::getCampaignPayout($campId, $coverageType, 0, $call); //For Get Campaign Payout As Per Critearea //Parameter (1-Camp Id),(2-Coverage 0-local,1-long),(3-tok 1-tok,0-automated),(4-call type inbound-1,outbound-0)
                        $newtop4Payout[$campId] = $campaignPayoutArray[$campId] ?? 0; //For Get Campaign Payout As Per Critearea //Parameter (1-Camp Id),(2-Coverage 0-local,1-long),(3-tok 1-tok,0-automated),(4-call type inbound-1,outbound-0)
                    }
                    $newtop4[$campId] = $t4;
                }
                arsort($newtop4);
                if (count($trioCampaignIds) == 2) {
                    $newtop4CampId = 0;
                    if (!empty($newtop4Payout)) {
                        $newtop4CampId = array_search(max($newtop4Payout), $newtop4Payout);
                    }
                    //print_r($newtop4Payout); die;
                    foreach ($newtop4 as $campId => $t4) {
                        if ($t4 == @max($newtop4)) {
                            if (!empty($newtop4CampId) && $newtop4CampId != $campId) {
                                continue;
                            }
                            $trioCampaignIds[$campId] = $t4;
                        }
                    }
                } else {
                    $newtop2 = array_slice($newtop4, 0, 2, true);
                    foreach ($newtop2 as $campId => $t2) {
                        $trioCampaignIds[$campId] = $t2;
                    }
                }
            }

            if (isset($getLeadData[0]) && ($getLeadData[0]['sold_type'] <> 'premium') || $call == 1 || $isPingPostType == 8) {
                if (isset($getLeadData[0]) && ($getLeadData[0]['sold_type'] == 'trio' || $getLeadData[0]['sold_type'] == 'dual' || $isPingPostType == 8)) {
                    $top3 = array_slice($trioCampaignIds, 0, $remainSlot, true);
                } else {
                    $top3 = array_slice($trioCampaignIds, 0, 3, true);
                }

                if ($directRoute == 3) {
                    $top3 = array_slice($trioCampaignIds, 0, $remainSlot, true);
                }
            }

            arsort($top3newpayoutwiseload);
            if ($isPingPost == 0) {
                $this->LeadRoutelog($leadId, 'remin slot :' . $remainSlot . ' ==lead id and payout trio camp ids = ' . json_encode($top3newpayoutwiseload), 107);
            }
            if (count($top3) > 0) {
                foreach ($top3 as $campaignId => $score) {
                    //$leadPayout = CommonFunctions::getCampaignPayout($campaignId, $coverageType, 0, $call); //Comment By BK On 29/02/2024 as per discussion with MA and DS
                    $leadPayout = $campaignPayoutArray[$campaignId] ?? 0;
                    $trioAmount += $leadPayout;
                    $newtrioAmount += $leadPayout;
                }
            }

            //Added By BK on 13/06/2025
            if (count($top3) > 0) {
                $trioSoldCampIdArray = array_keys($top3);
                foreach ($trioSoldCampIdArray as $key => $value)
                    if (!in_array($businessIdArr[$value], $trioSoldBusinessIdArray)) {
                        $trioSoldBusinessIdArray[] = $businessIdArr[$value];
                    }
            }
            if ($remainSlot == 4) {
                $trioRemainingSlot = (count($top3) < 4) ? 3 - count($top3) : 0;
            } else {
                $trioRemainingSlot = (count($top3) < 4) ? $remainSlot - count($top3) : 0;
            }
        }

        // For Dual Slot
        if (/*$remainSlot == $leadRouteSlot*/ ($isPingPostType == 0 || $isPingPostType == 6)) { //$isPingPostType=8 (Dual Slot)
            if (count($dualSlotCampaignIds) > 0) {
                arsort($dualSlotCampaignIds);
                if (count($dualSlotCampaignIds) > 0) {
                    //remove duplicate business' campaign
                    $dualkeys = array_keys($dualSlotCampaignIds);
                    $data = Campaign::having('total', '>', 1)->groupBy('business_id')->select('business_id', DB::raw('count(campaign_id) as total'))->whereIn('campaign_id', $dualkeys)->get()->toArray();
                    foreach ($data as $camp) {
                        $dupCampaignList = Campaign::where('business_id', $camp['business_id'])->whereIn('campaign_id', $dualkeys)->get(['campaign_id'])->toArray(); //It remove from loop
                        $dupData = [];
                        foreach ($dupCampaignList as $campList) {
                            $dupData[$campList['campaign_id']] = $dualSlotCampaignIds[$campList['campaign_id']];
                        }
                        asort($dupData);
                        $cDup = 1;
                        $removeRecordDup = [];
                        foreach ($dupData as $kDup => $vDup) {
                            if ($cDup == count($dupData)) {
                                break;
                            }
                            $removeRecordDup[$kDup] = $vDup;
                            $cDup++;
                        }
                        $dualSlotCampaignIds = array_diff_key($dualSlotCampaignIds, $removeRecordDup);
                    }
                }

                //exclusive dual lead on/off check
                $exclusiveDualSlotStatus = 0;
                $getExclusiveDualSlot = MstCallConfig::where('call_config', 'exclusivedualslot')->get(['status'])->toArray();
                if (count($getExclusiveDualSlot) > 0) {
                    if (strtolower($getExclusiveDualSlot[0]['status']) == "active") {
                        $exclusiveDualSlotStatus = 1;
                    }
                }

                //when remove duplicate businesses campaign after count 1 than push one trio campaign in dual slot array
                if (count($dualSlotCampaignIds) == 1 && $exclusiveDualSlotStatus == 0 && $isPingPostType == 0) {
                    $businessPartnerIdArray = $newtop3 = $newtop3Payout = [];
                    //get dual slot campaign business id
                    $dualBusinessId = array_keys($dualSlotCampaignIds);
                    //get dual slot partner business
                    $businessPartner = BusinessPartnerMapping::where('business_id', $businessIdArr[$dualBusinessId[0]])->get(['business_partner_id'])->toArray();
                    for ($n = 0; $n < count($businessPartner); $n++) {
                        $businessPartnerIdArray[] = $businessPartner[$n]['business_partner_id'];
                    }

                    //remove campaign from same business or partner company
                    if (isset($new_trioCampaignIds) && count($new_trioCampaignIds) > 0) {
                        $top31 = array_slice($new_trioCampaignIds, 0, 3, true); //only get original trio campaign
                        foreach ($top31 as $campId => $t3) {
                            if ($businessIdArr[$dualBusinessId[0]] == $businessIdArr[$campId] || in_array($businessIdArr[$campId], $businessPartnerIdArray)) {
                                continue;
                            }
                            if ($t3 == @max($top31)) { //if score same choose highest payout
                                //$newtop4Payout[$campId] = CommonFunctions::getCampaignPayout($campId, $coverageType, 0, $call); //For Get Campaign Payout As Per Critearea //Parameter (1-Camp Id),(2-Coverage 0-local,1-long),(3-tok 1-tok,0-automated),(4-call type inbound-1,outbound-0)
                                $newtop3Payout[$campId] = $campaignPayoutArray[$campId] ?? 0; //For Get Campaign Payout As Per Critearea //Parameter (1-Camp Id),(2-Coverage 0-local,1-long),(3-tok 1-tok,0-automated),(4-call type inbound-1,outbound-0)
                            }
                            $newtop3[$campId] = $t3;
                        }
                        arsort($newtop3);
                        $newtop3CampId = 0;
                        if (!empty($newtop3Payout)) {
                            $newtop3CampId = array_search(max($newtop3Payout), $newtop3Payout);
                        }
                        //print_r($newtop4Payout); die;
                        foreach ($newtop3 as $campId => $t3) {
                            if ($t3 == @max($newtop3)) {
                                if (!empty($newtop3CampId) && $newtop3CampId != $campId) {
                                    continue;
                                }
                                $dualSlotCampaignIds[$campId] = $t3;
                            }
                        }
                    }
                }

                //when remove duplicate businesses campaign after count 1 than push one premium campaign in dual slot array
                if (count($dualSlotCampaignIds) == 1 && $exclusiveDualSlotStatus == 0 && $isPingPostType == 0) {
                    $businessPartnerIdArray = $newtop4 = $newtop4Payout = [];
                    //get dual slot campaign business id
                    $dualBusinessId = array_keys($dualSlotCampaignIds);
                    //get dual slot partner business
                    $businessPartner = BusinessPartnerMapping::where('business_id', $businessIdArr[$dualBusinessId[0]])->get(['business_partner_id'])->toArray();
                    for ($n = 0; $n < count($businessPartner); $n++) {
                        $businessPartnerIdArray[] = $businessPartner[$n]['business_partner_id'];
                    }

                    //remove campaign from same business or partner company
                    foreach ($top4 as $campId => $t4) {
                        if ($businessIdArr[$dualBusinessId[0]] == $businessIdArr[$campId] || in_array($businessIdArr[$campId], $businessPartnerIdArray)) {
                            continue;
                        }
                        if ($t4 == @max($top4)) { //if score same choose highest payout
                            //$newtop4Payout[$campId] = CommonFunctions::getCampaignPayout($campId, $coverageType, 0, $call); //For Get Campaign Payout As Per Critearea //Parameter (1-Camp Id),(2-Coverage 0-local,1-long),(3-tok 1-tok,0-automated),(4-call type inbound-1,outbound-0)
                            $newtop4Payout[$campId] = $campaignPayoutArray[$campId] ?? 0; //For Get Campaign Payout As Per Critearea //Parameter (1-Camp Id),(2-Coverage 0-local,1-long),(3-tok 1-tok,0-automated),(4-call type inbound-1,outbound-0)
                        }
                        $newtop4[$campId] = $t4;
                    }
                    arsort($newtop4);
                    $newtop4CampId = 0;
                    if (!empty($newtop4Payout)) {
                        $newtop4CampId = array_search(max($newtop4Payout), $newtop4Payout);
                    }
                    //print_r($newtop4Payout); die;
                    foreach ($newtop4 as $campId => $t4) {
                        if ($t4 == @max($newtop4)) {
                            if (!empty($newtop4CampId) && $newtop4CampId != $campId) {
                                continue;
                            }
                            $dualSlotCampaignIds[$campId] = $t4;
                        }
                    }
                }

                $newdualslotpayoutwiseload = $dataInsert = array();
                if (count($dualSlotCampaignIds) > 0) {
                    if ($isPingPost == 0) {
                        if ($leadId != 0) {
                            foreach ($dualSlotCampaignIds as $campId => $campScore) {
                                /* if (in_array($campId, $pingPostIdArr)) {
                                  $pingResponse = $pingPayoutPm->pingPrimeMarketing($getLeadData, $campId);
                                  $leadPayout = 0;
                                  if (isset($pingResponse['payout'])) {
                                  $leadPayout = $pingResponse['payout'];
                                  }
                                  //echo "<pre>";print_R($leadPayout);die;
                                  $newdualslotpayoutwiseload[$campId] = $leadPayout;
                                  } else {
                                  $newdualslotpayoutwiseload[$campId] = CommonFunctions::getCampaignPayout($campId, $coverageType, 0, $call);
                                  } */

                                $businessesCampaign = $campAssocDataArr[$campId];
                                $creditAvailable = 0;
                                if ($businessesCampaign['payment_type'] == 0) {
                                    $businessesData = $businessesDataArr[$businessesCampaign['business_id']];
                                    $creditAvailable = $businessesData['credit_available'];
                                } else {
                                    $creditAvailable = $businessesCampaign['credit_available'];
                                }

                                //$newdualslotpayoutwiseload[$campId] = CommonFunctions::getCampaignPayout($campId, $coverageType, 0, $call); //Comment By BK On 29/02/2024 as per discussion with MA and DS
                                $newdualslotpayoutwiseload[$campId] = $campaignPayoutArray[$campId] ?? 0;
                                if (!isset($routedDataArr[$leadId][$campId])) {
                                    $dataInsert[] = [
                                        'lead_id' => $leadId,
                                        'campaign_id' => $campId,
                                        'score' => $campScore,
                                        'payout' => $newdualslotpayoutwiseload[$campId],
                                        'route_status' => "unsold",
                                        'is_free_lead' => "no",
                                        'created_at' => date("Y-m-d H:i:s"),
                                        'balance_after_charge' => $creditAvailable,
                                        'payment_type' => $businessesCampaign['payment_type']
                                    ];
                                }
                            }
                            if (count($dataInsert) > 0) {
                                LeadRouting::insert($dataInsert);
                            }
                        }
                    }
                }
                if ($isPingPost == 0) {
                    $this->LeadRoutelog($leadId, 'remin slot :2 ==lead id and dual slot score camp ids = ' . json_encode($dualSlotCampaignIds), 47);
                }
                if ((isset($getLeadData[0]) && ($getLeadData[0]['sold_type'] <> 'premium') && isset($getLeadData[0]) && ($getLeadData[0]['sold_type'] <> 'trio')) || $call == 1 || $isPingPostType == 6) {
                    if ($exclusiveDualSlotStatus == 0) {
                        if (isset($getLeadData[0]) && ($getLeadData[0]['sold_type'] == 'dual') || $isPingPostType == 6) {
                            $top2 = array_slice($dualSlotCampaignIds, 0, $remainSlot, true);
                        } else {
                            $top2 = array_slice($dualSlotCampaignIds, 0, 2, true);
                        }
                    } else {
                        $top2 = array_slice($dualSlotCampaignIds, 0, 1, true);
                    }
                    if ($directRoute == 3) {
                        $top2 = array_slice($dualSlotCampaignIds, 0, $remainSlot, true);
                    }
                }
                if ($isPingPost == 0) {
                    $this->LeadRoutelog($leadId, 'remin slot :2 ==lead id and dual slot score win camp ids = ' . json_encode($top2), 48);
                }
                if ($isPingPost == 0) {
                    $this->LeadRoutelog($leadId, 'remin slot :2 ==lead id and dual slot payout camp ids = ' . json_encode($newdualslotpayoutwiseload), 49);
                }
                arsort($newdualslotpayoutwiseload);
                if (count($top2) > 0) {
                    foreach ($top2 as $campaignId => $score) {
                        /* if (in_array($campaignId, $pingPostIdArr)) {
                          $pingResponse = $pingPayoutPm->pingPrimeMarketing($getLeadData, $campaignId);
                          $leadPayout = 0;
                          if (isset($pingResponse['payout'])) {
                          $leadPayout = $pingResponse['payout'];
                          }
                          //echo "<pre>";print_R($leadPayout);die;
                          } else {
                          $leadPayout = CommonFunctions::getCampaignPayout($campaignId, $coverageType, 0, $call);
                          } */
                        //$leadPayout = CommonFunctions::getCampaignPayout($campaignId, $coverageType, 0, $call); //Comment By BK On 29/02/2024 as per discussion with MA and DS
                        $leadPayout = $campaignPayoutArray[$campaignId] ?? 0;
                        $dualSlotAmount += $leadPayout;
                        $newDualSlotAmount += $leadPayout;
                    }
                }
            }

            //Added By BK on 13/06/2025
            if (count($top2) > 0) {
                $dualSoldCampIdArray = array_keys($top2);
                foreach ($dualSoldCampIdArray as $key => $value)
                    if (!in_array($businessIdArr[$value], $dualSoldBusinessIdArray)) {
                        $dualSoldBusinessIdArray[] = $businessIdArr[$value];
                    }
            }
            if ($remainSlot == 4) {
                $dualRemainingSlot = (count($top2) < 2) ? 2 - count($top2) : 0;
            } else {
                $dualRemainingSlot = (count($top2) < 2) ? $remainSlot - count($top2) : 0;
            }
        }

        //$remainSlot = 4; //Remove this
        // For Exclusive
        if ($remainSlot == $leadRouteSlot && ($isPingPostType == 0 || $isPingPostType == 2)) { //$isPingPostType=2 (Exclusive)
            arsort($exclusiveCampaignIds);
            $newpayoutwiseload = $dataInsert = $top1NewArray = array();
            $newexclusiveAmount = $isWholesellerPing = $studioPing = $studioPayout = $studioCampId = $zapPing = $zapPayout = $zapCampId = $lsPremiumAmount = $lsSharedCampaignIds = $zapPremiumAmount = $zapDualAmount = $zapTrioAmount = $zapSharedCampaignIds = $zapDualCampaignIds = $zapTrioCampaignIds = 0;
            if (count($exclusiveCampaignIds) > 0) {
                // Store possible matches in lead_matches table
                if ($isPingPost == 0) {
                    if ($leadId > 0) {
                        foreach ($exclusiveCampaignIds as $campId => $campScore) {
                            /*if (in_array($campId, $pingPostIdArr)) {
                                $pingResponse = $pingPayoutPm->pingPrimeMarketing($getLeadData, $campId);
                                $leadPayout = 0;
                                if (isset($pingResponse['payout'])) {
                                    $leadPayout = $pingResponse['payout'];
                                }
                                $newpayoutwiseload[$campId] = $leadPayout;
                                //echo "<pre>";print_R($newpayoutwiseload);die;
                            } else {
                                $newpayoutwiseload[$campId] = CommonFunctions::getCampaignPayout($campId, $coverageType, 0, $call);
                            }*/

                            $leadPayout = 0;
                            //Added By BK On 07/06/2023 For Wholesaler Ping Post API Start
                            /*if (isset($campaignBuyerType[$campId]) && $campaignBuyerType[$campId] == 3 && $lead_category_id == 1 && $businessIdArr[$campId] == $studioBusinessId && $campTypeArr[$campId] == 2 && $campaignType != 4) {
                                $leasStudioResponse = $leadStudioObj->ping($getLeadData, $campId);
                                //echo "<pre>";print_r($leasStudioResponse);die;
                                if (isset($leasStudioResponse['payout']) && $leasStudioResponse['payout'] > 0) {
                                    $leadPayout = $leasStudioResponse['payout'];
                                }

                                if ($leadPayout > 2) {
                                    $newpayoutwiseload[$campId] = $leadPayout;
                                } else {
                                    unset($exclusiveCampaignIds[$campId]);
                                }
                            }*/
                            if(isset($campaignBuyerType[$campId]) && $campaignBuyerType[$campId] == 3 && $lead_category_id == 1 && $businessIdArr[$campId] == $studioBusinessId && $campTypeArr[$campId] == 2 && $campaignType != 4){
                                // Ping started to shared and exclusive in common Added By BK On 20/09/2023
                                $leasStudioResponse = $leadStudioObj->ping($getLeadData, $campId, [4, $premiumRemainingSlot], $premiumSoldBusinessIdArray);
                                //echo "<pre>";print_r($leasStudioResponse);die;
                                if(isset($leasStudioResponse['buying_slot_payout'][4]) && $leasStudioResponse['buying_slot_payout'][4] > 0){
                                    $leadPayout = $leasStudioResponse['buying_slot_payout'][4];
                                }

                                if (count($leasStudioResponse['buying_slot_payout']) > 0) { foreach ($leasStudioResponse['buying_slot_payout'] as $key => $value) { if ($key != 4) {
                                    if(isset($leasStudioResponse['buying_slot_payout'][$key]) && $leasStudioResponse['buying_slot_payout'][$key] > 0){
                                        // $premiumAmount += $leasStudioResponse['buying_slot_payout'][$key];
                                        // $newpremiumAmount += $leasStudioResponse['buying_slot_payout'][$key];
                                        //when win premium during this campaign id and score push in top4 bundle
                                        $sharedCampaignIds[$campId] = $campScore;
                                        $lsPremiumAmount = $leasStudioResponse['buying_slot_payout'][$key];
                                        $lsSharedCampaignIds = $campId;
                                    }
                                } } }

                                if($leadPayout > 2){
                                    $newpayoutwiseload[$campId] = $leadPayout;
                                    $isWholesellerPing = 1;
                                }else{
                                    unset($exclusiveCampaignIds[$campId]);
                                }
                            } else if(isset($campaignBuyerType[$campId]) && $campaignBuyerType[$campId] == 3 && $lead_category_id == 1 && $businessIdArr[$campId] == $zapBusinessId && $campTypeArr[$campId] == 2 && $campaignType != 4){
                                // Ping started to shared and exclusive in common Added By BK On 27/09/2023
                                $zapResponse = $zapObj->ping($getLeadData, $campId, [4, $dualRemainingSlot, $trioRemainingSlot, $premiumRemainingSlot], $dualSoldBusinessIdArray, $trioSoldBusinessIdArray, $premiumSoldBusinessIdArray);
                                //echo "<pre>";print_r($leasStudioResponse);die;
                                if(isset($zapResponse['buying_slot_payout']['exclusive'][4]) && $zapResponse['buying_slot_payout']['exclusive'][4] > 0){
                                    $leadPayout = $zapResponse['buying_slot_payout']['exclusive'][4];
                                }
                                /*if (count($zapResponse['buying_slot_payout']) > 0) { foreach ($zapResponse['buying_slot_payout'] as $key => $value) { if ($key != 4) {
                                    if(isset($zapResponse['buying_slot_payout'][$key]) && $zapResponse['buying_slot_payout'][$key] > 0){
                                        //when win premium during this campaign id and score push in top4 bundle
                                        $sharedCampaignIds[$campId] = $campScore;

                                        $zapPremiumAmount = $zapResponse['buying_slot_payout'][$key];
                                        $zapSharedCampaignIds = $campId;
                                    }
                                } } }*/

                                if (!empty($zapResponse['buying_slot_payout'])) {
                                    foreach ($zapResponse['buying_slot_payout'] as $slotType => $slotData) {
                                        foreach ($slotData as $slotId => $amount) {
                                            if ($amount > 0) {
                                                switch ($slotType) {
                                                    case 'shared':
                                                        $zapPremiumAmount = $amount;
                                                        $zapSharedCampaignIds = $campId;
                                                        $sharedCampaignIds[$campId] = $campScore;
                                                        break;

                                                    case 'dual':
                                                        $zapDualAmount = $amount;
                                                        $zapDualCampaignIds = $campId;
                                                        $dualCampaignIds[$campId] = $campScore;
                                                        break;

                                                    case 'trio':
                                                        $zapTrioAmount = $amount;
                                                        $zapTrioCampaignIds = $campId;
                                                        $trioCampaignIds[$campId] = $campScore;
                                                        break;
                                                }
                                            }
                                        }
                                    }
                                }
                                if($leadPayout > 2){
                                    $newpayoutwiseload[$campId] = $leadPayout;
                                    $isWholesellerPing = 1;
                                }else{
                                    unset($exclusiveCampaignIds[$campId]);
                                }
                            } else if(isset($campaignBuyerType[$campId]) && $campaignBuyerType[$campId] == 3 && $lead_category_id == 1 && $businessIdArr[$campId] == $moverJunctionBusinessId && $campTypeArr[$campId] == 2 && $campaignType != 4){
                                // Ping started to exclusive in common Added By BK On 28/07/2025
                                $moverJunctionResponse = $moverJunctionObj->ping($getLeadData, $campId);
                                $leadPayout = $moverJunctionResponse['price'] ?? 0;
                              
                                $newpayoutwiseload[$campId] = $leadPayout;
                            } else {
                                //$newpayoutwiseload[$campId] = CommonFunctions::getCampaignPayout($campId, $coverageType, 0, $call); //Comment By BK On 29/02/2024 as per discussion with MA and DS
                                $newpayoutwiseload[$campId] = $campaignPayoutArray[$campId] ?? 0;
                                $leadPayout = $newpayoutwiseload[$campId];
                            }

                            $businessesCampaign = $campAssocDataArr[$campId];
                            $creditAvailable = 0;
                            if ($businessesCampaign['payment_type'] == 0) {
                                $businessesData = $businessesDataArr[$businessesCampaign['business_id']];
                                $creditAvailable = $businessesData['credit_available'];
                            } else {
                                $creditAvailable = $businessesCampaign['credit_available'];
                            }

                            if (!isset($routedDataArr[$leadId][$campId])) {
                                $dataInsert[] = [
                                    'lead_id' => $leadId,
                                    'campaign_id' => $campId,
                                    'score' => $campScore,
                                    'payout' => ($leadPayout > 2) ? $newpayoutwiseload[$campId] : $leadPayout,
                                    'route_status' => "unsold",
                                    'is_free_lead' => "no",
                                    'created_at' => date("Y-m-d H:i:s"),
                                    'balance_after_charge' => $creditAvailable,
                                    'payment_type' => $businessesCampaign['payment_type']
                                ];
                            }

                            if ($businessIdArr[$campId] == $moverJunctionBusinessId) {
                                // Ping started to exclusive in common Added By BK On 28/07/2025
                                if ($coverageType == 'long' && $leadPayout >= 8) {
                                    $newpayoutwiseload[$campId] = $leadPayout;
                                    $isWholesellerPing = 1;
                                } else if ($coverageType == 'local' && $leadPayout >= 5) {
                                    $newpayoutwiseload[$campId] = $leadPayout;
                                    $isWholesellerPing = 1;
                                } else {
                                    unset($exclusiveCampaignIds[$campId]);
                                }
                            }
                        }
                        //echo "<pre>";print_r($dataInsert);die;
                        if (count($dataInsert) > 0) {
                            LeadRouting::insert($dataInsert);
                        }
                        //check ls and zap premium amount
                        if ($lsPremiumAmount > $zapPremiumAmount) {
                            $premiumAmount += $lsPremiumAmount;
                            $newpremiumAmount += $lsPremiumAmount;
                            unset($sharedCampaignIds[$zapSharedCampaignIds]);
                        } else if ($zapPremiumAmount > 0) {
                            $premiumAmount += $zapPremiumAmount;
                            $newpremiumAmount += $zapPremiumAmount;
                            unset($sharedCampaignIds[$lsSharedCampaignIds]);
                        }

                        if ($zapDualAmount > 0) {
                            $dualSlotAmount += $zapDualAmount;
                            $newDualSlotAmount += $zapDualAmount;
                        }

                        if ($zapTrioAmount > 0) {
                            $trioAmount += $zapTrioAmount;
                            $newtrioAmount += $zapTrioAmount;
                        }
                    }
                }

                $getRoutingData = LeadRouting::where('lead_id', $leadId)->get()->toArray();
                for ($fd = 0; $fd < count($getRoutingData); $fd++) {
                    //echo $getRoutingData[$fd]['campaign_id'];die;
                    $routedDataArr[$getRoutingData[$fd]['lead_id']][$getRoutingData[$fd]['campaign_id']] = $getRoutingData[$fd]['lead_routing_id'];
                }
            }
            if ($isPingPost == 0) {
                $this->LeadRoutelog($leadId, 'check exclusive score camp ids = ' . json_encode($exclusiveCampaignIds), 42);
            }

            //exclusive campaign payout wise win
            arsort($newpayoutwiseload);
            $top1New = array_slice($newpayoutwiseload, 0, 1, true);
            if (count($top1New) > 0) {
                $top1NewArray = array_keys($top1New);
            }

            arsort($exclusiveCampaignIds);
            if ($isWholesellerPing > 0) {
                $top1 = array_intersect_key($exclusiveCampaignIds, array_flip($top1NewArray));
            } else {
                $top1 = array_slice($exclusiveCampaignIds, 0, 1, true);
            }
            //Update Sore of  listed company in array
            if ($isPingPost == 0) {
                $this->LeadRoutelog($leadId, 'remin slot :1 ==lead id and exclusive score camp ids = ' . json_encode($top1), 43);
                $this->LeadRoutelog($leadId, 'check exclusive payout camp ids = ' . json_encode($newpayoutwiseload), 44);
            }

            arsort($newpayoutwiseload);
            $top1New = array_slice($newpayoutwiseload, 0, 1, true);
            if ($isPingPost == 0) {
                $this->LeadRoutelog($leadId, 'remin slot :1 ==lead id and exclusive payout camp ids = ' . json_encode($top1New), 45);
            }

            if (count($top1) > 0) {
                foreach ($top1 as $campaignId => $score) {
                    /*if (in_array($campaignId, $pingPostIdArr)) {
                        $pingResponse = $pingPayoutPm->pingPrimeMarketing($getLeadData, $campaignId);
                        $leadPayout = 0;
                        if (isset($pingResponse['payout'])) {
                            $leadPayout = $pingResponse['payout'];
                        }
                        //echo "<pre>";print_R($leadPayout);die;
                    } else {
                        $leadPayout = CommonFunctions::getCampaignPayout($campaignId, $coverageType, 0, $call);
                    }*/

                    //Added By BK On 07/06/2023 For Wholesaler Ping Post API Start
                    /*if (isset($campaignBuyerType[$campaignId]) && $campaignBuyerType[$campaignId] == 3 && $lead_category_id == 1 && $businessIdArr[$campaignId] == $studioBusinessId && $campTypeArr[$campaignId] == 2 && $campaignType != 4) {
                        $leasStudioResponse = $leadStudioObj->ping($getLeadData, $campaignId);
                        $leadPayout = 0;
                        if (isset($leasStudioResponse['payout']) && $leasStudioResponse['payout'] > 0) {
                            $leadPayout = $leasStudioResponse['payout'];
                        }
                        $studioCampId = $campaignId;
                        $studioPayout = $leadPayout;
                        //For Lead route to Studio Campaign If Payout > 2 Start
                        if ($studioPayout > 2) {
                            $studioPing = 1;
                        }
                    }*/
                    if(isset($campaignBuyerType[$campaignId]) && $campaignBuyerType[$campaignId] == 3 && $lead_category_id == 1 && $businessIdArr[$campaignId] == $studioBusinessId && $campTypeArr[$campaignId] == 2 && $campaignType != 4){
                        // Ping started to shared and exclusive in common Added By BK On 20/09/2023
                        $leasStudioResponse = $leadStudioObj->ping($getLeadData, $campaignId, [4, $premiumRemainingSlot], $premiumSoldBusinessIdArray);
                        //echo "<pre>";print_r($leasStudioResponse);die;
                        if(isset($leasStudioResponse['buying_slot_payout'][4]) && $leasStudioResponse['buying_slot_payout'][4] > 0){
                            $leadPayout = $leasStudioResponse['buying_slot_payout'][4];
                        }

                        $studioCampId = $campaignId;
                        $studioPayout = $leadPayout;
                        //For Lead route to Studio Campaign If Payout > 2 Start
                        if ($studioPayout > 2){
                            $studioPing = 1;
                        }
                    } else if(isset($campaignBuyerType[$campaignId]) && $campaignBuyerType[$campaignId] == 3 && $lead_category_id == 1 && $businessIdArr[$campaignId] == $zapBusinessId && $campTypeArr[$campaignId] == 2 && $campaignType != 4){
                        // Ping started to shared and exclusive in common Added By BK On 20/09/2023
                        $zapResponse = $zapObj->ping($getLeadData, $campaignId, [4, $dualRemainingSlot, $trioRemainingSlot, $premiumRemainingSlot], $dualSoldBusinessIdArray, $trioSoldBusinessIdArray, $premiumSoldBusinessIdArray);
                        //echo "<pre>";print_r($leasStudioResponse);die;
                        if(isset($zapResponse['buying_slot_payout']['exclusive'][4]) && $zapResponse['buying_slot_payout']['exclusive'][4] > 0){
                            $leadPayout = $zapResponse['buying_slot_payout']['exclusive'][4];
                        }

                        $zapCampId = $campaignId;
                        $zapPayout = $leadPayout;
                        //For Lead route to Studio Campaign If Payout > 2 Start
                        if ($zapPayout > 2){
                            $zapPing = 1;
                        }
                    } else if(isset($campaignBuyerType[$campaignId]) && $campaignBuyerType[$campaignId] == 3 && $lead_category_id == 1 && $businessIdArr[$campaignId] == $moverJunctionBusinessId && $campTypeArr[$campaignId] == 2 && $campaignType != 4){
                        // Ping started to exclusive in common Added By BK On 28/07/2025
                        $moverJunctionResponse = $moverJunctionObj->ping($getLeadData, $campaignId);
                        $leadPayout = $moverJunctionResponse['price'] ?? 0;
                    } else {
                        //$leadPayout = CommonFunctions::getCampaignPayout($campaignId, $coverageType, 0, $call); //Comment By BK On 29/02/2024 as per discussion with MA and DS
                        $leadPayout = $campaignPayoutArray[$campaignId] ?? 0;
                    }

                    $newexclusiveAmount += $leadPayout;
                }
            }

            if ($studioPing > 0 && $studioPayout <= 0) {
                if (isset($exclusiveCampaignIds[$studioCampId])) {
                    unset($exclusiveCampaignIds[$studioCampId]);
                    arsort($exclusiveCampaignIds);
                    $top1 = array_slice($exclusiveCampaignIds, 0, 1, true);
                    //Update Sore of listed company in array
                    arsort($newpayoutwiseload);
                    $top1New = array_slice($newpayoutwiseload, 0, 1, true);
                    //echo "<pre>";print_r($top1New);die;
                    $newexclusiveAmount = $studioPing = $studioPayout = $studioCampId = 0;
                    //echo "<pre>dd";print_r($top1);die;
                    if (count($top1) > 0) {
                        foreach ($top1 as $campaignId => $score) {
                            //$leadPayout = CommonFunctions::getCampaignPayout($campaignId, $coverageType, 0, $call); //Comment By BK On 29/02/2024 as per discussion with MA and DS
                            $leadPayout = $campaignPayoutArray[$campaignId] ?? 0;
                            $newexclusiveAmount += $leadPayout;
                        }
                    }
                }
            }else if ($zapPing > 0 && $zapPayout <= 0) {
                $this->LeadRoutelog($leadId, 'zapPing>0= ' . $zapPing. ', zapPayout<=0 = ' . $zapPayout, 50);
                if (isset($exclusiveCampaignIds[$zapCampId])) {
                    unset($exclusiveCampaignIds[$zapCampId]);
                    arsort($exclusiveCampaignIds);
                    $top1 = array_slice($exclusiveCampaignIds, 0, 1, true);
                    //Update Sore of listed company in array
                    arsort($newpayoutwiseload);
                    $top1New = array_slice($newpayoutwiseload, 0, 1, true);
                    //echo "<pre>";print_r($top1New);die;
                    $newexclusiveAmount = $zapPing = $zapPayout = $zapCampId = 0;
                    //echo "<pre>dd";print_r($top1);die;
                    if (count($top1) > 0) {
                        foreach ($top1 as $campaignId => $score) {
                            //$leadPayout = CommonFunctions::getCampaignPayout($campaignId, $coverageType, 0, $call); //Comment By BK On 29/02/2024 as per discussion with MA and DS
                            $leadPayout = $campaignPayoutArray[$campaignId] ?? 0;
                            $newexclusiveAmount += $leadPayout;
                        }
                    }
                }
            }
        }

        //For Custom Slot
        if ($remainSlot == $leadRouteSlot) {
            arsort($customCampaignIds);
            if (count($customCampaignIds) > 0) {
                // Store possible matches in lead_matches table
                if ($isPingPost == 0) {
                    $bundleIdArray = [];
                    $customkeys = array_keys($customCampaignIds);
                    $bundleDetail = CampaignCustom::whereIn('primary_campaign_id', $customkeys)->get()->toArray();
                    if (count($bundleDetail) > 0) {
                        foreach ($bundleDetail as $bundle) {
                            $bundleIdArray[$bundle['primary_campaign_id']] = $bundle['campaign_id'];
                        }
                    }

                    if ($leadId > 0) {
                        $dataInsert = $bundleAmountArray = array();
                        foreach ($customCampaignIds as $campId => $campScore) {
                            $businessesCampaign = $campAssocDataArr[$campId];
                            $creditAvailable = 0;
                            if ($businessesCampaign['payment_type'] == 0) {
                                $businessesData = $businessesDataArr[$businessesCampaign['business_id']];
                                $creditAvailable = $businessesData['credit_available'];
                            } else {
                                $creditAvailable = $businessesCampaign['credit_available'];
                            }

                            if (!isset($routedDataArr[$leadId][$campId])) {
                                $dataInsert[] = [
                                    'lead_id' => $leadId,
                                    'campaign_id' => $campId,
                                    'score' => $campScore,
                                    'payout' => $campaignPayoutArray[$campId] ?? 0,
                                    'route_status' => "unsold",
                                    'is_free_lead' => "no",
                                    'created_at' => date("Y-m-d H:i:s"),
                                    'balance_after_charge' => $creditAvailable,
                                    'payment_type' => $businessesCampaign['payment_type']
                                ];
                            }

                            if (isset($bundleIdArray[$campId])) {
                                $bundleId = explode(',', $bundleIdArray[$campId]);
                                $this->LeadRoutelog($leadId, 'check bundleId := ' . json_encode($bundleId), 107);
                                $bundleAmountArray[$campId] = count($bundleId) * $campaignPayoutArray[$campId] ?? 0;
                            }
                        }
                        if (count($dataInsert) > 0) {
                            LeadRouting::insert($dataInsert);
                        }
                        $this->LeadRoutelog($leadId, 'check Custom := ' . json_encode($bundleAmountArray), 107);
                        if (count($bundleAmountArray) > 0) {
                            $maxIndex = array_search(max($bundleAmountArray), $bundleAmountArray);
                            if (isset($bundleIdArray[$maxIndex])) {
                                foreach ($customCampaignIds as $campId => $campScore) {
                                    $bundleId = explode(',', $bundleIdArray[$maxIndex]);
                                    if (!in_array($campId, $bundleId)) {
                                        unset($customCampaignIds[$campId]);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            $custom = array_slice($customCampaignIds, 0, count($customCampaignIds), true);
            if (count($custom) > 0) {
                foreach ($custom as $campaignId => $score) {
                    $leadPayout = $campaignPayoutArray[$campaignId] ?? 0;
                    $customAmount += $leadPayout;
                    $newcustomAmount += $leadPayout;
                }
            }
        }

        $finalCampaignIds = array();
        $exclusiveextra_amount_ex = $dualslotextra_amount_ex = $trioextra_amount_ex = $customextra_amount_ex = 0;
        //Extra payout checking only for Moving category
        if ($lead_category_id == 1) {
            $settingObj = new SettingController();
            if ($newexclusiveAmount > 0) {
                $exclusiveextra_amount_ex = $settingObj->bidAdjustlogic('exclusive');
                $newexclusiveAmount += $exclusiveextra_amount_ex;
            }
            if ($newDualSlotAmount > 0) {
                $dualslotextra_amount_ex = $settingObj->bidAdjustlogic('dual');
                $newDualSlotAmount += $dualslotextra_amount_ex;
            }
            if ($newtrioAmount > 0) {
                $trioextra_amount_ex = $settingObj->bidAdjustlogic('trio');
                $newtrioAmount += $trioextra_amount_ex;
            }
            if ($newcustomAmount > 0) {
                $customextra_amount_ex = $settingObj->bidAdjustlogic('custom');
                $newcustomAmount += $customextra_amount_ex;
            }
        }
        $is_verified = 1; //Remain to get now static
        if ($isPingPost == 0) {
            $this->LeadRoutelog($leadId, 'premium amount = ' . $newpremiumAmount . ' and dual slot amount = ' . $newDualSlotAmount . ' and exclusive amount = ' . $newexclusiveAmount . ' and trio amount = ' . $newtrioAmount . ' and custom amount = ' . $newcustomAmount . ' and verified lead = ' . $is_verified, 51);
        }
        $isSharedWin = 0;
        if ($newexclusiveAmount > 0) {
            if ($newcustomAmount >= $newexclusiveAmount && $newcustomAmount >= $newtrioAmount && $newcustomAmount >= $newDualSlotAmount && $newcustomAmount >= $newpremiumAmount) {
                $nonWsBuyerAmount = $newcustomAmount;
                $finalCampaignIds = $custom;
                // For Exclusive Slot
            } else if ($newexclusiveAmount > $newpremiumAmount && $newexclusiveAmount > $newDualSlotAmount && $newexclusiveAmount > $newtrioAmount && $newexclusiveAmount > $newcustomAmount) {
                $nonWsBuyerAmount = $newexclusiveAmount;
                $finalCampaignIds = $top1;
                // For Trio Slot
            } else if ($newtrioAmount > $newpremiumAmount && $newtrioAmount >= $newexclusiveAmount && $newtrioAmount >= $newDualSlotAmount && $newtrioAmount >= $newcustomAmount) {
                $nonWsBuyerAmount = $newtrioAmount;
                $finalCampaignIds = $top3;
                $isSharedWin = 3;
                // For Dual Slot
            } else if ($newDualSlotAmount >= $newexclusiveAmount && $newDualSlotAmount > $newpremiumAmount && $newDualSlotAmount > $newtrioAmount && $newDualSlotAmount >= $newcustomAmount) {
                $nonWsBuyerAmount = $newDualSlotAmount;
                $finalCampaignIds = $top2;
                $isSharedWin = 2;
            } else {
                $nonWsBuyerAmount = $premiumAmount;
                $finalCampaignIds = $top4;
                $isSharedWin = 4;
            }
        } else {
            if ($newcustomAmount >= $newexclusiveAmount && $newcustomAmount >= $newtrioAmount && $newcustomAmount >= $newDualSlotAmount && $newcustomAmount >= $newpremiumAmount) {
                $nonWsBuyerAmount = $newcustomAmount;
                $finalCampaignIds = $custom;
                // For Exclusive Slot
            } else if ($newexclusiveAmount > $newpremiumAmount && $newexclusiveAmount > $newDualSlotAmount && $newexclusiveAmount > $newtrioAmount && $newexclusiveAmount > $newcustomAmount) {
                $nonWsBuyerAmount = $newexclusiveAmount;
                $finalCampaignIds = $top1;
                // For Trio Slot
            } else if ($newtrioAmount > $newpremiumAmount && $newtrioAmount >= $newexclusiveAmount && $newtrioAmount >= $newDualSlotAmount && $newtrioAmount >= $newcustomAmount) {
                $nonWsBuyerAmount = $newtrioAmount;
                $finalCampaignIds = $top3;
                $isSharedWin = 3;
                // For Dual Slot
            } else if ($newDualSlotAmount >= $newexclusiveAmount && $newDualSlotAmount > $newpremiumAmount && $newDualSlotAmount > $newtrioAmount && $newDualSlotAmount >= $newcustomAmount) {
                $nonWsBuyerAmount = $newDualSlotAmount;
                $finalCampaignIds = $top2;
                $isSharedWin = 2;
            } else {
                $nonWsBuyerAmount = $premiumAmount;
                $finalCampaignIds = $top4;
                $isSharedWin = 4;
            }
        }
        //echo "<pre>";print_r($finalCampaignIds);die;

        //While routing local leads, do not check for condition of $15 for Rexdirect if we have campaign of moving Ally in final selected group of campaign
        //code comment as per discuss with DS
        /*if (is_array($finalCampaignIds) && count($finalCampaignIds) > 0) {
            foreach ($finalCampaignIds as $campaignId => $campaignScore) {
                if (in_array($campaignId, $movingAllyCampaignIdArray)) {
                    $isMovingAllyCampaign = 1;
                    break;
                }
            }
        }*/
        if ($isSharedWin == 2) {
            if (count($dualCampaignIds) > 0) { foreach ($dualCampaignIds as $campaignId => $campaignScore) {
                $studioCampId = $campaignId;
                $zapCampId = $campaignId;
                $finalCampaignIds[$campaignId] = $campaignScore;
            } }
        }
        if ($isSharedWin == 3) {
            if (count($trioCampaignIds) > 0) { foreach ($trioCampaignIds as $campaignId => $campaignScore) {
                $studioCampId = $campaignId;
                $zapCampId = $campaignId;
                $finalCampaignIds[$campaignId] = $campaignScore;
            } }
        }
        if ($isSharedWin == 4) {
            if (count($sharedCampaignIds) > 0) { foreach ($sharedCampaignIds as $campaignId => $campaignScore) {
                $studioCampId = $campaignId;
                $zapCampId = $campaignId;
                $finalCampaignIds[$campaignId] = $campaignScore;
            } }
        }
        //echo "<pre>";print_r($finalCampaignIds);die;

        if ($isPingPost == 0) {
            $this->LeadRoutelog($leadId, 'lead id and ' . $call . ': call and win campaign ids ' . json_encode($finalCampaignIds), 52);
        }
        $campaignSortBundle = array();
        if ($call == 1) { // This is pending for call
            DB::table('dev_debug')->insert(['sr_no' => 52, 'result' => "matchedcallcampaigns call= " . $call, 'created_at' => date('Y-m-d H:i:s')]);
            foreach ($finalCampaignIds as $campaignIdKey => $score) {
                if ($cust_number != "" && $cust_dial_number != "") {
                    $previous_LPcallForward = $this->LeadCall_Donot_reapeat_customer_plivonumber($cust_number, $cust_dial_number, $campaignIdKey);
                    DB::table('dev_debug')->insert(['sr_no' => 52, 'result' => "matchedcallcampaigns Donot_reapeat_plivonumber 1 = " . $previous_LPcallForward, 'created_at' => date('Y-m-d H:i:s')]);
                    // echo "<pre>";print_r($previous_LPcallForward);
                    if ($previous_LPcallForward == 1) {
                        if ($isPingPost == 0) {
                            $this->LeadRoutelog($leadId, 'Donot_reapeat_plivonumber' . $campaignIdKey, 37);
                            DB::table('dev_debug')->insert(['sr_no' => 53, 'result' => "matchedcallcampaigns Donot_reapeat_plivonumber 2= " . $campaignIdKey, 'created_at' => date('Y-m-d H:i:s')]);
                        }
                        continue;
                    }
                    $previous_LPcallForward_dublicate_customer = $this->LeadCall_Donot_reapeat_customer_number($cust_number, $cust_dial_number, $campaignIdKey);
                    DB::table('dev_debug')->insert(['sr_no' => 54, 'result' => "matchedcallcampaigns Donot_reapeat_customer 2= " . $previous_LPcallForward_dublicate_customer, 'created_at' => date('Y-m-d H:i:s')]);
                    if ($previous_LPcallForward_dublicate_customer == 1) {
                        if ($isPingPost == 0) {
                            $this->LeadRoutelog($leadId, 'Donot_reapeat_customer' . $campaignIdKey, 38);
                            DB::table('dev_debug')->insert(['sr_no' => 55, 'result' => "matchedcallcampaigns Donot_reapeat_customer 2= " . $campaignIdKey, 'created_at' => date('Y-m-d H:i:s')]);
                        }
                        continue;
                    }
                }
                $campaign = Campaign::with(['campaignCallPayout'])->where('campaign_id', $campaignIdKey)->first()->toArray();
                DB::table('dev_debug')->insert(['sr_no' => 57, 'result' => "call campaign data= " . json_encode($campaign), 'created_at' => date('Y-m-d H:i:s')]);
                $campaign_active_call = CampaignOngoingCall::where('campaign_id', $campaignIdKey)->count();
                if (count($campaign['campaign_call_payout']) > 0) {
                    $call_limit = $campaign['campaign_call_payout'][0]['real_time_call_limit'];
                    if ($campaign_active_call >= $call_limit && $call_limit != 0) {
                        if ($isPingPost == 0) {
                            $this->LeadRoutelog($leadId, 'Call limit' . $campaignIdKey, 39);
                            DB::table('dev_debug')->insert(['sr_no' => 57, 'result' => "call campaign limit= " . $campaignIdKey . "===" . json_encode($campaign_active_call), 'created_at' => date('Y-m-d H:i:s')]);
                        }
                        continue;
                    }
                }
                $businessesData = Business::where('business_id', $campaign['business_id'])->get(['credit_available','payment_type'])->first();
                DB::table('dev_debug')->insert(['sr_no' => 58, 'result' => "call business data= " . $campaignIdKey . "===" . json_encode($businessesData), 'created_at' => date('Y-m-d H:i:s')]);
                $leadPayout = CommonFunctions::getCampaignPayout($campaignIdKey, $coverageType, 0, $call);
                if ($businessesData->payment_type == "post") {
                    if ($leadPayout > 0) {
                        if ($campaign['payment_type'] == 0) {
                            if ($isPingPost == 0) {
                                DB::select("UPDATE `business` set `credit_available`=(`credit_available`-$leadPayout) where `business_id`='" . $campaign['business_id'] . "'");
                            }
                        } else {
                            if ($isPingPost == 0) {
                                DB::select("UPDATE `campaign` set `credit_available`=(`credit_available`-$leadPayout) where `campaign_id`='" . $campaignIdKey . "'");
                            }
                        }
                    }
                    $campaignSortBundle[$campaignIdKey]['score'] = $score;
                    $campaignSortBundle[$campaignIdKey]['payout'] = $leadPayout;
                    $campaignSortBundle[$campaignIdKey]['free_lead'] = 0;
                    break;
                } else {
                    if ($campaign['payment_type'] == 0) {
                        $creditAvailable = $businessesData->credit_available;
                        if ($isPingPost == 0) {
                            $this->LeadRoutelog($leadId, 'Businesses Before blance :' . $creditAvailable, 40);
                        }
                    } else {
                        $creditAvailable = $campaign['credit_available'];
                        if ($isPingPost == 0) {
                            $this->LeadRoutelog($leadId, 'BusinessesCampaign Before blance :' . $creditAvailable, 41);
                        }
                    }
                    DB::table('dev_debug')->insert(['sr_no' => 58, 'result' => "call campaign creditAvailable= " . $creditAvailable . "===" . $leadPayout, 'created_at' => date('Y-m-d H:i:s')]);
                    if ($creditAvailable >= $leadPayout) {
                        $applyFreeLead = 0;
                        if ($campaign['free_lead'] > 0) {
                            //Added By HJ On 07-12-2021 For Check Campaign Free Lead Count with Balance As Per Discuss with MV Start
                            $applyFreeLead = $this->checkCampaingBalance($campaignIdKey, $leadPayout, $creditAvailable);
                            if ($applyFreeLead > 0) {
                                $leadPayout = 0;
                                $leftFreeLead = $campaign['free_lead'] - 1;
                                Campaign::where('campaign_id', $campaignIdKey)->update(['free_lead' => $leftFreeLead]);
                            }
                            //Added By HJ On 07-12-2021 For Check Campaign Free Lead Count with Balance As Per Discuss with MV End
                        }
                        if ($leadPayout > 0) {
                            if ($campaign['payment_type'] == 0) {
                                $creditAvailable -= $leadPayout;
                                if ($isPingPost == 0) {
                                    Business::where('business_id', $campaign['business_id'])->update(['credit_available' => $creditAvailable]);
                                }
                            } else {
                                $creditAvailable -= $leadPayout;
                                if ($isPingPost == 0) {
                                    Campaign::where('campaign_id', $campaignIdKey)->update(['credit_available' => $creditAvailable]);
                                }
                            }
                        }
                        if ($isPingPost == 0) {
                            $this->LeadRoutelog($leadId, 'After blance :' . "==" . $campaignIdKey . "==" . $creditAvailable . "===" . $leadPayout . "===" . $campaign['payment_type'] . "==" . $applyFreeLead, 42);
                        }
                        $campaignSortBundle[$campaignIdKey]['score'] = $score;
                        $campaignSortBundle[$campaignIdKey]['payout'] = $leadPayout;
                        $campaignSortBundle[$campaignIdKey]['free_lead'] = $applyFreeLead;
                        break;
                    }
                }
            }
            if ($isPingPost == 0) {
                $this->LeadRoutelog($leadId, 'bundle win campaign ids ' . json_encode($campaignSortBundle), 43);
            }
            //echo "<pre>";print_R($leadPayout);die;

            return $campaignSortBundle;
        }

        $rexDirectPayout = $rexDirectCamapignId = $etnAmericaPayout = $etnAmericaCamapignId = $wholesellerCamapignId = $wholesellerPingPost = 0;
        $wholesellerBusinessIdArray = $wholesellerCampaignIdArray = array();
        if ($nonWsBuyerAmount < 15 && $campaignType != 4 && $lead_category_id == 1 && $isPingPost == 0) {
            if ($remainSlot == 4 && $directRoute < 4) {
                $businessCampaignDetail = $this->activeCamapignWholeseller($coverageType, 1, $getLeadData[0]['is_verified'], $leadId);
                $this->LeadRoutelog($leadId, 'lead id and call id ' . $call . ' business id ' . json_encode($businessCampaignDetail), 100);
                /*if (is_array($businessCampaignDetail) && count($businessCampaignDetail) > 0) { foreach ($businessCampaignDetail as $business) {*/
                    /*foreach ($businessCampaign->businessCampaign as $business) {*/
                        /*if ($business->business_id == 55) {
                            $rexDirectResponse = $rexDirectObj->ping($getLeadData, $business->campaign_id);
                            $this->LeadRoutelog($leadId, 'lead id and call id ' . $call . ' rexdirect response ' . json_encode($rexDirectResponse), 101);
                            if (isset($rexDirectResponse['price']) && $rexDirectResponse['price'] > 0) {
                                $rexDirectPayout = $rexDirectResponse['price'];
                                $rexDirectCamapignId = $business->campaign_id;
                            }
                        }*/

                        /*if ($business->business_id == 992) {
                            $etnAmericaResponse = $etnAmericaObj->ping($getLeadData, $business->campaign_id);
                            $this->LeadRoutelog($leadId, 'lead id and call id ' . $call . ' etnamerica response ' . json_encode($etnAmericaResponse), 101);
                            if (isset($etnAmericaResponse['price']) && $etnAmericaResponse['price'] > 0) {
                                $etnAmericaPayout = $etnAmericaResponse['price'];
                                $etnAmericaCamapignId = $business->campaign_id;
                            }
                        }*/
                    /*}*/
                /*} }*/

                if (is_array($businessCampaignDetail) && count($businessCampaignDetail) > 0) { foreach ($businessCampaignDetail as $business) {
                    if (!in_array($business->business_id, $wholesellerBusinessIdArray)) {
                        $wholesellerBusinessIdArray[] = $business->business_id;
                        $wholesellerCampaignIdArray[$business->business_id] = $business->campaign_id;
                    }
                } }

                if (!empty($wholesellerBusinessIdArray) && in_array(55, $wholesellerBusinessIdArray)) {
                    $rexDirectResponse = $rexDirectObj->ping($getLeadData, $wholesellerCampaignIdArray[55]);
                    $this->LeadRoutelog($leadId, 'lead id and call id ' . $call . ' rexdirect response ' . json_encode($rexDirectResponse), 101);
                    if (isset($rexDirectResponse['price']) && $rexDirectResponse['price'] > 0) {
                        $rexDirectPayout = $rexDirectResponse['price'];
                        $rexDirectCamapignId = $wholesellerCampaignIdArray[55];
                    }
                }

                if (!empty($wholesellerBusinessIdArray) && in_array(992, $wholesellerBusinessIdArray)) {
                    $etnAmericaResponse = $etnAmericaObj->ping($getLeadData, $wholesellerCampaignIdArray[992]);
                    $this->LeadRoutelog($leadId, 'lead id and call id ' . $call . ' etnamerica response ' . json_encode($etnAmericaResponse), 101);
                    if (isset($etnAmericaResponse['price']) && $etnAmericaResponse['price'] > 0) {
                        $etnAmericaPayout = $etnAmericaResponse['price'];
                        $etnAmericaCamapignId = $wholesellerCampaignIdArray[992];
                    }
                }

                //Added By BK On 01/02/2024 For Stop Wholesaler Ping Post API
                if ($wholesellerPingPost > 0) {
                    $successFlag = 1;
                    goto L1;
                }
                if ($rexDirectPayout > $etnAmericaPayout) {
                    if ($rexDirectPayout > $nonWsBuyerAmount) {
                        if (isset($finalCampaignIds[$studioCampId])) unset($finalCampaignIds[$studioCampId]);
                        if (isset($finalCampaignIds[$zapCampId])) unset($finalCampaignIds[$zapCampId]);
                        $wholesellerCamapignId = $rexDirectCamapignId;
                    }
                } else {
                    if ($etnAmericaPayout > $nonWsBuyerAmount) {
                        $notSoldWholeseller = 0;
                        if ($coverageType == 'long' && isset($getLeadData) && $getLeadData[0]['is_verified'] == 'yes' && $etnAmericaPayout < 15) {
                            $notSoldWholeseller = 1;
                        }
                        if ($coverageType == 'local' && isset($getLeadData) && $getLeadData[0]['is_verified'] == 'yes' && $etnAmericaPayout < 7) {
                            $notSoldWholeseller = 1;
                        }
                        if ($coverageType == 'long' && isset($getLeadData) && $getLeadData[0]['is_verified'] == 'no' && $etnAmericaPayout < 7) {
                            $notSoldWholeseller = 1;
                        }
                        if ($coverageType == 'local' && isset($getLeadData) && $getLeadData[0]['is_verified'] == 'no' && $etnAmericaPayout < 5) {
                            $notSoldWholeseller = 1;
                        }

                        if ($notSoldWholeseller == 0) {
                            if (isset($finalCampaignIds[$studioCampId])) unset($finalCampaignIds[$studioCampId]);
                            if (isset($finalCampaignIds[$zapCampId])) unset($finalCampaignIds[$zapCampId]);
                            $wholesellerCamapignId = $etnAmericaCamapignId;
                        }
                    }
                }
            }
        }

        if ($call == 0) {
            $successFlag = 0;
            //Added By BK On 07/06/2023 For LeadStudio Wholesaler Ping Post API Start
            foreach ($finalCampaignIds as $wsCampaignId => $wsCampaignScore) {
                if (isset($campaignBuyerType[$wsCampaignId]) && $campaignBuyerType[$wsCampaignId] == 3 && $lead_category_id == 1 && $campTypeArr[$wsCampaignId] == 2) {
                    $leadType = "exclusive";
                    if ($isSharedWin == 2) {
                        $leadType = "dual";
                    }
                    if ($isSharedWin == 3) {
                        $leadType = "trio";
                    }
                    if ($isSharedWin == 4) {
                        $leadType = "shared";
                    }

                    $wsPayoutData = PingPostPayout::where('lead_id', $leadId)->where('campaign_id', $wsCampaignId)->where('ping_type', $leadType)->where('is_request', 'ping')->orderBy('payout', 'DESC')->get(['campaign_id', 'payout'])->toArray();
                    $wsCampaignPayout = 0;
                    if (count($wsPayoutData) > 0) {
                        $wsCampaignPayout = $wsPayoutData[0]['payout'];
                        //$wsCampaignPayout = 1;
                    }

                    //All Long-Distance leads $32 & All Local-Distance leads $10
                    /*$wsCampaignPayout = 10;
                    if ($coverageType > 0) {
                        $wsCampaignPayout = 32;
                    }*/

                    $businessesCampaign = $campAssocDataArr[$wsCampaignId];
                    if ($businessesCampaign['payment_type'] == 0) {
                        if (isset($businessesDataArr[$businessesCampaign['business_id']])) {
                            $businessesData = $businessesDataArr[$businessesCampaign['business_id']];
                        }
                        $creditAvailable = $businessesData['credit_available'];
                    } else {
                        $creditAvailable = $businessesCampaign['credit_available'];
                    }

                    if ($businessIdArr[$wsCampaignId] == $studioBusinessId) {
                        if ($leadStudioObj->post($getLeadData, $wsCampaignId, $leadType) && $campaignType != 4) {
                            if (isset($campAssocDataArr[$wsCampaignId])) {

                                // Reduce Amount from businesses_campaign table
                                /*$businessesCampaign = $campAssocDataArr[$wsCampaignId];
                                if ($businessesCampaign['payment_type'] == 0) {
                                    if (isset($businessesDataArr[$businessesCampaign['business_id']])) {
                                        $businessesData = $businessesDataArr[$businessesCampaign['business_id']];
                                    }
                                    $creditAvailable  = $businessesData['credit_available'];
                                    $creditAvailable -= $wsCampaignPayout;
                                    DB::select("UPDATE `business` set `credit_available`=(`credit_available`-$wsCampaignPayout) where `business_id`='" . $businessesCampaign['business_id'] . "'");
                                    //Business::where('business_id', $businessesCampaign['business_id'])->update(['credit_available' => $creditAvailable]);
                                } else {
                                    $creditAvailable  = $businessesCampaign['credit_available'];
                                    $creditAvailable -= $wsCampaignPayout;
                                    DB::select("UPDATE `campaign` set `credit_available`=(`credit_available`-$wsCampaignPayout) where `campaign_id`='" . $wsCampaignId . "'");
                                    //Campaign::where('campaign_id', $wsCampaignId)->update(['credit_available' => $creditAvailable]);
                                }*/
                                if ($businessesCampaign['payment_type'] == 0) {
                                    $creditAvailable -= $wsCampaignPayout;
                                    DB::select("UPDATE `business` set `credit_available`=(`credit_available`-$wsCampaignPayout) where `business_id`='" . $businessesCampaign['business_id'] . "'");
                                    //Business::where('business_id', $businessesCampaign['business_id'])->update(['credit_available' => $creditAvailable]);
                                } else {
                                    $creditAvailable -= $wsCampaignPayout;
                                    DB::select("UPDATE `campaign` set `credit_available`=(`credit_available`-$wsCampaignPayout) where `campaign_id`='" . $wsCampaignId . "'");
                                    //Campaign::where('campaign_id', $wsCampaignId)->update(['credit_available' => $creditAvailable]);
                                }
                                /*LeadRouting::create([
                                    'lead_id' => $leadId,
                                    'campaign_id' => $wsCampaignId,
                                    'score' => $wsCampaignScore,
                                    'payout' => $wsCampaignPayout,
                                    'route_status' => "sold",
                                    'is_free_lead' => "no",
                                    'created_at' => date("Y-m-d H:i:s"),
                                    'balance_after_charge' => $creditAvailable,
                                    'payment_type' => $businessesCampaign['payment_type']
                                ]);*/

                                LeadRouting::where('lead_id', $leadId)->where('campaign_id', $wsCampaignId)->update([
                                    'score' => $wsCampaignScore,
                                    'payout' => $wsCampaignPayout,
                                    'route_status' => "sold",
                                    'is_free_lead' => "no",
                                    'balance_after_charge' => $creditAvailable,
                                    'payment_type' => $businessesCampaign['payment_type']
                                ]);

                                if ($isSharedWin > 0) {
                                    Lead::where('lead_id', $leadId)->update(['sold_type'=>'premium']);
                                    unset($finalCampaignIds[$wsCampaignId]);

                                    $successFlag = 0;
                                    goto L1;
                                } else {
                                    Lead::where('lead_id', $leadId)->update(['sold_type'=>'exclusive']);
                                    $successFlag = 1;
                                    goto L1;
                                }
                            }
                        } else {
                            /*LeadRouting::create([
                                'lead_id' => $leadId,
                                'campaign_id' => $wsCampaignId,
                                'score' => $wsCampaignScore,
                                'payout' => 0,
                                'route_status' => "unsold",
                                'is_free_lead' => "no",
                                'created_at' => date("Y-m-d H:i:s"),
                                'balance_after_charge' => $creditAvailable,
                                'payment_type' => $businessesCampaign['payment_type']
                            ]);*/
                            if ($isSharedWin > 0) {
                                unset($finalCampaignIds[$wsCampaignId]);

                                $successFlag = 0;
                                goto L1;
                            } else {
                                $successFlag = 1;
                                goto L1;
                            }
                        }
                    } else if ($businessIdArr[$wsCampaignId] == $zapBusinessId) {
                        if ($zapObj->post($getLeadData, $wsCampaignId, $leadType) && $campaignType != 4) {
                            if (isset($campAssocDataArr[$wsCampaignId])) {

                                // Reduce Amount from businesses_campaign table
                                /*$businessesCampaign = $campAssocDataArr[$wsCampaignId];
                                if ($businessesCampaign['payment_type'] == 0) {
                                    if (isset($businessesDataArr[$businessesCampaign['business_id']])) {
                                        $businessesData = $businessesDataArr[$businessesCampaign['business_id']];
                                    }
                                    $creditAvailable  = $businessesData['credit_available'];
                                    $creditAvailable -= $wsCampaignPayout;
                                    DB::select("UPDATE `business` set `credit_available`=(`credit_available`-$wsCampaignPayout) where `business_id`='" . $businessesCampaign['business_id'] . "'");
                                    //Business::where('business_id', $businessesCampaign['business_id'])->update(['credit_available' => $creditAvailable]);
                                } else {
                                    $creditAvailable  = $businessesCampaign['credit_available'];
                                    $creditAvailable -= $wsCampaignPayout;
                                    DB::select("UPDATE `campaign` set `credit_available`=(`credit_available`-$wsCampaignPayout) where `campaign_id`='" . $wsCampaignId . "'");
                                    //Campaign::where('campaign_id', $wsCampaignId)->update(['credit_available' => $creditAvailable]);
                                }*/
                                if ($businessesCampaign['payment_type'] == 0) {
                                    $creditAvailable -= $wsCampaignPayout;
                                    DB::select("UPDATE `business` set `credit_available`=(`credit_available`-$wsCampaignPayout) where `business_id`='" . $businessesCampaign['business_id'] . "'");
                                    //Business::where('business_id', $businessesCampaign['business_id'])->update(['credit_available' => $creditAvailable]);
                                } else {
                                    $creditAvailable -= $wsCampaignPayout;
                                    DB::select("UPDATE `campaign` set `credit_available`=(`credit_available`-$wsCampaignPayout) where `campaign_id`='" . $wsCampaignId . "'");
                                    //Campaign::where('campaign_id', $wsCampaignId)->update(['credit_available' => $creditAvailable]);
                                }
                                /*LeadRouting::create([
                                    'lead_id' => $leadId,
                                    'campaign_id' => $wsCampaignId,
                                    'score' => $wsCampaignScore,
                                    'payout' => $wsCampaignPayout,
                                    'route_status' => "sold",
                                    'is_free_lead' => "no",
                                    'created_at' => date("Y-m-d H:i:s"),
                                    'balance_after_charge' => $creditAvailable,
                                    'payment_type' => $businessesCampaign['payment_type']
                                ]);*/

                                LeadRouting::where('lead_id', $leadId)->where('campaign_id', $wsCampaignId)->update([
                                    'score' => $wsCampaignScore,
                                    'payout' => $wsCampaignPayout,
                                    'route_status' => "sold",
                                    'is_free_lead' => "no",
                                    'balance_after_charge' => $creditAvailable,
                                    'payment_type' => $businessesCampaign['payment_type']
                                ]);

                                if ($isSharedWin == 2) {
                                    Lead::where('lead_id', $leadId)->update(['sold_type'=>'dual']);
                                    unset($finalCampaignIds[$wsCampaignId]);

                                    $successFlag = 0;
                                    goto L1;
                                } else if ($isSharedWin == 3) {
                                    Lead::where('lead_id', $leadId)->update(['sold_type'=>'trio']);
                                    unset($finalCampaignIds[$wsCampaignId]);

                                    $successFlag = 0;
                                    goto L1;
                                } else if ($isSharedWin == 4) {
                                    Lead::where('lead_id', $leadId)->update(['sold_type'=>'premium']);
                                    unset($finalCampaignIds[$wsCampaignId]);

                                    $successFlag = 0;
                                    goto L1;
                                } else {
                                    Lead::where('lead_id', $leadId)->update(['sold_type'=>'exclusive']);
                                    $successFlag = 1;
                                    goto L1;
                                }
                            }
                        }  else {
                            /*LeadRouting::create([
                                'lead_id' => $leadId,
                                'campaign_id' => $wsCampaignId,
                                'score' => $wsCampaignScore,
                                'payout' => 0,
                                'route_status' => "unsold",
                                'is_free_lead' => "no",
                                'created_at' => date("Y-m-d H:i:s"),
                                'balance_after_charge' => $creditAvailable,
                                'payment_type' => $businessesCampaign['payment_type']
                            ]);*/
                            if ($isSharedWin > 0) {
                                unset($finalCampaignIds[$wsCampaignId]);

                                $successFlag = 0;
                                goto L1;
                            } else {
                                $successFlag = 1;
                                goto L1;
                            }
                        }
                    } else if ($businessIdArr[$wsCampaignId] == $moverJunctionBusinessId) {
                        if ($moverJunctionObj->post($getLeadData, $wsCampaignId) && $campaignType != 4) {
                            if (isset($campAssocDataArr[$wsCampaignId])) {

                                // Reduce Amount from businesses_campaign table
                                if ($businessesCampaign['payment_type'] == 0) {
                                    $creditAvailable -= $wsCampaignPayout;
                                    DB::select("UPDATE `business` set `credit_available`=(`credit_available`-$wsCampaignPayout) where `business_id`='" . $businessesCampaign['business_id'] . "'");
                                    //Business::where('business_id', $businessesCampaign['business_id'])->update(['credit_available' => $creditAvailable]);
                                } else {
                                    $creditAvailable -= $wsCampaignPayout;
                                    DB::select("UPDATE `campaign` set `credit_available`=(`credit_available`-$wsCampaignPayout) where `campaign_id`='" . $wsCampaignId . "'");
                                    //Campaign::where('campaign_id', $wsCampaignId)->update(['credit_available' => $creditAvailable]);
                                }

                                LeadRouting::where('lead_id', $leadId)->where('campaign_id', $wsCampaignId)->update([
                                    'score' => $wsCampaignScore,
                                    'payout' => $wsCampaignPayout,
                                    'route_status' => "sold",
                                    'is_free_lead' => "no",
                                    'balance_after_charge' => $creditAvailable,
                                    'payment_type' => $businessesCampaign['payment_type']
                                ]);

                                Lead::where('lead_id', $leadId)->update(['sold_type'=>'exclusive']);
                                $successFlag = 1;
                                goto L1;
                            }
                        }  else {
                            unset($finalCampaignIds[$wsCampaignId]);

                            $successFlag = 0;
                            goto L1;
                        }
                    }
                }
            }
            //Added By VP On 30/10/2023 For LeadStudio and Zap Wholesaler Ping Post API End

            //Added By BK On 07/06/2023 For RexDirect Wholesaler Ping Post API Start
            if ($remainSlot == 4 && $campaignType != 4 && $lead_category_id == 1 && $isPingPost == 0) {
                $wsPayoutData = PingPostPayout::where('lead_id', $leadId)->where('campaign_id', $wholesellerCamapignId)->where('payout', '>', $nonWsBuyerAmount)->where('payout', '>=', $minPayout)->orderBy('payout', 'DESC')->get(['campaign_id', 'payout']);
                $this->LeadRoutelog($leadId, 'lead id and call id ' . $call . ' ping response ' . json_encode($wsPayoutData), 102);
                foreach ($wsPayoutData as $wsPayout) {
                    $wholesellerBusiness = Campaign::where('campaign_id', $wsPayout->campaign_id)->get()->first();
                    $wholesellerBusinessId = $paymentType = 0;
                    if (isset($wholesellerBusiness)) {
                        $wholesellerBusinessId = $wholesellerBusiness->business_id;
                        $paymentType = $wholesellerBusiness->payment_type;
                    }
                    if ($wholesellerBusinessId == 55) {
                        if ($paymentType == 0) {
                            $wholesellerBusiness = Business::where('business_id', $wholesellerBusinessId)->get()->first();
                            $creditAvailable = $wholesellerBusiness->credit_available;
                        } else {
                            $creditAvailable = $wholesellerBusiness->credit_available;
                        }
                        if ($rexDirectObj->post($getLeadData, $wsPayout->campaign_id)) {
                            $creditAvailable -= $wsPayout->payout;
                            $leadPayout = $wsPayout->payout;
                            if ($paymentType == 0) {
                                // Reduce Amount from businesses table
                                DB::select("UPDATE `business` set `credit_available`=(`credit_available`-$leadPayout) where `business_id`='" . $wholesellerBusinessId . "'");

                            } else {
                                // Reduce Amount from campaign table
                                DB::select("UPDATE `campaign` set `credit_available`=(`credit_available`-$leadPayout) where `campaign_id`='" . $wsPayout->campaign_id . "'");
                            }
                            //Campaign::where('campaign_id', $wsPayout->campaign_id)->update(['credit_available' => $creditAvailable]);

                            LeadRouting::create([
                                'lead_id' => $leadId,
                                'campaign_id' => $wsPayout->campaign_id,
                                'score' => 0,
                                'payout' => $wsPayout->payout,
                                'route_status' => "sold",
                                'is_free_lead' => "no",
                                'created_at' => date("Y-m-d H:i:s"),
                                'balance_after_charge' => $creditAvailable,
                                'payment_type' => $paymentType
                            ]);

                            Lead::where('lead_id', $leadId)->where('sold_type', 'none')->update(['sold_type' => 'exclusive']);

                            $successFlag = 1;
                            goto L1;
                        } else {
                            LeadRouting::create([
                                'lead_id' => $leadId,
                                'campaign_id' => $wsPayout->campaign_id,
                                'score' => 0,
                                'payout' => 0,
                                'route_status' => "unsold",
                                'is_free_lead' => "no",
                                'created_at' => date("Y-m-d H:i:s"),
                                'balance_after_charge' => $creditAvailable,
                                'payment_type' => $paymentType
                            ]);
                            //$successFlag = 1;
                            //goto L1;
                        }
                    }

                    if ($wholesellerBusinessId == 992) {
                        if ($paymentType == 0) {
                            $wholesellerBusiness = Business::where('business_id', $wholesellerBusinessId)->get()->first();
                            $creditAvailable = $wholesellerBusiness->credit_available;
                        } else {
                            $creditAvailable = $wholesellerBusiness->credit_available;
                        }

                        $notSoldWholeseller = 0;
                        if ($coverageType == 'long' && isset($getLeadData) && $getLeadData[0]['is_verified'] == 'yes' && $wsPayout->payout < 15) {
                            $notSoldWholeseller = 1;
                        }
                        if ($coverageType == 'local' && isset($getLeadData) && $getLeadData[0]['is_verified'] == 'yes' && $wsPayout->payout < 7) {
                            $notSoldWholeseller = 1;
                        }
                        if ($coverageType == 'long' && isset($getLeadData) && $getLeadData[0]['is_verified'] == 'no' && $wsPayout->payout < 7) {
                            $notSoldWholeseller = 1;
                        }
                        if ($coverageType == 'local' && isset($getLeadData) && $getLeadData[0]['is_verified'] == 'no' && $wsPayout->payout < 5) {
                            $notSoldWholeseller = 1;
                        }
                        if ($notSoldWholeseller  > 0) {
                            LeadRouting::create([
                                'lead_id' => $leadId,
                                'campaign_id' => $wsPayout->campaign_id,
                                'score' => 0,
                                'payout' => 0,
                                'route_status' => "unsold",
                                'is_free_lead' => "no",
                                'created_at' => date("Y-m-d H:i:s"),
                                'balance_after_charge' => $creditAvailable,
                                'payment_type' => $paymentType
                            ]);

                            $successFlag = 0;
                            goto L1;
                        }

                        if ($etnAmericaObj->post($getLeadData, $wsPayout->campaign_id)) {
                            // Reduce Amount from businesses_campaign table
                            $creditAvailable -= $wsPayout->payout;
                            $leadPayout = $wsPayout->payout;
                            if ($paymentType == 0) {
                                // Reduce Amount from businesses table
                                DB::select("UPDATE `business` set `credit_available`=(`credit_available`-$leadPayout) where `business_id`='" . $wholesellerBusinessId . "'");

                            } else {
                                // Reduce Amount from campaign table
                                DB::select("UPDATE `campaign` set `credit_available`=(`credit_available`-$leadPayout) where `campaign_id`='" . $wsPayout->campaign_id . "'");
                            }
                            //Campaign::where('campaign_id', $wsPayout->campaign_id)->update(['credit_available' => $creditAvailable]);

                            LeadRouting::create([
                                'lead_id' => $leadId,
                                'campaign_id' => $wsPayout->campaign_id,
                                'score' => 0,
                                'payout' => $wsPayout->payout,
                                'route_status' => "sold",
                                'is_free_lead' => "no",
                                'created_at' => date("Y-m-d H:i:s"),
                                'balance_after_charge' => $creditAvailable,
                                'payment_type' => $paymentType
                            ]);

                            Lead::where('lead_id', $leadId)->where('sold_type', 'none')->update(['sold_type' => 'exclusive']);

                            $successFlag = 1;
                            goto L1;
                        } else {
                            LeadRouting::create([
                                'lead_id' => $leadId,
                                'campaign_id' => $wsPayout->campaign_id,
                                'score' => 0,
                                'payout' => 0,
                                'route_status' => "unsold",
                                'is_free_lead' => "no",
                                'created_at' => date("Y-m-d H:i:s"),
                                'balance_after_charge' => $creditAvailable,
                                'payment_type' => $paymentType
                            ]);
                            //$successFlag = 1;
                            //goto L1;
                        }
                    }
                }
            }

            // Fetch data from ws_payout
            L1:
            if ($successFlag == 0) {
                foreach ($finalCampaignIds as $campaignIdKey => $score) {
                    /* if (in_array($campaignIdKey, $pingPostIdArr)) {
                      $pingResponse = $pingPayoutPm->postPrimeMarketing($getLeadData, $campaignIdKey);
                      //echo "<pre>";print_R($pingResponse);die;
                      $leadPayout = 0;
                      if (isset($pingResponse['payout'])) {
                      $leadPayout = $pingResponse['payout'];
                      }
                      //echo "<pre>";print_R($leadPayout);die;
                      } else {
                      $leadPayout = CommonFunctions::getCampaignPayout($campaignIdKey, $coverageType, 0, $call);
                      } */
                    //$leadPayout = CommonFunctions::getCampaignPayout($campaignIdKey, $coverageType, 0, $call); //Comment By BK On 29/02/2024 as per discussion with MA and DS
                    $leadPayout = $campaignPayoutArray[$campaignIdKey] ?? 0;
                    //check here pre or post payment type
                    if (isset($campAssocDataArr[$campaignIdKey])) {
                        $businessesCampaign = $campAssocDataArr[$campaignIdKey];
                        $businessPaymentType = 'pre';
                        if (isset($businessesDataArr[$businessesCampaign['business_id']])) {
                            $businessesData = $businessesDataArr[$businessesCampaign['business_id']];
                            $businessPaymentType = $businessesData['payment_type'];
                        }
                        $creditAvailable = $creditReserved = $afterBalance = 0;
                        $creditData = array();
                        if ($businessesCampaign['payment_type'] == 0) {
                            $getBusinessCredit = $creditData = Business::where('business_id', $businessesCampaign['business_id'])->get()->toArray();
                            if (count($getBusinessCredit) > 0) {
                                $creditAvailable = $getBusinessCredit[0]['credit_available'];
                                $creditReserved = $getBusinessCredit[0]['credit_reserved'];
                            }
                        } else {
                            $getCampignCredit = $creditData = Campaign::where('campaign_id', $campaignIdKey)->get()->toArray();
                            if (count($getCampignCredit) > 0) {
                                $creditAvailable = $getCampignCredit[0]['credit_available'];
                                $creditReserved = $getCampignCredit[0]['credit_reserved'];
                            }
                        }
                        if ($isPingPost == 0) {
                            $this->LeadRoutelog($leadId, 'free lead ==' . $businessesCampaign['free_lead'] . '==credit==' . $creditAvailable . ' of win campaign ids ' . json_encode($campaignSortBundle) . "==credit data=" . json_encode($creditData) . "===businessPaymentType==" . $businessPaymentType, 53);
                        }
                        $isFreeLead = "no";
                        if ($creditAvailable >= $leadPayout || $businessPaymentType == "post") {
                            if ($businessesCampaign['free_lead'] > 0) {
                                //Added By HJ On 07-12-2021 For Check Campaign Free Lead Count with Balance As Per Discuss with MV Start
                                $applyFreeLead = $this->checkCampaingBalance($campaignIdKey, $leadPayout, $creditAvailable);
                                if ($applyFreeLead > 0) {
                                    $isFreeLead = "yes";
                                    $leadPayout = 0;
                                    $leftFreeLead = ($businessesCampaign['free_lead'] - 1);
                                    if ($isPingPost == 0) {
                                        /*Campaign::where('campaign_id', $campaignIdKey)->update(['free_lead' => $leftFreeLead]);*/
                                        DB::select("UPDATE `campaign` set `free_lead`=(`free_lead`-1) where `campaign_id`='" . $campaignIdKey . "'");
                                    }
                                    $this->LeadRoutelog($leadId, 'Campaign Free Lead Applied == ' . $campaignIdKey . ',Free Lead=' . $businessesCampaign['free_lead'] . "-1==" . $leftFreeLead, 59);
                                }
                                //Added By HJ On 07-12-2021 For Check Campaign Free Lead Count with Balance As Per Discuss with MV End
                            }
                            if ($isPingPost == 0) {
                                $this->LeadRoutelog($leadId, 'payment type ==' . $businessesCampaign['payment_type'] . '==payout==' . $leadPayout . ' of win campaign Id ' . $campaignIdKey, 54);
                            }
                            if ($leadPayout > 0) {
                                if ($businessesCampaign['payment_type'] == 0) {
                                    $creditAvailable -= $leadPayout;
                                    $creditReserved += $leadPayout;
                                    if ($isPingPost == 0) {
                                        DB::select("UPDATE `business` set `credit_available`=(`credit_available`-$leadPayout) where `business_id`='" . $businessesCampaign['business_id'] . "'");
                                        //Business::where('business_id', $businessesCampaign['business_id'])->update(['credit_available' => $creditAvailable]);
                                    }
                                } else {
                                    $creditAvailable -= $leadPayout;
                                    $creditReserved += $leadPayout;
                                    if ($isPingPost == 0) {
                                        DB::select("UPDATE `campaign` set `credit_available`=(`credit_available`-$leadPayout) where `campaign_id`='" . $campaignIdKey . "'");
                                        //Campaign::where('campaign_id', $campaignIdKey)->update(['credit_available' => $creditAvailable]);
                                    }
                                }
                            }
                            $this->LeadRoutelog($leadId, 'Business payment type ==' . $businessPaymentType . '==payout==' . $leadPayout . ' of win campaign Id ' . $campaignIdKey, 53);
                            $campaignSortBundle[$campaignIdKey] = $score;
                        }

                        if ($isPingPost == 0) {
                            LeadRouting::where('lead_id', $leadId)->where('campaign_id', $campaignIdKey)->update([
                                'balance_after_charge' => $creditAvailable,
                                'payment_type' => $businessesCampaign['payment_type'],
                                'payout' => $leadPayout,
                                'is_free_lead' => $isFreeLead
                            ]);
                        }
                    }
                }
                if ($isPingPost == 0) {
                    $this->LeadRoutelog($leadId, 'bundle win campaign ids ' . json_encode($campaignSortBundle), 55);
                }
                $wholesellerPingPost = 1;
                //echo "<pre>";print_r($campaignSortBundle);die;
                return $campaignSortBundle;
            } else {
                $wholesellerPingPost = 1;
                return array();
            }
        }
    }

    public function LeadCall_Donot_reapeat_customer_plivonumber($cust_number, $cust_dial_number, $campaign) {
        $temp = $difference_between_call = 0;
        $currentdate = date('Y-m-d H:i:s');
        $date48 = new DateTime($currentdate);
        $date48->modify('-72 hours');
        $before_72hours_date = $date48->format('Y-m-d H:i:s');
        $inbound = LeadCall::where('from_number', $cust_number)->where('to_number', $cust_dial_number)->where("call_type", "inbound")->whereBetween('created_at', [$before_72hours_date, $currentdate])->orderby('lead_call_id', 'DESC')->first();
        if (isset($inbound)) {
            $last_call = strtotime($inbound->created_at);
            $timestamp2 = strtotime($currentdate);
            $difference_between_call = abs($timestamp2 - $last_call) / (60 * 60);

            if ($difference_between_call > 2) {
                // echo "<pre>hiii = " . $difference_between_call; // print_r($inbound); // last call is comeing more then 2 hours and less then 72 hours
                if ($inbound->campaign_id == $campaign) {
                    $temp = 1;
                }
            }
        }
        DB::table('dev_debug')->insert(['sr_no' => 55, 'result' => "matchedcallcampaigns LeadCall_Donot_reapeat_customer_plivonumber 1 = " . $cust_number . "==" . $cust_dial_number . "=inbound=" . $before_72hours_date . "==" . $currentdate . "==" . $difference_between_call . "==" . $temp . "==" . $temp . "==" . json_encode($inbound), 'created_at' => date('Y-m-d H:i:s')]);
        return $temp;
    }

    public function LeadCall_Donot_reapeat_customer_number($cust_number, $cust_dial_number, $campaign) {
        $temp = 0;
        $currentdate = date('Y-m-d H:i:s');
        $date48 = new DateTime($currentdate);
        $date48->modify('-72 hours');
        $before_72hours_date = $date48->format('Y-m-d H:i:s');
        $inbound = LeadCall::where('from_number', $cust_number)->whereNotIn('to_number', [$cust_dial_number])->where("call_type", "inbound")->whereBetween('created_at', [$before_72hours_date, $currentdate])->orderby('lead_call_id', 'DESC')->first();
        // echo "<pre>hiii = "; print_r($inbound); die;
        if (isset($inbound)) {
            if ($inbound->campaign_id == $campaign) {
                $temp = 1;
            }
        }
        DB::table('dev_debug')->insert(['sr_no' => 56, 'result' => "matchedcallcampaigns LeadCall_Donot_reapeat_customer_number 1 = " . $cust_number . "==" . $cust_dial_number . "=inbound=" . $before_72hours_date . "==" . $currentdate . "==" . $campaign . "==" . $temp . "==" . json_encode($inbound), 'created_at' => date('Y-m-d H:i:s')]);
        return $temp;
    }

    public function checkCampaingBalance($campaignId, $payout, $balance) {
        $return = 0;
        if ($balance >= $payout) {
            $return = 1;
        }
        return $return;
    }

    //Added By BK On 21-11-2023 For Check Campaign Source Restriction Start
    public function checkCampaignSource($campaignIds, $leadId, $sourceId=0, $sourceName = "")
    {
        $sourceCampaignIds = [];
        if ($leadId > 0) {
            $leadRouteControllerObj = new LeadRouteController();
            $leadData = $leadRouteControllerObj->getLeadCategoryType($leadId);
            if (isset($leadData['origin_id'])) {
                $sourceId = $leadData['origin_id'];
            }
        }

        if (empty($sourceName)) {
            $campaignSourceDetails = CampaignLeadSource::whereIn('campaign_id', $campaignIds)->where('lead_source_id', $sourceId)->get(['campaign_id'])->toArray();
            if (count($campaignSourceDetails) > 0) {
                for ($c=0; $c < count($campaignSourceDetails); $c++) {
                    $sourceCampaignIds[] = $campaignSourceDetails[$c]['campaign_id'];
                }
            }
        } else {
            $sourceId = MstLeadSource::where('lead_parent_source', $sourceName)->pluck('lead_source_id')->toArray();
            $campaignSourceDetails = CampaignLeadSource::whereIn('campaign_id', $campaignIds)->whereIn('lead_source_id', $sourceId)->get(['campaign_id'])->toArray();
            if (count($campaignSourceDetails) > 0) {
                for ($c=0; $c < count($campaignSourceDetails); $c++) {
                    $sourceCampaignIds[] = $campaignSourceDetails[$c]['campaign_id'];
                }
            }
        }


        $campaignIds = array_values(array_diff($campaignIds, $sourceCampaignIds));
        //echo '<pre>'; print_r($campaignIds); die;
        return $campaignIds;
    }

    public function checkCampaignSourceIS($campaignIds, $sourceId)
    {
        $sourceCampaignIds=[];
        $campaignSourceDetails = CampaignLeadSource::whereIn('campaign_id', $campaignIds)->where('lead_source_id', $sourceId)->get(['campaign_id'])->toArray();
        if (count($campaignSourceDetails) > 0) {
            for ($c=0; $c < count($campaignSourceDetails); $c++) {
                $sourceCampaignIds[] = $campaignSourceDetails[$c]['campaign_id'];
            }
        }

        $campaignIds = array_values(array_diff($campaignIds, $sourceCampaignIds));
        //echo '<pre>'; print_r($campaignIds); die;
        return $campaignIds;
    }

    //Added By HJ On 24-12-2022 For Check Campaign Excluded Move Date Restriction Start
    public function checkCampaignExcludedDates($campaignIds, $moveDateDataArr, $moveDate) {
        //echo "<pre>dd";print_r(count($campaignIds));die;
        $todayDate = date("Y-m-d");
        $campIdsNewArr = array();
        if (count($campaignIds) > 0) {
            foreach ($campaignIds as $campId) {
                //echo $campId . "<br>";
                if (isset($moveDateDataArr[$campId])) {
                    $campData = $moveDateDataArr[$campId];
                    //echo "<pre>";print_r($campData);die;
                    $removeCampId = $removeDayCampId = 1;
                    if (isset($campData->move_days_after) && $campData->move_days_after > 0) {
                        $move_days_after = $campData->move_days_after;
                        $countDate = date('Y-m-d', strtotime($todayDate . ' + ' . $move_days_after . ' days'));
                        if ($moveDate >= $countDate) {
                            $removeDayCampId = 1;
                        } else {
                            $removeDayCampId = 0;
                        }
                    }
                    if ($campData->from_exclude_date != "0000-00-00" && $campData->from_exclude_date != "") {
                        $from_exclude_date = $campData->from_exclude_date;
                        $to_exclude_date = $campData->to_exclude_date;
                        //echo $moveDate."==".$to_exclude_date."==".$from_exclude_date;die;
                        if ($moveDate >= $from_exclude_date && $moveDate <= $to_exclude_date) {
                            $removeCampId = 0;
                        } else {
                            $removeCampId = 1;
                        }
                    }
                    if ($removeCampId > 0 && $removeDayCampId > 0) {
                        if (!in_array($campId, $campIdsNewArr)) {
                            $campIdsNewArr[] = $campId;
                        }
                    }
                } else {
                    if (!in_array($campId, $campIdsNewArr)) {
                        $campIdsNewArr[] = $campId;
                    }
                }
            }
        } else {
            $campIdsNewArr = $campaignIds;
        }
        //echo "<pre>";print_r($campIdsNewArr);die;
        return $campIdsNewArr;
    }

    //Added By HJ On 24-12-2022 For Check Campaign Excluded Move Date Restriction End
    public function checkTiming($campaignIds, $leadDate, $leadTime, $coverageType, $leadRouteArr) {
        $campaignIdsUpdate = $campaignHourArr = $limitArr = $campaignHourIdArr = $campIdArr = array();
        // Find day
        $date = strtotime($leadDate);
        $dayNumber = date("N", $date);
        // Find Hour
        $hour = strtotime($leadTime);
        $hourNumber = date('H', $hour);
        if (count($campaignIds) > 0) {
            //Added By HJ On 27-08-2021 For Optimize Code Start
            $businessesHoursLimit = CampaignScheule::whereIn('campaign_id', $campaignIds)->distinct()->get(['campaign_id'])->toArray();
            for ($ga = 0; $ga < count($businessesHoursLimit); $ga++) {
                if (isset($campaignHourIdArr[$businessesHoursLimit[$ga]['campaign_id']])) {
                    if (!in_array($businessesHoursLimit[$ga]['campaign_id'], $campaignHourIdArr[$businessesHoursLimit[$ga]['campaign_id']])) {
                        $campaignHourIdArr[$businessesHoursLimit[$ga]['campaign_id']][] = $businessesHoursLimit[$ga]['campaign_id'];
                    }
                } else {
                    $campIdArr[] = $businessesHoursLimit[$ga]['campaign_id'];
                    $campaignHourIdArr[$businessesHoursLimit[$ga]['campaign_id']][] = $businessesHoursLimit[$ga]['campaign_id'];
                }
            }
            $campaignList = CampaignScheule::whereIn('campaign_id', $campIdArr)->where('day', $dayNumber)->where('is_pause', 'no')->where('status', 'active')->where('start_hour', '<=', $hourNumber)->where('end_hour', '>', $hourNumber)->get(['campaign_id', 'daily_limit'])->toArray();
            for ($da = 0; $da < count($campaignList); $da++) {
                $limitArr[$campaignList[$da]['campaign_id']][] = $campaignList[$da];
            }
            //echo "<pre>dd";print_r($campIdArr);die;
            //echo "<pre>";print_r($leadRouteArr);die;
            //Added By HJ On 27-08-2021 For Optimize Code End
            foreach ($campaignIds as $campaignId) {
                $flag = 1;
                $campaignHourData = array();
                if (isset($campaignHourIdArr[$campaignId])) {
                    $campaignHourData = $campaignHourIdArr[$campaignId];
                } else {
                    continue;
                }
                //echo "<pre>";print_r($campaignHourData);die;
                foreach ($campaignHourData as $hourType) {
                    //echo "<pre>";print_r($hourType);die;
                    $campaignList = array();
                    if (isset($limitArr[$hourType])) {
                        $campaignList = $limitArr[$hourType];
                    }
                    if (count($campaignList) > 0) {
                        for ($cm = 0; $cm < count($campaignList); $cm++) {
                            //echo $campaignList[$cm]['campaign_id']."-".$campaignList[$cm]['daily_limit']."<br>";die;
                            $dailyLimit = $campaignList[$cm]['daily_limit'];
                            $campaignIdFinal = $campaignList[$cm]['campaign_id'];
                            if ($dailyLimit > 0) {
                                $leadRouting = array();
                                if (isset($leadRouteArr[$campaignIdFinal])) {
                                    $leadRouting = $leadRouteArr[$campaignIdFinal];
                                }
                                if (count($leadRouting) > 0) {
                                    if (count($leadRouting) >= $dailyLimit) {
                                        $flag = 0;
                                        goto l1;
                                    }
                                }
                            }
                        }
                    } else {
                        $flag = 0;
                    }
                }
                l1:
                if ($flag == 1) {
                    $campaignIdsUpdate[] = $campaignId;
                }
            }
        }
        return $campaignIdsUpdate;
    }

    public function checkLimit($campaignIds, $activeCampAssocDataAr, $coverageType, $tokPayout = 0, $inboundCall = 0) {
        // if ($inboundCall > 0) {
        //     $payoutType = 'inbound_call';
        // } else {
        //     $payoutType = 'outbound_call';
        // }
        if ($inboundCall > 0) { // For Inbound
            $payoutType = "inbound_call";
            if ($tokPayout > 0) {
                $payoutType = "automated_inbound";
            }
        } else {
            $payoutType = "automated_outbound";
            if ($tokPayout > 0) {
                $payoutType = "outbound_call";
            }
        }

        $campaignIdsUpdate = $leadRouteArr = $limitCampIdArr = $payoutDataArr = $leadRouteHourArr = $lpCallDateDataArr = array();
        $businessesCampaignLimit = $limitCampIdArr = $campTypeArr = array();
        foreach ($activeCampAssocDataAr as $key => $val) {
            $limitCampIdArr[] = $key;
        }
        //Added By HJ On 22-02-2022 For Solved Daily Limit Issue End
        if (count($limitCampIdArr) > 0) {
            $businessesCampaignPayout = CommonFunctions::getCampaignPayout($limitCampIdArr, $coverageType, $tokPayout, $inboundCall);
            for ($va = 0; $va < count($businessesCampaignPayout); $va++) {
                $payoutDataArr[$businessesCampaignPayout[$va]['campaign_id']] = $businessesCampaignPayout[$va];
            }
            //echo "<pre>";print_r($payoutDataArr);die;
        }
        //echo "<pre>";print_r($activeCampAssocDataAr);die;
        $dateValue = date("Y-m-d");
        //$leadRouteData = LeadRouting::whereIn('campaign_id', $campaignIds)->where('route_status', 'sold')->where('created_at', 'LIKE', $dateValue . '%')->where('payout', '>', 0)->groupBy('campaign_id')->select('campaign_id', DB::raw('count(lead_id) as Leads'), DB::raw('sum(payout) as Payout'))->get()->toArray();
        $leadRouteData = LeadRouting::whereDate('created_at', '=', $dateValue)->whereIn('campaign_id', $campaignIds)->where('route_status', 'sold')->where('payout', '>', 0)->groupBy('campaign_id')->select('campaign_id', DB::raw('count(lead_id) as Leads'), DB::raw('sum(payout) as Payout'))->get()->toArray();
        //echo "<pre>";print_r($leadRouteData);die;
        for ($le = 0; $le < count($leadRouteData); $le++) {
            $campaign_type = 0;
            if (isset($activeCampAssocDataAr[$leadRouteData[$le]['campaign_id']]->campaign_type_id)) {
                $campaign_type = $activeCampAssocDataAr[$leadRouteData[$le]['campaign_id']]->campaign_type_id;
            }
            //echo $campaign_type;die;
            if ($campaign_type != 3) {
                //$created_at_hour = date("Y-m-d H", strtotime($leadRouteData[$le]['created_at']));
                $leadRouteArr[$leadRouteData[$le]['campaign_id']]['Leads'] = $leadRouteData[$le]['Leads'];
                $leadRouteArr[$leadRouteData[$le]['campaign_id']]['Payout'] = $leadRouteData[$le]['Payout'];
            }
        }
        //echo "<pre>";print_r($leadRouteArr);die;
        //DB::table('test')->insert(['result' => 'leadRouteData data : ' . json_encode($leadRouteArr)]);
        $dateHourValue = date("Y-m-d H");
        $leadRouteHourData = LeadRouting::whereIn('campaign_id', $campaignIds)->where('route_status', 'sold')->where('created_at', 'LIKE', $dateHourValue . '%')->where('payout', '>', 0)->groupBy('campaign_id')->select('campaign_id', DB::raw('count(lead_id) as Leads'))->get()->toArray();
        //$leadRouteHourData = LeadRouting::whereDate('created_at', '>=', date("Y-m-d H:00:00"))->whereIn('campaign_id', $campaignIds)->where('route_status', 'sold')->where('payout', '>', 0)->groupBy('campaign_id')->select('campaign_id', DB::raw('count(lead_id) as Leads'))->get()->toArray();
        //echo "<pre>";print_r($leadRouteHourData);die;
        for ($lr = 0; $lr < count($leadRouteHourData); $lr++) {
            $campaign_type = 0;
            if (isset($activeCampAssocDataAr[$leadRouteHourData[$lr]['campaign_id']]->campaign_type_id)) {
                $campaign_type = $activeCampAssocDataAr[$leadRouteHourData[$lr]['campaign_id']]->campaign_type_id;
            }
            if ($campaign_type != 3) {
                $leadRouteHourArr[$leadRouteHourData[$lr]['campaign_id']]['Leads'] = $leadRouteHourData[$lr]['Leads'];
            }
        }
        //echo "<pre>";print_r($leadRouteHourArr);die;
        //Added By HJ On 27-08-2021 For Optimize Code End
        foreach ($campaignIds as $campaignId) {
            $businessesCampaignLimit = array();
            if (isset($activeCampAssocDataAr[$campaignId])) {
                $businessesCampaignLimit[] = $activeCampAssocDataAr[$campaignId];
            }
            //echo "<pre>";print_r($businessesCampaignLimit);die;
            $flag = 1;
            for ($fa = 0; $fa < count($businessesCampaignLimit); $fa++) {
                $daily_lead_limit = $businessesCampaignLimit[$fa]->daily_lead_limit;
                $daily_budget_limit = $businessesCampaignLimit[$fa]->daily_budget_limit;
                $hourly_lead_limit = $businessesCampaignLimit[$fa]->hourly_lead_limit;
                //echo $daily_lead_limit."==".$daily_budget_limit."==".$hourly_lead_limit;die;
                $payout = $leads = 0;
                if (isset($leadRouteArr[$campaignId])) {
                    $payout = $leadRouteArr[$campaignId]['Payout'];
                    $leads = $leadRouteArr[$campaignId]['Leads'];
                }
                $totalLeadCount = $leads;
                $totalLeadPayout = $payout;
                //echo $campaignId."==".$totalLeadCount."==".$totalLeadPayout;die;
                if ($totalLeadCount > 0) {
                    //DB::table('test')->insert(['result' => 'daily limit : lead payout:' . $leads . ',Leads Count:' . $payout . ', inbound call :' . $inboundCall . ', inbound payout :' . $inboundPayout . ',limit_value:' . $daily_lead_limit . ',limit_type:' . $limit_type . ',campaignId:' . $campaignId]);
                    if ($daily_lead_limit > 0) {
                        if ($totalLeadCount >= $daily_lead_limit) {
                            $flag = 0;
                            break;
                        }
                    }
                    if ($daily_budget_limit > 0) {
                        $leadPayout = 0;
                        //echo "<pre>";print_r($payoutDataArr[$campaignId]);die;
                        if (isset($payoutDataArr[$campaignId])) {
                            $leadPayout = $payoutDataArr[$campaignId][$payoutType];
                        }
                        if ($totalLeadPayout >= $daily_budget_limit) {
                            $flag = 0;
                            break;
                        }
                        if ($leadPayout > ($daily_budget_limit - $totalLeadPayout)) {
                            $flag = 0;
                            break;
                        }
                    }
                }
                $totalHourLimit = 0;
                if (isset($leadRouteHourArr[$campaignId])) {
                    $leads = $leadRouteHourArr[$campaignId]['Leads'];
                    $totalHourLimit = $totalHourLimit + $leads;
                }
                if ($hourly_lead_limit > 0) {
                    if ($totalHourLimit >= $hourly_lead_limit) {
                        $flag = 0;
                        break;
                    }
                }
            }
            if ($flag == 1) {
                $campaignIdsUpdate[] = $campaignId;
            }
        }
        return $campaignIdsUpdate;
    }

    public function checkFromZip($campaignIds, $activeCampAssocDataAr, $fromZip, $toZip, $fromState, $toState, $fromAreaCode, $toAreaCode = "") {
        // is_origin = 1 - from, type = 3 - Zip  , 1 - state, 2 - areacode
        //echo $fromAreaCode."<br>";
        $fromZip = str_pad($fromZip, 5, "0", STR_PAD_LEFT);
        $toZip = str_pad($toZip, 5, "0", STR_PAD_LEFT);
        //Optimized By HJ On 27-07-2022 Start
        $campaignIdsUpdate = $campaignRegionArr = $campRegionDataArr = $campCategoryArr = array();
        $businessesCampaignRegionType = CampaignLocation::whereIn('campaign_id', $campaignIds)->get()->toArray();
        //echo "<pre>";print_r($businessesCampaignRegionType);die;
        for ($r = 0; $r < count($businessesCampaignRegionType); $r++) {
            $campaignRegionArr[$businessesCampaignRegionType[$r]['campaign_id']][$businessesCampaignRegionType[$r]['location_coverage']] = $businessesCampaignRegionType[$r];
            $campRegionDataArr[$businessesCampaignRegionType[$r]['campaign_id']][$businessesCampaignRegionType[$r]['value']][$businessesCampaignRegionType[$r]['location_coverage']][$businessesCampaignRegionType[$r]['location_type']][] = $businessesCampaignRegionType[$r];
        }
        //echo "<pre>";print_r($activeCampAssocDataAr);die;
        //Optimized By HJ On 27-07-2022 End
        foreach ($campaignIds as $campaignId) {
            // Fetch Type from BusinessesCampaignRegion
            $flag = 0;
            if (isset($campaignRegionArr[$campaignId])) {
                $regionCampArr = $campaignRegionArr[$campaignId]; //Optimized By HJ On 27-07-2022
                //echo "<pre>";print_r($regionCampArr);die;
                foreach ($regionCampArr as $typeDetails) {
                    $regionType = $typeDetails['location_coverage']; //Optimized By HJ On 27-07-2022
                    $campCategory = 1;
                    if (isset($activeCampAssocDataAr[$campaignId]->lead_category_id)) {
                        $campCategory = $activeCampAssocDataAr[$campaignId]->lead_category_id;
                    }
                    if ($regionType == 'state') {
                        //Optimized By HJ On 27-07-2022 Start
                        $fromTypeArr = $toTypeArr = array();
                        if (isset($campRegionDataArr[$campaignId][$fromState]['state']['source'])) {
                            $fromTypeArr = $campRegionDataArr[$campaignId][$fromState]['state']['source'];
                        }
                        if (isset($campRegionDataArr[$campaignId][$toState]['state']['destination'])) {
                            $toTypeArr = $campRegionDataArr[$campaignId][$toState]['state']['destination'];
                        }
                        //echo "<pre>";print_r($campRegionDataArr);die;
                        $businessesCampaignRegion = array_merge($fromTypeArr, $toTypeArr);
                        $regionCount = 2;
                        if ($campCategory == 2) {
                            $businessesCampaignRegion = $fromTypeArr;
                            $regionCount = 1;
                        }
                        if (!empty($businessesCampaignRegion) && count($businessesCampaignRegion) == $regionCount) {
                            $flag = 1;
                            goto stopL1;
                        }
                    }
                    if ($regionType == 'state') {
                        //Optimized By HJ On 27-07-2022 Start
                        $fromTypeArr = $toTypeArr = array();
                        if (isset($campRegionDataArr[$campaignId][$fromState]['state']['source'])) {
                            $fromTypeArr = $campRegionDataArr[$campaignId][$fromState]['state']['source'];
                        }
                        if (isset($campRegionDataArr[$campaignId][$toAreaCode]['areacode']['destination'])) {
                            $toTypeArr = $campRegionDataArr[$campaignId][$toAreaCode]['areacode']['destination'];
                        }
                        $businessesCampaignRegion = array_merge($fromTypeArr, $toTypeArr);
                        $regionCount = 2;
                        if ($campCategory == 2) {
                            $businessesCampaignRegion = $fromTypeArr;
                            $regionCount = 1;
                        }
                        if (!empty($businessesCampaignRegion) && count($businessesCampaignRegion) == $regionCount) {
                            $flag = 1;
                            goto stopL1;
                        }
                    }
                    if ($regionType == 'state') {
                        //Optimized By HJ On 27-07-2022 Start
                        $fromTypeArr = $toTypeArr = array();
                        if (isset($campRegionDataArr[$campaignId][$fromState]['state']['source'])) {
                            $fromTypeArr = $campRegionDataArr[$campaignId][$fromState]['state']['source'];
                        }
                        if (isset($campRegionDataArr[$campaignId][$toZip]['zipcode']['destination'])) {
                            $toTypeArr = $campRegionDataArr[$campaignId][$toZip]['zipcode']['destination'];
                        }
                        $businessesCampaignRegion = array_merge($fromTypeArr, $toTypeArr);
                        $regionCount = 2;
                        if ($campCategory == 2) {
                            $businessesCampaignRegion = $fromTypeArr;
                            $regionCount = 1;
                        }
                        if (!empty($businessesCampaignRegion) && count($businessesCampaignRegion) == $regionCount) {
                            $flag = 1;
                            goto stopL1;
                        }
                    }
                    if ($regionType == 'areacode') {
                        //Optimized By HJ On 27-07-2022 Start
                        $fromTypeArr = $toTypeArr = array();
                        if (isset($campRegionDataArr[$campaignId][$fromAreaCode]['areacode']['source'])) {
                            $fromTypeArr = $campRegionDataArr[$campaignId][$fromAreaCode]['areacode']['source'];
                        }
                        if (isset($campRegionDataArr[$campaignId][$toState]['state']['destination'])) {
                            $toTypeArr = $campRegionDataArr[$campaignId][$toState]['state']['destination'];
                        }
                        $businessesCampaignRegion = array_merge($fromTypeArr, $toTypeArr);
                        $regionCount = 2;
                        if ($campCategory == 2) {
                            $businessesCampaignRegion = $fromTypeArr;
                            $regionCount = 1;
                        }
                        if (!empty($businessesCampaignRegion) && count($businessesCampaignRegion) == $regionCount) {
                            $flag = 1;
                            goto stopL1;
                        }
                    }
                    if ($regionType == 'areacode') {
                        //Optimized By HJ On 27-07-2022 Start
                        $fromTypeArr = $toTypeArr = array();
                        if (isset($campRegionDataArr[$campaignId][$fromAreaCode]['areacode']['source'])) {
                            $fromTypeArr = $campRegionDataArr[$campaignId][$fromAreaCode]['areacode']['source'];
                        }
                        if (isset($campRegionDataArr[$campaignId][$toZip]['zipcode']['destination'])) {
                            $toTypeArr = $campRegionDataArr[$campaignId][$toZip]['zipcode']['destination'];
                        }
                        $businessesCampaignRegion = array_merge($fromTypeArr, $toTypeArr);
                        $regionCount = 2;
                        if ($campCategory == 2) {
                            $businessesCampaignRegion = $fromTypeArr;
                            $regionCount = 1;
                        }
                        if (!empty($businessesCampaignRegion) && count($businessesCampaignRegion) == $regionCount) {
                            $flag = 1;
                            goto stopL1;
                        }
                    }
                    if ($regionType == 'areacode') {
                        //Optimized By HJ On 27-07-2022 Start
                        $fromTypeArr = $toTypeArr = array();
                        if (isset($campRegionDataArr[$campaignId][$fromAreaCode]['areacode']['source'])) {
                            $fromTypeArr = $campRegionDataArr[$campaignId][$fromAreaCode]['areacode']['source'];
                        }
                        if (isset($campRegionDataArr[$campaignId][$toAreaCode]['areacode']['destination'])) {
                            $toTypeArr = $campRegionDataArr[$campaignId][$toAreaCode]['areacode']['destination'];
                        }
                        $businessesCampaignRegion = array_merge($fromTypeArr, $toTypeArr);
                        $regionCount = 2;
                        if ($campCategory == 2) {
                            $businessesCampaignRegion = $fromTypeArr;
                            $regionCount = 1;
                        }
                        if (!empty($businessesCampaignRegion) && count($businessesCampaignRegion) == $regionCount) {
                            $flag = 1;
                            goto stopL1;
                        }
                    }
                    if ($regionType == 'zipcode') {
                        //Optimized By HJ On 27-07-2022 Start
                        $fromTypeArr = $toTypeArr = array();
                        if (isset($campRegionDataArr[$campaignId][$fromZip]['zipcode']['source'])) {
                            $fromTypeArr = $campRegionDataArr[$campaignId][$fromZip]['zipcode']['source'];
                        }
                        if (isset($campRegionDataArr[$campaignId][$toZip]['zipcode']['destination'])) {
                            $toTypeArr = $campRegionDataArr[$campaignId][$toZip]['zipcode']['destination'];
                        }
                        $businessesCampaignRegion = array_merge($fromTypeArr, $toTypeArr);
                        $regionCount = 2;
                        if ($campCategory == 2) {
                            $businessesCampaignRegion = $fromTypeArr;
                            $regionCount = 1;
                        }
                        if (!empty($businessesCampaignRegion) && count($businessesCampaignRegion) == $regionCount) {
                            $flag = 1;
                            goto stopL1;
                        }
                    }
                    if ($regionType == 'zipcode') {
                        //Optimized By HJ On 27-07-2022 Start
                        $fromTypeArr = $toTypeArr = array();
                        if (isset($campRegionDataArr[$campaignId][$fromZip]['zipcode']['source'])) {
                            $fromTypeArr = $campRegionDataArr[$campaignId][$fromZip]['zipcode']['source'];
                        }
                        if (isset($campRegionDataArr[$campaignId][$toState]['state']['destination'])) {
                            $toTypeArr = $campRegionDataArr[$campaignId][$toState]['state']['destination'];
                        }
                        $businessesCampaignRegion = array_merge($fromTypeArr, $toTypeArr);
                        $regionCount = 2;
                        if ($campCategory == 2) {
                            $businessesCampaignRegion = $fromTypeArr;
                            $regionCount = 1;
                        }
                        if (!empty($businessesCampaignRegion) && count($businessesCampaignRegion) == $regionCount) {
                            $flag = 1;
                            goto stopL1;
                        }
                    }
                    if ($regionType == 'zipcode') {
                        //Optimized By HJ On 27-07-2022 Start
                        $fromTypeArr = $toTypeArr = array();
                        if (isset($campRegionDataArr[$campaignId][$fromZip]['zipcode']['source'])) {
                            $fromTypeArr = $campRegionDataArr[$campaignId][$fromZip]['zipcode']['source'];
                        }
                        if (isset($campRegionDataArr[$campaignId][$toAreaCode]['areacode']['destination'])) {
                            $toTypeArr = $campRegionDataArr[$campaignId][$toAreaCode]['areacode']['destination'];
                        }
                        $businessesCampaignRegion = array_merge($fromTypeArr, $toTypeArr);
                        $regionCount = 2;
                        if ($campCategory == 2) {
                            $businessesCampaignRegion = $fromTypeArr;
                            $regionCount = 1;
                        }
                        if (!empty($businessesCampaignRegion) && count($businessesCampaignRegion) == $regionCount) {
                            $flag = 1;
                            goto stopL1;
                        }
                    }
                }
            }
            stopL1:
            if ($flag == 1) {
                $campaignIdsUpdate[] = $campaignId;
            }
        }
        //print_r($campaignIdsUpdate);
        return $campaignIdsUpdate;
    }

    public function checkSoldToBuyer($campaignIds, $leadId, $verify = 0) {
        $duplicateArray = $campIdArr = $businessIdArr = array();
        if ($verify == 0) {
            $routeData = $data = LeadRouting::where('lead_id', $leadId)->whereIn('campaign_id', $campaignIds)->where('route_status', 'sold')->where('payout', '>', 0)->get('campaign_id')->toArray();
            for ($d = 0; $d < count($routeData); $d++) {
                if (!in_array($routeData[$d]['campaign_id'], $campIdArr)) {
                    $campIdArr[] = $routeData[$d]['campaign_id'];
                    $duplicateArray[] = $routeData[$d]['campaign_id'];
                }
            }
            if (count($campIdArr) > 0) {
                $businesses = Campaign::whereIn('campaign_id', $campaignIds)->get()->toArray();
                for ($b = 0; $b < count($businesses); $b++) {
                    if (!in_array($businesses[$b]['business_id'], $businessIdArr)) {
                        $businessIdArr[] = $businesses[$b]['business_id'];
                    }
                }
            }
            $listCamp = Campaign::whereNotIn('campaign_id', $campIdArr)->whereIn('business_id', $businessIdArr)->get('campaign_id')->toArray();
            for ($ba = 0; $ba < count($listCamp); $ba++) {
                $duplicateArray[] = $listCamp[$ba]['campaign_id'];
            }
            $campaignIds = array_diff($campaignIds, $duplicateArray);
            //echo "<pre>";print_r($campaignIds);die;
        } else if ($verify == 1) {
            $routeData = $data = LeadRouting::where('lead_id', $leadId)->where('route_status', 'sold')->where('payout', '>', 0)->get('campaign_id')->toArray();
            for ($d = 0; $d < count($routeData); $d++) {
                if (!in_array($routeData[$d]['campaign_id'], $campIdArr)) {
                    $campIdArr[] = $routeData[$d]['campaign_id'];
                }
            }
            if (count($campIdArr) > 0) {
                $businesses = Campaign::whereIn('campaign_id', $campaignIds)->get()->toArray();
                for ($b = 0; $b < count($businesses); $b++) {
                    if (!in_array($businesses[$b]['business_id'], $businessIdArr)) {
                        $businessIdArr[] = $businesses[$b]['business_id'];
                    }
                }
            }
            if (count($businessIdArr) > 0) {
                $listCamp = Campaign::whereIn('business_id', $businessIdArr)->get('campaign_id')->toArray();
                foreach ($listCamp as $dupBus) {
                    $duplicateArray[] = $dupBus['campaign_id'];
                }
            }
            $campaignIds = array_diff($campaignIds, $duplicateArray);
        }
        return $campaignIds;
    }

    public function checkDuplicateBuyer($campaignIds, $email, $phone, $leadId, $dateTime, $categoryId=1) {
        $soldBuyer = [];
        $date30 = new DateTime($dateTime);
        $date30->modify('-30 days');
        $formatted_date_30 = $date30->format('Y-m-d H:i:s');
        // check 30 days leads
//        $leadData30 = DB::select("select lead_id from `lead` where (lead_generated_at between '" . $formatted_date_30 . "' and '" . $dateTime . "') AND lead_category_id = " . $categoryId . " AND lead_id <> " . $leadId . " AND (email = '" . $email . "' or phone = '" . $phone . "')");
        $leadData30 = DB::select("select lead_id from `lead` where (lead_generated_at between '" . $formatted_date_30 . "' and '" . $dateTime . "') AND lead_category_id = " . $categoryId . " AND lead_id <> " . $leadId . " AND phone = '" . $phone . "' ");
        if (count($leadData30) > 0) {
            $leads = [];
            foreach ($leadData30 as $lead) {
                $leads[] = $lead->lead_id;
            }
            if (count($leads) > 0) {
                $leadVal = implode(",", $leads);
                $countRouting = DB::select("select distinct(campaign_id) as id from `lead_routing` where lead_id in (" . $leadVal . ") and route_status = 'sold'");
                if (count($countRouting) > 0) {
                    foreach ($countRouting as $route) {
                        $businesses = Campaign::where('campaign_id', $route->id)->first();
                        $listCamp = Campaign::where('campaign_id', '<>', $route->id)->where('business_id', $businesses->business_id)->get('campaign_id');
                        foreach ($listCamp as $dupBus) {
                            if (!in_array($dupBus->campaign_id, $soldBuyer)) {
                                $soldBuyer[] = $dupBus->campaign_id;
                            }
                        }
                        if (!in_array($route->id, $soldBuyer)) {
                            $soldBuyer[] = $route->id;
                        }
                    }
                }
            }
        }
        return array_diff($campaignIds, $soldBuyer);
    }

    public function LeadRoutelog($leadid, $log, $type) {
        Logs::create([
            'lead_id' => $leadid,
            'log' => $log,
            'log_srno' => $type,
            'created_at' => date("Y-m-d H:i:s")
        ]);
    }

    public function checkFund($callcampIds, $coverageType) {
        $logicCampaign = new LeadCampaignLogic();
        $campAssocArr = $businessIdArr = $businessAssocArr = $finalcamid = $campaignBalance = array();
        if (count($callcampIds) > 0) {
            DB::table('dev_debug')->insert(['sr_no' => 52, 'result' => "checkFund callcampIds > " . json_encode($callcampIds, true), 'created_at' => date('Y-m-d H:i:s')]);
            $getCampData = Campaign::whereIn('campaign_id', $callcampIds)->get(['campaign_id', 'business_id', 'campaign_type_id', 'payment_type', 'credit_available', 'credit_reserved', 'free_lead'])->toArray();
            for ($x = 0; $x < count($getCampData); $x++) {
                $campAssocArr[$getCampData[$x]['campaign_id']] = $getCampData[$x];

                if (!in_array($getCampData[$x]['business_id'], $businessIdArr)) {
                    $businessIdArr[] = $getCampData[$x]['business_id'];
                }
            }
        }
        DB::table('dev_debug')->insert(['sr_no' => 52, 'result' => "checkFund campAssocArr > " . json_encode($campAssocArr, true) . "== businessIdArr==" . json_encode($businessIdArr, true), 'created_at' => date('Y-m-d H:i:s')]);
        if (count($businessIdArr) > 0) {
            $getBusinessData = Business::whereIn('business_id', $businessIdArr)->get(['credit_available', 'credit_reserved', 'business_id', "payment_type"])->toArray();
            for ($y = 0; $y < count($getBusinessData); $y++) {
                $businessAssocArr[$getBusinessData[$y]['business_id']] = $getBusinessData[$y];
            }
        }

        if (count($callcampIds) > 0) {
            foreach ($callcampIds as $key => $campid) {
                $campaignDetails = $campAssocArr[$campid];
                $balance = 0;
                DB::table('dev_debug')->insert(['sr_no' => 52, 'result' => "checkFund camp=" . $campid . ", payment_type = " . $campaignDetails['payment_type'], 'created_at' => date('Y-m-d H:i:s')]);

                $leadPayout = CommonFunctions::getCampaignPayout($campid, $coverageType, 0, 1);
                DB::table('dev_debug')->insert(['sr_no' => 52, 'result' => "checkFund camp= " . $campid . ", leadPayout=" . $leadPayout, 'created_at' => date('Y-m-d H:i:s')]);
                if ($leadPayout > 0) {
                    if (isset($businessAssocArr[$campaignDetails['business_id']])) {
                        $businessesData = $businessAssocArr[$campaignDetails['business_id']];
                        DB::table('dev_debug')->insert(['sr_no' => 52, 'result' => "checkFund camp= " . $campid . ",  businesspayment_type=" . $businessesData['payment_type'] . " , campaignpayment_type=" . $campaignDetails['payment_type'], 'created_at' => date('Y-m-d H:i:s')]);
                        if ($businessesData['payment_type'] == 'pre') { // Check if business payment_type is PrePayment
                            DB::table('dev_debug')->insert(['sr_no' => 52, 'result' => "checkFund camp= " . $campid . ", businesspayment_type=" . $businessesData['payment_type'], 'created_at' => date('Y-m-d H:i:s')]); // Check if business payment_type is PrePayment
                            if ($campaignDetails['payment_type'] == 0) {
                                // business level balance;
                                if ($businessesData['credit_available'] >= $leadPayout && $leadPayout > 0) {
                                    $finalcamid[] = $campid;
                                    $campaignBalance[$campid] = $businessesData['credit_available'];
                                }
                            } else {
                                // campaign level balance
                                if ($campaignDetails['credit_available'] >= $leadPayout && $leadPayout > 0) {
                                    $finalcamid[] = $campid;
                                    $campaignBalance[$campid] = $campaignDetails['credit_available'];
                                }
                            }
                        } else { // If payment_type is PostPayment then no need to check credit availibility
                            $finalcamid[] = $campid;
                            $campaignBalance[$campid] = $campaignDetails['credit_available'];
                        }
                    }
                }
            }
        }
        DB::table('dev_debug')->insert(['sr_no' => 52, 'result' => "finalcamid=" . json_encode($campaignBalance, true), 'created_at' => date('Y-m-d H:i:s')]);
        return $finalcamid;
    }

    public function getactivecallcampaigns($coverageType, $lead_category) {
        DB::table('dev_debug')->insert(['sr_no' => 52, 'result' => "getactivecallcampaigns try", 'created_at' => date('Y-m-d H:i:s')]);
        $campaigns = DB::table('campaign')->select(["campaign_id"])
                        ->join('business', 'business.business_id', '=', 'campaign.business_id')
                        ->where("business.status", "active")
                        ->where("campaign.is_active", "yes")
                        ->where("campaign.lead_category_id", $lead_category)
                        ->where("campaign.lead_type_id", 2)
                        ->whereIn("campaign.call_type", ["inbound", "both"])
                        ->get()->toArray();
        $campids = [];
        foreach ($campaigns as $key => $value) {
            $campids[] = $value->campaign_id;
        }
        DB::table('dev_debug')->insert(['sr_no' => 52, 'result' => "getactivecallcampaigns comapigns = " . json_encode($campids, true), 'created_at' => date('Y-m-d H:i:s')]);
        $campaignmoving = CampaignMoving::whereIn("campaign_id", $campids)->where("move_type", $coverageType)->pluck('campaign_id')->toArray();
        $campaignJunk = CampaignJunkType::whereIn("campaign_id", $campids)->pluck('campaign_id')->toArray();
        $campaignHeavy = CampaignHeavyLifting::whereIn("campaign_id", $campids)->where("move_type", $coverageType)->pluck('campaign_id')->toArray();
        $campaignCar = CampaignCarTransport::whereIn("campaign_id", $campids)->where("move_type", $coverageType)->pluck('campaign_id')->toArray();
        $callcampIds = array_merge($campaignmoving, $campaignJunk, $campaignHeavy, $campaignCar);
        DB::table('dev_debug')->insert(['sr_no' => 52, 'result' => 'getactivecallcampaigns camps =' . json_encode($callcampIds, true), 'created_at' => date('Y-m-d H:i:s')]);
        return $callcampIds;
        // echo "<pre>finalCamId = "; print_r(  $finalCamId);die;
    }

    public function checkBusinessLeadCampaignLogic($campaignIds, $coverageType, $date, $datetime, $remainSlot, $leadId, $lead_category_id, $call, $directRoute = 0) {
        //Added By BK On 29/02/2024 For get campaign payout
        $payoutField = "automated_outbound";
        if ($call > 0) { // For Inbound
            $payoutField = "automated_inbound";
        }
        $campaignPayout = CampaignCallPayout::whereIn('campaign_id', $campaignIds)->get(['campaign_id', $payoutField])->toArray();
        $campaignPayoutArray = [];
        for ($p=0; $p < count($campaignPayout); $p++) {
            $campaignPayoutArray[$campaignPayout[$p]['campaign_id']] = $campaignPayout[$p][$payoutField];
        }
        //Ended get campaign payout

        $scoreLeadPriceObj = new ScoreLead();
        $getAllCampData = Campaign::whereIn('campaign_id', $campaignIds)->get(['campaign_id', 'default_score', 'additional_score', 'subscription_score', 'credit_available', 'credit_reserved', 'payment_type', 'business_id', 'campaign_type_id', 'free_lead'])->toArray();
        $businessesCampaignPremium = $businessesCampaignExclusive = $businessesCampaignDualSlot = $allTypeCampIds = $finalCampaignIds = $campaignSortBundle = $businessesDataArr = $dataInsert = array();

        for ($v = 0; $v < count($getAllCampData); $v++) {
            $campAssocDataArr[$getAllCampData[$v]['campaign_id']] = $getAllCampData[$v];
            /* if (!in_array($getAllCampData[$v]['business_id'], $businessIdArr)) { */
            $businessIdArr[$getAllCampData[$v]['campaign_id']] = $getAllCampData[$v]['business_id'];
            /* } */
            if ($getAllCampData[$v]['campaign_type_id'] == 1) {
                $businessesCampaignPremium[] = $getAllCampData[$v];
            } else if ($getAllCampData[$v]['campaign_type_id'] == 2) {
                $businessesCampaignExclusive[] = $getAllCampData[$v];
            } else if ($getAllCampData[$v]['campaign_type_id'] == 6) {
                $businessesCampaignDualSlot[] = $getAllCampData[$v];
            }
            $campTypeArr[$getAllCampData[$v]['campaign_id']] = $getAllCampData[$v]['campaign_type_id'];
            $allTypeCampIds[$getAllCampData[$v]['campaign_id']] = $getAllCampData[$v]['default_score'] + $getAllCampData[$v]['additional_score'] + $getAllCampData[$v]['subscription_score'];
        }

        $businessesData = Business::whereIn('business_id', $businessIdArr)->get(['payment_type', 'credit_available', 'credit_reserved', 'business_id'])->toArray();
        //echo "<pre>";print_r($businessesData);die;
        for ($x = 0; $x < count($businessesData); $x++) {
            $businessesDataArr[$businessesData[$x]['business_id']] = $businessesData[$x];
        }
        //echo "<pre>";print_r($businessesDataArr);die;
        if (count($businessesCampaignPremium) > 0) {
            //Comment By BK on 28/05/2025 as per discussion with client
            /*$allTypeCampIds = $scoreLeadPriceObj->countScoreRemainingTime($date, $datetime, $allTypeCampIds);
            if ($call == 0) {
                $allTypeCampIds = $scoreLeadPriceObj->countScoreLastLead($allTypeCampIds);
            } else {
                $allTypeCampIds = $scoreLeadPriceObj->countScoreLastPhoneLead($allTypeCampIds);
                //echo "<pre>";print_r($allTypeCampIds);die;
            }

            $allTypeCampIds = $scoreLeadPriceObj->countScoreCampaignFund($allTypeCampIds);*/

            //Added By BK on 28/05/2025 as per discussion with client
            $allTypeCampIds = $scoreLeadPriceObj->countScoreLeadPrice($allTypeCampIds);
            $this->LeadRoutelog($leadId, 'countScoreLeadPrice = ' . json_encode($allTypeCampIds), 33);

            $allTypeCampIds = $scoreLeadPriceObj->countScoreRecurrentPayment($allTypeCampIds);
            $this->LeadRoutelog($leadId, 'countScoreRecurrentPayment =' . json_encode($allTypeCampIds), 35);

            $allTypeCampIds = $scoreLeadPriceObj->countScoreCampaignFund($allTypeCampIds);
            $this->LeadRoutelog($leadId, 'countScoreCampaignFund =' . json_encode($allTypeCampIds), 35);

            if ($lead_category_id == 2) {
                $logicJunk = new LogicJunk();
                $allTypeCampIds = $logicJunk->junkScoreCalculate($allTypeCampIds);
                $this->LeadRoutelog($leadId, 'junkScoreCalculate =' . json_encode($allTypeCampIds), 36);
                //echo "<pre>";print_r($allTypeCampIds);die;
            }
            if ($lead_category_id == 3) {
                $logicHeavyLift = new LogicHeavyLift();
                $allTypeCampIds = $logicHeavyLift->heavyLiftScoreCalculate($allTypeCampIds);
                $this->LeadRoutelog($leadId, 'junkScoreCalculate =' . json_encode($allTypeCampIds), 36);
                //echo "<pre>Score==";print_r($allTypeCampIds);die;
            }
            if ($lead_category_id == 4) {
                $logicCarTransport = new LogicCarTransport();
                $allTypeCampIds = $logicCarTransport->carTransportScoreCalculate($allTypeCampIds);
                $this->LeadRoutelog($leadId, 'carTransportScoreCalculate =' . json_encode($allTypeCampIds), 37);
                //echo "<pre>";print_r($allTypeCampIds);die;
            }
            //echo "<pre>";print_r($allTypeCampIds);die;
        }
        //echo "<pre>";print_r($allTypeCampIds);die;
        foreach ($allTypeCampIds as $key => $val) {
            $finalCampaignIds[$key] = $val;
        }
        $this->LeadRoutelog($leadId, 'checkBusinessLeadCampaignLogic organic =' . json_encode($finalCampaignIds), 38);
        foreach ($finalCampaignIds as $campaignIdKey => $score) {
            //$leadPayout = CommonFunctions::getCampaignPayout($campaignIdKey, $coverageType, 0, $call); //Comment By BK On 29/02/2024 as per discussion with MA and DS
            $leadPayout = $campaignPayoutArray[$campaignIdKey] ?? 0;
            //check here pre or post payment type
            if (isset($campAssocDataArr[$campaignIdKey])) {
                $businessesCampaign = $campAssocDataArr[$campaignIdKey];
                if (isset($businessesDataArr[$businessesCampaign['business_id']])) {
                    $businessesData = $businessesDataArr[$businessesCampaign['business_id']];
                }
                if ($businessesCampaign['payment_type'] == 0) {
                    $creditAvailable = $businessesData['credit_available'];
                    $creditReserved = $businessesData['credit_reserved'];
                } else {
                    $creditAvailable = $businessesCampaign['credit_available'];
                    $creditReserved = $businessesCampaign['credit_reserved'];
                }
                $isFreeLead = "no";
                /*if ($creditAvailable >= $leadPayout) {*/
                    if ($businessesCampaign['free_lead'] > 0) {
                        $applyFreeLead = $this->checkCampaingBalance($campaignIdKey, $leadPayout, $creditAvailable);
                        if ($applyFreeLead > 0) {
                            $isFreeLead = "yes";
                            $leadPayout = 0;
                            $leftFreeLead = ($businessesCampaign['free_lead'] - 1);
                            /*Campaign::where('campaign_id', $campaignIdKey)->update(['free_lead' => $leftFreeLead]);*/
                            DB::select("UPDATE `campaign` set `free_lead`=(`free_lead`-1) where `campaign_id`='" . $campaignIdKey . "'");
                            $this->LeadRoutelog($leadId, 'Campaign Free Lead Applied 2 == ' . $campaignIdKey . ',Free Lead=' . $businessesCampaign['free_lead'] . "-1==" . $leftFreeLead, 59);
                        }
                    }

                    if ($leadPayout > 0) {
                        if ($businessesCampaign['payment_type'] == 0) {
                            $creditAvailable -= $leadPayout;
                            $creditReserved += $leadPayout;
                            DB::select("UPDATE `business` set `credit_available`=(`credit_available`-$leadPayout) where `business_id`='" . $businessesCampaign['business_id'] . "'");
                            //Business::where('business_id', $businessesCampaign['business_id'])->update(['credit_available' => $creditAvailable]);
                        } else {
                            $creditAvailable -= $leadPayout;
                            $creditReserved += $leadPayout;
                            DB::select("UPDATE `campaign` set `credit_available`=(`credit_available`-$leadPayout) where `campaign_id`='" . $campaignIdKey . "'");
                            //Campaign::where('campaign_id', $campaignIdKey)->update(['credit_available' => $creditAvailable]);
                        }
                    }
                    $this->LeadRoutelog($leadId, 'checkBusinessLeadCampaignLogic organic =' . $campaignIdKey . "==" . $creditAvailable . "==" . $leadPayout . "==" . $businessesCampaign['payment_type'] . "==", 38);

                    $dataInsert[] = [
                        'lead_id' => $leadId,
                        'campaign_id' => $campaignIdKey,
                        'score' => $score,
                        'payout' => $leadPayout,
                        'route_status' => "unsold",
                        'is_free_lead' => $isFreeLead,
                        'created_at' => date("Y-m-d H:i:s"),
                        'balance_after_charge' => $creditAvailable,
                        'payment_type' => $businessesCampaign['payment_type']
                    ];
                    $campaignSortBundle[$campaignIdKey] = $score;
                /*}*/
            }
        }

        if (count($dataInsert) > 0) {
            LeadRouting::insert($dataInsert);
        }
        //echo "<pre>";print_r($campaignSortBundle);die;
        return $campaignSortBundle;
    }

    public function checkFundOrganic($businessesCampaignData, $campaignIdArr, $businessIdArr) {
        //echo "<pre>";print_r($campaignIdArr);die;
        $organicPayoutArr = $businessDataArr = array();
        if (count($campaignIdArr) > 0) {
            $businessesCampaignPayout = CampaignOrganic::whereIn('campaign_id', $campaignIdArr)->get(['call_charge', 'campaign_id'])->toArray();
            for ($cv = 0; $cv < count($businessesCampaignPayout); $cv++) {
                $organicPayoutArr[$businessesCampaignPayout[$cv]['campaign_id']] = $businessesCampaignPayout[$cv]['call_charge'];
            }
            $businessesData = Business::whereIn('business_id', $businessIdArr)->get(['payment_type', 'credit_available', 'credit_reserved', 'business_id'])->toArray();
            for ($bv = 0; $bv < count($businessesData); $bv++) {
                $businessDataArr[$businessesData[$bv]['business_id']] = $businessesData[$bv];
            }
        }

        $campaignIds = array();
        foreach ($businessesCampaignData as $campaignData) {
            //echo "<pre>";print_r($campaignData['business_campaign']);die;
            if (isset($campaignData['business_campaign'])) {
                foreach ($campaignData['business_campaign'] as $campaign) {
                    //echo "<pre>";print_r($campaign);die;
                    $payout = 0;
                    //echo "<pre>";print_r($businessesCampaignPayout);die;
                    if (isset($organicPayoutArr[$campaign['campaign_id']])) {
                        $payout = $organicPayoutArr[$campaign['campaign_id']];
                    }
                    if ($payout > 0) {
                        // Condition to check business payment type - added by MD
                        //$businessesData = Business::where('id', $campaign['business_id'])->get(['payment_type', 'credit_available', 'credit_reserved'])->first();
                        if (isset($businessDataArr[$campaign['business_id']])) { // Check if business payment_type
                            $payment_type = $businessDataArr[$campaign['business_id']]['payment_type'];
                            $credit_available = $businessDataArr[$campaign['business_id']]['credit_available'];
                            if ($payment_type == "pre") { // Check if business payment_type is PrePayment
                                //echo $campaign['business_id'];die;
                                if ($campaign['payment_type'] == 0) {
                                    if ($credit_available >= $payout && $payout > 0) {
                                        $campaignIds[] = $campaign['campaign_id'];
                                    }
                                } else {
                                    if ($campaign['credit_available'] >= $payout && $payout > 0) {
                                        $campaignIds[] = $campaign['campaign_id'];
                                    }
                                }
                            } else { // If payment_type is PostPayment then no need to check credit availibility
                                $campaignIds[] = $campaign['campaign_id'];
                            }
                        }
                    }
                }
            }
        }
        //echo "<pre>";print_r($campaignIds);die;
        return $campaignIds;
    }

    public function activeCamapignWholeseller($coverageType, $leadType = 0, $isVerified = 0, $leadId = 0) {
        $status = 'yes';
        $categoryId = 1;
        $leadRoute = $isVerified = 0;
        $movingAllyBusinessId = 462;
        $keyword = $where = "";
        if ($isVerified == 'yes') {
            $isVerified = 1;
        }
        if (!empty($keyword)) {
            $where = " AND c.is_remove_truck_rental = 'no'";
        }

        if ($leadRoute > 0) {
            /*$query = Business::with([
                        'businessCampaign' => function ($query) use ($status, $coverageType, $leadType, $isVerified, $categoryId, $keyword) {
                            $query->where('lead_status', 'like', '%' . $isVerified . '%')->where('is_active', '=', $status)->where('lead_type_id', $leadType)->where('lead_category_id', $categoryId)->where('campaign_type_id', 2);
                            if (!empty($keyword)) {
                                $query->where('is_remove_truck_rental', 'no');
                            }
                        },
                        'businessCampaign.campaignMoving' => function ($query) use ($coverageType) {
                            $query->where('move_type', $coverageType);
                        }
            ]);
            $businessCampaign = $query->where('status', 'active')->whereIn('business_id', [55, 992])->where('buyer_type_id', 3)->get();*/
            $businessCampaign = DB::select("SELECT b.business_id, c.campaign_id FROM `business` b INNER JOIN `campaign` c ON b.business_id = c.business_id
                                INNER JOIN `campaign_moving` cm ON c.campaign_id = cm.campaign_id
                                WHERE c.lead_status LIKE '%".$isVerified."%' AND c.is_active = 'yes' AND c.lead_type_id = $leadType AND c.lead_category_id = $categoryId AND c.campaign_type_id = 2 AND cm.move_type = '".$coverageType."' AND b.status = 'active' AND b.business_id IN (55, 992) AND b.buyer_type_id = 3 $where");
        } else {
            /*$query = Business::with([
                        'businessCampaign' => function ($query) use ($status, $coverageType, $leadType, $isVerified, $categoryId, $keyword) {
                            $query->where('lead_status', 'like', '%' . $isVerified . '%')->where('is_active', '=', $status)->where('lead_type_id', $leadType)->where('lead_category_id', $categoryId)->where('campaign_type_id', 2);
                            if (!empty($keyword)) {
                                $query->where('is_remove_truck_rental', 'no');
                            }
                        },
                        'businessCampaign.campaignMoving' => function ($query) use ($coverageType) {
                            $query->where('move_type', $coverageType);
                        }
            ]);
            $businessCampaign = $query->where('status', 'active')->whereIn('business_id', [55, 992])->where('buyer_type_id', 3)->get();*/
            $businessCampaign = DB::select("SELECT b.business_id, c.campaign_id FROM `business` b INNER JOIN `campaign` c ON b.business_id = c.business_id
                                INNER JOIN `campaign_moving` cm ON c.campaign_id = cm.campaign_id
                                WHERE c.lead_status LIKE '%".$isVerified."%' AND c.is_active = 'yes' AND c.lead_type_id = $leadType AND c.lead_category_id = $categoryId AND c.campaign_type_id = 2 AND cm.move_type = '".$coverageType."' AND b.status = 'active' AND b.business_id IN (55, 992) AND b.buyer_type_id = 3 $where");
        }
        if ($businessCampaign) {
            return $businessCampaign;
        } else {
            return false;
        }
    }

    public function checkOrganicCampaignLogic($campaignIds, $coverageType, $date, $datetime, $remainSlot, $leadId, $call = 0) {
        //Added By BK On 29/02/2024 For get campaign payout
        $payoutField = "automated_outbound";
        if ($call > 0) { // For Inbound
            $payoutField = "automated_inbound";
        }
        $campaignPayout = CampaignCallPayout::whereIn('campaign_id', $campaignIds)->get(['campaign_id', $payoutField])->toArray();
        $campaignPayoutArray = [];
        for ($p=0; $p < count($campaignPayout); $p++) {
            $campaignPayoutArray[$campaignPayout[$p]['campaign_id']] = $campaignPayout[$p][$payoutField];
        }
        //Ended get campaign payout

        //For Organic
        $campaignDetail = Campaign::whereIn('campaign_id', $campaignIds)->where('campaign_type_id', 4)->get(['campaign_id', 'default_score', 'additional_score', 'subscription_score']);
        $organicCampaignIds = $campaignSortBundle = array();
        if ($campaignDetail) {
            foreach ($campaignDetail as $campaign) {
                $organicCampaignIds[$campaign->campaign_id] = $campaign->default_score + $campaign->additional_score + $campaign->subscription_score;
            }
        }
        arsort($organicCampaignIds);

        if (count($organicCampaignIds) > 0) {
            // Store possible matches in lead_matches table
            $dataInsert = [];
            foreach ($organicCampaignIds as $campId => $campScore) {
                $payoutMatch = CommonFunctions::getCampaignPayout($campId, $coverageType, 0, $call); //Comment By BK On 29/02/2024 as per discussion with MA and DS
                $dataInsert[] = [
                    'lead_id' => $leadId,
                    'campaign_id' => $campId,
                    'score' => $campScore,
                    'payout' => $campaignPayoutArray[$campId] ?? 0,
                    'route_status' => "unsold",
                    'is_free_lead' => "no",
                    'created_at' => date("Y-m-d H:i:s")
                ];
            }
            LeadRouting::insert($dataInsert);
        }
        $top1 = array_slice($organicCampaignIds, 0, 1, true);
        $finalCampaignIds = $top1;

        foreach ($finalCampaignIds as $campaignIdKey => $score) {
            //$leadPayout = CommonFunctions::getCampaignPayout($campaignIdKey, $coverageType, 0, $call); //Comment By BK On 29/02/2024 as per discussion with MA and DS
            $leadPayout = $campaignPayoutArray[$campaignIdKey] ?? 0;
            $businessesCampaign = Campaign::where('campaign_id', $campaignIdKey)->get(['credit_available', 'credit_reserved', 'payment_type', 'business_id', 'free_lead'])->first();
            if ($businessesCampaign->payment_type == 0) {
                $businessDetail = Business::where('business_id', $businessesCampaign->business_id)->get(['credit_available', 'credit_reserved'])->first();
                //dd($businessesData);
                $creditAvailable = $businessDetail->credit_available;
                $creditReserved = $businessDetail->credit_reserved;
            } else {
                $creditAvailable = $businessesCampaign->credit_available;
                $creditReserved = $businessesCampaign->credit_reserved;
            }
            $isFreeLead = "no";
            if ($creditAvailable >= $leadPayout) {
                if ($businessesCampaign->free_lead > 0) {
                    //For Check Campaign Free Lead Count with Balance As Per Discuss with MV Start
                    $freeLead = $this->checkCampaingBalance($campaignIdKey, $leadPayout, $creditAvailable);
                    if ($freeLead > 0) {
                        $isFreeLead = "yes";
                        $leadPayout = 0;
                        $leftFreeLead = ($businessesCampaign->free_lead - 1);
                        Campaign::where('campaign_id', $campaignIdKey)->update(['free_lead' => $leftFreeLead]);
                        $this->LeadRoutelog($leadId, 'Campaign Free Lead Applied 3 == ' . $campaignIdKey . ',Free Lead=' . $businessesCampaign->free_lead . "-1==" . $leftFreeLead, 59);
                    }

                    if ($freeLead > 0) {
                        $leadPayout = 0;
                    }
                    //For Check Campaign Free Lead Count with Balance As Per Discuss with MV End
                }

                if ($leadPayout > 0) {
                    if ($businessesCampaign->payment_type == 0) {
                        $creditAvailable -= $leadPayout;
                        $creditReserved += $leadPayout;
                        DB::select("UPDATE `business` set `credit_available`=(`credit_available`-$leadPayout) where `business_id`='" . $businessesCampaign->business_id . "'");
                        //Business::where('business_id', $businessesCampaign->business_id)->update(['credit_available' => $creditAvailable]);
                    } else {
                        $creditAvailable -= $leadPayout;
                        $creditReserved += $leadPayout;
                        DB::select("UPDATE `campaign` set `credit_available`=(`credit_available`-$leadPayout) where `campaign_id`='" . $campaignIdKey . "'");
                        //Campaign::where('campaign_id', $campaignIdKey)->update(['credit_available' => $creditAvailable]);
                    }
                }

                LeadRouting::where('lead_id', $leadId)->where('campaign_id', $campaignIdKey)->update([
                    'balance_after_charge' => $creditAvailable,
                    'payment_type' => $businessesCampaign->payment_type,
                    'is_free_lead' => $isFreeLead,
                    'payout' => $leadPayout
                ]);

                $campaignSortBundle[$campaignIdKey] = $score;
            }
        }

        return $campaignSortBundle;
    }

}
