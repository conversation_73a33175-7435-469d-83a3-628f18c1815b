<?php

namespace App\Http\Controllers\OutboundCall;

use App\Http\Controllers\Controller;
use Auth;
use DB;
use Illuminate\Http\Request;
use App\Models\SalesRep\RepActiveCall;
use App\Models\SalesRep\CallItUserLoginLog;
use App\Models\Outbound\LeadCall;
use App\Models\Outbound\LeadCallLog;
use App\Models\Outbound\CallitSystemLogic;
//use App\Models\Outbound\CallitVoiceMessageLog;
use App\Models\Lead\Lead;
use App\Models\Lead\LeadCallBuffer;
use App\Models\Lead\LeadOldCallBuffer;
use App\Models\Lead\LeadTransferCall;
use App\Models\Lead\LeadRouting;
use App\Models\Lead\LeadMoving;
use App\Models\Lead\LeadCarTransport;
use App\Models\Lead\LeadHeavyLifting;
use App\Models\Lead\LeadFollowUp;
use App\Models\Master\MstLeadSource;
use App\Http\Controllers\LeadCallBuffer\LeadCallBufferController;
use App\Http\Controllers\API\LeadDelivery\LeadDeliveryApi;
use App\Helpers\Helper;
use App\Helpers\CommonFunctions;
use Exception;
use App\Models\Campaign\Campaign;
use App\Models\Campaign\CampaignOngoingCall;
use App\Models\Campaign\CampaignCallPayout;
use App\Models\Business\Business;
use App\Models\User;
use App\Models\Master\MstCallConfig;
use App\Models\Master\MstTimeZone;
use App\Models\Master\MstCampaignType;
use App\Models\Lead\LeadActivity;
use App\Models\B2bcalls\B2BCalls;
use DateTime;
use DateTimeZone;
use App\Http\Controllers\Lead\LeadListController;
use App\Models\Lead\LeadCustomerSmsLog;
use App\Models\Inbound\LeadIbCall;
use App\Http\Controllers\PlivoSms\FollowUpCallSms;

class OutboundCallControllerTestTransfer extends Controller {

    public function testCallR(Request $req) {
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        $called_from = "***********";
        
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $url = 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/Call/';
        $data = array(
            'from' => $called_from,
            'to' => "***********",
            'answer_url' => $matrixBaseURL_Outbound . '/testcalltocustrans',
            'hangup_url' => $matrixBaseURL_Outbound . '/testcallhangcus'
        );
        $data = json_encode($data);
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $general_variable_2 = Helper::checkServer()['general_variable_2'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
        $ch = curl_init($url);
        curl_setopt_array($ch, array(
            CURLOPT_RETURNTRANSFER => TRUE,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_USERPWD => $decVariable1 . ':' . $decVariable2,
            CURLOPT_FOLLOWLOCATION => 1,
            CURLOPT_HTTPHEADER => array('Content-type: application/json', 'Cache-Control:no-store', 'Content-Length: ' . strlen($data)),
            CURLOPT_TIMEOUT => 40
        ));
        $response = curl_exec($ch);
        DB::table('dev_debug')->insert(['sr_no' => 10, 'result' => "10 callit log Calltocurl call==" . $response]);
        //echo "<pre>Calltocurl==";print_r($response);die;
        //dd($response);
        curl_close($ch);
    }
    public function testCallC(Request $req) {
        $phoneNumber = "17739429082";
        $response = '<Response>
                        <Dial  callerId="***********">
                            <Number>' . $phoneNumber . '</Number>
                        </Dial>
                    </Response>';
        // Echo the Response
        header("Content-type: text/xml");
        DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 snapIt log callforwardtomain call==" . $response]);
        echo $response;
        exit;
    }
    public function testCallB(Request $req) {
        $Parameters = $req->all();
        $callUuid = "";
        if (isset($Parameters['CallUUID'])) {
           $callUuid = $Parameters['CallUUID'];
        }
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $plivoBaseURL = 'https://api.plivo.com/v1/Account/' . $decVariable1;
        $url = $plivoBaseURL . '/Call/' . $callUuid; //Code comment by BK on 25/07/2025
        //$url = $plivoBaseURL . '/Call/' . $callUuid . '/Transfer';
        
        $datatran = array('legs' => 'aleg', 'aleg_method' => 'POST', 'aleg_url' => $matrixBaseURL_Outbound . '/testcalltomovertrans');
        
        $datatran = $dataEncode = json_encode($datatran);

        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $general_variable_2 = Helper::checkServer()['general_variable_2'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
        $ch = curl_init($url);
        curl_setopt_array($ch, array(
            CURLOPT_RETURNTRANSFER => TRUE,
            CURLOPT_POSTFIELDS => $datatran,
            CURLOPT_USERPWD => $decVariable1 . ':' . $decVariable2,
            CURLOPT_FOLLOWLOCATION => 1,
            CURLOPT_HTTPHEADER => array('Content-type: application/json', 'Cache-Control:no-store', 'Content-Length: ' . strlen($datatran)),
            CURLOPT_TIMEOUT => 40
        ));
        $response = curl_exec($ch);
        DB::table('dev_debug')->insert(['sr_no' => 6, 'result' => "6 snapIt log Calltocurl call==" . $response]);
        //echo "<pre>Calltocurl==";print_r($response);die;
        //dd($response);
        curl_close($ch);
        return $response;

    }
    public function testCallM(Request $req) {
        $phoneNumber = "18888122668";
        $response = '<Response>
                        <Dial  callerId="17739429082">
                            <Number>' . $phoneNumber . '</Number>
                        </Dial>
                    </Response>';
        // Echo the Response
        header("Content-type: text/xml");
        DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 snapIt log callforwardtomain call==" . $response]);
        echo $response;
        exit;
    }

}
