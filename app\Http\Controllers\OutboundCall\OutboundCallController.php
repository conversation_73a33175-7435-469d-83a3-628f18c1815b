<?php

namespace App\Http\Controllers\OutboundCall;

use App\Http\Controllers\Controller;
use Auth;
use DB;
use Illuminate\Http\Request;
use App\Models\SalesRep\RepActiveCall;
use App\Models\SalesRep\CallItUserLoginLog;
use App\Models\Outbound\LeadCall;
use App\Models\Outbound\LeadCallLog;
use App\Models\Outbound\CallitSystemLogic;
//use App\Models\Outbound\CallitVoiceMessageLog;
use App\Models\Lead\Lead;
use App\Models\Lead\LeadCallBuffer;
use App\Models\Lead\LeadOldCallBuffer;
use App\Models\Lead\LeadTransferCall;
use App\Models\Lead\LeadRouting;
use App\Models\Lead\LeadMoving;
use App\Models\Lead\LeadCarTransport;
use App\Models\Lead\LeadHeavyLifting;
use App\Models\Lead\LeadFollowUp;
use App\Models\Master\MstLeadSource;
use App\Http\Controllers\LeadCallBuffer\LeadCallBufferController;
use App\Http\Controllers\API\LeadDelivery\LeadDeliveryApi;
use App\Helpers\Helper;
use App\Helpers\CommonFunctions;
use Exception;
use App\Models\Campaign\Campaign;
use App\Models\Campaign\CampaignOngoingCall;
use App\Models\Campaign\CampaignCallPayout;
use App\Models\Business\Business;
use App\Models\User;
use App\Models\Master\MstCallConfig;
use App\Models\Master\MstTimeZone;
use App\Models\Master\MstCampaignType;
use App\Models\Lead\LeadActivity;
use App\Models\B2bcalls\B2BCalls;
use DateTime;
use DateTimeZone;
use App\Http\Controllers\Lead\LeadListController;
use App\Models\Lead\LeadCustomerSmsLog;
use App\Models\Inbound\LeadIbCall;
use App\Http\Controllers\PlivoSms\FollowUpCallSms;

class OutboundCallController extends Controller {

    // Logic for outbound call logic
    public $hangup = 'autooutboundcallhangup';
    public $answer = 'autooutboundcallanswer';
    public $digit_select = 'autooutbounddigitselect';

    public function getOutboundCallHangup(Request $request) {
        $Parameters = $request->all();
        $lead_call_id = isset($Parameters['lead_call_id']) ? $Parameters['lead_call_id'] : 0;
        $lead_id = isset($Parameters['lead_id']) ? $Parameters['lead_id'] : 0;
        $user_id = isset($Parameters['user_id']) ? $Parameters['user_id'] : 0;
        $auto = isset($Parameters['auto']) ? $Parameters['auto'] : 0;
        $b2bcall = isset($Parameters['b2bcall']) ? $Parameters['b2bcall'] : 0; //Added By HJ On 26-09-2023 For B2B Call Answer Process
        $this->createOutboundLogs($lead_id, $lead_call_id, 1, "1 snapIt log getOutboundCallHangup lead call id==" . $lead_call_id . "==auto==" . $auto . "==user_id=" . $user_id . "==post data" . json_encode($Parameters));
        try {
            $calluuId = "";
            $callDuration = "0";
            if (isset($Parameters['CallUUID']) && trim($Parameters['CallUUID']) != "") {
                $calluuId = trim($Parameters['CallUUID']);
            }
            if (isset($Parameters['RequestUUID']) && trim($Parameters['RequestUUID']) != "") {
                $calluuId = trim($Parameters['RequestUUID']);
            }
            if (isset($Parameters['Duration'])) {
                $callDuration = $Parameters['Duration'];
            }
            if ($b2bcall > 0) {
                $b2bcalldata = B2BCalls::where('b2b_calls_id', $b2bcall)->first();
                $call_status = $b2bcalldata->call_status;
                if ($call_status == 'ringing' || $call_status == 'not_answered') {
                    B2BCalls::where('b2b_calls_id', $b2bcall)->update(['call_status' => 'not_answered', 'call_duration' => $callDuration, 'legA_call_uuid' => $calluuId, 'updated_at' => date("Y-m-d H:i:s")]);
                } else {
                    B2BCalls::where('b2b_calls_id', $b2bcall)->update(['call_status' => 'answered', 'call_duration' => $callDuration, 'legA_call_uuid' => $calluuId, 'updated_at' => date("Y-m-d H:i:s")]);
                }
                DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 snapIt log B2B getOutboundCallHangup==" . $call_status]);
            } else {
                $getLeadCallBuffer = LeadCallBuffer::where('lead_id', $lead_id)->first();
                if (isset($getLeadCallBuffer)) {
                    $leadactiviyobj = new LeadActivity();
                    $leadactiviyobj->createActivity($lead_id, 8);
                }
                LeadCallBuffer::where('lead_id', $lead_id)->delete();
                if ($calluuId != "") {
                    //DB::table('test')->insert(['result' => "HANGUP Outbound call update==".date('Y-m-d H:i:s')."===duration==".$callDuration."==call_uuid=".$calluuId]);
                    //$outbound_bound_entry = LeadCall::where('legA_call_uuid', $calluuId)->get()->first();
                    /* if ($outbound_bound_entry->call_disposition_id > 1 || $outbound_bound_entry->call_disposition_id == 0) {
                      CallHangupEvent::dispatch($outbound_bound_entry);
                      } */
                    RepActiveCall::where('call_uuid', $calluuId)->delete();
                }
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
            $this->createOutboundLogs($lead_id, $lead_call_id, 2, "2 snapIt log getOutboundCallHangup error catched==" . $error);
        }
        $content = '<Response>
            <Speak>Thank you</Speak>                
        </Response>';
        header("Content-type: text/xml");
        echo $content;
        exit;
    }

    public function getoutboundCallAnswer(Request $request) {
        $Parameters = $request->all();
        $lead_call_id = isset($Parameters['lead_call_id']) ? $Parameters['lead_call_id'] : 0;
        $lead_id = isset($Parameters['lead_id']) ? $Parameters['lead_id'] : 0;
        $user_id = isset($Parameters['user_id']) ? $Parameters['user_id'] : 0;
        $auto = isset($Parameters['auto']) ? $Parameters['auto'] : 0;
        $b2bcall = isset($Parameters['b2bcall']) ? $Parameters['b2bcall'] : 0; //Added By HJ On 26-09-2023 For B2B Call Answer Process

        $conferenceName = "Call_".$lead_call_id;
        
        $responseX = '<Response>
                    <Conference
                        waitSound="ring"
                        startConferenceOnEnter="true"
                        endConferenceOnExit="false"
                    >'.$conferenceName.'</Conference>
                </Response>';
        // Echo the Response
        header("Content-type: text/xml");
        //DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 snapIt log callforwardtomain call==" . $response]);
        echo $responseX;

        $this->createOutboundLogs($lead_id, $lead_call_id, 1, "1 snapIt log getoutboundCallAnswer call==" . json_encode($Parameters) . "==auto==" . $auto . "==user_id=" . $user_id);
        try {
            $calluuId = "";
            if (isset($Parameters['CallUUID']) && trim($Parameters['CallUUID']) != "") {
                $calluuId = trim($Parameters['CallUUID']);
            }
            if (isset($Parameters['RequestUUID']) && trim($Parameters['RequestUUID']) != "") {
                $calluuId = trim($Parameters['RequestUUID']);
            }
            //Added By HJ On 30-03-2023 For Solved Call UUID Issue End
            if ($b2bcall > 0) {
                $this->createOutboundLogs($lead_id, $lead_call_id, 1, "1 snapIt B2B log getoutboundCallAnswer call==" . json_encode($Parameters) . "==auto==" . $auto . "==user_id=" . $user_id);
                $recording_url = $recording_id = "";
                $responsecallrecordb2b = $this->CallRecordB2B($calluuId);
                $responsecallrecordb2b = json_decode($responsecallrecordb2b, true);
                $recording_url = $responsecallrecordb2b['url'];
                $recording_id = $responsecallrecordb2b['recording_id'];

                B2BCalls::where('b2b_calls_id', $b2bcall)->update(['legA_call_uuid' => $calluuId, 'call_status' => 'ringing', 'recording_url' => $recording_url, 'recording_id' => $recording_id, 'updated_at' => date("Y-m-d H:i:s")]);
                $this->b2bcallforwardtomain($Parameters['lead_phone_number'], $Parameters['From'], $b2bcall);
            } else {
                if ($lead_call_id > 0) {
                    $OutboundCallsData = LeadCall::where('lead_call_id', $lead_call_id)->orderBy('lead_call_id', 'desc')->get()->toArray();
                } else {
                    $OutboundCallsData = LeadCall::where('lead_id', $lead_id)->orderBy('lead_call_id', 'desc')->get()->toArray();
                }
                $this->createOutboundLogs($lead_id, $lead_call_id, 2, "2 snapIt log getoutboundCallAnswer outbound data=" . json_encode($OutboundCallsData) . "==user_id=" . $user_id);
                if (count($OutboundCallsData) > 0) {
                    $outbound_id = $OutboundCallsData[0]['lead_call_id'];
                    //Added By HJ On 23-05-2023 For Automated Outbound Call Cron Start
                    if ($auto > 0) {
                        $OutboundCallsData[0]['to_number'] = $OutboundCallsData[0]['from_number'];
                        $OutboundCallsData[0]['from_number'] = $OutboundCallsData[0]['transfer_number'];
                        if ($OutboundCallsData[0]['campaign_id'] > 0 && $lead_id > 0 && $lead_call_id > 0) {
                            $call_track = ['lead_id' => $lead_id, 'campaign_id' => $OutboundCallsData[0]['campaign_id'], 'lead_call_id' => $lead_call_id, 'created_at' => date('Y-m-d H:i:s')];
                            $this->createOutboundLogs($lead_id, $lead_call_id, 3, "3 snapIt log getoutboundCallAnswer CampaignOngoingCall data=" . json_encode($OutboundCallsData) . "==user_id=" . $user_id);
                            CampaignOngoingCall::create($call_track);
                        }
                        /* Call forward to business comaigpn if press 1. */
                        $new_payout = $OutboundCallsData[0]['payout'];
                        $idFreeLead = "yes";
                        if ($new_payout > 0) {
                            $idFreeLead = "no";
                        }
                        $Lead_Routedata = [
                            'lead_id' => $lead_id,
                            'campaign_id' => $OutboundCallsData[0]['campaign_id'],
                            'payout' => $new_payout,
                            'score' => $OutboundCallsData[0]['campaign_score'],
                            'route_status' => "sold",
                            'is_free_lead' => $idFreeLead,
                            'created_at' => date("Y-m-d H:i:s")
                        ];
                        //echo "<pre>";print_r($Lead_Routedata);die;
                        $lead_routing_id = LeadRouting::create($Lead_Routedata)->lead_routing_id;
                        $this->createOutboundLogs($lead_id, $lead_call_id, 4, "4 snapIt log getoutboundCallAnswer Lead Route entry=" . json_encode($Lead_Routedata) . "==user_id=" . $user_id);
                        CommonFunctions::insertLeadCallRoutingData($lead_id, $lead_call_id, $lead_routing_id); //Added By HJ On 25-07-2023 For Insert Data Into New Table lead_call_routing
                        /* Cut Funds from the campaign */
                        $getRoutingData = LeadRouting::select(DB::raw('count(*) as totalLead'), DB::raw('SUM(payout) as totalPayout'))->where('route_status', 'sold')->where('lead_id', $lead_id)->get()->toArray();
                        //echo "<pre>";print_r($getRoutingData);die;
                        if (count($getRoutingData) > 0) {
                            $totalPayout = (isset($getRoutingData[0]['totalPayout'])) ? $getRoutingData[0]['totalPayout'] : 0;
                            Lead::where('lead_id', $lead_id)->update(['payout' => $totalPayout]);
                        }
                        $getCampaignData = Campaign::where('campaign_id', $OutboundCallsData[0]['campaign_id'])->get()->toArray();
                        $business_name = "";
                        $balance_after_charge = $getCampaignData[0]['credit_available'];
                        if (count($getCampaignData) > 0) {
                            $getBusinessData = Business::where('business_id', $getCampaignData[0]['business_id'])->get()->toArray();
                            if (count($getBusinessData) > 0) {
                                $business_name = $getBusinessData[0]['business_name'];
                                $balance_after_charge = $getBusinessData[0]['credit_available'];
                            }
                        }
                        //echo "<pre>";print_r($getCampaignData);die;
                        if ($new_payout > 0 && count($getCampaignData) > 0) {
                            if ($getCampaignData[0]['payment_type'] == 0 || $getCampaignData[0]['payment_type'] == '') {
                                if (count($getBusinessData) > 0) {
                                    //echo "<pre>";print_r($getBusinessData);die;
                                    $this->createOutboundLogs($lead_id, $lead_call_id, 5, "5 snapIt log getoutboundCallAnswer Businesses Before credit_available: $ " . $getBusinessData[0]['credit_available'] . "==user_id=" . $user_id);
                                    $balance = $getBusinessData[0]['credit_available'];
                                    $update_credit_bus_available = $balance_after_charge = ($getBusinessData[0]['credit_available'] - $new_payout);
                                    Business::where('business_id', $getCampaignData[0]['business_id'])->update(['credit_available' => $update_credit_bus_available]);
                                    $this->createOutboundLogs($lead_id, $lead_call_id, 6, "6 snapIt log getoutboundCallAnswer Businesses After credit_available: $ " . ($update_credit_bus_available) . "==user_id=" . $user_id);
                                }
                            } else {
                                $this->createOutboundLogs($lead_id, $lead_call_id, 7, "7 snapIt log getoutboundCallAnswer Campaign Before credit_available: $ " . ($getCampaignData[0]['credit_available']) . "==user_id=" . $user_id);
                                $balance = $getCampaignData[0]['credit_available'];
                                $update_credit_available = $balance_after_charge = ($getCampaignData[0]['credit_available'] - $new_payout);
                                Campaign::where('campaign_id', $getCampaignData[0]['campaign_id'])->update(['credit_available' => $update_credit_available]);
                                $this->createOutboundLogs($lead_id, $lead_call_id, 8, "8 snapIt log getoutboundCallAnswer Campaign After credit_available: $ " . ($update_credit_available) . "==user_id=" . $user_id);
                            }
                        } else {
                            $this->createOutboundLogs($lead_id, $lead_call_id, 9, "9 snapIt log getoutboundCallAnswer tweet lead Free call applied: camp Id " . $OutboundCallsData[0]['campaign_id'] . ", payout :" . $new_payout . "==user_id=" . $user_id);
                        }
                        LeadRouting::where('lead_routing_id', $lead_routing_id)->update(['balance_after_charge' => $balance_after_charge, 'payment_type' => $getCampaignData[0]['payment_type']]); //Added By HJ On 28-07-2023 For Insert Business Or Campaign Credit and Payment Type After Call Transfer
                        /* $lead_deliveryCtl = new OutboundCallLeadDeliveryController();
                          $lead_deliveryCtl->OutboundLeadDelivery($outbound_data);
                          $bufferController = new CallsbufferController();
                          $bufferController->SaveToBufferViaTweet($outbound_data->lead_id); */
                        $leadDeliverApi = new LeadDeliveryApi();
                        $jsonArray = array(
                            "campaign_id" => $OutboundCallsData[0]['campaign_id'],
                            "lead_id" => $lead_id,
                            "cost" => $new_payout,
                            "balance" => $balance,
                            "score" => $OutboundCallsData[0]['campaign_score']
                        );
                        $this->createOutboundLogs($lead_id, $lead_call_id, 10, "10 snapIt log getoutboundCallAnswer leadDeliveryTemplate request data " . json_encode($jsonArray) . "==user_id=" . $user_id);
                        $leadDeliverApi->leadDeliveryTemplate(json_encode($jsonArray));
                    }
                    //Added By HJ On 23-05-2023 For Automated Outbound Call Cron End
                    LeadCall::where('lead_call_id', $outbound_id)->update(['legA_call_uuid' => $calluuId]);
                    $this->CallRecord($calluuId);
                    $sipData = User::where('id', $user_id)->first();
                    $this->createOutboundLogs($lead_id, $lead_call_id, 44, "44 checking flow==user_id=" . $user_id);
                    $this->callforwardtomain($OutboundCallsData[0]['from_number'], $OutboundCallsData[0]['to_number'], $outbound_id, $sipData->sip_endpoint);
                }
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
            $this->createOutboundLogs($lead_id, $lead_call_id, 10, "10 snapIt log getoutboundCallAnswer error catched==" . $error . "==user_id=" . $user_id);
        }
        /*$content = '<Response><Speak>Thank you</Speak></Response>';
        header("Content-type: text/xml");
        echo $content;*/
        exit;
    }

    public function CallRecord($callUuid) {
        try {
            $endDecObj = new Helper;
            $general_variable_1 = Helper::checkServer()['general_variable_1'];
            $general_variable_2 = Helper::checkServer()['general_variable_2'];
            $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
            $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
            $plivoBaseURL = 'https://api.plivo.com/v1/Account/' . $decVariable1;
            $para = json_encode(array('time_limit' => '3600'));
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $plivoBaseURL . '/Call/' . $callUuid . '/Record/');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $para);
            curl_setopt($ch, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
            $headers = array();
            $headers[] = 'Content-Type: application/json';
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            $result = curl_exec($ch);
            DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => $plivoBaseURL . '/Call/' . $callUuid . '/Record/']);
            DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 snapIt log CallRecord call==" . $result]);
            if (curl_errno($ch)) {
                //echo 'Error:' . curl_error($ch);
            }
            curl_close($ch);
        } catch (Exception $ex) {
            $error = $ex->getMessage();
            DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 snapIt log CallRecord call catch error==" . $error]);
        }
    }

    public function CallRecordB2B($callUuid) {
        try {
            $endDecObj = new Helper;
            $general_variable_1 = Helper::checkServer()['general_variable_1'];
            $general_variable_2 = Helper::checkServer()['general_variable_2'];
            $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
            $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
            $plivoBaseURL = 'https://api.plivo.com/v1/Account/' . $decVariable1;
            $para = json_encode(array('time_limit' => '3600'));
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $plivoBaseURL . '/Call/' . $callUuid . '/Record/');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $para);
            curl_setopt($ch, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
            $headers = array();
            $headers[] = 'Content-Type: application/json';
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            $result = curl_exec($ch);
            DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 snapIt log CallRecord call==" . $result]);
            if (curl_errno($ch)) {
                //echo 'Error:' . curl_error($ch);
            }
            curl_close($ch);
            return $result;
        } catch (Exception $ex) {
            $error = $ex->getMessage();
            DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 snapIt log CallRecord call catch error==" . $error]);
        }
    }

    public function callforwardtomain($phoneNumber, $cust_dial_number,$outbound_id,$sip="") {
        // Need to add sip for b2b call
        $country_code = Helper::checkServer()['country_code'];
        if(strlen($cust_dial_number) == 10) {
            $callerID = $country_code.$cust_dial_number;    
        } elseif(strlen($cust_dial_number) == 11) {
            $callerID = '+'.$cust_dial_number;
        } else {
            $callerID = '+1'.$cust_dial_number;
        }
        
        $insertIbData = array();
        $insertIbData['lead_call_id'] = $outbound_id;
        $insertIbData['created_at'] = date('Y-m-d H:i:s');
        $insertIbData['cust_received_time'] = date('Y-m-d H:i:s');
        LeadIbCall::create($insertIbData);
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        /*$response = '<Response>
                        <Dial  callerId="' . $callerID . '" callbackUrl="' . $matrixBaseURL_Outbound . '/inboundcallcustaccept?lead_call_id=' . $outbound_id . '" action="' . $matrixBaseURL_Outbound . '/conferencecall?lead_call_id=' . $outbound_id . '">
                            <Number>' . $country_code . $phoneNumber . '</Number>
                        </Dial>
                    </Response>';*/
        /*$response = '<Response>
                        <Dial  callerId="' . $callerID . '" callbackUrl="' . $matrixBaseURL_Outbound . '/conferencecall?lead_call_id=' . $outbound_id . '">
                            <Number>' . $country_code . $phoneNumber . '</Number>
                        </Dial>
                    </Response>';            
        // Echo the Response
        header("Content-type: text/xml");
        DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 snapIt log callforwardtomain call==" . $response]);
        echo $response;
        exit;*/
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $plivoBaseURL = 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/Call/';
        $data = array(
            'from' => $callerID,
            'to' => $country_code . $phoneNumber,
            'answer_url' => $matrixBaseURL_Outbound . '/conferencecall?lead_call_id=' . $outbound_id,
            'hangup_url' => ''
        );
        $data = json_encode($data);
        $this->Calltocurl($plivoBaseURL, $data);
        
    }

    public function b2bcallforwardtomain($phoneNumber, $cust_dial_number, $b2bcall) {
        $country_code = Helper::checkServer()['country_code'];
        if(strlen($cust_dial_number) == 10) {
            $callerID = $country_code.$cust_dial_number;    
        } elseif(strlen($cust_dial_number) == 11) {
            $callerID = '+'.$cust_dial_number;
        } else {
            $callerID = '+1'.$cust_dial_number;
        }
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "0 B2B log b2bcallforwardtomain call==" . $callerID]);
        $response = '<Response>
                        <Dial callerId="' . $callerID . '" callbackUrl="' . $matrixBaseURL_Outbound . '/b2binboundcallcustaccept?b2b_call_id=' . $b2bcall . '">
                        <Number>' . $country_code . $phoneNumber . '</Number>
                        </Dial>
                    </Response>';
        // Echo the Response
        header("Content-type: text/xml");
        DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 snapIt log b2bcallforwardtomain call==" . $response]);
        echo $response;
        exit;
    }

    public function getOutboundCallCompany($leadId, $liveTransfer = 0, $inboundCall = 0) {
        $logic = new CallitSystemLogic();
        DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 snapIt log getOutboundCallCompany result==lead id==" . $leadId . "===" . $liveTransfer . "===" . $inboundCall]);
        $new_bus_fetch = $logic->LiveLeadBusinessCriteriaCheck($leadId, 0, $liveTransfer, $inboundCall);
        //echo "<pre>";print_r($new_bus_fetch);die;
        $new_campaign_fetch_array = array();
        if (isset($new_bus_fetch) && $new_bus_fetch != NULL) {
            if ($new_bus_fetch['campaign_id'] > 0) {
                $new_campaign_fetch_array['businesses_campaign_id'] = $new_bus_fetch['campaign_id'];
                $new_campaign_fetch_array['forward_number'] = $new_bus_fetch['phonenumber'];
                $new_campaign_fetch_array['score'] = $new_bus_fetch['latest_score'];
                $new_campaign_fetch_array['payout'] = $new_bus_fetch['payout'];
                $new_campaign_fetch_array['livetransfer'] = 1;
                return (object) $new_campaign_fetch_array;
            }
        } else {
            $new_campaign_fetch_array['businesses_campaign_id'] = 0;
            $new_campaign_fetch_array['forward_number'] = '';
            $new_campaign_fetch_array['score'] = 0;
            $new_campaign_fetch_array['payout'] = 0;
            $new_campaign_fetch_array['livetransfer'] = 1;
            return (object) $new_campaign_fetch_array;
        }
        return '';
    }

    public static function tokLoginLog($is_on_call, $userId) {
        $tokRepLog = CallItUserLoginLog::where('user_id', $userId)->whereNull('logout_at')->orderby('callit_user_login_log_id', 'desc')->get()->take(1)->toArray();
        if (count($tokRepLog) > 0) {
            CallItUserLoginLog::where('callit_user_login_log_id', $tokRepLog[0]['callit_user_login_log_id'])->update([
                'is_on_call' => $is_on_call,
            ]);
        }
    }

    public function createOutboundLogs($leadId, $callId, $srno, $log) {
        if ($leadId > 0 && $callId > 0 && $callId != "null" && $callId != null) {
            $log = array(
                'lead_id' => $leadId,
                'lead_call_id' => $callId,
                'log_srno' => $srno,
                'log' => $log,
                'created_at' => date("Y-m-d H:i:s"),
            );
            LeadCallLog::create($log);
        } else {
            DB::table('dev_debug')->insert(['sr_no' => $srno, 'result' => $srno . " createOutboundLogs log leadId: " . $leadId . "==Call Id==" . $callId . "==Log==" . $log]);
        }
    }

    public function callTransfer(Request $request) {
        $parameters = $request->all();
        $status = "FAILURE";
        $message = $error = '';
        $lead_call_id = isset($parameters['id']) ? $parameters['id'] : 0;
        $lead_id = isset($parameters['lead_id']) ? $parameters['lead_id'] : 0;
        $user_id = isset($parameters['rep_id']) ? $parameters['rep_id'] : 0;
        $this->createOutboundLogs($lead_id, $lead_call_id, 6, "6 snapIt log getoutboundCallAnswer post data==" . json_encode($parameters) . "==user_id=" . $user_id);
        //echo "<pre>callTransfer==";print_r($parameters);die;
        try {
            //$dispositions = TokDispositions::wherenotNull('name')->pluck('id');
            if (!$parameters['action'] || !$parameters['id']) {
                throw new Exception("Parameters is not valid error.");
            }
            $status = "SUCCESS";
            if (trim($parameters['action']) == 1 || trim($parameters['action']) == "1") {
                $message = $this->LiveTransferSwitch($parameters);
                if (strtoupper($message) == "FAILURE") {
                    $status = "FAILURE";
                    $message = $error = "This buyer not available at this moment and no other buyer available";
                }
            } else {
                $message = $this->hangupDisposition($parameters);
                $this->tokHangupManually($parameters);
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
            $this->createOutboundLogs($lead_id, $lead_call_id, 7, "7 snapIt log callTransfer error catched==" . $error . "==user_id=" . $user_id);
        }
        $response = array('status' => $status, 'data' => $message, 'error' => $error);
        //echo "<pre>";print_r($response);die;
        $this->createOutboundLogs($lead_id, $lead_call_id, 8, "8 snapIt log callTransfer response==" . json_encode($response) . "==user_id=" . $user_id);
        return json_encode($response);
    }

    public function LiveTransferSwitch($parameters) {
        //echo "<pre>";print_r($parameters);die;
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        $type = isset($parameters['type']) ? $parameters['type'] : 0;
        $lead_call_id = isset($parameters['id']) ? $parameters['id'] : 0;
        $lead_id = isset($parameters['lead_id']) ? $parameters['lead_id'] : 0;
        $digit_pressed = isset($parameters['action']) ? $parameters['action'] : 0;
        $logicCheck = isset($parameters['logic_check']) ? $parameters['logic_check'] : 0;
        $reviewCall = isset($parameters['review_call']) ? $parameters['review_call'] : 0;
        $acceptCall = isset($parameters['accept_call']) ? $parameters['accept_call'] : 0;
        $repId = isset($parameters['rep_id']) ? $parameters['rep_id'] : 0;
        $categoryId = isset($parameters['categoryId']) ? $parameters['categoryId'] : 1;
        $usaBusinessCheck = isset($parameters['usaBusinessCheck']) ? $parameters['usaBusinessCheck'] : 0;
        $outbound_data = LeadCall::where('lead_call_id', $lead_call_id)->get()->toArray();
        $this->createOutboundLogs($lead_id, $lead_call_id, 8, "8 snapIt log LiveTransferSwitch post data==" . json_encode($parameters) . "==LeadCall Data=" . json_encode($outbound_data) . "==user_id=" . $repId);
        //echo "<pre>";print_r($outbound_data);die;
        if (count($outbound_data) > 0) {
            $callUuid = $outbound_data[0]['legA_call_uuid'];
            if ($outbound_data[0]['call_type'] == "inbound") {
                $acceptCall = 1;
            }
        } else {
            exit;
        }
        $callType = "outbound";
        if ($acceptCall > 0) {
            $callType = "inbound";
        }
        //echo "<pre>";print_r($outbound_data);die;
        $transferType = "forward";
        $freeLead = $campaignType = 1;
        $idFreeLead = "no";
        $transferBusinessId = 0;
        if ($logicCheck > 0) {
            $transferType = "transfer";
            $logic = new CallitSystemLogic(); //Required
            if($usaBusinessCheck == 1) {
                //$usaCampaignID = 741; // staging
                $usaCampaignID = 1214; // prod
                $checkCampaignUSA = Campaign::where('campaign_id', $usaCampaignID)->where('is_active', 'yes')->first();
                $checkCampaignPayoutUSA = CampaignCallPayout::where('campaign_id', $usaCampaignID)
                                ->first();
                $new_bus_fetch = [
                    'campaign_id' => $usaCampaignID, // 4517
                    'phonenumber' => $checkCampaignUSA->forward_number,
                    'payout' => $checkCampaignPayoutUSA->automated_outbound,
                    'latest_score' => 100
                ];
            } else {
                $new_bus_fetch = $logic->LiveLeadBusinessCriteriaCheck($lead_id, $lead_call_id, 1, $acceptCall);    
            }
            
            //echo "<pre>";print_r($new_bus_fetch);die;
            $this->createOutboundLogs($lead_id, $lead_call_id, 9, "9 snapIt log LiveLeadBusinessCriteriaCheck data==" . json_encode($new_bus_fetch) . "==user_id=" . $repId);
            if (!empty($new_bus_fetch)) {
                $campaign_id = $new_bus_fetch['campaign_id'];
                $new_phonenumber = $new_bus_fetch['phonenumber'];
                if ($new_bus_fetch['payout'] <= 0) {
                    $freeLead = 0;
                }
                $new_payout = $this->checkUpdateFreeLead($lead_id, $campaign_id, $new_bus_fetch['payout'], $repId, $lead_call_id);
                if ($new_payout <= 0 && $freeLead > 0) {
                    $idFreeLead = "yes";
                }
                $new_latest_score = $new_bus_fetch['latest_score'];
                if ($campaign_id <= 0 || $lead_id <= 0 || $lead_call_id <= 0) {
                    $error = "FAILURE";
                    $this->createOutboundLogs($lead_id, $lead_call_id, 10, "10 snapIt log error while transfer call data==" . $error . "==" . $lead_id . "==" . $campaign_id . "==" . $lead_call_id . "==user_id=" . $repId);
                    return $error;
                }
                $getCampaignData = Campaign::where('campaign_id', $campaign_id)->get()->toArray();
                if (count($getCampaignData) > 0) {
                    $transferBusinessId = $getCampaignData[0]['business_id'];
                }
                LeadCall::where('lead_call_id', $lead_call_id)->update(['campaign_id' => $campaign_id, 'business_id' => $transferBusinessId, 'transfer_number' => $new_phonenumber, 'payout' => $new_payout, 'campaign_score' => $new_latest_score, 'call_disposition_id' => $digit_pressed, 'transfer_type' => $transferType]);
                $outbound_data[0]['campaign_id'] = $campaign_id;
                $outbound_data[0]['transfer_number'] = $new_phonenumber;
                $outbound_data[0]['payout'] = $new_payout;
                $outbound_data[0]['campaign_score'] = $new_latest_score;
                $outbound_data[0]['call_disposition_id'] = $digit_pressed;
                $outbound_data[0]['transfer_type'] = $transferType;
            } else {
                $error = "FAILURE";
                $this->createOutboundLogs($lead_id, $lead_call_id, 11, "10 snapIt log error while transfer call data==" . $error . "==" . $lead_id . "==" . $lead_call_id . "==user_id=" . $repId);
                return $error;
            }
        } else { //Added BY HJ On 15-06-2023 For Forward Call Data
            $outbound_data[0]['campaign_id'] = isset($parameters['campaignId']) ? $parameters['campaignId'] : 0;
            $outbound_data[0]['campaign_score'] = isset($parameters['campaignScore']) ? $parameters['campaignScore'] : 0;
            $outbound_data[0]['transfer_number'] = isset($parameters['campaignNumber']) ? $parameters['campaignNumber'] : 0;
            $outbound_data[0]['payout'] = isset($parameters['campaignPayout']) ? $parameters['campaignPayout'] : 0;
            if ($outbound_data[0]['campaign_id'] <= 0 || $lead_id <= 0 || $lead_call_id <= 0) {
                $error = "FAILURE";
                $this->createOutboundLogs($lead_id, $lead_call_id, 11, "11 snapIt log error while transfer call data==" . $error . "==" . $lead_id . "==" . $outbound_data[0]['campaign_id'] . "==" . $lead_call_id . "==user_id=" . $repId);
                return $error;
            }
            //Added BY HJ On 17-07-2023 For Checked Dialer Campaign Routed Or Not Start
            $lead_payout = LeadRouting::where('route_status', 'sold')->where('lead_id', $lead_id)->where('campaign_id', $outbound_data[0]['campaign_id'])->get()->sum("payout");
            if ($lead_payout > 0) {
                $outbound_data[0]['payout'] = $new_payout = 0;
            }
            if ($outbound_data[0]['payout'] <= 0) {
                $freeLead = 0;
            }
            //Added BY HJ On 17-07-2023 For Checked Dialer Campaign Routed Or Not End
            $new_payout = $this->checkUpdateFreeLead($lead_id, $outbound_data[0]['campaign_id'], $outbound_data[0]['payout'], $repId, $lead_call_id);
            $outbound_data[0]['payout'] = $new_payout;
            if ($new_payout <= 0 && $freeLead > 0) {
                $idFreeLead = "yes";
            }
            $getCampaignData = Campaign::where('campaign_id', $outbound_data[0]['campaign_id'])->get()->toArray();
            if (count($getCampaignData) > 0) {
                $transferBusinessId = $getCampaignData[0]['business_id'];
            }
            LeadCall::where('lead_call_id', $lead_call_id)->update(['campaign_id' => $outbound_data[0]['campaign_id'], 'business_id' => $transferBusinessId, 'transfer_number' => $outbound_data[0]['transfer_number'], 'payout' => $new_payout, 'campaign_score' => $outbound_data[0]['campaign_score'], 'call_disposition_id' => $digit_pressed, 'transfer_type' => $transferType]);
        }
        $transfer_number = $outbound_data[0]['transfer_number'];
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $plivoBaseURL = 'https://api.plivo.com/v1/Account/' . $decVariable1;
        $urltoaleg = $plivoBaseURL . '/Call/' . $callUuid;
        if ($acceptCall > 0) {
            $datatran = array('legs' => 'aleg', 'aleg_url' => $matrixBaseURL_Outbound . '/tokoutboundtocampaign?lead_id=' . $lead_id . '&phone=' . $transfer_number . '&accept_call=1&user_id=' . $repId . '&lead_call_id=' . $lead_call_id . '&transfer=1&call_uuid=' . $outbound_data[0]['legA_call_uuid']); //For Inbound Transfer
        } else {
            $datatran = array('legs' => 'bleg', 'bleg_url' => $matrixBaseURL_Outbound . '/tokoutboundtocampaign?lead_id=' . $lead_id . '&phone=' . $transfer_number . '&accept_call=0&user_id=' . $repId . '&lead_call_id=' . $lead_call_id . '&transfer=0');
        }

        if ($acceptCall > 0) { 
            $fromNumber = "1".$outbound_data[0]['from_number'];
        } else {
            $fromNumber = "***********";
        }

        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $plivoBaseURL = 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/Call/';
        $data = array(
            'from' => $fromNumber,
            'to' => "1" . $transfer_number,
            'answer_url' => $matrixBaseURL_Outbound . '/conferencecall?lead_call_id=' . $lead_call_id.'&mover=1',
            'hangup_url' => ''
        );
        $data = json_encode($data);
        $aleg_response = $this->Calltocurl($plivoBaseURL, $data);


        //echo "<pre>";print_r($datatran);die;
        //$datatran = $dataEncode = json_encode($datatran);
        //$this->createOutboundLogs($lead_id, $lead_call_id, 10, "10 snapIt log plivo request==" . $dataEncode . "==Url=" . $urltoaleg . "==user_id=" . $repId);
        //$aleg_response = $this->Calltocurl($urltoaleg, $datatran);
        //echo "<pre>";print_r($aleg_response);die;
        $this->createOutboundLogs($lead_id, $lead_call_id, 11, "11 snapIt log plivo response==" . $aleg_response );
        $aleg_response = json_decode($aleg_response);
        $transferCallUuid = $callUuid;
        $this->createOutboundLogs($lead_id, $lead_call_id, 11, "111 snapIt log plivo response==" . $aleg_response->request_uuid );
        if (trim($callUuid) != "") {
            $this->createOutboundLogs($lead_id, $lead_call_id, 11, "112 snapIt log plivo response==" . $aleg_response->request_uuid );
            if ($acceptCall > 0) {
                // For Conference call
                $transferCallUuid = $aleg_response->request_uuid;
                $this->CallRecord($transferCallUuid);
            } else if (isset($aleg_response->request_uuid)) {
                $transferCallUuid = $aleg_response->request_uuid;
                $this->CallRecord($transferCallUuid);
            }
            if ($outbound_data[0]['campaign_id'] > 0 && $lead_id > 0 && $lead_call_id > 0) {
                $call_track = ['lead_id' => $lead_id, 'campaign_id' => $outbound_data[0]['campaign_id'], 'lead_call_id' => $lead_call_id, 'created_at' => date('Y-m-d H:i:s')];
                CampaignOngoingCall::create($call_track);
                $this->createOutboundLogs($lead_id, $lead_call_id, 12, "12 snapIt log Call track entry." . json_encode($call_track) . "==user_id=" . $repId);
            }
        }
        $live_transfer = array(
            'lead_id' => $lead_id,
            'campaign_id' => $outbound_data[0]['campaign_id'],
            'lead_call_id' => $lead_call_id,
            'calls_type' => $callType,
            'call_uuid' => $transferCallUuid,
            'duration' => '',
            'recording_url' => '',
            'payout' => $outbound_data[0]['payout'],
            'is_checked_rec' => "no",
            'transfer_type' => $transferType,
            'transfer_by' => $repId,
            'lead_category_id' => $categoryId,
            'created_at' => date("Y-m-d H:i:s")
        );
        LeadTransferCall::create($live_transfer);
        RepActiveCall::where('call_uuid', $callUuid)->delete();
        $this->tokLoginLog("no", $repId);
        $leadactiviyobj = new LeadActivity();
        $leadactiviyobj->createActivity($lead_id, 6);
        $this->createOutboundLogs($lead_id, $lead_call_id, 13, "13 snapIt log Live transfer entry." . json_encode($live_transfer) . "==user_id=" . $repId);
        /* Call forward to business comaigpn if press 1. */
        $Lead_Routedata = [
            'lead_id' => $lead_id,
            'campaign_id' => $outbound_data[0]['campaign_id'],
            'payout' => $outbound_data[0]['payout'],
            'score' => $outbound_data[0]['campaign_score'],
            'route_status' => "sold",
            'is_free_lead' => $idFreeLead,
            'created_at' => date("Y-m-d H:i:s")
        ];
        //echo "<pre>";print_r($Lead_Routedata);die;
        $lead_routing_id = LeadRouting::create($Lead_Routedata)->lead_routing_id;
        CommonFunctions::insertLeadCallRoutingData($lead_id, $lead_call_id, $lead_routing_id); //Added By HJ On 25-07-2023 For Insert Data Into New Table lead_call_routing
        $this->createOutboundLogs($lead_id, $lead_call_id, 14, "14 snapIt log Lead Route entry :" . json_encode($Lead_Routedata) . "==user_id=" . $repId);
        $checkAlready = Lead::where('lead_id', $lead_id)->get(['book_type_id', 'is_marketplace', 'sold_type'])->toArray();
        $leadBook = 0;
        $is_marketplace = "no";
        $sold_type = "none";
        if (count($checkAlready) > 0) {
            $leadBook = $checkAlready[0]['book_type_id'];
            $is_marketplace = $checkAlready[0]['is_marketplace'];
            $sold_type = $checkAlready[0]['sold_type'];
        }
        /* Cut Funds from the campaign */
        $getRoutingData = LeadRouting::select(DB::raw('count(*) as totalLead'), DB::raw('SUM(payout) as totalPayout'))->where('route_status', 'sold')->where('lead_id', $lead_id)->get()->toArray();
        //echo "<pre>";print_r($getRoutingData);die;
        if (count($getRoutingData) > 0) {
            $totalPayout = (isset($getRoutingData[0]['totalPayout'])) ? $getRoutingData[0]['totalPayout'] : 0;
            //Added By HJ On 18-07-2023 For Insert Sold type as per transfer campaign Start
            $soldType = "premium";
            if (count($getCampaignData) > 0) {
                $campaign_type_id = $getCampaignData[0]['campaign_type_id'];
                $getCampaignTypeData = MstCampaignType::where('campaign_type_id', $campaign_type_id)->get()->toArray();
                if (count($getCampaignTypeData) > 0) {
                    $soldType = strtolower($getCampaignTypeData[0]['campaign_type']);
                    if (strpos($soldType, 'dual') !== false) {
                        $soldType = "dual";
                    } else if (strpos($soldType, 'organic') !== false) {
                        $soldType = "organic";
                    }
                }
            }
            $updatePayoutArr = array();
            if (strtolower($sold_type) == "none" && $logicCheck > 0) {
                $updatePayoutArr['sold_type'] = $soldType;
            }
            //Added By HJ On 18-07-2023 For Insert Sold type as per transfer campaign End
            $updatePayoutArr['payout'] = $totalPayout;
            Lead::where('lead_id', $lead_id)->update($updatePayoutArr);
        }
        $business_name = "";
        if (count($getCampaignData) > 0) {
            $getBusinessData = Business::where('business_id', $getCampaignData[0]['business_id'])->get()->toArray();
            if (count($getBusinessData) > 0) {
                $business_name = $getBusinessData[0]['business_name'];
            }
            $campaignType = $getCampaignData[0]['campaign_type_id'];
        }
        //$checkAlready = array();
        //check campaign type for insert records in lead_marketplace table by BK 17/05/2023 Start
        if (count($getRoutingData) > 0) {
            //lead already sold to exclusive and organic not inserted in marketplace during live transfer
            if ($campaignType != 3) {
                if ($campaignType != 2 && $campaignType != 4) {
                    //$checkAlready = Lead::where('lead_id', $lead_id)->where('is_marketplace', 'yes')->get()->toArray();
                    $remainSlot = $remaingSlot = 4 - $getRoutingData[0]['totalLead'];
                    if (isset($getCampaignData[0]['campaign_type_id']) && $getCampaignData[0]['campaign_type_id'] == 6) {
                        if ($remainSlot <= 3) {
                            $remaingSlot = 0;
                        }
                    }
                    if (isset($getCampaignData[0]['campaign_type_id']) && $getCampaignData[0]['campaign_type_id'] == 8) {
                        if ($remainSlot <= 2) {
                            $remaingSlot = 0;
                        }
                    }
                    if ($is_marketplace == "yes") {
                        if (isset($getCampaignData[0]['campaign_type_id']) && $getCampaignData[0]['campaign_type_id'] == 2) {
                            Lead::where('lead_id', $lead_id)->update(['remaining_slot' => 0, 'is_marketplace' => 'no']);
                        } else {
                            Lead::where('lead_id', $lead_id)->update(['remaining_slot' => $remaingSlot, 'is_marketplace' => 'no']);
                        }
                    } else {
                        $soldCampaign = LeadRouting::where('route_status', 'sold')->where('lead_id', $lead_id)->pluck('campaign_id');

                        $checkSlot = 0;
                        $checkEx = Campaign::whereIn('campaign_id', $soldCampaign)->where('campaign_type_id',2)->first();
                        $checkPrimumCount = Campaign::whereIn('campaign_id', $soldCampaign)->where('campaign_type_id',1)->get()->count();
                        $checkDualCount = Campaign::whereIn('campaign_id', $soldCampaign)->where('campaign_type_id',6)->get()->count();
                        $checkTrioCount = Campaign::whereIn('campaign_id', $soldCampaign)->where('campaign_type_id',8)->get()->count();
                        $checkOrganicCount = Campaign::whereIn('campaign_id', $soldCampaign)->where('campaign_type_id',4)->get()->count();

                        if($checkEx) {
                            $checkSlot = 1;
                        }
                        if($checkOrganicCount >= 1) {
                            $checkSlot = 1;
                        }
                        if($checkDualCount == 2) {
                            $checkSlot = 1;
                        }
                        if($checkPrimumCount == 4) {
                            $checkSlot = 1;
                        }
                        if(($checkDualCount+$checkPrimumCount) >= 2){
                            $checkSlot = 1;
                        }
                        if($checkTrioCount == 3) {
                            $checkSlot = 1;
                        }
                        if(($checkDualCount+$checkTrioCount) >= 2){
                            $checkSlot = 1;
                        }
                        if(($checkPrimumCount+$checkTrioCount) >= 3){
                            $checkSlot = 1;
                        }

                        if($checkSlot == 1) {
                            Lead::where('lead_id', $lead_id)->update([
                                'remaining_slot' => 0,
                                'is_marketplace' => 'no'
                            ]);
                        } else {
                            Lead::where('lead_id', $lead_id)->update([
                                'remaining_slot' => $remainSlot,
                                'is_marketplace' => 'yes'
                            ]);
                        }

                        
                    }
                } else {
                    Lead::where('lead_id', $lead_id)->update(['remaining_slot' => 0, 'is_marketplace' => 'no']);
                }
            }
        }
        //check campaign type for insert records in lead_marketplace table by BK 17/05/2023 End
        //echo "<pre>";print_r($getCampaignData);die;
        $balance = 0;
        $balance_after_charge = $getCampaignData[0]['credit_available'];
        if ($getCampaignData[0]['payment_type'] == 0 || $getCampaignData[0]['payment_type'] == '') {
            $balance_after_charge = $getBusinessData[0]['credit_available'];
        }
        if ($outbound_data[0]['payout'] > 0 && count($getCampaignData) > 0) {
            if ($getCampaignData[0]['payment_type'] == 0 || $getCampaignData[0]['payment_type'] == '') {
                if (count($getBusinessData) > 0) {
                    //echo "<pre>";print_r($getBusinessData);die;
                    $this->createOutboundLogs($lead_id, $lead_call_id, 15, "15 snapIt log Businesses Before credit_available: $ " . $getBusinessData[0]['credit_available'] . "==user_id=" . $repId);
                    $balance = $getBusinessData[0]['credit_available'];
                    $update_credit_bus_available = $balance_after_charge = ($getBusinessData[0]['credit_available'] - $outbound_data[0]['payout']);
                    //Business::where('business_id', $getCampaignData[0]['business_id'])->update(['credit_available' => $update_credit_bus_available]);
                    $deductPayout = $outbound_data[0]['payout'];
                    DB::select("UPDATE `business` set `credit_available`=(`credit_available`-$deductPayout) where `business_id`='" . $getCampaignData[0]['business_id'] . "'");
                    $this->createOutboundLogs($lead_id, $lead_call_id, 16, "16 snapIt log Businesses After credit_available: $ " . ($update_credit_bus_available) . "==user_id=" . $repId);
                }
            } else {
                $this->createOutboundLogs($lead_id, $lead_call_id, 17, "17 snapIt log Campaign Before credit_available: $ " . ($getCampaignData[0]['credit_available']) . "==user_id=" . $repId);
                $balance = $getCampaignData[0]['credit_available'];
                $update_credit_available = $balance_after_charge = ($getCampaignData[0]['credit_available'] - $outbound_data[0]['payout']);
                //Campaign::where('campaign_id', $getCampaignData[0]['campaign_id'])->update(['credit_available' => $update_credit_available]);
                $deductPayout = $outbound_data[0]['payout'];
                DB::select("UPDATE `campaign` set `credit_available`=(`credit_available`-$deductPayout) where `campaign_id`='" . $getCampaignData[0]['campaign_id'] . "'");
                $this->createOutboundLogs($lead_id, $lead_call_id, 18, "18 snapIt log Campaign After credit_available: $ " . ($update_credit_available) . "==user_id=" . $repId);
            }
        } else {
            $this->createOutboundLogs($lead_id, $lead_call_id, 19, "19 snapIt log tweet lead Free call applied: camp Id " . $outbound_data[0]['campaign_id'] . ", payout :" . $outbound_data[0]['payout'] . "==user_id=" . $repId);
        }
        LeadRouting::where('lead_routing_id', $lead_routing_id)->update(['balance_after_charge' => $balance_after_charge, 'payment_type' => $getCampaignData[0]['payment_type']]); //Added By HJ On 28-07-2023 For Insert Business Or Campaign Credit and Payment Type After Call Transfer
        //Start For Lead Delivery API Comment B'coz Pending this
        /* $lead_deliveryCtl = new OutboundCallLeadDeliveryController();
          $outbound_data[0]['accept_call'] = $acceptCall;
          $outbound_data[0]['tok_payout'] = 1;
          $lead_deliveryCtl->OutboundLeadDelivery($outbound_data); */
        //Start For Lead Delivery API Comment B'coz Pending this
        $leadDeliverApi = new LeadDeliveryApi();
        $jsonArray = array(
            "campaign_id" => $outbound_data[0]['campaign_id'],
            "lead_id" => $lead_id,
            "cost" => $outbound_data[0]['payout'],
            "balance" => $balance,
            "score" => $outbound_data[0]['campaign_score']
        );
        $this->createOutboundLogs($lead_id, $lead_call_id, 20, "20 snapIt log LiveTransferSwitch leadDeliveryTemplate request data " . json_encode($jsonArray) . "==user_id=" . $repId);
        $leadDeliverApi->leadDeliveryTemplate(json_encode($jsonArray));
        if ($reviewCall > 0) {
            LeadOldCallBuffer::where('lead_id', $outbound_data[0]['lead_id'])->where('lead_category_id', $categoryId)->delete();
        } else {
            $getLeadCallBuffer = LeadCallBuffer::where('lead_id', $outbound_data[0]['lead_id'])->where('lead_category_id', $categoryId)->first();
            if (isset($getLeadCallBuffer)) {
                $leadactiviyobj->createActivity($outbound_data[0]['lead_id'], 8);
            }
            LeadCallBuffer::where('lead_id', $outbound_data[0]['lead_id'])->where('lead_category_id', $categoryId)->delete();
            /* if (count($checkAlready) == 0) {
              $checkAlready = Lead::where('lead_id', $lead_id)->get()->toArray();
              }
              if (count($checkAlready) > 0) {
              $leadBook = $checkAlready[0]['book_type_id'];
              } */
            if ($leadBook > 1) { //If booked
                $getLeadFollowUp = LeadFollowUp::where('lead_id', $lead_id)->first();
                if (isset($getLeadFollowUp)) {
                    $leadactiviyobj->createActivity($lead_id, 13);
                }
                LeadFollowUp::where('lead_id', $lead_id)->delete(); // No entry into follow-up.
            } else {
                $buffercontroller = new LeadCallBufferController();
                $date = date('Y-m-d H:i:s');
                if (date('G') >= 18) {
                    $date = date('Y-m-d H:i:s');
                    $follow_up_date = date('Y-m-d', strtotime('+1 day', strtotime($date)));
                    $follow_up_time = '10:00:00';
                    $follow_up_date_time = $follow_up_date . " " . $follow_up_time;
                } else {
                    $date = date('Y-m-d H:i:s');
                    $follow_up_date_time = date('Y-m-d H:i:s', strtotime('+2 hours', strtotime($date)));
                }
                $buffercontroller->saveFollowupentryWithDispositions($outbound_data[0]['lead_id'], $follow_up_date_time, 0);
            }
        }
        $message = 'Call ' . ucfirst($transferType) . ' to ' . $business_name . ' Successfully.';
        if ($business_name == "") {
            $message = 'Call ' . ucfirst($transferType) . ' Successfully.';
        }
        return $message;
    }

    public function hangupDisposition($parameters) {
        $leadactiviyobj = new LeadActivity();
        $message = 'No any disposition selected by you.';
        $lead_call_id = isset($parameters['id']) ? $parameters['id'] : 0;
        $lead_id = isset($parameters['lead_id']) ? $parameters['lead_id'] : 0;
        $repId = isset($parameters['rep_id']) ? $parameters['rep_id'] : 0;
        $buffer_id_arr = isset($parameters['buffer_id_arr']) ? $parameters['buffer_id_arr'] : array();
        if ($buffer_id_arr == null || $buffer_id_arr == Null) {
            $buffer_id_arr = array();
        }
        $this->createOutboundLogs($lead_id, $lead_call_id, 20, "20 snapIt log hangupDisposition post data : " . json_encode($parameters) . '===user_id-' . $repId);
        try {
            //echo "<pre>";print_r($parameters);die;
            $dispositionId = isset($parameters['action']) ? $parameters['action'] : 0;
            $reviewCall = isset($parameters['review_call']) ? $parameters['review_call'] : 0;
            $acceptCall = isset($parameters['accept_call']) ? $parameters['accept_call'] : 0;
            $lead_category = isset($parameters['lead_category']) ? $parameters['lead_category'] : 1;
            $follow_up_date_time = $parameters['follow_up_date_time'];
            $timezone_id = isset($parameters['timezone_id']) ? $parameters['timezone_id'] : 0;
            //$follow_up_time = $parameters['follow_up_time'];
            //$follow_up_date_time = $follow_up_date . " " . $follow_up_time;
            date_default_timezone_set('US/Eastern');
            $date = date('Y-m-d H:i:s');
            $hours = 0;
            $leadIdEmailArr = [];
            $leadListControllerobj = new LeadListController();
            if ($lead_call_id > 0 && $dispositionId > 0 && $lead_id > 0) {
                $buffercontroller = new LeadCallBufferController();
                $outbound_data = LeadCall::where('lead_call_id', $lead_call_id)->get()->toArray();
                //echo "<pre>";print_r($outbound_data);die;
                $this->createOutboundLogs($lead_id, $lead_call_id, 21, "21 snapIt log LeadCall table data : " . json_encode($outbound_data) . '===user_id-' . $repId);
                if (count($outbound_data) > 0) {
                    $lead_id = $outbound_data[0]['lead_id'];
                    $leadData = Lead::where('lead_id', $lead_id)->get()->toArray();
                    $book_type_id = 1; //None
                    $phone = $lead_source_id = "";
                    if (count($leadData) > 0) {
                        $book_type_id = $leadData[0]['book_type_id'];
                        $phone = $leadData[0]['phone'];
                        $lead_source_id = $leadData[0]['lead_source_id'];
                        $lead_category = $leadData[0]['lead_category_id'];
                    }
                    $getBufferData = array();
                    if (count($buffer_id_arr) > 0) {
                        //$getBufferData = LeadCallBuffer::where('customer_number', $phone)->where('lead_category_id', '!=', $lead_category)->get()->toArray(); // Associated Lead (Call buffer associated leads for that number with other category)
                        $getBufferData = LeadCallBuffer::whereIn('lead_call_buffer_id', $buffer_id_arr)->get()->toArray(); // Associated Lead (Call buffer associated leads for that number with other category)
                    }
                    if ($dispositionId == 2) { // DNC
                        $start_date = date('Y-m-d', strtotime("-90 days")) . ' 00:00:00';
                        $end_date = date('Y-m-d 23:59:59');
                        $getLeadFollowUp = LeadFollowUp::where('lead_id', $lead_id)->first();
                        if (isset($getLeadFollowUp)) {
                            $leadactiviyobj->createActivity($lead_id, 13);
                        }
                        LeadFollowUp::where('lead_id', $lead_id)->delete(); // No entry into follow-up.
                        Lead::where('lead_id', $lead_id)->update(['is_dnc_call' => 'yes']); //Mark as DNC
                        // $leadListControllerobj->dncemailsend($lead_id, "yes", "yes", "yes");
                        $leadIdEmailArr[] = $lead_id;
                        $this->createOutboundLogs($leadData[0]['lead_id'], $lead_call_id, 22, "22 snapIt log hangupDisposition DNC : " . $start_date . "==" . $end_date . $reviewCall . '===user_id-' . $repId);
                        for ($fd = 0; $fd < count($getBufferData); $fd++) {
                            Lead::where('lead_id', $getBufferData[$fd]['lead_id'])->update(['is_dnc_call' => 'yes']); //Mark this lead as DNC.
                            $leadIdEmailArr[] = $getBufferData[$fd]['lead_id'];
                            // $leadListControllerobj->dncemailsend($getBufferData[$fd]['lead_id'], "yes", "yes", "yes");
                        }
                        Lead::where('phone', $phone)->where('book_type_id', '>', 1)->whereBetween('lead_generated_at', [$start_date, $end_date])->update(['is_dnc_call' => 'yes']); // If booked No entry into follow-up. Mark as DNC all leads with that phone number.
                        $getleads = Lead::where('phone', $phone)->where('book_type_id', '>', 1)->whereBetween('lead_generated_at', [$start_date, $end_date])->get()->toArray();
                        for ($d = 0; $d < count($getleads); $d++) {
                            $leadIdEmailArr[] = $getleads[$d]['lead_id'];
                            // $leadListControllerobj->dncemailsend($getleads[$d]['lead_id'], "yes", "yes", "yes");
                        }
                        Lead::where('phone', $phone)->where('book_type_id', 1)->where('lead_category_id', $lead_category)->whereBetween('lead_generated_at', [$start_date, $end_date])->update(['is_dnc_call' => 'yes']); // Not booked No entry into follow-up. Mark as DNC all leads across the category with that phone number.
                        $getleadsbooked = Lead::where('phone', $phone)->where('book_type_id', 1)->where('lead_category_id', $lead_category)->whereBetween('lead_generated_at', [$start_date, $end_date])->get()->toArray();
                        for ($e = 0; $e < count($getleadsbooked); $e++) {
                            $leadIdEmailArr[] = $getleadsbooked[$e]['lead_id'];
                            $leadactiviyobj->createActivity($getleadsbooked[$e]['lead_id'], 24);
                            //$leadactiviyobj->createActivity($getleadsbooked[$e]['lead_id'], 25);
                            //$leadListControllerobj->dncemailsend($getleadsbooked[$e]['lead_id'], "yes", "yes", "yes");
                        }
                    }

                    if ($dispositionId == 3) { //Not Interested
                        $getLeadFollowUp = LeadFollowUp::where('lead_id', $lead_id)->first();
                        if (isset($getLeadFollowUp)) {
                            $leadactiviyobj->createActivity($lead_id, 13);
                        }
                        LeadFollowUp::where('lead_id', $lead_id)->delete(); // No entry into follow-up.
                        Lead::where('lead_id', $lead_id)->update(['is_dnc_call' => 'yes']); //Mark this lead as DNC.
                        // $leadListControllerobj->dncemailsend($lead_id, "yes", "yes", "yes");
                        $leadIdEmailArr[] = $lead_id;
                        $leadactiviyobj->createActivity($lead_id, 24);
                        //$leadactiviyobj->createActivity($lead_id, 25);
                        for ($f = 0; $f < count($getBufferData); $f++) {
                            $leadData = Lead::where('lead_id', $getBufferData[$f]['lead_id'])->get()->toArray();
                            if (count($leadData) > 0) {
                                $leadBook = $leadData[0]['book_type_id'];
                                $this->createOutboundLogs($leadData[0]['lead_id'], $lead_call_id, 22, "22 snapIt log hangupDisposition saveFollowupentryWithDispositions Not Interested : " . $follow_up_date_time . "==" . $hours . "==" . $leadBook . "==" . $reviewCall . '===user_id-' . $repId);
                                if ($leadBook > 1) { //If booked
                                    $getLeadFollowUp = LeadFollowUp::where('lead_id', $leadData[0]['lead_id'])->first();
                                    if (isset($getLeadFollowUp)) {
                                        $leadactiviyobj->createActivity($leadData[0]['lead_id'], 13);
                                    }
                                    LeadFollowUp::where('lead_id', $leadData[0]['lead_id'])->delete(); // No entry into follow-up.
                                } else { //Not booked Entry in follow up table next day 8 AM Local Time
                                    $follow_up_date = date('Y-m-d', strtotime('+1 day', strtotime($date)));
                                    $follow_up_date_time = $follow_up_date . ' 08:00:00';
                                    $buffercontroller->saveFollowupentryWithDispositions($leadData[0]['lead_id'], $follow_up_date_time, $hours);
                                }
                                if ($reviewCall > 0) {
                                    LeadOldCallBuffer::where('lead_id', $leadData[0]['lead_id'])->delete();
                                } else {
                                    $getLeadCallBuffer = LeadCallBuffer::where('lead_id', $leadData[0]['lead_id'])->first();
                                    if (isset($getLeadCallBuffer)) {
                                        $leadactiviyobj->createActivity($leadData[0]['lead_id'], 8);
                                    }
                                    LeadCallBuffer::where('lead_id', $leadData[0]['lead_id'])->delete();
                                }
                            }
                        }
                    }

                    if ($dispositionId == 4) { //Follow Up
                        //Added By HJ On 13-07-2023 For Convert Date time as per selected timezone Start
                        if ($timezone_id > 0) {
                            $orgDateTimeSele = $follow_up_date_time;
                            $getTimeZone = MstTimeZone::where('timezone_id', $timezone_id)->get()->toArray();
                            if (count($getTimeZone) > 0) {
                                $timeZoneName = $getTimeZone[0]['timezone'];
                                date_default_timezone_set($timeZoneName);
                                //echo "Chicago==".$follow_up_date_time."<br>";
                                $timeZoneCust = new DateTime($follow_up_date_time);
                                $estTimezone = new DateTimeZone('America/New_York');
                                $timeZoneCust->setTimeZone($estTimezone);
                                $follow_up_date_time = $timeZoneCust->format('Y-m-d H:i:s');
                            }
                            $this->createOutboundLogs($lead_id, $lead_call_id, 21, "21 snapIt log hangupDisposition saveFollowupentryWithDispositions Follow Up : " . $orgDateTimeSele . "==" . $follow_up_date_time . "==" . $timeZoneName . "==" . $timezone_id . '===user_id-' . $repId);
                        }
                        //Added By HJ On 13-07-2023 For Convert Date time as per selected timezone End
                        $buffercontroller->saveFollowupentryWithDispositions($lead_id, $follow_up_date_time, $hours);
                        for ($f = 0; $f < count($getBufferData); $f++) {
                            $leadData = Lead::where('lead_id', $getBufferData[$f]['lead_id'])->get()->toArray();
                            if (count($leadData) > 0) {
                                $leadBook = $leadData[0]['book_type_id'];
                                $this->createOutboundLogs($leadData[0]['lead_id'], $lead_call_id, 22, "22 snapIt log hangupDisposition saveFollowupentryWithDispositions Follow Up : " . $follow_up_date_time . "==" . $hours . "==" . $leadBook . "==" . $reviewCall . '===user_id-' . $repId);
                                if ($leadBook > 1) { //If booked
                                    $getLeadFollowUp = LeadFollowUp::where('lead_id', $leadData[0]['lead_id'])->first();
                                    if (isset($getLeadFollowUp)) {
                                        $leadactiviyobj->createActivity($leadData[0]['lead_id'], 13);
                                    }
                                    LeadFollowUp::where('lead_id', $leadData[0]['lead_id'])->delete(); // No entry into follow-up.
                                } else {
                                    $buffercontroller->saveFollowupentryWithDispositions($leadData[0]['lead_id'], $follow_up_date_time, $hours);
                                }
                                if ($reviewCall > 0) {
                                    LeadOldCallBuffer::where('lead_id', $leadData[0]['lead_id'])->delete();
                                } else {
                                    $getLeadCallBuffer = LeadCallBuffer::where('lead_id', $leadData[0]['lead_id'])->first();
                                    if (isset($getLeadCallBuffer)) {
                                        $leadactiviyobj->createActivity($leadData[0]['lead_id'], 8);
                                    }
                                    LeadCallBuffer::where('lead_id', $leadData[0]['lead_id'])->delete();
                                }
                            }
                        }
                    }

                    if ($dispositionId == 5) { //No Buyer
                        $fail_attempt = LeadCall::where('lead_id', $lead_id)->whereIn('call_disposition_id', [9, 5, 6])->count();
                        if ($fail_attempt > 3) {
                            $getLeadFollowUp = LeadFollowUp::where('lead_id', $lead_id)->first();
                            if (isset($getLeadFollowUp)) {
                                $leadactiviyobj->createActivity($lead_id, 13);
                            }
                            LeadFollowUp::where('lead_id', $lead_id)->delete(); // Do not enter in follow-up.
                        } else {
                            $this->storeLeadFollowupByDistance($lead_id, $date, $lead_category); // Same lead
                        }
                        for ($f = 0; $f < count($getBufferData); $f++) {
                            $leadData = Lead::where('lead_id', $getBufferData[$f]['lead_id'])->get()->toArray();
                            if (count($leadData) > 0) {
                                $leadBook = $leadData[0]['book_type_id'];
                                $this->createOutboundLogs($leadData[0]['lead_id'], $lead_call_id, 22, "22 snapIt log hangupDisposition saveFollowupentryWithDispositions No Buyer : " . $date . "==" . $leadBook . "==" . $reviewCall . '===user_id-' . $repId);
                                if ($leadBook > 1) { //If booked
                                    $getLeadFollowUp = LeadFollowUp::where('lead_id', $leadData[0]['lead_id'])->first();
                                    if (isset($getLeadFollowUp)) {
                                        $leadactiviyobj->createActivity($leadData[0]['lead_id'], 13);
                                    }
                                    LeadFollowUp::where('lead_id', $leadData[0]['lead_id'])->delete(); // No entry into follow-up.
                                } else { //Not booked
                                    $this->storeLeadFollowupByDistance($leadData[0]['lead_id'], $date, $lead_category); //Associated Lead
                                }
                                if ($reviewCall > 0) {
                                    LeadOldCallBuffer::where('lead_id', $leadData[0]['lead_id'])->delete();
                                } else {
                                    $getLeadCallBuffer = LeadCallBuffer::where('lead_id', $leadData[0]['lead_id'])->first();
                                    if (isset($getLeadCallBuffer)) {
                                        $leadactiviyobj->createActivity($leadData[0]['lead_id'], 8);
                                    }
                                    LeadCallBuffer::where('lead_id', $leadData[0]['lead_id'])->delete();
                                }
                            }
                        }
                    }

                    if ($dispositionId == 6 || $dispositionId == 14) { //Voice Message/ not answered: And IB Pickup
                        $fail_attempt = 0;
                        if ($reviewCall > 0) {
                            //$getFailAttempData = LeadOldCallBuffer::where('lead_id', $lead_id)->get()->toArray();
                            $getFailAttempData = array(); // Need Discussion For Old Lead
                        } else {
                            //$getFailAttempData = LeadCallBuffer::where('lead_id', $lead_id)->get()->toArray(); // Same lead
                            $fail_attempt = LeadCall::where('lead_id', $lead_id)->whereIn('call_disposition_id', [9, 5, 6])->count();
                        }
                        if (date('G') >= 18) {
                            $date = date('Y-m-d H:i:s');
                            $follow_up_date = date('Y-m-d', strtotime('+1 day', strtotime($date)));
                            $follow_up_time = '10:00:00';
                            $follow_up_date_time = $follow_up_date . " " . $follow_up_time;
                        } else {
                            $date = date('Y-m-d H:i:s');
                            $follow_up_date_time = date('Y-m-d H:i:s', strtotime('+2 hours', strtotime($date)));
                        }
                        $this->createOutboundLogs($lead_id, $lead_call_id, 22, "22 snapIt log hangupDisposition saveFollowupentryWithDispositions Voice Message : " . $follow_up_date_time . "==" . $reviewCall . "==" . $fail_attempt . '===user_id-' . $repId);
                        if ($fail_attempt > 3) { //If voice message disposition fail attempt > 4
                            $getLeadFollowUp = LeadFollowUp::where('lead_id', $lead_id)->first();
                            if (isset($getLeadFollowUp)) {
                                $leadactiviyobj->createActivity($lead_id, 13);
                            }
                            LeadFollowUp::where('lead_id', $lead_id)->delete(); // Do not enter in follow-up.
                        } else { //If voice message disposition fail attempt < 4
                            $buffercontroller->saveFollowupentryWithDispositions($lead_id, $follow_up_date_time, $hours); //Entry into follow-up with 2 hours
                        }
                        for ($f = 0; $f < count($getBufferData); $f++) {
                            $leadData = Lead::where('lead_id', $getBufferData[$f]['lead_id'])->get()->toArray();
                            if (count($leadData) > 0) {
                                $leadBook = $leadData[0]['book_type_id'];
                                if ($leadBook > 1) { //If booked
                                    $getLeadFollowUp = LeadFollowUp::where('lead_id', $leadData[0]['lead_id'])->first();
                                    if (isset($getLeadFollowUp)) {
                                        $leadactiviyobj->createActivity($leadData[0]['lead_id'], 13);
                                    }
                                    LeadFollowUp::where('lead_id', $leadData[0]['lead_id'])->delete(); // No entry into follow-up.
                                } else { //Not booked
                                    $buffercontroller->saveFollowupentryWithDispositions($leadData[0]['lead_id'], $follow_up_date_time, $hours); //Entry into follow-up with 2 hours
                                }
                                if ($reviewCall > 0) {
                                    LeadOldCallBuffer::where('lead_id', $leadData[0]['lead_id'])->delete();
                                } else {
                                    $getLeadCallBuffer = LeadCallBuffer::where('lead_id', $leadData[0]['lead_id'])->first();
                                    if (isset($getLeadCallBuffer)) {
                                        $leadactiviyobj->createActivity($leadData[0]['lead_id'], 8);
                                    }
                                    LeadCallBuffer::where('lead_id', $leadData[0]['lead_id'])->delete();
                                }
                            }
                        }
                    }

                    if ($dispositionId == 7) { //Wrong Phone
                        $getLeadFollowUp = LeadFollowUp::where('lead_id', $lead_id)->first();
                        if (isset($getLeadFollowUp)) {
                            $leadactiviyobj->createActivity($lead_id, 13);
                        }
                        LeadFollowUp::where('lead_id', $lead_id)->delete(); // No entry into follow-up.
                        Lead::where('lead_id', $lead_id)->update(['is_dnc_call' => 'yes', 'is_dnc_sms' => 'yes']); //Mark this lead as DNC.
                        // $leadListControllerobj->dncemailsend($lead_id, "yes", "yes", "yes");
                        $leadIdEmailArr[] = $lead_id;
                        $follow_up_date = date('Y-m-d', strtotime('+1 day', strtotime($date)));
                        $follow_up_date_time = $follow_up_date . ' 08:00:00';
                        $this->createOutboundLogs($lead_id, $lead_call_id, 22, "22 snapIt log hangupDisposition saveFollowupentryWithDispositions Wrong Phone : " . $follow_up_date_time . "==" . $reviewCall . '===user_id-' . $repId);
                        for ($f = 0; $f < count($getBufferData); $f++) {
                            $leadData = Lead::where('lead_id', $getBufferData[$f]['lead_id'])->get()->toArray();
                            if (count($leadData) > 0) {
                                $leadBook = $leadData[0]['book_type_id'];
                                if ($leadBook > 1) { //If booked
                                    $getLeadFollowUp = LeadFollowUp::where('lead_id', $leadData[0]['lead_id'])->first();
                                    if (isset($getLeadFollowUp)) {
                                        $leadactiviyobj->createActivity($leadData[0]['lead_id'], 13);
                                    }
                                    LeadFollowUp::where('lead_id', $leadData[0]['lead_id'])->delete(); // No entry into follow-up.
                                } else { //Not booked
                                    $buffercontroller->saveFollowupentryWithDispositions($leadData[0]['lead_id'], $follow_up_date_time, $hours); //Entry in follow up table next day 8 AM Local Time
                                }
                                if ($reviewCall > 0) {
                                    LeadOldCallBuffer::where('lead_id', $leadData[0]['lead_id'])->delete();
                                } else {
                                    $getLeadCallBuffer = LeadCallBuffer::where('lead_id', $leadData[0]['lead_id'])->first();
                                    if (isset($getLeadCallBuffer)) {
                                        $leadactiviyobj->createActivity($leadData[0]['lead_id'], 8);
                                    }
                                    LeadCallBuffer::where('lead_id', $leadData[0]['lead_id'])->delete();
                                }
                            }
                        }
                    }

                    if ($dispositionId == 8 || $dispositionId == 10 || $dispositionId == 11 || $dispositionId == 12 || $dispositionId == 13) { //Booked and Truck Rental
                        $getLeadFollowUp = LeadFollowUp::where('lead_id', $lead_id)->first();
                        if (isset($getLeadFollowUp)) {
                            $leadactiviyobj->createActivity($lead_id, 13);
                        }
                        LeadFollowUp::where('lead_id', $lead_id)->delete(); // No entry into follow-up.
                    }
                    // $leadactiviyobj = new LeadActivity();
                    // $leadactiviyobj->createActivity($lead_id, 8);
                    if ($reviewCall > 0) {
                        LeadOldCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $lead_category)->delete();
                    } else {
                        $getLeadCallBuffer = LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $lead_category)->first();
                        if (isset($getLeadCallBuffer)) {
                            $leadactiviyobj->createActivity($lead_id, 8);
                        }
                        LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $lead_category)->delete();
                    }
                    LeadCall::where('lead_call_id', $lead_call_id)->update(['call_disposition_id' => ((int) ($dispositionId))]);
                    RepActiveCall::where('call_uuid', $outbound_data[0]['legA_call_uuid'])->delete();
                    $this->tokLoginLog("no", $repId);
                }
            }
            if (count($leadIdEmailArr) > 0) {
                $leadIdEmailArr = array_unique($leadIdEmailArr);
                foreach ($leadIdEmailArr as $key => $value) {
                    $leadListControllerobj->dncemailsend($value, "yes", "yes", "yes");
                }
            }
            if ($dispositionId == 2) { //DNC
                $message = 'Successfully store into DNC.';
            } else if ($dispositionId == 3) { //Not Intersted
                $message = 'Successfully store into DNC/Not Intersted.';
            } else if ($dispositionId == 4) { //Folloup
                $message = 'Successfully store into follow-up.';
            } else if ($dispositionId == 5) { //No Buyer
                $message = 'Successfully store into No Buyer.';
            } else if ($dispositionId == 6) { //Voice Message
                $message = 'Successfully Store Voice Message.';
            } else if ($dispositionId == 7) { //Wrong Phone
                $message = 'Successfully Store Wrong Phone.';
            } else if ($dispositionId == 8 || $dispositionId == 10 || $dispositionId == 11 || $dispositionId == 12) { //Booked with other movers
                $message = 'Successfully Store Booked.';
            } else if ($dispositionId == 13) { //Truck Rental
                $message = 'Successfully Store Truck Rental.';
            } else if ($dispositionId == 14) { //IB Pickup
                $message = 'Successfully Store IB Pickup.';
            }

            if($dispositionId == 4 || $dispositionId == 5 || $dispositionId == 6 || $dispositionId == 15) {
                $checkLeadCall = LeadCall::where('lead_call_id', $lead_call_id)->where('inbound_type', 'manual')->first();
                if($checkLeadCall) {
                    if($checkLeadCall->call_disposition_id == 1) {
                        $smsDetail = LeadCustomerSmsLog::where('sms_subject', 'Live Transfer SMS')->where('lead_id', $lead_id)->first();
                        if(empty($smsDetail)) {
                            $businessesData = Business::where('business_id', $checkLeadCall->business_id)->get(['credit_available', 'credit_reserved','business_name'])->first();
                            if ($businessesData) {
                                $liveTransferSmsObj = New LiveTransferSms();
                                $liveTransferSmsObj->liveTransferSMS($lead_id, $businessesData->business_name);
                            }    
                        }
                    } else {
                        if($checkLeadCall->call_type == 'inbound') {
                            $smsDetail = LeadCustomerSmsLog::where('sms_subject', 'Follow Up Call SMS')->where('lead_id', $lead_id)->first();
                            if (empty($smsDetail)) {
                                $followUpCallSmsObj = New FollowUpCallSms();
                                $followUpCallSmsObj->followUpCallSMS($lead_id);
                            }    
                        }    
                    }
                }   
            }

            return $message;
        } catch (Exception $ex) {
            $error = $ex->getMessage();
            $this->createOutboundLogs($lead_id, $lead_call_id, 22, "22 snapIt log hangupDisposition Catch Exception : " . $error . '===user_id-' . $repId);
            return $error;
        }
    }

    public function tokHangupManually($parameters) {
        $reviewCall = isset($parameters['review_call']) ? $parameters['review_call'] : 0;
        $acceptCall = isset($parameters['accept_call']) ? $parameters['accept_call'] : 0;
        $lead_call_id = isset($parameters['id']) ? $parameters['id'] : 0;
        $lead_id = isset($parameters['lead_id']) ? $parameters['lead_id'] : 0;
        $repId = isset($parameters['rep_id']) ? $parameters['rep_id'] : 0;
        //echo $acceptCall."===".$type."====<br>";
        $this->createOutboundLogs($lead_id, $lead_call_id, 23, '23 snapIt log tokHangupManually post data==' . json_encode($parameters) . '===user_id-' . $repId);
        $urlto_hangup = $call_uuid = $tableName = $dataJson = "";
        if ($lead_call_id > 0) {
            $outbound_data = LeadCall::where('lead_call_id', $lead_call_id)->get()->toArray();
            if (count($outbound_data) > 0) {
                $dataJson = json_encode($outbound_data);
                $tableName = "lead_call";
                $call_uuid = $outbound_data[0]['legA_call_uuid'];
                if (trim($call_uuid) != "") {
                    $endDecObj = new Helper;
                    $general_variable_1 = Helper::checkServer()['general_variable_1'];
                    $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
                    $plivoBaseURL = 'https://api.plivo.com/v1/Account/' . $decVariable1;
                    $urlto_hangup = $plivoBaseURL . '/Call/' . $call_uuid;
                    $checkStatus = $this->checkCallStatus($urlto_hangup);
                    //echo "<pre>";print_r($checkStatus);die;
                    $this->createOutboundLogs($lead_id, $lead_call_id, 27, '27 snapIt log checkCallStatus response==' . json_encode($checkStatus) . '== call_uuid==' . $call_uuid . ',json data==' . $dataJson . '==url=' . $urlto_hangup . ',tablename=' . $tableName . '===user_id-' . $repId);
                    if (isset($checkStatus['call_status']) && strtoupper($checkStatus['call_status']) == "IN-PROGRESS") {
                        if (trim($urlto_hangup) != "") {
                            try {
                                $this->createOutboundLogs($lead_id, $lead_call_id, 24, '24 snapIt log tokHangupManually function call request== .' . $dataJson . ',call_uuid==' . $call_uuid . ',Hangup URL==' . $urlto_hangup . ',tablename=' . $tableName . '===user_id-' . $repId);
                                $result = $this->CalltoHangup($urlto_hangup);
                                $this->createOutboundLogs($lead_id, $lead_call_id, 25, '25 snapIt log CalltoHangup function call response== .' . json_encode($result) . '===user_id-' . $repId);
                            } catch (Exception $e) {
                                $message = 'Error while Hangup Call.';
                                $error = $e->getMessage();
                                $this->createOutboundLogs($lead_id, $lead_call_id, 26, '26 snapIt log tokHangupManually catch error .' . $error . ',call_uuid==' . $call_uuid . ',json data==' . $dataJson . ',tablename=' . $tableName . '===user_id-' . $repId);
                            }
                        } else {
                            $this->createOutboundLogs($lead_id, $lead_call_id, 27, '27 snapIt log tokHangupManually blank error call_uuid==' . $call_uuid . ',json data==' . $dataJson . ',tablename=' . $tableName . '===user_id-' . $repId);
                        }
                    } else {
                        $urlto_hangup = "";
                        $this->createOutboundLogs($lead_id, $lead_call_id, 27, '27 snapIt log tokHangupManually blank error call_uuid==' . $call_uuid . ',json data==' . $dataJson . ',tablename=' . $tableName . '===user_id-' . $repId);
                    }
                }
            }
        }
        $message = 'Call Sucessfully Hangup.';
        //return $message;
    }

    public function getTokoutboundtocampaign(Request $request) {
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        $Parameters = $request->all();
        //Added By HJ On 02-08-2023 For Comio Number Start
        if (isset($Parameters['SIP-H-To'])) {
            $Parameters['To'] = substr($Parameters['SIP-H-To'], 6, 11);
            $Parameters['From'] = trim($Parameters['CallerName'], "+");
        }
        //Added By HJ On 02-08-2023 For Comio Number End
        $lead_call_id = isset($Parameters['lead_call_id']) ? $Parameters['lead_call_id'] : 0;
        $lead_id = isset($Parameters['lead_id']) ? $Parameters['lead_id'] : 0;
        $user_id = isset($Parameters['user_id']) ? $Parameters['user_id'] : 0;
        $accept_call = isset($Parameters['accept_call']) ? $Parameters['accept_call'] : 0;
        $phoneNumber = isset($Parameters['phone']) ? $Parameters['phone'] : 0;
        $legBCallUUID = isset($Parameters['CallUUID']) ? $Parameters['CallUUID'] : 0;

        $conferenceName = "Call_".$lead_call_id;
        
        $responseX = '<Response>
                    <Conference
                        waitSound="ring"
                        startConferenceOnEnter="true"
                        endConferenceOnExit="true"
                    >'.$conferenceName.'</Conference>
                </Response>';
        // Echo the Response
        header("Content-type: text/xml");
        //DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 snapIt log callforwardtomain call==" . $response]);
        echo $responseX;

        $this->createOutboundLogs($lead_id, $lead_call_id, 28, "28 snapIt log getTokoutboundtocampaign Call post data=." . json_encode($Parameters) . "==transfer number=" . $phoneNumber . "==user_id=" . $user_id);
        try {
            $country_code = Helper::checkServer()['country_code'];
            $call_uuid = $callerId = "";
            if ($accept_call > 0 || $accept_call == "1") {
                $country_code = Helper::checkServer()['tok_country_code'];
                $call_uuid = $request->get('call_uuid');
                if (isset($Parameters['From'])) {
                    $callerId = trim($Parameters['From'], "+");
                }
                if (isset($Parameters['transfer']) && $Parameters['transfer'] == 0) {
                    $callerId = trim($Parameters['To'], "+");
                }
                $this->createOutboundLogs($lead_id, $lead_call_id, 29, "29 snapIt log getTokoutboundtocampaign caller id inbound==" . $callerId . ",==forward number=" . $country_code . $phoneNumber . "==user_id=" . $user_id);
            } else {
                if (isset($Parameters['To'])) {
                    $callerId = trim($Parameters['To'], "+");
                }
                $this->createOutboundLogs($lead_id, $lead_call_id, 30, "30 snapIt log getTokoutboundtocampaign caller id outbound==" . $callerId . ",==forward number=" . $country_code . $phoneNumber . "==user_id=" . $user_id);
            }
            $fetchLeadCall = LeadCall::where('lead_call_id', $lead_call_id)->first();
            $legACallUUID = '';
            if($fetchLeadCall) {
                $legACallUUID = $fetchLeadCall->legA_call_uuid;
            }
            //dd($phoneNumber);
            /*if ($callerId != "") {
                $response = '<?xml version="1.0" encoding="UTF-8"?><Response>
                    <Dial callerId="' . $callerId . '" callbackUrl="' . $matrixBaseURL_Outbound . '/playIVR?call_uuid=' . $legBCallUUID . '">
                        <Number>' . $country_code . $phoneNumber . '</Number>
                    </Dial>
                </Response>';
            } else {
                /*$response = '<?xml version="1.0" encoding="UTF-8"?><Response>
                    <Dial callbackUrl="' . $matrixBaseURL_Outbound . '/playIVR?call_uuid=' . $legBCallUUID . '">
                        <Number>' . $country_code . $phoneNumber . '</Number>
                    </Dial>
                </Response>';*/

                /*$response = '<?xml version="1.0" encoding="UTF-8"?><Response>
                    <Dial callbackUrl="' . $matrixBaseURL_Outbound . '/conferencecall?lead_call_id=' . $lead_call_id . '">
                        <Number>' . $country_code . $phoneNumber . '</Number>
                    </Dial>
                </Response>';


            }*/

            $endDecObj = new Helper;
            $general_variable_1 = Helper::checkServer()['general_variable_1'];
            $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
            $plivoBaseURL = 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/Call/';
            $data = array(
                'from' => $callerID,
                'to' => $country_code . $phoneNumber,
                'answer_url' => $matrixBaseURL_Outbound . '/conferencecall?lead_call_id=' . $lead_call_id,
                'hangup_url' => ''
            );
            $data = json_encode($data);
            $this->Calltocurl($plivoBaseURL, $data);


            // Echo the Response
            header("Content-type: text/xml");
            if (trim($call_uuid) != "" && $accept_call > 0) {
                $transfer = $request->get('transfer');
                $endDecObj = new Helper;
                $general_variable_1 = Helper::checkServer()['general_variable_1'];
                $general_variable_2 = Helper::checkServer()['general_variable_2'];
                $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
                $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
                $para = json_encode(array('time_limit' => '7200'));
                $ch = curl_init();

                curl_setopt($ch, CURLOPT_URL, 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/Call/' . $call_uuid . '/Record/');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $para);
                curl_setopt($ch, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);

                $headers = array();
                $headers[] = 'Content-Type: application/json';
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                $responseData = curl_exec($ch);
                $this->createOutboundLogs($lead_id, $lead_call_id, 30, "31 snapIt log getTokoutboundtocampaign Record response==" . $call_uuid . ',response=' . $responseData . "==user_id=" . $user_id);
                if ((!empty($responseData)) && ($responseData !== null)) {
                    $recordingData = json_decode($responseData, true);
                    if (!empty($recordingData)) {
                        $recording_url = isset($recordingData['url']) ? $recordingData['url'] : '';
                        $recording_id = isset($recordingData['recording_id']) ? $recordingData['recording_id'] : '';
                        if ($transfer > 0) {
                            LeadTransferCall::where('call_uuid', $call_uuid)->update(['recording_url' => $recording_url, 'recording_id' => $recording_id]);
                            /*$checkLeadCall = LeadCall::where('lead_call_id', $lead_call_id)->where('inbound_type', 'manual')->first();
                            if($checkLeadCall) {
                                $smsDetail = LeadCustomerSmsLog::where('sms_subject', 'Live Transfer SMS')->where('lead_id', $lead_id)->first();
                                $businessesData = Business::where('business_id', $checkLeadCall->business_id)->get(['credit_available', 'credit_reserved','business_name'])->first();
                                if(empty($smsDetail)) {
                                    if ($businessesData) {
                                        $liveTransferSmsObj = New LiveTransferSms();
                                        $liveTransferSmsObj->liveTransferSMS($lead_id, $businessesData->business_name);
                                    }
                                }
                            }*/
                            
                        } else {
                            LeadCall::where('legA_call_uuid', $call_uuid)->update(['recording_url' => $recording_url, 'recording_id' => $recording_id]);
                        }
                        $this->createOutboundLogs($lead_id, $lead_call_id, 31, "31 snapIt log getTokoutboundtocampaign==" . $call_uuid . ",==recording=" . $recording_url . ',response=' . $responseData . "==user_id=" . $user_id);
                    }
                }
            }
            $this->createOutboundLogs($lead_id, $lead_call_id, 32, "32 snapIt log getTokoutboundtocampaign transfer xml==" . $response . "==user_id=" . $user_id);
            echo $response;
            exit;
        } catch (Exception $ex) {
            $error = $ex->getMessage();
            $this->createOutboundLogs($lead_id, $lead_call_id, 33, "33 snapIt log getTokoutboundtocampaign Catche error==" . $error . "==user_id=" . $user_id);
        }
    }

    public function CalltoHangup($url) {
        $status = "FAILURE";
        $error = "Error while Hangup Call loan.";
        try {
            $endDecObj = new Helper;
            $general_variable_1 = Helper::checkServer()['general_variable_1'];
            $general_variable_2 = Helper::checkServer()['general_variable_2'];
            $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
            $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            curl_setopt($ch, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
            $response = curl_exec($ch);
            DB::table('dev_debug')->insert(['sr_no' => 4, 'result' => "4 snapIt log CalltoHangup call==" . $response]);
            //echo "<pre>";print_r($response);die;
            curl_close($ch);
            return $response;
        } catch (Exception $e) {
            $error = $e->getMessage();
            DB::table('dev_debug')->insert(['sr_no' => 5, 'result' => "5 snapIt log CalltoHangup call catch error==" . $error]);
        }
        $response = array('status' => $status, 'error' => $error);
        return $response;
    }

    public function checkCallStatus($plivoUrl) {
        $status = "FAILURE";
        $message = $error = '';
        try {
            $endDecObj = new Helper;
            $general_variable_1 = Helper::checkServer()['general_variable_1'];
            $general_variable_2 = Helper::checkServer()['general_variable_2'];
            $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
            $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
            //echo $decVariable1."==".$decVariable2."<br>";
            //Added By HJ On 04-01-2022 For Custom Encrypt and Decrypt Method End
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $plivoUrl . '/?status=live');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
            curl_setopt($ch, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
            $result = curl_exec($ch);
            DB::table('dev_debug')->insert(['sr_no' => 8, 'result' => "8 snapIt log checkCallStatus call==" . $result]);
            curl_close($ch);
            $apiResponse = json_decode($result, true);
            return $apiResponse;
        } catch (Exception $ex) {
            $error = $ex->getMessage();
            DB::table('dev_debug')->insert(['sr_no' => 9, 'result' => "9 snapIt log checkCallStatus catch error==" . $error]);
        }
        $apiResponse = array();
        $apiResponse['error'] = $error;
        $apiResponse['call_status'] = $status;
        return $apiResponse;
    }

    public function checkUpdateFreeLead($leadId, $campId, $payout, $userId, $leadCallId) {
        if ($payout > 0) {
            $businessesCampaignDetails = Campaign::where('campaign_id', $campId)->get(['credit_available', 'payment_type', 'free_lead', 'campaign_id', 'business_id'])->toArray();
            if (count($businessesCampaignDetails) > 0) {
                //echo "<pre>";print_r($businessesCampaignDetails);die;
                $balance = 0;
                if ($businessesCampaignDetails[0]['payment_type'] == 0) {
                    $businessesData = Business::where('business_id', $businessesCampaignDetails[0]['business_id'])->get(['credit_available'])->toArray();
                    //echo "<pre>";print_r($businessesData);die;
                    if (count($businessesData) > 0) {
                        $balance = $businessesData[0]['credit_available'];
                    }
                } else {
                    $balance = $businessesCampaignDetails[0]['credit_available'];
                }
                $actualPayout = $payout;
                if ($businessesCampaignDetails[0]['free_lead'] > 0) {
                    $applyFreeLead = $this->checkCampaingBalance($campId, $payout, $balance); //Added By MD On 28-2-2022 For Check Campaign Free Lead Count with Balance
                    if ($applyFreeLead > 0) {
                        $payout = 0;
                        // Store in lead_routing_free table
                        //CampaignFreeLead::create(['lead_id' => $leadId, 'camapaign_id' => $campId, 'lead_id' => $leadId, 'free_lead' => 1, 'user_id' => $userId, 'created_at' => date('Y-m-d H:i:s')]);
                        // Update In free_lead entry in businesses_campaign table
                        $leftFreeLead = $businessesCampaignDetails[0]['free_lead'] - 1;
                        Campaign::where('campaign_id', $campId)->update(['free_lead' => $leftFreeLead]);
                        //$logic = new TokSystemLogic();
                        $this->createOutboundLogs($leadId, $leadCallId, 34, '34 snapIt log BusinessesCampaign : ' . $campId . ', before aval free call' . $businessesCampaignDetails[0]['free_lead'] . ', after aval free call: ' . $leftFreeLead, ', actual payout:' . $actualPayout . ', payment type: ' . $businessesCampaignDetails[0]['payment_type'] . ', credit_available :' . $balance . "==user_id=" . $userId);
                    }
                }
            }
        }
        return $payout;
    }

    public function checkCampaingBalance($campaignId, $payout, $balance) {
        $return = 0;
        if ($balance >= $payout) {
            $return = 1;
        }
        return $return;
    }

    public function acceptCallbySalesrep(Request $request) {
        $parameters = $request->all();
        $status = "FAILURE";
        $message = $error = $outbound_entry = '';
        $lead_call_id = isset($parameters['lead_call_id']) ? $parameters['lead_call_id'] : 0;
        $lead_id = isset($parameters['lead_id']) ? $parameters['lead_id'] : 0;
        $user_id = isset($parameters['rep_id']) ? $parameters['rep_id'] : 0;
        if ($lead_call_id > 0 && $lead_id > 0) {
            $this->createOutboundLogs($lead_id, $lead_call_id, 1, "1 snapIt log acceptCallbySalesrep post data==" . json_encode($parameters) . "==user_id=" . $user_id);
        } else {
            DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 snapIt log acceptCallbySalesrep post data==" . json_encode($parameters) . "==user_id=" . $user_id]);
        }
        try {
            if ($lead_call_id > 0 && $lead_id > 0) {
                $getCallData = LeadCall::where('lead_call_id', $lead_call_id)->orderBy('lead_call_id', 'desc')->get()->toArray();
                if (count($getCallData) > 0) {
                    $callUuid = isset($getCallData[0]['legA_call_uuid']) ? trim($getCallData[0]['legA_call_uuid']) : '';
                    $cust_number = isset($getCallData[0]['from_number']) ? trim($getCallData[0]['from_number']) : '';
                    $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
                    $endDecObj = new Helper;
                    $general_variable_1 = Helper::checkServer()['general_variable_1'];
                    $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
                    $urltoaleg = 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/Call/' . $callUuid . '/';
                    $datatran = array('legs' => 'aleg', 'aleg_url' => $matrixBaseURL_Outbound . '/transferincomingcalltosip?user_id=' . $user_id .'&call_id='.$lead_call_id);
                    $encodeData = json_encode($datatran);
                    $this->createOutboundLogs($lead_id, $lead_call_id, 3, '3 snapIt log acceptCallbySalesrep request .' . $encodeData);
                    $aleg_response = $this->Calltocurl($urltoaleg, $encodeData);
                    $this->createOutboundLogs($lead_id, $lead_call_id, 4, '4 snapIt log acceptCallbySalesrep response .' . $aleg_response);
                    $status = "SUCCESS";
                    $rep_live_call = array('user_id' => $user_id, 'phone' => $cust_number, 'call_uuid' => $callUuid);
                    RepActiveCall::create($rep_live_call);
                    return $aleg_response;
                }
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
        }
        $response = array('status' => $status, 'data' => $outbound_entry, 'error' => $error);
        if ($lead_call_id > 0 && $lead_id > 0) {
            $this->createOutboundLogs($lead_id, $lead_call_id, 5, "5 snapIt log acceptCallbySalesrep catch error==" . json_encode($response) . "==user_id=" . $user_id);
        } else {
            DB::table('dev_debug')->insert(['sr_no' => 5, 'result' => "5 snapIt log acceptCallbySalesrep catch error==" . json_encode($response) . "==user_id=" . $user_id]);
        }
        return json_encode($response);
    }

    public function transferIncomingCallToSip(Request $request) {
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        $post_data = $request->all();
        DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 snapIt log transferIncomingCallToSip post data==" . json_encode($post_data) . "=user_id=" . $post_data['user_id']]);
        $error = "success";
        $content = '<?xml version="1.0" encoding="UTF-8"?><Response></Response>';
        try {
            if (isset($post_data['user_id']) && trim($post_data['user_id']) > 0) {
                $call_id = $post_data['call_id'];
                $userId = trim($post_data['user_id']);
                $user_data = User::where('id', $userId)->get()->toArray();
                DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 snapIt log transferIncomingCallToSip user data==" . json_encode($user_data) . "=user_id=" . $userId]);
                if (count($user_data) > 0) {
                    $call_uuid = $post_data['CallUUID'];
                    $sip_endpoint = $user_data[0]['sip_endpoint'];
                    $params = [
                        'call_uuid' => $call_uuid,
                        'call_id' => $call_id
                    ];
                    $redirectUrl = $matrixBaseURL_Outbound . '/playIVR?'. http_build_query($params);
                    $escapedUrl = htmlspecialchars($redirectUrl, ENT_XML1);
                    $content = '<?xml version="1.0" encoding="UTF-8"?>
                        <Response>
                            <Dial callbackUrl="' . $escapedUrl . '">
                                <User>sip:' . $sip_endpoint . '@phone.plivo.com</User>
                            </Dial>
                        </Response>';
                }
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
        }
        DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 snapIt log transferIncomingCallToSip xml==" . $content . "===" . $error . "=user_id=" . $userId]);
        return response($content, 200)->header('Content-Type', 'text/xml');
    }

    public function Calltocurl($url, $datatran) {
        try {
            // Call The Customer
            $endDecObj = new Helper;
            $general_variable_1 = Helper::checkServer()['general_variable_1'];
            $general_variable_2 = Helper::checkServer()['general_variable_2'];
            $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
            $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
            $ch = curl_init($url);
            curl_setopt_array($ch, array(
                CURLOPT_RETURNTRANSFER => TRUE,
                CURLOPT_POSTFIELDS => $datatran,
                CURLOPT_USERPWD => $decVariable1 . ':' . $decVariable2,
                CURLOPT_FOLLOWLOCATION => 1,
                CURLOPT_HTTPHEADER => array('Content-type: application/json', 'Cache-Control:no-store', 'Content-Length: ' . strlen($datatran)),
                CURLOPT_TIMEOUT => 40
            ));
            $response = curl_exec($ch);
            DB::table('dev_debug')->insert(['sr_no' => 6, 'result' => "6 snapIt log Calltocurl call==" . $response]);
            //echo "<pre>Calltocurl==";print_r($response);die;
            //dd($response);
            curl_close($ch);
            return $response;
        } catch (Exception $ex) {
            $error = $ex->getMessage();
            DB::table('dev_debug')->insert(['sr_no' => 7, 'result' => "7 snapIt log Calltocurl call catch error==" . $error]);
        }
    }

    public function storeLeadFollowupByDistance($lead_id, $date, $categoryId) {
        $move_type = "local";
        $leadMoveInfos = array();
        if ($categoryId == 1) {
            $leadMoveInfos = LeadMoving::where('lead_id', $lead_id)->get()->toArray();
        } else if ($categoryId == 3) {
            $leadMoveInfos = LeadHeavyLifting::where('lead_id', $lead_id)->get()->toArray();
        } else if ($categoryId == 4) {
            $leadMoveInfos = LeadCarTransport::where('lead_id', $lead_id)->get()->toArray();
        }
        if (count($leadMoveInfos) > 0) {
            if ($leadMoveInfos[0]['move_type'] != "") {
                $move_type = $leadMoveInfos[0]['move_type'];
            }
        }
        $hours = 0;
        if ($move_type == "local") { // Local lead Entry in follow-up with 2 hours
            $follow_up_date_time = date('Y-m-d H:i:s', strtotime('+2 hours', strtotime($date)));
        } else { // Long Distance Entry in follow-up with 30 mins
            $follow_up_date_time = date('Y-m-d H:i:s', strtotime('+30 mint', strtotime($date)));
        }
        $buffercontroller = new LeadCallBufferController();
        $buffercontroller->saveFollowupentryWithDispositions($lead_id, $follow_up_date_time, $hours);
    }

    public function createSIPUser() {
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $general_variable_2 = Helper::checkServer()['general_variable_2'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
        echo $decVariable1 . "==" . $decVariable2 . "<br>";
        die;
        $userName = "analyst";
        $rand = rand(0000, 1111);
        $password = ucfirst($userName) . "@pwd" . $rand;
        $userArr = ["username" => $userName . "" . $rand, "password" => $password, "alias" => strtoupper($userName) . "_" . $rand,];
        echo "<pre>";
        print_r($userArr);
        echo "<br>";
        $url = "https://api.plivo.com/v1/Account/" . $decVariable1 . "/Endpoint/";
        $userData = json_encode($userArr);
        $plivoResponse = $this->Calltocurl($url, $userData);
        echo "<pre>";
        print_r($plivoResponse);
        die;
    }

    public function autoOutboundCallCronCallItOn() {
        $from_zipcode = $to_zipcode = 0;
        $automatedoutboundcalls = MstCallConfig::where('call_config', 'automatedoutboundcalls')->where('status', 'active')->get()->toArray();
         // echo "<pre>"; print_r($automatedoutboundcalls); exit();
        if (count($automatedoutboundcalls) > 0) {
            //echo "start call"; die();
            DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 snapIt log autoOutboundCallCronCallItOn Cron Run At==" . date("Y-m-d H:i:s")]);
            // echo "Stopped By HJ On 18-09-2023 As Per Discussed With Varun Sir";
            // die; //Stopped By HJ On 18-09-2023 As Per Discussed With Varun Sir
            try {
                $hours_check_cron = date('G');
                if ($hours_check_cron <= 9 || $hours_check_cron >= 20) {
                    DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 snapIt log autooutboundcallcroncallitoff Night time ==" . date("Y-m-d H:i:s")]);
                    echo 'Night time.<br>';
                    exit; //Remove this line
                }
                $leadactiviyobj = new LeadActivity();
                $calitoutbound = MstCallConfig::where('call_config', 'calitoutbound')->where('status', 'active')->get()->toArray();
                //echo "<pre>";print_r($calitoutbound);die;
                DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 snapIt log autoOutboundCallCronCallItOn Config ==" . json_encode($calitoutbound)]);
                if (count($calitoutbound) > 0) {
                    $currentdateTime = date('Y-m-d H:i:s');
                    $leadCallCriteArea = new LeadCall();
                    // $getAllLeads = LeadCallBuffer::with('lead', 'routingInfo')->where('fail_attempt', 0)->where('lead_id', 3677)->get()->toArray();
                    $getAllLeads = LeadCallBuffer::with('lead', 'routingInfo')->where('fail_attempt', 0)->orderby('call_buffer_score', 'ASC')->orderBy('lead_call_buffer_id', 'ASC')->get()->toArray();
                     // echo "<pre>";print_r($getAllLeads);die;
                    $leadIdArr = $attemptcheck = array();
                    for ($d = 0; $d < count($getAllLeads); $d++) {
                        $leadIdArr[] = $getAllLeads[$d]['lead_id'];
                    }
                    //echo "<pre>";print_r($leadIdArr);die;
                    $dncListData = Lead::where('is_dnc_call', 'yes')->whereIn('lead_id', $leadIdArr)->get()->toArray();
                    $dncNumberArr = array();
                    for ($x = 0; $x < count($dncListData); $x++) {
                        $dncNumberArr[] = $dncListData[$x]['phone'];
                    }
                    if (count($leadIdArr) > 0) {
                        $attemptcheck = LeadCall::whereIn('lead_id', $leadIdArr)->orderby('lead_call_id', 'DESC')->get(['lead_call_id', 'lead_id', 'call_datetime'])->toArray();
                        for ($at = 0; $at < count($attemptcheck); $at++) {
                            if (!isset($attemptDataArr[$attemptcheck[$at]['lead_id']])) {
                                $attemptDataArr[$attemptcheck[$at]['lead_id']] = $attemptcheck[$at];
                            }
                        }
                    }
                    //echo "<pre>";print_r($getAllLeads);die;
                    $dialCallCount = 0;
                    DB::table('dev_debug')->insert(['sr_no' => 4, 'result' => "4 snapIt log autoOutboundCallCronCallItOn Buffer Data ==" . json_encode($getAllLeads)]);
                    for ($g = 0; $g < count($getAllLeads); $g++) {
                        $leadvalue = $getAllLeads[$g]['lead'];
                        $lead_id = $getAllLeads[$g]['lead_id'];
                        $lead_category = $leadvalue['lead_category_id'];
                        $book_type_id = $leadvalue['book_type_id'];
                        $lead_source_id = $leadvalue['lead_source_id'];
                        $lead_type = $leadvalue['lead_type'];
                        $remaining_slot = $leadvalue['remaining_slot'];
                        $phone = $leadvalue['phone'];
                        if (!in_array($getAllLeads[$g]['customer_number'], $dncNumberArr) && $dialCallCount < 5 && $lead_type == "normal" && $remaining_slot > 0) {
                            $lead_dialer = 0;
                            if ($leadvalue['sold_type'] == "exclusive") {
                                $lead_dialer = 1;
                            }
                            $lead_routing_exist_check = $attemptcheck = array();
                            $routing_info = $getAllLeads[$g]['routing_info'];
                            //echo "<pre>";print_r($routing_info);die;
                            for ($f = 0; $f < count($routing_info); $f++) {
                                $lead_routing_exist_check[] = $routing_info[$f]['campaign_id'];
                            }
                            //echo "<pre>";print_r($lead_dialer);die;
                            //$attemptcheck = LeadCall::where('lead_id', $lead_id)->where('call_date', '>=', $leadvalue['lead_generated_at'])->first();
                            if (isset($attemptDataArr[$lead_id])) {
                                $attemptcheck[] = $attemptDataArr[$lead_id];
                            }
                            if (count($attemptcheck) > 0) {
                                //echo "<pre>";print_r($attemptcheck);die;
                                $call_date_time = $attemptcheck[0]['call_datetime'];
                                $last_call_attempcheck_timestamp1 = strtotime($call_date_time);
                                $timestamp2 = strtotime($currentdateTime);
                                $difference = abs($timestamp2 - $last_call_attempcheck_timestamp1) / (60 * 60);
                                if ($difference < 2) {
                                    $buffercontroller = new LeadCallBufferController();
                                    $getLeadCallBuffer = LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $lead_category)->first();
                                    if (isset($getLeadCallBuffer)) {
                                        $leadactiviyobj->createActivity($lead_id, 8);
                                    }
                                    LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $lead_category)->delete();
                                    if (date('G') >= 18) {
                                        $follow_up_date = date('Y-m-d', strtotime('+1 day', strtotime($currentdateTime)));
                                        $follow_up_time = '10:00:00';
                                        $follow_up_date_time = $follow_up_date . " " . $follow_up_time;
                                    } else {
                                        $follow_up_date_time = date('Y-m-d H:i:s', strtotime('+2 hours', strtotime($currentdateTime)));
                                    }
                                    $buffercontroller->saveFollowupentryWithDispositions($lead_id, $follow_up_date_time, 0);
                                    DB::table('dev_debug')->insert(['sr_no' => 5, 'result' => '5 snapIt log autoOutboundCallCronCallItOn Outbound Attempt In 2 hours slot. ==' . $lead_id . "==category=" . $lead_category]);
                                    continue;
                                }
                            }
                            // check talked / booked
                            if ($book_type_id > 1) {
                                continue;
                            }
                            //For Exclusive Lead Start
                            $new_lead_own = array();
                            if ($lead_dialer > 0) {
                                //echo "1<br>";
                                $businesses = $leadCallCriteArea->getBusinesses($lead_routing_exist_check, $lead_id, $lead_dialer, "inactive", 0);
                                $new_lead_own = $leadCallCriteArea->getBusinessesCriteria($businesses, $lead_id, $remaining_slot, 0, $lead_dialer, 0, 'inactive', 0, $lead_category);
                            }
                            DB::table('dev_debug')->insert(['sr_no' => 6, 'result' => "6 snapIt log autoOutboundCallCronCallItOn Campaign Data 1 ==" . json_encode($new_lead_own)]);
                            
                            //For Exclusive Lead End
                            //echo $lead_id."<br>";
                            if (count($new_lead_own) == 0 && $remaining_slot > 2) {
                                //echo "2<br>";
                                $businesses = $leadCallCriteArea->getBusinesses($lead_routing_exist_check, $lead_id, $lead_dialer, "active", 0);
                                $new_lead_own = $leadCallCriteArea->getBusinessesCriteria($businesses, $lead_id, $remaining_slot, 0, $lead_dialer, 0, "active", 0, $lead_category);
                            }

                            //echo "<pre>";print_r($new_lead_own);die;
                            //echo $lead_id."<br>";
                            //echo "<pre>";print_r($lead_routing_exist_check);
                            //echo "<br>=======================================================================================<br>";
                            //echo "<pre>";print_r($businesses);
                            //echo "<br>=======================================================================================<br>";
                            //echo "<pre>";print_r($new_lead_own);die;
                            DB::table('dev_debug')->insert(['sr_no' => 7, 'result' => "7 snapIt log autoOutboundCallCronCallItOn Campaign Data 2 ==" . json_encode($new_lead_own) . "===" . $lead_dialer . "==" . $remaining_slot]);
                            if (count($new_lead_own) > 0) {
                                //$all[] = $new_lead_own[0];
                                $dialCallCount += 1;
                                
                                $from_zipcode = isset($new_lead_own[0]['lead_data'][0]['from_zipcode']) ? $new_lead_own[0]['lead_data'][0]['from_zipcode'] : 0;
                                $to_zipcode = isset($new_lead_own[0]['lead_data'][0]['to_zipcode']) ? $new_lead_own[0]['lead_data'][0]['to_zipcode'] : 0;
                                if ((isset($from_zipcode) && $from_zipcode !== 0) && (isset($to_zipcode) && $to_zipcode !== 0)) {
                                    echo "Called to==" . $phone . "==lead Id" . $lead_id . "===" . $dialCallCount . "<br>";
                                    $this->outboundCallToCustomer($new_lead_own[0], $lead_category, $phone, $lead_source_id);
                                } else {
                                    echo "Not Call to==" . $phone . "==lead Id" . $lead_id . "===" . $dialCallCount . "<br>";
                                }
                                //LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $lead_category)->delete();
                                // echo "<pre>";print_r($new_lead_own);die;
                            } else {
                                DB::table('dev_debug')->insert(['sr_no' => 8, 'result' => "8 snapIt log autoOutboundCallCronCallItOn Campaing not found ==" . json_encode($getAllLeads[$g])]);
                            }
                        } else {
                            DB::table('dev_debug')->insert(['sr_no' => 9, 'result' => "9 snapIt log autoOutboundCallCronCallItOn Dial count over ==" . $dialCallCount . "==Lead Id==" . $lead_id . "==Phone DNC--" . json_encode($getAllLeads[$g]) . "===Lead Type==" . $lead_type]);
                        }
                    }
                } else {
                    echo 'Outbound Dial-Up Flag Disabled.<br>';
                    DB::table('dev_debug')->insert(['sr_no' => 10, 'result' => "10 snapIt log autoOutboundCallCronCallItOn Outbound Call Config Off ==" . date("Y-m-d H:i:s")]);
                }
            } catch (Exception $ex) {
                $error = $ex->getMessage();
                DB::table('dev_debug')->insert(['sr_no' => 11, 'result' => "11 snapIt log autoOutboundCallCronCallItOn catch error ==" . $error]);
            }
            echo "autoOutboundCallCronCallItOn Cron Ran Successfully.";
            die;

        }else{
            //echo "not call"; die();
            DB::table('dev_debug')->insert(['sr_no' => 12, 'result' => "12 Automated Outbound calls Config Off ==" . date("Y-m-d H:i:s")]);
        }
        
    }

    public function autooutboundcallcroncallitoff() {
        DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 snapIt log autooutboundcallcroncallitoff Cron Run On ==" . date("Y-m-d H:i:s")]);
        echo "Stopped By HJ On 13-10-2023 As Per Discussed With Dhruvbhai";
        die; //Stopped By HJ On 13-10-2023 As Per Discussed With Dhruvbhai
        try {
            $leadactiviyobj = new LeadActivity();
            $leadCall = new LeadCall();
            $hours_check_cron = date('G');
            if ($hours_check_cron <= 9 || $hours_check_cron >= 20) {
                DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 snapIt log autooutboundcallcroncallitoff Night time ==" . date("Y-m-d H:i:s")]);
                echo 'Night time.<br>';
                exit; //Remove this line
            }
            $calitoutbound = MstCallConfig::where('call_config', 'calitoutbound')->where('status', 'active')->get()->toArray();
            //echo "<pre>";print_r($calitoutbound);die;
            if (count($calitoutbound) > 0) {
                DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 snapIt log autooutboundcallcroncallitoff Stop System Outbound calls. ==" . date("Y-m-d H:i:s")]);
                echo 'Stop System Outbound calls.';
                exit;
            }
            //echo "<pre>";print_r($calitoutbound);die;
            $currentdateTime = date('Y-m-d H:i:s');
            $getAllLeads = LeadCallBuffer::with('lead', 'routingInfo')->orderby('call_buffer_score', 'DESC')->orderBy('lead_call_buffer_id', 'DESC')->limit(10)->get()->toArray();
            $all = $leadIdArr = $attemptcheck = $dncNumberArr = array();
            for ($d = 0; $d < count($getAllLeads); $d++) {
                $leadIdArr[] = $getAllLeads[$d]['lead_id'];
            }
            //echo "<pre>";print_r($leadIdArr);die;
            if (count($leadIdArr) > 0) {
                $attemptcheck = LeadCall::whereIn('lead_id', $leadIdArr)->orderby('lead_call_id', 'DESC')->get(['lead_call_id', 'lead_id', 'call_datetime'])->toArray();
                for ($at = 0; $at < count($attemptcheck); $at++) {
                    if (!isset($attemptDataArr[$attemptcheck[$at]['lead_id']])) {
                        $attemptDataArr[$attemptcheck[$at]['lead_id']] = $attemptcheck[$at];
                    }
                }
                $dncListData = Lead::where('is_dnc_call', 'yes')->whereIn('lead_id', $leadIdArr)->get()->toArray();
                $dncNumberArr = array();
                for ($x = 0; $x < count($dncListData); $x++) {
                    $dncNumberArr[] = $dncListData[$x]['phone'];
                }
            }
            DB::table('dev_debug')->insert(['sr_no' => 4, 'result' => "4 snapIt log autooutboundcallcroncallitoff picked Lead Data ==" . json_encode($getAllLeads)]);
            //echo "<pre>";print_r($getAllLeads);die;
            for ($g = 0; $g < count($getAllLeads); $g++) {
                $leadvalue = $getAllLeads[$g]['lead'];
                $lead_id = $getAllLeads[$g]['lead_id'];
                $lead_category = $leadvalue['lead_category_id'];
                $book_type_id = $leadvalue['book_type_id'];
                $lead_source_id = $leadvalue['lead_source_id'];
                $lead_type = $leadvalue['lead_type'];
                $remaining_slot = $leadvalue['remaining_slot'];
                $phone = $leadvalue['phone'];
                if (!in_array($getAllLeads[$g]['customer_number'], $dncNumberArr) && $lead_type == "normal" && $remaining_slot > 0) {
                    //echo "<pre>";print_r($getAllLeads[$g]);die;
                    $lead_dialer = 0;
                    if ($leadvalue['sold_type'] == "exclusive") {
                        $lead_dialer = 1;
                    }
                    $lead_routing_exist_check = $attemptcheck = array();
                    if (isset($getAllLeads[$g]['routing_info'])) {
                        $routing_info = $getAllLeads[$g]['routing_info'];
                        //echo "<pre>";print_r($routing_info);die;
                        for ($f = 0; $f < count($routing_info); $f++) {
                            $lead_routing_exist_check[] = $routing_info[$f]['campaign_id'];
                        }
                    }
                    if (isset($attemptDataArr[$lead_id])) {
                        $attemptcheck[] = $attemptDataArr[$lead_id];
                    }
                    //echo "<pre>";print_r($attemptcheck);die;
                    if (count($attemptcheck) > 0) {
                        $last_call_attempcheck_timestamp1 = strtotime($attemptcheck[0]['call_datetime']);
                        $timestamp2 = strtotime($currentdateTime);
                        $difference = abs($timestamp2 - $last_call_attempcheck_timestamp1) / (60 * 60);
                        if ($difference < 2) {
                            $buffercontroller = new LeadCallBufferController();
                            $getLeadCallBuffer = LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $lead_category)->first();
                            if (isset($getLeadCallBuffer)) {
                                $leadactiviyobj->createActivity($lead_id, 8);
                            }
                            LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $lead_category)->delete();
                            if (date('G') >= 18) {
                                $follow_up_date = date('Y-m-d', strtotime('+1 day', strtotime($currentdateTime)));
                                $follow_up_time = '10:00:00';
                                $follow_up_date_time = $follow_up_date . " " . $follow_up_time;
                            } else {
                                $follow_up_date_time = date('Y-m-d H:i:s', strtotime('+2 hours', strtotime($currentdateTime)));
                            }
                            $follow_up_date_time = date('Y-m-d H:i:s', strtotime('+2 hours', strtotime($currentdateTime)));
                            $buffercontroller->saveFollowupentryWithDispositions($lead_id, $follow_up_date_time, 0);
                            echo "saveFollowupentryWithDispositions to==" . $phone . "==lead Id" . $lead_id . "===time==" . $follow_up_date_time . "<br>";
                            DB::table('dev_debug')->insert(['sr_no' => 5, 'result' => '5 snapIt log autooutboundcallcroncallitoff Outbound Attempt In 2 hours slot. ==' . $lead_id . "==category=" . $lead_category]);
                            continue;
                        }
                    }
                    // check talked / booked
                    if ($book_type_id > 1) {
                        continue;
                    }
                    $new_lead_own = array();
                    //echo $lead_dialer."<br>";
                    if ($lead_dialer > 0) {
                        $businesses = $leadCall->getBusinesses($lead_routing_exist_check, $lead_id, $lead_dialer, 'inactive', 0);
                        $new_lead_own = $leadCall->getBusinessesCriteria($businesses, $lead_id, $remaining_slot, 0, $lead_dialer, 0, 'inactive', 0, $lead_category);
                    }
                    if (count($new_lead_own) == 0 && $remaining_slot > 2) {
                        $businesses = $leadCall->getBusinesses($lead_routing_exist_check, $lead_id, $lead_dialer, 'active', 0);
                        $new_lead_own = $leadCall->getBusinessesCriteria($businesses, $lead_id, $remaining_slot, 0, $lead_dialer, 0, 'active', 0, $lead_category);
                    }
                    //echo $lead_id."<br>";
                    //echo "<pre>";print_r($lead_routing_exist_check);
                    //echo "<br>=======================================================================================<br>";
                    //echo "<pre>";print_r($businesses);
                    //echo "<br>=======================================================================================<br>";
                    //echo "<pre>";print_r($new_lead_own);die;
                    DB::table('dev_debug')->insert(['sr_no' => 6, 'result' => "6 snapIt log autooutboundcallcroncallitoff Campaing Data ==" . json_encode($new_lead_own) . "===lead_dialer==" . $lead_dialer . "==spot_left==" . $remaining_slot]);
                    if (count($new_lead_own) > 0) {
                        //echo $hours_check_cron."===".$hours_check_cron."===".$phone;
                        //echo "<br>";
                        if ($hours_check_cron > 9 || $hours_check_cron < 20) {
                            $all[] = $new_lead_own[0];
                            echo "Called to==" . $phone . "==lead Id" . $lead_id . "<br>";
                            //echo "<pre>";print_r($new_lead_own);
                            //echo "<br>";
                            //LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $lead_category)->delete();
                            $this->outboundCallToCustomer($new_lead_own[0], $lead_category, $phone, $lead_source_id);
                            //echo "done";die;
                        } else {
                            DB::table('dev_debug')->insert(['sr_no' => 7, 'result' => "7 snapIt log autooutboundcallcroncallitoff hour not matched ==" . $hours_check_cron]);
                        }
                    } else {
                        $getLeadCallBuffer = LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $lead_category)->first();
                        if (isset($getLeadCallBuffer)) {
                            $leadactiviyobj->createActivity($lead_id, 8);
                        }
                        LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $lead_category)->delete();
                        DB::table('dev_debug')->insert(['sr_no' => 8, 'result' => "8 snapIt log autooutboundcallcroncallitoff inser In follow up b'coz Campaign not found == lead id=" . $lead_id . "== For 1 Hours" . "==Category =" . $lead_category]);
                        $buffercontroller = new LeadCallBufferController();
                        if (date('G') >= 18) {
                            $follow_up_date = date('Y-m-d', strtotime('+1 day', strtotime($currentdateTime)));
                            $follow_up_time = '10:00:00';
                            $follow_up_date_time = $follow_up_date . " " . $follow_up_time;
                        } else {
                            $follow_up_date_time = date('Y-m-d H:i:s', strtotime('+1 hours', strtotime($currentdateTime)));
                        }
                        echo "saveFollowupentryWithDispositions to==" . $phone . "==lead Id" . $lead_id . "===time==" . $follow_up_date_time . "<br>";
                        $buffercontroller->saveFollowupentryWithDispositions($lead_id, $follow_up_date_time, 0);
                    }
                } else {
                    DB::table('dev_debug')->insert(['sr_no' => 9, 'result' => "9 snapIt log autooutboundcallcroncallitoff Dial count over ==" . $lead_id . "==Phone DNC--" . json_encode($getAllLeads[$g]) . "===Lead Type==" . $lead_type]);
                }
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
            //echo $error;die;
            DB::table('dev_debug')->insert(['sr_no' => 10, 'result' => "10 snapIt log autooutboundcallcroncallitoff catch error ==" . $error]);
            return array(0, 0, 0);
        }
        echo "autooutboundcallcroncallitoff Cron Ran Successfully.";
        die;
    }

    public function outboundCallToCustomer($leadData, $lead_category = 1, $phone, $lead_source_id) {
        $error = $outbound_entry = "";
        //$status = "FAILURE";
        DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => '1 snapIt log outboundCallToCustomer post data ==' . json_encode($leadData) . "==category=" . $lead_category]);
        try {
            //echo "<pre>";print_r($leadData);die;
            $leadId = isset($leadData['lead_id']) ? $leadData['lead_id'] : 0;
            $leadCall = new LeadCall();
            $called_from = $leadCall->getNumber($lead_source_id, $phone);
            //echo $called_from."<br>";die;
            $call_type = "outbound";
            $outboundcall_parameter = array(
                'lead_id' => $leadId,
                'from_number' => $phone, // 3
                'to_number' => $called_from, // 1
                'transfer_number' => $leadData['forward_number'], // 1
                'transfer_type' => 'call',
                'call_datetime' => date('Y-m-d H:i:s'),
                'user_id' => 0,
                'duration' => 0,
                'recording_url' => '',
                'call_type' => $call_type,
                'call_disposition_id' => 6,
                'legA_call_uuid' => '',
                'call_count' => 1,
                'is_checked_recording' => 'no',
                'campaign_id' => $leadData['campaign_id'],
                'payout' => $leadData['payout'],
                'campaign_score' => $leadData['latest_score'],
                'inbound_type' => "automatic",
                'created_at' => date('Y-m-d H:i:s'),
            );
            $leadCallId = LeadCall::create($outboundcall_parameter)->lead_call_id;
            $this->createOutboundLogs($leadId, $leadCallId, 1, '1 snapIt log outboundCallToCustomer LeadCall Insert Data=' . json_encode($outboundcall_parameter));
            $response = $this->plivoCallCurl($leadId, $phone, $called_from, $leadCallId);
            $decodeData = json_decode($response, true);
            $request_uuid = isset($decodeData['request_uuid']) ? $decodeData['request_uuid'] : '';
            LeadCall::where('lead_call_id', $leadCallId)->update(['legA_call_uuid' => $request_uuid]);
            //echo "<pre>";print_r($decodeData);die;
            $this->createOutboundLogs($leadId, $leadCallId, 2, '2 snapIt log outboundCallToCustomer Response Data=' . $response);
            $outbound_entry = LeadCall::where('lead_call_id', $leadCallId)->get(['lead_call_id', 'lead_id'])->first();
            $outbound_entry->type = 1;
            return $outbound_entry;
        } catch (Exception $e) {
            $error = $e->getMessage();
            //echo $error;die;
            DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 snapIt log outboundCallToCustomer catch  error ==" . $error]);
        }
        //$response = array('status' => $status, 'data' => "Error during call", 'error' => $error);
        //Log::info(json_encode($response));
        return $outbound_entry;
    }

    public function plivoCallCurl($lead_id, $cust_number, $called_from, $leadCallId) {
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $general_variable_2 = Helper::checkServer()['general_variable_2'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
        $plivoBaseURL = 'https://api.plivo.com/v1/Account/' . $decVariable1;
        $url = $plivoBaseURL . '/Call/';
        $country_code = Helper::checkServer()['tok_country_code'];
        $data = array(
            'from' => $called_from,
            'to' => $country_code . $cust_number,
            'answer_url' => $matrixBaseURL_Outbound . '/' . $this->answer . '?lead_id=' . $lead_id . '&lead_phone_number=' . $cust_number . '&lead_call_id=' . $leadCallId . '&auto=1&user_id=0',
            'hangup_url' => $matrixBaseURL_Outbound . '/' . $this->hangup . '?lead_id=' . $lead_id . '&lead_phone_number=' . $cust_number . '&lead_call_id=' . $leadCallId . '&auto=1&user_id=0',
        );
        $data = json_encode($data);
        DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 snapIt log plivoCallCurl request ==" . $data . "===URL=" . $url]);
        //echo "<pre>";print_r($data);die;
        // Call The Customer
        $ch = curl_init($url);
        curl_setopt_array($ch, array(
            CURLOPT_RETURNTRANSFER => TRUE,
            CURLOPT_POSTFIELDS => $data,
            CURLOPT_USERPWD => $decVariable1 . ':' . $decVariable2,
            CURLOPT_FOLLOWLOCATION => 1,
            CURLOPT_HTTPHEADER => array('Content-type: application/json', 'Cache-Control:no-store', 'Content-Length: ' . strlen($data)),
            CURLOPT_TIMEOUT => 40
        ));
        $response = curl_exec($ch);
        DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 snapIt log plivoCallCurl response ==" . $response]);
        //dd($response);
        curl_close($ch);
        return $response;
    }

    public function getAutoOutboundCallHangup(Request $request) {
        $Parameters = $request->all();
        $leadId = $lead_call_id = 0;
        $this->createOutboundLogs($leadId, $lead_call_id, 1, "1 snapIt log getAutoOutboundCallHangup called==" . json_encode($Parameters));
        try {
            $lead_routeval = 0; //Pending now b'coz this configuration not in config table
            $outbound_data = LeadCall::where('legA_call_uuid', $Parameters['RequestUUID'])->get()->toArray();
            if (count($outbound_data) > 0) {
                $leadId = $outbound_data[0]['lead_id'];
                $lead_call_id = $outbound_data[0]['lead_call_id'];
                $autoCalloldBuffer = $outbound_data[0]['auto_old_buffer'];
                $inbound_call_status = $outbound_data[0]['inbound_call_status'];
                $call_disposition_id = $outbound_data[0]['call_disposition_id'];
                CampaignOngoingCall::where('lead_call_id', $lead_call_id)->delete();
                $getLeadPhone = Lead::where('lead_id', $leadId)->get()->toArray();
                $this->createOutboundLogs($leadId, $lead_call_id, 2, "2 snapIt log getAutoOutboundCallHangup LeadCall data found==" . json_encode($outbound_data) . "==DNC==" . json_encode($getLeadPhone));
                $date = date('Y-m-d H:i:s');
                $updateStatus = array();
                $updateStatus['cust_ended_time'] = $date;
                LeadIbCall::where('lead_call_id', $lead_call_id)->update($updateStatus);
                if (count($getLeadPhone) > 0) {
                    $is_dnc_call = $getLeadPhone[0]['is_dnc_call'];
                    if (strtoupper($is_dnc_call) == "NO") {
                        if (strtoupper($autoCalloldBuffer) == "YES") {
                            $is_dnc_sms = $getLeadPhone[0]['is_dnc_sms'];
                            if ($inbound_call_status != "answered" && strtoupper($is_dnc_sms) == "NO" && $call_disposition_id != 2) {
                                $origin_id = $getLeadPhone[0]['lead_source_id'];
                                $custName = $getLeadPhone[0]['name'];
                                $lead_type = ucfirst($getLeadPhone[0]['lead_type']);
                                $country_code = Helper::checkServer()['tok_country_code'];
                                $custPhone = $country_code . $getLeadPhone[0]['phone'];
                                $getSourceName = MstLeadSource::where('lead_source_id', $origin_id)->get()->toArray();
                                if (count($getSourceName) > 0) {
                                    $sourceName = $getSourceName[0]['lead_source_name'];
                                    $pos = strpos($sourceName, 'VM');
                                    $pos1 = strpos($sourceName, 'QTM');
                                    $pos2 = strpos($sourceName, 'IS');
                                    if ($pos !== false) {
                                        $messagetxt = "Hello " . $custName . ", Greetings from Van Lines Move! New availability with top-rated movers is now open for your move at discounted price. CALL now at ******-455-2576 to get Quote.Reply STOP to unsubscribe. Msg & data rates may apply.";
                                        $fromNumber = '7252081224';  //VM & Commio
                                    } else if ($pos1 !== false) {
                                        $messagetxt = "Hello " . $custName . ", Greetings from Quote the Move! We have new deals for your move request to make it an affordable move. Press 1 for a quote. CALL now at ******-455-2577 to get Quote.Reply STOP to unsubscribe. Msg & data rates may apply.";
                                        $fromNumber = '4153290031'; // QTM & commio
                                    } else if ($pos2 !== false) {
                                        $leadMoveInfos = LeadMoving::where('lead_id', $leadId)->get(['to_state'])->toArray();
                                        $destState = "";
                                        if (count($leadMoveInfos) > 0) {
                                            $destState = $leadMoveInfos[0]['to_state'];
                                        }
                                        $messagetxt = "Hello " . $custName . " from Interstate Movers! We just got unbeatable deals for your " . $destState . " Move and have movers on your route ready to save you 30-50%. CALL Now ******-524-2018 to get Quote.Reply STOP to unsubscribe. Msg & data rates may apply.";
                                        $fromNumber = '5615560060'; // ISM & commio
                                    }
                                }
                                //$leadCallObj = new LeadCall();
                                //$fromNumber = $leadCallObj->getNumber($origin_id, $getLeadPhone[0]['phone']);
                                $this->createOutboundLogs($leadId, $lead_call_id, 3, "3 snapIt log getAutoOutboundCallHangup Commio SMS Request== Cust Number=" . $custPhone . "==From Number=" . $fromNumber . "==Message" . $messagetxt . "==Source=" . $sourceName);
                                $smsResponse = CommonFunctions::send_sms_by_comio($custPhone, $fromNumber, $messagetxt);
                                $this->createOutboundLogs($leadId, $lead_call_id, 4, "4 snapIt log getAutoOutboundCallHangup Commio SMS Response== Cust Number=" . $custPhone . "==From Number=" . $fromNumber . "==Message" . $messagetxt . "==Source=" . $sourceName . "==Response==" . json_encode($smsResponse));
                                
                                $smsLogData = array();
                                $smsLogData['from_number'] = $fromNumber;
                                $smsLogData['to_number'] = $custPhone;
                                $smsLogData['lead_type'] = $lead_type;
                                $smsLogData['sms_subject'] = "Old Buffer Lead Auto Outbound Call SMS";
                                $smsLogData['request'] = $messagetxt;
                                $smsLogData['response'] = json_encode($smsResponse);
                                $smsLogData['lead_id'] = $leadId;
                                $smsLogData['status'] = "success";
                                $smsLogData['created_at'] = date('Y-m-d H:i:s');
                                LeadCustomerSmsLog::create($smsLogData); //SMS Log Here By HJ On 10-10-2023 End
                            }
                            $getLeadCallBuffer = LeadOldCallBuffer::where('lead_id', $leadId)->first();
                            if (isset($getLeadCallBuffer)) {
                                $leadactiviyobj = new LeadActivity(); //Remove this line if remove query leadcallbuffer query 
                                $leadactiviyobj->createActivity($leadId, 8);
                            } //Remove this line if remove query leadcallbuffer query
                            LeadOldCallBuffer::where('lead_id', $leadId)->delete(); //Remove this line
                        } else {
                            $date = date('Y-m-d H:i:s');
                            if (date('G') >= 18) {
                                $follow_up_date = date('Y-m-d', strtotime('+1 day', strtotime($date)));
                                $follow_up_time = '10:00:00';
                                $follow_up_date_time = $follow_up_date . " " . $follow_up_time;
                            } else {
                                $follow_up_date_time = date('Y-m-d H:i:s', strtotime('+2 hours', strtotime($date)));
                            }
                            $getLeadCallBuffer = LeadCallBuffer::where('lead_id', $leadId)->first();
                            if (isset($getLeadCallBuffer)) {
                                $leadactiviyobj = new LeadActivity(); //Remove this line if remove query leadcallbuffer query 
                                $leadactiviyobj->createActivity($leadId, 8);
                            } //Remove this line if remove query leadcallbuffer query
                            LeadCallBuffer::where('lead_id', $leadId)->delete(); //Remove this line
                            $buffercontroller = new LeadCallBufferController();
                            $buffercontroller->saveFollowupentryWithDispositions($leadId, $follow_up_date_time, 0);
                        }
                    }
                }
                /* if ($lead_routeval == 1) {
                  if ($outbound_data->digit_pressed == 0) {
                  $is_verifiedLead = Leads::where('id', $outbound_data->lead_id)->get()->first()->is_verified;
                  if ($is_verifiedLead == 1 || $is_verifiedLead == 2) {
                  // DIrect route lead //
                  $outbound_lead_entry = OutboundCalls::where('lead_id', $outbound_data->lead_id)->count();
                  if ($outbound_lead_entry == 1) {
                  Leads::where('id', $outbound_data->lead_id)->update(['lead_handler_id' => 1]);
                  LeadRouteInboundOutbound::where('lead_id', $outbound_data->lead_id)->update(['flag' => 1]);
                  $leadRouteCron = new LeadRouteCron();
                  $matchCompany = $leadRouteCron->reRouteLead($outbound_data->lead_id, 11);
                  }
                  } else {
                  Leads::where('id', $outbound_data->lead_id)->update(['lead_handler_id' => NULL]);
                  LeadRouteInboundOutbound::where('lead_id', $outbound_data->lead_id)->update(['flag' => 0]);
                  }
                  }
                  } */
            } else {
                $this->createOutboundLogs($leadId, $lead_call_id, 5, "5 snapIt log getAutoOutboundCallHangup LeadCall data not found==" . json_encode($Parameters));
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
            $this->createOutboundLogs($leadId, $lead_call_id, 6, "6 snapIt log getAutoOutboundCallHangup error catched==" . $error . "==Postdata=" . json_encode($Parameters));
        }
        $content = '<Response>
            <Speak>Thank you</Speak>
        </Response>';
        header("Content-type: text/xml");
        echo $content;
        exit;
    }

    public function getAutoOutboundCallAnswer(Request $request) {
        $Parameters = $request->all();
        $leadId = $lead_call_id = 0;
        $this->createOutboundLogs($leadId, $lead_call_id, 1, "1 snapIt log getAutoOutboundCallAnswer called==" . json_encode($Parameters));
        try {

            $lead_call_id = isset($Parameters['lead_call_id']) ? $Parameters['lead_call_id'] : 0;
            $leadId = isset($Parameters['lead_id']) ? $Parameters['lead_id'] : 0;
            $user_id = isset($Parameters['user_id']) ? $Parameters['user_id'] : 0;
            if ($leadId > 0) {
                $OutboundCallsData = LeadCall::where('lead_id', $leadId)->where('legA_call_uuid', $Parameters['RequestUUID'])->orderBy('lead_call_id', 'desc')->get()->toArray();
            } else {
                $OutboundCallsData = LeadCall::where('legA_call_uuid', $Parameters['RequestUUID'])->orderBy('lead_call_id', 'desc')->get()->toArray();
            }
            if (count($OutboundCallsData) > 0) {
                $lead_call_id = $OutboundCallsData[0]['lead_call_id'];
                $leadId = $OutboundCallsData[0]['lead_id'];
                $autoCalloldBuffer = $OutboundCallsData[0]['auto_old_buffer'];
                $this->createOutboundLogs($leadId, $lead_call_id, 2, "2 snapIt log getAutoOutboundCallAnswer LeadCall data found==" . json_encode($OutboundCallsData));
                $current_lead = Lead::where('lead_id', $leadId)->get()->toArray();
                $origin_id = 19;
                if (count($current_lead) > 0) {
                    $origin_id = $current_lead[0]['lead_source_id'];
                }
                $leadCallObj = new LeadCall();
                if (strtoupper($autoCalloldBuffer) == "YES") {
                    $loop = 3;
                    $digitValid = "129";
                    $voicepromtfilename = $leadCallObj->getOldVoicePromt($origin_id);
                } else {
                    $loop = 1;
                    $digitValid = "1209";
                    $voicepromtfilename = $leadCallObj->getVoicePromt($origin_id);
                }
                $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
                //LeadCall::where('lead_call_id', $lead_call_id)->update(['legA_call_uuid' => $Parameters['CallUUID']]);
                $content = '<Response>
                <GetDigits action="' . $matrixBaseURL_Outbound . '/' . $this->digit_select . '?outbound_id=' . $lead_call_id . '" method="POST" timeout="30" numDigits="1" validDigits="' . $digitValid . '">
                    <Play>' . $matrixBaseURL_Outbound . '/mp3/additional_2.mp3</Play>
                    <Play loop="' . $loop . '">' . $matrixBaseURL_Outbound . '/mp3/' . $voicepromtfilename . '</Play>
                </GetDigits>
                </Response>';
                $this->createOutboundLogs($leadId, $lead_call_id, 3, "3 snapIt log getAutoOutboundCallAnswer XML==" . $content);
                header("Content-type: text/xml");
                echo $content;
                exit;
            } else {
                $this->createOutboundLogs($leadId, $lead_call_id, 4, "4 snapIt log getAutoOutboundCallAnswer LeadCall data not found==" . json_encode($Parameters));
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
            $this->createOutboundLogs($leadId, $lead_call_id, 5, "5 snapIt log getAutoOutboundCallAnswer error catched==" . $error . "==Postdata=" . json_encode($Parameters));
        }
        $content = '<Response>
            <Speak>Thank you</Speak>
          </Response>';
        header("Content-type: text/xml");
        echo $content;
        exit;
    }

    public function getAutoOutboundDigitSelect(Request $request) {
        $parameters = $request->all();
        $lead_call_id = $leadId = 0;
        $this->createOutboundLogs($leadId, $lead_call_id, 1, "1 snapIt log getAutoOutboundDigitSelect ==" . json_encode($parameters));
        try {
            $leadactiviyobj = new LeadActivity();
            $leadListControllerobj = new LeadListController();
            $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
            if (isset($parameters['outbound_id']) && trim($parameters['outbound_id']) > 0) {
                $lead_call_id = trim($parameters['outbound_id']);
            }
            $leadIdEmailArr = [];
            $outbound_data = LeadCall::where('lead_call_id', $lead_call_id)->orderBy('lead_call_id', 'desc')->get()->toArray();
            if (count($outbound_data) > 0) {
                $leadId = $outbound_data[0]['lead_id'];
                $autoCalloldBuffer = $outbound_data[0]['auto_old_buffer'];
                // Check if Digits and CallUUID is set or not
                if ((!isset($parameters['Digits']) ) || (!isset($parameters['CallUUID']) )) {
                    $this->createOutboundLogs($leadId, $lead_call_id, 1, "1 snapIt log getAutoOutboundDigitSelect Digits Or CallUUID not found ==" . json_encode($parameters));
                    exit;
                }
                // Upgrade Data in Table
                $digit_pressed = ((int) ($parameters['Digits']));
                $callUuid = $parameters['CallUUID'];
                $lead_routeval = $balance = 0; //Pending now b'coz this configuration not in config table
                if ($digit_pressed == 1 || $digit_pressed == '1') {
                    if (strtoupper($autoCalloldBuffer) == "YES") {
                        LeadCall::where('lead_call_id', $lead_call_id)->update(['inbound_call_status' => 'answered','transfer_type' => 'transfer', 'call_disposition_id' => ((int) ($parameters['Digits']))]);
                    }else{
                        LeadCall::where('lead_call_id', $lead_call_id)->update(['transfer_type' => 'transfer', 'call_disposition_id' => ((int) ($parameters['Digits']))]);
                    }
                    
                    if (trim($callUuid) != "") {
                        $this->CallRecord($callUuid);
                        if ($outbound_data[0]['campaign_id'] > 0 && $leadId > 0 && $lead_call_id > 0) {
                            $call_track = ['lead_id' => $leadId, 'campaign_id' => $outbound_data[0]['campaign_id'], 'lead_call_id' => $lead_call_id, 'created_at' => date('Y-m-d H:i:s')];
                            $this->createOutboundLogs($leadId, $lead_call_id, 2, "2 snapIt log getAutoOutboundDigitSelect CampaignOngoingCall data=" . json_encode($call_track));
                            CampaignOngoingCall::create($call_track);
                        }
                    }
                    /* Call forward to business comaigpn if press 1. */
                    $new_payout = $this->checkUpdateFreeLead($leadId, $outbound_data[0]['campaign_id'], $outbound_data[0]['payout'], 0, $lead_call_id);
                    $idFreeLead = "yes";
                    if ($new_payout > 0) {
                        $idFreeLead = "no";
                    }
                    $current_lead = Lead::where('lead_id', $leadId)->get()->toArray();
                    $categoryId = 1;
                    if (count($current_lead) > 0) {
                        $categoryId = $current_lead[0]['lead_category_id'];
                    }
                    $live_transfer = array(
                        'lead_id' => $leadId,
                        'campaign_id' => $outbound_data[0]['campaign_id'],
                        'lead_call_id' => $lead_call_id,
                        'calls_type' => "outbound",
                        'call_uuid' => $callUuid,
                        'duration' => '',
                        'recording_url' => '',
                        'payout' => $new_payout,
                        'is_checked_rec' => "no",
                        'transfer_type' => "transfer",
                        'transfer_by' => 0,
                        'lead_category_id' => $categoryId,
                        'created_at' => date("Y-m-d H:i:s")
                    );
                    LeadTransferCall::create($live_transfer);
                    $this->createOutboundLogs($leadId, $lead_call_id, 3, "3 snapIt log getAutoOutboundDigitSelect Live transfer entry." . json_encode($live_transfer));
                    $Lead_Routedata = [
                        'lead_id' => $leadId,
                        'campaign_id' => $outbound_data[0]['campaign_id'],
                        'payout' => $new_payout,
                        'score' => $outbound_data[0]['campaign_score'],
                        'route_status' => "sold",
                        'is_free_lead' => $idFreeLead,
                        'created_at' => date("Y-m-d H:i:s")
                    ];
                    //echo "<pre>";print_r($Lead_Routedata);die;
                    $lead_routing_id = LeadRouting::create($Lead_Routedata)->lead_routing_id;
                    CommonFunctions::insertLeadCallRoutingData($leadId, $lead_call_id, $lead_routing_id); //Added By HJ On 25-07-2023 For Insert Data Into New Table lead_call_routing
                    $this->createOutboundLogs($leadId, $lead_call_id, 4, "4 snapIt log getAutoOutboundDigitSelect Lead Route entry :" . json_encode($Lead_Routedata));
                    $lead_payout = LeadRouting::where('route_status', 'sold')->where('lead_id', $leadId)->get()->sum("payout");
                    if ($lead_payout) {
                        $totalPayout = $lead_payout;
                        Lead::where('lead_id', $leadId)->update(['payout' => $totalPayout]);
                    }
                    if ($lead_routeval == 1) {
                        Lead::where('lead_id', $leadId)->update(['is_handled' => 1]);
                        //LeadRouteInboundOutbound::where('lead_id', $leadId)->update(['flag' => 1]);
                    }
                    $credit_find = Campaign::where('campaign_id', $outbound_data[0]['campaign_id'])->first();
                    $balance_after_charge = $credit_find->credit_available;
                    if ($credit_find->payment_type == 0 || $credit_find->payment_type == '') {
                        $credit_bus_find = Business::where('business_id', $credit_find->business_id)->first();
                        $balance_after_charge = $credit_bus_find->credit_available;
                    }
                    if ($new_payout > 0) {
                        if ($credit_find->payment_type == 0 || $credit_find->payment_type == '') {
                            $this->createOutboundLogs($leadId, $lead_call_id, 5, "5 snapIt log getAutoOutboundDigitSelect Business Before credit_available: $ :" . $credit_bus_find->credit_available);
                            $balance = $credit_bus_find->credit_available;
                            $update_credit_bus_available = $balance_after_charge = ($credit_bus_find->credit_available - $new_payout);
                            $credit_bus_find->update(['credit_available' => $update_credit_bus_available]);
                            $this->createOutboundLogs($leadId, $lead_call_id, 6, "6 snapIt log getAutoOutboundDigitSelect Business After credit_available: $ :" . ($update_credit_bus_available));
                        } else {
                            $this->createOutboundLogs($leadId, $lead_call_id, 7, "7 snapIt log getAutoOutboundDigitSelect Campaign Before credit_available: $ :" . $credit_find->credit_available);
                            $balance = $credit_find->credit_available;
                            $update_credit_available = $balance_after_charge = ($credit_find->credit_available - $new_payout);
                            $credit_find->update(['credit_available' => $update_credit_available]);
                            $this->createOutboundLogs($leadId, $lead_call_id, 8, "8 snapIt log getAutoOutboundDigitSelect Campaign After credit_available: $ :" . ($update_credit_available));
                        }
                    } else {
                        $this->createOutboundLogs($leadId, $lead_call_id, 9, "9 snapIt log getAutoOutboundDigitSelect auto outbound phone lead Free call applied: camp Id :" . $outbound_data[0]['campaign_id'] . "===payout=" . $new_payout);
                    }
                    LeadRouting::where('lead_routing_id', $lead_routing_id)->update(['balance_after_charge' => $balance_after_charge, 'payment_type' => $credit_find->payment_type]); //Added By HJ On 28-07-2023 For Insert Business Or Campaign Credit and Payment Type After Call Transfer
                    $leadDeliverApi = new LeadDeliveryApi();
                    $jsonArray = array(
                        "campaign_id" => $outbound_data[0]['campaign_id'],
                        "lead_id" => $leadId,
                        "cost" => $new_payout,
                        "balance" => $balance,
                        "score" => $outbound_data[0]['campaign_score']
                    );
                    $this->createOutboundLogs($leadId, $lead_call_id, 10, "10 snapIt log getAutoOutboundDigitSelect leadDeliveryTemplate request data " . json_encode($jsonArray));
                    $leadDeliverApi->leadDeliveryTemplate(json_encode($jsonArray));
                    /* $bufferController = new CallsbufferController();
                      $bufferController->SaveToBufferViaTweet($leadId); */
                    $this->createOutboundLogs($leadId, $lead_call_id, 11, "11 snapIt log getAutoOutboundDigitSelect auto outbound call applied: camp Id :" . $outbound_data[0]['campaign_id'] . "===transfer_number=" . $outbound_data[0]['transfer_number']);
                    $this->callforwardtomain($outbound_data[0]['transfer_number'], $outbound_data[0]['from_number'], $lead_call_id);
                }
                if ($digit_pressed == 2 || $digit_pressed == '2') {
                    if (trim($callUuid) != "") {
                        $this->CallRecord($callUuid);
                    }
                    $this->createOutboundLogs($leadId, $lead_call_id, 12, "12 snapIt log getAutoOutboundDigitSelect Digit pressed 2 :" . $outbound_data[0]['campaign_id'] . "===transfer_number=" . $outbound_data[0]['transfer_number']);
                    /* Do not call next time */

                    $getLeadPhone = Lead::where('lead_id', $leadId)->get()->toArray();
                    if (count($getLeadPhone) > 0) {
                        $phone = $getLeadPhone[0]['phone'];
                        $lead_category = $getLeadPhone[0]['lead_category_id'];
                        $start_date = date('Y-m-d', strtotime("-90 days")) . ' 00:00:00';
                        $end_date = date('Y-m-d 23:59:59');
                        if (strtoupper($autoCalloldBuffer) == "YES") {
                            $getLeadCallBuffer = LeadOldCallBuffer::where('customer_number', $phone)->first();
                        } else {
                            $getLeadCallBuffer = LeadCallBuffer::where('customer_number', $phone)->first();
                        }
                        if (isset($getLeadCallBuffer)) {
                            $leadactiviyobj->createActivity($leadId, 8);
                        }
                        LeadCallBuffer::where('customer_number', $phone)->delete(); //Remove this line
                        $getLeadFollowUp = LeadFollowUp::where('cust_phone_number', $phone)->first();
                        if (isset($getLeadFollowUp)) {
                            $leadactiviyobj->createActivity($leadId, 13);
                        }
                        LeadFollowUp::where('cust_phone_number', $phone)->delete(); // No entry into follow-up.
                        Lead::where('phone', $phone)->where('book_type_id', '>', 1)->whereBetween('lead_generated_at', [$start_date, $end_date])->update(['is_dnc_call' => 'yes', 'is_dnc_sms' => 'yes']); // If booked No entry into follow-up. Mark as DNC all leads with that phone number.
                        $getleads = Lead::where('phone', $phone)->where('book_type_id', '>', 1)->whereBetween('lead_generated_at', [$start_date, $end_date])->get()->toArray();
                        for ($d = 0; $d < count($getleads); $d++) {
                            // $leadListControllerobj->dncemailsend($getleads[$d]['lead_id'], "yes", "yes", "yes");
                            $leadIdEmailArr[] = $getleads[$d]['lead_id'];
                        }
                        Lead::where('phone', $phone)->where('book_type_id', 1)->where('lead_category_id', $lead_category)->whereBetween('lead_generated_at', [$start_date, $end_date])->update(['is_dnc_call' => 'yes', 'is_dnc_sms' => 'yes']); // Not booked No entry into follow-up. Mark as DNC all leads across the category with that phone number.
                        $getleadsbooked = Lead::where('phone', $phone)->where('book_type_id', 1)->where('lead_category_id', $lead_category)->whereBetween('lead_generated_at', [$start_date, $end_date])->get()->toArray();
                        for ($d = 0; $d < count($getleadsbooked); $d++) {
                            // $leadListControllerobj->dncemailsend($getleadsbooked[$d]['lead_id'], "yes", "yes", "yes");
                            $leadIdEmailArr[] = $getleadsbooked[$d]['lead_id'];
                        }
                    }
                    if ($lead_routeval == 1) {
                        Lead::where('lead_id', $leadId)->update(['is_handled' => NULL]);
                        //LeadRouteInboundOutbound::where('lead_id', $leadId)->update(['flag' => 0]);
                    }
                    if (strtoupper($autoCalloldBuffer) == "YES") {
                        LeadCall::where('lead_call_id', $lead_call_id)->update(['inbound_call_status' => 'answered','call_disposition_id' => ((int) ($parameters['Digits']))]);
                    }else{
                        LeadCall::where('lead_call_id', $lead_call_id)->update(['call_disposition_id' => ((int) ($parameters['Digits']))]);
                    }
                    
                    if (strtoupper($autoCalloldBuffer) == "YES") {
                        $getLeadCallBuffer = LeadOldCallBuffer::where('lead_id', $leadId)->first();
                    } else {
                        $getLeadCallBuffer = LeadCallBuffer::where('lead_id', $leadId)->first();
                    }
                    if (isset($getLeadCallBuffer)) {
                        $leadactiviyobj->createActivity($leadId, 8);
                    }
                    if (strtoupper($autoCalloldBuffer) == "YES") {
                        LeadOldCallBuffer::where('lead_id', $leadId)->delete();
                    } else {
                        LeadCallBuffer::where('lead_id', $leadId)->delete(); //Remove this line
                    }
                    $content = '<Response>
                        <Speak>Thank you</Speak>
                    </Response>';
                    header("Content-type: text/xml");
                    echo $content;
                    exit;
                }
                if ($digit_pressed == 9 || $digit_pressed == '9') {
                    $this->createOutboundLogs($leadId, $lead_call_id, 13, "13 snapIt log getAutoOutboundDigitSelect Digit pressed 9 :" . $outbound_data[0]['campaign_id'] . "===transfer_number=" . $outbound_data[0]['transfer_number']);
                    if ($lead_routeval == 1) {
                        Lead::where('lead_id', $leadId)->update(['is_handled' => NULL]);
                        //LeadRouteInboundOutbound::where('lead_id', $leadId)->update(['flag' => 0]);
                    }
                    /* Voice prompt play again if press 0. */
                    $current_lead = Lead::where('lead_id', $leadId)->get()->toArray();
                    $origin_id = 19;
                    if (count($current_lead) > 0) {
                        $origin_id = $current_lead[0]['lead_source_id'];
                    }
                    $leadCallObj = new LeadCall();
                    if (strtoupper($autoCalloldBuffer) == "YES") {
                        $loop = 3;
                        $digitValid = "129";
                        $voicepromtfilename = $leadCallObj->getOldVoicePromt($origin_id);
                    } else {
                        $loop = 1;
                        $digitValid = "1209";
                        $voicepromtfilename = $leadCallObj->getVoicePromt($origin_id);
                    }
                    $content = '<Response>
                                        <GetDigits action="' . $matrixBaseURL_Outbound . '/' . $this->digit_select . '?outbound_id=' . $lead_call_id . '" method="POST" timeout="30" numDigits="1" validDigits="' . $digitValid . '">
                                             <Play>' . $matrixBaseURL_Outbound . '/mp3/additional_2.mp3</Play>
                                             <Play loop="' . $loop . '">' . $matrixBaseURL_Outbound . '/mp3/' . $voicepromtfilename . '</Play>
                                        </GetDigits>
                                    </Response>';
                    $this->createOutboundLogs($leadId, $lead_call_id, 14, "14 snapIt log getAutoOutboundDigitSelect XML==" . $content);
                    header("Content-type: text/xml");
                    echo $content;
                    exit;
                }
                if ($lead_routeval == 1) {
                    Lead::where('lead_id', $leadId)->update(['is_handled' => NULL]);
                    //LeadRouteInboundOutbound::where('lead_id', $leadId)->update(['flag' => 0]);
                }
                if (count($leadIdEmailArr) > 0) {
                    $leadIdEmailArr = array_unique($leadIdEmailArr);
                    foreach ($leadIdEmailArr as $key => $value) {
                        $leadListControllerobj->dncemailsend($value, "yes", "yes", "yes");
                    }
                }
            } else {
                $this->createOutboundLogs($leadId, $lead_call_id, 1, "1 snapIt log getAutoOutboundDigitSelect LeadCall data not found==" . json_encode($parameters));
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
            $this->createOutboundLogs($leadId, $lead_call_id, 2, "2 snapIt log getAutoOutboundDigitSelect error catched==" . $error . "==Postdata=" . json_encode($parameters));
        }
        $content = '<Response>
            <Speak>Thank you</Speak>
        </Response>';
        header("Content-type: text/xml");
        echo $content;
        exit;
    }

    public function oldBufferAutoOutboundCallCron(Request $request) {
        $leadId = $lead_call_id = $from_zipcode = $to_zipcode = 0;
        try {
            $hours_check_cron = date('G');
            if ($hours_check_cron < 11 || $hours_check_cron >= 20) {
                //DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 snapIt log oldBufferAutoOutboundCallCron Night time ==" . date("Y-m-d H:i:s")]);
                echo 'Night time.<br>';
                exit; //Remove this line
            }
            //$leadactiviyobj = new LeadActivity();
            $calitoutbound = MstCallConfig::where('call_config', 'oldbufferautooutbound')->where('status', 'active')->get()->toArray();
            //echo "<pre>";print_r($calitoutbound);die;
            //DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 snapIt log oldBufferAutoOutboundCallCron Config ==" . json_encode($calitoutbound)]);
            if (count($calitoutbound) > 0) {
                $getAllLeads = LeadOldCallBuffer::with(['leads', 'routingInfo'])->where('lead_category_id', 1)->where('dialed', 'no')->orderBy('lead_old_call_buffer_id', 'DESC')->get()->toArray();
                $all = $leadIdArr = $attemptcheck = $dncNumberArr = $checkedLeadIdArr = array();
                for ($d = 0; $d < count($getAllLeads); $d++) {
                    $leadIdArr[] = $getAllLeads[$d]['lead_id'];
                }
                //echo "<pre>";print_r($leadIdArr);die;
                if (count($leadIdArr) > 0) {
                    $dncListData = Lead::where('is_dnc_call', 'yes')->whereIn('lead_id', $leadIdArr)->get()->toArray();
                    $dncNumberArr = array();
                    for ($x = 0; $x < count($dncListData); $x++) {
                        $dncNumberArr[] = $dncListData[$x]['phone'];
                    }
                }
                $dialCallCount = 0;
                $leadCallCriteArea = new LeadCall();
                // LeadOldCallBuffer::whereIn('lead_id', $leadIdArr)->update(['dialed' => 'yes']);
                //echo "<pre>";print_r($getAllLeads);die;
                for ($g = 0; $g < count($getAllLeads); $g++) {
                    $leadvalue = $getAllLeads[$g]['leads'];
                    $lead_id = $getAllLeads[$g]['lead_id'];
                    $lead_category = $leadvalue['lead_category_id'];
                    $book_type_id = $leadvalue['book_type_id'];
                    $lead_source_id = $leadvalue['lead_source_id'];
                    $lead_type = $leadvalue['lead_type'];
                    $remaining_slot = $leadvalue['remaining_slot'];
                    $phone = $leadvalue['phone'];

                    if (!in_array($getAllLeads[$g]['customer_number'], $dncNumberArr) && $dialCallCount < 5) {
                        $lead_dialer = 0;
                        if ($leadvalue['sold_type'] == "exclusive") {
                            $lead_dialer = 1;
                        }
                        $checkedLeadIdArr[] = $lead_id;
                        $lead_routing_exist_check = $attemptcheck = array();
                        $routing_info = $getAllLeads[$g]['routing_info'];
                        for ($f = 0; $f < count($routing_info); $f++) {
                            $lead_routing_exist_check[] = $routing_info[$f]['campaign_id'];
                        }
                        $new_lead_own = array();
                        if ($lead_dialer > 0) {
                            //echo "1<br>";
                            $businesses = $leadCallCriteArea->getBusinesses($lead_routing_exist_check, $lead_id, $lead_dialer, "inactive", 0);
                            $new_lead_own = $leadCallCriteArea->getBusinessesCriteria($businesses, $lead_id, $remaining_slot, 0, $lead_dialer, 0, 'inactive', 0, $lead_category);
                        }
                        if (count($new_lead_own) == 0) {
                            $businesses = $leadCallCriteArea->getBusinesses($lead_routing_exist_check, $lead_id, $lead_dialer, "active", 0);
                            $new_lead_own = $leadCallCriteArea->getBusinessesCriteria($businesses, $lead_id, $remaining_slot, 0, $lead_dialer, 0, "active", 0, $lead_category);
                        }
                        //echo "<pre>";print_r($new_lead_own);die;
                        //DB::table('dev_debug')->insert(['sr_no' => 7, 'result' => "7 snapIt log oldBufferAutoOutboundCallCron Campaign Data 2 ==" . json_encode($new_lead_own) . "===" . $lead_dialer . "==" . $remaining_slot]);
                        if (count($new_lead_own) > 0) {
                            //$all[] = $new_lead_own[0];
                            //echo $lead_source_id."==".$lead_category.'<br>';
                            //echo "Called to==" . $phone . "==lead Id==" . $lead_id . "===" . $dialCallCount . "<br>";die;
                            $dialCallCount += 1;
                            $from_zipcode = isset($new_lead_own[0]['lead_data'][0]['from_zipcode']) ? $new_lead_own[0]['lead_data'][0]['from_zipcode'] : 0;
                            $to_zipcode = isset($new_lead_own[0]['lead_data'][0]['to_zipcode']) ? $new_lead_own[0]['lead_data'][0]['to_zipcode'] : 0;
                            if ((isset($from_zipcode) && $from_zipcode !== 0) && (isset($to_zipcode) && $to_zipcode !== 0)) {
                                $this->oldOutboundCallToCustomer($new_lead_own[0], $lead_category, $phone, $lead_source_id);
                            } else {
                                echo "Not Call";
                            }
                            
                            //LeadCallBuffer::where('lead_id', $lead_id)->where('lead_category_id', $lead_category)->delete();
                            // echo "<pre>";print_r($new_lead_own);die;
                        } else {
                            //DB::table('dev_debug')->insert(['sr_no' => 8, 'result' => "8 snapIt log oldBufferAutoOutboundCallCron Campaing not found ==" . json_encode($getAllLeads[$g])]);
                        }
                    } else {
                        //DB::table('dev_debug')->insert(['sr_no' => 9, 'result' => "9 snapIt log oldBufferAutoOutboundCallCron Dial count over ==" . $dialCallCount . "==Lead Id==" . $lead_id . "==Phone DNC--" . json_encode($getAllLeads[$g]) . "===Lead Type==" . $lead_type]);
                    }
                }
                if(count($checkedLeadIdArr) > 0){
                    LeadOldCallBuffer::whereIn('lead_id', $checkedLeadIdArr)->update(['dialed' => 'yes', 'updated_at' => date('Y-m-d H:i:s')]);
                }
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
            //DB::table('dev_debug')->insert(['sr_no' => 10, 'result' => "10 snapIt log oldBufferAutoOutboundCallCron Catch error ==" . $error. "===Time==" . date("Y-m-d H:i:s")]);
            echo $error;
            die;
        }
    }

    public function oldOutboundCallToCustomer($leadData, $lead_category = 1, $phone, $lead_source_id) {
        $error = $outbound_entry = "";
        //$status = "FAILURE";
        //DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => '1 snapIt log outboundCallToCustomer post data ==' . json_encode($leadData) . "==category=" . $lead_category]);
        try {
            //echo "<pre>";print_r($leadData);die;
            $leadId = isset($leadData['lead_id']) ? $leadData['lead_id'] : 0;
            $leadCall = new LeadCall();
            $called_from = $leadCall->getNumber($lead_source_id, $phone);
            //echo $called_from."<br>";die;
            $call_type = "outbound";
            $outboundcall_parameter = array(
                'lead_id' => $leadId,
                'from_number' => $phone, // 3
                'to_number' => $called_from, // 1
                'transfer_number' => $leadData['forward_number'], // 1
                'transfer_type' => 'call',
                'call_datetime' => date('Y-m-d H:i:s'),
                'user_id' => 0,
                'duration' => 0,
                'recording_url' => '',
                'call_type' => $call_type,
                'call_disposition_id' => 9,
                'legA_call_uuid' => '',
                'call_count' => 1,
                'is_checked_recording' => 'no',
                'campaign_id' => $leadData['campaign_id'],
                'payout' => $leadData['payout'],
                'campaign_score' => $leadData['latest_score'],
                'inbound_call_status' => "not_answered",
                'inbound_type' => "automatic",
                'auto_old_buffer' => "yes",
                'created_at' => date('Y-m-d H:i:s'),
            );
            $leadCallId = LeadCall::create($outboundcall_parameter)->lead_call_id;
            //$leadCallId = 1626;
            //$this->createOutboundLogs($leadId, $leadCallId, 1, '1 snapIt log outboundCallToCustomer LeadCall Insert Data=' . json_encode($outboundcall_parameter));
            $response = $this->oldPlivoCallCurl($leadId, $phone, $called_from, $leadCallId);
            $decodeData = json_decode($response, true);
            $request_uuid = isset($decodeData['request_uuid']) ? $decodeData['request_uuid'] : '';
            LeadCall::where('lead_call_id', $leadCallId)->update(['legA_call_uuid' => $request_uuid]);
            //echo "<pre>";print_r($decodeData);die;
            $leadactiviyobj = new LeadActivity();
            $leadactiviyobj->createActivity($leadId, 20);
            LeadOldCallBuffer::where('lead_id', $leadId)->delete();
            $this->createOutboundLogs($leadId, $leadCallId, 2, '2 snapIt log outboundCallToCustomer Response Data=' . $response);
            $outbound_entry = LeadCall::where('lead_call_id', $leadCallId)->get(['lead_call_id', 'lead_id'])->first();
            $outbound_entry->type = 1;
            return $outbound_entry;
        } catch (Exception $e) {
            $error = $e->getMessage();
            //DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 snapIt log outboundCallToCustomer catch  error ==" . $error]);
        }
        return $outbound_entry;
    }

    public function oldPlivoCallCurl($lead_id, $cust_number, $called_from, $leadCallId) {
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $general_variable_2 = Helper::checkServer()['general_variable_2'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
        $plivoBaseURL = 'https://api.plivo.com/v1/Account/' . $decVariable1;
        $url = $plivoBaseURL . '/Call/';
        $country_code = Helper::checkServer()['tok_country_code'];
        $data = array(
            'from' => $called_from,
            'to' => $country_code . $cust_number,
            'answer_url' => $matrixBaseURL_Outbound . '/' . $this->answer . '?lead_id=' . $lead_id . '&lead_phone_number=' . $cust_number . '&lead_call_id=' . $leadCallId . '&autocall=1',
            'hangup_url' => $matrixBaseURL_Outbound . '/' . $this->hangup . '?lead_id=' . $lead_id . '&lead_phone_number=' . $cust_number . '&lead_call_id=' . $leadCallId . '&autocall=1',
        );
        $callData = json_encode($data);
        //$this->createOutboundLogs($lead_id, $leadCallId, 1, "1 snapIt log oldPlivoCallCurl request=" . $callData . "===URL=" . $url);
        //echo "<pre>";print_r($callData);die;
        // Call The Customer
        $ch = curl_init($url);
        curl_setopt_array($ch, array(
            CURLOPT_RETURNTRANSFER => TRUE,
            CURLOPT_POSTFIELDS => $callData,
            CURLOPT_USERPWD => $decVariable1 . ':' . $decVariable2,
            CURLOPT_FOLLOWLOCATION => 1,
            CURLOPT_HTTPHEADER => array('Content-type: application/json', 'Cache-Control:no-store', 'Content-Length: ' . strlen($callData)),
            CURLOPT_TIMEOUT => 40
        ));
        $response = curl_exec($ch);
        $this->createOutboundLogs($lead_id, $leadCallId, 2, "2 snapIt log oldPlivoCallCurl request=" . $response."==".$callData . "===URL=" . $url);
        curl_close($ch);
        return $response;
    }

}
