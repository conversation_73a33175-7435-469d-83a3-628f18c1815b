<?php

namespace App\Http\Controllers\Report;

use App\Http\Controllers\Controller;
use App\Models\GoogleAdwords\GoogleAdwordsCampaignCost;
use App\Models\BingAds\BingAdsCampaignCost;
use App\Models\Lead\Lead;
use App\Models\Seller\PPPost;
use App\Models\Seller\PPSeller;
use App\Models\Lead\LeadTransferCall;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

class PerfomanceAllController extends Controller
{
    function __construct()
    {
        $this->middleware('permission:perfomancereport', ['only' => ['perfomanceReport']]);
    }

    public function perfomanceReportAllData()
    {
        $googlecost_datetime = GoogleAdwordsCampaignCost::orderby('google_adwards_campaign_cost_id', 'desc')->first();
        $bingcost_datetime = BingAdsCampaignCost::select('created_at')->orderby('bing_ads_campaign_cost_id', 'desc')->first();
        if ($googlecost_datetime) {
            $googlecost_datetime = $googlecost_datetime->created_at;
        } else {
            $googlecost_datetime = null;
        }
        if ($bingcost_datetime) {
            $bingcost_datetime = $bingcost_datetime->created_at;
        } else {
            $bingcost_datetime = null;
        }
        return view("reports.perfomanceall")->with('googlecost_datetime', $googlecost_datetime)->with('bingcost_datetime', $bingcost_datetime);
    }

    public function perfomanceReportFilterAllData(Request $request)
    {
        $post = $request->all();

        $castrevenuecheckboxvalue = $request->revenuecheckboxvalue === "true" ? true : false;
        $castleadcheckboxvalue = $request->leadcheckboxvalue === "true" ? true : false;
        $leadStartDate = date('Y-m-d 00:00:00', strtotime($request->valStartDate));
        $leadEndDate = date('Y-m-d 23:59:59', strtotime($request->valEndDate));
        $revenueStartDate = date('Y-m-d 00:00:00', strtotime($request->revenueStartDate));
        $revenueEndDate = date('Y-m-d 23:59:59', strtotime($request->revenueEndDate));

        $when_category = ($post['category'] != 'alldata') ? true : false;
        $category_id = $when_category ? $post['category'] : 0;
        $sourceIdArr = $sourceIdLocalArr = $sourceIdLongArr = [];
        $totalLeads = $totalPayout = $totalAvgPayout = $totalLocal = $totalLocalV = $totalLong = $totalLongV = $totalLocalUnsold = $totalLongUnsold = $totalClicks = $totalCost = $totalCpa = $dataFound = $totalLiveTransfer = $totalLocalLiveTransfer = $totalLongLiveTransfer = 0;

        $append_category = "";
        if ($when_category) {
            $append_category = " AND `lead`.lead_category_id = $category_id";
        }

        $whereDateQuery = '';
        if ($castrevenuecheckboxvalue === true) {
            $whereDateQuery = "lr.created_at BETWEEN '$revenueStartDate' AND '$revenueEndDate'";
        } else if ($castleadcheckboxvalue === true) {
            $whereDateQuery = "`lead`.created_at BETWEEN '$leadStartDate' AND '$leadEndDate'";
        } else if ($castrevenuecheckboxvalue === true && $castleadcheckboxvalue === true) {
            $whereDateQuery = "lr.created_at BETWEEN '$revenueStartDate' AND '$revenueEndDate' AND `lead`.created_at BETWEEN '$leadStartDate' AND '$leadEndDate'";
        }

        $leadOrigin = DB::select(
            "SELECT
                `lead`.lead_source_id AS id,
                o.lead_source_name AS name,
                COUNT(DISTINCT `lead`.lead_id) AS Id,
                IFNULL(SUM(CASE WHEN lr.route_status='sold' THEN lr.payout END), 0) AS Payout,
                COUNT(DISTINCT CASE WHEN lm.lm_local_long='local' OR lhl.lhl_local_long='local' OR lct.lct_local_long='local' OR ljr.lead_id IS NOT NULL THEN `lead`.lead_id ELSE NULL END) AS state_local,
                COUNT(DISTINCT CASE WHEN lm.lm_local_long='long' OR lhl.lhl_local_long='long' OR lct.lct_local_long='long' THEN `lead`.lead_id ELSE NULL END) AS state_long,
                COUNT(DISTINCT CASE WHEN (lm.lm_local_long='local' OR lhl.lhl_local_long='local' OR lct.lct_local_long='local' OR ljr.lead_id IS NOT NULL) AND `lead`.is_verified = 'yes' THEN `lead`.lead_id ELSE NULL END) AS local_verified,
                COUNT(DISTINCT CASE WHEN (lm.lm_local_long='long' OR lhl.lhl_local_long='long' OR lct.lct_local_long='long') AND `lead`.is_verified = 'yes' THEN `lead`.lead_id ELSE NULL END) AS long_verified,
                COUNT(DISTINCT CASE WHEN (lm.lm_local_long='local' OR lhl.lhl_local_long='local' OR lct.lct_local_long='local' OR ljr.lead_id IS NOT NULL) AND (NOT EXISTS (SELECT lead_id FROM lead_routing WHERE `lead`.lead_id = lead_routing.lead_id AND route_status='sold')) THEN `lead`.lead_id ELSE NULL END) AS local_unsold,
                COUNT(DISTINCT CASE WHEN (lm.lm_local_long='long' OR lhl.lhl_local_long='long' OR lct.lct_local_long='long') AND (NOT EXISTS (SELECT lead_id FROM lead_routing WHERE `lead`.lead_id = lead_routing.lead_id AND route_status='sold')) THEN `lead`.lead_id ELSE NULL END) AS long_unsold
                /*COUNT(DISTINCT `lc`.lead_call_id) AS live_transfer*/
            FROM
            `lead`
            LEFT JOIN lead_routing AS lr ON lr.lead_id = `lead`.lead_id
            LEFT JOIN mst_lead_source AS o ON `lead`.lead_source_id = o.lead_source_id
            LEFT JOIN (SELECT lead_id,CASE WHEN from_state = to_state THEN 'local' ELSE 'long' END AS lm_local_long from lead_moving WHERE from_state IS NOT NULL AND to_state IS NOT NULL) AS lm ON `lead`.lead_id = lm.lead_id
            LEFT JOIN (SELECT lead_id from lead_junk_removal WHERE from_state IS NOT NULL) AS ljr ON `lead`.lead_id = ljr.lead_id
            LEFT JOIN (SELECT lead_id,CASE WHEN from_state = to_state THEN 'local' ELSE 'long' END AS lhl_local_long from lead_heavy_lifting WHERE from_state IS NOT NULL AND to_state IS NOT NULL) AS lhl ON `lead`.lead_id = lhl.lead_id
            LEFT JOIN (SELECT lead_id,CASE WHEN from_state = to_state THEN 'local' ELSE 'long' END AS lct_local_long from lead_car_transport WHERE from_state IS NOT NULL AND to_state IS NOT NULL) AS lct ON `lead`.lead_id = lct.lead_id
            /*LEFT JOIN lead_transfer_call AS lc ON `lead`.lead_id = lc.lead_id*/
            WHERE
                $whereDateQuery
                $append_category
            GROUP BY `lead`.lead_source_id ORDER BY Payout DESC"
        );

        $liveTransferCallsQuery = LeadTransferCall::leftJoin('lead_call as lc', 'lc.lead_call_id', 'lead_transfer_call.lead_call_id')
            ->leftJoin('lead AS l', 'l.lead_id', 'lc.lead_id')
            /*->leftJoin('lead_moving AS lm', 'l.lead_id', 'lm.lead_id')*/
            ->leftJoin('campaign_moving AS cm', 'cm.campaign_id', 'lead_transfer_call.campaign_id')
            ->leftJoin('mst_lead_source AS o', 'l.lead_source_id', 'o.lead_source_id');
        if ($castrevenuecheckboxvalue === true) {
            $liveTransferCallsQuery->whereBetween('lc.created_at', [$revenueStartDate, $revenueEndDate]);
        }
        if ($castleadcheckboxvalue === true) {
            $liveTransferCallsQuery->whereBetween('l.created_at', [$leadStartDate, $leadEndDate]);
        }
        $liveTransferCallsQuery->select(
            'lc.lead_call_id',
            'l.lead_source_id',
            DB::raw("COUNT(*) AS live_transfer"),
            DB::raw("SUM(CASE WHEN cm.move_type = 'long' THEN 1 ELSE 0 END) AS long_live_transfer"),
            DB::raw("SUM(CASE WHEN cm.move_type = 'local' OR cm.move_type IS NULL THEN 1 ELSE 0 END) AS local_live_transfer")
        );
        $liveTransferCalls = $liveTransferCallsQuery->groupBy('l.lead_source_id')->get()->toArray();

        for ($l = 0; $l < count($liveTransferCalls); $l++) {
            /*if (!in_array($liveTransferCalls[$l]['lead_source_id'], $sourceIdArr)) {*/
            $sourceIdArr[$liveTransferCalls[$l]['lead_source_id']] = $liveTransferCalls[$l]['live_transfer'] ?? 0;
            $sourceIdLocalArr[$liveTransferCalls[$l]['lead_source_id']] = $liveTransferCalls[$l]['local_live_transfer'] ?? 0;
            $sourceIdLongArr[$liveTransferCalls[$l]['lead_source_id']] = $liveTransferCalls[$l]['long_live_transfer'] ?? 0;
            /*}*/
        }
        // echo "<pre>";print_r($leadOrigin);exit;
        //dump($leadOrigin);

        //Added By HJ On 05-08-2022 For Get Prime Marketing Cost Start
        $sellerIdArr = $sellerSourceArr = $sourceCostArr = array();
        $getSellerSource = PPSeller::distinct()->get(['source_name', 'seller_id'])->toArray();
        for ($g = 0; $g < count($getSellerSource); $g++) {
            if (!in_array($getSellerSource[$g]['seller_id'], $sellerIdArr)) {
                $sellerIdArr[] = $getSellerSource[$g]['seller_id'];
            }
            $sellerSourceArr[$getSellerSource[$g]['seller_id']] = $getSellerSource[$g]['source_name'];
        }
        // echo "<pre>";
        // print_r($sellerSourceArr);
        // exit();
        // echo "<pre>";
        // print_r($sellerIdArr);
        // exit();
        //echo $startdate."==".$enddate;die;
        $startdate = date("Y-m-d",strtotime($request->valStartDate));
        $enddate = date("Y-m-d",strtotime($request->valEndDate));
        $getPostData = PPPost::whereBetween('created_at', [$startdate . " 00:00:00", $enddate . " 23:59:59"])->whereIn('seller_id', $sellerIdArr)->get(['buyer_amount', 'post_id', 'lead_id', 'seller_id'])->toArray();
        // echo "<pre>";
        // print_r($getPostData);
        // exit();

        for ($d = 0; $d < count($getPostData); $d++) {
            if (isset($sellerSourceArr[$getPostData[$d]['seller_id']])) {
                $vmSource = "PMM(" . $sellerSourceArr[$getPostData[$d]['seller_id']] . ")";
                if (isset($sourceCostArr[$vmSource])) {
                    $sourceCostArr[$vmSource]['cost'] += $getPostData[$d]['buyer_amount'];
                    $sourceCostArr[$vmSource]['count'] += 1;
                } else {
                    $sourceCostArr[$vmSource]['cost'] = $getPostData[$d]['buyer_amount'];
                    $sourceCostArr[$vmSource]['count'] = 1;
                }
            }
        }
        // echo "<pre>";
        // print_r($sourceCostArr);
        // exit();
        $vmoriginArr = $qtmoriginArr = $isoriginArr = [];
        foreach ($leadOrigin as $key => $value) {
            $origin = $value->name;
            if (strpos($origin, 'VM') !== false) {
                $source_id = '15';
                $leadSourceId = 20;
                $vmoriginArr[] = $origin;
            }
            if (strpos($origin, 'QTM') !== false) {
                /*$source_id = '13';
                $leadSourceId = 12;*/
                $source_id = '118';
                $leadSourceId = 139;
                $qtmoriginArr[] = $origin;
            }
            if (strpos($origin, 'QTMOLD') !== false) {
                $source_id = '118';
                $leadSourceId = 139;
                $qtmoriginArr[] = $origin;
            }
            if (strpos($origin, 'IS') !== false) {
                $source_id = '64';
                $isoriginArr[] = $origin;
            }

            $cost = $bingCost = 0;
            $value->cost = 0;
            $value->clicks = 0;
            $value->cpa = 0;
            $value->live_transfer = 0;
            $value->local_live_transfer = 0;
            $value->long_live_transfer = 0;
            if ($origin == 'VM(G)' || $origin == 'QTM(G)' || $origin == 'QTMOLD(G)' || $origin == 'IS') {
                $cost = GoogleAdwordsCampaignCost::where('lead_source_id', $source_id)->whereBetween('cost_date', [$startdate, $enddate])->sum('cost');
                $clicks = GoogleAdwordsCampaignCost::where('lead_source_id', $source_id)->whereBetween('cost_date', [$startdate, $enddate])->count('click');
                $value->cost = number_format($cost, 2);
                $value->clicks = $clicks;
                $value->cpa = number_format($cost / $value->Id, 2);
            }
            if ($origin == 'VM(B)' || $origin == 'QTM(B)' || $origin == 'QTMOLD(B)') {
                $bingCost = BingAdsCampaignCost::where('lead_source_id', $leadSourceId)->whereBetween('cost_date', [$startdate, $enddate])->sum('cost');
                $value->cost = number_format($bingCost, 2);
                $value->cpa = number_format($bingCost / $value->Id, 2);
            }
            $value->sdate = $startdate;
            $value->edate = $enddate;

            if (isset($sourceCostArr[$origin])) {
                $costls = $sourceCostArr[$origin]['cost'];
                $value->cost = number_format($costls, 2);
                $value->cpa = number_format($costls / $sourceCostArr[$origin]['count'], 2);
            }

            if (isset($sourceIdArr[$value->id])) {
                $value->live_transfer = $sourceIdArr[$value->id];
            }

            if (isset($sourceIdLocalArr[$value->id])) {
                $value->local_live_transfer = $sourceIdLocalArr[$value->id];
            }

            if (isset($sourceIdLongArr[$value->id])) {
                $value->long_live_transfer = $sourceIdLongArr[$value->id];
            }
        }

        foreach ($leadOrigin as $leadOriginkey => $leadOriginvalue) {
            $dataFound = 1;
            /*$leadOriginvalue->clicks = 0;*/
            $leadOriginvalue->totalRow = 0;
            $leadOriginvalue->AvgPayout = $leadOriginvalue->Payout / $leadOriginvalue->Id;
            $totalLeads += $leadOriginvalue->Id;
            $totalPayout += str_replace(",", "", $leadOriginvalue->Payout);
            $totalAvgPayout += str_replace(",", "", 0);
            $totalLocal += $leadOriginvalue->state_local;
            $totalLocalV += $leadOriginvalue->local_verified;
            $totalLong += $leadOriginvalue->state_long;
            $totalLongV += $leadOriginvalue->long_verified;
            $totalLocalUnsold += $leadOriginvalue->local_unsold;
            $totalLongUnsold += $leadOriginvalue->long_unsold;
            $totalClicks += str_replace(",", "", $leadOriginvalue->clicks);
            $totalCost += str_replace(",", "", $leadOriginvalue->cost);
            $totalCpa += str_replace(",", "", $leadOriginvalue->cpa);
            $totalLiveTransfer += $leadOriginvalue->live_transfer;
            $totalLocalLiveTransfer += $leadOriginvalue->local_live_transfer;
            $totalLongLiveTransfer += $leadOriginvalue->long_live_transfer;
            // $leadOriginvalue->cost = 0;
            // $leadOriginvalue->cpa = 0;
            // $leadOriginvalue->clicks = 1;
        }

        $newTotalArr = array();
        if ($dataFound > 0) {
            $newTotalArr['totalRow'] = 1;
            $newTotalArr['id'] = 0;
            $newTotalArr['name'] = "<b>Total</b>";
            $newTotalArr['local_unsold'] = $totalLocalUnsold;
            $newTotalArr['long_unsold'] = $totalLongUnsold;
            $newTotalArr['local_verified'] = $totalLocalV;
            $newTotalArr['long_verified'] = $totalLongV;
            $newTotalArr['state_local'] = $totalLocal;
            $newTotalArr['state_long'] = $totalLong;
            $newTotalArr['Id'] = $totalLeads;
            $newTotalArr['Payout'] = $totalPayout;
            $newTotalArr['AvgPayout'] = $totalPayout / $totalLeads;
            $newTotalArr['cost'] = $this->setTwoDecimalPoint($totalCost);
            $newTotalArr['cpa'] = $this->setTwoDecimalPoint($totalCost / $totalLeads);
            $newTotalArr['clicks'] = $totalClicks;
            $newTotalArr['live_transfer'] = $totalLiveTransfer;
            $newTotalArr['local_live_transfer'] = $totalLocalLiveTransfer;
            $newTotalArr['long_live_transfer'] = $totalLongLiveTransfer;
            $newRowArr[] = $newTotalArr;
            $objectFromArr = json_decode(json_encode($newRowArr), FALSE);
            $obj_merged = (object) array_merge((array) $objectFromArr, $leadOrigin);
        } else {
            $obj_merged = $leadOrigin;
        }

        return response()->json(['leadOrigin' => $obj_merged]);
    }

    public function getcampaignalldata(Request $request)
    {
        $post = $request->all();
        $oid = $post['id'];
        $sourceIdArr = $sourceIdLocalArr = $sourceIdLongArr = [];
        $when_category = ($post['category'] != 'alldata') ? true : false;
        $category_id = $when_category ? $post['category'] : 0;

        $castrevenuecheckboxvalue = $request->revenuecheckboxvalue === "true" ? true : false;
        $castleadcheckboxvalue = $request->leadcheckboxvalue === "true" ? true : false;
        $leadStartDate = date('Y-m-d 00:00:00', strtotime($request->valStartDate));
        $leadEndDate = date('Y-m-d 23:59:59', strtotime($request->valEndDate));
        $revenueStartDate = date('Y-m-d 00:00:00', strtotime($request->revenueStartDate));
        $revenueEndDate = date('Y-m-d 23:59:59', strtotime($request->revenueEndDate));

        $whereDateQuery = '';
        if ($castrevenuecheckboxvalue === true) {
            $whereDateQuery = "lr.created_at BETWEEN '$revenueStartDate' AND '$revenueEndDate'";
        } else if ($castleadcheckboxvalue === true) {
            $whereDateQuery = "`lead`.created_at BETWEEN '$leadStartDate' AND '$leadEndDate'";
        } else if ($castrevenuecheckboxvalue === true && $castleadcheckboxvalue === true) {
            $whereDateQuery = "lr.created_at BETWEEN '$revenueStartDate' AND '$revenueEndDate' AND `lead`.created_at BETWEEN '$leadStartDate' AND '$leadEndDate'";
        }

        $append_category = "";
        if ($when_category) {
            $append_category = " AND `lead`.lead_category_id = $category_id";
        }

        $leadOrigin = DB::select(
            "SELECT
                lp.landing_campaign_id,
                lp.utm_campaign,
                o.lead_source_name as name,
                COUNT(DISTINCT `lead`.lead_id) as Id,
                IFNULL(SUM(CASE WHEN lr.route_status='sold' THEN lr.payout END), 0) as Payout,
                COUNT(DISTINCT CASE WHEN lm.lm_local_long='local' OR lhl.lhl_local_long='local' OR lct.lct_local_long='local' OR ljr.lead_id IS NOT NULL THEN `lead`.lead_id ELSE NULL END) as state_local,
                COUNT(DISTINCT CASE WHEN lm.lm_local_long='long' OR lhl.lhl_local_long='long' OR lct.lct_local_long='long' THEN `lead`.lead_id ELSE NULL END) AS state_long,
                COUNT(DISTINCT CASE WHEN (lm.lm_local_long='local' OR lhl.lhl_local_long='local' OR lct.lct_local_long='local' OR ljr.lead_id IS NOT NULL) AND `lead`.is_verified = 'yes' THEN `lead`.lead_id ELSE NULL END) AS local_verified,
                COUNT(DISTINCT CASE WHEN (lm.lm_local_long='long' OR lhl.lhl_local_long='long' OR lct.lct_local_long='long') AND `lead`.is_verified = 'yes' THEN `lead`.lead_id ELSE NULL END) AS long_verified,
                COUNT(DISTINCT CASE WHEN (lm.lm_local_long='local' OR lhl.lhl_local_long='local' OR lct.lct_local_long='local' OR ljr.lead_id IS NOT NULL) AND (NOT EXISTS (SELECT lead_id FROM lead_routing WHERE `lead`.lead_id = lead_routing.lead_id AND route_status='sold')) THEN `lead`.lead_id ELSE NULL END) AS local_unsold,
                COUNT(DISTINCT CASE WHEN (lm.lm_local_long='long' OR lhl.lhl_local_long='long' OR lct.lct_local_long='long') AND (NOT EXISTS (SELECT lead_id FROM lead_routing WHERE `lead`.lead_id = lead_routing.lead_id AND route_status='sold')) THEN `lead`.lead_id ELSE NULL END) AS long_unsold,
                MAX(lp.landing_campaign_id) AS camp_id,
                lp.ad_group_id as ad_grp_id,
                lp.ad_group as ad_group
                /*COUNT(DISTINCT `lc`.lead_call_id) AS live_transfer*/
            FROM
            `lead`
            LEFT JOIN lead_routing AS lr ON lr.lead_id = `lead`.lead_id
            LEFT JOIN mst_lead_source AS o ON `lead`.lead_source_id = o.lead_source_id
            LEFT JOIN lead_landing_page AS lp ON `lead`.lead_id = lp.lead_id
            LEFT JOIN (SELECT lead_id,CASE WHEN from_state = to_state THEN 'local' ELSE 'long' END AS lm_local_long from lead_moving WHERE from_state IS NOT NULL AND to_state IS NOT NULL) AS lm ON `lead`.lead_id = lm.lead_id
            LEFT JOIN (SELECT lead_id from lead_junk_removal WHERE from_state IS NOT NULL) AS ljr ON `lead`.lead_id = ljr.lead_id
            LEFT JOIN (SELECT lead_id,CASE WHEN from_state = to_state THEN 'local' ELSE 'long' END AS lhl_local_long from lead_heavy_lifting WHERE from_state IS NOT NULL AND to_state IS NOT NULL) AS lhl ON `lead`.lead_id = lhl.lead_id
            LEFT JOIN (SELECT lead_id,CASE WHEN from_state = to_state THEN 'local' ELSE 'long' END AS lct_local_long from lead_car_transport WHERE from_state IS NOT NULL AND to_state IS NOT NULL) AS lct ON `lead`.lead_id = lct.lead_id
            /*LEFT JOIN lead_transfer_call AS lc ON `lead`.lead_id = lc.lead_id*/
            WHERE
                $whereDateQuery
                $append_category
                AND o.lead_source_id = $oid
            GROUP BY lp.landing_campaign_id ORDER BY Payout DESC"
        );

        $liveTransferCallsQuery = LeadTransferCall::leftJoin('lead_call as lc', 'lc.lead_call_id', 'lead_transfer_call.lead_call_id')
            ->leftJoin('lead AS l', 'l.lead_id', 'lc.lead_id')
            /*->leftJoin('lead_moving AS lm', 'l.lead_id', 'lm.lead_id')*/
            ->leftJoin('campaign_moving AS cm', 'cm.campaign_id', 'lead_transfer_call.campaign_id')
            ->leftJoin('lead_landing_page AS lp', 'l.lead_id', 'lp.lead_id')
            ->leftJoin('mst_lead_source AS o', 'l.lead_source_id', 'o.lead_source_id');
        if ($castrevenuecheckboxvalue === true) {
            $liveTransferCallsQuery->whereBetween('lc.created_at', [$revenueStartDate, $revenueEndDate]);
        }
        if ($castleadcheckboxvalue === true) {
            $liveTransferCallsQuery->whereBetween('l.created_at', [$leadStartDate, $leadEndDate]);
        }
        $liveTransferCallsQuery->select(
            'lp.landing_campaign_id',
            'lp.utm_campaign',
            'l.lead_source_id',
            DB::raw("COUNT(*) AS live_transfer"),
            DB::raw("SUM(CASE WHEN cm.move_type = 'long' THEN 1 ELSE 0 END) AS long_live_transfer"),
            DB::raw("SUM(CASE WHEN cm.move_type = 'local' OR cm.move_type IS NULL THEN 1 ELSE 0 END) AS local_live_transfer")
        );
        $liveTransferCalls = $liveTransferCallsQuery->where('o.lead_source_id', $oid)->groupBy('lp.utm_campaign')->get()->toArray();

        for ($l = 0; $l < count($liveTransferCalls); $l++) {
            /*if (!in_array($liveTransferCalls[$l]['utm_campaign'], $sourceIdArr)) {*/
            if (isset($sourceIdArr[$liveTransferCalls[$l]['landing_campaign_id']])) {
                $sourceIdArr[$liveTransferCalls[$l]['landing_campaign_id']] += $liveTransferCalls[$l]['live_transfer'] ?? 0;
            } else {
                $sourceIdArr[$liveTransferCalls[$l]['landing_campaign_id']] = $liveTransferCalls[$l]['live_transfer'] ?? 0;
            }
            if (isset($sourceIdLocalArr[$liveTransferCalls[$l]['landing_campaign_id']])) {
                $sourceIdLocalArr[$liveTransferCalls[$l]['landing_campaign_id']] += $liveTransferCalls[$l]['local_live_transfer'] ?? 0;
            } else {
                $sourceIdLocalArr[$liveTransferCalls[$l]['landing_campaign_id']] = $liveTransferCalls[$l]['local_live_transfer'] ?? 0;
            }
            if (isset($sourceIdLongArr[$liveTransferCalls[$l]['landing_campaign_id']])) {
                $sourceIdLongArr[$liveTransferCalls[$l]['landing_campaign_id']] += $liveTransferCalls[$l]['long_live_transfer'] ?? 0;
            } else {
                $sourceIdLongArr[$liveTransferCalls[$l]['landing_campaign_id']] = $liveTransferCalls[$l]['long_live_transfer'] ?? 0;
            }
            /*}*/
        }

        $vmoriginArr = $qtmoriginArr = $isoriginArr = [];
        $startdate = date('Y-m-d', strtotime($post['valStartDate']));
        $enddate = date('Y-m-d', strtotime($post['valEndDate']));
        foreach ($leadOrigin as $key => $value) {
            $origin = $value->name;
            if (strpos($origin, 'VM') !== false) {
                $source_id = '15';
                $leadSourceId = 20;
                $vmoriginArr[] = $origin;
            }
            if (strpos($origin, 'QTM') !== false) {
                $source_id = '13';
                $leadSourceId = 12;
                $source_id = '118';
                $leadSourceId = 139;
                $qtmoriginArr[] = $origin;
            }
            if (strpos($origin, 'QTMOLD') !== false) {
                $source_id = '118';
                $leadSourceId = 139;
                $qtmoriginArr[] = $origin;
            }
            if (strpos($origin, 'IS') !== false) {
                $source_id = '64';
                $isoriginArr[] = $origin;
            }

            $cost = $bingCost = 0;
            $value->cost = 0;
            $value->clicks = 0;
            $value->cpa = 0;
            $value->live_transfer = 0;
            $value->local_live_transfer = 0;
            $value->long_live_transfer = 0;
            if ($origin == 'VM(G)' || $origin == 'VMO(G)' || $origin == 'VMNL(G)' || $origin == 'QTMJR(G)' || $origin == 'QTMHL(G)' || $origin == 'QTMCT(G)' || $origin == 'QTM(G)' || $origin == 'QTMOLD(G)' || $origin == 'IS' || $origin == 'IS(G)' || $origin == 'ISM(G)' || $origin == 'ISF(G)') {
                $cost = GoogleAdwordsCampaignCost::where('lead_source_id', $source_id)->whereBetween('cost_date', [$startdate, $enddate])->where('campaign_id', $value->camp_id)->sum('cost');
                $clicks = GoogleAdwordsCampaignCost::where('lead_source_id', $source_id)->whereBetween('cost_date', [$startdate, $enddate])->where('campaign_id', $value->camp_id)->count('click');
                $value->cost = number_format($cost, 2);
                $value->clicks = $clicks;
                $value->cpa = number_format($cost / $value->Id, 2);
            }
            if ($origin == 'VM(B)' || $origin == 'VMO(B)' || $origin == 'VMNL(B)' || $origin == 'QTM(B)' || $origin == 'QTMOLD(B)') {
                $bingCost = BingAdsCampaignCost::where('lead_source_id', $leadSourceId)->whereBetween('cost_date', [$startdate, $enddate])->where('campaign_id', $value->camp_id)->sum('cost');
                $value->cost = number_format($bingCost, 2);
                $value->cpa = number_format($bingCost / $value->Id, 2);
            }

            if (isset($sourceIdArr[$value->landing_campaign_id])) {
                $value->live_transfer = $sourceIdArr[$value->landing_campaign_id];
            }

            if (isset($sourceIdLocalArr[$value->landing_campaign_id])) {
                $value->local_live_transfer = $sourceIdLocalArr[$value->landing_campaign_id];
            }

            if (isset($sourceIdLongArr[$value->landing_campaign_id])) {
                $value->long_live_transfer = $sourceIdLongArr[$value->landing_campaign_id];
            }
        }

        foreach ($leadOrigin as $leadOriginkey => $leadOriginvalue) {
            /*$leadOriginvalue->clicks = 0;*/
            $leadOriginvalue->AvgPayout = $leadOriginvalue->Payout / $leadOriginvalue->Id;
        }
        // echo "<pre>";
        // print_r($leadOrigin);
        // exit();

        return response()->json(['result' => '1', 'leadOrigin' => $leadOrigin]);
    }

    public function getaddgrpalldata(Request $request)
    {
        $post = $request->all();
        $oid = $post['id'];
        $sourceIdArr = $sourceIdLocalArr = $sourceIdLongArr = [];
        $campname = $post['campname'];
        $when_category = ($post['category'] != 'alldata') ? true : false;
        $category_id = $when_category ? $post['category'] : 0;

        $castrevenuecheckboxvalue = $request->revenuecheckboxvalue === "true" ? true : false;
        $castleadcheckboxvalue = $request->leadcheckboxvalue === "true" ? true : false;
        $leadStartDate = date('Y-m-d 00:00:00', strtotime($request->valStartDate));
        $leadEndDate = date('Y-m-d 23:59:59', strtotime($request->valEndDate));
        $revenueStartDate = date('Y-m-d 00:00:00', strtotime($request->revenueStartDate));
        $revenueEndDate = date('Y-m-d 23:59:59', strtotime($request->revenueEndDate));

        $whereDateQuery = '';
        if ($castrevenuecheckboxvalue === true) {
            $whereDateQuery = "lr.created_at BETWEEN '$revenueStartDate' AND '$revenueEndDate'";
        } else if ($castleadcheckboxvalue === true) {
            $whereDateQuery = "`lead`.created_at BETWEEN '$leadStartDate' AND '$leadEndDate'";
        } else if ($castrevenuecheckboxvalue === true && $castleadcheckboxvalue === true) {
            $whereDateQuery = "lr.created_at BETWEEN '$revenueStartDate' AND '$revenueEndDate' AND `lead`.created_at BETWEEN '$leadStartDate' AND '$leadEndDate'";
        }

        $append_category = "";
        if ($when_category) {
            $append_category = " AND `lead`.lead_category_id = $category_id";
        }

        $leadOrigin = DB::select(
            "SELECT
                lp.ad_id as ad_id,
                COUNT(DISTINCT `lead`.lead_id) as Id,
                IFNULL(SUM(CASE WHEN lr.route_status='sold' THEN lr.payout END), 0) as Payout,
                COUNT(DISTINCT CASE WHEN lm.lm_local_long='local' OR lhl.lhl_local_long='local' OR lct.lct_local_long='local' OR ljr.lead_id IS NOT NULL THEN `lead`.lead_id ELSE NULL END) as state_local,
                COUNT(DISTINCT CASE WHEN lm.lm_local_long='long' OR lhl.lhl_local_long='long' OR lct.lct_local_long='long' THEN `lead`.lead_id ELSE NULL END) AS state_long,
                COUNT(DISTINCT CASE WHEN (lm.lm_local_long='local' OR lhl.lhl_local_long='local' OR lct.lct_local_long='local' OR ljr.lead_id IS NOT NULL) AND `lead`.is_verified = 'yes' THEN `lead`.lead_id ELSE NULL END) AS local_verified,
                COUNT(DISTINCT CASE WHEN (lm.lm_local_long='long' OR lhl.lhl_local_long='long' OR lct.lct_local_long='long') AND `lead`.is_verified = 'yes' THEN `lead`.lead_id ELSE NULL END) AS long_verified,
                COUNT(DISTINCT CASE WHEN (lm.lm_local_long='local' OR lhl.lhl_local_long='local' OR lct.lct_local_long='local' OR ljr.lead_id IS NOT NULL) AND (NOT EXISTS (SELECT lead_id FROM lead_routing WHERE `lead`.lead_id = lead_routing.lead_id AND route_status='sold')) THEN `lead`.lead_id ELSE NULL END) AS local_unsold,
                COUNT(DISTINCT CASE WHEN (lm.lm_local_long='long' OR lhl.lhl_local_long='long' OR lct.lct_local_long='long') AND (NOT EXISTS (SELECT lead_id FROM lead_routing WHERE `lead`.lead_id = lead_routing.lead_id AND route_status='sold')) THEN `lead`.lead_id ELSE NULL END) AS long_unsold
            FROM
            `lead`
            LEFT JOIN lead_routing AS lr ON lr.lead_id = `lead`.lead_id
            LEFT JOIN mst_lead_source AS o ON `lead`.lead_source_id = o.lead_source_id
            LEFT JOIN lead_landing_page AS lp ON `lead`.lead_id = lp.lead_id
            LEFT JOIN (SELECT lead_id,CASE WHEN from_state = to_state THEN 'local' ELSE 'long' END AS lm_local_long from lead_moving WHERE from_state IS NOT NULL AND to_state IS NOT NULL) AS lm ON `lead`.lead_id = lm.lead_id
            LEFT JOIN (SELECT lead_id from lead_junk_removal WHERE from_state IS NOT NULL) AS ljr ON `lead`.lead_id = ljr.lead_id
            LEFT JOIN (SELECT lead_id,CASE WHEN from_state = to_state THEN 'local' ELSE 'long' END AS lhl_local_long from lead_heavy_lifting WHERE from_state IS NOT NULL AND to_state IS NOT NULL) AS lhl ON `lead`.lead_id = lhl.lead_id
            LEFT JOIN (SELECT lead_id,CASE WHEN from_state = to_state THEN 'local' ELSE 'long' END AS lct_local_long from lead_car_transport WHERE from_state IS NOT NULL AND to_state IS NOT NULL) AS lct ON `lead`.lead_id = lct.lead_id
            WHERE
                $whereDateQuery
                AND `lead`.lead_source_id = '$oid'
                /*AND lp.utm_campaign = '$campname'*/
                AND lp.landing_campaign_id = '$campname'
                $append_category
            GROUP BY lp.ad_id ORDER BY Payout DESC"
        );

        $liveTransferCallsQuery = LeadTransferCall::leftJoin('lead_call as lc', 'lc.lead_call_id', 'lead_transfer_call.lead_call_id')
            ->leftJoin('lead AS l', 'l.lead_id', 'lc.lead_id')
            /*->leftJoin('lead_moving AS lm', 'l.lead_id', 'lm.lead_id')*/
            ->leftJoin('campaign_moving AS cm', 'cm.campaign_id', 'lead_transfer_call.campaign_id')
            ->leftJoin('lead_landing_page AS lp', 'l.lead_id', 'lp.lead_id')
            ->leftJoin('mst_lead_source AS o', 'l.lead_source_id', 'o.lead_source_id');
        if ($castrevenuecheckboxvalue === true) {
            $liveTransferCallsQuery->whereBetween('lc.created_at', [$revenueStartDate, $revenueEndDate]);
        }
        if ($castleadcheckboxvalue === true) {
            $liveTransferCallsQuery->whereBetween('l.created_at', [$leadStartDate, $leadEndDate]);
        }
        $liveTransferCallsQuery->select(
            'lp.ad_id',
            'l.lead_source_id',
            DB::raw("COUNT(*) AS live_transfer"),
            DB::raw("SUM(CASE WHEN cm.move_type = 'long' THEN 1 ELSE 0 END) AS long_live_transfer"),
            DB::raw("SUM(CASE WHEN cm.move_type = 'local' OR cm.move_type IS NULL THEN 1 ELSE 0 END) AS local_live_transfer")
        );
        $liveTransferCalls = $liveTransferCallsQuery->where('o.lead_source_id', $oid)->where('lp.utm_campaign', $campname)->groupBy('lp.ad_id')->get()->toArray();

        for ($l = 0; $l < count($liveTransferCalls); $l++) {
            if (!in_array($liveTransferCalls[$l]['ad_id'], $sourceIdArr)) {
                $sourceIdArr[$liveTransferCalls[$l]['ad_id']] = $liveTransferCalls[$l]['live_transfer'] ?? 0;
                $sourceIdLocalArr[$liveTransferCalls[$l]['ad_id']] = $liveTransferCalls[$l]['local_live_transfer'] ?? 0;
                $sourceIdLongArr[$liveTransferCalls[$l]['ad_id']] = $liveTransferCalls[$l]['long_live_transfer'] ?? 0;
            }
        }

        foreach ($leadOrigin as $leadOriginkey => $leadOriginvalue) {
            $leadOriginvalue->AvgPayout = $leadOriginvalue->Payout / $leadOriginvalue->Id;
            $leadOriginvalue->live_transfer = 0;
            if (isset($sourceIdArr[$leadOriginvalue->ad_id])) {
                $leadOriginvalue->live_transfer = $sourceIdArr[$leadOriginvalue->ad_id];
            }
            if (isset($sourceIdLocalArr[$leadOriginvalue->ad_id])) {
                $leadOriginvalue->local_live_transfer = $sourceIdLocalArr[$leadOriginvalue->ad_id];
            }
            if (isset($sourceIdLongArr[$leadOriginvalue->ad_id])) {
                $leadOriginvalue->long_live_transfer = $sourceIdLongArr[$leadOriginvalue->ad_id];
            }
        }
        return response()->json(['result' => '1', 'leadOrigin' => $leadOrigin]);
    }

    public function setTwoDecimalPoint($value)
    {
        //return number_format($value, 2, '.', '');
        //return number_format(floatval($value), 2); --->old code
        return number_format(floatval($value), 2, ".", "");
    }
}
