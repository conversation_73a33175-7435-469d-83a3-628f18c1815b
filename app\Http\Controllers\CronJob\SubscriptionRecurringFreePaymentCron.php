<?php

namespace App\Http\Controllers\CronJob;

use Illuminate\Http\Request;
use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\Subscription\Subscription;
use App\Models\Subscription\SubscriptionRecuringPayment;
use App\Models\Business\Business;
use App\Models\Business\BusinessCC;
use App\Models\Business\BusinessCampaignPayment;
use App\Models\Business\BusinessCampaingnPaymentLog;
use App\Http\Controllers\Businesses\BusinessPaymentController;
use App\Models\Master\MstSubscriptionPlan;
use App\Models\Emails\RecurringEmailLog;
use App\Models\Emails\TemplateEmail;
use App\Models\CronJob\CronLog;
use App\Models\DevDebug;
use DB;
use Exception;
use Auth;

class SubscriptionRecurringFreePaymentCron extends Controller
{
    public function subscriptionRecurringPayment() {
        try {
            $this->insertLog(11,'SubscriptionRecurringPayment Cron Start: ' . date("Y-m-d H:i:s"));
            $subscriptionDetail = Subscription::where('subscription_start', '>=', date('Y-m-01', strtotime('+1 month')))->where('subscription_end', '<=', date('Y-m-t', strtotime('+1 month')))->where('is_active', 'yes')->get(['business_id']);
            $businessIdArray = $subscriptionPlanArray = [];
            $currentDate = date('Y-m-d', time());
            if (count($subscriptionDetail) > 0) { foreach($subscriptionDetail as $subscription) {
                $businessIdArray[] = $subscription->business_id;
            } }

            $subscriptionPlan = MstSubscriptionPlan::get();
            for ($p=0; $p<count($subscriptionPlan); $p++) {
                $subscriptionPlanArray[$subscriptionPlan[$p]->subscription_plan_id] = $subscriptionPlan[$p]->subscription_plan;
            }

            $recuringPaymentDetail = Subscription::whereIn('business_id', [525, 462])->whereNotIn('business_id', $businessIdArray)->get();
            //echo '<pre>'; print_r($recuringPaymentDetail); die;
            foreach ($recuringPaymentDetail as $recuringPayment) {
                $subscriptionPlanDetail = MstSubscriptionPlan::where('subscription_plan_id', $recuringPayment->subscription_plan_id)->first();
                $businessId = $recuringPayment->business_id;
                // echo '<pre>flag = '. $flag .', businessCampaignType = '. $businessId;

                Subscription::create([
                    'business_id' => $businessId,
                    'subscription_plan_id' => $recuringPayment->subscription_plan_id,
                    'subscription_start' => date('Y-m-01', strtotime('+1 month', strtotime($currentDate))),
                    'subscription_end' => date('Y-m-t', strtotime('+1 month', strtotime($currentDate))),
                    'amount' => $subscriptionPlanDetail->amount,
                    'is_active' => 'yes',
                    'created_user_id' => 6,
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                DevDebug::create(['sr_no' => 213, 'result' => 'businessId ' . $businessId, 'created_at' => date('Y-m-d H:i:s')]);
                stop:
            }
        } catch (Exception $e) {
            DevDebug::create(['sr_no' => 214, 'result' => 'Catch error ' . $e->getMessage(), 'created_at' => date('Y-m-d H:i:s')]);
            dd($e->getMessage());
        }
    }

    public function insertLog($srno, $log){
        CronLog::create([
            'log_srno' => $srno,
            'log' => $log,
            'cron_type' => 'recurringpaymentcron',
            'created_at' => date('Y-m-d H:i:s'),
        ]);
    }
}
