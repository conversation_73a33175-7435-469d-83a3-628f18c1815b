<?php

namespace App\Http\Controllers\Campaign;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Helpers\CommonFunctions;
use App\Models\Business\Business;
use App\Models\Campaign\Campaign;
use App\Models\Campaign\CampaignScheule;
use App\Models\Campaign\CampaignJunkType;
use App\Models\Campaign\CampaignJunkSubType;
use App\Models\Campaign\CampaignHeavyLifting;
use App\Models\Campaign\CampaignCarTransport;
use App\Models\Campaign\CampaignLocation;
use App\Models\Campaign\CampaignMoving;
use App\Models\Campaign\CampaignMoveSize;
use App\Models\Campaign\CampaignLeadSource;
use App\Models\Campaign\CampaignLeadDeliveryType;
use App\Models\Campaign\CampaignEmailDelivery;
use App\Models\Campaign\CampaignSmsDelivery;
use App\Models\Campaign\CampaignCrmDelivery;
use App\Models\Campaign\CampaignLeadDialer;
use App\Models\Campaign\CampaignDialerPoints;
use App\Models\Campaign\CampaignCallPayout;
use App\Models\Campaign\CampaignOrganic;
use App\Models\Campaign\CampaignUpdateLog;
use App\Models\Campaign\LeadDeliveryCrmDefaultFields;
use App\Models\Master\MstCampaignType;
use App\Models\Master\MstLeadCategory;
use App\Models\Master\MstZipcode;
use App\Models\Master\MstLeadSource;
use App\Models\Master\MstMoveSize;
use App\Models\Master\MstLeadType;
use App\Models\Master\MstJunkType;
use App\Models\Master\MstJunkSubType;
use App\Models\Master\MstHeavyLiftingType;
use App\Models\Master\MstCarType;
use App\Models\Lead\LeadDeliveryEmailTemplate;
use App\Models\Lead\LeadDeliverySMSCarrier;
use App\Models\Lead\LeadRouting;
use App\Models\Delivery\DeliveryCrm;
use App\Models\User;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use DateTime;
use App\Models\Campaign\CampaignCarTransportType;
use Illuminate\Support\Facades\Log;

class CampaignController extends Controller
{

    function __construct()
    {
        $this->middleware('permission:campaign-list', ['only' => ['view']]);
        $this->middleware('permission:campaign-add', ['only' => ['create', 'insert']]);
        $this->middleware('permission:campaign-edit', ['only' => ['edit', 'update']]);
    }

    //load view of add campaign
    public function create()
    {
        $businessDetails = Business::distinct()->get(['business_id', 'business_name']);
        //get all state records from mst_zipcode table
        $stateDetails = MstZipcode::distinct()->get(['state']);
        //get all move size records from mst_move_size table
        $movesizeDetails = MstMoveSize::distinct()->get(['move_size_id', 'move_size']);
        //get all campaign type records from mst_zipcode table
        $campaigntypeDetails = MstCampaignType::distinct()->get(['campaign_type_id', 'campaign_type']);
        //get all category records from mst_campaign_type table
        $categoryDetails = MstLeadCategory::distinct()->get(['lead_category_id', 'lead_category_name']);
        //get all category records from mst_lead_type table
        $leadtypeDetails = MstLeadType::distinct()->get(['lead_type_id', 'lead_type']);
        $junktypeDetails = MstJunkType::distinct()->get(['junk_type_id', 'junk_type']);
        $junksubtypeDetails = MstJunkSubType::distinct()->get(['junk_sub_type_id', 'junk_sub_type']);
        $heavyLiftingTypeDetails = MstHeavyLiftingType::distinct()->get(['heavy_lifting_type_id', 'heavy_lifting_type']);
        $carTypeDetails = MstCarType::distinct()->get(['car_type_id', 'car_type']);
        $smsCarrier = LeadDeliverySMSCarrier::distinct()->get(['lead_delivery_sms_carrier_id', 'carrier']);
        $crmLists = DeliveryCrm::get();
        $originDetails = MstLeadSource::get(['lead_source_id', 'lead_source_name']);
        return view("campaign.create")->with([
            'businessDetails' => $businessDetails,
            'stateDetails' => $stateDetails,
            'movesizeDetails' => $movesizeDetails,
            'campaigntypeDetails' => $campaigntypeDetails,
            'categoryDetails' => $categoryDetails,
            'leadtypeDetails' => $leadtypeDetails,
            'junktypeDetails' => $junktypeDetails,
            'junksubtypeDetails' => $junksubtypeDetails,
            'heavyLiftingTypeDetails' => $heavyLiftingTypeDetails,
            'carTypeDetails' => $carTypeDetails,
            'smsCarrier' => $smsCarrier,
            'crmLists' => $crmLists,
            'originDetails' => $originDetails,
        ]);
    }

    //insert record in campaign related table
    public function insert(Request $request)
    {
        $responseStatus = 0;
        $message = 'Failure';
        $error = $logArray = $campaignHoursData = array();
        $logDetails = $logPaymentDetails = $logEmail = $logSms = $logCRM = $logHoursDetails = '';

        try {
            //print_r( $request->all()); die;
            $campaignCategory = $request->get('campaignArray')['campaignCategory'];
            $businessesId = $request->get('campaignArray')['businessesId'];
            $campaignName = $request->get('campaignArray')['campaignName'];
            $campaignType = $request->get('campaignArray')['campaignType'];
            $minimumDistance = $request->get('campaignArray')['minimumDistance'];
            // $score = $request->get('campaignArray')['score'];
            $score = (isset($request->get('campaignArray')['score'])) ? $request->get('campaignArray')['score'] : 0;
            $subscriptionScore = (isset($request->get('campaignArray')['subscriptionScore'])) ? $request->get('campaignArray')['subscriptionScore'] : 0;
            $leadType = $request->get('campaignArray')['leadType'];
            $forwardPhone = $request->get('campaignArray')['forwardPhone'];
            $callType = $request->get('campaignArray')['callType'];
            $callLimit = $request->get('campaignArray')['callLimit'];
            $leadDistance = $request->get('campaignArray')['leadDistance'];
            $leadDistance = (!empty($leadDistance)) ? $leadDistance : 0;
            $payoutType = $request->get('campaignArray')['payoutTypes'];
            $payoutTypes = explode(",", $payoutType);
            $automatedOutboundLocal = $request->get('campaignArray')['automatedOutboundLocal'];
            $automatedOutboundLong = $request->get('campaignArray')['automatedOutboundLong'];
            $automatedInbound = $request->get('campaignArray')['automatedInbound'];
            $tokOutboundLocal = $request->get('campaignArray')['tokOutboundLocal'];
            $tokOutboundLong = $request->get('campaignArray')['tokOutboundLong'];
            $tokInbound = $request->get('campaignArray')['tokInbound'];
            $carTransportation = $request->get('campaignArray')['carTransportation'];
            $mobileHomeTransportation = $request->get('campaignArray')['mobileHomeTransportation'];
            $moveDays = $request->get('campaignArray')['moveDays'];
            $applyExcludedDate = $request->get('campaignArray')['applyExcludedDate'];
            $moveDateFrom = ($applyExcludedDate > 0) ? $request->get('campaignArray')['moveDateFrom'] : null;
            $moveDateTo = ($applyExcludedDate > 0) ? $request->get('campaignArray')['moveDateTo'] : null;
            $verified = $request->get('campaignArray')['verified'];
            $unverified = $request->get('campaignArray')['unverified'];
            $is_remove_truck_rental = $request->get('campaignArray')['is_remove_truck_rental'];
            $is_spanish = $request->get('campaignArray')['is_spanish'];
            $status =  $request->get('campaignArray')['status'];
            $leadDialerCampaign = ($campaignType == '3' && isset($request->get('campaignArray')['leadDialerCampaign'])) ? $request->get('campaignArray')['leadDialerCampaign'] : [];
            $customPoint =  $request->get('campaignArray')['customPoint'];
            // $totalpoints =  $request->get('campaignArray')['totalpoints'];
            $dailyLeadLimit = $request->get('campaignLimitArray')['dailyLeadLimit'];
            $dailyBudgetLimit = $request->get('campaignLimitArray')['dailyBudgetLimit'];
            $hourlyLimit = $request->get('campaignLimitArray')['hourlyLimit'];
            $isRevShare = $request->get('campaignArray')['isRevShare'];
            $revSharePerc = $request->get('campaignArray')['revSharePerc'];
            $floorBid = $request->get('campaignArray')['floorBid'];

            if (!empty($request->get('campaignFromOriginArray')['fromState'])) {
                $fromState = $request->get('campaignFromOriginArray')['fromState'];
            }
            $fromAreaCode = $request->get('campaignFromOriginArray')['fromAreaCode'];
            $fromZipCode = $request->get('campaignFromOriginArray')['fromZipCode'];

            if (!empty($request->get('campaignToOriginArray')['toState'])) {
                $toState = $request->get('campaignToOriginArray')['toState'];
            }
            $toAreaCode = $request->get('campaignToOriginArray')['toAreaCode'];
            $toZipCode = $request->get('campaignToOriginArray')['toZipCode'];
            if (array_key_exists("campaignMoveSize", $request->all())){
                $campaignMoveSize = implode(",", $request->get('campaignMoveSize'));
            }
            $emailTemplatData = $request->get('emailTemplatArray');
            $granotData = $request->get('granotArray');
            $smsPhonesData = $request->get('smsPhonesArray');
            $crmData = $request->get('crmArray');
            $sourceArray = $request->get('sourceArray') ?? [];

            if (empty($businessesId)) {
                $error['businessesIdError'] = 'Select business name';
            }

            if (empty($campaignName)) {
                $error['campaignNameError'] = 'Enter campaign name';
            }

            if (empty($campaignType)) {
                $error['campaignTypeError'] = 'Select campaign type';
            }

            if ($campaignType == '3') {
                if (count($leadDialerCampaign) == 0) {
                    $error['leadDialerCampaignError'] = 'Select lead dialer campaign';
                }
            }

            if (($leadDistance == "" && $campaignCategory != '2') && $campaignType  != '7') {
                $error['leadDistanceError'] = 'Select lead distance';
            }

            if ($leadType == 1) {
                if ($campaignType != 3 && $campaignType != 5 && empty($automatedOutboundLocal) && empty($automatedOutboundLong))
                    $error['automatedOutboundError'] = 'Please enter payout';
            } elseif ($leadType == 2) {
                if (!in_array(1, $payoutTypes) && !in_array(2, $payoutTypes) && $callType == 1) {
                    $error['tokInboundError'] = 'Please select atleast 1 payout';
                } else if (!in_array(3, $payoutTypes) && !in_array(4, $payoutTypes) && $callType == 2) {
                    $error['tokOutboundError'] = 'Please select atleast 1 payout';
                } else if (!in_array(1, $payoutTypes) && !in_array(2, $payoutTypes) && !in_array(3, $payoutTypes) && !in_array(4, $payoutTypes)) {
                    $error['tokOutboundError'] = 'Please select atleast 1 payout';
                }
                if ($callType == 0 || $callType == 1) {
                    /*if (!in_array(1, $payoutTypes) && !in_array(2, $payoutTypes)) {
                        $error['automatedInboundError'] = 'Select automated inbound payout';
                        $error['tokInboundError'] = 'Select dialup inbound payout';
                    } else {*/
                        if (in_array(1, $payoutTypes) && empty($automatedInbound) && $campaignType != 3 && $campaignType != 5)
                            $error['automatedInboundError'] = 'Enter automated inbound payout';
                        if (in_array(2, $payoutTypes) && empty($tokInbound) && $campaignType != 3 && $campaignType != 5)
                            $error['tokInboundError'] = 'Enter dialup inbound payout';
                    /*}*/
                }
                if ($callType == 0 || $callType == 2) {
                    /*if (!in_array(3, $payoutTypes) && !in_array(4, $payoutTypes)) {
                        $error['automatedOutboundError'] = 'Select automated outbound payout';
                        $error['tokOutboundError'] = 'Select dialup outbound payout';
                    } else {*/
                        if (in_array(3, $payoutTypes) && empty($automatedOutboundLocal) && empty($automatedOutboundLong) && $campaignType != 3 && $campaignType != 5)
                            $error['automatedOutboundError'] = 'Enter automated outbound payout';
                        if (in_array(4, $payoutTypes) && empty($tokOutboundLocal) && empty($tokOutboundLong) && $campaignType != 3 && $campaignType != 5)
                            $error['tokOutboundError'] = 'Enter dialup outbound payout';
                    /*}*/
                }
            }

            if ($leadType == 2 && ($forwardPhone == "" || strlen($forwardPhone) != 10)) {
                $error['forwardPhoneError'] = 'Enter forward number';
            }
            if ($leadType == 2 && $callType == "") {
                $error['callTypeError'] = 'Please Select Call type';
            }
            if ($leadType == 1) {
                if ($verified == '' && $unverified == '') {
                    $error['leadsVerifiedUnverifiedError'] = 'Please select atleast one checkbox';
                }
            }

            if ($isRevShare > 0 && $revSharePerc == "") {
                $error['revSharePercError'] = 'Enter rev share percentage';
            }

            if (empty($floorBid)) {
                $error['floorBidError'] = 'Please enter floor bid';
            }

            // Check same campaign name with businesses
            $campaignData = Campaign::where('business_id', $businessesId)
                ->where('campaign_name', $campaignName)
                ->first();
            if ($campaignData) {
                $error['campaignNameError'] = 'Campaign already in database';
            }

            if ($campaignType == 4) {
                $organicData = $request->get('vmOrganicArray');
                $error = $this->validateVmOrganicCampaignFields($organicData, $error);
            }

            if ($campaignType == 5) {
                $bookingData = $request->get('bookingArray');
                $error = $this->validateTokBookingCampaignFields($bookingData, $error);
            }

            if ($campaignCategory == 2) {
                $junkData = $request->get('junkArray');
                $error = $this->validateJunkCategoryFields($junkData, $error);
            } else if ($campaignCategory == 3) {
                $liftData = $request->get('liftingArray');
                $liftData['leadDistance'] = ($leadDistance == 0) ? 'local' : 'long';
                $liftData['minimumDistance'] = $minimumDistance;
                $error = $this->validateLiftCategoryFields($liftData, $error);
            } else if ($campaignCategory == 4) {
                $carData = $request->get('carArray');
                $carData['leadDistance'] = ($leadDistance == 0) ? 'local' : 'long';
                $carData['minimumDistance'] = $minimumDistance;
                $carData['carTransportation'] = $carTransportation;
                $carData['mobileHomeTransportation'] = $mobileHomeTransportation;
                $error = $this->validateCarCategoryFields($carData, $error);
            }

            $eDisplayMoveSize = $this->getUserRoleNames();
            $moveSizeArray = [];
            if (array_key_exists("campaignMoveSize", $request->all())){
                $moveSizeArray = $request->get('campaignMoveSize');
            }

            if (count($moveSizeArray) == 0 && $campaignCategory == 1) {
                $error['movesizeError'] = 'Please select atleast one move size.';
            }
            if ($score <= -1001) {
                $error['scorevalue'] = 'Score will be minimum -1000.';
            }
            $verifiedUnverified = '';
            if ($verified != '' && $unverified != '') {
                $verifiedUnverified = $verified . ',' . $unverified;
            } else if ($verified == '' && $unverified != '') {
                $verifiedUnverified = $unverified;
            } else if ($verified != '' && $unverified == '') {
                $verifiedUnverified = $verified;
            }

            if (count($error) > 0) {
                throw new Exception('Validation Error');
            }

            $campaigntypeDetails = MstCampaignType::distinct()->get(['campaign_type_id', 'campaign_type']);
            $campaigntypeArray = [];
            for ($t = 0; $t < count($campaigntypeDetails); $t++) {
                $campaigntypeArray[$campaigntypeDetails[$t]->campaign_type_id] = $campaigntypeDetails[$t]->campaign_type;
            }

            if ($callType == 0) {
                $callType = 'both';
            } elseif ($callType == 1) {
                $callType = 'inbound';
            } elseif ($callType == 2) {
                $callType = 'outbound';
            }

            $campaignId = Campaign::create([
                'business_id' => $businessesId,
                'lead_category_id' => $campaignCategory,
                'campaign_name' => $campaignName,
                'lead_type_id' => $leadType,
                'forward_number' => ($leadType == 2 && !empty($forwardPhone)) ? $forwardPhone : 0,
                'campaign_type_id' => $campaignType,
                'call_type' => $callType,
                'lead_payout' => 0,
                'lead_status' => $verifiedUnverified,
                'is_active' => $status,
                'payment_type' => 0,
                'daily_lead_limit' => $dailyLeadLimit,
                'daily_budget_limit' => $dailyBudgetLimit,
                'hourly_lead_limit' => $hourlyLimit,
                'move_days_after' => $moveDays,
                'from_exclude_date' => $moveDateFrom,
                'to_exclude_date' => $moveDateTo,
                'additional_score' => $score,
                'subscription_score' => $subscriptionScore,
                'floor_bid' => $floorBid,
                'is_remove_truck_rental' => $is_remove_truck_rental,
                'is_spanish' => $is_spanish,
                'is_rev_share' => ($isRevShare > 0) ? 'yes' : 'no',
                'rev_share_perc' => ($isRevShare > 0) ? $revSharePerc : 0,
                'created_at' => date('Y-m-d H:i:s'),
                'created_user_id' => Auth::user()->id
            ])->campaign_id;

            if (isset($businessesId) && !empty($businessesId)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'business_id',
                    'new_value' => $businessesId,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($campaignCategory) && !empty($campaignCategory)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'lead_category_id',
                    'new_value' => $campaignCategory,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($campaignName) && !empty($campaignName)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'campaign_name',
                    'new_value' => $campaignName,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($leadType) && !empty($leadType)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'lead_type_id',
                    'new_value' => $leadType,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($forwardPhone) && !empty($forwardPhone)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'forward_number',
                    'new_value' => $forwardPhone,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($campaignType) && !empty($campaignType)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'campaign_type_id',
                    'new_value' => $campaignType,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($callType) && !empty($callType)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'call_type',
                    'new_value' => $callType,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }
            $verifiedText = ( $verified == 1 && isset($verified )) ? "active" : "inactive";
            // if (isset($verified) && !empty($verified)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'lead_verify_status',
                    'new_value' => $verifiedText,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            // }
            $unverifiedText = ( $unverified == 0 && isset($unverified )) ? "active" : "inactive";
            // if (isset($unverified) && !empty($unverified)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'lead_unverify_status',
                    'new_value' => $unverifiedText,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            // }
            if ( $campaignType != 7 && $campaignCategory == 1) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'is_remove_truck_rental',
                    'new_value' => $is_remove_truck_rental,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }
            if ( $campaignType != 7) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'is_spanish',
                    'new_value' => $is_spanish,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($status) && !empty($status)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'campaign_status',
                    'new_value' => ($status == "yes" ) ? "active" : ($status == "permenant" ? "permenant" : "inactive"),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($dailyLeadLimit) && !empty($dailyLeadLimit)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'daily_lead_limit',
                    'new_value' => $dailyLeadLimit,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($dailyBudgetLimit) && !empty($dailyBudgetLimit)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'daily_budget_limit',
                    'new_value' => $dailyBudgetLimit,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($hourlyLimit) && !empty($hourlyLimit)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'hourly_lead_limit',
                    'new_value' => $hourlyLimit,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($moveDays) && !empty($moveDays)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'move_days_after',
                    'new_value' => $moveDays,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($moveDateFrom) && !empty($moveDateFrom)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'from_exclude_date',
                    'new_value' => $moveDateFrom,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($moveDateTo) && !empty($moveDateTo)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'to_exclude_date',
                    'new_value' => $moveDateTo,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if ($campaignType != "7") {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'campaign_score',
                    'new_value' => $score,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];

                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'subscription_score',
                    'new_value' => $subscriptionScore,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($floorBid) && !empty($floorBid)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'floor_bid',
                    'new_value' => $floorBid,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            $automatedOutbound = NULL;
            $tokOutbound = NULL;
            if ($leadDistance == 0) {
                if ($automatedOutboundLocal > 0 || $campaignType == 3) {
                    $automatedOutbound = $automatedOutboundLocal;
                }
                if ($tokOutboundLocal > 0 || $campaignType == 3) {
                    $tokOutbound = $tokOutboundLocal;
                }
            } else if ($leadDistance == 1) {
                if ($automatedOutboundLong > 0 || $campaignType == 3) {
                    $automatedOutbound = $automatedOutboundLong;
                }
                if ($tokOutboundLong > 0 || $campaignType == 3) {
                    $tokOutbound = $tokOutboundLong;
                }
            }

            CampaignCallPayout::create([
                'campaign_id' => $campaignId,
                'real_time_call_limit' => $callLimit,
                'automated_inbound' => ($automatedInbound > 0 || $campaignType == 3) ? $automatedInbound : NULL,
                'inbound_call' => ($tokInbound > 0 || $campaignType == 3) ? $tokInbound : NULL,
                'automated_outbound' => $automatedOutbound,
                'outbound_call' => $tokOutbound,
                'created_at' => date('Y-m-d H:i:s')
            ])->campaign_call_payout_id;

            if (isset($callLimit) && !empty($callLimit)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'real_time_call_limit',
                    'new_value' => $callLimit,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($automatedInbound) && !empty($automatedInbound)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'automated_inbound',
                    'new_value' => $automatedInbound,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($tokInbound) && !empty($tokInbound)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'inbound_call',
                    'new_value' => $tokInbound,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($automatedOutboundLocal) && !empty($automatedOutboundLocal) || isset($automatedOutboundLong) && !empty($automatedOutboundLong)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'payout',
                    'new_value' => ($leadDistance == 0) ? $automatedOutboundLocal : $automatedOutboundLong,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (isset($tokOutboundLocal) && !empty($tokOutboundLocal) || isset($tokOutboundLong) && !empty($tokOutboundLong)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'outbound_call',
                    'new_value' => ($leadDistance == 0) ? $tokOutboundLocal : $tokOutboundLong,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if ($campaignCategory != 2) {
                CampaignMoving::create([
                    'campaign_id' => $campaignId,
                    'move_type' => ($leadDistance == 0) ? 'local' : 'long',
                    'min_distance' => $minimumDistance,
                    'created_at' => date('Y-m-d H:i:s'),
                    'created_user_id' => Auth::user()->id
                ])->campaign_moving_id;

                if (isset($leadDistance) && !empty($leadDistance) && $campaignType != "7") {
                    $logArray[] = [
                        'created_user_id' => Auth::user()->id,
                        'campaign_id' => $campaignId,
                        'field_name' => 'lead_distance_type',
                        'new_value' => ($leadDistance == 0) ? 'local' : 'long',
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $request->ip()
                    ];
                }
                if($campaignType != "7"){
                    $logArray[] = [
                        'created_user_id' => Auth::user()->id,
                        'campaign_id' => $campaignId,
                        'field_name' => 'min_distance',
                        'new_value' => $minimumDistance,
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $request->ip()
                    ];
                }
            }

            if (is_array($moveSizeArray) && count($moveSizeArray) > 0) {
                foreach ($moveSizeArray as $moveSize) {
                    CampaignMoveSize::create([
                        'campaign_id' => $campaignId,
                        'move_size_id' => $moveSize,
                        'created_at' => date('Y-m-d H:i:s')
                    ])->campaign_move_size_id;
                }
            }

            if (is_array($moveSizeArray) && count($moveSizeArray) > 0) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'move_size_id',
                    'new_value' => implode(',', $moveSizeArray),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            //VM Organic Campaign Data Start
            if ($campaignType == 4) {
                $organicData = $request->get('vmOrganicArray');
                $vmOrganicData = $this->insertUpdateVmOrganicData($campaignId, $organicData, Auth::user()->id, $request->ip());
                foreach ($vmOrganicData as $vmOrganicLog) {
                    $logArray[] = $vmOrganicLog;
                }
            } else {
                //Remove VM Organic Data
                CampaignOrganic::where('campaign_id', $campaignId)->delete();
            }
            //VM Organic Campaign Data End

            if ($campaignCategory == 2) {
                //Added Junk Removal Data
                $junkRemovalData = $this->insertUpdateCategoryData($campaignId, $junkData, $campaignCategory, Auth::user()->id, $request->ip());
                foreach ($junkRemovalData as $junkRemoval) {
                    $logArray[] = $junkRemoval;
                }
            } else if ($campaignCategory == 3) {
                //Added Heavy Lifting Data
                $heavyLiftData = $this->insertUpdateCategoryData($campaignId, $liftData, $campaignCategory, Auth::user()->id, $request->ip());
                foreach ($heavyLiftData as $heavyLift) {
                    $logArray[] = $heavyLift;
                }
            } else if ($campaignCategory == 4) {
                //Added Heavy Lifting Data
                $carTransportationData = $this->insertUpdateCategoryData($campaignId, $carData, $campaignCategory, Auth::user()->id, $request->ip());
                foreach ($carTransportationData as $carTransportation) {
                    $logArray[] = $carTransportation;
                }
            } else {
                //Remove Junk Removal and Heavy Lifting Data
                CampaignJunkType::where('campaign_id', $campaignId)->delete();
                CampaignJunkSubType::where('campaign_id', $campaignId)->delete();
                CampaignHeavyLifting::where('campaign_id', $campaignId)->delete();
                CampaignCarTransport::where('campaign_id', $campaignId)->delete();
                CampaignCarTransportType::where('campaign_id', $campaignId)->delete();
            }

            if (!empty($fromState)) {
                $stateData = [];
                foreach ($fromState as $state) {
                    $stateData[] = [
                        'campaign_id' => $campaignId,
                        'location_coverage' => 'state',
                        'location_type' => 'source',
                        'value' => $state,
                        'created_at' => date('Y-m-d H:i:s'),
                    ];
                }
                $stateData = collect($stateData);
                foreach($stateData->chunk(100) as $chunk){
                    CampaignLocation::insert($chunk->toArray());
                }
                $fromStateString = implode(",", $fromState);
                $logDetails .= $fromStateString . '</li>';
            }

            if (isset($fromState) && !empty($fromState)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'from_state',
                    'new_value' => implode(",", $fromState),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (!empty($fromAreaCode)) {
                $fromAreaCode = str_replace("\n", ",", $fromAreaCode);
                $fromAreaCode = preg_replace("/[^0-9,]/", "", $fromAreaCode);
                $fromAreaCode = explode(',', $fromAreaCode);
                $areaCodeData = [];
                foreach ($fromAreaCode as $areacode) {
                    $areaCodeData[] = [
                        'campaign_id' => $campaignId,
                        'location_coverage' => 'areacode',
                        'location_type' => 'source',
                        'value' => $areacode,
                        'created_at' => date('Y-m-d H:i:s'),
                    ];
                }
                $areaCodeData = collect($areaCodeData);
                foreach($areaCodeData->chunk(100) as $chunk){
                    CampaignLocation::insert($chunk->toArray());
                }
            }

            if (isset($fromAreaCode) && !empty($fromAreaCode)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'from_areacode',
                    'new_value' => implode(",", $fromAreaCode),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (!empty($fromZipCode)) {
                $fromZipCode = str_replace("\n", ",", $fromZipCode);
                $fromZipCode = preg_replace("/[^0-9,]/", "", $fromZipCode);
                $fromZipCode = explode(',', $fromZipCode);
                $zipCodeData = [];
                foreach ($fromZipCode as $zipcode) {
                    $zipCodeData[] = [
                        'campaign_id' => $campaignId,
                        'location_coverage' => 'zipcode',
                        'location_type' => 'source',
                        'value' => $zipcode,
                        'created_at' => date('Y-m-d H:i:s'),
                    ];
                }
                $zipCodeData = collect($zipCodeData);
                foreach($zipCodeData->chunk(100) as $chunk){
                    CampaignLocation::insert($chunk->toArray());
                }
            }

            if (isset($fromZipCode) && !empty($fromZipCode)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'from_zipcode',
                    'new_value' => implode(",", $fromZipCode),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (!empty($toState)) {
                $stateData = [];
                foreach ($toState as $state) {
                    $stateData[] = [
                        'campaign_id' => $campaignId,
                        'location_coverage' => 'state',
                        'location_type' => 'destination',
                        'value' => $state,
                        'created_at' => date('Y-m-d H:i:s'),
                    ];
                }
                $stateData = collect($stateData);
                foreach($stateData->chunk(100) as $chunk){
                    CampaignLocation::insert($chunk->toArray());
                }
            }

            if (isset($toState) && !empty($toState)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'to_state',
                    'new_value' => implode(",", $toState),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (!empty($toAreaCode)) {
                $toAreaCode = str_replace("\n", ",", $toAreaCode);
                $toAreaCode = preg_replace("/[^0-9,]/", "", $toAreaCode);
                $toAreaCode = explode(',', $toAreaCode);
                $areaCodeData = [];
                foreach ($toAreaCode as $areacode) {
                    $areaCodeData[] = [
                        'campaign_id' => $campaignId,
                        'location_coverage' => 'areacode',
                        'location_type' => 'destination',
                        'value' => $areacode,
                        'created_at' => date('Y-m-d H:i:s'),
                    ];
                }
                $areaCodeData = collect($areaCodeData);
                foreach($areaCodeData->chunk(100) as $chunk){
                    CampaignLocation::insert($chunk->toArray());
                }
            }

            if (isset($toAreaCode) && !empty($toAreaCode)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'to_areacode',
                    'new_value' => implode(",", $toAreaCode),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (!empty($toZipCode)) {
                $toZipCode = str_replace("\n", ",", $toZipCode);
                $toZipCode = preg_replace("/[^0-9,]/", "", $toZipCode);
                $toZipCode = explode(',', $toZipCode);
                $zipCodeData = [];
                foreach ($toZipCode as $zipcode) {
                    $zipCodeData[] = [
                        'campaign_id' => $campaignId,
                        'location_coverage' => 'zipcode',
                        'location_type' => 'destination',
                        'value' => $zipcode,
                        'created_at' => date('Y-m-d H:i:s'),
                    ];
                }
                $zipCodeData = collect($zipCodeData);
                foreach($zipCodeData->chunk(100) as $chunk){
                    CampaignLocation::insert($chunk->toArray());
                }
            }

            if (isset($toZipCode) && !empty($toZipCode)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'to_zipcode',
                    'new_value' => implode(",", $toZipCode),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (!empty($emailTemplatData)) {
                if (!empty($emailTemplatData['emailAddress'])) {
                    if ($emailTemplatData['emailAddress'][0] != '') {
                        $campaignLeadDelieveryTypeId = CampaignLeadDeliveryType::create([
                            'campaign_id' => $campaignId,
                            'lead_delivery_type_id' => 1,
                            'created_at' => date('Y-m-d H:i:s'),
                            'created_user_id' => Auth::user()->id
                        ])->campaign_lead_delivery_type_id;

                        $leadDeliveryEmailTemplate = LeadDeliveryEmailTemplate::get()->toArray();
                        $leadDeliveryEmailTemplateId = array();
                        for ($t = 0; $t < count($leadDeliveryEmailTemplate); $t++) {
                            $leadDeliveryEmailTemplateId[$leadDeliveryEmailTemplate[$t]['lead_delivery_email_template_id']] = $leadDeliveryEmailTemplate[$t]['template_name'];
                        }
                        for ($i = 0; $i < count($emailTemplatData['emailAddress']); $i++) {
                            $emailAddress = $emailTemplatData['emailAddress'][$i];
                            $emailTemplate = $emailTemplatData['emailTemplate'][$i];

                            CampaignEmailDelivery::create([
                                'campaign_lead_delivery_type_id' => $campaignLeadDelieveryTypeId,
                                'to_email' => $emailAddress,
                                'lead_delivery_email_template_id' => $emailTemplate,
                                'created_at' => date('Y-m-d H:i:s'),
                                'created_user_id' => Auth::user()->id
                            ]);
                            $logEmail .= '{email:' . $emailAddress . ', template:' . $leadDeliveryEmailTemplateId[$emailTemplate] . '} ';
                        }
                        $logArray[] = [
                            'created_user_id' => Auth::user()->id,
                            'campaign_id' => $campaignId,
                            'field_name' => 'lead_delivery_email',
                            'new_value' => $logEmail,
                            'created_at' => date('Y-m-d H:i:s'),
                            'ip' => $request->ip()
                        ];
                    }
                }
            }
            if (!empty($smsPhonesData)) {
                if (!empty($smsPhonesData['smsPhones'])) {
                    if ($smsPhonesData['smsPhones'][0] != '') {
                        $campaignLeadDelieveryTypeId = CampaignLeadDeliveryType::create([
                            'campaign_id' => $campaignId,
                            'lead_delivery_type_id' => 3,
                            'created_at' => date('Y-m-d H:i:s'),
                            'created_user_id' => Auth::user()->id
                        ])->campaign_lead_delivery_type_id;

                        $leadDeliverySMSCarrier = LeadDeliverySMSCarrier::get()->toArray();
                        $leadDeliverySMSCarrierId = array();
                        for ($t = 0; $t < count($leadDeliverySMSCarrier); $t++) {
                            $leadDeliverySMSCarrierId[$leadDeliverySMSCarrier[$t]['lead_delivery_sms_carrier_id']] = $leadDeliverySMSCarrier[$t]['carrier'];
                        }
                        for ($i = 0; $i < count($smsPhonesData['smsPhones']); $i++) {
                            $smsPhone = $smsPhonesData['smsPhones'][$i];
                            $carriers = $smsPhonesData['carriers'][$i];

                            CampaignSmsDelivery::create([
                                'campaign_lead_delivery_type_id' => $campaignLeadDelieveryTypeId,
                                'phone' => $smsPhone,
                                'lead_delivery_sms_carrier_id' => $carriers,
                                'created_at' => date('Y-m-d H:i:s'),
                                'created_user_id' => Auth::user()->id
                            ]);
                            //str_replace("-", "",str_replace(")", "",str_replace("(", "", $smsPhone))),
                            $logSms .= '{phone:' . $smsPhone . ', carrier:' . $leadDeliverySMSCarrierId[$carriers] . '} ';
                        }
                        $logArray[] = [
                            'created_user_id' => Auth::user()->id,
                            'campaign_id' => $campaignId,
                            'field_name' => 'lead_delivery_sms',
                            'new_value' => $logSms,
                            'created_at' => date('Y-m-d H:i:s'),
                            'ip' => $request->ip()
                        ];
                    }
                }
            }
            $newcrmIdArr = $deliveryCrmNames = [];
            $deliveryCrms = DeliveryCrm::get()->toArray();
            foreach ($deliveryCrms as $key => $value) {
                $deliveryCrmNames[$value['delivery_crm_id']] = $value['name'];
            }
            if (!empty($crmData)) {
                if (!empty($crmData['crmUrls'])) {
                    if ($crmData['crmUrls'][0] != '') {
                        $campaignLeadDelieveryTypeId = CampaignLeadDeliveryType::create([
                            'campaign_id' => $campaignId,
                            'lead_delivery_type_id' => 4,
                            'created_at' => date('Y-m-d H:i:s'),
                            'created_user_id' => Auth::user()->id
                        ])->campaign_lead_delivery_type_id;
                        for ($i = 0; $i < count($crmData['crmUrls']); $i++) {
                            $crmUrl = $crmData['crmUrls'][$i];
                            /*if ($crmUrl == '') {
                                continue;
                            }*/
                            $crmIds = $crmData['crmNames'][$i];
                            if ($crmIds != "") {
                                $campaignDeliveryArr = [
                                    'campaign_lead_delivery_type_id' => $campaignLeadDelieveryTypeId,
                                    'delivery_crm_id' => $crmIds,
                                    'post_url' => $crmUrl,
                                    'campaign_id' => $campaignId,
                                    'user_id' => Auth::user()->id,
                                    'created_at' => date('Y-m-d H:i:s'),
                                    'updated_at' => date('Y-m-d H:i:s'),
                                ];
                                $logCRM .= '{url:' . $crmUrl . ', name:' . $deliveryCrmNames[$crmIds] . '} ';
                                //echo '<pre>';print_r($campaignDeliveryArr);
                                $newcrm = CampaignCrmDelivery::create($campaignDeliveryArr);

                                $newcrmIdArr[] = $newcrm->campaign_crm_delivery_id;
                                if (array_key_exists('crm_defauldata', $crmData)) {
                                    if (array_key_exists($i, $crmData['crm_defauldata'])) {
                                        $crm_defauldata = $crmData['crm_defauldata'][$i];
                                        foreach ($crm_defauldata as $key => $value) {
                                            LeadDeliveryCrmDefaultFields::create([
                                                'campaign_crm_delivery_id' => $newcrm->campaign_crm_delivery_id,
                                                'delivery_mapping_fields_id' => $value['mappingfieldid'],
                                                'variable_name' => $value['variable_name'],
                                                'variable_value' => $value['variable_value']
                                            ]);
                                        }
                                    }
                                }
                            }
                        }
                        $newdefaultfields = LeadDeliveryCrmDefaultFields::whereIn('campaign_crm_delivery_id', $newcrmIdArr)->get()->toArray();
                        foreach ($newdefaultfields as $key1 => $value1) {
                            $logCRM .= '{defaultname:' . $value1['variable_name'] . ', defaultvalue:' . $value1['variable_value'] . '} ';
                        }
                        $logArray[] = [
                            'created_user_id' => Auth::user()->id,
                            'campaign_id' => $campaignId,
                            'field_name' => 'lead_delivery_crm',
                            'new_value' => $logCRM,
                            'created_at' => date('Y-m-d H:i:s'),
                            'ip' => $request->ip()
                        ];
                    }
                }
            }

            if (!empty($request->get('campaignTimeHours')['day0'])) {
                $dayArray0 = json_decode($request->get('campaignTimeHours')['day0'], true);
                $limit0 = $request->get('campaignTimeHours')['limit0'];
                $isPauseSun = $request->get('campaignTimeHours')['isPauseSun'];
                $isPauseSun = ($isPauseSun > 0) ? 'yes' : 'no';
                $logHoursDetails .= '{day:Sun';
                foreach ($dayArray0 as $timeHours) {
                    $startTime = CampaignScheule::hours_setup($timeHours['start'], 'start');
                    $endTime = CampaignScheule::hours_setup($timeHours['end'], 'end');
                    if ($startTime == 0 && $endTime == 0) {
                        $endTime = 23;
                    }
                    $campaignHoursData[] = [
                        'campaign_id' => $campaignId,
                        'day' => 7,
                        'start_hour' => $startTime,
                        'end_hour' => $endTime,
                        'daily_limit' => $limit0,
                        'is_pause' => $isPauseSun,
                        'status' => 'active',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    $logHoursDetails .= ', ' . $startTime . 'to' . $endTime;
                }
                $logHoursDetails .= ', daily_limit:' . $limit0 . ', is_pause:' . $isPauseSun . '}';
            }
            if (!empty($request->get('campaignTimeHours')['day1'])) {
                $dayArray1 = json_decode($request->get('campaignTimeHours')['day1'], true);
                $limit1 = $request->get('campaignTimeHours')['limit1'];
                $isPauseMon = $request->get('campaignTimeHours')['isPauseMon'];
                $isPauseMon = ($isPauseMon > 0) ? 'yes' : 'no';
                $logHoursDetails .= '{day:Mon';
                foreach ($dayArray1 as $timeHours) {
                    $startTime = CampaignScheule::hours_setup($timeHours['start'], 'start');
                    $endTime = CampaignScheule::hours_setup($timeHours['end'], 'end');
                    if ($startTime == 0 && $endTime == 0) {
                        $endTime = 23;
                    }
                    $campaignHoursData[] = [
                        'campaign_id' => $campaignId,
                        'day' => 1,
                        'start_hour' => $startTime,
                        'end_hour' => $endTime,
                        'daily_limit' => $limit1,
                        'is_pause' => $isPauseMon,
                        'status' => 'active',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    $logHoursDetails .= ', ' . $startTime . 'to' . $endTime;
                }
                $logHoursDetails .= ', daily_limit:' . $limit1 . ', is_pause:' . $isPauseMon . '}';
            }
            if (!empty($request->get('campaignTimeHours')['day2'])) {
                $dayArray2 = json_decode($request->get('campaignTimeHours')['day2'], true);
                $limit2 = $request->get('campaignTimeHours')['limit2'];
                $isPauseTue = $request->get('campaignTimeHours')['isPauseTue'];
                $isPauseTue = ($isPauseTue > 0) ? 'yes' : 'no';
                $logHoursDetails .= '{day:Tue';
                foreach ($dayArray2 as $timeHours) {
                    $startTime = CampaignScheule::hours_setup($timeHours['start'], 'start');
                    $endTime = CampaignScheule::hours_setup($timeHours['end'], 'end');
                    if ($startTime == 0 && $endTime == 0) {
                        $endTime = 23;
                    }
                    $campaignHoursData[] = [
                        'campaign_id' => $campaignId,
                        'day' => 2,
                        'start_hour' => $startTime,
                        'end_hour' => $endTime,
                        'daily_limit' => $limit2,
                        'is_pause' => $isPauseTue,
                        'status' => 'active',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    $logHoursDetails .= ', ' . $startTime . 'to' . $endTime;
                }
                $logHoursDetails .= ', daily_limit:' . $limit2 . ', is_pause:' . $isPauseTue . '}';
            }
            if (!empty($request->get('campaignTimeHours')['day3'])) {
                $dayArray3 = json_decode($request->get('campaignTimeHours')['day3'], true);
                $limit3 = $request->get('campaignTimeHours')['limit3'];
                $isPauseWed = $request->get('campaignTimeHours')['isPauseWed'];
                $isPauseWed = ($isPauseWed > 0) ? 'yes' : 'no';
                $logHoursDetails .= '{day:Wed';
                foreach ($dayArray3 as $timeHours) {
                    $startTime = CampaignScheule::hours_setup($timeHours['start'], 'start');
                    $endTime = CampaignScheule::hours_setup($timeHours['end'], 'end');
                    if ($startTime == 0 && $endTime == 0) {
                        $endTime = 23;
                    }
                    $campaignHoursData[] = [
                        'campaign_id' => $campaignId,
                        'day' => 3,
                        'start_hour' => $startTime,
                        'end_hour' => $endTime,
                        'daily_limit' => $limit3,
                        'is_pause' => $isPauseWed,
                        'status' => 'active',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    $logHoursDetails .= ', ' . $startTime . 'to' . $endTime;
                }
                $logHoursDetails .= ', daily_limit:' . $limit3 . ', is_pause:' . $isPauseWed . '}';
            }
            if (!empty($request->get('campaignTimeHours')['day4'])) {
                $dayArray4 = json_decode($request->get('campaignTimeHours')['day4'], true);
                $limit4 = $request->get('campaignTimeHours')['limit4'];
                $isPauseThu = $request->get('campaignTimeHours')['isPauseThu'];
                $isPauseThu = ($isPauseThu > 0) ? 'yes' : 'no';
                $logHoursDetails .= '{day:Thu';
                foreach ($dayArray4 as $timeHours) {
                    $startTime = CampaignScheule::hours_setup($timeHours['start'], 'start');
                    $endTime = CampaignScheule::hours_setup($timeHours['end'], 'end');
                    if ($startTime == 0 && $endTime == 0) {
                        $endTime = 23;
                    }
                    $campaignHoursData[] = [
                        'campaign_id' => $campaignId,
                        'day' => 4,
                        'start_hour' => $startTime,
                        'end_hour' => $endTime,
                        'daily_limit' => $limit4,
                        'is_pause' => $isPauseThu,
                        'status' => 'active',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    $logHoursDetails .= ', ' . $startTime . 'to' . $endTime;
                }
                $logHoursDetails .= ', daily_limit:' . $limit4 . ', is_pause:' . $isPauseThu . '}';
            }
            if (!empty($request->get('campaignTimeHours')['day5'])) {
                $dayArray5 = json_decode($request->get('campaignTimeHours')['day5'], true);
                $limit5 = $request->get('campaignTimeHours')['limit5'];
                $isPauseFri = $request->get('campaignTimeHours')['isPauseFri'];
                $isPauseFri = ($isPauseFri > 0) ? 'yes' : 'no';
                $logHoursDetails .= '{day:Fri';
                foreach ($dayArray5 as $timeHours) {
                    $startTime = CampaignScheule::hours_setup($timeHours['start'], 'start');
                    $endTime = CampaignScheule::hours_setup($timeHours['end'], 'end');
                    if ($startTime == 0 && $endTime == 0) {
                        $endTime = 23;
                    }
                    $campaignHoursData[] = [
                        'campaign_id' => $campaignId,
                        'day' => 5,
                        'start_hour' => $startTime,
                        'end_hour' => $endTime,
                        'daily_limit' => $limit5,
                        'is_pause' => $isPauseFri,
                        'status' => 'active',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    $logHoursDetails .= ', ' . $startTime . 'to' . $endTime;
                }
                $logHoursDetails .= ', daily_limit:' . $limit5 . ', is_pause:' . $isPauseFri . '}';
            }
            if (!empty($request->get('campaignTimeHours')['day6'])) {
                $dayArray6 = json_decode($request->get('campaignTimeHours')['day6'], true);
                $limit6 = $request->get('campaignTimeHours')['limit6'];
                $isPauseSat = $request->get('campaignTimeHours')['isPauseSat'];
                $isPauseSat = ($isPauseSat > 0) ? 'yes' : 'no';
                $logHoursDetails .= '{day:Sat';
                foreach ($dayArray6 as $timeHours) {
                    $startTime = CampaignScheule::hours_setup($timeHours['start'], 'start');
                    $endTime = CampaignScheule::hours_setup($timeHours['end'], 'end');
                    if ($startTime == 0 && $endTime == 0) {
                        $endTime = 23;
                    }
                    $campaignHoursData[] = [
                        'campaign_id' => $campaignId,
                        'day' => 6,
                        'start_hour' => $startTime,
                        'end_hour' => $endTime,
                        'daily_limit' => $limit6,
                        'is_pause' => $isPauseSat,
                        'status' => 'active',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    $logHoursDetails .= ', ' . $startTime . 'to' . $endTime;
                }
                $logHoursDetails .= ', daily_limit:' . $limit6 . ', is_pause:' . $isPauseSat . '}';
            }

            if (is_array($campaignHoursData) && count($campaignHoursData) > 0) {
                CampaignScheule::insert($campaignHoursData);

                if (strlen($logHoursDetails) > 0) {
                    $logArray[] = [
                        'created_user_id' => Auth::user()->id,
                        'campaign_id' => $campaignId,
                        'field_name' => 'campaign_timing',
                        'new_value' => $logHoursDetails,
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $request->ip()
                    ];
                }
            }

            if (count($leadDialerCampaign) > 0) {
                $leaddialerCamps = [];
                for ($v = 0; $v < count($leadDialerCampaign); $v++) {
                    $leaddialerCamps[$v] = [
                        'campaign_id' => $leadDialerCampaign[$v],
                        'live_transfer_campaign_id' => $campaignId,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'lead_dialer_campaign',
                    'new_value' => implode(",", $leadDialerCampaign),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
                CampaignLeadDialer::insert($leaddialerCamps);
            }
            if ($campaignType == 3) {
                $campaignDialerPoints = [
                    'campaign_id' => $campaignId,
                    'custom_points' => $customPoint,
                    'created_at' => date('Y-m-d H:i:s')
                ];
                CampaignDialerPoints::insert($campaignDialerPoints);
            }
            if (count($logArray) > 0) {
                CampaignUpdateLog::insert($logArray);
            }

            $sourceNameArray = [];
            if (count($sourceArray) > 0) { foreach ($sourceArray as $source) {
                $sourceNameArray[] = [
                    'campaign_id' => $campaignId,
                    'lead_source_id' => $source['source'],
                    'user_id' => Auth::user()->id,
                    'created_at' => date("Y-m-d H:i:s"),
                ];
            } }
            if (count($sourceNameArray) > 0) {
                CampaignLeadSource::insert($sourceNameArray);
            }

            $responseStatus = 1;
            $message = 'Success';
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        $jsonResponse = [
            'status' => $responseStatus,
            'message' => $message,
            'error' => $error,
        ];
        //dd($jsonResponse);
        return response()->json($jsonResponse);
    }

    //load view of edit campaign
    public function edit($id)
    {
        $businessDetails = Business::distinct()->get(['business_id', 'business_name']);
        //get all state records from mst_zipcode table
        $stateDetails = MstZipcode::distinct()->get(['state']);
        //get all move size records from mst_move_size table
        $movesizeDetails = MstMoveSize::distinct()->get(['move_size_id', 'move_size']);
        //get all campaign type records from mst_zipcode table
        $campaigntypeDetails = MstCampaignType::distinct()->get(['campaign_type_id', 'campaign_type']);
        //get all category records from mst_campaign_type table
        $categoryDetails = MstLeadCategory::distinct()->get(['lead_category_id', 'lead_category_name']);
        //get all category records from mst_lead_type table
        $leadtypeDetails = MstLeadType::distinct()->get(['lead_type_id', 'lead_type']);
        $junktypeDetails = MstJunkType::distinct()->get(['junk_type_id', 'junk_type']);
        $junksubtypeDetails = MstJunkSubType::distinct()->get(['junk_sub_type_id', 'junk_sub_type']);
        $heavyLiftingTypeDetails = MstHeavyLiftingType::distinct()->get(['heavy_lifting_type_id', 'heavy_lifting_type']);
        $carTypeDetails = MstCarType::distinct()->get(['car_type_id', 'car_type']);
        $smsCarrier = LeadDeliverySMSCarrier::distinct()->get(['lead_delivery_sms_carrier_id', 'carrier']);

        $campaignId = CommonFunctions::customeDecryption($id);
        // campaign Details
        $campaignDetails = Campaign::with('campaignMoving')
            ->with('campaignMoveSize')
            ->with('campaignCallPayout')
            ->with('campaignScheule')
            ->with('campaignJunkType')
            ->with('campaignJunkSubType')
            ->with('campaignHeavyLifting')
            ->with('campaignCarTransport')
            ->with('campaignLocation')
            ->with('campaignleaddialer')
            ->with('campaignDialerPoints')
            ->with('campaignCarTransportType')
            ->where('campaign_id', $campaignId)->first();

        $organicDataArr = array();
        $organicCampaignData = CampaignOrganic::where('campaign_id', $campaignId)->get()->toArray();
        if (count($organicCampaignData) > 0) {
            // $organicCampaignData[0]['services_offer'] = explode(",", $organicCampaignData[0]['services_offer']);
            $organicCampaignData[0]['serve_area'] = explode(",", $organicCampaignData[0]['serve_area']);
            $organicDataArr = $organicCampaignData[0];
        }
        foreach ($businessDetails as $bKey => $bVal) {
            if ($campaignDetails->business_id == $bVal->campaign_id) {
                if ($organicDataArr['licence'] == "") {
                    $organicDataArr['licence'] = $bVal->licence;
                }
            }
        }

        if ($campaignDetails->campaign_type_id == 5) {
            $emailTemplate = LeadDeliveryEmailTemplate::where('is_booking_template', '1')->where('lead_category_id', $campaignDetails->lead_category_id)->get(['lead_delivery_email_template_id', 'template_name']);
        } else {
            $emailTemplate = LeadDeliveryEmailTemplate::where('is_booking_template', '0')->where('lead_category_id', $campaignDetails->lead_category_id)->get(['lead_delivery_email_template_id', 'template_name']);
        }

        $leadDelieveryTypeDetails = CampaignLeadDeliveryType::where('campaign_id', $campaignId)
            ->with('campaignEmailDelivery')
            ->with('campaignSmsDelivery')
            ->get();

        $crmLists = DeliveryCrm::get();
        // $campaignCrmDelivery = CampaignCrmDelivery::with('LeadDeliveryCrmDefaultFields')->where('campaign_id', $campaignId)->get()->toArray();
        $campaignCrmDeliveryold = CampaignCrmDelivery::with(['LeadDeliveryCrmDefaultFields', 'LeadDeliveryCrmDefaultFields.deliveryMappingfieldsdata'])->where('campaign_id', $campaignId)->get()->toArray();
        $campaignCrmDeliveryArr = $default_arr = $campaignCrmDelivery = $leaddefaultdataArr = [];
        foreach ($campaignCrmDeliveryold as $key => $value) {
            $campaignCrmDeliveryArr[$value['campaign_crm_delivery_id']] =  $value['lead_delivery_crm_default_fields'];
            foreach ($value['lead_delivery_crm_default_fields'] as $key1 => $value1) {
                if ($value1['delivery_mappingfieldsdata']['is_default'] == "Default value") {
                    $default_arr[$key1] = $value1;
                }
            }
            unset($value['lead_delivery_crm_default_fields']);
            $campaignCrmDelivery[$key]  = $value;
            $campaignCrmDelivery[$key]['lead_delivery_crm_default_fields']  = $default_arr;
        }

        $allCampaignDetails = Campaign::where('business_id', $campaignDetails->business_id)->get()->toArray();

        $originDetails = MstLeadSource::get(['lead_source_id', 'lead_source_name']);
        $campaignSourceDetails = CampaignLeadSource::where('campaign_id', $campaignId)->get(['lead_source_id'])->toArray();
        $campaignSourceArray = [];
        if (count($campaignSourceDetails) > 0) { foreach ($campaignSourceDetails as $campaignSource) {
            $campaignSourceArray[] = $campaignSource['lead_source_id'];
        } }

        // echo '<pre>';print_r($campaignCrmDelivery );  die;
        // dd($organicDataArr);
        return view("campaign.edit")->with([
            'businessDetails' => $businessDetails,
            'stateDetails' => $stateDetails,
            'movesizeDetails' => $movesizeDetails,
            'campaigntypeDetails' => $campaigntypeDetails,
            'categoryDetails' => $categoryDetails,
            'leadtypeDetails' => $leadtypeDetails,
            'junktypeDetails' => $junktypeDetails,
            'junksubtypeDetails' => $junksubtypeDetails,
            'heavyLiftingTypeDetails' => $heavyLiftingTypeDetails,
            'carTypeDetails' => $carTypeDetails,
            'crmLists' => $crmLists,
            'emailTemplate' => $emailTemplate,
            'smsCarrier' => $smsCarrier,
            'campaignCrmDelivery' => $campaignCrmDelivery,
            'campaignDetails' => $campaignDetails,
            'leadDelieveryTypeDetails' => $leadDelieveryTypeDetails,
            'organicData' => $organicDataArr,
            'allCampaignDetails' => $allCampaignDetails,
            'originDetails' => $originDetails,
            'campaignSourceArray' => $campaignSourceArray
        ]);
    }

    public function update(Request $request)
    {
        $responseStatus = 0;
        $message = 'Failure';
        $error = $logArray = $campaignHoursData = array();
        $logDetails = $logPaymentDetails = $logOldEmail = $logEmail = $logOldSms = $logSms = $logOldHoursDetails = $logHoursDetails = $logOldCRM = $logCRM = '';
        try {
            //print_r( $request->all()); die;
            $campaignId = $request->get('campaignArray')['campaignId'];
            $campaignCategory = $request->get('campaignArray')['campaignCategory'];
            $businessesId = $request->get('campaignArray')['businessesId'];
            $campaignName = $request->get('campaignArray')['campaignName'];
            $campaignType = $request->get('campaignArray')['campaignType'];
            $minimumDistance = $request->get('campaignArray')['minimumDistance'];
            $score =  (isset($request->get('campaignArray')['score'])) ? $request->get('campaignArray')['score'] : 0;
            $subscriptionScore = (isset($request->get('campaignArray')['subscriptionScore'])) ? $request->get('campaignArray')['subscriptionScore'] : 0;
            $oldScore = $request->get('campaignArray')['oldScore'];
            $leadType = $request->get('campaignArray')['leadType'];
            $forwardPhone = $request->get('campaignArray')['forwardPhone'];
            $callType = $request->get('campaignArray')['callType'];
            $callLimit = $request->get('campaignArray')['callLimit'];
            $leadDistance = $request->get('campaignArray')['leadDistance'];
            $leadDistance = (!empty($leadDistance)) ? $leadDistance : 0;
            $payoutType = $request->get('campaignArray')['payoutTypes'];
            $payoutTypes = explode(",", $payoutType);
            $automatedOutboundLocal = $request->get('campaignArray')['automatedOutboundLocal'];
            $automatedOutboundLong = $request->get('campaignArray')['automatedOutboundLong'];
            $automatedInbound = $request->get('campaignArray')['automatedInbound'];
            $tokOutboundLocal = $request->get('campaignArray')['tokOutboundLocal'];
            $tokOutboundLong = $request->get('campaignArray')['tokOutboundLong'];
            $tokInbound = $request->get('campaignArray')['tokInbound'];
            $carTransportation = $request->get('campaignArray')['carTransportation'];
            $mobileHomeTransportation = $request->get('campaignArray')['mobileHomeTransportation'];
            $moveDays = $request->get('campaignArray')['moveDays'];
            $applyExcludedDate = $request->get('campaignArray')['applyExcludedDate'];
            $moveDateFrom = ($applyExcludedDate > 0) ? $request->get('campaignArray')['moveDateFrom'] : null;
            $moveDateTo = ($applyExcludedDate > 0) ? $request->get('campaignArray')['moveDateTo'] : null;
            $verified = $request->get('campaignArray')['verified'];
            $unverified = $request->get('campaignArray')['unverified'];
            $is_remove_truck_rental = $request->get('campaignArray')['is_remove_truck_rental'];
            $is_spanish = $request->get('campaignArray')['is_spanish'];
            $status =  $request->get('campaignArray')['status'];
            $leadDialerCampaign = ($campaignType == '3' && isset($request->get('campaignArray')['leadDialerCampaign'])) ? $request->get('campaignArray')['leadDialerCampaign'] : [];
            $customPoint =  $request->get('campaignArray')['customPoint'];
            $dailyLeadLimit = $request->get('campaignLimitArray')['dailyLeadLimit'];
            $dailyBudgetLimit = $request->get('campaignLimitArray')['dailyBudgetLimit'];
            $hourlyLimit = $request->get('campaignLimitArray')['hourlyLimit'];
            $isRevShare = $request->get('campaignArray')['isRevShare'];
            $revSharePerc = $request->get('campaignArray')['revSharePerc'];
            $floorBid = $request->get('campaignArray')['floorBid'];

            if (!empty($request->get('campaignFromOriginArray')['fromState'])) {
                $fromState = $request->get('campaignFromOriginArray')['fromState'];
            }
            $fromAreaCode = $request->get('campaignFromOriginArray')['fromAreaCode'];
            $fromZipCode = $request->get('campaignFromOriginArray')['fromZipCode'];

            if (!empty($request->get('campaignToOriginArray')['toState'])) {
                $toState = $request->get('campaignToOriginArray')['toState'];
            }
            $toAreaCode = $request->get('campaignToOriginArray')['toAreaCode'];
            $toZipCode = $request->get('campaignToOriginArray')['toZipCode'];

            $emailTemplatData = $request->get('emailTemplatArray');
            $granotData = $request->get('granotArray');
            $smsPhonesData = $request->get('smsPhonesArray');
            $crmData = $request->get('crmArray');
            $sourceArray = $request->get('sourceArray') ?? [];

            // campaign Details
            $campaignDetails = Campaign::with('campaignMoving')
                ->with('campaignMoveSize')
                ->with('campaignCallPayout')
                ->with('campaignScheule')
                ->with('campaignJunkType')
                ->with('campaignJunkSubType')
                ->with('campaignHeavyLifting')
                ->with('campaignCarTransport')
                ->with('campaignLocation')
                ->where('campaign_id', $campaignId)->first();
            //echo '<pre>'; print_r($campaignDetails); die;

            $oldFromState = $oldToState = $oldFromAreacode = $oldToAreacode = $oldFromZipcode = $oldToZipcode = [];
            if (count($campaignDetails->campaignLocation) > 0) {
                foreach ($campaignDetails->campaignLocation as $location) {
                    if ($location->location_coverage == 'state' && $location->location_type == 'source') {
                        $oldFromState[] = $location->value;
                    }
                    if ($location->location_coverage == 'state' && $location->location_type == 'destination') {
                        $oldToState[] = $location->value;
                    }
                    if ($location->location_coverage == 'areacode' && $location->location_type == 'source') {
                        $oldFromAreacode[] = $location->value;
                    }
                    if ($location->location_coverage == 'areacode' && $location->location_type == 'destination') {
                        $oldToAreacode[] = $location->value;
                    }
                    if ($location->location_coverage == 'zipcode' && $location->location_type == 'source') {
                        $oldFromZipcode[] = $location->value;
                    }
                    if ($location->location_coverage == 'zipcode' && $location->location_type == 'destination') {
                        $oldToZipcode[] = $location->value;
                    }
                }
            }

            if (empty($businessesId)) {
                $error['businessesIdError'] = 'Select business name';
            }

            if (empty($campaignName)) {
                $error['campaignNameError'] = 'Enter campaign name';
            }

            if ($campaignType == '3') {
                if (count($leadDialerCampaign) == 0) {
                    $error['leadDialerCampaignError'] = 'Select lead dialer campaign';
                }
            }

            if (($leadDistance == "" && $campaignCategory != '2') && $campaignType != '7') {
                $error['leadDistanceError'] = 'Select lead distance';
            }

            if ($leadType == 1) {
                if ($campaignType != 3 && $campaignType != 5 && empty($automatedOutboundLocal) && empty($automatedOutboundLong))
                    $error['automatedOutboundError'] = 'Please enter payout';
            } elseif ($leadType == 2) {
                if (!in_array(1, $payoutTypes) && !in_array(2, $payoutTypes) && $callType == 1) {
                    $error['tokInboundError'] = 'Please select atleast 1 payout';
                } else if (!in_array(3, $payoutTypes) && !in_array(4, $payoutTypes) && $callType == 2) {
                    $error['tokOutboundError'] = 'Please select atleast 1 payout';
                } else if (!in_array(1, $payoutTypes) && !in_array(2, $payoutTypes) && !in_array(3, $payoutTypes) && !in_array(4, $payoutTypes)) {
                    $error['tokOutboundError'] = 'Please select atleast 1 payout';
                }
                if ($callType == 0 || $callType == 1) {
                    /*if (!in_array(1, $payoutTypes) && !in_array(2, $payoutTypes)) {
                        $error['automatedInboundError'] = 'Select automated inbound payout';
                        $error['tokInboundError'] = 'Select dialup inbound payout';
                    } else {*/
                        if (in_array(1, $payoutTypes) && empty($automatedInbound) && $campaignType != 3 && $campaignType != 5)
                            $error['automatedInboundError'] = 'Enter automated inbound payout';
                        if (in_array(2, $payoutTypes) && empty($tokInbound) && $campaignType != 3 && $campaignType != 5)
                            $error['tokInboundError'] = 'Enter dialup inbound payout';
                    /*}*/
                }
                if ($callType == 0 || $callType == 2) {
                    /*if (!in_array(3, $payoutTypes) && !in_array(4, $payoutTypes)) {
                        $error['automatedOutboundError'] = 'Select automated outbound payout';
                        $error['tokOutboundError'] = 'Select dialup outbound payout';
                    } else {*/
                        if (in_array(3, $payoutTypes) && empty($automatedOutboundLocal) && empty($automatedOutboundLong) && $campaignType != 3 && $campaignType != 5)
                            $error['automatedOutboundError'] = 'Please enter payout';
                        if (in_array(4, $payoutTypes) && empty($tokOutboundLocal) && empty($tokOutboundLong) && $campaignType != 3 && $campaignType != 5)
                            $error['tokOutboundError'] = 'Enter dialup outbound payout';
                    /*}*/
                }
            }

            if ($leadType == 2 && ($forwardPhone == "" || strlen($forwardPhone) != 10)) {
                $error['forwardPhoneError'] = 'Enter forward number';
            }
            if ($leadType == 2 && $callType == "") {
                $error['callTypeError'] = 'Please Select Call type';
            }

            if ($leadType == 1 && $campaignType != 7) {
                if ($verified == '' && $unverified == '') {
                    $error['leadsVerifiedUnverifiedError'] = 'Please select atleast One.';
                }
            }

            if ($isRevShare > 0 && $revSharePerc == "") {
                $error['revSharePercError'] = 'Enter rev share percentage';
            }

            if (empty($floorBid) && $campaignType != 7) {
                $error['floorBidError'] = 'Please enter floor bid';
            }

            // Check same campaign name with businesses
            /*$campaignData = Campaign::where('business_id', $businessesId)
                ->where('campaign_name', $campaignName)
                ->first();
            if ($campaignData) {
                $error['campaignNameError'] = 'Campaign already in database';
            }*/

            if ($campaignType == 4) {
                $organicData = $request->get('vmOrganicArray');
                $error = $this->validateVmOrganicCampaignFields($organicData, $error);

                if ($organicData['isFullLead'] == 'no') {
                    unset($error['automatedOutboundError']);
                    unset($error['floorBidError']);
                }
            }

            if ($campaignType == 5) {
                $bookingData = $request->get('bookingArray');
                $error = $this->validateTokBookingCampaignFields($bookingData, $error);
            }

            if ($campaignCategory == 2) {
                $junkData = $request->get('junkArray');
                $error = $this->validateJunkCategoryFields($junkData, $error);
            } else if ($campaignCategory == 3) {
                $liftData = $request->get('liftingArray');
                $liftData['leadDistance'] = ($leadDistance == 0) ? 'local' : 'long';
                $liftData['minimumDistance'] = $minimumDistance;
                $error = $this->validateLiftCategoryFields($liftData, $error);
            } else if ($campaignCategory == 4) {
                $carData = $request->get('carArray');
                $carData['leadDistance'] = ($leadDistance == 0) ? 'local' : 'long';
                $carData['minimumDistance'] = $minimumDistance;
                $carData['carTransportation'] = $carTransportation;
                $carData['mobileHomeTransportation'] = $mobileHomeTransportation;
                $error = $this->validateCarCategoryFields($carData, $error);
            }

            $eDisplayMoveSize = $this->getUserRoleNames();
            $moveSizeArray = [];
            if (array_key_exists("campaignMoveSize", $request->all())){
                $moveSizeArray = $request->get('campaignMoveSize');
            }

            if (count($moveSizeArray) == 0 && $campaignCategory == 1) {
                $error['movesizeError'] = 'Please select atleast one move size.';
            }
            $verifiedUnverified = '';
            if ($verified != '' && $unverified != '') {
                $verifiedUnverified = $verified . ',' . $unverified;
            } else if ($verified == '' && $unverified != '') {
                $verifiedUnverified = $unverified;
            } else if ($verified != '' && $unverified == '') {
                $verifiedUnverified = $verified;
            }

            if ($campaignDetails->lead_status == '1,0') {
                $oldVerifiedText = $oldUnverifiedText = "active";
            } else if ($campaignDetails->lead_status == '0') {
                $oldUnverifiedText = "active";
                $oldVerifiedText = 'inactive';
            } else if ($campaignDetails->lead_status == '1') {
                $oldVerifiedText = "active";
                $oldUnverifiedText = 'inactive';
            }else{
                $oldVerifiedText = $oldUnverifiedText = 'inactive';
            }
            if ($score <= -1001) {
                $error['scorevalue'] = 'Score will be minimum -1000.';
            }
            if (count($error) > 0) {
                throw new Exception('Validation Error');
            }

            $campaigntypeDetails = MstCampaignType::distinct()->get(['campaign_type_id', 'campaign_type']);
            $campaigntypeArray = [];
            for ($t = 0; $t < count($campaigntypeDetails); $t++) {
                $campaigntypeArray[$campaigntypeDetails[$t]->campaign_type_id] = $campaigntypeDetails[$t]->campaign_type;
            }

            if ($callType == 0) {
                $callType = 'both';
            } elseif ($callType == 1) {
                $callType = 'inbound';
            } elseif ($callType == 2) {
                $callType = 'outbound';
            }

            Campaign::where('campaign_id', $campaignId)->update([
                'business_id' => $businessesId,
                'lead_category_id' => $campaignCategory,
                'campaign_name' => $campaignName,
                'lead_type_id' => $leadType,
                'forward_number' => ($leadType == 2 && !empty($forwardPhone)) ? $forwardPhone : 0,
                'campaign_type_id' => $campaignType,
                'call_type' => $callType,
                'lead_payout' => 0,
                'lead_status' => $verifiedUnverified,
                'is_active' => $status,
                'daily_lead_limit' => $dailyLeadLimit,
                'daily_budget_limit' => $dailyBudgetLimit,
                'hourly_lead_limit' => $hourlyLimit,
                'move_days_after' => $moveDays,
                'from_exclude_date' => $moveDateFrom,
                'to_exclude_date' => $moveDateTo,
                'additional_score' => $score,
                /*'subscription_score' => $subscriptionScore,*/
                'floor_bid' => $floorBid,
                'is_remove_truck_rental' => $is_remove_truck_rental,
                'is_spanish' => $is_spanish,
                'is_rev_share' => ($isRevShare > 0) ? 'yes' : 'no',
                'rev_share_perc' => ($isRevShare > 0) ? $revSharePerc : 0,
                'created_at' => date('Y-m-d H:i:s'),
                'created_user_id' => Auth::user()->id
            ]);

            if ($campaignName != $campaignDetails->campaign_name && !empty($campaignName)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'campaign_name',
                    'old_value' => $campaignDetails->campaign_name,
                    'new_value' => $campaignName,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if ($leadType != $campaignDetails->lead_type_id && !empty($leadType)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'lead_type_id',
                    'old_value' => $campaignDetails->lead_type_id,
                    'new_value' => $leadType,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if ($forwardPhone != $campaignDetails->forward_number && !empty($forwardPhone)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'forward_number',
                    'old_value' => $campaignDetails->forward_number,
                    'new_value' => $forwardPhone,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if ($campaignType != $campaignDetails->campaign_type_id && !empty($campaignType)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'campaign_type_id',
                    'old_value' => $campaignDetails->campaign_type_id,
                    'new_value' => $campaignType,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if ($callType != $campaignDetails->call_type && !empty($callType)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'call_type',
                    'old_value' => $campaignDetails->call_type,
                    'new_value' => $callType,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }
            $verifiedText = ( $verified == 1 && isset($verified )) ? "active" : "inactive";
            if ($verifiedText != $oldVerifiedText ) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'lead_verify_status',
                    'old_value' => $oldVerifiedText,
                    'new_value' => $verifiedText,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }
            $unverifiedText = ( $unverified == 0 && isset($unverified )) ? "active" : "inactive";
            if ($unverifiedText != $oldUnverifiedText ) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'lead_unverify_status',
                    'old_value' => $oldUnverifiedText,
                    'new_value' => $unverifiedText,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }
            if ($campaignCategory == 1 && $campaignType != 7 && ( $is_remove_truck_rental != $campaignDetails->is_remove_truck_rental && !empty($is_remove_truck_rental))) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'is_remove_truck_rental',
                    'old_value' => $campaignDetails->is_remove_truck_rental,
                    'new_value' => $is_remove_truck_rental,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }
            if ($is_spanish != $campaignDetails->is_spanish && $campaignType != 7) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'is_spanish',
                    'old_value' => $campaignDetails->is_spanish,
                    'new_value' => $is_spanish,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if ($status != $campaignDetails->is_active && !empty($status)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'campaign_status',
                    'old_value' => ($campaignDetails->is_active == "yes" ) ? "active" : ($campaignDetails->is_active == "permenant" ? "permenant" : "inactive"),
                    'new_value' => ($status == "yes" ) ? "active" : ($status == "permenant" ? "permenant" : "inactive"),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if ($dailyLeadLimit != $campaignDetails->daily_lead_limit && !empty($dailyLeadLimit)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'daily_lead_limit',
                    'old_value' => $campaignDetails->daily_lead_limit,
                    'new_value' => $dailyLeadLimit,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if ($dailyBudgetLimit != $campaignDetails->daily_budget_limit && !empty($dailyBudgetLimit)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'daily_budget_limit',
                    'old_value' => $campaignDetails->daily_lead_limit,
                    'new_value' => $dailyBudgetLimit,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if ($hourlyLimit != $campaignDetails->hourly_lead_limit && !empty($hourlyLimit)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'hourly_lead_limit',
                    'old_value' => $campaignDetails->hourly_lead_limit,
                    'new_value' => $hourlyLimit,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if ($moveDays != $campaignDetails->move_days_after && !empty($moveDays)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'move_days_after',
                    'old_value' => $campaignDetails->move_days_after,
                    'new_value' => $moveDays,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if ($moveDateFrom != $campaignDetails->from_exclude_date && !empty($moveDateFrom)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'from_exclude_date',
                    'old_value' => $campaignDetails->from_exclude_date,
                    'new_value' => $moveDateFrom,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if ($moveDateTo != $campaignDetails->to_exclude_date && !empty($moveDateTo)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'to_exclude_date',
                    'old_value' => $campaignDetails->to_exclude_date,
                    'new_value' => $moveDateTo,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if ($score != $campaignDetails->additional_score && $campaignType != "7") {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'campaign_score',
                    'old_value' => $campaignDetails->additional_score,
                    'new_value' => $score,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if ($subscriptionScore != $campaignDetails->subscription_score && $campaignType != "7") {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'subscription_score',
                    'old_value' => $campaignDetails->subscription_score,
                    'new_value' => $subscriptionScore,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if ($floorBid != $campaignDetails->floor_bid && $campaignType != "7") {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'floor_bid',
                    'old_value' => $campaignDetails->floor_bid,
                    'new_value' => $floorBid,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            $campaignCallPayout = CampaignCallPayout::where('campaign_id', $campaignId)->first();
            $automatedOutbound = NULL;
            $tokOutbound = NULL;
            if ($leadDistance == 0) {
                if ($automatedOutboundLocal > 0 || $campaignType == 3) {
                    $automatedOutbound = $automatedOutboundLocal;
                }
                if ($tokOutboundLocal > 0 || $campaignType == 3) {
                    $tokOutbound = $tokOutboundLocal;
                }
            } else if ($leadDistance == 1) {
                if ($automatedOutboundLong > 0 || $campaignType == 3) {
                    $automatedOutbound = $automatedOutboundLong;
                }
                if ($tokOutboundLong > 0 || $campaignType == 3) {
                    $tokOutbound = $tokOutboundLong;
                }
            }
            if(isset($campaignCallPayout) && !empty($campaignCallPayout) && $campaignCallPayout != null){
                CampaignCallPayout::where('campaign_id', $campaignId)
                    ->update([
                        'real_time_call_limit' => $callLimit,
                        'automated_inbound' => ($automatedInbound > 0 || $campaignType == 3) ? $automatedInbound : NULL,
                        'inbound_call' => ($tokInbound > 0 || $campaignType == 3) ? $tokInbound : NULL,
                        'automated_outbound' => $automatedOutbound,
                        'outbound_call' => $tokOutbound,
                    ]);
            }else{
                CampaignCallPayout::insert([
                    'campaign_id' => $campaignId,
                    'real_time_call_limit' => $callLimit,
                    'automated_inbound' => ($automatedInbound > 0) ? $automatedInbound : NULL,
                    'inbound_call' => ($tokInbound > 0) ? $tokInbound : NULL,
                    'automated_outbound' => $automatedOutbound,
                    'outbound_call' => $tokOutbound,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }
            if (isset($campaignDetails->campaignCallPayout) && count($campaignDetails->campaignCallPayout) > 0) {
                if (!empty($campaignDetails->campaignCallPayout[0]) && $callLimit != $campaignDetails->campaignCallPayout[0]->real_time_call_limit && !empty($callLimit)) {
                    $logArray[] = [
                        'created_user_id' => Auth::user()->id,
                        'campaign_id' => $campaignId,
                        'field_name' => 'real_time_call_limit',
                        'old_value' => $campaignDetails->campaignCallPayout[0]->real_time_call_limit,
                        'new_value' => $callLimit,
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $request->ip()
                    ];
                }

                if (!empty($campaignDetails->campaignCallPayout[0]) && $automatedInbound != $campaignDetails->campaignCallPayout[0]->automated_inbound && !empty($automatedInbound)) {
                    $logArray[] = [
                        'created_user_id' => Auth::user()->id,
                        'campaign_id' => $campaignId,
                        'field_name' => 'automated_inbound',
                        'old_value' => $campaignDetails->campaignCallPayout[0]->automated_inbound,
                        'new_value' => $automatedInbound,
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $request->ip()
                    ];
                }

                if (!empty($campaignDetails->campaignCallPayout[0]) && $tokInbound != $campaignDetails->campaignCallPayout[0]->inbound_call && !empty($tokInbound)) {
                    $logArray[] = [
                        'created_user_id' => Auth::user()->id,
                        'campaign_id' => $campaignId,
                        'field_name' => 'inbound_call',
                        'old_value' => $campaignDetails->campaignCallPayout[0]->inbound_call,
                        'new_value' => $tokInbound,
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $request->ip()
                    ];
                }

                if (  ($automatedOutboundLocal != $campaignDetails->campaignCallPayout[0]->automated_outbound && $automatedOutboundLocal != 0 ) || ($automatedOutboundLong != $campaignDetails->campaignCallPayout[0]->automated_outbound  &&  $automatedOutboundLong != 0 ) ) {
                    $logArray[] = [
                        'created_user_id' => Auth::user()->id,
                        'campaign_id' => $campaignId,
                        'field_name' => 'payout',
                        'old_value' => $campaignDetails->campaignCallPayout[0]->automated_outbound,
                        'new_value' => ($leadDistance == 0) ? $automatedOutboundLocal : $automatedOutboundLong,
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $request->ip()
                    ];
                }

                if ( ($tokOutboundLocal != $campaignDetails->campaignCallPayout[0]->outbound_call || $tokOutboundLong != $campaignDetails->campaignCallPayout[0]->outbound_call) && (!empty($tokOutboundLocal) || !empty($tokOutboundLong)) ) {
                    $logArray[] = [
                        'created_user_id' => Auth::user()->id,
                        'campaign_id' => $campaignId,
                        'field_name' => 'outbound_call',
                        'old_value' => $campaignDetails->campaignCallPayout[0]->outbound_call,
                        'new_value' => ($leadDistance == 0) ? $tokOutboundLocal : $tokOutboundLong,
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $request->ip()
                    ];
                }
            }

            if ($campaignCategory != 2) {
                CampaignMoving::where('campaign_id', $campaignId)->update([
                    'campaign_id' => $campaignId,
                    'move_type' => ($leadDistance == 0) ? 'local' : 'long',
                    'min_distance' => $minimumDistance,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

                // $oldMoveType = $campaignDetails->campaignMoving[0]->move_type;
                if ($campaignCategory == 1) {
                    $oldMoveType = $campaignDetails->campaignMoving[0]->move_type;
                }else  if ($campaignCategory == 3) {
                    $oldMoveType = $campaignDetails->campaignHeavyLifting[0]->move_type;
                }else  if ($campaignCategory == 4) {
                    $oldMoveType = $campaignDetails->campaignCarTransport[0]->move_type;
                }
                $moveType = ($leadDistance == 0) ? 'local' : 'long';
                if ($moveType != $oldMoveType && !empty($leadDistance) && $campaignType != "7") {
                    $logArray[] = [
                        'created_user_id' => Auth::user()->id,
                        'campaign_id' => $campaignId,
                        'field_name' => 'lead_distance_type',
                        'old_value' => $oldMoveType,
                        'new_value' => $moveType,
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $request->ip()
                    ];
                }

                // $oldMinDistance = $campaignDetails->campaignMoving[0]->min_distance;
                if ($campaignCategory == 1) {
                    $oldMinDistance = $campaignDetails->campaignMoving[0]->min_distance;
                }else  if ($campaignCategory == 3) {
                    $oldMinDistance = $campaignDetails->campaignHeavyLifting[0]->min_distance;
                }else  if ($campaignCategory == 4) {
                    $oldMinDistance = $campaignDetails->campaignCarTransport[0]->min_distance;
                }
                if ($minimumDistance != $oldMinDistance && $campaignType != "7") {
                    $logArray[] = [
                        'created_user_id' => Auth::user()->id,
                        'campaign_id' => $campaignId,
                        'field_name' => 'min_distance',
                        'old_value' => $oldMinDistance,
                        'new_value' => $minimumDistance,
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $request->ip()
                    ];
                }
            } else {
                CampaignMoving::where('campaign_id', $campaignId)->delete();
            }

            CampaignMoveSize::where('campaign_id', $campaignId)->delete();
            if (is_array($moveSizeArray) && count($moveSizeArray) > 0) {
                foreach ($moveSizeArray as $moveSize) {
                    CampaignMoveSize::create([
                        'campaign_id' => $campaignId,
                        'move_size_id' => $moveSize,
                        'created_at' => date('Y-m-d H:i:s')
                    ])->campaign_move_size_id;
                }
            }

            $oldMoveSize = [];
            foreach ($campaignDetails->campaignMoveSize as $moveSize) {
                $oldMoveSize[] = $moveSize->move_size_id;
            }

            if (count($moveSizeArray) != count($oldMoveSize) && is_array($moveSizeArray) && count($moveSizeArray) > 0) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'move_size_id',
                    'old_value' => implode(',', $oldMoveSize),
                    'new_value' => implode(',', $moveSizeArray),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            //VM Organic Campaign Data Start
            if ($campaignType == 4) {
                $organicData = $request->get('vmOrganicArray');
                $vmOrganicData = $this->insertUpdateVmOrganicData($campaignId, $organicData, Auth::user()->id, $request->ip());
                foreach ($vmOrganicData as $vmOrganicLog) {
                    $logArray[] = $vmOrganicLog;
                }
            } else {
                //Remove VM Organic Data
                CampaignOrganic::where('campaign_id', $campaignId)->delete();
            }
            //VM Organic Campaign Data End

            if ($campaignCategory == 2) {
                //Added Junk Removal Data
                $junkRemovalData = $this->insertUpdateCategoryData($campaignId, $junkData, $campaignCategory, Auth::user()->id, $request->ip());
                foreach ($junkRemovalData as $carTransportation) {
                    $logArray[] = $carTransportation;
                }
            } else if ($campaignCategory == 3) {
                //Added Heavy Lifting Data
                $heavyLiftData = $this->insertUpdateCategoryData($campaignId, $liftData, $campaignCategory, Auth::user()->id, $request->ip());
                foreach ($heavyLiftData as $heavyLift) {
                    $logArray[] = $heavyLift;
                }
            } else if ($campaignCategory == 4) {
                //Added Heavy Lifting Data
                $carTransportationData = $this->insertUpdateCategoryData($campaignId, $carData, $campaignCategory, Auth::user()->id, $request->ip());
                foreach ($carTransportationData as $carTransportation) {
                    $logArray[] = $carTransportation;
                }
            } else {
                //Remove Junk Removal and Heavy Lifting Data
                CampaignJunkType::where('campaign_id', $campaignId)->delete();
                CampaignJunkSubType::where('campaign_id', $campaignId)->delete();
                CampaignHeavyLifting::where('campaign_id', $campaignId)->delete();
                CampaignCarTransport::where('campaign_id', $campaignId)->delete();
                CampaignCarTransportType::where('campaign_id', $campaignId)->delete();
            }

            //remove old record from campaign_location table
            CampaignLocation::where('campaign_id', $campaignId)->delete();
            if (!empty($fromState)) {
                $stateData = [];
                foreach ($fromState as $state) {
                    $stateData[] = [
                        'campaign_id' => $campaignId,
                        'location_coverage' => 'state',
                        'location_type' => 'source',
                        'value' => $state,
                        'created_at' => date('Y-m-d H:i:s'),
                    ];
                }
                $stateData = collect($stateData);
                foreach($stateData->chunk(100) as $chunk){
                    CampaignLocation::insert($chunk->toArray());
                }
            }

            if (isset($fromState) && !empty($fromState) && $fromState != $oldFromState) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'from_state',
                    'old_value' => implode(',', $oldFromState),
                    'new_value' => implode(",", $fromState),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (!empty($fromAreaCode)) {
                $fromAreaCode = str_replace("\n", ",", $fromAreaCode);
                $fromAreaCode = preg_replace("/[^0-9,]/", "", $fromAreaCode);
                $fromAreaCode = explode(',', $fromAreaCode);
                $areaCodeData = [];
                foreach ($fromAreaCode as $areacode) {
                    $areaCodeData[] = [
                        'campaign_id' => $campaignId,
                        'location_coverage' => 'areacode',
                        'location_type' => 'source',
                        'value' => $areacode,
                        'created_at' => date('Y-m-d H:i:s'),
                    ];
                }
                $areaCodeData = collect($areaCodeData);
                foreach($areaCodeData->chunk(100) as $chunk){
                    CampaignLocation::insert($chunk->toArray());
                }
            }

            if (isset($fromAreaCode) && !empty($fromAreaCode) && $fromAreaCode != $oldFromAreacode) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'from_areacode',
                    'old_value' => implode(',', $oldFromAreacode),
                    'new_value' => implode(",", $fromAreaCode),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (!empty($fromZipCode)) {
                $fromZipCode = str_replace("\n", ",", $fromZipCode);
                $fromZipCode = preg_replace("/[^0-9,]/", "", $fromZipCode);
                $fromZipCode = explode(',', $fromZipCode);
                $zipCodeData = [];
                foreach ($fromZipCode as $zipcode) {
                    $zipCodeData[] = [
                        'campaign_id' => $campaignId,
                        'location_coverage' => 'zipcode',
                        'location_type' => 'source',
                        'value' => $zipcode,
                        'created_at' => date('Y-m-d H:i:s'),
                    ];
                }
                $zipCodeData = collect($zipCodeData);
                foreach($zipCodeData->chunk(100) as $chunk){
                    CampaignLocation::insert($chunk->toArray());
                }
            }

            if (isset($fromZipCode) && !empty($fromZipCode) && $fromZipCode != $oldFromZipcode) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'from_zipcode',
                    'old_value' => implode(',', $oldFromZipcode),
                    'new_value' => implode(",", $fromZipCode),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (!empty($toState)) {
                $stateData = [];
                foreach ($toState as $state) {
                    $stateData[] = [
                        'campaign_id' => $campaignId,
                        'location_coverage' => 'state',
                        'location_type' => 'destination',
                        'value' => $state,
                        'created_at' => date('Y-m-d H:i:s'),
                    ];
                }
                $stateData = collect($stateData);
                foreach($stateData->chunk(100) as $chunk){
                    CampaignLocation::insert($chunk->toArray());
                }
            }

            if (isset($toState) && !empty($toState) && $toState != $oldToState) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'to_state',
                    'old_value' => implode(',', $oldToState),
                    'new_value' => implode(",", $toState),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (!empty($toAreaCode)) {
                $toAreaCode = str_replace("\n", ",", $toAreaCode);
                $toAreaCode = preg_replace("/[^0-9,]/", "", $toAreaCode);
                $toAreaCode = explode(',', $toAreaCode);
                $areaCodeData = [];
                foreach ($toAreaCode as $areacode) {
                    $areaCodeData[] = [
                        'campaign_id' => $campaignId,
                        'location_coverage' => 'areacode',
                        'location_type' => 'destination',
                        'value' => $areacode,
                        'created_at' => date('Y-m-d H:i:s'),
                    ];
                }
                $areaCodeData = collect($areaCodeData);
                foreach($areaCodeData->chunk(100) as $chunk){
                    CampaignLocation::insert($chunk->toArray());
                }
            }

            if (isset($toAreaCode) && !empty($toAreaCode) && $toAreaCode != $oldToAreacode) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'to_areacode',
                    'old_value' => implode(',', $oldToAreacode),
                    'new_value' => implode(",", $toAreaCode),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            if (!empty($toZipCode)) {
                $toZipCode = str_replace("\n", ",", $toZipCode);
                $toZipCode = preg_replace("/[^0-9,]/", "", $toZipCode);
                $toZipCode = explode(',', $toZipCode);
                $zipCodeData = [];
                foreach ($toZipCode as $zipcode) {
                    $zipCodeData[] = [
                        'campaign_id' => $campaignId,
                        'location_coverage' => 'zipcode',
                        'location_type' => 'destination',
                        'value' => $zipcode,
                        'created_at' => date('Y-m-d H:i:s'),
                    ];
                }
                $zipCodeData = collect($zipCodeData);
                foreach($zipCodeData->chunk(100) as $chunk){
                    CampaignLocation::insert($chunk->toArray());
                }
            }

            if (isset($toZipCode) && !empty($toZipCode) && $toZipCode != $oldToZipcode) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'to_zipcode',
                    'old_value' => implode(',', $oldToZipcode),
                    'new_value' => implode(",", $toZipCode),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            //remove record from phonecampaignleaddelivery, phoneleaddeliveryemail, phoneleaddeliverysms and phonegranot table
            $oldEmail = $oldSms = $oldCrm = $deliveryCrmNames = [];
            $leadDelieveryTypeDetails = CampaignLeadDeliveryType::where('campaign_id', $campaignId)->get();
            $deliveryCrms = DeliveryCrm::get()->toArray();
            foreach ($deliveryCrms as $key => $value) {
                $deliveryCrmNames[$value['delivery_crm_id']] = $value['name'];
            }
            $oldEmailId = $oldSmsId = $oldGranotId = $oldCrmId = 0;
            Log::info("start leadDelieveryTypeDetails");
            if (isset($leadDelieveryTypeDetails) && count($leadDelieveryTypeDetails) > 0) {
                Log::info("leadDelieveryTypeDetails =" . json_encode($leadDelieveryTypeDetails));
                foreach ($leadDelieveryTypeDetails as $leadDelievery) {
                    if ($leadDelievery->lead_delivery_type_id == 1) {
                        $oldEmailId = $leadDelievery->campaign_lead_delivery_type_id;
                    }
                    if ($leadDelievery->lead_delivery_type_id == 2) {
                        $oldGranotId = $leadDelievery->campaign_lead_delivery_type_id;
                    }
                    if ($leadDelievery->lead_delivery_type_id == 3) {
                        $oldSmsId = $leadDelievery->campaign_lead_delivery_type_id;
                    }
                    if ($leadDelievery->lead_delivery_type_id == 4) {
                        $oldCrmId = $leadDelievery->campaign_lead_delivery_type_id;
                    }
                    Log::info("oldCrmId 1=". $oldCrmId);
                }
                // for email

                if ($oldEmailId > 0) {
                    $oldEmail = CampaignEmailDelivery::where('campaign_lead_delivery_type_id', $oldEmailId)->get()->toArray();
                }
                // for sms
                if ($oldSmsId > 0) {
                    $oldSms = CampaignSmsDelivery::where('campaign_lead_delivery_type_id', $oldSmsId)
                        ->get()->toArray();
                }
                // for crm
                $logOldCRM = $logCRM = "";
                if ($oldCrmId > 0) {
                    $crmIdArr = [];
                    $oldCrm = CampaignCrmDelivery::where('campaign_lead_delivery_type_id', $oldCrmId)->get()->toArray();
                    foreach ($oldCrm as $key => $value) {
                        $crmIdArr[] = $value['campaign_crm_delivery_id'];
                        $oldcrmurl = $value['post_url'];
                        $old_crm_name = $deliveryCrmNames[$value['delivery_crm_id']];
                        $logOldCRM .= '{url:' . $oldcrmurl . ', name:' . $old_crm_name . '} ';
                    }
                    $defaultfields = LeadDeliveryCrmDefaultFields::whereIn('campaign_crm_delivery_id', $crmIdArr)->get()->toArray();
                    foreach ($defaultfields as $key1 => $value1) {
                        $logOldCRM .= '{defaultname:' . $value1['variable_name'] . ', defaultvalue:' . $value1['variable_value'] . '} ';
                    }
                    LeadDeliveryCrmDefaultFields::whereIn('campaign_crm_delivery_id', $crmIdArr)->delete();
                }

                // delete section start
                CampaignEmailDelivery::where('campaign_lead_delivery_type_id', $oldEmailId)
                    ->delete();
                CampaignSmsDelivery::where('campaign_lead_delivery_type_id', $oldSmsId)
                    ->delete();
                CampaignCrmDelivery::where('campaign_lead_delivery_type_id', $oldCrmId)
                    ->delete();
                    Log::info("oldCrmId 2= ". $oldCrmId. ", campaignId= ". $campaignId);
                CampaignLeadDeliveryType::where('campaign_id', $campaignId)
                    ->delete();
                // delete section end
            }
            // for lead delivery email
            $leadDeliveryEmailTemplate = LeadDeliveryEmailTemplate::get()->toArray();
            $leadDeliveryEmailTemplateId = array();
            for ($t = 0; $t < count($leadDeliveryEmailTemplate); $t++) {
                $leadDeliveryEmailTemplateId[$leadDeliveryEmailTemplate[$t]['lead_delivery_email_template_id']] = $leadDeliveryEmailTemplate[$t]['template_name'];
            }
            $oldEmailData = $oldSmsData = array();
            for ($i = 0; $i < count($oldEmail); $i++) {
                $oldEmailData['emailTemplate'][] = $oldEmail[$i]['lead_delivery_email_template_id'];
                $oldEmailData['emailAddress'][] = $oldEmail[$i]['to_email'];
            }

            if ( count($oldEmailData) > 0) {
                for ($i = 0; $i < count($oldEmailData['emailAddress']); $i++) {
                    // $emailAddress = $emailTemplatData['emailAddress'][$i];
                    // $emailTemplate = $emailTemplatData['emailTemplate'][$i];
                    $oldEmailAddress = $oldEmailData['emailAddress'][$i];
                    $oldEmailTemplateId = $oldEmailData['emailTemplate'][$i];
                    // if ($oldEmailAddress != $emailAddress || $oldEmailTemplateId != $emailTemplate || count($emailTemplatData['emailAddress']) != count($oldEmailData)) {
                        $logOldEmail .= '{email:' . $oldEmailAddress . ', template:' . $leadDeliveryEmailTemplateId[$oldEmailTemplateId] . '} ';
                    // }
                }
            }
            if (!empty($emailTemplatData)) {
                if (!empty($emailTemplatData['emailAddress'])) {
                    if ($emailTemplatData['emailAddress'][0] != '') {
                        $campaignLeadDelieveryTypeId = CampaignLeadDeliveryType::create([
                            'campaign_id' => $campaignId,
                            'lead_delivery_type_id' => 1,
                            'created_at' => date('Y-m-d H:i:s'),
                            'created_user_id' => Auth::user()->id
                        ])->campaign_lead_delivery_type_id;
                        for ($i = 0; $i < count($emailTemplatData['emailAddress']); $i++) {
                            $emailAddress = $emailTemplatData['emailAddress'][$i];
                            $emailTemplate = $emailTemplatData['emailTemplate'][$i];

                            CampaignEmailDelivery::create([
                                'campaign_lead_delivery_type_id' => $campaignLeadDelieveryTypeId,
                                'to_email' => $emailAddress,
                                'lead_delivery_email_template_id' => $emailTemplate,
                                'created_at' => date('Y-m-d H:i:s'),
                                'created_user_id' => Auth::user()->id
                            ]);
                            $logEmail .= '{email:' . $emailAddress . ', template:' . $leadDeliveryEmailTemplateId[$emailTemplate] . '} ';
                        }
                    }
                }
            }
            if ( $logOldEmail != $logEmail ) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'lead_delivery_email',
                    'old_value' => (!empty($logOldEmail)) ? $logOldEmail : "--",
                    'new_value' => (!empty($logEmail)) ? $logEmail : "--",
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }
            // for sms delivery
            $leadDeliverySMSCarrier = LeadDeliverySMSCarrier::get()->toArray();
            $leadDeliverySMSCarrierId = array();
            for ($t = 0; $t < count($leadDeliverySMSCarrier); $t++) {
                $leadDeliverySMSCarrierId[$leadDeliverySMSCarrier[$t]['lead_delivery_sms_carrier_id']] = $leadDeliverySMSCarrier[$t]['carrier'];
            }
            for ($i = 0; $i < count($oldSms); $i++) {
                $oldSmsData['smsPhones'][] = $oldSms[$i]['phone'];
                $oldSmsData['carriers'][] = $oldSms[$i]['lead_delivery_sms_carrier_id'];
            }
            $smsPhone = $carriers =  "" ;
            if ( count($oldSmsData) > 0) {
                for ($i = 0; $i < count($oldSmsData['smsPhones']); $i++) {
                    $oldSmsPhone = $oldSmsData['smsPhones'][$i];
                    $oldCarriers = $oldSmsData['carriers'][$i];
                    // if ($oldSmsPhone != $smsPhone || $oldCarriers != $carriers || count($smsPhonesData['smsPhones']) != count($oldSmsData)) {
                        // $logOldSms[] = ['phone' => $oldSmsPhone, 'carrier' => $leadDeliverySMSCarrierId[$oldCarriers]];
                        $logOldSms .= '{phone:' . $oldSmsPhone . ', carrier:' . $leadDeliverySMSCarrierId[$oldCarriers] . '} ';
                    // }
                }
            }
            if (!empty($smsPhonesData)) {
                if (!empty($smsPhonesData['smsPhones'])) {
                    if ($smsPhonesData['smsPhones'][0] != '') {
                        $campaignLeadDelieveryTypeId = CampaignLeadDeliveryType::create([
                            'campaign_id' => $campaignId,
                            'lead_delivery_type_id' => 3,
                            'created_at' => date('Y-m-d H:i:s'),
                            'created_user_id' => Auth::user()->id
                        ])->campaign_lead_delivery_type_id;

                        for ($i = 0; $i < count($smsPhonesData['smsPhones']); $i++) {
                            $smsPhone = $smsPhonesData['smsPhones'][$i];
                            $carriers = $smsPhonesData['carriers'][$i];

                            CampaignSmsDelivery::create([
                                'campaign_lead_delivery_type_id' => $campaignLeadDelieveryTypeId,
                                'phone' => $smsPhone,
                                'lead_delivery_sms_carrier_id' => $carriers,
                                'created_at' => date('Y-m-d H:i:s'),
                                'created_user_id' => Auth::user()->id
                            ]);
                            $logSms .= '{phone:' . $smsPhone . ', carrier:' . $leadDeliverySMSCarrierId[$carriers] . '} ';

                        }
                    }
                }
            }
            if( $logOldSms != $logSms) {
                // if (!empty($smsPhonesData) && $smsPhonesData['smsPhones'][0] != '' && count($smsPhonesData['smsPhones']) != count($oldSms)) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'lead_delivery_sms',
                    'old_value' => (!empty($logOldSms)) ? $logOldSms : "--",
                    'new_value' => (!empty($logSms)) ? $logSms : "--",
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }
            // echo "<pre>logOldCRM = ";
            // print_r($logOldCRM);
            // die;
            if (!empty($crmData)) {
                $newcrmIdArr = [];
                if (!empty($crmData['crmUrls'])) {
                    if ($crmData['crmUrls'][0] != '') {
                        $campaignLeadDelieveryTypeId = CampaignLeadDeliveryType::create([
                            'campaign_id' => $campaignId,
                            'lead_delivery_type_id' => 4,
                            'created_at' => date('Y-m-d H:i:s'),
                            'created_user_id' => Auth::user()->id
                        ])->campaign_lead_delivery_type_id;
                        for ($i = 0; $i < count($crmData['crmUrls']); $i++) {
                            $crmUrl = $crmData['crmUrls'][$i];
                            /*if ($crmUrl == '') {
                                continue;
                            }*/
                            $crmIds = $crmData['crmNames'][$i];
                            if ($crmIds != "") {
                                $campaignDeliveryArr = [
                                    'delivery_crm_id' => $crmIds,
                                    'campaign_lead_delivery_type_id' => $campaignLeadDelieveryTypeId,
                                    'post_url' => $crmUrl,
                                    'campaign_id' => $campaignId,
                                    'user_id' => Auth::user()->id,
                                    'created_at' => date('Y-m-d H:i:s'),
                                    'updated_at' => date('Y-m-d H:i:s'),
                                ];
                                // echo '<pre>crmIds = ';print_r($deliveryCrmNames);
                                $newcrm = CampaignCrmDelivery::create($campaignDeliveryArr);
                                $newcrmIdArr[] = $newcrm->campaign_crm_delivery_id;
                                $crmurl = $crmUrl;
                                $crm_name = $deliveryCrmNames[$crmIds];
                                $logCRM .= '{url:' . $crmurl . ', name:' . $crm_name . '} ';
                                if (array_key_exists('crm_defauldata', $crmData)) {
                                    if (array_key_exists($i, $crmData['crm_defauldata'])) {
                                        $crm_defauldata = $crmData['crm_defauldata'][$i];
                                        foreach ($crm_defauldata as $key => $value) {
                                            LeadDeliveryCrmDefaultFields::create([
                                                'campaign_crm_delivery_id' => $newcrm->campaign_crm_delivery_id,
                                                'delivery_mapping_fields_id' => $value['mappingfieldid'],
                                                'variable_name' => $value['variable_name'],
                                                'variable_value' => $value['variable_value']
                                            ]);
                                            // $logCRM .= '{defaultname:' . $value['variable_name'] . ', defaultvalue:' . $value['variable_value'] . '} ';
                                        }

                                    }
                                }
                            }
                        }
                        $newdefaultfields = LeadDeliveryCrmDefaultFields::whereIn('campaign_crm_delivery_id', $newcrmIdArr)->get()->toArray();
                        foreach ($newdefaultfields as $key1 => $value1) {
                            $logCRM .= '{defaultname:' . $value1['variable_name'] . ', defaultvalue:' . $value1['variable_value'] . '} ';
                        }
                    }
                }
            }
            if ( $logOldCRM != $logCRM ) {
                $logArray[] = [
                    'created_user_id' => Auth::user()->id,
                    'campaign_id' => $campaignId,
                    'field_name' => 'lead_delivery_crm',
                    'old_value' => (!empty($logOldCRM)) ? $logOldCRM : "--",
                    'new_value' => (!empty($logCRM)) ? $logCRM : "--",
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $request->ip()
                ];
            }

            /*if (!empty($crmData)) {
                if (!empty($crmData['crmUrls'])) {
                    if ($crmData['crmUrls'][0] != '') {
                        $campaignLeadDelieveryTypeId = CampaignLeadDeliveryType::create([
                            'campaign_id' => $campaignId,
                            'lead_delivery_type_id' => 4,
                            'created_at' => date('Y-m-d H:i:s'),
                            'created_user_id' => Auth::user()->id
                        ])->campaign_lead_delivery_type_id;

                        $logDetails .= '<li>Lead Delivery CRM : ';
                        for ($i = 0; $i < count($crmData['crmUrls']); $i++) {
                            $crmUrl = $crmData['crmUrls'][$i];
                            if ($crmUrl == '') {
                                continue;
                            }
                            $crmRefTag = $crmData['crmRefTags'][$i];
                            $crmPostData = $crmData['crmPostDatas'][$i];
                            $crmName = $crmData['crmNames'][$i];
                            $crmUniquekey1 = $crmData['crmUniquekeys1'][$i];
                            $crmUniquekey2 = $crmData['crmUniquekeys2'][$i];

                            CampaignCrmDelivery::create([
                                'campaign_lead_delivery_type_id' => $campaignLeadDelieveryTypeId,
                                'post_url' => $crmUrl,
                                'phone_crm_delivery_tag_id' => $crmRefTag,
                                'phone_crm_post_id' => $crmPostData,
                                'crm_id' => $crmName,
                                'unique_variable_1' => $crmUniquekey1,
                                'unique_variable_2' => $crmUniquekey2,
                            ]);
                        }
                        $logDetails .= '</li>';
                    }
                }
            }*/

            // Remove Old Record from BusinessesCampaignHours table
            $oldSundayHours = $oldMondayHours = $oldTuedayHours = $oldWeddayHours = $oldThudayHours = $oldFridayHours = $oldSatdayHours = array();
            for ($fd = 0; $fd < count($campaignDetails->campaignScheule); $fd++) {
                if ($campaignDetails->campaignScheule[$fd]['day'] == 7) {
                    $oldSundayHours[] = $campaignDetails->campaignScheule[$fd];
                } else if ($campaignDetails->campaignScheule[$fd]['day'] == 1) {
                    $oldMondayHours[] = $campaignDetails->campaignScheule[$fd];
                } else if ($campaignDetails->campaignScheule[$fd]['day'] == 2) {
                    $oldTuedayHours[] = $campaignDetails->campaignScheule[$fd];
                } else if ($campaignDetails->campaignScheule[$fd]['day'] == 3) {
                    $oldWeddayHours[] = $campaignDetails->campaignScheule[$fd];
                } else if ($campaignDetails->campaignScheule[$fd]['day'] == 4) {
                    $oldThudayHours[] = $campaignDetails->campaignScheule[$fd];
                } else if ($campaignDetails->campaignScheule[$fd]['day'] == 5) {
                    $oldFridayHours[] = $campaignDetails->campaignScheule[$fd];
                } else if ($campaignDetails->campaignScheule[$fd]['day'] == 6) {
                    $oldSatdayHours[] = $campaignDetails->campaignScheule[$fd];
                }
            }
            //print_r($oldSundayHours); die;
            CampaignScheule::where('campaign_id', $campaignId)->delete();

            if (!empty($request->get('campaignTimeHours')['day0'])) {
                $dayArray0 = json_decode($request->get('campaignTimeHours')['day0'], true);
                $limit0 = $request->get('campaignTimeHours')['limit0'];
                $isPauseSun = $request->get('campaignTimeHours')['isPauseSun'];
                $isPauseSun = ($isPauseSun > 0) ? 'yes' : 'no';

                // old hour log start
                $logOldSunDetails = $logSunDetails = "";
                if (count($oldSundayHours) > 0) {
                    foreach ($oldSundayHours as $key => $oldtimeHours) {
                        $oldstartTime = $oldtimeHours['start_hour'];
                        $oldendTime = $oldtimeHours['end_hour'];
                        $oldlimit0 = $oldtimeHours['daily_limit'];
                        $oldisPauseSun = $oldtimeHours['is_pause'];
                        if (count($dayArray0) != count($oldSundayHours) || $oldlimit0 != $limit0 || $oldisPauseSun != $isPauseSun) {
                            $logOldSunDetails .= ', ' . $oldstartTime . 'to' . $oldendTime;
                        }
                    }
                    if (strlen($logOldSunDetails) > 0) {
                        $logOldHoursDetails .= '{day:Sun' . $logOldSunDetails . ', daily_limit:' . $oldlimit0 . ', is_pause:' . $oldisPauseSun . '}';
                    }
                }
                // old hour log end

                foreach ($dayArray0 as $timeHours) {
                    $startTime = CampaignScheule::hours_setup($timeHours['start'], 'start');
                    $endTime = CampaignScheule::hours_setup($timeHours['end'], 'end');
                    if ($startTime == 0 && $endTime == 0) {
                        $endTime = 23;
                    }
                    $campaignHoursData[] = [
                        'campaign_id' => $campaignId,
                        'day' => 7,
                        'start_hour' => $startTime,
                        'end_hour' => $endTime,
                        'daily_limit' => $limit0,
                        'is_pause' => $isPauseSun,
                        'status' => 'active',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    if (count($dayArray0) != count($oldSundayHours) || $oldlimit0 != $limit0 || $oldisPauseSun != $isPauseSun) {
                        $logSunDetails .= ', ' . $startTime . 'to' . $endTime;
                    }
                }
                if (strlen($logSunDetails) > 0) {
                    $logHoursDetails .= '{day:Sun' . $logSunDetails . ', daily_limit:' . $limit0 . ', is_pause:' . $isPauseSun . '}';
                }
            }
            if (!empty($request->get('campaignTimeHours')['day1'])) {
                $dayArray1 = json_decode($request->get('campaignTimeHours')['day1'], true);
                $limit1 = $request->get('campaignTimeHours')['limit1'];
                $isPauseMon = $request->get('campaignTimeHours')['isPauseMon'];
                $isPauseMon = ($isPauseMon > 0) ? 'yes' : 'no';

                // old hour log start
                $logOldMonDetails = $logMonDetails = "";
                if (count($oldMondayHours) > 0) {
                    for ($i = 0; $i < count($oldMondayHours); $i++) {
                        $oldstartTime = $oldMondayHours[$i]['start_hour'];
                        $oldendTime = $oldMondayHours[$i]['end_hour'];
                        $oldlimit1 = $oldMondayHours[$i]['daily_limit'];
                        $oldisPauseMon = $oldMondayHours[$i]['is_pause'];
                        if (count($dayArray1) != count($oldMondayHours) || $oldlimit1 != $limit1 || $oldisPauseMon != $isPauseMon) {
                            $logOldMonDetails .= ', ' . $oldstartTime . 'to' . $oldendTime;
                        }
                    }
                    if (strlen($logOldMonDetails) > 0) {
                        $logOldHoursDetails .= '{day:Mon' . $logOldMonDetails . ', daily_limit:' . $oldlimit1 . ', is_pause:' . $oldisPauseMon . '}';
                    }
                }
                // old hour log end

                foreach ($dayArray1 as $timeHours) {
                    $startTime = CampaignScheule::hours_setup($timeHours['start'], 'start');
                    $endTime = CampaignScheule::hours_setup($timeHours['end'], 'end');
                    if ($startTime == 0 && $endTime == 0) {
                        $endTime = 23;
                    }
                    $campaignHoursData[] = [
                        'campaign_id' => $campaignId,
                        'day' => 1,
                        'start_hour' => $startTime,
                        'end_hour' => $endTime,
                        'daily_limit' => $limit1,
                        'is_pause' => $isPauseMon,
                        'status' => 'active',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    if (count($dayArray1) != count($oldMondayHours) || $oldlimit1 != $limit1 || $oldisPauseMon != $isPauseMon) {
                        $logMonDetails .= ', ' . $startTime . 'to' . $endTime;
                    }
                }
                if (strlen($logMonDetails) > 0) {
                    $logHoursDetails .= '{day:Mon' . $logMonDetails . ', daily_limit:' . $limit1 . ', is_pause:' . $isPauseMon . '}';
                }
            }
            if (!empty($request->get('campaignTimeHours')['day2'])) {
                $dayArray2 = json_decode($request->get('campaignTimeHours')['day2'], true);
                $limit2 = $request->get('campaignTimeHours')['limit2'];
                $isPauseTue = $request->get('campaignTimeHours')['isPauseTue'];
                $isPauseTue = ($isPauseTue > 0) ? 'yes' : 'no';

                // old hour log start
                $logOldTueDetails = $logTueDetails = "";
                if (count($oldTuedayHours) > 0) {
                    for ($i = 0; $i < count($oldTuedayHours); $i++) {
                        $oldstartTime = $oldTuedayHours[$i]['start_hour'];
                        $oldendTime = $oldTuedayHours[$i]['end_hour'];
                        $oldlimit2 = $oldTuedayHours[$i]['daily_limit'];
                        $oldisPauseTue = $oldTuedayHours[$i]['is_pause'];
                        if (count($dayArray2) != count($oldTuedayHours) || $oldlimit2 != $limit2 || $oldisPauseTue != $isPauseTue) {
                            $logOldTueDetails .= ', ' . $oldstartTime . 'to' . $oldendTime;
                        }
                    }
                    if (strlen($logOldTueDetails) > 0) {
                        $logOldHoursDetails .= '{day:Tue' . $logOldTueDetails . ', daily_limit:' . $oldlimit2 . ', is_pause:' . $oldisPauseTue . '}';
                    }
                }
                // old hour log end

                foreach ($dayArray2 as $timeHours) {
                    $startTime = CampaignScheule::hours_setup($timeHours['start'], 'start');
                    $endTime = CampaignScheule::hours_setup($timeHours['end'], 'end');
                    if ($startTime == 0 && $endTime == 0) {
                        $endTime = 23;
                    }
                    $campaignHoursData[] = [
                        'campaign_id' => $campaignId,
                        'day' => 2,
                        'start_hour' => $startTime,
                        'end_hour' => $endTime,
                        'daily_limit' => $limit2,
                        'is_pause' => $isPauseTue,
                        'status' => 'active',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    if (count($dayArray2) != count($oldTuedayHours) || $oldlimit2 != $limit2 || $oldisPauseTue != $isPauseTue) {
                        $logTueDetails .= ', ' . $startTime . 'to' . $endTime;
                    }
                }
                if (strlen($logTueDetails) > 0) {
                    $logHoursDetails .= '{day:Tue' . $logTueDetails . ', daily_limit:' . $limit2 . ', is_pause:' . $isPauseTue . '}';
                }
            }
            if (!empty($request->get('campaignTimeHours')['day3'])) {
                $dayArray3 = json_decode($request->get('campaignTimeHours')['day3'], true);
                $limit3 = $request->get('campaignTimeHours')['limit3'];
                $isPauseWed = $request->get('campaignTimeHours')['isPauseWed'];
                $isPauseWed = ($isPauseWed > 0) ? 'yes' : 'no';

                // old hour log start
                $logOldWedDetails = $logWedDetails = "";
                if (count($oldWeddayHours) > 0) {
                    for ($i = 0; $i < count($oldWeddayHours); $i++) {
                        $oldstartTime = $oldWeddayHours[$i]['start_hour'];
                        $oldendTime = $oldWeddayHours[$i]['end_hour'];
                        $oldlimit3 = $oldWeddayHours[$i]['daily_limit'];
                        $oldisPauseWed = $oldWeddayHours[$i]['is_pause'];
                        if (count($dayArray3) != count($oldWeddayHours) || $oldlimit3 != $limit3 || $oldisPauseWed != $isPauseWed) {
                            $logOldWedDetails .= ', ' . $oldstartTime . 'to' . $oldendTime;
                        }
                    }
                    if (strlen($logOldWedDetails) > 0) {
                        $logOldHoursDetails .= '{day:Wed' . $logOldWedDetails . ', daily_limit:' . $oldlimit3 . ', is_pause:' . $oldisPauseWed . '}';
                    }
                }
                // old hour log end

                foreach ($dayArray3 as $timeHours) {
                    $startTime = CampaignScheule::hours_setup($timeHours['start'], 'start');
                    $endTime = CampaignScheule::hours_setup($timeHours['end'], 'end');
                    if ($startTime == 0 && $endTime == 0) {
                        $endTime = 23;
                    }
                    $campaignHoursData[] = [
                        'campaign_id' => $campaignId,
                        'day' => 3,
                        'start_hour' => $startTime,
                        'end_hour' => $endTime,
                        'daily_limit' => $limit3,
                        'is_pause' => $isPauseWed,
                        'status' => 'active',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    if (count($dayArray3) != count($oldWeddayHours) || $oldlimit3 != $limit3 || $oldisPauseWed != $isPauseWed) {
                        $logWedDetails .= ', ' . $startTime . 'to' . $endTime;
                    }
                }
                if (strlen($logWedDetails) > 0) {
                    $logHoursDetails .= '{day:Wed' . $logWedDetails . ', daily_limit:' . $limit3 . ', is_pause:' . $isPauseWed . '}';
                }
            }
            if (!empty($request->get('campaignTimeHours')['day4'])) {
                $dayArray4 = json_decode($request->get('campaignTimeHours')['day4'], true);
                $limit4 = $request->get('campaignTimeHours')['limit4'];
                $isPauseThu = $request->get('campaignTimeHours')['isPauseThu'];
                $isPauseThu = ($isPauseThu > 0) ? 'yes' : 'no';

                // old hour log start
                $logOldThuDetails = $logThuDetails = "";
                if (count($oldThudayHours) > 0) {
                    for ($i = 0; $i < count($oldThudayHours); $i++) {
                        $oldstartTime = $oldThudayHours[$i]['start_hour'];
                        $oldendTime = $oldThudayHours[$i]['end_hour'];
                        $oldlimit4 = $oldThudayHours[$i]['daily_limit'];
                        $oldisPauseThu = $oldThudayHours[$i]['is_pause'];
                        if (count($dayArray4) != count($oldThudayHours) || $oldlimit4 != $limit4 || $oldisPauseThu != $isPauseThu) {
                            $logOldThuDetails .= ', ' . $oldstartTime . 'to' . $oldendTime;
                        }
                    }
                    if (strlen($logOldThuDetails) > 0) {
                        $logOldHoursDetails .= '{day:Thu' . $logOldThuDetails . ', daily_limit:' . $oldlimit4 . ', is_pause:' . $oldisPauseThu . '}';
                    }
                }
                // old hour log end

                foreach ($dayArray4 as $timeHours) {
                    $startTime = CampaignScheule::hours_setup($timeHours['start'], 'start');
                    $endTime = CampaignScheule::hours_setup($timeHours['end'], 'end');
                    if ($startTime == 0 && $endTime == 0) {
                        $endTime = 23;
                    }
                    $campaignHoursData[] = [
                        'campaign_id' => $campaignId,
                        'day' => 4,
                        'start_hour' => $startTime,
                        'end_hour' => $endTime,
                        'daily_limit' => $limit4,
                        'is_pause' => $isPauseThu,
                        'status' => 'active',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    if (count($dayArray4) != count($oldThudayHours) || $oldlimit4 != $limit4 || $oldisPauseThu != $isPauseThu) {
                        $logThuDetails .= ', ' . $startTime . 'to' . $endTime;
                    }
                }
                if (strlen($logThuDetails) > 0) {
                    $logHoursDetails .= '{day:Thu' . $logThuDetails . ', daily_limit:' . $limit4 . ', is_pause:' . $isPauseThu . '}';
                }
            }
            if (!empty($request->get('campaignTimeHours')['day5'])) {
                $dayArray5 = json_decode($request->get('campaignTimeHours')['day5'], true);
                $limit5 = $request->get('campaignTimeHours')['limit5'];
                $isPauseFri = $request->get('campaignTimeHours')['isPauseFri'];
                $isPauseFri = ($isPauseFri > 0) ? 'yes' : 'no';

                // old hour log start
                $logOldFriDetails = $logFriDetails = "";
                if (count($oldFridayHours) > 0) {
                    for ($i = 0; $i < count($oldFridayHours); $i++) {
                        $oldstartTime = $oldFridayHours[$i]['start_hour'];
                        $oldendTime = $oldFridayHours[$i]['end_hour'];
                        $oldlimit5 = $oldFridayHours[$i]['daily_limit'];
                        $oldisPauseFri = $oldFridayHours[$i]['is_pause'];
                        if (count($dayArray5) != count($oldFridayHours) || $oldlimit5 != $limit5 || $oldisPauseFri != $isPauseFri) {
                            $logOldFriDetails .= ', ' . $oldstartTime . 'to' . $oldendTime;
                        }
                    }
                    if (strlen($logOldFriDetails) > 0) {
                        $logOldHoursDetails .= '{day:Fri' . $logOldFriDetails . ', daily_limit:' . $oldlimit5 . ', is_pause:' . $oldisPauseFri . '}';
                    }
                }
                // old hour log end

                foreach ($dayArray5 as $timeHours) {
                    $startTime = CampaignScheule::hours_setup($timeHours['start'], 'start');
                    $endTime = CampaignScheule::hours_setup($timeHours['end'], 'end');
                    if ($startTime == 0 && $endTime == 0) {
                        $endTime = 23;
                    }
                    $campaignHoursData[] = [
                        'campaign_id' => $campaignId,
                        'day' => 5,
                        'start_hour' => $startTime,
                        'end_hour' => $endTime,
                        'daily_limit' => $limit5,
                        'is_pause' => $isPauseFri,
                        'status' => 'active',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    if (count($dayArray5) != count($oldFridayHours) || $oldlimit5 != $limit5 || $oldisPauseFri != $isPauseFri) {
                        $logFriDetails .= ', ' . $startTime . 'to' . $endTime;
                    }
                }
                if (strlen($logFriDetails) > 0) {
                    $logHoursDetails .= '{day:Fri' . $logFriDetails . ', daily_limit:' . $limit5 . ', is_pause:' . $isPauseFri . '}';
                }
            }
            if (!empty($request->get('campaignTimeHours')['day6'])) {
                $dayArray6 = json_decode($request->get('campaignTimeHours')['day6'], true);
                $limit6 = $request->get('campaignTimeHours')['limit6'];
                $isPauseSat = $request->get('campaignTimeHours')['isPauseSat'];
                $isPauseSat = ($isPauseSat > 0) ? 'yes' : 'no';

                // old hour log start
                $logOldSatDetails = $logSatDetails = "";
                if (count($oldSatdayHours) > 0) {
                    for ($i = 0; $i < count($oldSatdayHours); $i++) {
                        $oldstartTime = $oldSatdayHours[$i]['start_hour'];
                        $oldendTime = $oldSatdayHours[$i]['end_hour'];
                        $oldlimit6 = $oldSatdayHours[$i]['daily_limit'];
                        $oldisPauseSat = $oldSatdayHours[$i]['is_pause'];
                        if (count($dayArray6) != count($oldSatdayHours) || $oldlimit6 != $limit6 || $oldisPauseSat != $isPauseSat) {
                            $logOldSatDetails .= ', ' . $oldstartTime . 'to' . $oldendTime;
                        }
                    }
                    if (strlen($logOldSatDetails) > 0) {
                        $logOldHoursDetails .= '{day:Sat' . $logOldSatDetails . ', daily_limit:' . $oldlimit6 . ', is_pause:' . $oldisPauseSat . '}';
                    }
                }
                // old hour log end

                foreach ($dayArray6 as $timeHours) {
                    $startTime = CampaignScheule::hours_setup($timeHours['start'], 'start');
                    $endTime = CampaignScheule::hours_setup($timeHours['end'], 'end');
                    if ($startTime == 0 && $endTime == 0) {
                        $endTime = 23;
                    }
                    $campaignHoursData[] = [
                        'campaign_id' => $campaignId,
                        'day' => 6,
                        'start_hour' => $startTime,
                        'end_hour' => $endTime,
                        'daily_limit' => $limit6,
                        'is_pause' => $isPauseSat,
                        'status' => 'active',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    if (count($dayArray6) != count($oldSatdayHours) || $oldlimit6 != $limit6 || $oldisPauseSat != $isPauseSat) {
                        $logSatDetails .= ', ' . $startTime . 'to' . $endTime;
                    }
                }
                if (strlen($logSatDetails) > 0) {
                    $logHoursDetails .= '{day:Sat' . $logSatDetails . ', daily_limit:' . $limit6 . ', is_pause:' . $isPauseSat . '}';
                }
            }

            if (is_array($campaignHoursData) && count($campaignHoursData) > 0) {
                CampaignScheule::insert($campaignHoursData);

                if (strlen($logOldHoursDetails) > 0 && strlen($logHoursDetails) > 0) {
                    $logArray[] = [
                        'created_user_id' => Auth::user()->id,
                        'campaign_id' => $campaignId,
                        'field_name' => 'campaign_timing',
                        'old_value' => $logOldHoursDetails,
                        'new_value' => $logHoursDetails,
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $request->ip()
                    ];
                }
            }
            $oldCampaignLeadDialer = CampaignLeadDialer::where('live_transfer_campaign_id', $campaignId)->pluck('campaign_id')->toArray();
            //first delete then insert
            CampaignLeadDialer::where('live_transfer_campaign_id', $campaignId)->delete();
            if (count($leadDialerCampaign) > 0) {
                $leaddialerCamps = [];
                for ($v = 0; $v < count($leadDialerCampaign); $v++) {
                    $leaddialerCamps[$v] = [
                        'campaign_id' => $leadDialerCampaign[$v],
                        'live_transfer_campaign_id' => $campaignId,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                if(implode(",", $oldCampaignLeadDialer) != implode(",", $leadDialerCampaign)){
                    $logArray[] = [
                        'created_user_id' => Auth::user()->id,
                        'campaign_id' => $campaignId,
                        'field_name' => 'lead_dialer_campaign',
                        'old_value' => implode(",", $oldCampaignLeadDialer),
                        'new_value' => implode(",", $leadDialerCampaign),
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $request->ip()
                    ];
                }
                CampaignLeadDialer::insert($leaddialerCamps);
            }
            if ($campaignType == 3) {
                $existcampaignDialerPoints = CampaignDialerPoints::where('campaign_id', $campaignId)->first();
                if (isset($existcampaignDialerPoints)) {
                    $campaignDialerPoints = [
                        'custom_points' => $customPoint
                    ];
                    CampaignDialerPoints::where('campaign_id', $campaignId)->update($campaignDialerPoints);
                } else {
                    $campaignDialerPoints = [
                        'campaign_id' => $campaignId,
                        'custom_points' => $customPoint,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    CampaignDialerPoints::where('campaign_id', $campaignId)->insert($campaignDialerPoints);
                }
            }
            if (count($logArray) > 0) {
                CampaignUpdateLog::insert($logArray);
            }

            //first delete then insert
            CampaignLeadSource::where('campaign_id', $campaignId)->delete();
            $sourceNameArray = [];
            if (count($sourceArray) > 0) { foreach ($sourceArray as $source) {
                $sourceNameArray[] = [
                    'campaign_id' => $campaignId,
                    'lead_source_id' => $source['source'],
                    'user_id' => Auth::user()->id,
                    'created_at' => date("Y-m-d H:i:s"),
                ];
            } }
            if (count($sourceNameArray) > 0) {
                CampaignLeadSource::insert($sourceNameArray);
            }

            $responseStatus = 1;
            $message = 'Success';
        } catch (Exception $e) {
            $message = $e->getMessage();
        }
        $jsonResponse = [
            'status' => $responseStatus,
            'message' => $message,
            'error' => $error,
        ];
        //dd($jsonResponse);
        return response()->json($jsonResponse);
    }

    //load view of campaign list
    public function view()
    {
        //all array define
        $campaignIdArray  = $businessIdArray = $businessArray = $campaignWiseRouteCountArray = $campaignHoursArray = $campaignTypeArray = $campaignCategoryArray = $campaignMovingArray = $finalData = $campaignLeadDialerArray = [];

        $currentDate = strtotime(date("Y-m-d"));
        $startDate = date('Y-m-d') . " 00:00:00";
        $endDate = date('Y-m-d', strtotime('+1 day')) . " 00:00:00";
        $dayNumber = date("N", $currentDate);
        $hour = strtotime(date("Y-m-d H:i:s"));
        $hourNumber = date('H', $hour);

        //get all records from campaign table
        $campaignDetails = Campaign::with(["campaignLeadDeliveryOptions", "campaignLeadDeliveryOptions.mstLeadCampaignDeliveryType"])->orderBy('campaign_id', 'desc')->get()->toArray();
        if (count($campaignDetails) > 0) {
            foreach ($campaignDetails as $campaign) {
                $campaignIdArray[] = $campaign['campaign_id'];
                $businessIdArray[] = $campaign['business_id'];
            }
        }

        if (count($campaignIdArray) > 0) {
            //get all records from business table
            $businessData = DB::table('business')->select('business_id', 'credit_available', 'credit_reserved')->whereIn('business_id', $businessIdArray)->get(['business_id', 'credit_available', 'credit_reserved'])->toArray();
            for ($b = 0; $b < count($businessData); $b++) {
                $businessArray[$businessData[$b]->business_id] = $businessData[$b];
            }

            /*$leadRoutingData = DB::table('lead_routing')->whereIn('campaign_id', $campaignIdArray)->where('created_at', ">=", $startDate)->where('created_at', "<", $endDate)->where('route_status', 'sold')->get('campaign_id')->toArray();*/
            $leadRoutingData = LeadRouting::select('campaign_id', DB::raw('COUNT(lead_routing_id) AS total_lead'))->whereIn('campaign_id', $campaignIdArray)->where('created_at', ">=", $startDate)->where('created_at', "<", $endDate)->where('route_status', 'sold')->groupBy('campaign_id')->get()->toArray();
            for ($l = 0; $l < count($leadRoutingData); $l++) {
                /*if (isset($campaignWiseRouteCountArray[$leadRoutingData[$l]->campaign_id])) {
                    $campaignWiseRouteCountArray[$leadRoutingData[$l]->campaign_id] += 1;
                } else {
                    $campaignWiseRouteCountArray[$leadRoutingData[$l]->campaign_id] = 1;
                }*/
                if (!in_array($leadRoutingData[$l]['campaign_id'], $campaignWiseRouteCountArray)) {
                    $campaignWiseRouteCountArray[$leadRoutingData[$l]['campaign_id']] = $leadRoutingData[$l]['total_lead'];
                }
            }

            /*$campaignScheuleData = CampaignScheule::whereIn('campaign_id', $campaignIdArray)->where('day', $dayNumber)->where('is_pause', 'no')->where('start_hour', '<=', $hourNumber)->where('end_hour', '>', $hourNumber)->get()->toArray();
            for ($h = 0; $h < count($campaignScheuleData); $h++) {
                $campaignHoursArray[$campaignScheuleData[$h]['campaign_id']][] = $campaignScheuleData[$h];
            }*/

            //get campaign type name from mst_campaign_type table
            $campaignTypeData = MstCampaignType::get(['campaign_type_id', 'campaign_type'])->toArray();
            for ($t = 0; $t < count($campaignTypeData); $t++) {
                $campaignTypeArray[$campaignTypeData[$t]['campaign_type_id']][] = $campaignTypeData[$t]['campaign_type'];
            }

            //get campaign type name from mst_campaign_type table
            $campaignCategoryData = MstLeadCategory::get(['lead_category_id', 'lead_category_name'])->toArray();
            for ($c = 0; $c < count($campaignCategoryData); $c++) {
                $campaignCategoryArray[$campaignCategoryData[$c]['lead_category_id']][] = $campaignCategoryData[$c]['lead_category_name'];
            }

            $campaignMovingData = CampaignMoving::get(['campaign_id', 'move_type'])->toArray();
            for ($m = 0; $m < count($campaignMovingData); $m++) {
                $campaignMovingArray[$campaignMovingData[$m]['campaign_id']] = ucwords($campaignMovingData[$m]['move_type']);
            }

            $campaignHLData = CampaignHeavyLifting::get(['campaign_id', 'move_type'])->toArray();
            for ($l = 0; $l < count($campaignHLData); $l++) {
                $campaignMovingArray[$campaignHLData[$l]['campaign_id']] = ucwords($campaignHLData[$l]['move_type']);
            }

            $campaignCTData = CampaignCarTransport::get(['campaign_id', 'move_type'])->toArray();
            for ($c = 0; $c < count($campaignCTData); $c++) {
                $campaignMovingArray[$campaignCTData[$c]['campaign_id']] = ucwords($campaignCTData[$c]['move_type']);
            }

            $campaignLeadDialerData = CampaignLeadDialer::get(['campaign_lead_dialer_id', 'campaign_id'])->toArray();
            for ($d = 0; $d < count($campaignLeadDialerData); $d++) {
                $campaignLeadDialerArray[$campaignLeadDialerData[$d]['campaign_id']] = $campaignLeadDialerData[$d]['campaign_lead_dialer_id'];
            }
        }
        //echo '<pre>'; print_r($campaignMovingArray); die;

        if (count($campaignDetails) > 0) {
            for ($c = 0; $c < count($campaignDetails); $c++) {
                if ($campaignDetails[$c]['payment_type'] == 0) {
                    // business level credit
                    $totalCredit = 0;
                    if (isset($businessArray[$campaignDetails[$c]['business_id']])) {
                        $totalCredit = $businessArray[$campaignDetails[$c]['business_id']]->credit_available + $businessArray[$campaignDetails[$c]['business_id']]->credit_reserved;
                    }
                } else {
                    // campign level credit
                    $totalCredit = $campaignDetails[$c]['credit_available'] + $campaignDetails[$c]['credit_reserved'];
                }

                $leadCount = 0;
                if (isset($campaignWiseRouteCountArray[$campaignDetails[$c]['campaign_id']])) {
                    $leadCount = $campaignWiseRouteCountArray[$campaignDetails[$c]['campaign_id']];
                }

                $hoursArray = [];
                /*if (isset($campaignHoursArray[$campaignDetails[$c]['campaign_id']])) {
                    $hoursArray = $campaignHoursArray[$campaignDetails[$c]['campaign_id']];
                }*/

                $leadDelivery = [];
                $leadDeliveryOptions = $campaignDetails[$c]['campaign_lead_delivery_options'];
                if (count($leadDeliveryOptions) > 0) {
                    foreach ($leadDeliveryOptions as $key => $value) {
                        $leadDelivery[] =  $value['mst_lead_campaign_delivery_type']['lead_delivery_type'];
                    }
                }
                //custome encryption campaign_id
                $campaignDetails[$c]['encryption_id'] = CommonFunctions::customeEncryption($campaignDetails[$c]['campaign_id']);
                $campaignDetails[$c]['action'] = $campaignDetails[$c]['encryption_id'];

                $campaignData = [];
                $campaignData['campaignId'] = $campaignDetails[$c]['campaign_id'];
                $campaignData['campaignName'] = $campaignDetails[$c]['campaign_name'];
                $lead_type_id = "Call";
                $lead_type_color = "bg-primary";
                if($campaignDetails[$c]['lead_type_id'] == 1){
                    $lead_type_id = "Lead";
                    $lead_type_color = "bg-success";
                }
                $campaignData['leadType'] = $lead_type_id;
                $campaignData['leadTypeColor'] = $lead_type_color;
                if ($campaignDetails[$c]['payment_type'] == 0) {
                    $campaignData['credit'] = ($totalCredit == 0) ? 0 : number_format($totalCredit, 2, '.', ''). ' (B)';
                } else {
                    $campaignData['credit'] = ($totalCredit == 0) ? 0 : number_format($totalCredit, 2, '.', '') . ' (C)';
                }
                $campaignData['score'] = $campaignDetails[$c]['default_score'] + $campaignDetails[$c]['additional_score'] + $campaignDetails[$c]['subscription_score'];
                //$campaignData['campaignType'] = $campaignTypeArray[$campaignDetails[$c]['campaign_type_id']];
                if ($campaignDetails[$c]['is_rev_share'] == 'yes') {
                    $campaignData['campaignType'] = implode("", $campaignTypeArray[$campaignDetails[$c]['campaign_type_id']]) . ' Rev';
                } else {
                    $campaignData['campaignType'] = implode("", $campaignTypeArray[$campaignDetails[$c]['campaign_type_id']]);
                }
                //$campaignData['campaignCategory'] = $campaignCategoryArray[$campaignDetails[$c]['lead_category_id']];
                $campaignData['campaignCategory'] = implode("", $campaignCategoryArray[$campaignDetails[$c]['lead_category_id']]);
                //$campaignData['coverage'] = ($campaignDetails[$c]['lead_category_id'] != '2') ? $campaignMovingArray[$campaignDetails[$c]['campaign_id']] : '';
                if(isset($campaignMovingArray[$campaignDetails[$c]['campaign_id']])){
                    $campaignData['coverage'] = ($campaignDetails[$c]['lead_category_id'] != '2') ? $campaignMovingArray[$campaignDetails[$c]['campaign_id']] : '';
                } else {
                    $campaignData['coverage'] = '';
                }
                $campaignData['coverageColor'] = CommonFunctions::getCampaignMoveType($campaignData['coverage']);
                $campaignData['leadCount'] = $leadCount;
                $campaignData['payoutAmount'] = $campaignDetails[$c]['lead_payout'];
                $campaignData['amount'] = $totalCredit;
                $campaignData['status'] = ($campaignDetails[$c]['is_active'] == "yes") ? "Active" : "Inactive";
                $campaignData['action'] = $campaignDetails[$c]['encryption_id'];
                $campaignData['lead_delivery'] = $leadDelivery;
                if (count($hoursArray) > 0) {
                    $campaignData['time'] = 1;
                } else {
                    $campaignData['time'] = 0;
                }

                $campaignData['color'] = "#22b783"; //campaignActive
                $campaignData['progress_status'] = "All Set";
                if ($campaignData['amount'] < $campaignData['payoutAmount'] || $campaignData['amount'] <= 0) {
                    $campaignData['color'] = "#E54343"; //campaignNoFund
                    $campaignData['progress_status'] = "No Fund";
                }
                if ($campaignData['amount'] >= $campaignData['payoutAmount'] && $campaignData['amount'] > 0 && $campaignData['time'] == 0) {
                    $campaignData['color'] = "#ff9f43"; //campaignTime
                    $campaignData['progress_status'] = "No Time";
                }
                if ($campaignData['status'] == 'Inactive') {
                    $campaignData['color'] = "#000000"; //campaignInactive
                    $campaignData['progress_status'] = "Inactive";
                }
                if (isset($campaignLeadDialerArray[$campaignDetails[$c]['campaign_id']])) {
                    $campaignData['isLeadDialer'] = 1;
                } else {
                    $campaignData['isLeadDialer'] = 0;
                }
                $finalData[] = $campaignData;
            }
        }
        //echo "<pre>";print_r($finalData);die;
        return view("campaign.list")->with('campaignData', $finalData);
    }

    public function change_status(Request $request)
    {
        $input = $request->all();
        //echo "<pre>";print_r($input);die;
        $status = 0;
        $message = 'fail';
        $error = $logArray = array();
        try {
            if (Auth::check()) {
                $commonFunctionsObj = new CommonFunctions;
                $campaignId = $commonFunctionsObj->customeDecryption($request->input('campaignId'));
                if ($campaignId > 0) {
                    $campaignDetail = Campaign::where('campaign_id', $campaignId)->first();

                    $status = 1;
                    $message = 'Success';
                    $campaignStatus = "no";
                    if ($request->input('status') == "Inactive") {
                        $campaignStatus = "yes";
                    }
                    $statusArray = array();
                    $statusArray['is_active'] = $campaignStatus;
                    $statusArray['updated_at'] = date("Y-m-d H:i:s");
                    Campaign::where('campaign_id', $campaignId)->update($statusArray);

                    //Added log by BK on 21/07/2023
                    if ($campaignStatus != $campaignDetail->is_active && !empty($campaignStatus)) {
                        $logArray[] = [
                            'created_user_id' => Auth::user()->id,
                            'campaign_id' => $campaignId,
                            'field_name' => 'is_active',
                            'old_value' => $campaignDetail->is_active,
                            'new_value' => $campaignStatus,
                            'created_at' => date('Y-m-d H:i:s'),
                            'ip' => $request->ip()
                        ];
                    }
                    if (@count($logArray) > 0) {
                        CampaignUpdateLog::insert($logArray);
                    }
                }
                $responseData = ['status' => $status, 'message' => $message, 'error' => $error];
                return response()->json($responseData);
            } else {
                return redirect('/logout');
            }
        } catch (Exception $ex) {
            return redirect('/campaign-list');
        }
    }

    public function deleteUser($id)
    {
        try {
            $campaignId = CommonFunctions::customeDecryption($id);
            if ($campaignId > 0) {
                Campaign::where('campaign_id', trim($campaignId))->delete();
                return redirect('/campaign-list');
            }
        } catch (Exception $ex) {
            return redirect('/campaign-list');
        }
    }

    public function getUserRoleNames()
    {
        $user = User::with(['roles'])->find(Auth::user()->id);
        $userRole = $user->getRoleNames()->toArray();
        $eDisplayMoveSize = 0;
        if (in_array("Admin", $userRole)) {
            $eDisplayMoveSize = 1;
        }
        return $eDisplayMoveSize;
    }

    public function validateVmOrganicCampaignFields($organicData, $error)
    {
        if (empty($organicData['businessName'])) {
            $error['businessNameError'] = 'Please enter business name';
        } else {
            if (strlen($organicData['businessName']) > 50) {
                $error['businessNameError'] = 'Busiess name cannot exceed 50 characters';
            }
        }
        if (empty($organicData['businessAddress'])) {
            $error['businessAddressError'] = 'Please enter business address';
        } else {
            if (strlen($organicData['businessAddress']) > 255) {
                $error['businessAddressError'] = 'Busiess name cannot exceed 255 characters';
            }
        }
        if (empty($organicData['totalYear'])) {
            $error['totalYearError'] = 'Please enter total business year';
        }
        if (!empty($organicData['contactNumber']) && strlen($organicData['contactNumber']) != 10) {
            $error['contactNumberError'] = 'Please enter 10 digit rely number';
        }
        if ($organicData['ranking_score'] == "" || $organicData['ranking_score'] == null) {
            $error['ranking_scoreError'] = 'Please enter ranking score';
        }
        if ($organicData['isCallLead'] == 'yes') {
            if (empty($organicData['forwardNumber'])) {
                $error['forwardNumberError'] = 'Please enter forward number.';
            } else {
                if (strlen($organicData['forwardNumber']) != 10) {
                    $error['forwardNumberError'] = 'Please enter 10 digit forward number';
                }
            }
            if (empty($organicData['callCharge'])) {
                $error['callChargeError'] = 'Please enter call charge';
            } else if ($organicData['callCharge'] <= 0) {
                $error['callChargeError'] = 'Call charge must be greater than 0 value';
            }
        }
        if (empty($organicData['licence'])) {
            $error['licenceError'] = 'Please enter licence';
        } else {
            if (strlen($organicData['licence']) > 50) {
                $error['licenceError'] = 'licence number cannot exceed 50 characters';
            }
        }
        if ($organicData['review'] == '' || $organicData['review'] == null) {
            $error['reviewError'] = 'Please enter review';
        }
        if ($organicData['review'] == '' || $organicData['review'] == null) {
            $error['reviewError'] = 'Please enter review';
        } else {
            $review = (float) $organicData['review'];
            if ((0 <= $review) && ($review <= 5)) {
            } else {
                $error['reviewError'] = 'Please enter review beetween 0 to 5';
            }
        }
        if (empty($organicData['servicesOffer'])) {
            $error['servicesOfferError'] = 'Please enter at-least one service';
        }
        if (empty($organicData['serveArea'])) {
            $error['serveAreaError'] = 'Please select atleast one serve area';
        }
        // if (count($organicData['serveArea']) == 0) {
        //     $error['serveAreaError'] = 'Please select atleast one serve area';
        // }
        if (empty($organicData['nationwide_availability'])) {
            $error['nationwide_availabilityError'] = 'Please select nationwide availability';
        }
        if (empty($organicData['storage_availability'])) {
            $error['storage_availabilityError'] = 'Please select storage availability';
        }
        if (empty($organicData['organic_company_type'])) {
            $error['organic_company_typeError'] = 'Please select company type';
        }
        if (empty($organicData['organic_heavy_equipment'])) {
            $error['organic_heavy_equipmentError'] = 'Please select heavy equipment';
        }
        if (empty($organicData['organic_car_transportation'])) {
            $error['organic_car_transportationError'] = 'Please select car transportation';
        }
        if (empty($organicData['organic_moving_container'])) {
            $error['organic_moving_containerError'] = 'Please select moving container';
        }
        if (empty($organicData['organic_language_support'])) {
            $error['organic_language_supportError'] = 'Please enter language support';
        }
        /*if (empty($organicData['organic_no_of_years'])) {
            $error['organic_no_of_yearsError'] = 'Please enter year established in';
        }*/
        if (empty($organicData['organic_price_range'])) {
            $error['organic_price_rangeError'] = 'Please select price range';
        }
        if (!empty($organicData['organic_business_url'])) {
            if (filter_var($organicData['organic_business_url'], FILTER_VALIDATE_URL) === FALSE) {
                $error['organic_business_urlError'] = 'Please enter valid url from the business website';
            }
        }
        /*if (empty($organicData['organic_mc_number'])) {
            $error['organic_mc_numberError'] = 'Please enter mc number';
        } else {
            if (strlen($organicData['organic_mc_number']) > 50) {
                $error['organic_mc_numberError'] = 'Mc number cannot exceed 50 characters';
            }
        }*/
        /*if (empty($organicData['business_about'])) {
            $error['business_aboutError'] = 'Please enter business about';
        }
        if (empty($organicData['business_highlights'])) {
            $error['business_highlightsError'] = 'Please enter at-least one business highlights';
        }*/
        return $error;
    }

    function validateTokBookingCampaignFields($bookingData, $error)
    {
        if (empty($bookingData['hourlyWage'])) {
            $error['hourlyWageError'] = 'Please enter hourly wage.';
        }
        if (empty($bookingData['transportationRate'])) {
            $error['transportationRateError'] = 'Please transportation rate.';
        }
        if (empty($bookingData['locationZipcode'])) {
            $error['locationZipcodeError'] = 'Please enter warehouse location zipcode.';
        }
        if (empty($bookingData['avgCostStairs'])) {
            $error['avgCostStairsError'] = 'Please enter average cost for stairs.';
        }
        if (empty($bookingData['minimumHours'])) {
            $error['minimumHoursError'] = 'Please enter minimum hours.';
        }
        return $error;
    }

    public function validateJunkCategoryFields($junkData, $error)
    {
        //echo "<pre>";print_r($junkData);die;
        if (!isset($junkData['junkType']) || count($junkData['junkType']) == 0) {
            $error['junkTypeError'] = 'Please select at least one junk type';
        }
        if (!isset($junkData['junkSubType']) || count($junkData['junkSubType']) == 0) {
            $error['junkSubTypeError'] = 'Please select at least one junk sub type';
        }
        return $error;
    }

    public function validateLiftCategoryFields($liftData, $error)
    {
        //echo "<pre>";print_r($liftData);die;
        if (empty($liftData['heavyLiftType'])) {
            $error['heavyLiftTypeError'] = 'Please select heavy lift type';
        }
        return $error;
    }

    public function validateCarCategoryFields($carData, $error)
    {
        //echo "<pre>";print_r($liftData);die;
        if (empty($carData['carType'])) {
            $error['carTypeError'] = 'Please select car type';
        }
        return $error;
    }

    public function insertUpdateVmOrganicData($campaignId, $organicData, $createdbyuser, $ip='')
    {
        $businessName = $organicData['businessName'];
        $businessAddress = $organicData['businessAddress'];
        $addressLocality = (isset($organicData['addressLocality'])) ? $organicData['addressLocality'] : "";
        $addressRegion = (isset($organicData['addressRegion'])) ? $organicData['addressRegion'] : "";
        $postalCode = (isset($organicData['postalCode'])) ? $organicData['postalCode'] : "";
        $totalYear = $organicData['totalYear'];
        $contactNumber = (isset($organicData['contactNumber'])) ? $organicData['contactNumber'] : "";
        $ranking_score = $organicData['ranking_score'] ?? 0;
        $forwardNumber = $organicData['forwardNumber'] ?? 0;
        $callCharge = $organicData['callCharge'] ?? 0;
        $licence = $organicData['licence'];
        $review = $organicData['review'] ?? "";
        $servicesOffer = $organicData['servicesOffer'] ?? "";
        $otherServicesOffer = $organicData['other_services_offer'] ?? "";
        $serveArea = $organicData['serveArea'];
        $nationwide_availability = (isset($organicData['nationwide_availability'])) ? $organicData['nationwide_availability'] : "";
        $storage_availability = (isset($organicData['storage_availability'])) ? $organicData['storage_availability'] : "";
        $organic_company_type = (isset($organicData['organic_company_type'])) ? $organicData['organic_company_type'] : "";
        $organic_heavy_equipment = (isset($organicData['organic_heavy_equipment'])) ? $organicData['organic_heavy_equipment'] : "";
        $organic_car_transportation = (isset($organicData['organic_car_transportation'])) ? $organicData['organic_car_transportation'] : "";
        $organic_moving_container = (isset($organicData['organic_moving_container'])) ? $organicData['organic_moving_container'] : "";
        $organic_language_support = (isset($organicData['organic_language_support'])) ? $organicData['organic_language_support'] : "";
        $organic_no_of_years = (isset($organicData['organic_no_of_years'])) ? $organicData['organic_no_of_years'] : "";
        $organic_cancellation_policy = (isset($organicData['organic_cancellation_policy'])) ? $organicData['organic_cancellation_policy'] : "";
        $organic_price_range = isset($organicData['organic_price_range']) ? $organicData['organic_price_range'] : "";
        $organic_business_url = (isset($organicData['organic_business_url'])) ? $organicData['organic_business_url'] : "";
        $organic_mc_number = (isset($organicData['organic_mc_number'])) ? $organicData['organic_mc_number'] : "";
        $business_about = (isset($organicData['business_about'])) ? $organicData['business_about'] : "";
        $business_highlights = (isset($organicData['business_highlights'])) ? $organicData['business_highlights'] : "";
        $other_business_highlights = (isset($organicData['other_business_highlights'])) ? $organicData['other_business_highlights'] : "";
        $moveType = $organicData['moveType'] ?? "";
        $movingServices = $organicData['movingServices'] ?? "";
        $isFullLead = $organicData['isFullLead'];
        $isCallLead = $organicData['isCallLead'];

        $vmOrganicArray = $logArray = array();
        $logDetails = "";
        $vmOrganicArray['campaign_id'] = $campaignId;
        $vmOrganicArray['business_name'] = $businessName;
        $vmOrganicArray['business_address'] = $businessAddress;
        $vmOrganicArray['address_locality'] = $addressLocality;
        $vmOrganicArray['address_region'] = $addressRegion;
        $vmOrganicArray['postal_code'] = $postalCode;
        $vmOrganicArray['total_business_year'] = $totalYear;
        $vmOrganicArray['services_offer'] = (!empty($servicesOffer)) ? implode(",", $servicesOffer) : "";
        $vmOrganicArray['other_services_offer'] = $otherServicesOffer;
        $vmOrganicArray['contact_number'] = $contactNumber;
        $vmOrganicArray['forward_number'] = $forwardNumber;
        $vmOrganicArray['call_charge'] = $callCharge;
        $vmOrganicArray['serve_area'] = implode(",", $serveArea);
        $vmOrganicArray['is_nationwide_availability'] = $nationwide_availability;
        $vmOrganicArray['is_storage_availability'] = $storage_availability;
        $vmOrganicArray['company_type'] = $organic_company_type;
        $vmOrganicArray['is_heavy_equipment'] = $organic_heavy_equipment;
        $vmOrganicArray['is_car_transportation'] = $organic_car_transportation;
        $vmOrganicArray['is_moving_container'] = $organic_moving_container;
        $vmOrganicArray['language_support'] = $organic_language_support;
        $vmOrganicArray['no_of_years'] = $organic_no_of_years;
        $vmOrganicArray['cancellation_policy'] = $organic_cancellation_policy;
        $vmOrganicArray['price_range'] = $organic_price_range;
        $vmOrganicArray['business_url'] = $organic_business_url;
        $vmOrganicArray['mc_number'] = $organic_mc_number;
        $vmOrganicArray['licence'] = $licence;
        $vmOrganicArray['review'] = $review;
        $vmOrganicArray['ranking_score'] = $ranking_score;
        $vmOrganicArray['business_about'] = $business_about;
        $vmOrganicArray['business_highlights'] = (!empty($business_highlights)) ? implode(",", $business_highlights) : "";
        $vmOrganicArray['other_business_highlights'] = $other_business_highlights;
        $vmOrganicArray['move_type'] = (!empty($moveType)) ? implode(",", $moveType) : "";
        $vmOrganicArray['moving_services'] = (!empty($movingServices)) ? implode(",", $movingServices) : "";
        $vmOrganicArray['is_full_lead'] = $isFullLead;
        $vmOrganicArray['is_call_lead'] = $isCallLead;
        $vmOrganicArray['created_at'] = date('Y-m-d H:i:s');
        $checkvmData = CampaignOrganic::where('campaign_id', $campaignId)->first();

        if ($checkvmData) {
            $vmOrganicArray['ranking_score'] = ($ranking_score == 0) ? $checkvmData->ranking_score : $ranking_score;
            $vmOrganicArray['forward_number'] = ($forwardNumber == 0) ? $checkvmData->forward_number : $forwardNumber;
            $vmOrganicArray['call_charge'] = ($callCharge == 0) ? $checkvmData->call_charge : $callCharge;
            $vmOrganicArray['contact_number'] = ($contactNumber == "") ? $checkvmData->contact_number : $contactNumber;
            $vmOrganicArray['review'] = ($review == "") ? $checkvmData->review : $review;
            if ( !empty($businessName) && $checkvmData->business_name != $businessName) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_business_name',
                    'old_value' => $checkvmData->business_name,
                    'new_value' => $businessName,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($businessAddress) && $checkvmData->business_address != $businessAddress ){
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_business_address',
                    'old_value' => $checkvmData->business_address,
                    'new_value' => $businessAddress,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($addressLocality) && $checkvmData->address_locality != $addressLocality ){
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_address_locality',
                    'old_value' => $checkvmData->address_locality,
                    'new_value' => $addressLocality,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($addressRegion) && $checkvmData->address_region != $addressRegion ){
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_address_region',
                    'old_value' => $checkvmData->address_region,
                    'new_value' => $addressRegion,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($postalCode) && $checkvmData->postal_code != $postalCode ){
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_postal_code',
                    'old_value' => $checkvmData->postal_code,
                    'new_value' => $postalCode,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($totalYear) && $checkvmData->total_business_year != $totalYear) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_total_business_year',
                    'old_value' => $checkvmData->total_business_year,
                    'new_value' => $totalYear,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($contactNumber) && $contactNumber != $checkvmData->contact_number) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_contact_number',
                    'old_value' => $checkvmData->contact_number,
                    'new_value' => $contactNumber,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($ranking_score) && $ranking_score != $checkvmData->ranking_score) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_ranking_score',
                    'old_value' => $checkvmData->ranking_score,
                    'new_value' => $ranking_score,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($forwardNumber) && $forwardNumber != $checkvmData->forward_number) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_forward_number',
                    'old_value' => $checkvmData->forward_number,
                    'new_value' => $forwardNumber,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($callCharge) && $callCharge != $checkvmData->call_charge) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_call_charge',
                    'old_value' => $checkvmData->call_charge,
                    'new_value' => $callCharge,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($licence) && $licence != $checkvmData->licence) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_licence',
                    'old_value' => $checkvmData->licence,
                    'new_value' => $licence,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($review) && $review != $checkvmData->review) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_review',
                    'old_value' => $checkvmData->review,
                    'new_value' => $review,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($servicesOffer) && $servicesOffer != $checkvmData->services_offer) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_services_offer',
                    'old_value' => $checkvmData->services_offer,
                    'new_value' => implode(",", $servicesOffer),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($otherServicesOffer) && $otherServicesOffer != $checkvmData->other_services_offer) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'other_services_offer',
                    'old_value' => $checkvmData->other_services_offer,
                    'new_value' => $otherServicesOffer,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty(implode(",", $serveArea)) && implode(",", $serveArea) != $checkvmData->serve_area) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_serve_area',
                    'old_value' => $checkvmData->serve_area,
                    'new_value' => implode(",", $serveArea),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($nationwide_availability) && $nationwide_availability != $checkvmData->is_nationwide_availability) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_is_nationwide_availability',
                    'old_value' => $checkvmData->is_nationwide_availability,
                    'new_value' => $nationwide_availability,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($storage_availability) && $storage_availability != $checkvmData->is_storage_availability) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_is_storage_availability',
                    'old_value' => $checkvmData->is_storage_availability,
                    'new_value' => $storage_availability,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_company_type) && $organic_company_type != $checkvmData->company_type) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_company_type',
                    'old_value' => $checkvmData->company_type,
                    'new_value' => $organic_company_type,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_heavy_equipment) && $organic_heavy_equipment != $checkvmData->is_heavy_equipment) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_is_heavy_equipment',
                    'old_value' => $checkvmData->is_heavy_equipment,
                    'new_value' => $organic_heavy_equipment,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_car_transportation) && $organic_car_transportation != $checkvmData->is_car_transportation) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_is_car_transportation',
                    'old_value' => $checkvmData->is_car_transportation,
                    'new_value' => $organic_car_transportation,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_moving_container) && $organic_moving_container != $checkvmData->is_moving_container) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_is_moving_container',
                    'old_value' => $checkvmData->is_moving_container,
                    'new_value' => $organic_moving_container,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_language_support) && $organic_language_support != $checkvmData->language_support) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_language_support',
                    'old_value' => $checkvmData->language_support,
                    'new_value' => $organic_language_support,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_no_of_years) && $organic_no_of_years != $checkvmData->no_of_years) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_no_of_years',
                    'old_value' => $checkvmData->no_of_years,
                    'new_value' => $organic_no_of_years,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_cancellation_policy) && $organic_cancellation_policy != $checkvmData->cancellation_policy) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_cancellation_policy',
                    'old_value' => $checkvmData->cancellation_policy,
                    'new_value' => $organic_cancellation_policy,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_price_range) && $organic_price_range != $checkvmData->price_range) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_price_range',
                    'old_value' => $checkvmData->price_range,
                    'new_value' => $organic_price_range,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_business_url) && $organic_business_url != $checkvmData->business_url) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_business_url',
                    'old_value' => $checkvmData->business_url,
                    'new_value' => $organic_business_url,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_mc_number) && $organic_mc_number != $checkvmData->mc_number) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_mc_number',
                    'old_value' => $checkvmData->mc_number,
                    'new_value' => $organic_mc_number,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($business_about) && $business_about != $checkvmData->business_about) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'business_about',
                    'old_value' => $checkvmData->business_about,
                    'new_value' => $business_about,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($business_highlights) && $business_highlights != $checkvmData->business_highlights) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'business_highlights',
                    'old_value' => $checkvmData->business_highlights,
                    'new_value' => implode(",", $business_highlights),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($other_business_highlights) && $other_business_highlights != $checkvmData->other_business_highlights) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'other_business_highlights',
                    'old_value' => $checkvmData->other_business_highlights,
                    'new_value' => $other_business_highlights,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($moveType) && $moveType != $checkvmData->move_type) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'move_type',
                    'old_value' => $checkvmData->move_type,
                    'new_value' => implode(',', $moveType),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($movingServices) && $movingServices != $checkvmData->moving_services) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'moving_services',
                    'old_value' => $checkvmData->moving_services,
                    'new_value' => implode(',', $movingServices),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($isFullLead) && $isFullLead != $checkvmData->is_full_lead) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'is_full_lead',
                    'old_value' => $checkvmData->is_full_lead,
                    'new_value' => $isFullLead,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($isCallLead) && $isCallLead != $checkvmData->is_call_lead) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'is_call_lead',
                    'old_value' => $checkvmData->is_call_lead,
                    'new_value' => $isCallLead,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            $vmOrganicId = $checkvmData->campaign_organic_id;
            CampaignOrganic::where('campaign_organic_id', $vmOrganicId)->update($vmOrganicArray);
            // echo "<pre>";
            // print_r($logArray); die;
            return $logArray;
        } else {
            if (!empty($businessName)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_business_name',
                    'new_value' => $businessName,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($businessAddress)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_business_address',
                    'new_value' => $businessAddress,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($addressLocality)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_address_locality',
                    'new_value' => $addressLocality,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($addressRegion)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_address_region',
                    'new_value' => $addressRegion,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($postalCode)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_postal_code',
                    'new_value' => $postalCode,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($totalYear)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_total_business_year',
                    'new_value' => $totalYear,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($contactNumber)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_contact_number',
                    'new_value' => $contactNumber,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($ranking_score)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_ranking_score',
                    'new_value' => $ranking_score,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($forwardNumber)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_forward_number',
                    'new_value' => $forwardNumber,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($callCharge)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_call_charge',
                    'new_value' => $callCharge,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($licence)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_licence',
                    'new_value' => $licence,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($review)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_review',
                    'new_value' => $review,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($servicesOffer)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_services_offer',
                    'new_value' => implode(",", $servicesOffer),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($otherServicesOffer)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'other_services_offer',
                    'new_value' => $otherServicesOffer,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (count($serveArea) > 0 ) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_serve_area',
                    'new_value' => implode(",", $serveArea),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($nationwide_availability)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_is_nationwide_availability',
                    'new_value' => $nationwide_availability,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($storage_availability)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_is_storage_availability',
                    'new_value' => $storage_availability,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_company_type)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_company_type',
                    'new_value' => $organic_company_type,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_heavy_equipment)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_is_heavy_equipment',
                    'new_value' => $organic_heavy_equipment,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_car_transportation)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_is_car_transportation',
                    'new_value' => $organic_car_transportation,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_moving_container)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_is_moving_container',
                    'new_value' => $organic_moving_container,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_language_support)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_language_support',
                    'new_value' => $organic_language_support,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_no_of_years)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_no_of_years',
                    'new_value' => $organic_no_of_years,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_cancellation_policy)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_cancellation_policy',
                    'new_value' => $organic_cancellation_policy,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_price_range)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_price_range',
                    'new_value' => $organic_price_range,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_business_url)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_business_url',
                    'new_value' => $organic_business_url,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($organic_mc_number)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'vmo_mc_number',
                    'new_value' => $organic_mc_number,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($business_about)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'business_about',
                    'new_value' => $business_about,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($business_highlights)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'business_highlights',
                    'new_value' => implode(",", $business_highlights),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($other_business_highlights)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'other_business_highlights',
                    'new_value' => $other_business_highlights,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($moveType)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'move_type',
                    'new_value' => implode(',', $moveType),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($movingServices)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'moving_services',
                    'new_value' => implode(',', $movingServices),
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($isFullLead)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'is_full_lead',
                    'new_value' => $isFullLead,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            if (!empty($isCallLead)) {
                $logArray[] = [
                    'created_user_id' => $createdbyuser,
                    'campaign_id' => $campaignId,
                    'field_name' => 'is_call_lead',
                    'new_value' => $isCallLead,
                    'created_at' => date('Y-m-d H:i:s'),
                    'ip' => $ip
                ];
            }
            CampaignOrganic::create($vmOrganicArray);
            return $logArray;
        }
    }

    public function insertUpdateTokBookingData($campaignId, $bookingData)
    {
        $logDetails = "";
        $hourlyWage = $bookingData['hourlyWage'];
        $transportationRate = $bookingData['transportationRate'];
        $locationZipcode = $bookingData['locationZipcode'];
        $avgCostStairs = $bookingData['avgCostStairs'];
        $minimumHours = $bookingData['minimumHours'];
        $address = $bookingData['address'];

        $tokBookingArray = array();
        $tokBookingArray['campaign_id'] = $campaignId;
        $tokBookingArray['hourly_wage'] = $hourlyWage;
        $tokBookingArray['transportation_rate'] = $transportationRate;
        $tokBookingArray['location_zipcode'] = $locationZipcode;
        $tokBookingArray['avg_cost_stairs'] = $avgCostStairs;
        $tokBookingArray['minimum_hours'] = $minimumHours;
        $tokBookingArray['address'] = $address;
        $tokBookingArray['created_at'] = date('Y-m-d H:i:s');
        //echo "<pre>";print_r($tokBookingArray);die;
        $checkbookingData = BusinessCampaignBooking::where('campaign_id', $campaignId)->get()->toArray();
        //echo "<pre>";print_r($tokBookingArray);die;
        if (count($checkbookingData) > 0) {
            if ($hourlyWage != $checkbookingData[0]['hourly_wage']) {
                if ($checkbookingData[0]['hourly_wage'] != "") {
                    $logDetails .= "<li>Old Hourly Wage : " . $checkbookingData[0]['hourly_wage'] . "</li>";
                }
                $logDetails .= "<li>Hourly Wage : " . $hourlyWage . "</li>";
            }
            if ($transportationRate != $checkbookingData[0]['transportation_rate']) {
                if ($checkbookingData[0]['transportation_rate'] != "") {
                    $logDetails .= "<li>Old Transportation Rate : " . $checkbookingData[0]['transportation_rate'] . "</li>";
                }
                $logDetails .= "<li>Transportation Rate : " . $transportationRate . "</li>";
            }
            if ($locationZipcode != $checkbookingData[0]['location_zipcode']) {
                if ($checkbookingData[0]['location_zipcode'] != "") {
                    $logDetails .= "<li>Old Warehouse Location Zipcode : " . $checkbookingData[0]['location_zipcode'] . "</li>";
                }
                $logDetails .= "<li>Warehouse Location Zipcode : " . $locationZipcode . "</li>";
            }
            if ($avgCostStairs != $checkbookingData[0]['avg_cost_stairs']) {
                if ($checkbookingData[0]['avg_cost_stairs'] != "") {
                    $logDetails .= "<li>Old Average Cost for Stairs : " . $checkbookingData[0]['avg_cost_stairs'] . "</li>";
                }
                $logDetails .= "<li>Average Cost for Stairs : " . $avgCostStairs . "</li>";
            }
            if ($minimumHours != $checkbookingData[0]['minimum_hours']) {
                if ($checkbookingData[0]['minimum_hours'] != "") {
                    $logDetails .= "<li>Old Minimum Chargeable Hours : " . $checkbookingData[0]['minimum_hours'] . "</li>";
                }
                $logDetails .= "<li>Minimum Chargeable Hours " . $minimumHours . "</li>";
            }
            if ($address != $checkbookingData[0]['address']) {
                if ($checkbookingData[0]['address'] != "") {
                    $logDetails .= "<li>Old Address : " . $checkbookingData[0]['address'] . "</li>";
                }
                $logDetails .= "<li>Address : " . $address . "</li>";
            }

            $tokBookingId = $checkbookingData[0]['id'];
            BusinessCampaignBooking::where('id', $tokBookingId)->update($tokBookingArray);

            return $logDetails;
        } else {
            if (!empty($hourlyWage)) {
                $logDetails .= "<li>Hourly Wage : " . $hourlyWage . "</li>";
            }
            if (!empty($transportationRate)) {
                $logDetails .= "<li>Transportation Rate : " . $transportationRate . "</li>";
            }
            if (!empty($locationZipcode)) {
                $logDetails .= "<li>Warehouse Location Zipcode : " . $locationZipcode . "</li>";
            }
            if (!empty($avgCostStairs)) {
                $logDetails .= "<li>Average Cost for Stairs : " . $avgCostStairs . "</li>";
            }
            if (!empty($minimumHours)) {
                $logDetails .= "<li>Minimum Chargeable Hours " . $minimumHours . "</li>";
            }
            if (!empty($address)) {
                $logDetails .= "<li>Address : " . $address . "</li>";
            }
            BusinessCampaignBooking::create($tokBookingArray);
            return $logDetails;
        }
    }

    public function insertUpdateCategoryData($campaignId, $categoryData, $campaignCategory, $createdbyuser, $ip='')
    {
        $logDetails = $description = "";
        $junkSubType = [];
        $logArray = $carTypes = $carType = $carArr = [];

        $categoryArray = array();
        $categoryArray['campaign_id'] = $campaignId;
        $categoryArray['created_at'] = date('Y-m-d H:i:s');
        if ($campaignCategory == 2) {
            if (count($categoryData['junkType']) > 0) {
                $junkType = $categoryData['junkType'];
            }
            if (count($categoryData['junkSubType']) > 0) {
                // $junkSubType = $categoryData['junkSubType'];
                foreach ($categoryData['junkSubType'] as $category) {
                    $junkSubType[] = $category;
                }
            }
            $checkCategoryData = CampaignJunkType::where('campaign_id', $campaignId)->get()->toArray();
            $oldJunkType = [];
            foreach ($checkCategoryData as $category) {
                $oldJunkType[] = $category['junk_type_id'];
            }
            $checkCategoryData = CampaignJunkSubType::where('campaign_id', $campaignId)->get()->toArray();
            $oldJunkSubType = [];
            foreach ($checkCategoryData as $category) {
                $oldJunkSubType[] = $category['junk_sub_type_id'];
            }
        } else if ($campaignCategory == 3) {
            $categoryArray['move_type'] = $categoryData['leadDistance'];
            $categoryArray['min_distance'] = $categoryData['minimumDistance'];
            $categoryArray['heavy_lifting_type_id'] = $categoryData['heavyLiftType'];
            $categoryArray['is_required_assistence'] = $categoryData['loadingAssistance'];
            $categoryArray['is_operational'] = $categoryData['isOperational'];
            $categoryArray['created_user_id'] = $createdbyuser;
            $checkCategoryData = CampaignHeavyLifting::where('campaign_id', $campaignId)->get()->toArray();
        } else if ($campaignCategory == 4) {
            $categoryArray['move_type'] = $categoryData['leadDistance'];
            $categoryArray['min_distance'] = $categoryData['minimumDistance'];
            if (count($categoryData['carType']) > 0) {
                $carArr = $categoryData['carType'];
                $i = 0;
                foreach ($carArr as $key => $category) {
                    $carType[$i] = $categoryData['carType'][$key];
                    $i = $i+1;
                }
            }
            $car_typeData = CampaignCarTransportType::where('campaign_id', $campaignId)->get()->toArray();
            foreach ($car_typeData as $category) {
                $carTypes[] = $category['car_type_id'];
            }
            // $categoryArray['car_type_id'] =  $carType[0];
            $categoryArray['is_required_assistence'] = $categoryData['carLoadingAssistance'];
            $categoryArray['is_operational'] = $categoryData['carIsOperational'];
            $categoryArray['transport_type'] = $categoryData['transportType'];
            $categoryArray['car_transportation'] = $categoryData['carTransportation'];
            $categoryArray['mobile_home_transportation'] = $categoryData['mobileHomeTransportation'];
            $categoryArray['created_user_id'] = $createdbyuser;
            $checkCategoryData = CampaignCarTransport::where('campaign_id', $campaignId)->get()->toArray();
        }

        if (count($checkCategoryData) > 0) {
            if ($campaignCategory == 3) {
                $categoryId = $checkCategoryData[0]['campaign_heavy_lifting_id'];
            } else if ($campaignCategory == 4) {
                $categoryId = $checkCategoryData[0]['campaign_car_transport_id'];
            }
            if ($logDetails != "") {
                $logDetails .= "<li>Category Type : " . $campaignCategory . "</li>";
            }
            if ($campaignCategory == 2) {
                /*if ($junkType != $checkCategoryData[0]['junk_type']) {
                    if ($checkCategoryData[0]['junk_type'] != "") {
                        $logDetails .= "<li>Old Junk Type : " . $checkCategoryData[0]['junk_type'] . "</li>";
                    }
                    $logDetails .= "<li>Junk Type : " . $junkType . "</li>";
                }
                if ($junkSubType != $checkCategoryData[0]['junk_sub_type_id']) {
                    if ($checkCategoryData[0]['junk_sub_type_id'] != "") {
                        $logDetails .= "<li>Old Junk Sub Type : " . $checkCategoryData[0]['junk_sub_type_id'] . "</li>";
                    }
                    $logDetails .= "<li>Junk Sub Type : " . $junkSubType . "</li>";
                }*/
                CampaignJunkType::where('campaign_id', $campaignId)->delete();
                foreach ($junkType as $type) {
                    CampaignJunkType::create([
                        'campaign_id' => $campaignId,
                        'junk_type_id' => $type,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                }

                CampaignJunkSubType::where('campaign_id', $campaignId)->delete();
                foreach ($junkSubType as $subType) {
                    CampaignJunkSubType::create([
                        'campaign_id' => $campaignId,
                        'junk_sub_type_id' => $subType,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                }

                if (!empty($junkType) && $junkType != $oldJunkType) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'junk_type_id',
                        'old_value' => implode(',', $oldJunkType),
                        'new_value' => implode(',', $junkType),
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                if (!empty($junkSubType) && $junkSubType != $oldJunkSubType) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'junk_sub_type_id',
                        'old_value' => implode(',', $oldJunkSubType),
                        'new_value' => implode(',', $junkSubType),
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
            } else if ($campaignCategory == 3) {
                if ($categoryData['heavyLiftType'] != $checkCategoryData[0]['heavy_lifting_type_id'] && !empty($categoryData['heavyLiftType'])) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'heavy_lifting_type_id',
                        'old_value' => $checkCategoryData[0]['heavy_lifting_type_id'],
                        'new_value' => $categoryData['heavyLiftType'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                if ($categoryData['loadingAssistance'] != $checkCategoryData[0]['is_required_assistence'] && !empty($categoryData['loadingAssistance'])) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'is_required_assistence',
                        'old_value' => $checkCategoryData[0]['is_required_assistence'],
                        'new_value' => $categoryData['loadingAssistance'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                if ($categoryData['isOperational'] != $checkCategoryData[0]['is_operational'] && !empty($categoryData['isOperational'])) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'is_operational',
                        'old_value' => $checkCategoryData[0]['is_operational'],
                        'new_value' => $categoryData['isOperational'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                CampaignHeavyLifting::where('campaign_heavy_lifting_id', $categoryId)->update($categoryArray);
            } else if ($campaignCategory == 4) {
                if (!empty($carType) && $carType != $carTypes) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'car_type_id',
                        'old_value' => implode(',', $carTypes),
                        'new_value' => implode(',', $carType),
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                CampaignCarTransportType::where('campaign_id', $campaignId)->delete();
                $cartypedata = [];
                foreach ($carType as $carTypevalue) {
                    $cartypedata[] = [
                        'campaign_id' => $campaignId,
                        'car_type_id' => $carTypevalue,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                CampaignCarTransportType::insert($cartypedata);
                if ($categoryData['carLoadingAssistance'] != $checkCategoryData[0]['is_required_assistence'] && !empty($categoryData['carLoadingAssistance'])) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'is_required_assistence',
                        'old_value' => $checkCategoryData[0]['is_required_assistence'],
                        'new_value' => $categoryData['carLoadingAssistance'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                if ($categoryData['carIsOperational'] != $checkCategoryData[0]['is_operational'] && !empty($categoryData['carIsOperational'])) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'is_operational',
                        'old_value' => $checkCategoryData[0]['is_operational'],
                        'new_value' => $categoryData['carIsOperational'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                if ($categoryData['transportType'] != $checkCategoryData[0]['transport_type'] && !empty($categoryData['transportType'])) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'transport_type',
                        'old_value' => $checkCategoryData[0]['transport_type'],
                        'new_value' => $categoryData['transportType'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                if ($categoryData['carTransportation'] != $checkCategoryData[0]['car_transportation'] && !empty($categoryData['carTransportation'])) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'car_transportation',
                        'old_value' => $checkCategoryData[0]['car_transportation'],
                        'new_value' => $categoryData['carTransportation'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                if ($categoryData['mobileHomeTransportation'] != $checkCategoryData[0]['mobile_home_transportation'] && !empty($categoryData['mobileHomeTransportation'])) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'mobile_home_transportation',
                        'old_value' => $checkCategoryData[0]['mobile_home_transportation'],
                        'new_value' => $categoryData['mobileHomeTransportation'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                CampaignCarTransport::where('campaign_car_transport_id', $categoryId)->update($categoryArray);
            }
            return $logArray;
        } else {
            if ($campaignCategory == 2) {
                if (!empty($junkType)) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'junk_type_id',
                        'new_value' => implode(',', $junkType),
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                if (!empty($junkSubType)) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'junk_sub_type_id',
                        'new_value' => implode(',', $junkSubType),
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                foreach ($junkType as $type) {
                    CampaignJunkType::create([
                        'campaign_id' => $campaignId,
                        'junk_type_id' => $type,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                }
                foreach ($junkSubType as $subType) {
                    CampaignJunkSubType::create([
                        'campaign_id' => $campaignId,
                        'junk_sub_type_id' => $subType,
                        'created_at' => date('Y-m-d H:i:s')
                    ]);
                }
            } else if ($campaignCategory == 3) {
                if (!empty($categoryData['heavyLiftType'])) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'heavy_lifting_type_id',
                        'new_value' => $categoryData['heavyLiftType'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                if (!empty($categoryData['loadingAssistance'])) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'is_required_assistence',
                        'new_value' => $categoryData['loadingAssistance'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                if (!empty($categoryData['isOperational'])) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'is_operational',
                        'new_value' => $categoryData['isOperational'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                CampaignHeavyLifting::create($categoryArray);
            } else if ($campaignCategory == 4) {
                // carTransportType
                if (!empty($carType)) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'car_type_id',
                        'new_value' => implode(',', $carType),
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                $cartypedata = [];
                foreach ($carType as $carTypevalue) {
                    $cartypedata[] = [
                        'campaign_id' => $campaignId,
                        'car_type_id' => $carTypevalue,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                CampaignCarTransportType::insert($cartypedata);
                if (!empty($categoryData['carLoadingAssistance'])) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'is_required_assistence',
                        'new_value' => $categoryData['carLoadingAssistance'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                if (!empty($categoryData['carIsOperational'])) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'is_operational',
                        'new_value' => $categoryData['carIsOperational'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                if (!empty($categoryData['transportType'])) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'transport_type',
                        'new_value' => $categoryData['transportType'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                if (!empty($categoryData['carTransportation'])) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'car_transportation',
                        'new_value' => $categoryData['carTransportation'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                if (!empty($categoryData['mobileHomeTransportation'])) {
                    $logArray[] = [
                        'created_user_id' => $createdbyuser,
                        'campaign_id' => $campaignId,
                        'field_name' => 'mobile_home_transportation',
                        'new_value' => $categoryData['mobileHomeTransportation'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'ip' => $ip
                    ];
                }
                CampaignCarTransport::create($categoryArray);
            }
            return $logArray;
        }
    }

    public function getCampaignMoveSizeArray()
    {
        $moveSizeArray = array("Studio", "1 Bedroom", "2 Bedrooms", "3 Bedrooms", "4 Bedrooms", "5+ Bedrooms");
        return $moveSizeArray;
    }

    public function get_leaddialer_campaign(Request $request)
    {
        $campaignId = $request->get('campaignId');
        $businessId = $request->get('businessId');
        //echo $campaignId."===".$businessId;die;
        $selectedcampaign = [];
        if ($campaignId != 0) {
            $selectedcampaign = CampaignLeadDialer::where('live_transfer_campaign_id',  $campaignId)->pluck('campaign_id')->toArray();
        }
        if ($campaignId > 0) {
            $getExistsDialer = CampaignLeadDialer::where('campaign_id', "!=", $campaignId)->get('live_transfer_campaign_id')->toArray();
        } else {
            $getExistsDialer = CampaignLeadDialer::get('live_transfer_campaign_id')->toArray();
        }

        $campaignArray = array();
        for ($g = 0; $g < count($getExistsDialer); $g++) {
            $campaignArray[] = $getExistsDialer[$g]['live_transfer_campaign_id'];
        }
        // echo "<pre>";print_r($selectedcampaign);die;
        $leadDialerCampaign = Campaign::select([
            'campaign_id', 'campaign_name', \DB::raw('(CASE
                WHEN campaign_type_id = "1" THEN "Premium"
                WHEN campaign_type_id = "2" THEN "Exclusive"
                WHEN campaign_type_id = "4" THEN "VM Organic"
                WHEN campaign_type_id = "6" THEN "Dual Slot"
                WHEN campaign_type_id = "9" THEN "Custom"
                ELSE "Lead Dialer"
                END) AS campaignType')
        ])
            ->where('business_id', $businessId)
            ->where('lead_type_id', '1')
            ->whereIn('campaign_type_id', ['1', '2', '4','6', '9'])
            ->whereNotIn('campaign_id', $campaignArray)
            ->orderBy('lead_type_id', 'ASC')
            ->orderBy('campaign_name', 'ASC')
            ->get()->toArray();
        //  echo "<pre>";print_r($campaignArray);die;
        $responseData = [
            'selectedcampaign' => $selectedcampaign,
            'leadDialerCampaign' => $leadDialerCampaign
        ];
        return response()->json($responseData);
    }

    public function get_areacode_zipcode(Request $request)
    {
        $status = 0;
        $message = 'Failure';
        $data = array();
        try {
            $states = $request->get('states');
            $coverage = $request->get('coverage');
            $codeArray = array();

            foreach ($states as $state) {
                $codeDetails = MstZipcode::where('state', $state)
                    ->select($coverage)->distinct()->get()->toArray();
                foreach ($codeDetails as $code) {
                    $codeArray[$state][] = $code[$coverage];
                }
            }

            $status = 1;
            $message = 'Success';
            $data = $codeArray;
        } catch (Exception $e) {
            //dd($e->getMessage());
            $message = $e->getMessage();
        }
        $jsonResponse = [
            'status' => $status,
            'message' => $message,
            'data' => $data
        ];
        return response()->json($jsonResponse);
    }

    public function getCampaignDuplicate($id)
    {

        $campaignId = CommonFunctions::customeDecryption($id);
        // campaign Details
        $campaignDetails = Campaign::with('campaignMoving')
            ->with('campaignMoveSize')
            ->with('campaignCallPayout')
            ->with('campaignScheule')
            ->with('campaignJunkType')
            ->with('campaignJunkSubType')
            ->with('campaignHeavyLifting')
            ->with('campaignCarTransport')
            ->with('campaignLocation')
            ->where('campaign_id', $campaignId)->first();

        $newCampaignId = Campaign::create([
            'business_id' => $campaignDetails->business_id,
            'lead_category_id' => $campaignDetails->lead_category_id,
            'campaign_name' => $campaignDetails->campaign_name . ' Copy',
            'lead_type_id' => $campaignDetails->lead_type_id,
            'forward_number' => ($campaignDetails->lead_type_id == 2 && !empty($campaignDetails->forward_number)) ? $campaignDetails->forward_number : 0,
            'campaign_type_id' => $campaignDetails->campaign_type_id,
            'call_type' => $campaignDetails->call_type,
            'lead_payout' => 0,
            'lead_status' => $campaignDetails->lead_status,
            'is_active' => $campaignDetails->is_active,
            'payment_type' => $campaignDetails->payment_type,
            'daily_lead_limit' => $campaignDetails->daily_lead_limit,
            'daily_budget_limit' => $campaignDetails->daily_budget_limit,
            'hourly_lead_limit' => $campaignDetails->hourly_lead_limit,
            'move_days_after' => $campaignDetails->move_days_after,
            'from_exclude_date' => $campaignDetails->from_exclude_date,
            'to_exclude_date' => $campaignDetails->to_exclude_date,
            'additional_score' => $campaignDetails->additional_score,
            'created_at' => date('Y-m-d H:i:s'),
            'created_user_id' => Auth::user()->id
        ])->campaign_id;

        $campaignMoving = $campaignJunkType = $campaignJunkSubType = $campaignHeavyLifting = $campaignCarTransport = $campaignLocation = $campaignScheule = $campaignCarTransportTypes = [];
        if ($campaignDetails->lead_category_id == '1') {
            $movingDetails = CampaignMoving::where('campaign_id', $campaignId)->get()->toArray();
            if (isset($movingDetails)) {
                foreach ($movingDetails as $moving) {
                    $campaignMoving[] = [
                        'campaign_id' => $newCampaignId,
                        'move_type' => $moving['move_type'],
                        'min_distance' => $moving['min_distance'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'created_user_id' => Auth::user()->id
                    ];
                }
                CampaignMoving::insert($campaignMoving);
            }
        } else if ($campaignDetails->lead_category_id == '2') {
            $junkTypeDetails = CampaignJunkType::where('campaign_id', $campaignId)->get()->toArray();
            $junkSubTypeDetails = CampaignJunkSubType::where('campaign_id', $campaignId)->get()->toArray();
            if (isset($junkTypeDetails)) {
                foreach ($junkTypeDetails as $junkType) {
                    $campaignJunkType[] = [
                        'campaign_id' => $newCampaignId,
                        'junk_type_id' => $junkType['junk_type_id'],
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                CampaignJunkType::insert($campaignJunkType);
            }

            if (isset($junkSubTypeDetails)) {
                foreach ($junkSubTypeDetails as $junkSubType) {
                    $campaignJunkSubType[] = [
                        'campaign_id' => $newCampaignId,
                        'junk_sub_type_id' => $junkSubType['junk_sub_type_id'],
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                CampaignJunkSubType::insert($campaignJunkSubType);
            }
        } else if ($campaignDetails->lead_category_id == '3') {
            $heavyLiftingDetails = CampaignHeavyLifting::where('campaign_id', $campaignId)->get()->toArray();
            if (isset($heavyLiftingDetails)) {
                foreach ($heavyLiftingDetails as $heavyLifting) {
                    $campaignHeavyLifting[] = [
                        'campaign_id' => $newCampaignId,
                        'move_type' => $heavyLifting['move_type'],
                        'min_distance' => $heavyLifting['min_distance'],
                        'heavy_lifting_type_id' => $heavyLifting['heavy_lifting_type_id'],
                        'is_required_assistence' => $heavyLifting['is_required_assistence'],
                        'is_operational' => $heavyLifting['is_operational'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'created_user_id' => Auth::user()->id
                    ];
                }
                CampaignHeavyLifting::insert($campaignHeavyLifting);
            }
        } else if ($campaignDetails->lead_category_id == '4') {
            $carTransportDetails = CampaignCarTransport::where('campaign_id', $campaignId)->get()->toArray();
            if (isset($carTransportDetails)) {
                foreach ($carTransportDetails as $carTransport) {
                    $campaignCarTransport[] = [
                        'campaign_id' => $newCampaignId,
                        'move_type' => $carTransport['move_type'],
                        'min_distance' => $carTransport['min_distance'],
                        // 'car_type_id' => $carTransport['car_type_id'],
                        'is_required_assistence' => $carTransport['is_required_assistence'],
                        'is_operational' => $carTransport['is_operational'],
                        'created_at' => date('Y-m-d H:i:s'),
                        'created_user_id' => Auth::user()->id
                    ];
                }
                CampaignCarTransport::insert($campaignCarTransport);
            }
            $carTypeDetails = CampaignCarTransportType::where('campaign_id', $campaignId)->get()->toArray();
            if (isset($carTypeDetails)) {
                foreach ($carTypeDetails as $key => $value) {
                    $campaignCarTransportTypes[] = [
                        'campaign_id' => $newCampaignId,
                        'car_type_id' => $value['car_type_id'],
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                CampaignCarTransportType::insert($campaignCarTransportTypes);
            }
        }

        if ($campaignDetails->lead_category_id != '2') {
            if (isset($campaignDetails->campaignMoveSize)) {
                $campaignMoveSize = [];
                foreach ($campaignDetails->campaignMoveSize as $moveSize) {
                    $campaignMoveSize[] = [
                        "campaign_id" => $newCampaignId,
                        "move_size_id" => $moveSize->move_size_id,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                CampaignMoveSize::insert($campaignMoveSize);
            }
        }

        if ($campaignDetails->campaign_type_id == '4') {
            $organicCampaignData = CampaignOrganic::where('campaign_id', $campaignId)->get()->toArray();
            if (count($organicCampaignData) > 0) {
                $vmOrganicArray['campaign_id'] = $newCampaignId;
                $vmOrganicArray['business_name'] = $organicCampaignData[0]['business_name'];
                $vmOrganicArray['business_address'] = $organicCampaignData[0]['business_address'];
                $vmOrganicArray['total_business_year'] = $organicCampaignData[0]['total_business_year'];
                $vmOrganicArray['services_offer'] = $organicCampaignData[0]['services_offer'];
                $vmOrganicArray['contact_number'] = $organicCampaignData[0]['contact_number'];
                $vmOrganicArray['forward_number'] = $organicCampaignData[0]['forward_number'];
                $vmOrganicArray['call_charge'] = $organicCampaignData[0]['call_charge'];
                $vmOrganicArray['serve_area'] = $organicCampaignData[0]['serve_area'];
                $vmOrganicArray['is_nationwide_availability'] = $organicCampaignData[0]['is_nationwide_availability'];
                $vmOrganicArray['is_storage_availability'] = $organicCampaignData[0]['is_storage_availability'];
                $vmOrganicArray['company_type'] = $organicCampaignData[0]['company_type'];
                $vmOrganicArray['is_heavy_equipment'] = $organicCampaignData[0]['is_heavy_equipment'];
                $vmOrganicArray['is_car_transportation'] = $organicCampaignData[0]['is_car_transportation'];
                $vmOrganicArray['is_moving_container'] = $organicCampaignData[0]['is_moving_container'];
                $vmOrganicArray['language_support'] = $organicCampaignData[0]['language_support'];
                $vmOrganicArray['no_of_years'] = $organicCampaignData[0]['no_of_years'];
                $vmOrganicArray['cancellation_policy'] = $organicCampaignData[0]['cancellation_policy'];
                $vmOrganicArray['price_range'] = $organicCampaignData[0]['price_range'];
                $vmOrganicArray['business_url'] = $organicCampaignData[0]['business_url'];
                $vmOrganicArray['mc_number'] = $organicCampaignData[0]['mc_number'];
                $vmOrganicArray['licence'] = $organicCampaignData[0]['licence'];
                $vmOrganicArray['review'] = $organicCampaignData[0]['review'];
                $vmOrganicArray['ranking_score'] = $organicCampaignData[0]['ranking_score'];
                $vmOrganicArray['created_at'] = date('Y-m-d H:i:s');
                CampaignOrganic::insert($vmOrganicArray);
            }
        }


        if (count($campaignDetails->campaignCallPayout) > 0) {

            CampaignCallPayout::create([
                'campaign_id' => $newCampaignId,
                'real_time_call_limit' => $campaignDetails->campaignCallPayout[0]->real_time_call_limit,
                'automated_inbound' => $campaignDetails->campaignCallPayout[0]->automated_inbound,
                'inbound_call' => $campaignDetails->campaignCallPayout[0]->inbound_call,
                'automated_outbound' => $campaignDetails->campaignCallPayout[0]->automated_outbound,
                'outbound_call' => $campaignDetails->campaignCallPayout[0]->outbound_call,
                'created_at' => date('Y-m-d H:i:s')
            ])->campaign_call_payout_id;
        }

        if (isset($campaignDetails->campaignScheule)) {
            foreach ($campaignDetails->campaignScheule as $scheule) {
                $campaignScheule[] = [
                    "campaign_id" => $newCampaignId,
                    "day" => $scheule->day,
                    "start_hour" => $scheule->start_hour,
                    "end_hour" => $scheule->end_hour,
                    "status" => $scheule->status,
                    "daily_limit" => $scheule->daily_limit,
                    "is_pause" => $scheule->is_pause,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            CampaignScheule::insert($campaignScheule);
        }

        if (isset($campaignDetails->campaignLocation)) {
            foreach ($campaignDetails->campaignLocation as $location) {
                $campaignLocation[] = [
                    "campaign_id" => $newCampaignId,
                    "location_coverage" => $location->location_coverage,
                    "location_type" => $location->location_type,
                    "value" => $location->value,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }
            $campaignLocation = collect($campaignLocation);
            foreach($campaignLocation->chunk(100) as $chunk){
                CampaignLocation::insert($chunk->toArray());
            }
            // CampaignLocation::insert($campaignLocation);
        }

        $leadDelieveryTypeDetails = CampaignLeadDeliveryType::where('campaign_id', $campaignId)
            ->with('campaignEmailDelivery')
            ->with('campaignSmsDelivery')
            ->with('campaignCrmDelivery')
            ->get();

        if (isset($leadDelieveryTypeDetails)) {
            foreach ($leadDelieveryTypeDetails as $leadDelieveryType) {
                $leadEmailDelivery = $leadSmsDelivery = $leadCrmDelivery = $campaignCrmDeliveryId = [];

                if ($leadDelieveryType->lead_delivery_type_id == 1) {
                    if (isset($leadDelieveryType->campaignEmailDelivery)) {
                        $leadDeliveryTypeId = CampaignLeadDeliveryType::create([
                            "campaign_id" => $newCampaignId,
                            "lead_delivery_type_id" => $leadDelieveryType->lead_delivery_type_id,
                            "created_user_id" => Auth::user()->id,
                            "created_at" => date('Y-m-d H:i:s')
                        ])->campaign_lead_delivery_type_id;

                        foreach ($leadDelieveryType->campaignEmailDelivery as $emailDelivery) {
                            $leadEmailDelivery[] = [
                                "campaign_lead_delivery_type_id" => $leadDeliveryTypeId,
                                "to_email" => $emailDelivery->to_email,
                                "lead_delivery_email_template_id" => $emailDelivery->lead_delivery_email_template_id,
                                'created_at' => date('Y-m-d H:i:s'),
                                'created_user_id' => Auth::user()->id
                            ];
                        }
                        CampaignEmailDelivery::insert($leadEmailDelivery);
                    }
                }

                if ($leadDelieveryType->lead_delivery_type_id == 3) {
                    if (isset($leadDelieveryType->campaignSmsDelivery)) {
                        $leadDeliveryTypeId = CampaignLeadDeliveryType::create([
                            "campaign_id" => $newCampaignId,
                            "lead_delivery_type_id" => $leadDelieveryType->lead_delivery_type_id,
                            "created_user_id" => Auth::user()->id,
                            "created_at" => date('Y-m-d H:i:s')
                        ])->campaign_lead_delivery_type_id;

                        foreach ($leadDelieveryType->campaignSmsDelivery as $smsDelivery) {
                            $leadSmsDelivery[] = [
                                "campaign_lead_delivery_type_id" => $leadDeliveryTypeId,
                                "phone" => $smsDelivery->phone,
                                "lead_delivery_sms_carrier_id" => $smsDelivery->lead_delivery_sms_carrier_id,
                                'created_at' => date('Y-m-d H:i:s'),
                                'created_user_id' => Auth::user()->id
                            ];
                        }
                        CampaignSmsDelivery::insert($leadSmsDelivery);
                    }
                }

                if ($leadDelieveryType->lead_delivery_type_id == 4) {
                    if (isset($leadDelieveryType->campaignCrmDelivery)) {
                        $leadDeliveryTypeId = CampaignLeadDeliveryType::create([
                            "campaign_id" => $newCampaignId,
                            "lead_delivery_type_id" => $leadDelieveryType->lead_delivery_type_id,
                            "created_user_id" => Auth::user()->id,
                            "created_at" => date('Y-m-d H:i:s')
                        ])->campaign_lead_delivery_type_id;

                        foreach ($leadDelieveryType->campaignCrmDelivery as $crmDelivery) {
                            $leadCrmDelivery[] = [
                                "campaign_lead_delivery_type_id" => $leadDeliveryTypeId,
                                "delivery_crm_id" => $crmDelivery->delivery_crm_id,
                                "post_url" => $crmDelivery->post_url,
                                "campaign_id" => $newCampaignId,
                                'created_at' => date('Y-m-d H:i:s'),
                                'user_id' => Auth::user()->id
                            ];

                            $campaignCrmDeliveryId[] = $crmDelivery->campaign_crm_delivery_id;
                        }
                        CampaignCrmDelivery::insert($leadCrmDelivery);

                        $campaignCrmDeliveryDetail = CampaignCrmDelivery::where('campaign_id', $newCampaignId)->get();
                        if (count($campaignCrmDeliveryDetail) > 0) {
                            $leadDelieveryDefaultFieldDetail = LeadDeliveryCrmDefaultFields::whereIn('campaign_crm_delivery_id', $campaignCrmDeliveryId)->get()->toArray();
                            //echo '<pre>'; print_r($leadDelieveryDefaultFieldDetail); die;
                            foreach ($campaignCrmDeliveryDetail as $key => $crmDelivery) {
                                $leadDelieveryDefaultField[] = [
                                    "campaign_crm_delivery_id" => $crmDelivery->campaign_crm_delivery_id,
                                    "delivery_mapping_fields_id" => $leadDelieveryDefaultFieldDetail[$key]['delivery_mapping_fields_id'],
                                    "variable_name" => $leadDelieveryDefaultFieldDetail[$key]['variable_name'],
                                    "variable_value" => $leadDelieveryDefaultFieldDetail[$key]['variable_value'],
                                    'created_at' => date('Y-m-d H:i:s')
                                ];
                            }
                            LeadDeliveryCrmDefaultFields::insert($leadDelieveryDefaultField);
                        }
                    }
                }
            }
        }
        return redirect('/campaign-list')->with('status', 'Campaign Duplicate Created.');
    }

    public function getBusinessData(Request $request)
    {
        $businessesArray = array();
        $forwardNo = $businessLiccence = $businessDotNumber = $relayNo = "";
        $businessesData = Business::where('business_id', $request->get('businessesId'))->first()->toArray();
        if (isset($businessesData['license_number'])) {
            $businessLiccence = $businessesData['license_number'];
            //$businessDotNumber = $businessesData['dot_number'];
            $forwardNo = $businessesData['forward_number'];
            $relayNo = $businessesData['rely_number'];
        }
        $businessesArray['licence'] = $businessLiccence;
        //$businessesArray['dot_number'] = $businessDotNumber;
        $businessesArray['forwardno'] = (string) $forwardNo;
        $businessesArray['relaydno'] = (string) $relayNo;
        return response()->json($businessesArray);
    }
}
