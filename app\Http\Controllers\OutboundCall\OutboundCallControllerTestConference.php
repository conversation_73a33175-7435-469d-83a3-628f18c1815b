<?php

namespace App\Http\Controllers\OutboundCall;

use App\Http\Controllers\Controller;
use Auth;
use DB;
use Illuminate\Http\Request;
use App\Models\SalesRep\RepActiveCall;
use App\Models\SalesRep\CallItUserLoginLog;
use App\Models\Outbound\LeadCall;
use App\Models\Outbound\LeadCallLog;
use App\Models\Outbound\CallitSystemLogic;
//use App\Models\Outbound\CallitVoiceMessageLog;
use App\Models\Lead\Lead;
use App\Models\Lead\LeadCallBuffer;
use App\Models\Lead\LeadOldCallBuffer;
use App\Models\Lead\LeadTransferCall;
use App\Models\Lead\LeadRouting;
use App\Models\Lead\LeadMoving;
use App\Models\Lead\LeadCarTransport;
use App\Models\Lead\LeadHeavyLifting;
use App\Models\Lead\LeadFollowUp;
use App\Models\Master\MstLeadSource;
use App\Http\Controllers\LeadCallBuffer\LeadCallBufferController;
use App\Http\Controllers\API\LeadDelivery\LeadDeliveryApi;
use App\Helpers\Helper;
use App\Helpers\CommonFunctions;
use Exception;
use App\Models\Campaign\Campaign;
use App\Models\Campaign\CampaignOngoingCall;
use App\Models\Campaign\CampaignCallPayout;
use App\Models\Business\Business;
use App\Models\User;
use App\Models\Master\MstCallConfig;
use App\Models\Master\MstTimeZone;
use App\Models\Master\MstCampaignType;
use App\Models\Lead\LeadActivity;
use App\Models\B2bcalls\B2BCalls;
use DateTime;
use DateTimeZone;
use App\Http\Controllers\Lead\LeadListController;
use App\Models\Lead\LeadCustomerSmsLog;
use App\Models\Inbound\LeadIbCall;
use App\Http\Controllers\PlivoSms\FollowUpCallSms;

class OutboundCallControllerTestConference extends Controller {

    public function testCallR(Request $req) {
        $Parameters = $req->all();
        $leadId = "";
        if (isset($Parameters['lead_id'])) {
           $leadId = $Parameters['lead_id'];
        }
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        $from = "***********";

        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $conference_url = $matrixBaseURL_Outbound.'/testcalltocustconf?lead_id='.$leadId;

        
        $general_variable_2 = Helper::checkServer()['general_variable_2'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
        
        
        $this->makeOutboundConferenceCall("***********", $from, $conference_url, $decVariable1, $decVariable2);

        //$this->makeOutboundConferenceCall("***********", $from, $conference_url, $decVariable1, $decVariable2);

        

    }
    public function makeOutboundConferenceCall($to, $from, $answer_url, $auth_id, $auth_token){
        $url = "https://api.plivo.com/v1/Account/{$auth_id}/Call/";
        $data = [
            'from' => $from,
            'to' => $to,
            'answer_url' => $answer_url,
            'answer_method' => 'POST'
        ];

        $ch = curl_init($url);
        curl_setopt_array($ch, [
            CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
            CURLOPT_USERPWD => "{$auth_id}:{$auth_token}",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query($data),
        ]);

        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        //echo "📞 Call to $to => HTTP $status\nResponse: $response\n\n";
    }
    public function testCallC(Request $req) {
        $Parameters = $req->all();
        $leadId = "";
        if (isset($Parameters['lead_id'])) {
           $leadId = $Parameters['lead_id'];
        }
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        $from = "***********";

        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $conference_url = $matrixBaseURL_Outbound.'/testcalltocustconftwo?lead_id='.$leadId;

        
        $general_variable_2 = Helper::checkServer()['general_variable_2'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $decVariable2 = $endDecObj->customeDecryption($general_variable_2);


        $event = $Parameters['event'];
        if (!in_array($event, ['answer', 'startapp']) && $Parameters['CallStatus'] !== 'in-progress') {
            //$this->makeOutboundConferenceCall("***********", $from, $conference_url, $decVariable1, $decVariable2);
        } else {
            $this->makeOutboundConferenceCall("***********", $from, $conference_url, $decVariable1, $decVariable2);
        }
        

        $response = '<Response>
                    <Conference
                        
                        startConferenceOnEnter="true"
                        endConferenceOnExit="false"
                    >my-meeting-room-'.$leadId.'</Conference>
                </Response>';
        // Echo the Response
        header("Content-type: text/xml");
        DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 snapIt log callforwardtomain call==" . json_encode($req->all())]);
        echo $response;

        

        exit;
    }
    public function testCallCTwo(Request $req) {
        $Parameters = $req->all();
        $leadId = "";
        if (isset($Parameters['lead_id'])) {
           $leadId = $Parameters['lead_id'];
        }
        $response = '<Response>
                    <Conference
                        waitSound="https://s3.amazonaws.com/plivocloud/music.mp3"
                        startConferenceOnEnter="true"
                        endConferenceOnExit="true"
                    >my-meeting-room-'.$leadId.'</Conference>
                </Response>';
        // Echo the Response
        header("Content-type: text/xml");
        DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 snapIt log callforwardtomain call==" . $response]);
        echo $response;
        exit;
    }
    public function conferenceCall(Request $req) {
        
        $Parameters = $req->all();
        DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 snapIt log callforwardtomain Request==" . json_encode($Parameters)]);

        if(isset($Parameters['mover'])) {


            try {
                $endDecObj = new Helper;
                $general_variable_1 = Helper::checkServer()['general_variable_1'];
                $general_variable_2 = Helper::checkServer()['general_variable_2'];
                $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
                $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
                $plivoBaseURL = 'https://api.plivo.com/v1/Account/' . $decVariable1;
                $callUuid = $Parameters['CallUUID'];
                $para = json_encode(array('time_limit' => '3600'));
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $plivoBaseURL . '/Call/' . $callUuid . '/Record/');
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $para);
                curl_setopt($ch, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
                $headers = array();
                $headers[] = 'Content-Type: application/json';
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                $result = curl_exec($ch);
                DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => $plivoBaseURL . '/Call/' . $callUuid . '/Record/']);
                DB::table('dev_debug')->insert(['sr_no' => 1, 'result' => "1 snapIt log CallRecord call==" . $result]);
                if (curl_errno($ch)) {
                    //echo 'Error:' . curl_error($ch);
                }
                curl_close($ch);
            } catch (Exception $ex) {
                $error = $ex->getMessage();
                DB::table('dev_debug')->insert(['sr_no' => 2, 'result' => "2 snapIt log CallRecord call catch error==" . $error]);
            }


        }

        $conferenceName = "Call_";
        if (isset($Parameters['lead_call_id'])) {
           $conferenceName .= $Parameters['lead_call_id'];
        }
        $response = '<Response>
                    <Conference
                        waitSound="ring"
                        startConferenceOnEnter="true"
                        endConferenceOnExit="true"
                    >'.$conferenceName.'</Conference>
                </Response>';
        // Echo the Response
        header("Content-type: text/xml");
        DB::table('dev_debug')->insert(['sr_no' => 3, 'result' => "3 snapIt log callforwardtomain call==" . $response]);
        echo $response;
        //exit;
    }

}
