<?php

namespace App\Http\Controllers\GoogleAdwords;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\GoogleAdwords\GoogleAdwordsCampaignCPA;
use App\Http\Controllers\GoogleAdwords\GoogleAdwordsGetCampaignsManualController;
use App\Http\Controllers\GoogleAdwords\GoogleAdwordsUpdateCampaignManualController;
use DB;
use Log;

class GoogleAdwordsCampaignController extends Controller
{
    //public static $CUSTOMER_ID;
    /*function __construct()
    {
        $this->middleware('permission:google-adwords-mt-adwords', ['only' => ['VMAdwords', 'VMAdwordsUpdate']]);
        $this->middleware('permission:google-adwords-pmm-adwords', ['only' => ['QTMAdwords', 'QTMAdwordsUpdate']]);
        $this->middleware('permission:google-adwords-pmm-adwords', ['only' => ['ISAdwords', 'ISAdwordsUpdate']]);
    }*/

    //for VM adwords campaign list
    public function VMAdwords (Request $request)
    {
        //print_r($request->type); die;
        $allGoogleAdwordsCampaign                   = GoogleAdwordsCampaignCPA::where('lead_source_id', 15)->where('bidding_strategy_type', strtoupper($request->type))->get();
        return view("googleadwords.index")->with('response', $allGoogleAdwordsCampaign)->with('fetchUrl','vm-campaign-fetch')->with('updateUrl','vm-campaign-update')->with('bidPercentage','vm-campaign-bid-percentage')->with('type', $request->type)->with('site', 'VM');
    }

    public function VMAdwordsFetch(Request $request) {
        //echo '<pre>'; print_r($request->campaignId); die;
        $status = 0;
        $message = 'Failed';
        $error = '';
        try{
            /*if (isset($request['campaignId']) && !empty($request['campaignId'])) {
                $campaignIdArray = $campaignArray = array();
                $response = GoogleAdwordsGetCampaignsManualController::main('VM', [$request['campaignId']]);
                //echo '<pre>'; print_r($response); die;
                $allGoogleAdwordsCampaign           = GoogleAdwordsCampaignCPA::where('lead_source_id', 15)->get();
                foreach ($allGoogleAdwordsCampaign as $campaign) {
                    $campaignIdArray[]              =  $campaign->campaign_id;
                }

                foreach ($response as $campaign) {
                    if (in_array($campaign->campaign_id, $campaignIdArray)) {
                        break;
                    }
                    $campaignArray[]                =  [
                        'lead_source_id'            => 15,
                        'campaign_id'               => $campaign->campaign_id,
                        'campaign_name'             => $campaign->campaign_name,
                        'advertising_channel_type'  => $campaign->advertising_channel_type,
                        'bidding_strategy_type'     => $campaign->bidding_strategy_type,
                        'current_bid'               => $campaign->target_cpa,
                        'created_at'                => date('Y-m-d H:i:s')
                    ];
                }
                if (count($campaignArray) > 0) {
                    GoogleAdwordsCampaignCPA::insert($campaignArray);
                    $status = 1;
                    $message = 'Success';
                } else {
                    $error = 'Error: No campaign is available.';
                }
            } else {
                $error = 'Error: No campaign is available.';
            }*/

            $campaignIdArray = $campaignArray = array();
            $campaignIds                                = ($request['campaignId'] > 0) ? [$request['campaignId']] : [];
            $response                                   = GoogleAdwordsGetCampaignsManualController::main('VM', $campaignIds);
            /*$allGoogleAdwordsCampaign                 = GoogleAdwordsCampaignCPA::where('lead_source_id', 15)->where('bidding_strategy_type', $request->type)->get();
            foreach ($allGoogleAdwordsCampaign as $campaign) {
                $campaignIdArray[]                      =  $campaign->campaign_id;
            }*/
            GoogleAdwordsCampaignCPA::where('lead_source_id', 15)->where('bidding_strategy_type', strtoupper($request->type))->delete();
            foreach ($response as $campaign) {
                if (strtoupper($request->type) == "TARGET_CPA" && $campaign->bidding_strategy_type == "TARGET_CPA" && empty($campaign->bidding_strategy)) {
                    /*if (in_array($campaign->campaign_id, $campaignIdArray)) {
                        continue;
                    }*/
                    $campaignArray[]                    =  [
                        'lead_source_id'                => 15,
                        'campaign_id'                   => $campaign->campaign_id,
                        'campaign_name'                 => $campaign->campaign_name,
                        'advertising_channel_type'      => $campaign->advertising_channel_type,
                        'bidding_strategy_type'         => $campaign->bidding_strategy_type,
                        'current_bid'                   => $campaign->target_cpa,
                        'created_at'                    => date('Y-m-d H:i:s')
                    ];
                } else if (strtoupper($request->type) == "MAXIMIZE_CONVERSIONS" && $campaign->bidding_strategy_type == "MAXIMIZE_CONVERSIONS") {
                    /*if (in_array($campaign->campaign_id, $campaignIdArray)) {
                        continue;
                    }*/
                    $campaignArray[]                    =  [
                        'lead_source_id'                => 15,
                        'campaign_id'                   => $campaign->campaign_id,
                        'campaign_name'                 => $campaign->campaign_name,
                        'advertising_channel_type'      => $campaign->advertising_channel_type,
                        'bidding_strategy_type'         => $campaign->bidding_strategy_type,
                        'current_bid'                   => $campaign->target_cpa,
                        'created_at'                    => date('Y-m-d H:i:s')
                    ];
                } else if (strtoupper($request->type) == "MAXIMIZE_CONVERSION_VALUE" && $campaign->bidding_strategy_type == "MAXIMIZE_CONVERSION_VALUE") {
                    /*if (in_array($campaign->campaign_id, $campaignIdArray)) {
                        continue;
                    }*/
                    $campaignArray[]                    =  [
                        'lead_source_id'                => 15,
                        'campaign_id'                   => $campaign->campaign_id,
                        'campaign_name'                 => $campaign->campaign_name,
                        'advertising_channel_type'      => $campaign->advertising_channel_type,
                        'bidding_strategy_type'         => $campaign->bidding_strategy_type,
                        'current_bid'                   => $campaign->target_cpa,
                        'created_at'                    => date('Y-m-d H:i:s')
                    ];
                }
            }
            if (count($campaignArray) > 0) {
                GoogleAdwordsCampaignCPA::insert($campaignArray);
                $status = 1;
                $message = 'Success';
            } else {
                $error = 'Error: No campaign is available.';
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
        $responseData = ['status' => $status, 'message' => $message, 'error' => $error];
        return response()->json($responseData);
    }

    public function VMAdwordsUpdate(Request $request) {
        //echo '<pre>'; print_r($request['bidding_strategy_type']); die;
        try{
            if (isset($request['campaign_ids']) && !empty($request['campaign_ids'])) {
                GoogleAdwordsUpdateCampaignManualController::main('VM', $request['campaign_ids'], $request['initial_bid'], $request['bidding_strategy_type']);

                //echo '<pre>'; print_r($response); die;
                $campaignArray                      = array();
                $campaignIds                        = (count($request['campaign_ids']) > 0) ? $request['campaign_ids'] : [];
                $response                           = GoogleAdwordsGetCampaignsManualController::main('VM', $campaignIds);

                foreach ($response as $campaign) {
                    GoogleAdwordsCampaignCPA::where('campaign_id', $campaign->campaign_id)->update([
                        'current_bid'               => $campaign->target_cpa,
                        'created_at'                => date('Y-m-d H:i:s')
                    ]);
                }
                if (count($campaignArray) > 0) {
                    GoogleAdwordsCampaignCPA::insert($campaignArray);
                    $request->session()->flash('alert-success', json_encode($response));
                } else {
                    $request->session()->flash('alert-danger', 'Error: No campaign is available.');
                }
                return redirect('/vm-campaign-list?type=' . $request->type);
            }else{
                $request->session()->flash('alert-danger', 'Error: No campaign is available.');
                return redirect('/vm-campaign-list?type=' . $request->type);
            }
        } catch (Exception $e) {
            $request->session()->flash('alert-danger', $e->getMessage());
            return redirect('/vm-campaign-list?type=' . $request->type);
        }
    }

    public function VMAdwordsBidPercentage(Request $request) {
        $status = 0;
        $message = 'Failed';
        $finalArray = [];
        $error = '';
        try{
            $allGoogleAdwordsCampaign               = GoogleAdwordsCampaignCPA::where('lead_source_id', 15)->where('bidding_strategy_type', strtoupper($request->type))->get();
            foreach ($allGoogleAdwordsCampaign as $campaign) {
                $finalArray[$campaign->campaign_id] = @number_format($campaign->current_bid * $request->bidPercentage / 100, 2);
            }
            if (count($finalArray) > 0) {
                $status = 1;
                $message = 'success';
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
        $responseData = ['status' => $status, 'message' => $message, 'data' => $finalArray, 'error' => $error];
        return response()->json($responseData);
    }

    //for QTM adwords campaign list
    public function QTMAdwords (Request $request)
    {
        //print_r($request->type); die;
        $allGoogleAdwordsCampaign                   = GoogleAdwordsCampaignCPA::where('lead_source_id', 118)->where('bidding_strategy_type', strtoupper($request->type))->get();
        return view("googleadwords.index")->with('response', $allGoogleAdwordsCampaign)->with('fetchUrl','qtm-campaign-fetch')->with('updateUrl','qtm-campaign-update')->with('bidPercentage','qtm-campaign-bid-percentage')->with('type', $request->type)->with('site', 'QTM');
    }

    public function QTMAdwordsFetch(Request $request) {
        //echo '<pre>'; print_r($request->campaign_id); die;
        $status = 0;
        $message = 'Failed';
        $error = '';
        try{
            $campaignIdArray = $campaignArray = array();
            $campaignIds                                = ($request['campaignId'] > 0) ? [$request['campaignId']] : [];
            $response                                   = GoogleAdwordsGetCampaignsManualController::main('QTM', $campaignIds);
            /*$allGoogleAdwordsCampaign                   = GoogleAdwordsCampaignCPA::where('lead_source_id', 118)->where('bidding_strategy_type', $request->type)->get();
            foreach ($allGoogleAdwordsCampaign as $campaign) {
                $campaignIdArray[]                      =  $campaign->campaign_id;
            }*/
            GoogleAdwordsCampaignCPA::where('lead_source_id', 118)->where('bidding_strategy_type', strtoupper(strtoupper($request->type)))->delete();
            foreach ($response as $campaign) {
                if (strtoupper($request->type) == "TARGET_CPA" && $campaign->bidding_strategy_type == "TARGET_CPA" && empty($campaign->bidding_strategy)) {
                    /*if (in_array($campaign->campaign_id, $campaignIdArray)) {
                        continue;
                    }*/
                    $campaignArray[]                    =  [
                        'lead_source_id'                => 118,
                        'campaign_id'                   => $campaign->campaign_id,
                        'campaign_name'                 => $campaign->campaign_name,
                        'advertising_channel_type'      => $campaign->advertising_channel_type,
                        'bidding_strategy_type'         => $campaign->bidding_strategy_type,
                        'current_bid'                   => $campaign->target_cpa,
                        'created_at'                    => date('Y-m-d H:i:s')
                    ];
                } else if (strtoupper($request->type) == "MAXIMIZE_CONVERSIONS" && $campaign->bidding_strategy_type == "MAXIMIZE_CONVERSIONS") {
                    /*if (in_array($campaign->campaign_id, $campaignIdArray)) {
                        continue;
                    }*/
                    $campaignArray[]                    =  [
                        'lead_source_id'                => 118,
                        'campaign_id'                   => $campaign->campaign_id,
                        'campaign_name'                 => $campaign->campaign_name,
                        'advertising_channel_type'      => $campaign->advertising_channel_type,
                        'bidding_strategy_type'         => $campaign->bidding_strategy_type,
                        'current_bid'                   => $campaign->target_cpa,
                        'created_at'                    => date('Y-m-d H:i:s')
                    ];
                } else if (strtoupper($request->type) == "MAXIMIZE_CONVERSION_VALUE" && $campaign->bidding_strategy_type == "MAXIMIZE_CONVERSION_VALUE") {
                    /*if (in_array($campaign->campaign_id, $campaignIdArray)) {
                        continue;
                    }*/
                    $campaignArray[]                    =  [
                        'lead_source_id'                => 118,
                        'campaign_id'                   => $campaign->campaign_id,
                        'campaign_name'                 => $campaign->campaign_name,
                        'advertising_channel_type'      => $campaign->advertising_channel_type,
                        'bidding_strategy_type'         => $campaign->bidding_strategy_type,
                        'current_bid'                   => $campaign->target_cpa,
                        'created_at'                    => date('Y-m-d H:i:s')
                    ];
                }
            }
            if (count($campaignArray) > 0) {
                GoogleAdwordsCampaignCPA::insert($campaignArray);
                $status = 1;
                $message = 'Success';
            } else {
                $error = 'Error: No campaign is available.';
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
        $responseData = ['status' => $status, 'message' => $message, 'error' => $error];
        return response()->json($responseData);
    }

    public function QTMAdwordsUpdate(Request $request) {
        //echo '<pre>'; print_r($request->campaign_id); die;
        try{
            if (isset($request['campaign_ids']) && !empty($request['campaign_ids'])) {
                GoogleAdwordsUpdateCampaignManualController::main('QTM', $request['campaign_ids'], $request['initial_bid'], $request['bidding_strategy_type']);

                //echo '<pre>'; print_r($response); die;
                $campaignArray                      = array();
                $campaignIds                        = (count($request['campaign_ids']) > 0) ? $request['campaign_ids'] : [];
                $response                           = GoogleAdwordsGetCampaignsManualController::main('QTM', $campaignIds);

                foreach ($response as $campaign) {
                    GoogleAdwordsCampaignCPA::where('campaign_id', $campaign->campaign_id)->update([
                        'current_bid'               => $campaign->target_cpa,
                        'created_at'                => date('Y-m-d H:i:s')
                    ]);
                }
                if (count($campaignArray) > 0) {
                    GoogleAdwordsCampaignCPA::insert($campaignArray);
                    $request->session()->flash('alert-success', json_encode($response));
                } else {
                    $request->session()->flash('alert-danger', 'Error: No campaign is available.');
                }
                return redirect('/qtm-campaign-list?type=' . $request->type);
            }else{
                $request->session()->flash('alert-danger', 'Error: No campaign is available.');
                return redirect('/qtm-campaign-list?type=' . $request->type);
            }
        } catch (Exception $e) {
            $request->session()->flash('alert-danger', $e->getMessage());
            return redirect('/qtm-campaign-list?type=' . $request->type);
        }
    }

    public function QTMAdwordsBidPercentage(Request $request) {
        $status = 0;
        $message = 'Failed';
        $finalArray = [];
        $error = '';
        try{
            $allGoogleAdwordsCampaign               = GoogleAdwordsCampaignCPA::where('lead_source_id', 118)->where('bidding_strategy_type', strtoupper($request->type))->get();
            foreach ($allGoogleAdwordsCampaign as $campaign) {
                $finalArray[$campaign->campaign_id] = @number_format($campaign->current_bid * $request->bidPercentage / 100, 2);
            }
            if (count($finalArray) > 0) {
                $status = 1;
                $message = 'success';
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
        $responseData = ['status' => $status, 'message' => $message, 'data' => $finalArray, 'error' => $error];
        return response()->json($responseData);
    }

    //for IS adwords campaign list
    public function ISAdwords (Request $request)
    {
        //print_r($request->type); die;
        $allGoogleAdwordsCampaign                   = GoogleAdwordsCampaignCPA::where('lead_source_id', 64)->where('bidding_strategy_type', $request->type)->get();
        return view("googleadwords.index")->with('response', $allGoogleAdwordsCampaign)->with('fetchUrl','is-campaign-fetch')->with('updateUrl','is-campaign-update')->with('bidPercentage','is-campaign-bid-percentage')->with('type', $request->type)->with('site', 'IS');
    }

    public function ISAdwordsFetch(Request $request) {
        //echo '<pre>'; print_r($request->campaign_id); die;
        $status = 0;
        $message = 'Failed';
        $error = '';
        try{
            $campaignIdArray = $campaignArray = array();
            $campaignIds                                = ($request['campaignId'] > 0) ? [$request['campaignId']] : [];
            $response                                   = GoogleAdwordsGetCampaignsManualController::main('IS', $campaignIds);
            /*$allGoogleAdwordsCampaign                 = GoogleAdwordsCampaignCPA::where('lead_source_id', 64)->where('bidding_strategy_type', strtoupper($request->type))->get();
            foreach ($allGoogleAdwordsCampaign as $campaign) {
                $campaignIdArray[]                      =  $campaign->campaign_id;
            }*/
            GoogleAdwordsCampaignCPA::where('lead_source_id', 64)->where('bidding_strategy_type', strtoupper($request->type))->delete();
            foreach ($response as $campaign) {
                if (strtoupper($request->type) == "TARGET_CPA" && $campaign->bidding_strategy_type == "TARGET_CPA" && empty($campaign->bidding_strategy)) {
                    /*if (in_array($campaign->campaign_id, $campaignIdArray)) {
                        continue;
                    }*/
                    $campaignArray[]                    =  [
                        'lead_source_id'                => 64,
                        'campaign_id'                   => $campaign->campaign_id,
                        'campaign_name'                 => $campaign->campaign_name,
                        'advertising_channel_type'      => $campaign->advertising_channel_type,
                        'bidding_strategy_type'         => $campaign->bidding_strategy_type,
                        'current_bid'                   => $campaign->target_cpa,
                        'created_at'                    => date('Y-m-d H:i:s')
                    ];
                } else if (strtoupper($request->type) == "MAXIMIZE_CONVERSIONS" && $campaign->bidding_strategy_type == "MAXIMIZE_CONVERSIONS") {
                    /*if (in_array($campaign->campaign_id, $campaignIdArray)) {
                        continue;
                    }*/
                    $campaignArray[]                    =  [
                        'lead_source_id'                => 64,
                        'campaign_id'                   => $campaign->campaign_id,
                        'campaign_name'                 => $campaign->campaign_name,
                        'advertising_channel_type'      => $campaign->advertising_channel_type,
                        'bidding_strategy_type'         => $campaign->bidding_strategy_type,
                        'current_bid'                   => $campaign->target_cpa,
                        'created_at'                    => date('Y-m-d H:i:s')
                    ];
                } else if (strtoupper($request->type) == "MAXIMIZE_CONVERSION_VALUE" && $campaign->bidding_strategy_type == "MAXIMIZE_CONVERSION_VALUE") {
                    /*if (in_array($campaign->campaign_id, $campaignIdArray)) {
                        continue;
                    }*/
                    $campaignArray[]                    =  [
                        'lead_source_id'                => 64,
                        'campaign_id'                   => $campaign->campaign_id,
                        'campaign_name'                 => $campaign->campaign_name,
                        'advertising_channel_type'      => $campaign->advertising_channel_type,
                        'bidding_strategy_type'         => $campaign->bidding_strategy_type,
                        'current_bid'                   => $campaign->target_cpa,
                        'created_at'                    => date('Y-m-d H:i:s')
                    ];
                }
            }
            if (count($campaignArray) > 0) {
                GoogleAdwordsCampaignCPA::insert($campaignArray);
                $status = 1;
                $message = 'Success';
            } else {
                $error = 'Error: No campaign is available.';
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
        $responseData = ['status' => $status, 'message' => $message, 'error' => $error];
        return response()->json($responseData);
    }

    public function ISAdwordsUpdate(Request $request) {
        //echo '<pre>'; print_r($request->campaign_id); die;
        try{
            if (isset($request['campaign_ids']) && !empty($request['campaign_ids'])) {
                GoogleAdwordsUpdateCampaignManualController::main('IS', $request['campaign_ids'], $request['initial_bid'], $request['bidding_strategy_type']);

                //echo '<pre>'; print_r($response); die;
                $campaignArray                      = array();
                $campaignIds                        = (count($request['campaign_ids']) > 0) ? $request['campaign_ids'] : [];
                $response                           = GoogleAdwordsGetCampaignsManualController::main('IS', $campaignIds);

                foreach ($response as $campaign) {
                    GoogleAdwordsCampaignCPA::where('campaign_id', $campaign->campaign_id)->update([
                        'current_bid'               => $campaign->target_cpa,
                        'created_at'                => date('Y-m-d H:i:s')
                    ]);
                }
                if (count($campaignArray) > 0) {
                    GoogleAdwordsCampaignCPA::insert($campaignArray);
                    $request->session()->flash('alert-success', json_encode($response));
                } else {
                    $request->session()->flash('alert-danger', 'Error: No campaign is available.');
                }
                return redirect('/is-campaign-list?type=' . $request->type);
            }else{
                $request->session()->flash('alert-danger', 'Error: No campaign is available.');
                return redirect('/is-campaign-list?type=' . $request->type);
            }
        } catch (Exception $e) {
            $request->session()->flash('alert-danger', $e->getMessage());
            return redirect('/is-campaign-list?type=' . $request->type);
        }
    }

    public function ISAdwordsBidPercentage(Request $request) {
        $status = 0;
        $message = 'Failed';
        $finalArray = [];
        $error = '';
        try{
            $allGoogleAdwordsCampaign               = GoogleAdwordsCampaignCPA::where('lead_source_id', 64)->where('bidding_strategy_type', strtoupper($request->type))->get();
            foreach ($allGoogleAdwordsCampaign as $campaign) {
                $finalArray[$campaign->campaign_id] = @number_format($campaign->current_bid * $request->bidPercentage / 100, 2);
            }
            if (count($finalArray) > 0) {
                $status = 1;
                $message = 'success';
            }
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
        $responseData = ['status' => $status, 'message' => $message, 'data' => $finalArray, 'error' => $error];
        return response()->json($responseData);
    }
}
