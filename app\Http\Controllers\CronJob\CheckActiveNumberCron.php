<?php

namespace App\Http\Controllers\CronJob;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use App\Helpers\Helper;
use App\Models\Emails\TemplateEmail;
use App\Models\Outbound\LeadCallNumber;
use Exception;

class CheckActiveNumberCron extends Controller
{
    public function index()
    {
        $accountId          = 22374;
        $commioToken        = "milan:68758d120ac29760793bb4c940550281ec4d51e6";
        $commioToken        = base64_encode($commioToken); //Your authentication string is a base64 encoded version of username:token

        $perPage            = 100;
        $page               = 1;
        $activeNumbers      = [];
        $dbactiveNumbers    = [];
        $pcactiveNumbers    = [];
        $dataArray          = [];
        $finalArray         = [];

        do {
            $curl           = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => "https://api.thinq.com/origination/did/search2/did/{$accountId}?rows={$perPage}&page={$page}",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => array(
                    'Authorization: Basic ' . $commioToken
                ),
            ));

            $response       = curl_exec($curl);
            curl_close($curl);
            $responseData   = json_decode($response, true);

            // Check if rows exist and extract only 'id'
            if (isset($responseData['rows']) && is_array($responseData['rows'])) {
                foreach ($responseData['rows'] as $row) {
                    if (isset($row['id'])) {
                        $activeNumbers[] = $row['id'];
                    }
                    $dataArray[$row['id']] = [
                        'id' => $row['id'],
                        'npanxx' => $row['npanxx'],
                        'callerIdSummary' => $row['callerIdSummary'] ?? '--',
                        'routingSummary' => $row['routingSummary'] ?? '--',
                        'smsRoutingProfileName' => $row['sms_routing_profile_name'] ?? '--'
                    ];
                }
            }

            // Calculate total pages
            $totalRows      = $responseData['total_rows'] ?? 0;
            $totalPages     = ceil($totalRows / $perPage);

            $page++;
        } while ($page <= $totalPages);

        $limit              = 20;
        $offset             = 0;
        $endDecObj          = new Helper;
        $variable_1         = Helper::checkServer()['general_variable_1'];
        $variable_2         = Helper::checkServer()['general_variable_2'];
        $auth_id            = $endDecObj->customeDecryption($variable_1);
        $auth_token         = $endDecObj->customeDecryption($variable_2);

        do {
            $url            = "https://api.plivo.com/v1/Account/$auth_id/Number/?limit=$limit&offset=$offset";
            $ch             = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            curl_setopt($ch, CURLOPT_USERPWD, "$auth_id:$auth_token");

            $response       = curl_exec($ch);

            if (curl_errno($ch)) {
                echo 'Error:' . curl_error($ch);
                break;
            }

            $data           = json_decode($response, true);
            if (isset($data['objects']) && count($data['objects']) > 0) {
                foreach ($data['objects'] as $number) {
                    $activeNumbers[] = $number['number'];
                }
                $offset += $limit;
            } else {
                break;
            }
            curl_close($ch);

        } while ($offset < $data['meta']['total_count']);

        if (count($activeNumbers) > 0) {
            $leadCallNumber = LeadCallNumber::where('status', 'active')->get()->toArray();
            for ($i=0; $i < count($leadCallNumber); $i++) {
                if (!in_array($leadCallNumber[$i]['phone'], $activeNumbers)) {
                    $dbactiveNumbers[] = $leadCallNumber[$i]['phone'];
                }
            }
            if (count($dbactiveNumbers) > 0) {
                $subject    = 'Mismatch PM Number';
                $setFrom    = '<EMAIL>';
                $setTo      = '<EMAIL>, <EMAIL>';
                //$setTo    = '<EMAIL>';
                $token      = 'ebfcb0c9-240e-4196-a379-1e2832103d2a';

                $bodyData   = array();
                $mail       = new TemplateEmail();

                $html       = "The following list of number are active in database: " . implode(", ", $dbactiveNumbers);
                // send email
                $mail->setTo($setTo);
                //$mail->setBcc('<EMAIL>');
                $mail->setFrom($setFrom);
                $mail->setSubject($subject);
                $mail->setHtmlbody($html);
                $mail->send_email($token, 'active_number');
            }
        }

        if (count($activeNumbers) > 0) {
            $leadCallNumber = LeadCallNumber::where('status', 'active')->pluck('phone')->toArray();
            $pcactiveNumbers= array_values(array_diff($activeNumbers, $leadCallNumber));
            if (count($pcactiveNumbers) > 0) {
                $emailBody ="<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>
                                <tr>
                                    <th>ID</th>
                                    <th>NPANXX</th>
                                    <th>Caller ID Summary</th>
                                    <th>Routing Summary</th>
                                    <th>SMS Routing Profile Name</th>
                                </tr>";
                foreach ($pcactiveNumbers as $number) {
                    $row = $dataArray[$number] ?? $number;
                    $id = $row['id'] ?? $row;
                    $npanxx = $row['npanxx'] ?? '--';
                    $callerIdSummary = $row['callerIdSummary'] ?? '--';
                    $routingSummary = $row['routingSummary'] ?? '--';
                    $smsRoutingProfileName = $row['smsRoutingProfileName'] ?? '--';
                    $emailBody .="<tr>
                                    <td>{$id}</td>
                                    <td>{$npanxx}</td>
                                    <td>{$callerIdSummary}</td>
                                    <td>{$routingSummary}</td>
                                    <td>{$smsRoutingProfileName}</td>
                                </tr>";
                    //$finalArray[] = $dataArray[$number] ?? $number;
                }
                $emailBody .= "</table>";

                $subject    = 'Mismatch PM Number';
                $setFrom    = '<EMAIL>';
                $setTo      = '<EMAIL>, <EMAIL>';
                //$setTo    = '<EMAIL>';
                $token      = 'ebfcb0c9-240e-4196-a379-1e2832103d2a';

                $bodyData   = array();
                $mail       = new TemplateEmail();

                //$html     = "The following list of number are active in plivo and commio: \n" . json_encode($finalArray, true);
                $html       = "The following list of number are active in plivo and commio: \n\n" . $emailBody;
                // send email
                $mail->setTo($setTo);
                //$mail->setBcc('<EMAIL>');
                $mail->setFrom($setFrom);
                $mail->setSubject($subject);
                $mail->setHtmlbody($html);
                $mail->send_email($token, 'active_number');
            }
        }
        echo "Succcess"; die;
    }
}
