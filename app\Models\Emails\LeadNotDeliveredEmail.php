<?php 

namespace App\Models\Emails;
use Illuminate\Database\Eloquent\Model;   
use App\Helpers\Helper;
use App\Models\Emails\EmailLog; 
use App\Models\Lead\LeadActivity;
use App\Models\Emails\LeadNotDeliveredEmail;
class LeadNotDeliveredEmail extends Model {
     
    public function emailLeadNotDeliveredEmail( $crmNotDeliveredEmailData ){
        $postmarkey = Helper::checkServer()['postmark_token'];
        $leadname = (isset($crmNotDeliveredEmailData['leadname']) ) ? $crmNotDeliveredEmailData['leadname'] : '-';
        $leademail = (isset($crmNotDeliveredEmailData['leademail']) ) ? $crmNotDeliveredEmailData['leademail'] : '-';
        $leadphone = (isset($crmNotDeliveredEmailData['leadphone']) ) ? $crmNotDeliveredEmailData['leadphone'] : '-';
        $deliverytype = (isset($crmNotDeliveredEmailData['deliverytype']) ) ? $crmNotDeliveredEmailData['deliverytype'] : '-';
        $businessname = (isset($crmNotDeliveredEmailData['businessname']) ) ? $crmNotDeliveredEmailData['businessname'] : '-';
        $campaignname = (isset($crmNotDeliveredEmailData['campaignname']) ) ? $crmNotDeliveredEmailData['campaignname'] : '-';
        $leaddeliveryvalue1 = ( isset($crmNotDeliveredEmailData['leaddeliveryvalue1'] )) ? $crmNotDeliveredEmailData['leaddeliveryvalue1'] : '-';
        $leaddeliveryvalue2 = ( isset($crmNotDeliveredEmailData['leaddeliveryvalue2'] )) ? $crmNotDeliveredEmailData['leaddeliveryvalue2'] : '-';
        $dynamccontent = "";
        if($deliverytype == "CRM"){
            $dynamccontent = '<tr><td>CRM Name</td><td> : </td><td>'. $leaddeliveryvalue1 .'</td></tr><tr><td>CRM URL</td><td> : </td><td>'. $leaddeliveryvalue2 .'</td></tr>';
        }else if($deliverytype == "Email"){
            $dynamccontent = '<tr><td>Email</td><td> : </td><td>'. $leaddeliveryvalue1 .'</td></tr><tr><td>Template</td><td> : </td><td>'. $leaddeliveryvalue2 .'</td></tr>';     
        }else if($deliverytype == "SMS"){
            $dynamccontent = '<tr><td>Phone Number</td><td> : </td><td>'. $leaddeliveryvalue1 .'</td></tr><tr><td>Carrier</td><td> : </td><td>'. $leaddeliveryvalue2 .'</td></tr>';        
        };        
        // echo "<pre>emailDNCLeadToBusinessEmail = ";
        // print_r($crmname);
        // die;
        $contents = "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='ISO-8859-1'>
            <title>Linkup</title>
        </head>
        <body>
        <table>
            <tr><td>Below lead delivery is failed while sending leads.</td></tr>
            <tr>
                <td>
                    <table>
                        <tr><td width='200px'>Business Name</td><td> : </td><td>". $businessname ."</td></tr>
                        <tr><td>Campaign Name</td><td> : </td><td>". $campaignname ."</td></tr>
                        <tr><td>Lead Delivery Type</td><td> : </td><td>". $deliverytype ."</td></tr>
                        <tr><td>Customer Name</td><td> : </td><td>". $leadname ."</td></tr>
                        <tr><td>Customer Email</td><td> : </td><td>". $leademail ."</td></tr>
                        <tr><td>Customer Phone</td><td> : </td><td>". $leadphone ."</td></tr>
                        ".$dynamccontent."
                    </table>
                </td>
            </tr>
        </table>
        </body>
        </html>";
        
        $from = "<EMAIL>";
        $matrixBaseURL_Outbound = Helper::checkServer()['matrixBaseURL_Outbound'];
        // $to = "<EMAIL>";
        $to = "<EMAIL>,<EMAIL>";
        if (strpos($matrixBaseURL_Outbound, '127.0.0.1') !== false || strpos($matrixBaseURL_Outbound, 'tweetstage.tweetsoftware.co') !== false) {
            $to = "<EMAIL>,<EMAIL>";
        } else if (strpos($matrixBaseURL_Outbound, 'linkup.software') !== false) {
            $to = "<EMAIL>,<EMAIL>,<EMAIL>";
        }
        $subject =  $businessname . " - " .$deliverytype. " - Lead Delivery Notification Email Failed";
        $json = json_encode(array(
            'From' => $from,
            'To' => $to, 
            'Subject' => $subject,
            'HtmlBody' => $contents,
            'Headers' => [],
            'Attachments' => []
        ));
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.postmarkapp.com/email');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Accept: application/json',
            'Content-Type: application/json',
            'X-Postmark-Server-Token: ' . $postmarkey
        ));
        curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
        $result = curl_exec($ch);
        $response = json_decode($result, true); 
        $status = 'failed';      
        if($response['Message'] == 'OK' ){           
            $status = 'success'; 
        } 
        $leadactiviyobj = new LeadActivity();
        //$leadactiviyobj->createActivity($crmNotDeliveredEmailData['leadid'], 4);
        EmailLog::create([
            "business_id" => $crmNotDeliveredEmailData['businessid'],
            "campaign_id" => $crmNotDeliveredEmailData['campaignid'],
            "subject" => $subject,
            "set_to" => $to,
            "set_from" => $from,
            "response" => $result,
            "type" => "leaddelivery",
            'created_at' => date('Y-m-d H:i:s')
        ]);
        return $status;
    }
  
}
