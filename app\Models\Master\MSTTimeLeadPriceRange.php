<?php

namespace App\Models\Master;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MSTTimeLeadPriceRange extends Model
{
    use HasFactory;
    protected $table = "mst_time_lead_price_range";

    public $timestamps = false;

    protected $fillable = [
        "time_slot",
        "avg_winning_bid",
        "competitive_range_min",
        "competitive_range_max",
        "created_at"
    ];
}