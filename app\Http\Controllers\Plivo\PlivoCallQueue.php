<?php

namespace App\Http\Controllers\Plivo;

use App\Http\Controllers\Controller;
use DateTime;
use DB;
use Exception;
use App\Helpers\Helper;
use Illuminate\Http\Request;
use App\Models\Outbound\LeadCall;
use App\Models\Campaign\Campaign;
use App\Models\Business\Business;
use App\Models\Inbound\LeadIbCall;
use App\Models\Inbound\LeadIbCallLog;
use App\Models\Lead\Lead;
use App\Http\Controllers\API\LeadPhone\LeadPhoneInsert;
use App\Http\Controllers\LeadCallBuffer\CallsbufferCronController;
use App\Models\Campaign\CampaignOngoingCall;
use App\Models\Outbound\LeadCallLog;
use App\Models\Outbound\LeadCallRouting;
use Log;
use App\Models\Lead\LeadTransferCall;
use App\Models\Lead\LeadRouting;
use App\Models\Master\MstCallConfig;
use App\Models\DevDebug;
use App\Models\Lead\LeadCallBuffer;
use App\Models\Lead\LeadFollowUp;
use App\Models\Lead\LeadCustomerSmsLog;
use App\Http\Controllers\PlivoSms\LiveTransferSms;
use App\Http\Controllers\PlivoSms\FollowUpCallSms;

class PlivoCallQueue extends Controller {

    private $custDialNumber = "";

    function __construct() {
        // $this->middleware('permission:purchase_plivo_number', ['only' => ['purchasePlivoNumber']]);
    }

    public function updateRecording(Request $request) {
        //echo "<pre>";print_r($_REQUEST);die;
        $todayDate = date("Y-m-d");
        $startDate = $todayDate . ' 00:00:00';
        $startDate = date('Y-m-d', strtotime('-1 day', strtotime($startDate))). ' 00:00:00';
        $endDate = $todayDate . ' 23:59:59';
        $empty = '';
        echo $startDate."==".$endDate."<br>";
        //echo $startDate."==".$endDate;die;
        $leadCall = LeadCall::where('legA_call_uuid', "<>", "")->where('is_checked_recording', 'no')
                        ->where(function ($leadCall) use ($empty) {
                            $leadCall->where('recording_url', '<=', $empty)
                            ->orWhereNull('recording_url');
                        })->whereBetween('call_datetime', [$startDate, $endDate])->get();
        // ->where('is_checked_recording', "no")->get();
        // echo "<pre>";
        // print_r( $leadCall );
        // die;      
        // if ($leadCall === null) {
        if (count($leadCall) == 0) {
            echo 'Lead call data not found for recording.<br>';
        } else {
            // Custom Encrypt and Decrypt Method Start
            $endDecObj = new Helper();
            if (isset($_REQUEST['sip']) && trim($_REQUEST['sip']) > 0) {
                $general_variable_1 = Helper::checkServer()['general_variable_5'];
                $general_variable_2 = Helper::checkServer()['general_variable_6'];
            } else {
                $general_variable_1 = Helper::checkServer()['general_variable_1'];
                $general_variable_2 = Helper::checkServer()['general_variable_2'];
            }
            $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
            $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
            // Custom Encrypt and Decrypt Method End
            $recoding_all_update = array();
            for ($i = 0; $i < 5; $i++) {
                $offset = $i * 20;
                $url = '?limit=20&offset=' . $offset;
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/Recording/' . $url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
                curl_setopt($ch, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
                $result = curl_exec($ch);
                $record = json_decode($result, true);
                if (isset($record) && count($record) > 0 ){
                    if (array_key_exists("objects",$record)){
                        $recoding_all_update[] = $record['objects'];
                    }                    
                }
                if (curl_errno($ch)) {
                    echo 'Error:' . curl_error($ch);
                }
                curl_close($ch);
            }
            $finalarray = array();
            foreach ($recoding_all_update as $value) {
                if (count($value) > 0) {
                    foreach ($value as $eachkey => $eachvalue) {
                        $finalarray[] = $eachvalue;
                    }
                }
            }
            if (count($finalarray) > 0) {
                foreach ($finalarray as $value) {
                    foreach ($leadCall as $outkey => $outvalue) {
                        if ($value['call_uuid'] == $outvalue->legA_call_uuid) {
                            $recordingDuration = $value['recording_duration_ms'] / 1000;
                            $recordingURL = $value['recording_url'];
                            $from_number = $outvalue->from_number;
                            $to_number = $outvalue->to_number;
                            $data = array();
                            $lpCallForwardNew = LeadCall::with(['leadtransferCalls', 'leadData'])->where('legA_call_uuid', $value['call_uuid'])->get()->first();
                            if (isset($lpCallForwardNew->lead_call_id) && $lpCallForwardNew->lead_call_id > 0) {
                                $lead_call_id = $lpCallForwardNew->lead_call_id;
                                $lead_id = $lpCallForwardNew->lead_id;
                                $user_id = $lpCallForwardNew->user_id;
                                $updateStatus = $this->checkCallStatus($value['call_uuid']);
                                $is_checked_recording = 'no';
                                if ($updateStatus > 0) {
                                    $is_checked_recording = 'yes';
                                }
                                if ($recordingDuration < 0) {

                                    //Added by BK on 18/03/2025
                                    $smsDetail = LeadCustomerSmsLog::where('sms_subject', 'Follow Up Call SMS')->where('lead_id', $lead_id)->first();
                                    if (empty($smsDetail)) {
                                        $followUpCallSmsObj = New FollowUpCallSms();
                                        $followUpCallSmsObj->followUpCallSMS($lead_id);
                                    }
                                    //Ended by BK on 18/03/2025

                                    $data = ['recording_url' => $recordingURL, 'duration' => $recordingDuration, 'payout' => 0, 'is_checked_recording' => $is_checked_recording];
                                    if ($lpCallForwardNew === null) {
                                        
                                    } else {
                                        //add in call buffer system if call duration under 20 sec.
                                        //if ($recordingDuration < 40) {
                                            $this->leadPhoneStoreData($lpCallForwardNew);
                                        //}
                                        if ($lpCallForwardNew->campaign_id == null) {
                                            // continue;
                                        } else {
                                            $businessesCampaign = Campaign::where('campaign_id', $lpCallForwardNew->campaign_id)->first();
                                            $businessesData = Business::where('business_id', $businessesCampaign->business_id)->get(['credit_available', 'credit_reserved'])->first();
                                            if ($lpCallForwardNew->call_disposition_id != 9 && $lpCallForwardNew->user_id == 0) {
                                                LeadRouting::where('lead_id', $lead_id)->where('campaign_id', $lpCallForwardNew->campaign_id)->update(['payout' => 0]);
                                                $lead_payout = LeadRouting::where('route_status', 'sold')->where('lead_id', $lead_id)->get()->sum("payout");
                                                Lead::where('lead_id', $lead_id)->update(['payout' => $lead_payout]);

                                                if ($businessesCampaign->payment_type == 0) {
                                                    if ($lpCallForwardNew->call_type == 'inbound') {
                                                        $this->createInboundLogs($from_number, $to_number, '26', '60 sec : Business Before credit_available: $ ' . ($businessesData->credit_available));
                                                    } else {
                                                        $this->createOutboundLogs($lead_id, '26', '60 sec : Business Before credit_available: $ ' . ($businessesData->credit_available), $lead_call_id);
                                                    }
                                                    $oldBalance = $businessesData->credit_available;
                                                    Business::where('business_id', $businessesCampaign->business_id)->update(['credit_available' => $oldBalance + $lpCallForwardNew->payout]);
                                                    if ($lpCallForwardNew->call_type == 'inbound') {
                                                        $this->createInboundLogs($from_number, $to_number, '27', '60 sec :  Business After credit_available: $ ' . ($oldBalance + $lpCallForwardNew->payout));
                                                    } else {
                                                        $this->createOutboundLogs($lead_id, '27', '60 sec :  Business After credit_available: $ ' . ($oldBalance + $lpCallForwardNew->payout), $lead_call_id);
                                                    }
                                                } else {
                                                    if ($lpCallForwardNew->call_type == 'inbound') {
                                                        $this->createInboundLogs($from_number, $to_number, '26', '60 sec :  BusinessesCampaign Before credit_available: $ ' . ($businessesCampaign->credit_available));
                                                    } else {
                                                        $this->createOutboundLogs($lead_id, '26', '60 sec :  Campaign Before credit_available: $ ' . ($businessesCampaign->credit_available), $lead_call_id);
                                                    }
                                                    $oldBalance = $businessesCampaign->credit_available;
                                                    Campaign::where('campaign_id', $lpCallForwardNew->campaign_id)->update(['credit_available' => $oldBalance + $lpCallForwardNew->payout]);
                                                    if ($lpCallForwardNew->call_type == 'inbound') {
                                                        $this->createInboundLogs($from_number, $to_number, '27', '60 sec :  BusinessesCampaign After credit_available: $ ' . ($oldBalance + $lpCallForwardNew->payout));
                                                    } else {
                                                        $this->createOutboundLogs($lead_id, '27', '60 sec :  Campaign After credit_available: $ ' . ($oldBalance + $lpCallForwardNew->payout), $lead_call_id);
                                                    }
                                                }
                                            } else {
                                                // $businessesCampaign = Campaign::where('campaign_id', $lpCallForwardNew->campaign_id)->get()->first();                                           
                                                if ($businessesCampaign->payment_type == 0) {
                                                    $creditAvailable = $businessesData->credit_available;
                                                    $creditReserved = $businessesData->credit_reserved;
                                                    $oldBalance = $creditAvailable + $creditReserved;
                                                    if ($lpCallForwardNew->call_type == 'inbound') {
                                                        $this->createInboundLogs($from_number, $to_number, '40', '60 Sec : Businesses Before balance :$ ' . ($oldBalance));
                                                    } else {
                                                        $this->createOutboundLogs($lead_id, '40', '60 Sec : Business Before balance :$ ' . ($oldBalance), $lead_call_id);
                                                    }
                                                    Business::where('business_id', $businessesCampaign->business_id)->update(['credit_available' => $oldBalance + $lpCallForwardNew->payout]);
                                                    if ($lpCallForwardNew->call_type == 'inbound') {
                                                        $this->createInboundLogs($from_number, $to_number, '41', '60 Sec : Businesses After balance :$ ' . ($oldBalance + $lpCallForwardNew->payout));
                                                    } else {
                                                        $this->createOutboundLogs($lead_id, '41', '60 Sec : Business After balance :$ ' . ($oldBalance + $lpCallForwardNew->payout), $lead_call_id);
                                                    }
                                                } else {
                                                    $creditAvailable = $businessesCampaign->credit_available;
                                                    $creditReserved = $businessesCampaign->credit_reserved;
                                                    $oldBalance = $creditAvailable + $creditReserved;
                                                    if ($lpCallForwardNew->call_type == 'inbound') {
                                                        $this->createInboundLogs($from_number, $to_number, '42', '60 Sec : BusinessesCampaign Before balance :$ ' . ($oldBalance));
                                                    } else {
                                                        $this->createOutboundLogs($lead_id, '42', '60 Sec : Campaign Before balance :$ ' . ($oldBalance), $lead_call_id);
                                                    }
                                                    Campaign::where('campaign_id', $lpCallForwardNew->campaign_id)->update(['credit_available' => $oldBalance + $lpCallForwardNew->payout]);
                                                    if ($lpCallForwardNew->call_type == 'inbound') {
                                                        $this->createInboundLogs($from_number, $to_number, '43', '60 Sec : BusinessesCampaign After balance :$ ' . ($oldBalance + $lpCallForwardNew->payout));
                                                    } else {
                                                        $this->createOutboundLogs($lead_id, '43', '60 Sec : Campaign After balance :$ ' . ($oldBalance + $lpCallForwardNew->payout), $lead_call_id);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    //Added By VP On 14-07-2022 For Insert Call Buffer list If Call Duration Greater Than or Equal to 100 Start
                                    //if ($recordingDuration >= 100 && $user_id <= 0) {
                                        $this->leadPhoneStoreData($lpCallForwardNew);
                                    //}
                                    //Added By VP On 14-07-2022 For Insert Call Buffer list If Call Duration Greater Than or Equal to 100 End
                                    $data = ['recording_url' => $recordingURL, 'duration' => $recordingDuration, 'is_checked_recording' => $is_checked_recording];
                                }

                                // added by VP 24-5-2023 for automated inbound payout 0 if call duration is < 20
                                $campaign_id = $outvalue->campaign_id;
                                if (($outvalue->call_type == "inbound" && $outvalue->inbound_type == "automatic" && $campaign_id > 0) || ($outvalue->call_type == "outbound" && $outvalue->inbound_type == "automatic" && $campaign_id > 0)) {
                                    $checkCallData = LeadIbCall::where('lead_call_id', $lead_call_id)->get()->toArray();
                                    $this->createInboundLogs($from_number, $to_number, '44', 'call_type= inbound, inbound_type= automatic == Ib Call Data=' . json_encode($checkCallData));
                                    // echo "hiii";
                                    $getRoutingData = LeadRouting::where('route_status', 'sold')->where('campaign_id',$campaign_id)->where('lead_id', $lead_id)->get()->toArray();
                                    //Added By HJ On 25-07-2023 For Solved Update Routing Record issue when same camapign and lead route Start
                                    if(count($getRoutingData) > 1){
                                        $getLeadRoutingData = LeadCallRouting::where('lead_call_id', $lead_call_id)->where('lead_id', $lead_id)->get()->toArray();
                                        if(count($getLeadRoutingData) > 0){
                                            $getRoutingData = LeadRouting::where('route_status', 'sold')->where('lead_routing_id', $getLeadRoutingData[0]['lead_routing_id'])->where('campaign_id',$campaign_id)->where('lead_id', $lead_id)->get()->toArray();
                                        }
                                    }
                                    //Added By HJ On 25-07-2023 For Solved Update Routing Record issue when same camapign and lead route End
                                    $appliedPayout = $cust_duration = $cust_received_time = $cust_ended_time = $balance_after_charge = 0;
                                    $is_free_lead = "no";
                                    $lead_routing_id = 0;
                                    if(count($getRoutingData) > 0){
                                        $lead_routing_id = $getRoutingData[0]['lead_routing_id'];
                                        $is_free_lead = $getRoutingData[0]['is_free_lead'];
                                        $appliedPayout = $getRoutingData[0]['payout'];
                                        $balance_after_charge = $getRoutingData[0]['balance_after_charge'];
                                    }
                                    if(count($checkCallData) > 0){
                                        $cust_received_time = $checkCallData[0]['cust_received_time'];
                                        $cust_ended_time = $checkCallData[0]['cust_ended_time'];
                                        $cust_duration = (strtotime($cust_ended_time) - strtotime($cust_received_time));
                                    }
                                    $businessesCampaign = Campaign::where('campaign_id', $campaign_id)->first();
                                    if ($outvalue->call_type == "inbound") {
                                        $inboundsecondslimit = MstCallConfig::where('call_config', 'inbound_payout_after_second')->first()->value;

                                        //Added by BK on 18/03/2025
                                        if($businessesCampaign) {
                                            $smsDetail = LeadCustomerSmsLog::where('sms_subject', 'Live Transfer SMS')->where('lead_id', $lead_id)->first();
                                            $businessesData = Business::where('business_id', $businessesCampaign->business_id)->get(['credit_available', 'credit_reserved','business_name'])->first();
                                            if (!empty($businessesCampaign) && $businessesCampaign->campaign_type_id <> 2 && empty($smsDetail)) {
                                                $liveTransferSmsObj = New LiveTransferSms();
                                                $liveTransferSmsObj->liveTransferSMS($lead_id, $businessesData->business_name);
                                            }
                                        } else {
                                            //Added by BK on 18/03/2025
                                            $smsDetail = LeadCustomerSmsLog::where('sms_subject', 'Follow Up Call SMS')->where('lead_id', $lead_id)->first();
                                            if (empty($smsDetail)) {
                                                $followUpCallSmsObj = New FollowUpCallSms();
                                                $followUpCallSmsObj->followUpCallSMS($lead_id);
                                            }
                                            //Ended by BK on 18/03/2025
                                        }
                                        
                                        //Ended by BK on 18/03/2025
                                    }else{
                                        $inboundsecondslimit = MstCallConfig::where('call_config', 'outbound_payout_after_second')->first()->value;
                                    }
                                    if(count($getRoutingData) > 0){
                                        $is_free_lead = $getRoutingData[0]['is_free_lead'];
                                        if($is_free_lead == "yes" && $cust_duration <= $inboundsecondslimit && $cust_duration >= 0){
                                            if ($outvalue->call_type == "inbound") {
                                                $this->createInboundLogs($from_number, $to_number, '45', 'is_free_lead revert= ' . $is_free_lead . "==cust_received_time=" . $cust_received_time . "==cust_ended_time=" . $cust_ended_time . "==cust_duration=" . $cust_duration."===inbound_payout_after_second=".$inboundsecondslimit);
                                            }else{
                                                $this->createInboundLogs($from_number, $to_number, '45', 'is_free_lead revert= ' . $is_free_lead . "==cust_received_time=" . $cust_received_time . "==cust_ended_time=" . $cust_ended_time . "==cust_duration=" . $cust_duration."===outbound_payout_after_second=".$inboundsecondslimit);
                                            }
                                            $leftFreeLead = $businessesCampaign->free_lead + 1;
                                            Campaign::where('campaign_id', $campaign_id)->update(['free_lead' => $leftFreeLead]);
                                            LeadRouting::where('lead_routing_id', $lead_routing_id)->update(['is_free_lead' => 'no']);
                                        }
                                    }
                                    if ($appliedPayout > 0) {
                                        if ($outvalue->call_type == "inbound") {
                                            $this->createInboundLogs($from_number, $to_number, '45', 'inbound_payout_after_second= ' . $inboundsecondslimit . "==cust_received_time=" . $cust_received_time . "==cust_ended_time=" . $cust_ended_time . "==cust_duration=" . $cust_duration);
                                        }else{
                                            $this->createInboundLogs($from_number, $to_number, '45', 'outbound_payout_after_second= ' . $inboundsecondslimit . "==cust_received_time=" . $cust_received_time . "==cust_ended_time=" . $cust_ended_time . "==cust_duration=" . $cust_duration);
                                        }
                                        if ($cust_duration <= $inboundsecondslimit && $cust_duration >= 0) {
                                            if ($outvalue->call_type == "inbound") {
                                                $this->createInboundLogs($from_number, $to_number, '47', 'cust_duration < inbound_payout_after_second');
                                            }else{
                                                $this->createInboundLogs($from_number, $to_number, '47', 'cust_duration < outbound_payout_after_second');
                                            }
                                            $data['payout'] = 0;
                                            if ($businessesCampaign->payment_type == 0) {
                                                $businessesData = Business::where('business_id', $businessesCampaign->business_id)->get(['credit_available', 'credit_reserved'])->first();
                                                $creditAvailable = $businessesData->credit_available;
                                                $creditReserved = $businessesData->credit_reserved;
                                                $oldBalance = $creditAvailable + $creditReserved;
                                                $this->createInboundLogs($from_number, $to_number, '48', '< 20 Sec : Business Before balance :$ ' . ($oldBalance));
                                                Business::where('business_id', $businessesCampaign->business_id)->update(['credit_available' => $oldBalance + $appliedPayout]);
                                                $this->createInboundLogs($from_number, $to_number, '49', '< 20 Sec : Business After balance :$ ' . ($oldBalance + $appliedPayout));
                                            } else {
                                                $creditAvailable = $businessesCampaign->credit_available;
                                                $creditReserved = $businessesCampaign->credit_reserved;
                                                $oldBalance = $creditAvailable + $creditReserved;
                                                $this->createInboundLogs($from_number, $to_number, '50', '< 20 Sec : Campaign Before balance :$ ' . ($oldBalance));
                                                Campaign::where('campaign_id', $campaign_id)->update(['credit_available' => $oldBalance + $appliedPayout]);
                                                $this->createInboundLogs($from_number, $to_number, '51', '< 20 Sec : Campaign After balance :$ ' . ($oldBalance + $appliedPayout));
                                            }
                                            $updateArr = $updateRouteArr = array();
                                            $updateArr['payout'] = 0;
                                            $updateRouteArr['payout'] = 0;
                                            $updateRouteArr['balance_after_charge'] = $balance_after_charge+$appliedPayout;
                                            LeadRouting::where('lead_routing_id', $lead_routing_id)->update($updateRouteArr);
                                            LeadTransferCall::where('lead_id', $lead_id)->where('lead_call_id',$lead_call_id)->update($updateArr);
                                            $lead_payout = LeadRouting::where('route_status', 'sold')->where('lead_id', $lead_id)->get()->sum("payout");
                                            Lead::where('lead_id', $lead_id)->update(['payout' => $lead_payout]);
                                        }
                                    }
                                } else {
                                    if($outvalue->call_type == "inbound" && ($outvalue->inbound_type == "automatic" || $outvalue->inbound_type == "manual")) {
                                        //Added by BK on 18/03/2025
                                        $checkLeadTransfer = LeadTransferCall::where('lead_id', $lead_id)->where('lead_call_id',$lead_call_id)->first();
                                        if($outvalue->inbound_type == 'manual') {
                                            if($checkLeadTransfer){ 
                                            } else {
                                                if($outvalue->call_disposition_id == 4 || $outvalue->call_disposition_id == 5 || $outvalue->call_disposition_id == 6 || $outvalue->call_disposition_id == 15) {

                                                    $smsDetail = LeadCustomerSmsLog::where('sms_subject', 'Follow Up Call SMS')->where('lead_id', $lead_id)->first();
                                                    if (empty($smsDetail)) {
                                                        $followUpCallSmsObj = New FollowUpCallSms();
                                                        $followUpCallSmsObj->followUpCallSMS($lead_id);
                                                    }
                                                }
                                            }
                                        } else {
                                            $smsDetail = LeadCustomerSmsLog::where('sms_subject', 'Follow Up Call SMS')->where('lead_id', $lead_id)->first();
                                            if (empty($smsDetail)) {
                                                $followUpCallSmsObj = New FollowUpCallSms();
                                                $followUpCallSmsObj->followUpCallSMS($lead_id);
                                            }    
                                        }
                                        
                                    }
                                }
                                LeadCall::where('legA_call_uuid', $value['call_uuid'])->update($data);
                                if ($lead_call_id > 0) {
                                    $calltrack_entry = CampaignOngoingCall::where('lead_id', $lead_id)->where('campaign_id', $lpCallForwardNew->campaign_id)->where('lead_call_id', $lead_call_id)->get()->first();
                                    if ($calltrack_entry != null) {
                                        $checkLeadTransfer = LeadTransferCall::where('lead_id', $lead_id)->where('lead_call_id',$lead_call_id)->where('calls_type','outbound')->first();
                                        if (isset($checkLeadTransfer) && $checkLeadTransfer->recording_url != '') {
                                            DB::table('dev_debug')->insert(['sr_no' => 100, 'result' => "outbound && recording_url=" . $checkLeadTransfer->recording_url]);
                                            CampaignOngoingCall::where('campaign_ongoing_call_id', $calltrack_entry->campaign_ongoing_call_id)->delete();
                                        }
                                        if ($calltrack_entry->campaign_ongoing_call_id > 0 && $outvalue->call_type == "inbound") {
                                            DB::table('dev_debug')->insert(['sr_no' => 100, 'result' => "inbound && campaign_ongoing_call_id=" . $calltrack_entry->campaign_ongoing_call_id]);
                                            CampaignOngoingCall::where('campaign_ongoing_call_id', $calltrack_entry->campaign_ongoing_call_id)->delete();
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        echo count($leadCall) . "= Call recording Stored successfully.<br>";
        // update recording for Live transfer
        $this->getTokLiveTransferUpdaterecording();
    }

    public function leadPhoneStoreData($lpCallForwardNew) {
        // $date = new DateTime();
        // $date->modify('-720 minutes');
        // $formatted_date = $date->format('Y-m-d H:i:s');
        // $formatted_date1 = date('Y-m-d H:i:s');
        $checkNumber = $from_number = $this->setTenDigitNumber($lpCallForwardNew->from_number);
        $to_number = $lpCallForwardNew->to_number;
        $lead_category = $lpCallForwardNew->leadData->lead_category_id;
        $this->createInboundLogs($from_number, $lpCallForwardNew->to_number, '123', 'Insert In buffer of category : ' . $lead_category);
        $buffer = new CallsbufferCronController();
        // $buffer->SaveToBufferViaPhoneonly($lpCallForwardNew->lead_id);
        $hoursCheck = date('G');
        $devdeug = new DevDebug();
        $devdeug->devDebuglog(60, 'leadPhoneStoreData hoursCheck= ' . $hoursCheck);
        $bufferleadcount = LeadCallBuffer::where('lead_id', $lpCallForwardNew->lead_id)->count();
        $followupleadcount = LeadFollowUp::where('lead_id', $lpCallForwardNew->lead_id)->count();

        //check call count when count > 1 do not enter folloup and buffer (only for phone lead)
        //DNC, Not Interested, Wrong Phone, Booked, Truck Rental do not process for further
        // Live transfer
        $combinedQuery = LeadCall::selectRaw('
            COUNT(CASE WHEN call_disposition_id IN (9, 5, 6) THEN 1 END) as fail_attempt,
            COUNT(CASE WHEN call_disposition_id IN (2, 3, 7, 8, 13) THEN 1 END) as dnc_fail_attempt,
            COUNT(CASE WHEN call_disposition_id = 1 THEN 1 END) as live_transfercount,
            COUNT(*) as call_count
        ')
        ->where('lead_id', $lpCallForwardNew->lead_id)
        ->first();

        // Access the counts as properties
        $fail_attempt = $combinedQuery->fail_attempt;
        $dnc_fail_attempt = $combinedQuery->dnc_fail_attempt;
        $live_transfercount = $combinedQuery->live_transfercount;
        $call_count = $combinedQuery->call_count;
        
        $devdeug->devDebuglog(60, 'leadPhoneStoreData leadid= ' . $lpCallForwardNew->lead_id);

        if ($call_count > 1) {
            return true;
        }
        if ($dnc_fail_attempt == 0) {
            if ($bufferleadcount == 0 && $followupleadcount == 0 && $fail_attempt < 4) {
                $leadArr = Lead::where('lead_id', $lpCallForwardNew->lead_id)->first()->toArray();
                if ($hoursCheck >= 11 && $hoursCheck < 21) {
                    $devdeug->devDebuglog(60, 'leadPhoneStoreData date time');
                    if ($live_transfercount > 0) {
                        $buffer->saveFollowupentry($leadArr, 1, 1);
                    }else{    
                        $buffer->SaveToBufferViaPhoneonly($lpCallForwardNew->lead_id);
                    }
                }else{
                    $devdeug->devDebuglog(60, 'leadPhoneStoreData night time');
                    if ($leadArr['lead_type'] == 'phone') {
                        $buffer->saveFollowupentry($leadArr, 1, 1);
                    }else{
                        $buffer->saveFollowupentry($leadArr, 1);
                    }
                }
            }
        }
    }

    public function createOutboundLogs($lead_id, $type, $log, $lead_call_id = 0) {
        if ($lead_id > 0 && $lead_call_id > 0) {
            $oblog = array(
                'lead_id' => $lead_id,
                'lead_call_id' => $lead_call_id,
                'log_srno' => $type,
                'log' => $log,
                'created_at' => date("Y-m-d H:i:s")
            );
            LeadCallLog::create($oblog);
        } else {
            DB::table('dev_debug')->insert(['sr_no' => $type, 'result' => $type . " createOutboundLogs log leadId: " . $lead_id . "==Call Id==" . $lead_call_id . "==Log==" . $log]);
        }
    }

    public function getRecordingbycalluuid($caller_uuid) {
        // Log::info('getRecordingbycalluuid == ' . $caller_uuid);
        $updateStatus = $this->checkCallStatus($caller_uuid);
        $recoding_all_update = array();
        if ($updateStatus > 0) {
            //Added By VP On 04-01-2022 For Custom Encrypt and Decrypt Method Start
            $endDecObj = new Helper;
            if (isset($_REQUEST['sip']) && trim($_REQUEST['sip']) > 0) {
                $general_variable_1 = Helper::checkServer()['general_variable_5'];
                $general_variable_2 = Helper::checkServer()['general_variable_6'];
            } else {
                $general_variable_1 = Helper::checkServer()['general_variable_1'];
                $general_variable_2 = Helper::checkServer()['general_variable_2'];
            }
            $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
            $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
            //$plivoBaseURL = 'https://'.$decVariable1.':'.$decVariable2.'@api.plivo.com/v1/Account/'.$decVariable1;
            $plivoBaseURL = 'https://api.plivo.com/v1/Account/' . $decVariable1;
            //Added By VP On 04-01-2022 For Custom Encrypt and Decrypt Method End
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $plivoBaseURL . '/Recording/?call_uuid=' . $caller_uuid);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
            curl_setopt($ch, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
            $result = curl_exec($ch);
            $record = json_decode($result, true);
            if (isset($record['objects'])) {
                $recoding_all_update = $record['objects'];
                if (count($recoding_all_update) > 0) {
                    $recoding_all_update[0]['is_checked_rec'] = $updateStatus;
                }
            }
            //dd($record);
            if (curl_errno($ch)) {
                echo 'Error:' . curl_error($ch);
            }
            curl_close($ch);
        }
        //echo "<pre>";print_r($recoding_all_update);die;
        return $recoding_all_update;
    }

    public function getDurationById($recordingId) {
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $general_variable_2 = Helper::checkServer()['general_variable_2'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/Recording/' . $recordingId);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
        curl_setopt($ch, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
        $result = curl_exec($ch);
        $record = json_decode($result, true);
        //echo "<pre>";print_r($record);die;
        return $record;
    }

    public function checkCallStatus($callUuid) {
        //Added By VP On 04-01-2022 For Custom Encrypt and Decrypt Method Start
        $endDecObj = new Helper;
        $general_variable_1 = Helper::checkServer()['general_variable_1'];
        $general_variable_2 = Helper::checkServer()['general_variable_2'];
        $decVariable1 = $endDecObj->customeDecryption($general_variable_1);
        $decVariable2 = $endDecObj->customeDecryption($general_variable_2);
        //echo $decVariable1."==".$decVariable2;die;
        //Added By VP On 04-01-2022 For Custom Encrypt and Decrypt Method End

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.plivo.com/v1/Account/' . $decVariable1 . '/Call/' . $callUuid . '/?status=live');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
        curl_setopt($ch, CURLOPT_USERPWD, $decVariable1 . ':' . $decVariable2);
        $result = curl_exec($ch);
        curl_close($ch);
        $apiResponse = json_decode($result, true);
        $updateStatus = "1";
        if (isset($apiResponse['call_status']) && strtoupper($apiResponse['call_status']) == "IN-PROGRESS") {
            $updateStatus = "0";
        }
        return $updateStatus;
    }

    // for tok outbound end    
    public function getTokLiveTransferUpdaterecording() {
        Log::info('toklivetransferupdaterecording');

        $todayDate = date("Y-m-d");
        $startDate = $todayDate . ' 00:00:00';
        $endDate = $todayDate . ' 23:59:59';
        // Check recording url and duration in OutboundCalls table
        $empty = '';
        $Live_transfer_fetch = LeadTransferCall::WhereNotNull('call_uuid')
            ->where(function ($Live_transfer_fetch) use ($empty) {
                $Live_transfer_fetch->where('recording_url', '<=', $empty)
                ->orWhereNull('recording_url');
            })->whereBetween('created_at', [$startDate, $endDate])
            ->where('is_checked_rec', "no")
            ->get();

        $getRecordingId = LeadTransferCall::WhereNotNull('recording_id')->whereBetween('created_at', [$startDate, $endDate])->where('is_checked_rec', "no")->get()->toArray();
        if (count($Live_transfer_fetch) == 0 && count($getRecordingId) == 0) {
            echo 'No empty recoding url found';
            exit(0);
        } else {
            foreach ($Live_transfer_fetch as $TOkOutvalue) {
                $recoding_all_update = array();
                $caller_uuid = $TOkOutvalue->call_uuid;
                //Log::info('Toksystemcronphp=getTokLiveTransferUpdaterecording == ' . $caller_uuid);
                $recoding_all_update = $this->getRecordingbycalluuid($caller_uuid);
                // two recording find then last recording (first one )  need to store here. its recroding between customer and campaign
                foreach ($recoding_all_update as $tokrecodingvalue) {
                    $recordingDuration = $tokrecodingvalue['recording_duration_ms'] / 1000;
                    $recordingURL = $tokrecodingvalue['recording_url'];
                    // $is_checked_rec = 0;
                    $is_checked_rec = "no";
                    if (isset($tokrecodingvalue['is_checked_rec'])) {
                        // $is_checked_rec = $tokrecodingvalue['is_checked_rec'];
                        $is_checked_rec = 'yes';
                    }
                    $data = ['recording_url' => $recordingURL, 'duration' => $recordingDuration, 'is_checked_rec' => $is_checked_rec];
                    LeadTransferCall::where('call_uuid', $tokrecodingvalue['call_uuid'])->update($data);
                    $OutboundCallscall_track = LeadTransferCall::where('call_uuid', $tokrecodingvalue['call_uuid'])->get()->first();
                    $calltrack_entry = CampaignOngoingCall::where('lead_id', $OutboundCallscall_track->lead_id)->where('campaign_id', $OutboundCallscall_track->campaign_id)->get()->first();

                    //Added by BK on 15/08/2024
                    $campaignDetail = Campaign::where('campaign_id', $OutboundCallscall_track->campaign_id)->first();
                    $businessDetail = Business::where('business_id', $campaignDetail->business_id)->first();
                    $smsDetail = LeadCustomerSmsLog::where('sms_subject', 'Live Transfer SMS')->where('lead_id', $OutboundCallscall_track->lead_id)->first();
                    if (!empty($campaignDetail) && $campaignDetail->campaign_type_id <> 2 && empty($smsDetail)) {
                        $liveTransferSmsObj = New LiveTransferSms();
                        $liveTransferSmsObj->liveTransferSMS($OutboundCallscall_track->lead_id, $businessDetail->business_name);
                    }
                    //Ended by BK on 15/08/2024

                    if (isset($calltrack_entry)) {
                        CampaignOngoingCall::where('campaign_ongoing_call_id', $calltrack_entry->campaign_ongoing_call_id)->delete();
                        $this->createOutboundLogs($OutboundCallscall_track->lead_id, '28', 'Live transfer Call track entry delete.Id:' . $calltrack_entry->campaign_ongoing_call_id);
                    }
                    break;
                }
            }
            //Added By HJ On 19-05-2023 For Get Inbound Call Duration By Recording Id Start
            for ($b = 0; $b < count($getRecordingId); $b++) {
                $recording_id = $getRecordingId[$b]['recording_id'];
                if (trim($recording_id) != "") {
                    $responseData = $this->getDurationById($recording_id);
                    if (isset($responseData['recording_duration_ms'])) {
                        $transfer_id = $getRecordingId[$b]['lead_transfer_id'];
                        $recordingDuration = $responseData['recording_duration_ms'] / 1000;
                        $updateDurationData = ['recording_url' => $responseData['recording_url'], 'duration' => $recordingDuration, 'is_checked_rec' => 'yes'];
                        LeadTransferCall::where('lead_transfer_id', $transfer_id)->update($updateDurationData);
                        //Added by BK on 15/08/2024
                        $checkLeadCall = LeadTransferCall::where('lead_transfer_id', $transfer_id)->where('calls_type', 'inbound')->first();
                        if($checkLeadCall) {
                            $campData = Campaign::where('campaign_id', $checkLeadCall->campaign_id)->first();
                            $businessDetail = Business::where('business_id', $campData->business_id)->first();
                            $smsDetail = LeadCustomerSmsLog::where('sms_subject', 'Live Transfer SMS')->where('lead_id', $checkLeadCall->lead_id)->first();
                            if (empty($smsDetail)) {
                                if($businessDetail) {
                                    $liveTransferSmsObj = New LiveTransferSms();
                                    $liveTransferSmsObj->liveTransferSMS($checkLeadCall->lead_id, $businessDetail->business_name);    
                                }
                            }
                        }    
                        //Ended by BK on 15/08/2024
                    }
                }
            }

            $campaignOngoingCallIds = CampaignOngoingCall::pluck('lead_call_id')->toArray();
            $leadTransferCallIds = LeadTransferCall::whereIn('lead_call_id', $campaignOngoingCallIds)->WhereNotNull('recording_url')->where('is_checked_rec', "yes")->pluck('lead_call_id')->toArray();;
            CampaignOngoingCall::whereIn('lead_call_id', $leadTransferCallIds)->delete();

            //Added By HJ On 19-05-2023 For Get Inbound Call Duration By Recording Id End
            echo count($Live_transfer_fetch) . '= Live transfer recording successfully store';
            exit;
        }
    }

    public function createInboundLogs($fromnumber, $tonumber, $log_srno, $log) {
        $iblog = array(
            'from_number' => $fromnumber,
            'to_number' => $tonumber,
            'log_srno' => $log_srno,
            'log' => $log,
            'created_at' => date("Y-m-d H:i:s")
        );
        LeadIbCallLog::create($iblog);
    }

    public function setTenDigitNumber($phone) {
        $phoneLength = strlen(trim($phone));
        if ($phoneLength > 10) {
            $removeChr = $phoneLength - 10;
            $phone = substr(trim($phone), $removeChr);
        }
        //Added By VP On 16/06/2022 For Check Customer Number Not Valid Start
        $phoneLength = strlen(trim($phone));
        if ($phoneLength != 10) {
            $oldphone = $phone;
            $phone = 9999999999;
            $this->createInboundLogs($phone, $phone, '123', $phoneLength . "===phoneLength");
        }
        //Added By VP On 16/06/2022 For Check Customer Number Not Valid End
        return $phone;
    }
}
