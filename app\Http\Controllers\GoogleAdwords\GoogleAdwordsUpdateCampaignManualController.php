<?php

namespace App\Http\Controllers\GoogleAdwords;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use Google\Ads\GoogleAds\Examples\Utils\ArgumentNames;
use Google\Ads\GoogleAds\Examples\Utils\ArgumentParser;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClient;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsClientBuilder;
use Google\Ads\GoogleAds\Lib\V18\GoogleAdsException;
use Google\Ads\GoogleAds\Lib\OAuth2TokenBuilder;
use Google\Ads\GoogleAds\Util\FieldMasks;
use Google\Ads\GoogleAds\Util\V18\ResourceNames;
use Google\Ads\GoogleAds\V18\Enums\CampaignStatusEnum\CampaignStatus;
use Google\Ads\GoogleAds\V18\Enums\BiddingStrategyTypeEnum\BiddingStrategyType;
use Google\Ads\GoogleAds\V18\Errors\GoogleAdsError;
use Google\Ads\GoogleAds\V18\Resources\Campaign;
use Google\Ads\GoogleAds\V18\Services\CampaignOperation;
use Google\Ads\GoogleAds\V18\Services\MutateCampaignsRequest;
use Google\Ads\GoogleAds\V18\Common\TargetCpa;
use Google\Ads\GoogleAds\V18\Common\TargetCpa\TargetCpa as TargetCpaType;
use Google\Ads\GoogleAds\V18\Services\SearchGoogleAdsRequest;
use Google\Ads\GoogleAds\V18\Services\CampaignServiceClient;
use Google\Ads\GoogleAds\V18\Common\MaximizeConversions;
use Google\Ads\GoogleAds\V18\Common\MaximizeConversionValue;
use Google\ApiCore\ApiException;
use DateTime;
use DB;
use Exception;

class GoogleAdwordsUpdateCampaignManualController extends Controller
{
    public static $CUSTOMER_ID;
    public static $CAMPAIGN_ID;

    public static function main($originName, $campaignIds, $campaignBids, $biddingStrategyTypes)
    {
        //echo '<pre>'; print_r($campaignBids); die;
        // Either pass the required parameters for this example on the command line, or insert them
        // into the constants above.
        $filePath = base_path("google_ads_php.ini");
        if ($originName == "VM") {
            GoogleAdwordsUpdateCampaignManualController::$CUSTOMER_ID = 7000201456;
        } else if($originName == "QTM") {
            GoogleAdwordsUpdateCampaignManualController::$CUSTOMER_ID = 3189698923;
        } else if ($originName == "IS") {
            GoogleAdwordsUpdateCampaignManualController::$CUSTOMER_ID = 5101673588;
        } else if ($originName == "PL") {
            GoogleAdwordsUpdateCampaignManualController::$CUSTOMER_ID = 8305774076;
        }

        // Generate a refreshable OAuth2 credential for authentication.
        $oAuth2Credential = (new OAuth2TokenBuilder())->fromFile($filePath)->build();

        // Construct a Google Ads client configured from a properties file and the
        // OAuth2 credentials above.
        $googleAdsClient = (new GoogleAdsClientBuilder())->fromFile($filePath)
            ->withOAuth2Credential($oAuth2Credential)
            ->build();

        try {
            return $response = self::updateCampaign(
                $googleAdsClient,
                self::$CUSTOMER_ID,
                $campaignIds,
                $campaignBids,
                $biddingStrategyTypes
            );
        } catch (GoogleAdsException $googleAdsException) {
            printf(
                "Request with ID '%s' has failed.%sGoogle Ads failure details:%s",
                $googleAdsException->getRequestId(),
                PHP_EOL,
                PHP_EOL
            );
            foreach ($googleAdsException->getGoogleAdsFailure()->getErrors() as $error) {
                /** @var GoogleAdsError $error */
                printf(
                    "\t%s: %s%s",
                    $error->getErrorCode()->getErrorCode(),
                    $error->getMessage(),
                    PHP_EOL
                );
            }
            exit(1);
        } catch (ApiException $apiException) {
            printf(
                "ApiException was thrown with message '%s'.%s",
                $apiException->getMessage(),
                PHP_EOL
            );
            exit(1);
        }
    }

    /**
     * Creates a new TargetSpend (Maximize Clicks) cross-account bidding strategy in the manager
     * account.
     *
     * @param GoogleAdsClient $googleAdsClient the Google Ads API client
     * @param int $managerCustomerId the manager account's customer ID
     * @return string the resource name of the newly created bidding strategy
     */
    // [START create_cross_account_strategy]
    private static function updateCampaign(GoogleAdsClient $googleAdsClient, $customerId, $campaignIds, $campaignBids, $biddingStrategyTypes) {

        // Creates a portfolio bidding strategy.
        // [START set_currency_code]
        $responseResult = [];
        foreach($campaignIds as $campaign) {
            try {
                // Creates a campaign object with the specified resource name and other changes.
                if (isset($biddingStrategyTypes[$campaign]) && $biddingStrategyTypes[$campaign] == "TARGET_CPA") {
                    $campaign = new Campaign([
                        'resource_name' => 'customers/'.$customerId.'/campaigns/'.$campaign,
                        'target_cpa' => new TargetCpa([
                            'target_cpa_micros' => ($campaignBids[$campaign] * 1000000)
                        ])
                    ]);
                } elseif (isset($biddingStrategyTypes[$campaign]) && $biddingStrategyTypes[$campaign] == "MAXIMIZE_CONVERSIONS") {
                    $campaign = new Campaign([
                        'resource_name' => 'customers/'.$customerId.'/campaigns/'.$campaign,
                        'maximize_conversions' => new MaximizeConversions([
                            'target_cpa_micros' => ($campaignBids[$campaign] * 1000000)
                        ])
                    ]);
                } elseif (isset($biddingStrategyTypes[$campaign]) && $biddingStrategyTypes[$campaign] == "MAXIMIZE_CONVERSION_VALUE") {
                    $campaign = new Campaign([
                        'resource_name' => 'customers/'.$customerId.'/campaigns/'.$campaign,
                        'maximize_conversion_value' => new MaximizeConversionValue([
                            'target_roas' => ($campaignBids[$campaign] / 100)
                        ])
                    ]);
                }

                // Constructs an operation that will update the campaign with the specified resource name,
                // using the FieldMasks utility to derive the update mask. This mask tells the Google Ads
                // API which attributes of the campaign you want to change.
                $campaignOperation = new CampaignOperation();
                $campaignOperation->setUpdate($campaign);
                $campaignOperation->setUpdateMask(FieldMasks::allSetFieldsOf($campaign));

                // Issues a mutate request to update the campaign.
                $campaignServiceClient = $googleAdsClient->getCampaignServiceClient();
                $response = $campaignServiceClient->mutateCampaigns(
                    MutateCampaignsRequest::build($customerId, [$campaignOperation])
                );

                $response->getResults()[0];
            } catch (ApiException $e){
                $e->getErrors()[0]->getReason();
            }
        }
        return true;
    }
    // [END create_cross_account_strategy]
}
