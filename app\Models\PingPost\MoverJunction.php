<?php

namespace App\Models\PingPost;

use App\Models\PingPost\PingPostPayout;
use App\Models\Lead\LeadRoutingLog;
use App\Models\Lead\LeadRouting;
use App\Models\Lead\LeadMoving;
use App\Models\Lead\LeadLandingPage;
use App\Models\Business\Business;
use App\Models\DevDebug;
use Exception;
use DB;

class MoverJunction
{
    private $AFFKEY         = 'cbQicXIcFeOpPOfemIideUGBwECPKb'; //'TEST';
    private $lpResponse     = 'JSON';

    public function ping($leadData, $campaignId) {
        $this->campaignId   = $campaignId;
        $responseData       = [];
        DevDebug::create([
            'sr_no'         => 110,
            'result'        => 'LeadData Array ' . json_encode($leadData),
            'created_at'    => date("Y-m-d H:i:s"),
        ]);
        $leadId             = $leadData[0]['lead_id'];
        try {
            //var_dump($this->checkCampaignStatus());
            $responseData = $this->isAdmissiblePing($leadId, $campaignId);
            if(count($responseData) == 0 && $this->checkCampaignStatus($campaignId)) {
                $getMoveInfo = LeadMoving::where('lead_id', $leadId)->get()->toArray();
                $getLandingInfo = LeadLandingPage::where('lead_id', $leadId)->get()->toArray();

                $body = array(
                    'AFFKEY' => $this->AFFKEY,
                    'FROMZIP' => $getMoveInfo[0]['from_zipcode'],
                    'TOZIP' => $getMoveInfo[0]['to_zipcode'],
                    'MOVEDATE' => $this->moveDateValue($getMoveInfo[0]['move_date']),
                    'MOVESIZE' => $this->moveSizeValue($getMoveInfo[0]['move_size_id']),
                    'IPADDRESS' => $getLandingInfo[0]['ip']
                );

                //dd($body);
                try {
                    $url = 'https://www.leadsjunction.com/exchange/incoming/api/pinghitreslead.php';
                    $responseData = json_decode($this->getMethod($url, $body), true);
                } catch(Exception $e) {
                    $responseData = $e->getMessage();
                }

                LeadRoutingLog::create([
                    'lead_id' => $leadId,
                    'campaign_id' => $this->campaignId,
                    'request' => http_build_query($body),
                    'response' => json_encode($responseData)
                ]);

                $payout = 0;
                if (isset($responseData) && $responseData['result'] == 'success') {
                    $this->leadId = $leadId;
                    $this->pingId = $responseData['pingid'];
                    $payout = $responseData['price'];

                    PingPostPayout::create([
                        'lead_id' => $this->leadId,
                        'campaign_id' => $this->campaignId,
                        'ping_id' => $this->pingId,
                        'ping_request' => http_build_query($body),
                        'ping_response' => json_encode($responseData),
                        'ping_type' => 'exclusive',
                        'is_request' => 'ping',
                        'created_at' => date("Y-m-d H:i:s"),
                        'payout' => $payout,
                        'ping_slot' => 4,
                        'is_win' => 'no',
                    ]);
                }
            }
            return $responseData;
        } catch(Exception $e) {
            DevDebug::create([
                'sr_no' => 109,
                'result' => 'MoverJunction PingPost ' . $e->getMessage(),
                'created_at' => date("Y-m-d H:i:s"),
            ]);
            dd($e->getMessage());
        }
        return $responseData;
    }

    public function post($leadData, $campaignId) {
        $this->campaignId   = $campaignId;
        $leadId             = $leadData[0]['lead_id'];
        $leadName           = $leadData[0]['name'];
        $email              = $leadData[0]['email'];
        $phone              = $leadData[0]['phone'];
        if($this->isAdmissiblePost($leadId, $campaignId) && $this->checkCampaignStatus($campaignId)) {
            // Fetch PingId from ws_payout
            $wsPayoutData   = PingPostPayout::where('lead_id', $leadId)->where('campaign_id', $this->campaignId)->get(['ping_id'])->first();
            $getMoveInfo    = LeadMoving::where('lead_id', $leadId)->get()->toArray();
            $getLandingInfo = LeadLandingPage::where('lead_id', $leadId)->get()->toArray();
            $first_name     = $last_name = '';
            $nameData       = explode(" ", $leadName);
            if (isset($nameData[0]) && !empty($nameData[0])) {
                $first_name = $nameData[0];
            }
            if (isset($nameData[1]) && !empty($nameData[1])) {
                $last_name  = $nameData[1];
            }

            $firstName = $lastName = 'Test';
            if(strpos(url()->current(), 'linkup.software') !== false) {
                /*$this->lpTest = 0;*/
                $firstName  = $first_name;
                $lastName   = $last_name;
            } else if(strpos(url()->current(), '54.67.5.60') !== false || strpos(url()->current(), 'tweetstage.tweetsoftware.co') !== false) {
                /*$this->lpTest = 0;*/
                $firstName  = $first_name;
                $lastName   = $last_name;
            }

            $body = array(
                'PINGID' => $wsPayoutData->ping_id,
                'FIRSTNAME' => $firstName,
                'LASTNAME' => $lastName,
                'EMAIL' => $email,
                'PHONE1' => $phone,
                'PHONE2' => $phone,
                'VEHICLEMAKE1'=> '',
                'VEHICLEMODEL1'=> '',
                'VEHICLEYEAR1'=> '',
                'RUNS1'=> '',
                'VEHICLEMAKE2'=> '',
                'VEHICLEMODEL2'=> '',
                'VEHICLEYEAR2'=> '',
                'RUNS2'=> '',
                'IPADDRESS' => $getLandingInfo[0]['ip'],
                'SUBID' => '',
                'JORNAYAID' => '',
                'TRUSTEDFORMID' => $getLandingInfo[0]['trusted_form_cert_id']
            );
            //echo '<pre>'; print_r($body); die;

            try {
                $url = 'https://www.leadsjunction.com/exchange/incoming/api/postreslead';
                $responseData = json_decode($this->getMethod($url, $body), true);
            } catch(Exception $e) {
                $responseData = $e->getMessage();
            }

            LeadRoutingLog::create([
                'lead_id' => $leadId,
                'campaign_id' => $this->campaignId,
                'request' => http_build_query($body),
                'response' => json_encode($responseData)
            ]);

            if (isset($responseData['result']) && trim($responseData['result']) == 'success') {
                PingPostPayout::where(['lead_id' => $leadId, 'campaign_id' => $this->campaignId, 'is_request' => 'ping'])->update([
                    'is_win' => "yes"
                ]);
                return true;
            }
        }
        return false;
    }

    public function moveSizeValue($moveSize = 0) {
        if($moveSize == 0) {
            return '2000';
        } else if($moveSize == 1) { //Studio
            return '2450';
        } else if($moveSize == 2) { //1 Bedroom
            return '3000';
        } else if($moveSize == 3) { //2 Bedrooms
            return '4550';
        } else if($moveSize == 4) { //3 Bedrooms
            return '10000';
        } else if($moveSize >= 5) { //4 Bedrooms
            return '15000';
        } else {
            return '15000';
        }
    }

    public function moveDateValue($moveDate) {
        $date               = date_create($moveDate);
        return date_format($date, "m/d/Y");
    }

    public function isAdmissiblePing($leadId, $campaignId) {
        $this->campaignId   = $campaignId;
        $returnArr          = array();
        $payoutArr          = array();
        $wsPayout           = PingPostPayout::where('lead_id', $leadId)->where('campaign_id', $this->campaignId)->where('is_request', 'ping')->get()->toArray();
        //echo "<pre>"; print_r($wsPayout); die;
        if (count($wsPayout) > 0) {
            $payoutArr          = json_decode($wsPayout[0]['ping_response']);
            $returnArr['result']= $payoutArr->result;
            $returnArr['ping_id']= $payoutArr->pingid;
            $returnArr['price'] = $payoutArr->price;
            $returnArr['error'] = "";
            return $returnArr;
        } else {
            return $returnArr;
        }
    }

    public function isAdmissiblePost($leadId, $campaignId) {
        $this->campaignId   = $campaignId;
        $wsRouting          = LeadRouting::where('lead_id', $leadId)->where('campaign_id', $this->campaignId)->where('route_status', 'sold')->first();
        if ($wsRouting) {
            return false;
        } else {
            return true;
        }
    }

    public function checkCampaignStatus($campaignId) {
        $this->campaignId   = $campaignId;
        $status             = 'yes';
        $businessCampaign   = Business::with([
            'businessCampaign' => function ($query) use ($status) {
                return $query->where('is_active', '=', $status)->where('campaign_id', $this->campaignId);
            }])->where('status', 'active')->where('buyer_type_id', 3)->get();
        //dd(count($businessesCampaign));
        if (count($businessCampaign) > 0) {
            return true;
        } else {
            return false;
        }
    }

    public static function getMethod($url, $params) {
        try {
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => $url.'?'.http_build_query($params),
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
            ));

            $response = curl_exec($curl);
            curl_close($curl);
            return $response;
        }
        catch(Exception $e)
        {
            \Log::info($e);
            return "Get Method Curl not call. Error response.";
        }
    }

    public static function postMethod($url, $postFields) {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/x-www-form-urlencoded',
                'Cookie: PHPSESSID=9745ulq0mjftko6di697d0ib62'
            ));
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
            $response = curl_exec($ch);
            curl_close($ch);

            return $response;
        } catch (Exception $e) {
            //echo "error in recording";
        }
    }
}