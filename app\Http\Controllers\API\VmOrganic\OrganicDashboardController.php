<?php

namespace App\Http\Controllers\API\VmOrganic;

use App\Helpers\CommonFunctions;
use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\Business\Business;
use App\Models\Campaign\Campaign;
use App\Models\Campaign\CampaignOrganic;
use App\Models\Lead\LandingPageCustomerLogin;
use App\Models\Lead\Lead;
use App\Models\Lead\LeadCallBuffer;
use App\Models\Lead\LeadCarTransport;
use App\Models\Lead\LeadCustomerReview;
use App\Models\Lead\LeadFollowUp;
use App\Models\Lead\LeadHeavyLifting;
use App\Models\Lead\LeadJunkRemoval;
use App\Models\Lead\LeadRouting;
use App\Models\Master\MstLeadSource;
use Illuminate\Http\Request;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Lead\LeadJunkRemovalSubType;
use App\Models\Lead\LandingPageBusinessReview;
use App\Models\Outbound\LeadCallNumberAreacode;
use App\Models\Outbound\GeneralNumberConfig;
use App\Models\Master\MstSubscriptionPlan;
use App\Models\Subscription\Subscription;
use App\Models\Outbound\LeadCall;
use Carbon\Carbon;

class OrganicDashboardController extends Controller
{
    public $token = 'A1B2C3D4E5F6G7H8I9J0';

    public function getSecurityToken()
    {
        //eyJpdiI6IktUblB6QkRvOEUxejkzZ1pvS1dzdHc9PSIsInZhbHVlIjoiVnB5Q2l0TjV4KzBLSnBJVW5RcEZCWnJ4b01zM2RVeHBUeEtwcEgzSHRXST0iLCJtYWMiOiI1ZDVmMTkwNjA3NzU1OGM3MTc0MjFmZDExYWY0Mzg3ZWZlZTNiOTNmODUyMDEwNjg2YjQxNzlmZDM5M2U1NzBmIiwidGFnIjoiIn0=

        $token = $this->token;
        $endDecObj = new Helper;
        $encValue = $endDecObj->customeEncryption($token);
        echo $encValue;
        die;
    }

    public function checkSecurityToken($securityToken)
    {
        $endDecObj = new Helper;
        $decValue = $endDecObj->customeDecryption($securityToken);
        $token = $this->token;
        $success = 0;
        if ($decValue == $token) {
            $success = 1;
        }
        return $success;
    }

    public function getSourceId()
    {
        $id = 0;
        $originData = MstLeadSource::read('VM');
        if ($originData) {
            $id = $originData->lead_source_id;
        }
        return $id;
    }

    public function userRegister(Request $request)
    {
        //echo "<pre>";print_r($request->all());die;
        $postData = $request->all();
        $leadFinalData = array();
        $status = "FAIL";
        $error = "";
        try {
            if (isset($postData['security_token']) && trim($postData['security_token']) != "") {
                $securityToken = trim($postData['security_token']);
                $checkToken = $this->checkSecurityToken($securityToken);
                $soruce_id = $this->getSourceId();
                if ($soruce_id == 0) {
                    $error = "Something went wrong in source";
                } else if ($checkToken > 0) {
                    $phone = trim($postData['phone']);
                    //DB::table('test')->insert(['result' => 'VMO Dashboard API uerRegister==' . json_encode($postData)]);
                    $getData = LandingPageCustomerLogin::where('phone', $phone)->orderBy('landing_page_customer_login_id', 'DESC')->get()->toArray();
                    //echo "<pre>";print_r($getData);die;
                    if (count($getData) > 0) {
                        $error = "Phone already registered.";
                        $leadFinalData = $getData;
                    } else {
                        $registerArr = array();
                        $registerArr['lead_source_id'] = $soruce_id;
                        $registerArr['first_name'] = trim($postData['first_name']);
                        $registerArr['last_name'] = trim($postData['last_name']);
                        $registerArr['email'] = trim($postData['email']);
                        $registerArr['phone'] = trim($postData['phone']);
                        $registerArr['created_at'] = date('Y-m-d H:i:s');
                        //echo "<pre>";print_r($registerArr);die;
                        LandingPageCustomerLogin::Create($registerArr);
                        $status = "SUCCESS";
                    }
                    if (count($leadFinalData) == 0) {
                        $getData = LandingPageCustomerLogin::where('phone', $phone)->orderBy('landing_page_customer_login_id', 'DESC')->get()->toArray();
                        $leadFinalData = $getData;
                    }
                } else {
                    $error = "Token not valid.";
                }
            } else {
                $error = "Token not found.";
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
        }
        $response = array('status' => $status, 'data' => $leadFinalData, 'error' => $error);
        return json_encode($response);
    }

    public function verifyPhone(Request $request)
    {
        //echo "<pre>";print_r($request->all());die;
        $postData = $request->all();
        $leadFinalData = array();
        $status = "FAIL";
        $error = "";
        try {
            if (isset($postData['security_token']) && trim($postData['security_token']) != "") {
                $securityToken = trim($postData['security_token']);
                $checkToken = $this->checkSecurityToken($securityToken);
                if ($checkToken > 0) {
                    $phone = trim($postData['phone']);
                    $getData = LandingPageCustomerLogin::where('phone', $phone)->orderBy('landing_page_customer_login_id', 'DESC')->get()->toArray();
                    //echo "<pre>";print_r($getData);die;
                    $is_verified = trim($postData['is_verified']);
                    //DB::table('test')->insert(['result' => 'VMO Dashboard API verifyPhone==' . json_encode($postData)]);
                    if (count($getData) > 0) {
                        $updateArr = array();
                        $updateArr['is_verified'] = $is_verified;
                        $updateId = $getData[0]['landing_page_customer_login_id'];
                        LandingPageCustomerLogin::where('landing_page_customer_login_id', $updateId)->update($updateArr);
                        $status = "SUCCESS";
                        $getData[0]['is_verified'] = $is_verified;
                        $leadFinalData = $getData;
                    } else {
                        $error = "Phone not found.";
                    }
                } else {
                    $error = "Token not valid.";
                }
            } else {
                $error = "Token not found.";
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
        }
        $response = array('status' => $status, 'data' => $leadFinalData, 'error' => $error);
        return json_encode($response);
    }

    public function editUserProfile(Request $request)
    {
        //echo "<pre>";print_r($request->all());die;
        $postData = $request->all();
        $leadFinalData = array();
        $status = "FAIL";
        $error = "";
        try {
            if (isset($postData['security_token']) && trim($postData['security_token']) != "") {
                $securityToken = trim($postData['security_token']);
                $checkToken = $this->checkSecurityToken($securityToken);
                if ($checkToken > 0) {
                    //$id = trim($postData['id']);
                    $phone = trim($postData['phone']);
                    $getData = LandingPageCustomerLogin::where('phone', $phone)->orderBy('landing_page_customer_login_id', 'DESC')->get()->toArray();
                    //echo "<pre>";print_r($getData);die;
                    $first_name = trim($postData['first_name']);
                    $last_name = trim($postData['last_name']);
                    $email = trim($postData['email']);
                    //DB::table('test')->insert(['result' => 'VMO Dashboard API editUserProfile==' . json_encode($postData)]);
                    if (count($getData) > 0) {
                        $updateArr = array();
                        $updateArr['phone'] = $phone;
                        $updateArr['first_name'] = $first_name;
                        $updateArr['last_name'] = $last_name;
                        $updateArr['email'] = $email;
                        $updateArr['updated_at'] = date('Y-m-d H:i:s');
                        $updateId = $getData[0]['landing_page_customer_login_id'];
                        //echo "<pre>";print_r($updateArr);die;
                        LandingPageCustomerLogin::where('landing_page_customer_login_id', $updateId)->update($updateArr);
                        $status = "SUCCESS";
                        $getData[0]['phone'] = $phone;
                        $getData[0]['first_name'] = $first_name;
                        $getData[0]['last_name'] = $last_name;
                        $getData[0]['email'] = $email;
                        $leadFinalData = $getData;
                    } else {
                        $error = "User not found.";
                    }
                } else {
                    $error = "Token not valid.";
                }
            } else {
                $error = "Token not found.";
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
        }
        $response = array('status' => $status, 'data' => $leadFinalData, 'error' => $error);
        return json_encode($response);
    }

    public function dashboardLogin(Request $request)
    {
        //echo "<pre>";print_r($request->all());die;
        $postData = $request->all();
        $leadFinalData = array();
        $status = "FAIL";
        $error = "";
        $signup = 1;
        try {
            if (isset($postData['security_token']) && trim($postData['security_token']) != "") {
                $securityToken = trim($postData['security_token']);
                $checkToken = $this->checkSecurityToken($securityToken);
                $soruce_id = $this->getSourceId();
                if ($soruce_id == 0) {
                    $error = "Something went wrong in source";
                }
                else if ($checkToken > 0) {
                    if (isset($postData['phone']) && trim($postData['phone']) != "") {
                        $phone = trim($postData['phone']);
                        $isVerified = "no";
                        if (isset($postData['is_verified']) && trim($postData['is_verified']) == 'no') {
                            $isVerified = "yes";
                        }
                        $getData = LandingPageCustomerLogin::where('phone', $phone)->orderBy('landing_page_customer_login_id', 'DESC')->get()->toArray();
                        $updateArr = array();
                        $updateArr['phone'] = $phone;
                        $updateArr['is_verified'] = $isVerified;
                        if (count($getData) > 0) {
                            if ($getData[0]['is_verified'] == "no") {
                                LandingPageCustomerLogin::where('phone', $phone)->update($updateArr);
                            }
                            $updateArr = $getData[0];
                            $leadFinalData = $getData;
                            //echo "<pre>";print_r($updateArr);die;
                            $signup = 0;
                            $status = "SUCCESS";
                        } else {
                            $soruce_id = $this->getSourceId();
                            if ($soruce_id == 0) {
                                $error = "Something went wrong in source";
                            } else {
                                $getData = Lead::where('phone', $phone)->orderBy('lead_id', 'DESC')->get()->toArray();
                                if (count($getData) > 0) {
                                    $fullname = explode(" ", trim($getData[0]['name']));
                                    if (count($fullname) > 1) {
                                        $first_name = $fullname[0];
                                        $last_name = $fullname[1];
                                    } else {
                                        $first_name = $last_name = trim($getData[0]['name']);
                                    }
                                    $updateArr['lead_source_id'] = $soruce_id;
                                    $updateArr['first_name'] = $first_name;
                                    $updateArr['last_name'] = $last_name;
                                    $updateArr['email'] = trim($getData[0]['email']);
                                    $updateArr['created_at'] = date('Y-m-d H:i:s');
                                    //echo "<pre>";print_r($updateArr);die;
                                    LandingPageCustomerLogin::Create($updateArr);
                                    $signup = 0;
                                    $status = "SUCCESS";
                                } else {
                                    $error = "User not found.";
                                }
                            }
                        }
                        if ($signup == 0 && count($leadFinalData) == 0) {
                            $getData = LandingPageCustomerLogin::where('phone', $phone)->orderBy('landing_page_customer_login_id', 'DESC')->get()->toArray();
                            $leadFinalData = $getData;
                        }
                        //DB::table('test')->insert(['result' => 'VMO Dashboard API dashboardVMLogin==' . json_encode($leadFinalData)]);
                    } else {
                        $error = "Phone not found.";
                    }
                } else {
                    $error = "Token not valid.";
                }
            } else {
                $error = "Token not found.";
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
        }
        $response = array('status' => $status, 'data' => $leadFinalData, 'error' => $error, 'signup' => $signup);
        return json_encode($response);
    }

    public function saveCampaignReview(Request $request)
    {
        //echo "<pre>";print_r($request->all());die;
        $postData = $request->all();
        $status = "FAIL";
        $error = "";
        try {
            if (isset($postData['security_token']) && trim($postData['security_token']) != "") {
                $securityToken = trim($postData['security_token']);
                $checkToken = $this->checkSecurityToken($securityToken);
                if ($checkToken > 0) {
                    if (isset($postData['campaign_id']) && trim($postData['campaign_id']) > 0) {
                        $campaign_id = trim($postData['campaign_id']);
                        $lead_id = trim($postData['lead_id']);
                        $lead_route_id = trim($postData['lead_route_id']);
                        $insertArr = array();
                        if (isset($postData['comment']) && trim($postData['comment']) != "") {
                            $insertArr['comments'] = trim($postData['comment']);
                        }
                        if (isset($postData['rating']) && trim($postData['rating']) != "") {
                            $insertArr['rating'] = trim($postData['rating']);
                        }
                        if (isset($postData['quote_amt']) && trim($postData['quote_amt']) > 0) {
                            $insertArr['quote_amt'] = round(trim($postData['quote_amt']), 2);
                        }
                        //DB::table('test')->insert(['result' => 'VMO Dashboard API saveCampaignReview==' . json_encode($postData)]);
                        $getData = LeadCustomerReview::where(['lead_routing_id'=> $lead_route_id,'lead_id'=> $lead_id])->orderBy('lead_routing_id', 'DESC')->get()->toArray();
                        if (count($getData) > 0) {
                            $pkId = $getData[0]['lead_routing_id'];
                            //echo "<pre>";print_r($insertArr);die;
                            $getLeadCustomerReview =  LeadCustomerReview::where('lead_routing_id', $pkId)->first();
                            if($getLeadCustomerReview){
                                LeadCustomerReview::where('lead_routing_id', $pkId)->update($insertArr);
                                $status = "SUCCESS";
                            }
                            else{
                                $error = 'Customer review not found';
                                $status = "FAIL";
                            }

                        } else {
                            $getCampData = Campaign::where('campaign_id', $campaign_id)->get(['campaign_id', 'business_id'])->toArray();
                            if (count($getCampData) > 0) {
                                //echo "<pre>";print_r($getCampData);die;
                                $insertArr['lead_routing_id'] = $lead_route_id;
                                $insertArr['lead_id'] = $lead_id;
                                $insertArr['business_id'] = $getCampData[0]['business_id'];
                                $insertArr['lead_call_id '] = null; //Pending - Need to discuss with Front developer for lead_call_id
                                $insertArr['is_customer_talked'] = "no";
                                $insertArr['is_booked'] = "no";
                                $insertArr['user_id '] = null;
                                $insertArr['created_at'] = date('Y-m-d H:i:s');
                                //$insertArr['campaign_id'] = $campaign_id;
                                //echo "<pre>";print_r($insertArr);die;
                                LeadCustomerReview::Create($insertArr);
                                $status = "SUCCESS";
                            } else {
                                $error = "Business not found.";
                            }
                        }
                    } else {
                        $error = "Campaign not valid.";
                    }
                } else {
                    $error = "Token not valid.";
                }
            } else {
                $error = "Token not found.";
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
        }
        $response = array('status' => $status, 'error' => $error);
        return json_encode($response);
    }

    public function saveBusinessReview(Request $request)
    {
        //echo "<pre>";print_r($request->all());die;
        $postData = $request->all();
        $status = "FAIL";
        $error = "";
        try {
            if (isset($postData['security_token']) && trim($postData['security_token']) != "") {
                $checkToken = $this->checkSecurityToken(trim($postData['security_token']));
                if ($checkToken > 0) {
                    $firstName = trim($postData['first_name']) ?? "";
                    if (empty($firstName)) {
                        throw new Exception("First Name required");
                    }
                    $lastName = trim($postData['last_name']) ?? "";
                    if (empty($lastName)) {
                        throw new Exception("Last Name required");
                    }
                    $email = trim(strtolower($postData['email'])) ?? "";
                    if (empty($email)) {
                        throw new Exception("Email required");
                    } elseif (filter_var($email, FILTER_VALIDATE_EMAIL) === false) {
                        throw new Exception("Invalid email format");
                    }
                    $cost = trim($postData['cost']) ?? "";
                    $fromState = trim($postData['from_state']) ?? "";
                    $toState = trim($postData['to_state']) ?? "";
                    $rating = trim($postData['rating']) ?? "";
                    $review = trim($postData['review']) ?? "";

                    $landingPageBusinessReviewId = LandingPageBusinessReview::create([
                        'first_name' => $firstName,
                        'last_name' => $lastName,
                        'email' => $email,
                        'cost' => $cost,
                        'from_state' => $fromState,
                        'to_state' => $toState,
                        'rating' => $rating,
                        'review' => $review,
                        'created_at' => date("Y-m-d H:i:s")
                    ])->landing_page_business_review_id;
                    //echo $landingPageBusinessReviewId; die;
                    if ($landingPageBusinessReviewId > 0) {
                        $status = "SUCCESS";
                    } else {
                        $error = "Error during save data.";
                    }
                } else {
                    $error = "Unauthorized.";
                }
            } else {
                $error = "Token not found.";
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
        }
        $response = array('status' => $status, 'error' => $error);
        return json_encode($response);
    }

    public function getCustomerLeads(Request $request)
    {
        //echo "<pre>";print_r($request->all());die;
        $postData = $request->all();
        $leadFinalData = array();
        $status = "FAIL";
        $error = "";
        try {
            if (isset($postData['security_token']) && trim($postData['security_token']) != "") {
                $images_url = Helper::checkServer()['images_url'];
                $securityToken = trim($postData['security_token']);
                $checkToken = $this->checkSecurityToken($securityToken);
                if ($checkToken > 0) {
                    $leadIdArr = $junkLeadIdAr = $heavyLeadIdAr = $carLeadAr = $leadIntentArray = $junkMoveInfoArr = $heavyMoveInfoArr = $carMoveInfoArr = $businessIdArray = $campaignIdArray = $licenceArray = $servicesOfferArray = $businessAboutArray = array();
                    if (isset($postData['phone']) && trim($postData['phone']) != "") {
                        $phone = trim($postData['phone']);
                        $valStartDate = date('Y-m-d H:i:s', strtotime('-90 days'));
                        $valEndDate = date("Y-m-d H:i:s");
                        DB::enableQueryLog();
                        $leadList = Lead::with(['LeadMoving', 'routingInfoLeadList.routingCampaignInfo', 'leadReviewinfo'])->whereBetween('lead_generated_at', [$valStartDate, $valEndDate])->where('phone', $phone)->where('lead_type', 'normal')->where('is_verified', 'yes')->orderBy('lead_id', 'DESC')->get()->toArray();
                        $query = DB::getQueryLog();
                        CommonFunctions::createDevDebugLog(1, 'get customer lead query');
                        CommonFunctions::createDevDebugLog(1, json_encode($query));
                        $pos1 = $pos2 = $pos3 = "";
                        $callItNumber = 0;
                        $callItNumberArr = $leadIdArr = $sourceIdArr = $leadCampReviewArr = $businessNameArr = $businessPhoneArr = $businessRateArr = $businesslogoArr = $businessCmpanyURLArr = $leadMoveInfoArr = $leadEstimateArr = $junkLeadIdAr = $heavyLeadIdAr = $carLeadAr = $businessBuyerTypeArr = array();
                        for ($g = 0; $g < count($leadList); $g++) {
                            $leadIdArr[] = $leadList[$g]['lead_id'];
                            $sourceName = MstLeadSource::where('lead_source_id', $leadList[$g]['lead_source_id'])->select('lead_source_name')->first();
                            $pos1 = strpos($sourceName, 'VM');
                            $pos2 = strpos($sourceName, 'QTM');
                            $pos3 = strpos($sourceName, 'IS');
                            if ($pos1 !== false) {
                                $sourceIdArr[$leadList[$g]['lead_id']] = 19;
                                $callItNumber = GeneralNumberConfig::getDialNumberByAreacode($phone)['vm_areacode_outbound'];
                            } else if ($pos2 !== false) {
                                $sourceIdArr[$leadList[$g]['lead_id']] = 14;
                                $callItNumber = GeneralNumberConfig::getDialNumberByAreacode($phone)['qtm_areacode_outbound'];
                            } else if ($pos3 !== false) {
                                $sourceIdArr[$leadList[$g]['lead_id']] = 36;
                                $callItNumber = GeneralNumberConfig::getDialNumberByAreacode($phone)['ism_areacode_outbound'];
                            }

                            $callItNumberArr[$leadList[$g]['lead_id']] = substr($callItNumber, -10);

                            if ($leadList[$g]['lead_category_id'] == 2) {
                                $junkLeadIdAr[] = $leadList[$g]['lead_id'];
                            } else if ($leadList[$g]['lead_category_id'] == 3) {
                                $heavyLeadIdAr[] = $leadList[$g]['lead_id'];
                            } else if ($leadList[$g]['lead_category_id'] == 4) {
                                $carLeadAr[] = $leadList[$g]['lead_id'];
                            }
                        }
                        //Added by BK 10/04/2023
                        // if (count($leadIdArr) > 0) {
                        //     $leadIntent = LeadIntent::whereIn('lead_id', $leadIdArr)->get()->toArray();
                        //     for ($i = 0; $i < count($leadIntent); $i++) {
                        //         $leadIntentArray[$leadIntent[$i]['lead_id']] = $leadIntent[$i]['intent'];
                        //     }
                        // }
                        if (count($junkLeadIdAr) > 0) {
                            $getMoveInfo = LeadJunkRemoval::whereIn('lead_id', $junkLeadIdAr)->get()->toArray();
                            $getMoveSubTypeInfo = LeadJunkRemovalSubType::whereIn('lead_id', $junkLeadIdAr)->get()->toArray();
                            $subTypes = [];
                            if(count($getMoveSubTypeInfo ) > 0 ){
                                for ($k = 0; $k < count($getMoveSubTypeInfo); $k++) {
                                    $subTypes[$getMoveSubTypeInfo[$k]['lead_id']][] = $getMoveSubTypeInfo[$k]['junk_sub_type_id'];
                                }
                            }
                            for ($j = 0; $j < count($getMoveInfo); $j++) {
                                $getMoveInfo[$j]['move_date'] = $getMoveInfo[$j]['junk_remove_date'];
                                if(isset($subTypes[$getMoveInfo[$j]['lead_id']])){
                                    $getMoveInfo[$j]['junk_sub_type_id'] = implode(',',$subTypes[$getMoveInfo[$j]['lead_id']]);
                                }
                                $junkMoveInfoArr[$getMoveInfo[$j]['lead_id']] = $getMoveInfo[$j];
                            }
                        }
                        if (count($heavyLeadIdAr) > 0) {
                            $getMoveInfo = LeadHeavyLifting::whereIn('lead_id', $heavyLeadIdAr)->get()->toArray();
                            for ($m = 0; $m < count($getMoveInfo); $m++) {
                                $getMoveInfo[$m]['move_date'] = $getMoveInfo[$m]['lift_date'];
                                $heavyMoveInfoArr[$getMoveInfo[$m]['lead_id']] = $getMoveInfo[$m];
                            }
                        }
                        if (count($carLeadAr) > 0) {
                            $getMoveInfo = LeadCarTransport::whereIn('lead_id', $carLeadAr)->get()->toArray();
                            for ($c = 0; $c < count($getMoveInfo); $c++) {
                                $carMoveInfoArr[$getMoveInfo[$c]['lead_id']] = $getMoveInfo[$c];
                            }
                        }
                        $moveDate = date('Y-m-d', strtotime('-180 days'));
                        //echo "<pre>";print_r($heavyMoveInfoArr);die;
                        //$tokSaveData = TokLiveTransferSavedData::with(['moveInfo'])->whereHas('moveInfo', function ($q) use ($moveDate) { $q->where('created_at', '>=', $moveDate); })->where('quote_amt', '>', 1)->orderby('id', 'DESC')->get()->toArray();
                        $tokSaveData = LeadCustomerReview::where('created_at', '>=', $moveDate)->where('quote_amt', '>', 1)->orderby('lead_customer_review_id', 'DESC')->get()->toArray();
                        $leadIdArr = $moveSizeDataArr = $leadQuoteAmtArr = array();
                        for ($g = 0; $g < count($tokSaveData); $g++) {
                            $leadIdArr[] = $tokSaveData[$g]['lead_id'];
                            $leadQuoteAmtArr[$tokSaveData[$g]['lead_id']] = $tokSaveData[$g]['quote_amt'];
                        }
                        if (count($leadIdArr) > 0) {
                            $lead_move_data = Lead::with(['LeadMoving', 'LeadJunkRemoval', 'LeadCarTransport', 'LeadHeavyLifting','junksubMoveInfo'])->whereIn('lead_id', $leadIdArr)->get()->toArray();
                            for ($h = 0; $h < count($lead_move_data); $h++) {
                                if ($lead_move_data[$h]['lead_category_id'] == 1 && isset($lead_move_data[$h]['lead_moving'])) {
                                    $lead_move_data[$h]['lead_moving'] = $lead_move_data[$h]['lead_moving'];
                                    $sizemove = $lead_move_data[$h]['lead_moving']['move_size_id'];
                                    $moveSizeDataArr[$sizemove][] = $lead_move_data[$h];
                                    unset($lead_move_data[$h]['lead_moving']);
                                }
                                else if ($lead_move_data[$h]['lead_category_id'] == 2 && isset($lead_move_data[$h]['lead_junk_removal'])) {
                                    $lead_move_data[$h]['lead_junk_removal']['move_date'] = $lead_move_data[$h]['lead_junk_removal']['junk_remove_date'];
                                    $lead_move_data[$h]['lead_moving'] = $lead_move_data[$h]['lead_junk_removal'];
                                    if(isset($lead_move_data[$h]['junksub_move_info'] ) && count($lead_move_data[$h]['junksub_move_info']) > 0) {
                                        foreach ($lead_move_data[$h]['junksub_move_info'] as $key => $value) {
                                            $sizemove = $value['junk_sub_type_id'];
                                            $moveSizeDataArr[$sizemove][] = $lead_move_data[$h];
                                        }
                                    }
                                    unset($lead_move_data[$h]['lead_junk_removal']);
                                } else if ($lead_move_data[$h]['lead_category_id'] == 3 && isset($lead_move_data[$h]['lead_heavy_lifting'])) {
                                    $lead_move_data[$h]['lead_heavy_lifting']['move_date'] = $lead_move_data[$h]['lead_heavy_lifting']['lift_date'];
                                    $lead_move_data[$h]['lead_moving'] = $lead_move_data[$h]['lead_heavy_lifting'];
                                    $sizemove = $lead_move_data[$h]['lead_moving']['heavy_lifting_type_id'];
                                    $moveSizeDataArr[$sizemove][] = $lead_move_data[$h];
                                    unset($lead_move_data[$h]['lead_heavy_lifting']);
                                } else if ($lead_move_data[$h]['lead_category_id'] == 4 && isset($lead_move_data[$h]['lead_car_transport'])) {
                                    $lead_move_data[$h]['lead_moving'] = $lead_move_data[$h]['lead_car_transport'];
                                    $sizemove = $lead_move_data[$h]['lead_moving']['car_type_id'];
                                    $moveSizeDataArr[$sizemove][] = $lead_move_data[$h];
                                    unset($lead_move_data[$h]['lead_car_transport']);
                                }

                            }
                        }
                        //echo "<pre>";print_r($moveSizeDataArr);die;
                        if (count($leadList) > 0) {
                            $getData = LeadCustomerReview::leftJoin('lead_routing', 'lead_routing.lead_routing_id', 'lead_customer_review.lead_routing_id')->orderBy('lead_customer_review.lead_customer_review_id', 'DESC')->get()->toArray();
                            //echo "<pre>";print_r($leadMoveInfoArr);die;
                            for ($j = 0; $j < count($getData); $j++) {
                                if (in_array($getData[$j]['lead_id'], $leadIdArr)) {
                                    $leadCampReviewArr[$getData[$j]['lead_id']][$getData[$j]['lead_routing_id']][] = $getData[$j];
                                }
                                if ($getData[$j]['rating'] > 0) {
                                    if (isset($businessRateArr[$getData[$j]['business_id']])) {
                                        $businessRateArr[$getData[$j]['business_id']]['rate'] += $getData[$j]['rating'];
                                        $businessRateArr[$getData[$j]['business_id']]['count'] += 1;
                                    } else {
                                        $businessRateArr[$getData[$j]['business_id']]['rate'] = $getData[$j]['rating'];
                                        $businessRateArr[$getData[$j]['business_id']]['count'] = 1;
                                    }
                                }
                            }
                            //echo "<pre>";print_r($businessRateArr);die;
                            $getBusinessData = Business::where('buyer_type_id', '!=', 3)->get(['business_id', 'display_name', 'buyer_type_id', 'rely_number', 'logo', 'company_url'])->toArray();
                            for ($b = 0; $b < count($getBusinessData); $b++) {
                                $businessNameArr[$getBusinessData[$b]['business_id']] = $getBusinessData[$b]['display_name'];
                                $businessBuyerTypeArr[$getBusinessData[$b]['business_id']] = $getBusinessData[$b]['buyer_type_id'];
                                $businessPhoneArr[$getBusinessData[$b]['business_id']] = $getBusinessData[$b]['rely_number'];
                                $subscriptionPlan = Subscription::where('business_id', $getBusinessData[$b]['business_id'])
                                    ->where('is_active', 'yes')
                                    ->first();
                                if($subscriptionPlan) {
                                    $planDetails = MstSubscriptionPlan::where('subscription_plan_id', $subscriptionPlan->subscription_plan_id)->first();
                                    if($planDetails) {
                                        if($planDetails->subscription_plan_id == 2) {
                                            $callCountN = $planDetails->call_count;
                                            if($callCountN > 0) {
                                                $startOfMonth = Carbon::now()->startOfMonth();
                                                $endOfMonth = Carbon::now()->endOfMonth();
                                                $leadCallCount = LeadCall::where('transfer_type', 'businessrely')
                                                ->where('business_id', $getBusinessData[$b]['business_id'])
                                                ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
                                                ->count();

                                                if($leadCallCount >= $callCountN) {
                                                    $businessPhoneArr[$getBusinessData[$b]['business_id']] = '';
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    $businessPhoneArr[$getBusinessData[$b]['business_id']] = '';
                                }

                                
                                $businessCmpanyURLArr[$getBusinessData[$b]['business_id']] = $getBusinessData[$b]['company_url'];
                                if ($getBusinessData[$b]['logo'] != '' && file_exists(public_path() . '/images/businesslogo/' . $getBusinessData[$b]['logo'])) {
                                    $businesslogo = $images_url . '/images/businesslogo/' . $getBusinessData[$b]['logo'];
                                } else {
                                    $businesslogo = $images_url . '/images/businesslogo/movers.png'; // URL::to('/')
                                }
                                $businesslogoArr[$getBusinessData[$b]['business_id']] = $businesslogo;
                            }
                            // $getBusinessPhoneData = BusinessesPhones::where('type', 3)->get()->toArray();
                            // for ($r = 0; $r < count($getBusinessPhoneData); $r++) {
                            //     $businessPhoneArr[$getBusinessPhoneData[$r]['businesses_id']] = $getBusinessPhoneData[$r]['phone'];
                            // }
                            $getCampaignData = Campaign::where('campaign_type_id', 4)->get(['campaign_id', 'business_id'])->toArray();
                            for ($c = 0; $c < count($getCampaignData); $c++) {
                                $businessIdArray[$getCampaignData[$c]['campaign_id']] = $getCampaignData[$c]['business_id'];
                                $campaignIdArray[] = $getCampaignData[$c]['campaign_id'];
                            }

                            $getOrganicData = CampaignOrganic::whereIn('campaign_id', $campaignIdArray)->get(['campaign_id', 'licence', 'services_offer', 'business_about'])->toArray();
                            for ($o = 0; $o < count($getOrganicData); $o++) {
                                $licenceArray[$businessIdArray[$getOrganicData[$o]['campaign_id']]] = $getOrganicData[$o]['licence'];
                                $servicesOfferArray[$businessIdArray[$getOrganicData[$o]['campaign_id']]] = $getOrganicData[$o]['services_offer'];
                                $businessAboutArray[$businessIdArray[$getOrganicData[$o]['campaign_id']]] = $getOrganicData[$o]['business_about'];
                            }
                            //echo "<pre>";print_r($getOrganicData);die;
                            for ($g = 0; $g < count($leadList); $g++) {
                                // $customerIntent = "";
                                // if (isset($leadIntentArray[$leadList[$g]['id']])) {
                                //     $customerIntent = $leadIntentArray[$leadList[$g]['id']];
                                // }
                                // $leadList[$g]['customer_intent'] = $customerIntent;

                                $estimated_price = "$0";
                                if (isset($junkMoveInfoArr[$leadList[$g]['lead_id']])) {
                                    $leadList[$g]['lead_moving'] = $junkMoveInfoArr[$leadList[$g]['lead_id']];
                                }
                                if (isset($heavyMoveInfoArr[$leadList[$g]['lead_id']])) {
                                    $leadList[$g]['lead_moving'] = $heavyMoveInfoArr[$leadList[$g]['lead_id']];
                                }
                                if (isset($carMoveInfoArr[$leadList[$g]['lead_id']])) {
                                    $leadList[$g]['lead_moving'] = $carMoveInfoArr[$leadList[$g]['lead_id']];
                                }
                                $leadFinalData = $leadList;
                                //echo "<pre>";print_r($leadList[$g]);
                                if (isset($leadList[$g]['lead_moving'])) {
                                    /*$fromAreaCode = (array_key_exists('from_areacode', $leadList[$g]['lead_moving']) ? $leadList[$g]['lead_moving']['from_areacode'] : 0);
                                    if ($fromAreaCode > 0 && isset($sourceIdArr[$leadList[$g]['lead_id']])) {
                                        $callItNumberDetail = LeadCallNumberAreacode::select('callit_number')->where(['areacode'=>$fromAreaCode, 'status'=>'active', 'is_deleted'=>'no', 'lead_source_id'=>$sourceIdArr[$leadList[$g]['lead_id']]])->first();
                                        if (empty($callItNumberDetail)) {
                                            $callItNumberDetail = LeadCallNumberAreacode::select('callit_number')->where(['status'=>'active', 'is_deleted'=>'no', 'lead_source_id'=>$sourceIdArr[$leadList[$g]['lead_id']]])->first();
                                        }
                                        $callItNumber = $callItNumberDetail->callit_number ?? 0;
                                    }*/

                                    if (isset($callItNumberArr[$leadList[$g]['lead_id']])) {
                                        $callItNumber = $callItNumberArr[$leadList[$g]['lead_id']];
                                    }
                                    $leadList[$g]['callit_number'] = $callItNumber;

                                    $distance = (array_key_exists('distance', $leadList[$g]['lead_moving']) ? $leadList[$g]['lead_moving']['distance'] : 0);
                                    $move_size = isset($leadList[$g]['lead_moving']['move_size_id']) ? $leadList[$g]['lead_moving']['move_size_id'] : 1 ;
                                    $postData = array('distance' => $distance, 'move_size' => $move_size);
                                    //Calculate Lead Estimated Price Start
                                    if (isset($moveSizeDataArr[$move_size])) {
                                        $getMatchData = $moveSizeDataArr[$move_size];
                                        //echo "<pre>";print_r($getMatchData);exit();
                                        $lowDistance = 0;
                                        $highDistance = 10000000;
                                        for ($s = 0; $s < count($getMatchData); $s++) {
                                            $leadDistance = array_key_exists('distance', $getMatchData[$s]['lead_moving']) ? $getMatchData[$s]['lead_moving']['distance'] : 0;
                                            $leadMoveSize = isset($getMatchData[$s]['lead_moving']['move_size_id']) ? $getMatchData[$s]['lead_moving']['move_size_id'] : 1 ;
                                            if ($leadDistance > 0 && $leadMoveSize == $move_size) {
                                                //echo $leadDistance."==".$highDistance."===";
                                                if ($leadDistance > $distance && $leadDistance < $highDistance) {
                                                    $highDistance = $leadDistance;
                                                }
                                                if ($leadDistance < $distance && $leadDistance > $lowDistance) {
                                                    $lowDistance = $leadDistance;
                                                }
                                            }
                                        }
                                        $finalDataArr = array();
                                        for ($f = 0; $f < count($getMatchData); $f++) {
                                            $leadDistanceF = array_key_exists('distance', $getMatchData[$f]['lead_moving']) ? $getMatchData[$f]['lead_moving']['distance'] : 0;
                                            if ($leadDistanceF == $highDistance || $leadDistanceF == $lowDistance) {
                                                $getMatchData[$f]['quote_amt'] = $leadQuoteAmtArr[$getMatchData[$f]['lead_id']];
                                                $finalDataArr[] = $getMatchData[$f];
                                            }
                                        }
                                        $estimated_price = $this->getReviewAmt($finalDataArr);
                                        //echo "<pre>";print_r($returnValue);exit();
                                    }
                                }
                                //echo "<pre>";print_r($estimated_price);die;
                                $leadList[$g]['estimated_price'] = $estimated_price;
                                // //Calculate Lead Estimated Price End
                                if (isset($leadList[$g]['routing_info_lead_list'])) {
                                    $routingData = $leadList[$g]['routing_info_lead_list'];
                                    for ($r = 0; $r < count($routingData); $r++) {
                                        if (isset($routingData[$r]['routing_campaign_info'])) {
                                            $campData = $routingData[$r]['routing_campaign_info'];
                                            for ($c = 0; $c < count($campData); $c++) {
                                                if (isset($businessBuyerTypeArr[$campData['business_id']])) {
                                                    $business_name = "--";
                                                    if (isset($businessNameArr[$campData['business_id']])) {
                                                        $business_name = $businessNameArr[$campData['business_id']];
                                                    }
                                                    $leadList[$g]['routing_info_lead_list'][$r]['routing_campaign_info']['business_name'] = $business_name;
                                                    $business_rating = 0;
                                                    if (isset($businessRateArr[$campData['business_id']])) {
                                                        $business_rating = $businessRateArr[$campData['business_id']]['rate'] / $businessRateArr[$campData['business_id']]['count'];
                                                    }
                                                    $leadList[$g]['routing_info_lead_list'][$r]['routing_campaign_info']['business_rating'] = round($business_rating, 1);
                                                    $business_phone = "";
                                                    if (isset($businessPhoneArr[$campData['business_id']])) {
                                                        $business_phone = $businessPhoneArr[$campData['business_id']];
                                                    }
                                                    $leadList[$g]['routing_info_lead_list'][$r]['routing_campaign_info']['business_phone'] = $business_phone;
                                                    $businesslogo = "";
                                                    if (isset($businesslogoArr[$campData['business_id']])) {
                                                        $businesslogo = $businesslogoArr[$campData['business_id']];
                                                    }
                                                    $leadList[$g]['routing_info_lead_list'][$r]['routing_campaign_info']['logo'] = $businesslogo;
                                                    $leadList[$g]['routing_info_lead_list'][$r]['lead_reviewinfo'] = array();
                                                    if (isset($leadCampReviewArr[$leadList[$g]['lead_id']][$routingData[$r]['lead_routing_id']])) {
                                                        $leadList[$g]['routing_info_lead_list'][$r]['lead_reviewinfo'] = $leadCampReviewArr[$leadList[$g]['lead_id']][$routingData[$r]['lead_routing_id']];
                                                    }

                                                    if (isset($licenceArray[$campData['business_id']])) {
                                                        $leadList[$g]['routing_info_lead_list'][$r]['routing_campaign_info']['licence'] = $licenceArray[$campData['business_id']];
                                                    } else {
                                                        $leadList[$g]['routing_info_lead_list'][$r]['routing_campaign_info']['licence'] = "";
                                                    }

                                                    if (isset($servicesOfferArray[$campData['business_id']])) {
                                                        $leadList[$g]['routing_info_lead_list'][$r]['routing_campaign_info']['services_offer'] = $servicesOfferArray[$campData['business_id']];
                                                    } else {
                                                        $leadList[$g]['routing_info_lead_list'][$r]['routing_campaign_info']['services_offer'] = "";
                                                    }

                                                    if (isset($businessAboutArray[$campData['business_id']])) {
                                                        $leadList[$g]['routing_info_lead_list'][$r]['routing_campaign_info']['business_about'] = $businessAboutArray[$campData['business_id']];
                                                    } else {
                                                        $leadList[$g]['routing_info_lead_list'][$r]['routing_campaign_info']['business_about'] = "";
                                                    }

                                                    $businessWebsite = "";
                                                    if (isset($businessCmpanyURLArr[$campData['business_id']])) {
                                                        $businessWebsite = $businessCmpanyURLArr[$campData['business_id']];
                                                    }
                                                    $leadList[$g]['routing_info_lead_list'][$r]['routing_campaign_info']['business_website'] = $businessWebsite;
                                                } else {
                                                    unset($leadList[$g]['routing_info_lead_list']);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            //echo "<pre>";print_r($leadList);die;
                            //DB::table('test')->insert(['result' => 'VMO Dashboard API getCustomerLeads==' . json_encode($leadList)]);
                            $leadFinalData = $leadList;
                            $status = "SUCCESS";
                        } else {
                            $error = "Data not found.";
                        }
                    } else {
                        $error = "Phone not found.";
                    }
                } else {
                    $error = "Token not valid.";
                }
            } else {
                $error = "Token not found.";
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
        }
        $response = array('status' => $status, 'data' => $leadFinalData, 'error' => $error);
        return json_encode($response);
    }

    public function getReviewCostCalculator(Request $request){
        //echo "<pre>";print_r($request->all());
        $moveSize = $distance = $returnValue = 0;
        $createdDate = date('Y-m-d', strtotime('-180 days'));
        $status = "FAIL";
        if(isset($request['move_size']) && trim($request['move_size']) != ""){
            $moveSize = (int)trim($request['move_size']) + 1;
        }
        if(isset($request['distance']) && trim($request['distance'])  > 0){
            $distance = trim($request['distance']);
        }
        if(!isset($request['move_size']) || !isset($request['distance'])){
            $response = array('status' => $status,'data' => $returnValue);
            return json_encode($response);
        }
        //echo $moveSize."==".$distance."==".$moveDate."<br>";
        //DB::enableQueryLog();
        $getMatchData = LeadCustomerReview::with(['LeadMoving'])
            ->whereHas('LeadMoving', function($q) use ($moveSize, $distance, $createdDate) {
                $q->where('move_size_id', $moveSize)->where('distance', $distance)->where('created_at', '>=', $createdDate);
            })->where('quote_amt', '>', 100)->orderby('lead_customer_review_id', 'DESC')->get()->toArray();
        //echo "<pre>";print_r($getMatchData);exit();
        //dd(DB::getQueryLog());
        if (count($getMatchData) > 0) {
            //echo "1<br>";
            $returnValue = $this->getReviewAmt($getMatchData);
            $status = "SUCCESS";
        } else {
            //echo "2<br>";
            $getMatchData = LeadCustomerReview::with(['LeadMoving'])
                ->whereHas('LeadMoving', function($q) use ($moveSize, $createdDate) {
                    $q->where('move_size_id', $moveSize)->where('created_at', '>=', $createdDate);
                })->where('quote_amt', '>', 100)->orderby('lead_customer_review_id', 'DESC')->get()->toArray();
            //echo "<pre>";print_r($getMatchData);exit();
            $lowDistance = 0;
            $highDistance = 10000000;
            for($g=0;$g<count($getMatchData);$g++){
                $leadDistance = $getMatchData[$g]['lead_moving']['distance'];
                $leadMoveSize = $getMatchData[$g]['lead_moving']['move_size_id'];
                if($leadDistance > 0 && $leadMoveSize == $moveSize){
                    //echo $leadDistance."==".$highDistance."===";
                    if($leadDistance > $distance && $leadDistance < $highDistance){
                        $highDistance = $leadDistance;
                    }
                    //echo $highDistance."=====<br>";
                    //echo $leadDistance."==".$lowDistance."===";
                    if($leadDistance < $distance && $leadDistance > $lowDistance){
                        $lowDistance = $leadDistance;
                    }
                    //echo $lowDistance."=====<br>";
                }
            }
            //echo "High==".$highDistance."==Low==".$lowDistance;
            //echo "<br>";
            //echo "<br>";print_r($getMatchData);
            //echo "<br>";
            $finalDataArr = array();
            for($f=0;$f<count($getMatchData);$f++){
                $leadDistanceF = $getMatchData[$f]['lead_moving']['distance'];
                if($leadDistanceF == $highDistance || $leadDistanceF == $lowDistance){
                    $finalDataArr[] = $getMatchData[$f];
                }
            }
            //echo "<br>";print_r($finalDataArr);
            //echo "<br>";
            $status = "SUCCESS";
            $returnValue = $this->getReviewAmt($finalDataArr);
            //echo "<pre>";print_r($returnValue);exit();
        }
        $response = array('status' => $status,'data' => $returnValue);
        return json_encode($response);
    }

    public function getReviewAmt($getMatchData)
    {
        $totalLead = count($getMatchData);
        //echo "<pre>";print_r($getMatchData);die;
        $totolQuoteAmt = $returnValue = 0;
        for ($g = 0; $g < count($getMatchData); $g++) {
            $totolQuoteAmt += $getMatchData[$g]['quote_amt'];
        }
        //echo "amt ==".$totolQuoteAmt."<br>";
        //echo "lead ==".$totalLead."<br>";
        if ($totolQuoteAmt > 0) {
            $avgQuoteAmt = ($totolQuoteAmt / $totalLead);
            //echo $avgQuoteAmt;die;
            $differentAmt1 = 10 * $avgQuoteAmt / 100;
            $differentAmt2 = 20 * $avgQuoteAmt / 100;
            //echo "diff ==".$differentAmt."<br>";
            $returnValue = "$" . (int) ($avgQuoteAmt - $differentAmt1) . " - $" . (int) ($avgQuoteAmt + $differentAmt2);
            //echo "diff ==".$returnValue."<br>";die;
        }
        return $returnValue;
    }

    public function getCustomerCallNumber(Request $request)
    {
        //echo "<pre>";print_r($request->all());die;
        $postData = $request->all();
        $leadFinalData = array();
        $status = "FAIL";
        $error = "";
        try {
            if (isset($postData['security_token']) && trim($postData['security_token']) != "") {
                $securityToken = trim($postData['security_token']);
                $checkToken = $this->checkSecurityToken($securityToken);
                if ($checkToken > 0) {
                    if (isset($postData['phone']) && trim($postData['phone']) != "") {
                        $phone = trim($postData['phone']);
                        $sourceName = trim($postData['origin']);
                        $pos1 = strpos($sourceName, 'VM');
                        $pos2 = strpos($sourceName, 'QTM');
                        $pos3 = strpos($sourceName, 'IS');
                        $callItNumber = 0;
                        if ($pos1 !== false) {
                            $callItNumber = GeneralNumberConfig::getDialNumberByAreacode($phone)['vm_areacode_outbound'];
                        } else if ($pos2 !== false) {
                            $callItNumber = GeneralNumberConfig::getDialNumberByAreacode($phone)['qtm_areacode_outbound'];
                        } else if ($pos3 !== false) {
                            $callItNumber = GeneralNumberConfig::getDialNumberByAreacode($phone)['ism_areacode_outbound'];
                        }

                        $status = "SUCCESS";
                        $leadFinalData['callit_number'] = substr($callItNumber, -10);
                    } else {
                        $error = "Phone not found.";
                    }
                } else {
                    $error = "Token not valid.";
                }
            } else {
                $error = "Token not found.";
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
        }
        $response = array('status' => $status, 'data' => $leadFinalData, 'error' => $error);
        return json_encode($response);
    }

    public function updateCustomerCallNumber(Request $request)
    {
        //echo "<pre>";print_r($request->all());die;
        $postData = $request->all();
        $status = "FAIL";
        $error = "";
        try {
            if (isset($postData['security_token']) && trim($postData['security_token']) != "") {
                $securityToken  = trim($postData['security_token']);
                $checkToken     = $this->checkSecurityToken($securityToken);
                if ($checkToken > 0) {
                    if (isset($postData['lead_id']) && trim($postData['lead_id']) != "") {
                        $phone  = trim($postData['phone']);

                        $phone  = preg_replace("/\D/", "", trim($phone));
                        $invalid= strlen($phone) != 10  || preg_match('/^1/', $phone) || preg_match('/^.11/', $phone) || preg_match('/^...1/', $phone) || preg_match('/^....11/', $phone) || preg_match('/^.9/', $phone);
                        if ($invalid == true) {
                            $phone = "";
                        }
                        if (filter_var($phone, FILTER_VALIDATE_REGEXP, array("options" => ["regexp" => "/[1-9]{1}[0-9]{9}/"])) === false) {
                            throw new Exception("Invalid phone format");
                        }

                        if ($phone) {
                            Lead::where('lead_id', $postData['lead_id'])->update([
                                'phone' => $phone
                            ]);

                            LeadCallBuffer::where('lead_id', $postData['lead_id'])->update([
                                'customer_number' => $phone
                            ]);

                            LeadFollowUp::where('lead_id', $postData['lead_id'])->update([
                                'cust_phone_number' => $phone
                            ]);
                            $status = "SUCCESS";
                        } else {
                            $error = "Error during upadate records.";
                        }
                    } else {
                        $error = "Lead not found.";
                    }
                } else {
                    $error = "Token not valid.";
                }
            } else {
                $error = "Token not found.";
            }
        } catch (Exception $ex) {
            $error = $ex->getMessage();
        }
        $response = array('status' => $status, 'error' => $error);
        return json_encode($response);
    }
}
